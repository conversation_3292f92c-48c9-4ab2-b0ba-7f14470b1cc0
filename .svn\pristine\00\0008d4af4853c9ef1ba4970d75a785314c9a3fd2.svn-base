﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorLogs.Queries.GetDetail;

public class
    GetMYSQLMonitorLogsDetailQueryHandler : IRequestHandler<GetMYSQLMonitorLogsDetailQuery, MYSQLMonitorLogsDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorLogsRepository _mysqlMonitorLogsRepository;

    public GetMYSQLMonitorLogsDetailQueryHandler(IMysqlMonitorLogsRepository mysqlMonitorLogsRepository, IMapper mapper)
    {
        _mysqlMonitorLogsRepository = mysqlMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<MYSQLMonitorLogsDetailVm> Handle(GetMYSQLMonitorLogsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorLogs = await _mysqlMonitorLogsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(mysqlMonitorLogs, nameof(MYSQLMonitorLogs),
            new NotFoundException(nameof(MYSQLMonitorLogs), request.Id));

        var mysqlMonitorLogDetail = _mapper.Map<MYSQLMonitorLogsDetailVm>(mysqlMonitorLogs);

        return mysqlMonitorLogDetail ?? throw new NotFoundException(nameof(MYSQLMonitorLogs), request.Id);
    }
}