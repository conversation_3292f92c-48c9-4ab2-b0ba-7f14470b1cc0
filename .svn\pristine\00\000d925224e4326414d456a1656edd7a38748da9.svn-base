﻿@*@using ContinuityPatrol.Domain.ViewModels.FormModel*@
@model ContinuityPatrol.Domain.ViewModels.FormModel.FormViewModel;
@using ContinuityPatrol.Shared.Services.Helper;

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/lib/jquery.steps/jquery.steps.css" rel="stylesheet" />
<link href="~/css/wizard.css" rel="stylesheet" />
<link href="~/css/builder.css" rel="stylesheet" />
<link href="~/lib/formeo/formeo.min.css" rel="stylesheet" />

@Html.AntiForgeryToken()
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <div class="d-flex align-items-center gap-3">
                <h6 class="page_title"><i class="cp-form-builder"></i><span>Form Builder</span></h6>
            </div>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false"><i title="Filter" class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="name=" id="Name">
                                        <label class="form-check-label" for="Name">
                                            Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input autocomplete="off" class="form-check-input" type="checkbox" value="version=" id="Version">
                                        <label class="form-check-label" for="Version">
                                            Version
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <button class="btn btn-primary btn-sm me-2 d-none" id="exportObjects" type="button">
                    <i class="cp-export me-1"></i>Export
                </button>
                <button class="btn btn-primary btn-sm me-2" id="importObjects" type="button">
                    <i class="cp-import me-1"></i>Import
                </button>
                <input type="file" id="fileInput" style="display: none;" />               
                <button id="CreateModalForm" type="button" class="me-2 btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal">
                    <i class="cp-add me-1"></i>Create
                </button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="formBuilderList" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Name</th>
                        <th>Version</th>
                        <th>Last Updated</th>
                        <th>Mapped</th>
                        <th>Publish</th>
                        <th><input type="checkbox" class="form-check-input exportAllData" style="margin-left: -5px"> Export</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--Modal Create-->
<div class="modal fade" data-bs-backdrop="static" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen modal-dialog-centered" id="ModalCreation">
        <form class="modal-content" id="formBuilder-form">
            @* asp-controller="FormBuilder" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data" *@
            <div class="modal-header">
                <h6 class="page_title">
                    <i class="cp-form-builder"></i><span id="dynamicHeader">Create Form</span>
                </h6>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body py-2">
                @Html.AntiForgeryToken()
                <div class="form-group">
                    <div class="form-label" id="dynamicInputName">Form Name</div>
                    <div class="input-group w-25" id="dynamicWidth">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input asp-for="Name" type="text" autofocus class="form-control editFormName" id="formName"
                               placeholder="Enter Form Name" autocomplete="off" maxlength="100" />
                    </div>
                    <span class="w-25" asp-validation-for="Name" id="formNameError"></span>
                    <span id="ForSaveIspublish"></span>
                    <input asp-for="Id" type="hidden" id="formID" />
                    <input asp-for="IsRestore" type="hidden" id="isRestore" />
                    <input asp-for="Properties" type="hidden" id="formBuilderProperties" />
                    <input asp-for="Version" type="hidden" id="formBuilderVersion" />
                    <input asp-for="IsPublish" type="hidden" id="formBuilderIsPublish" />
                </div>
                <div id="formeo-editor"></div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary">
                    <i class="cp-note me-1"></i>Note: All fields are mandatory
                    except optional
                </small>
                <div class="gap-2 d-flex me-5 dynamicButton">
                    <button type="button" class="btn btn-secondary btn-sm btn-cancel"
                            data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm"
                            id="prevBtn">
                        Preview
                    </button>
                    @*                     <a class="btn btn-secondary clear_btn btn-sm" href="javascript:void(0)"
                    role="menuitem" id="ClearFormBuilder">
                    Clear
                    </a> *@
                    @*  <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem"
                    onclick="form.steps('previous')">
                    Previous
                    </a> *@
                    @* <a class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem"
                    id="nextButton">
                    Save
                    </a> *@
                    <button type="button" id="formBuilderSave" class="btn btn-primary btn-sm save-button">Save</button>
                </div>
            </div>
        </form>       
    </div>
</div>

@* <!--Dynamic Icon-->
<div id="selectIconModal" class="modal fade" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <form class="modal-content" id="IconForm">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-form-builder"></i><span>Select Icon</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="table table-bordered" id="formDynamicicon">
                    <tbody>
                        <tr>
                            <td><i role="button" title="Server" class="cp-server custom-cursor-on-hover"></i></td>
                            <td><i role="button" title="Solaris" class="cp-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                            <td><i role="button" title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                            <td><i role="button" title="AIX" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                        </tr>
                        <tr>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>
    </div>
</div> *@

<!---preview-->
<div id="prevModal" class="modal fade" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
        <form class="modal-content" id="CreateForm" method="post" enctype="multipart/form-data">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-form-builder"></i><span>Form Configuration</span></h6>
                <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div id="form-preview-container" class="modal-body">
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm mt-2" data-bs-dismiss="modal">Cancel</button>
                    @* <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>*@
                </div>
            </div>
        </form>
    </div>
</div>

<!---preview Version-->
<!--Compare Version Modal -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="restoreModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-form-builder"></i><span>Compare Form Builder</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center gap-3">
                    <div class="card Card_Design_None w-100">
                        <div class="card-header header">
                            <span class="input-group-text">Primary Version &nbsp;<span id="primaryVersion"></span></span>
                        </div>
                        <div class="card-body p-3 CompareAction-Scroll previousJson" id="secondaryVersionContainer">
                        </div>
                    </div>
                    <div class="vr"></div>
                    <div class="card Card_Design_None w-100">
                        <div class="card-header header">
                            <span class="input-group-text">Alternative Version</span>
                            <div class="input-group">
                                <select id="formBuilderVersionControl" onchange="versionControlChange()"
                                        class="form-select-modal" data-live-search="true"
                                        data-placeholder="Select Alternative Version">
                                </select>
                                <span id="versionRestoreError"></span>
                            </div>
                        </div>
                        <div class="card-body p-3 CompareAction-Scroll currentJson" id="primaryVersionContainer">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="versionRestore">Restore</button>
            </div>
        </div>
    </div>
</div>

<!--Modal Restore-->
<div class="modal fade" id="RestoreConfirmationModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="~/img/isomatric/confirmation.svg" alt="confirmation Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h5 class="fw-bold">Are you sure?</h5>
                <p>You want to Restore the <span class="font-weight-bolder text-primary" id="restoreData"></span> data?</p>
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="submit" class="btn btn-primary btn-sm save-button" id="confirmRestoreButton">Yes</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Form Builder lock -->
<div class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" id="LockFormModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-lock"></i><span id="tittleName">Lock Action</span></h6>
                <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label" title="Secret Key">
                                Secret Key
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="cp-key"></i></span>
                                <input class="form-control" id="passwordForLock" type="password" placeholder="Enter Secret Key" />
                            </div>
                            <span id="passwordlog-error"></span>
                        </div>
                    </div>
                    <input class="form-control d-none" id="usernameForLock" value="@WebHelper.UserSession.LoginName" type="text" />
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <small class="text-secondary"></small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" cursorshover="true">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm saveLock">Verify</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>


<script src="~/lib/formeo/formeo.min.js"></script>
@* <script src="~/lib/jqueryui/jquery-ui.min.js"></script> *@

<script src="~/js/siteadmin/form/commonfunctions.js"></script>
<script src="~/js/siteadmin/form/formbuilder/controloptions.js"></script>
<script src="~/js/siteadmin/form/formbuilder/formbuilderformupdate.js"></script>
<script src="~/js/siteadmin/form/formbuilder/formbuilderfunctions.js"></script>
<script src="~/js/siteadmin/form/formbuilder/formbuilderdatatable.js"></script>
<script src="~/js/siteadmin/form/formbuilder/formbuilder.js"></script>
<script src="~/js/siteadmin/form/formbuilder/formbuildertables.js"></script>
<script src="~/lib/jquery.steps/jquery.steps.js"></script>
<script src="~/js/common/wizard.js"></script>