﻿using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Create;
using ContinuityPatrol.Application.Features.DRReadyStatus.Commands.Update;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessFunctionListByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetBusinessServiceDrReadyDetails;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetDrReadinessByBusinessServices;
using ContinuityPatrol.Application.Features.DRReadyStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DRReadyStatusModel;
using ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class DrReadyStatusService : BaseClient, IDrReadyStatusService
{
    public DrReadyStatusService(IConfiguration config, IAppCache cache, ILogger<DrReadyStatusService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<DRReadyStatusListVm>> GetDrReadyStatus()
    {
        var request = new RestRequest($"api/v6/drreadystatus");

        return await GetFromCache<List<DRReadyStatusListVm>>(request, "GetDrReadyStatus");
    }

    public async Task<BaseResponse> CreateAsync(CreateDRReadyStatusCommand createDrReadyStatusCommand)
    {
        var request = new RestRequest("api/v6/drreadystatus", Method.Post);

        request.AddJsonBody(createDrReadyStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDRReadyStatusCommand updateDrReadyStatusCommand)
    {
        var request = new RestRequest("api/v6/drreadystatus", Method.Put);

        request.AddJsonBody(updateDrReadyStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/drreadystatus/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DRReadyStatusDetailVm> GetDrReadyStatusById(string id)
    {
        var request = new RestRequest($"api/v6/drreadystatus/{id}");

        return await Get<DRReadyStatusDetailVm>(request);
    }

    public async Task<PaginatedResult<DRReadyStatusListVm>> GetPaginatedDrReadyStatus(GetDRReadyStatusPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/paginated-list?Type={query}");

        return await Get<PaginatedResult<DRReadyStatusListVm>>(request);
    }

    public async Task<List<BusinessServiceDrReadyDetailVm>> GetBusinessServiceDrReady(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadystatus/businessservice-dr-readiness-details?businessServiceId={businessServiceId}");

        return await GetFromCache<List<BusinessServiceDrReadyDetailVm>>(request, "GetDrReadyStatus");
    }

    public async Task<List<DRReadyStatusByBusinessServiceIdVm>> GetDrReadyStatusByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadystatus/businessserviceid?businessServiceId={businessServiceId}");

        return await Get<List<DRReadyStatusByBusinessServiceIdVm>>(request);
    }
    public async Task<List<GetBusinessFunctionListByBusinessServiceIdVm>> GetBusinessFunctionListByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadystatus/businessfunction-by-businessserviceid?businessServiceId={businessServiceId}");

        return await Get<List<GetBusinessFunctionListByBusinessServiceIdVm>>(request);
    }

    public async Task<AllCount> GetBusinessServiceIdByCount(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadystatus/allcount?businessServiceId={businessServiceId}");

        return await Get<AllCount>(request);
    }

    public async Task<GetDrReadinessByBusinessServiceVm> GetReadinessDetails(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/drreadystatus/readiness-details?businessServiceId={businessServiceId}");

        return await Get<GetDrReadinessByBusinessServiceVm>(request);
    }
}