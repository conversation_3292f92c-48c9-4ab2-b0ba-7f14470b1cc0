﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Events.Create;

public class WorkflowCategoryCreatedEventHandler : INotificationHandler<WorkflowCategoryCreatedEvent>
{
    private readonly ILogger<WorkflowCategoryCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowCategoryCreatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowCategoryCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowCategoryCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} Action Builder",
            Entity = "Action Builder",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Action Builder '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Action Builder '{createdEvent.Name}' created successfully.");
    }
}