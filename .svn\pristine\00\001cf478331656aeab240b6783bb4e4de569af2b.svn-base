﻿using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;

namespace ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Queries.GetByBusinessServiceId;

public class GetRpoSlaDeviationReportByBusinessServiceIdQueryHandler : IRequestHandler<
    GetRpoSlaDeviationReportByBusinessServiceIdQuery, List<RpoSlaDeviationReportListVm>>
{
    private readonly IMapper _mapper;
    private readonly IRpoSlaDeviationReportRepository _rpoSlaDeviationReportRepository;

    public GetRpoSlaDeviationReportByBusinessServiceIdQueryHandler(
        IRpoSlaDeviationReportRepository rpoSlaDeviationReportRepository, IMapper mapper)
    {
        _rpoSlaDeviationReportRepository = rpoSlaDeviationReportRepository;
        _mapper = mapper;
    }

    public async Task<List<RpoSlaDeviationReportListVm>> Handle(
        GetRpoSlaDeviationReportByBusinessServiceIdQuery request, CancellationToken cancellationToken)
    {
        var rpoSlaDeviationReports =
            await _rpoSlaDeviationReportRepository.GetRpoSlaDeviationReportListByBusinessServiceId(
                request.BusinessServiceId);

        var rpoSlaDeviationReportsDto = _mapper.Map<List<RpoSlaDeviationReportListVm>>(rpoSlaDeviationReports);

        return rpoSlaDeviationReportsDto;
    }
}