﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class BusinessServiceRepository : BaseRepository<BusinessService>, IBusinessServiceRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public BusinessServiceRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<BusinessService>> ListAllAsync()
    {
        var query = base.QueryAll(bs => bs.CompanyId ==_loggedInUserService.CompanyId);

        var mappedQuery = MapBusinessService(query);

        return _loggedInUserService.IsAllInfra
            ? await mappedQuery.ToListAsync()
            : GetAssignedBusinessServices(mappedQuery);
    }
    public async Task<IReadOnlyList<BusinessService>> GetByReferenceIdsAsync(List<string> ids)
    {
        var businessService = _loggedInUserService.IsParent
           ? Entities.AsNoTracking().DescOrderById().Where(x => ids.Contains(x.ReferenceId))
           : Entities.AsNoTracking().DescOrderById().Where(bs => ids.Contains(bs.ReferenceId) && bs.CompanyId == _loggedInUserService.CompanyId);


        var mappedQuery = MapBusinessService(businessService);

        return _loggedInUserService.IsAllInfra
            ? await mappedQuery.ToListAsync()
            : GetAssignedBusinessServices(mappedQuery);
    }
    public override async Task<BusinessService> GetByReferenceIdAsync(string id)
    {
        var query = GetByReferenceId(id,
            bs => bs.CompanyId == _loggedInUserService.CompanyId && bs.ReferenceId==id);

        var mappedQuery = MapBusinessService(query);

        return _loggedInUserService.IsAllInfra
            ? await mappedQuery.FirstOrDefaultAsync()
            : GetBusinessServiceByReferenceId(mappedQuery.FirstOrDefault());
    }

    public async Task<BusinessService> GetFilterByReferenceIdAsync(string id)
    {
        var query = IsParent
            ? Entities.AsNoTracking().Active()
            : Entities.AsNoTracking().Active().Where(bs => bs.CompanyId == _loggedInUserService.CompanyId);

        var businessService = await query
            .Select(x => new BusinessService { Id = x.Id, ReferenceId = x.ReferenceId, Name = x.Name })
            .FirstOrDefaultAsync(x => x.ReferenceId == id);

        return _loggedInUserService.IsAllInfra
            ? businessService
            : GetBusinessServiceByReferenceId(businessService);
    }


    public async Task<List<BusinessService>> GetBusinessServiceNames()
    {
        var query = base.QueryAll(bs => bs.CompanyId == _loggedInUserService.CompanyId)
            .Select(x => new BusinessService
            {
                ReferenceId = x.ReferenceId,
                Name = x.Name,
                CompanyId = x.CompanyId,
                Priority = x.Priority,
                SiteProperties = x.SiteProperties
            });

        return _loggedInUserService.IsAllInfra
            ? await query.ToListAsync()
            : GetAssignedBusinessServices(query).ToList();
    }
    public override async Task<PaginatedResult<BusinessService>>PaginatedListAllAsync(int pageNumber,int pageSize,Specification<BusinessService> specification, string sortColumn, string sortOrder)
    {
        var baseQuery = Entities.Specify(specification);
        if (!IsParent)
        {
            baseQuery = baseQuery.Where(bs => bs.CompanyId == _loggedInUserService.CompanyId);
        }

        var mappedQuery = MapBusinessService(baseQuery.DescOrderById());
        var query = _loggedInUserService.IsAllInfra ? mappedQuery : GetPaginatedAssignedBusinessServices(mappedQuery);

        return await SelectBusinessService(query).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

    }
    public override IQueryable<BusinessService> GetPaginatedQuery()
    {
        var query = base.QueryAll(bs => bs.CompanyId == _loggedInUserService.CompanyId);
        var mappedQuery = MapBusinessService(query);

        return _loggedInUserService.IsAllInfra 
            ? mappedQuery.AsNoTracking().OrderByDescending(x => x.Id) 
            : GetPaginatedAssignedBusinessServices(mappedQuery).AsNoTracking().OrderByDescending(x => x.Id);
    }

    public async Task<bool> IsBusinessServiceNameExist(string name, string id)
    {
        if (!id.IsValidGuid())
            return await _dbContext.BusinessServices.AnyAsync(e => e.Name == name);

        return await _dbContext.BusinessServices.AnyAsync(e => e.Name == name && e.ReferenceId != id);
    }

    public async Task<bool> IsBusinessServiceNameUnique(string name)
    {
        return await _dbContext.BusinessServices.AnyAsync(e => e.Name == name);
    }

    public async Task<IReadOnlyList<BusinessService>> GetBySiteIds(List<string> siteIds)
    {
        var query = _loggedInUserService.IsParent
            ? _dbContext.BusinessServices.AsNoTracking()
            : _dbContext.BusinessServices.AsNoTracking().Where(x => x.CompanyId == _loggedInUserService.CompanyId);

        query = query.Where(x => siteIds.Any(siteId => x.SiteProperties.Contains(siteId)));

        var result = _loggedInUserService.IsAllInfra ? await query.ToListAsync() : GetAssignedBusinessServices(query).ToList();

        return result;
    }

    public async Task<IReadOnlyList<BusinessService>> GetBusinessServicesBySiteId(string siteId)
    {
        var query = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.SiteProperties.Contains(siteId))
            : base.FilterBy(x => x.SiteProperties.Contains(siteId) && x.CompanyId == _loggedInUserService.CompanyId);

        var mappedQuery = MapBusinessService(query);

        return _loggedInUserService.IsAllInfra ? await mappedQuery.ToListAsync() : GetAssignedBusinessServices(mappedQuery);
    }
    
    private IQueryable<BusinessService> MapBusinessService(IQueryable<BusinessService> bsServices)
    {
        return bsServices.Select(x => new
        {
            Company = _dbContext.Companies.FirstOrDefault(c => c.ReferenceId == x.CompanyId),
            BusinessServices = x
        })
        .Select(res => new BusinessService
        {
            Id = res.BusinessServices.Id,
            ReferenceId = res.BusinessServices.ReferenceId,
            Name = res.BusinessServices.Name,
            Description = res.BusinessServices.Description,
            CompanyId = res.Company.ReferenceId,
            CompanyName = res.Company.DisplayName,
            SiteProperties = res.BusinessServices.SiteProperties,
            Priority = res.BusinessServices.Priority,
            IsActive = res.BusinessServices.IsActive,
            CreatedBy = res.BusinessServices.CreatedBy,
            CreatedDate = res.BusinessServices.CreatedDate,
            LastModifiedBy = res.BusinessServices.LastModifiedBy,
            LastModifiedDate = res.BusinessServices.LastModifiedDate,
        });
    }
    private IQueryable<BusinessService> SelectBusinessService(IQueryable<BusinessService> query)
    {
        return query.Select(x => new BusinessService
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            Name=x.Name,
            CompanyId = x.CompanyId,
            CompanyName = x.CompanyName,
            Description = x.Description,
            SiteProperties = x.SiteProperties,
            Priority = x.Priority
        });
    }
}