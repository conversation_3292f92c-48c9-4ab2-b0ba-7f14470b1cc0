﻿using ContinuityPatrol.Domain.ViewModels.DRCalendar;

namespace ContinuityPatrol.Application.Features.DRCalendar.Queries.GetList;

public class GetDrCalendarListQueryHandler : IRequestHandler<GetDrCalendarListQuery, List<DrCalendarActivityListVm>>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDrCalenderRepository _drCalenderRepository;
    private readonly IMapper _mapper;

    public GetDrCalendarListQueryHandler(IMapper mapper, IDrCalenderRepository drCalenderRepository,
        IBusinessServiceRepository businessServiceRepository)
    {
        _drCalenderRepository = drCalenderRepository;
        _businessServiceRepository = businessServiceRepository;
        _mapper = mapper;
    }

    public async Task<List<DrCalendarActivityListVm>> Handle(GetDrCalendarListQuery request,
        CancellationToken cancellationToken)
    {
        var drCalendarVm = await _drCalenderRepository.ListAllAsync();

        var drCalendarActivity = drCalendarVm.Count == 0
            ? new List<DrCalendarActivityListVm>()
            : _mapper.Map<List<DrCalendarActivityListVm>>(drCalendarVm);              

        var businessServiceIds = drCalendarActivity.Where(x => x.BusinessServiceId.IsNotNullOrWhiteSpace()).Select(x => x.BusinessServiceId).ToList();

        var businessServices = await _businessServiceRepository.GetByReferenceIdsAsync(businessServiceIds);
        var businessServiceList = businessServices.ToDictionary(bs => bs.ReferenceId, bs => bs.Name);

       
        drCalendarActivity.ForEach(drCalendar =>
        {
            if (drCalendar.BusinessServiceId.IsNotNullOrWhiteSpace())
            {
                drCalendar.BusinessServiceName = businessServiceList.TryGetValue(drCalendar.BusinessServiceId, out var name)
                    ? name
                    : "NA";
            }
        });
        //foreach (var drCalendar in drCalendarActivity1)
        //    if (drCalendar.BusinessServiceId.IsNotNullOrWhiteSpace())
        //    {
        //        var businessService =
        //            await _businessServiceRepository.GetByReferenceIdAsync(drCalendar.BusinessServiceId);

        //        drCalendar.BusinessServiceName = businessService?.Name ?? "NA";
        //    }
       
        return drCalendarActivity;
    }
}