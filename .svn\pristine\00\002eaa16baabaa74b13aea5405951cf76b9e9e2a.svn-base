﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DataSyncJob.Events.Update;

public class DataSyncJobUpdatedEventHandler : INotificationHandler<DataSyncJobUpdatedEvent>
{
    private readonly ILogger<DataSyncJobUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DataSyncJobUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<DataSyncJobUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DataSyncJobUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} DataSyncJob",
            Entity = "DataSyncJob",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"DataSync Job '{notification.Name}' updated successfully."
        };
        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DataSync Job '{notification.Name}' updated successfully.");
    }
}