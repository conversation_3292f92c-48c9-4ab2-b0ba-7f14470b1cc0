﻿namespace ContinuityPatrol.Domain.Entities;

public class User : AuditableEntity
{
    public string LoginType { get; set; }

    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string LoginName { get; set; }

    [Column(TypeName = "NCLOB")] public string LoginPassword { get; set; }

    public string Role { get; set; }

    public string RoleName { get; set; }

    public bool IsLock { get; set; }

    public bool IsReset { get; set; }

    public bool InfraObjectAllFlag { get; set; }

    public int SessionTimeout { get; set; }
    public bool IsVerify { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public bool IsGroup { get; set; }
    public bool IsDefaultDashboard { get; set; }
    public string Url { get; set; }

    //public virtual UserInfo UserInfo { get; set; }

    //public virtual UserInfraObject UserInfraObject { get; set; }
}