namespace ContinuityPatrol.Domain.Entities;

public class BiaRules : AuditableEntity
{
    public string Description { get; set; }
    public string Type { get; set; }
    public string EntityId { get; set; }
    [Column(TypeName = "NCLOB")] public string Properties { get; set; }
    public string EffectiveDateFrom { get; set; }
    public string EffectiveDateTo { get; set; }
    public bool IsEffective { get; set; }
    public string RuleCode { get; set; }
}