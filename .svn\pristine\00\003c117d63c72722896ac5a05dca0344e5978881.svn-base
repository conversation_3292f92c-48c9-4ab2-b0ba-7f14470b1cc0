﻿using ContinuityPatrol.Application.Features.ComponentType.Events.Update;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.ComponentType.Commands.Update;

public class
    UpdateComponentTypeCommandHandler : IRequestHandler<UpdateComponentTypeCommand, UpdateComponentTypeResponse>
{
    private readonly IComponentTypeRepository _componentTypeRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateComponentTypeCommandHandler(IMapper mapper, IComponentTypeRepository componentTypeRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _componentTypeRepository = componentTypeRepository;
        _publisher = publisher;
    }

    public async Task<UpdateComponentTypeResponse> Handle(UpdateComponentTypeCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _componentTypeRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(ComponentType), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateComponentTypeCommand), typeof(Domain.Entities.ComponentType));

        await _componentTypeRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateComponentTypeResponse
        {
            Message =
                Message.Update("Component Type", GetJsonProperties.GetJsonValue(eventToUpdate.Properties, "name")),

            ComponentTypeId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(
            new ComponentTypeUpdatedEvent { Name = GetJsonProperties.GetJsonValue(eventToUpdate.Properties, "name") },
            cancellationToken);

        return response;
    }
}