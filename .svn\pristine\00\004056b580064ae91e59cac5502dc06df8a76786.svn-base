﻿namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Commands.Update;

public class UpdateWorkflowOperationGroupCommandValidator : AbstractValidator<UpdateWorkflowOperationGroupCommand>
{
    private readonly List<string> _allowedActionMode = new() { "step", "auto", "simulate" };
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public UpdateWorkflowOperationGroupCommandValidator(IWorkflowOperationRepository workflowOperationRepository)
    {
        _workflowOperationRepository = workflowOperationRepository;

        RuleFor(x => x.BusinessServiceName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.InfraObjectName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.WorkflowName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters");


        RuleFor(p => p.ActionMode)
            .NotEmpty().WithMessage("Select {PropertyName}")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Must(value => value != null && _allowedActionMode.Contains(value.ToLower()))
            .WithMessage("{PropertyName} is invalid.");


        //RuleFor(p => p.CurrentActionName)
        //    .NotEmpty().WithMessage("Select {PropertyName}")
        //    .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid {PropertyName}")
        //    .NotNull();

        RuleFor(x => x.ConditionalOperation)
            .InclusiveBetween(0, 10).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(x => x.IsResume)
            .InclusiveBetween(0, 9).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(x => x.IsReExecute)
            .InclusiveBetween(0, 9).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(x => x.IsPause)
            .InclusiveBetween(0, 9).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(x => x.IsAbort)
            .InclusiveBetween(0, 9).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(x => x.WaitToNext)
            .InclusiveBetween(0, 9).WithMessage("{PropertyName} should be allowed numbers.");

        RuleFor(p => p)
            .MustAsync(IsValidGuidId)
            .WithMessage("Id is invalid");
    }

    private Task<bool> IsValidGuidId(UpdateWorkflowOperationGroupCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.Id, "Id");
        Guard.Against.InvalidGuidOrEmpty(p.InfraObjectId, "InfraObject Id");
        Guard.Against.InvalidGuidOrEmpty(p.BusinessServiceId, "BusinessService Id");
        Guard.Against.InvalidGuidOrEmpty(p.WorkflowId, "Workflow Id");
        Guard.Against.InvalidGuidOrEmpty(p.CurrentActionId, "Action Id");
        Guard.Against.InvalidGuidOrEmpty(p.WorkflowOperationId, "WorkflowOperation Id");

        if (!string.IsNullOrEmpty(p.WorkflowExecutionTempId))
            Guard.Against.InvalidGuidOrEmpty(p.WorkflowExecutionTempId, "WorkflowExecutionTemp Id");

        return Task.FromResult(true);
    }
}