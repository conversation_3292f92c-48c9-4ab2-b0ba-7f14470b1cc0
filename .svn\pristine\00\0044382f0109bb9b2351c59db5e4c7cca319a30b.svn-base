﻿using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Create;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Delete;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Paginated;
using ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class WorkflowProfileInfoFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<WorkflowProfileInfo> WorkflowProfileInfos { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateWorkflowProfileInfoCommand CreateWorkflowProfileInfoCommand { get; set; }
    public UpdateWorkflowProfileInfoCommand UpdateWorkflowProfileInfoCommand { get; set; }
    public WorkflowProfileInfoCreatedEvent WorkflowProfileInfoCreatedEvent { get; set; }
    public WorkflowProfileInfoDeletedEvent WorkflowProfileInfoDeletedEvent { get; set; }
    public WorkflowProfileInfoUpdatedEvent WorkflowProfileInfoUpdatedEvent { get; set; }
    public WorkflowProfileInfoPaginatedEvent WorkflowProfileInfoPaginatedEvent { get; set; }
    public List<WorkflowView> WorkflowViews { get; set; }

    public WorkflowProfileInfoFixture()
    {
        WorkflowProfileInfos = AutoWorkflowProfileInfoFixture.Create<List<WorkflowProfileInfo>>();
        WorkflowViews = AutoWorkflowProfileInfoFixture.Create<List<WorkflowView>>();
        UserActivities = AutoWorkflowProfileInfoFixture.Create<List<UserActivity>>();
        CreateWorkflowProfileInfoCommand = AutoWorkflowProfileInfoFixture.Create<CreateWorkflowProfileInfoCommand>();
        UpdateWorkflowProfileInfoCommand = AutoWorkflowProfileInfoFixture.Create<UpdateWorkflowProfileInfoCommand>();
        WorkflowProfileInfoCreatedEvent = AutoWorkflowProfileInfoFixture.Create<WorkflowProfileInfoCreatedEvent>();
        WorkflowProfileInfoDeletedEvent = AutoWorkflowProfileInfoFixture.Create<WorkflowProfileInfoDeletedEvent>();
        WorkflowProfileInfoUpdatedEvent = AutoWorkflowProfileInfoFixture.Create<WorkflowProfileInfoUpdatedEvent>();
        WorkflowProfileInfoPaginatedEvent = AutoWorkflowProfileInfoFixture.Create<WorkflowProfileInfoPaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowProfileInfoProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowProfileInfoFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowProfileInfoCommand>(p => p.ProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowProfileInfoCommand>(p => p.ProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateWorkflowProfileInfoCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<WorkflowProfileInfo>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileInfoCreatedEvent>(p => p.WorkflowProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileInfoDeletedEvent>(p => p.WorkflowProfileName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<WorkflowProfileInfoUpdatedEvent>(p => p.WorkflowProfileName, 10));

            return fixture;
        }
    }
    public void Dispose()
    {

    }
}