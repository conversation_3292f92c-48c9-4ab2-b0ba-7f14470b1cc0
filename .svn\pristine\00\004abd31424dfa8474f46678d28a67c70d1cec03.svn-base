﻿using AutoFixture;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Events.PaginatedView;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Configuration.Controllers;
using FluentValidation.Results;

namespace ContinuityPatrol.Web.UnitTests.Areas.Configuration.Controller
{
    public class RoboCopyOptionsControllerTests
    {
        private readonly Mock<IPublisher> _mockPublisher =new();
        private readonly Mock<ILogger<RoboCopyOptionsController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Mock<IMapper> _mockMapper = new();
        private  RoboCopyOptionsController _controller;

        public RoboCopyOptionsControllerTests()
        {
            
            _controller = new RoboCopyOptionsController(
                _mockPublisher.Object,
                _mockLogger.Object,
                _mockDataProvider.Object,
                _mockMapper.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_PublishesEvent_And_ReturnsView()
        {
            // Arrange & Act
            var result = await _controller.List();

            // Assert
            _mockPublisher.Verify(p => p.Publish(It.IsAny<RoboCopyPaginatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public async Task GetPagination_ReturnsJsonResult()
        {
            // Arrange
            var query = new GetRoboCopyPaginatedListQuery();
            var expectedResult = new PaginatedResult<RoboCopyListVm>();
            _mockDataProvider.Setup(dp => dp.RoboCopy.GetPaginatedRoboCopys(query)).ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetPagination(query);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.Equal(expectedResult, jsonResult.Value);
        }

        [Fact]
        public async Task GetPagination_HandlesException()
        {
            
            var query = new GetRoboCopyPaginatedListQuery();
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RoboCopy.GetPaginatedRoboCopys(query)).ThrowsAsync(exception);

            
            var result = await _controller.GetPagination(query);

            
            
            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":false", json);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdate_CreatesWhenIdIsEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RoboCopyViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", ""); // Empty ID for create
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var createCommand = new CreateRoboCopyCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };
            _mockMapper.Setup(m => m.Map<CreateRoboCopyCommand>(viewModel)).Returns(createCommand);
            _mockDataProvider.Setup(dp => dp.RoboCopy.CreateAsync(createCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockMapper.Verify(m => m.Map<CreateRoboCopyCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.RoboCopy.CreateAsync(createCommand), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_UpdatesWhenIdIsNotEmpty()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RoboCopyViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22"); // Non-empty ID for update
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var updateCommand = new UpdateRoboCopyCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };
            _mockMapper.Setup(m => m.Map<UpdateRoboCopyCommand>(viewModel)).Returns(updateCommand);
            _mockDataProvider.Setup(dp => dp.RoboCopy.UpdateAsync(updateCommand)).ReturnsAsync(response);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
            _mockMapper.Verify(m => m.Map<UpdateRoboCopyCommand>(viewModel), Times.Once);
            _mockDataProvider.Verify(dp => dp.RoboCopy.UpdateAsync(updateCommand), Times.Once);
        }

        [Fact]
        public async Task Delete_DeletesSuccessfully()
        {
            // Arrange
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };
            _mockDataProvider.Setup(dp => dp.RoboCopy.DeleteAsync(id)).ReturnsAsync(response);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task Delete_HandlesException()
        {
            // Arrange
            var id = "1";
            var exception = new Exception("Test exception");
            _mockDataProvider.Setup(dp => dp.RoboCopy.DeleteAsync(id)).ThrowsAsync(exception);

            // Act
            var result = await _controller.Delete(id);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsRoboCopyNameExist_ReturnsCorrectResult()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.RoboCopy.IsRoboCopyNameExist(name, id)).ReturnsAsync(true);

            // Act
            var result = await _controller.IsRoboCopyNameExist(name, id);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesValidationException_ReturnsRedirectToList()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RoboCopyViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("Property", "Validation error"));
            var validationException = new ValidationException(validationResult);

            _mockMapper.Setup(m => m.Map<CreateRoboCopyCommand>(viewModel))
                .Throws(validationException);

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_HandlesGeneralException_ReturnsRedirectToList()
        {
            // Arrange
            var viewModel = new AutoFixture.Fixture().Create<RoboCopyViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;

            _mockMapper.Setup(m => m.Map<CreateRoboCopyCommand>(viewModel))
                .Throws(new Exception("General error"));

            // Act
            var result = await _controller.CreateOrUpdate(viewModel);

            // Assert
            var redirectResult = Assert.IsType<RedirectToActionResult>(result);
            Assert.Equal("List", redirectResult.ActionName);
        }

        [Fact]
        public async Task IsRoboCopyNameExist_HandlesException_ReturnsFalse()
        {
            // Arrange
            var name = "TestName";
            var id = "1";
            _mockDataProvider.Setup(dp => dp.RoboCopy.IsRoboCopyNameExist(name, id))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.IsRoboCopyNameExist(name, id);

            // Assert
            Assert.False(result);
        }
    }
}
