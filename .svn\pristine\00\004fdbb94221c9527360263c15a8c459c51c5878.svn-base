﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Validators;

public class CreateUserRoleValidatorTests
{
    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;
    private CreateUserRoleCommandValidator validator;
	public List<Domain.Entities.UserRole> UserRoles { get; set; }

    public CreateUserRoleValidatorTests()
    {
        UserRoles = new Fixture().Create<List<Domain.Entities.UserRole>>();

        _mockUserRoleRepository = UserRoleRepositoryMocks.CreateUserRoleRepository(UserRoles);
    }

    //Role

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Role_InUserRole_With_Empty(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Role_InUserRole_With_IsNull(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = null;

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Role_InUserRole_With_MinimumRange(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "AB";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_CreateUserRoleCommandValidator_Role_With_MaximumRange(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIGABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIGABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIGABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIGABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKJIG";
        createUserRoleCommand.Logo = "rgb(255, 255, 255)";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Role should contain between 3 to 100 characters.");
		
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "  PTS  ";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_DoubleSpace_InFront(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "  PTS";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_DoubleSpace_InBack(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "PTS  ";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_TripleSpace_InBetween(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "PTS   India";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_SpecialCharacters_InFront(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "@$#@%PTS India";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_SpecialCharacters_InBack(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "PTS*&&^%";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_SpecialCharacters_InBetween(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "PTS%%#@*&India";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_SpecialCharacters_Only(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "*&&^%%$#@^%$&^";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_UnderScore_InFront(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "_PTS";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_UnderScore_InBack(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "PTS_";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_UnderScore_InFront_AndBack(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "_PTS_";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_Numbers_InFront(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "465PTS";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "_465PTS_";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_Create_Valid_Role_With_InUserRole_With_Numbers_Only(CreateUserRoleCommand createUserRoleCommand)
    {
        validator = new CreateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        createUserRoleCommand.Role = "3665497454";

        var validateResult = await validator.ValidateAsync(createUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }
}