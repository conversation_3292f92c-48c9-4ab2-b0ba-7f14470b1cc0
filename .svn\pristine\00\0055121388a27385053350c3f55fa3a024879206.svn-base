﻿using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByServerId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObject.Queries;

public class GetInfraObjectByServerIdQueryHandlerTests : IClassFixture<InfraObjectFixture>
{
    private readonly InfraObjectFixture _infraObjectFixture;
    private Mock<IInfraObjectRepository> _mockInfraObjectRepository;
    private Mock<IInfraObjectViewRepository> _mockInfraObjectViewRepository;
    private readonly GetInfraObjectByServerIdQueryHandler _handler;

    public GetInfraObjectByServerIdQueryHandlerTests(InfraObjectFixture infraObjectFixture)
    {
        _infraObjectFixture = infraObjectFixture;

        _mockInfraObjectViewRepository = new Mock<IInfraObjectViewRepository>();

        _mockInfraObjectRepository = InfraObjectRepositoryMocks.GetInfraObjectByServerId(_infraObjectFixture.InfraObjects);

        _handler = new GetInfraObjectByServerIdQueryHandler(_infraObjectFixture.Mapper, _mockInfraObjectViewRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnInfraObject_When_ValidBusinessFunctionId()
    {
        var result = await _handler.Handle(new GetInfraObjectByServerIdQuery { ServerId = _infraObjectFixture.InfraObjects[0].BusinessFunctionId }, CancellationToken.None);

        result.ShouldBeOfType<List<GetInfraObjectByServerIdVm>>();

        result.Count.ShouldBe(3);

        result[0].Id.ShouldBe(_infraObjectFixture.InfraObjects[0].ReferenceId);

        result[0].Name.ShouldBe(_infraObjectFixture.InfraObjects[0].Name);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockInfraObjectRepository = InfraObjectRepositoryMocks.GetInfraObjectEmptyRepository();

        var handler = new GetInfraObjectByServerIdQueryHandler(_infraObjectFixture.Mapper, _mockInfraObjectViewRepository.Object);

        var result = await handler.Handle(new GetInfraObjectByServerIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetInfraObjectByBusinessFunctionIdMethod_OneTime()
    {
        await _handler.Handle(new GetInfraObjectByServerIdQuery { ServerId = _infraObjectFixture.InfraObjects[0].ReferenceId }, CancellationToken.None);

        _mockInfraObjectRepository.Verify(x => x.GetInfraObjectByServerId(It.IsAny<string>()), Times.Once);
    }
}