﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.MsSqlNativeLogShippingMonitorStatus.Queries;
public class GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQueryHandlerTests
{
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<IMsSqlNativeLogShippingMonitorStatusRepository> _repositoryMock = new();
    private readonly GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQueryHandler _handler;

    public GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQueryHandlerTests()
    {
        _handler = new GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQueryHandler(
            _mapperMock.Object,
            _repositoryMock.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldReturnReferenceId_WhenEntityIsFoundAndActive()
    {
        // Arrange
        var query = new GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQuery { InfraObjectId = "123" };
        var entity = new Domain.Entities.MsSqlNativeLogShippingMonitorStatus
        {
            Id = 1,
            IsActive = true,
            ReferenceId = "ref-123"
        };

        _repositoryMock.Setup(r =>
            r.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(query.InfraObjectId))
            .ReturnsAsync(entity);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().Be("ref-123");
        _repositoryMock.Verify(r =>
            r.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(query.InfraObjectId), Times.Once);
    }
    [Fact]
    public void Should_Assign_And_Assert_Id_Property()
    {
        // Arrange
        var vm = new GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdVm
        {
            Id = "Infra123"
        };

        // Assert
        vm.Id.Should().Be("Infra123");
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsNull()
    {
        // Arrange
        var query = new GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQuery { InfraObjectId = "456" };

        _repositoryMock.Setup(r =>
            r.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(query.InfraObjectId))
            .ReturnsAsync((Domain.Entities.MsSqlNativeLogShippingMonitorStatus?)null);

        // Act
        var act = async () => await _handler.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"*MsSqlNativeLogShippingMonitorStatus*{query.InfraObjectId}*");
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsDeactivated()
    {
        // Arrange
        var query = new GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQuery { InfraObjectId = "789" };

        var deactivatedEntity = new Domain.Entities.MsSqlNativeLogShippingMonitorStatus
        {
            Id = 2,
            IsActive = false,
            ReferenceId = "ref-789"
        };

        _repositoryMock.Setup(r =>
            r.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(query.InfraObjectId))
            .ReturnsAsync(deactivatedEntity);

        // Act
        var act = async () => await _handler.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"*MsSqlNativeLogShippingMonitorStatus*{query.InfraObjectId}*");
    }
}