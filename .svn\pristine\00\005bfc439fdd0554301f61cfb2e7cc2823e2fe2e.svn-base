﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Domain.ViewModels.OracleRACMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;

public class GetOracleMonitorStatusPaginatedListQueryHandler : IRequestHandler<GetOracleMonitorStatusPaginatedListQuery,
    PaginatedResult<OracleMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IOracleMonitorStatusRepository _oracleMonitorStatusRepository;

    public GetOracleMonitorStatusPaginatedListQueryHandler(IOracleMonitorStatusRepository oracleMonitorStatusRepository,
        IMapper mapper)
    {
        _oracleMonitorStatusRepository = oracleMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<OracleMonitorStatusListVm>> Handle(
        GetOracleMonitorStatusPaginatedListQuery request, CancellationToken cancellationToken)
    {
        //var queryable = _oracleMonitorStatusRepository.GetPaginatedQuery();

        var productFilterSpec = new OracleMonitorStatusFilterSpecification(request.SearchString);

        //var monitorLog = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<OracleMonitorStatusListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);
        var queryable = await _oracleMonitorStatusRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var monitorLog = _mapper.Map<PaginatedResult<OracleMonitorStatusListVm>>(queryable);

        return monitorLog;
    }
}