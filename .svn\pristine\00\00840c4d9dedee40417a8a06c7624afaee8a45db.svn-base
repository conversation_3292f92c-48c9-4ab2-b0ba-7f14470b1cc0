﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class GroupPolicyRepositoryMocks
{
    public static Mock<IGroupPolicyRepository> CreateGroupPolicyRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(groupPolicies);

        mockGroupPolicyRepository.Setup(repo => repo.AddAsync(It.IsAny<GroupPolicy>())).ReturnsAsync(
            (GroupPolicy groupPolicy) =>
            {
                groupPolicy.Id = new Fixture().Create<int>();

                groupPolicy.ReferenceId = new Fixture().Create<Guid>().ToString();

                groupPolicies.Add(groupPolicy);

                return groupPolicy;
            });

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> UpdateGroupPolicyRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(groupPolicies);

        mockGroupPolicyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => groupPolicies.SingleOrDefault(x => x.ReferenceId == i));

        mockGroupPolicyRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GroupPolicy>())).ReturnsAsync((GroupPolicy groupPolicy) =>
        {
            var index = groupPolicies.FindIndex(item => item.ReferenceId == groupPolicy.ReferenceId);

            groupPolicies[index] = groupPolicy;

            return groupPolicy;
        });

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> DeleteGroupPolicyRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(groupPolicies);

        mockGroupPolicyRepository.Setup(repo => repo.GetGroupPolicyByLoadBalancerId(It.IsAny<string>())).ReturnsAsync(groupPolicies);

        mockGroupPolicyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => groupPolicies.SingleOrDefault(x => x.ReferenceId == i));

        mockGroupPolicyRepository.Setup(repo => repo.UpdateAsync(It.IsAny<GroupPolicy>())).ReturnsAsync((GroupPolicy groupPolicy) =>
        {
            var index = groupPolicies.FindIndex(item => item.ReferenceId == groupPolicy.ReferenceId);

            if (index >= 0)
            {
                groupPolicy.IsActive = false;
                groupPolicies[index] = groupPolicy;
            }

            return groupPolicy;
        });

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> GetGroupPolicyRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(groupPolicies);

        mockGroupPolicyRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => groupPolicies.SingleOrDefault(x => x.ReferenceId == i));

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> GetGroupPolicyNamesRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.GetGroupPolicyNames()).ReturnsAsync(groupPolicies);

        return mockGroupPolicyRepository;
    }


    public static Mock<IGroupPolicyRepository> GetGroupPolicyNameUniqueRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.IsGroupPolicyNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => groupPolicies.Exists(x => x.GroupName == i && x.ReferenceId == j));

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> GetGroupPolicyEmptyRepository()
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        mockGroupPolicyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<GroupPolicy>());

        return mockGroupPolicyRepository;
    }

    public static Mock<IGroupPolicyRepository> GetPaginatedGroupPolicyRepository(List<GroupPolicy> groupPolicies)
    {
        var mockGroupPolicyRepository = new Mock<IGroupPolicyRepository>();

        var queryableGroupPolicy = groupPolicies.BuildMock();

        mockGroupPolicyRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableGroupPolicy);

        return mockGroupPolicyRepository;
    }
}