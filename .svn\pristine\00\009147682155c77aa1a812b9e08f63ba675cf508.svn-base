﻿let rtoChart, rtoCategoryAxis, rtoValueAxis, rtoSeries1, rtoSeries2, rtoLabel
function RtoSummary(valueDatarto) {

    const timeConverterRto = (time, mode) => {
        if (!time) {
            return mode === 'chart' ? 0 : '00:00';
        }

        let splitTimeWithDay = '';
        let splitTime = '';
        if (time?.includes('.')) {
            splitTimeWithDay = time?.replace('+', '')?.split('.')[0];
            splitTime = time?.split('.')[1];
        } else {
            splitTime = time?.replace('+', '');
        }

        let getTime = splitTime?.split(':');
        if (mode === 'chart') {
            let totalMinutes = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24 * 60) + (+getTime[0] * 60) + +getTime[1];
            return totalMinutes;
        } else {
            let totalHours = ((splitTimeWithDay ? +splitTimeWithDay : 0) * 24) + +getTime[0] + Math.trunc(+getTime[1] / 60);
            return splitTimeWithDay ? `${totalHours} hours` : `${+getTime[0]} hours ${+getTime[1]} min`;
        }

    }

    // Themes end
    let estimatedRTO;
    let configuredRTO;
    // Create chart instance
    if (!rtoChart) {
        rtoChart = am4core.create("RTOSummary", am4charts.RadarChart);
        if (rtoChart.logo) {
            rtoChart.logo.disabled = true;
        }

        // Make chart not full circle
        rtoChart.startAngle = -90;
        rtoChart.endAngle = 180;
        rtoChart.innerRadius = am4core.percent(50);

        // Set number format
        rtoChart.numberFormatter.numberFormat = "#.#'%'";

        // Change the padding values
        rtoChart.padding(-10, -10, -10, -10)
        rtoChart.defaultState.transitionDuration = 1000; 

        // Create axes
        rtoCategoryAxis = rtoChart.yAxes.push(new am4charts.CategoryAxis());
        rtoCategoryAxis.renderer.grid.template.location = 0;
        rtoCategoryAxis.renderer.grid.template.strokeOpacity = 0;
        rtoCategoryAxis.renderer.labels.template.fontWeight = 300;
        rtoCategoryAxis.renderer.labels.template.fontSize = 9;
        rtoCategoryAxis.renderer.labels.template.disabled = true;
        rtoCategoryAxis.renderer.minGridDistance = 10;
        rtoCategoryAxis.renderer.cellStartLocation = 0.5;
        rtoCategoryAxis.renderer.cellEndLocation = 1;

        rtoValueAxis = rtoChart.xAxes.push(new am4charts.ValueAxis());
        rtoValueAxis.renderer.grid.template.strokeOpacity = 0;
        rtoValueAxis.strictMinMax = true;
        rtoValueAxis.renderer.labels.template.disabled = true;

        rtoSeries1 = rtoChart.series.push(new am4charts.RadarColumnSeries());
        rtoSeries1.clustered = false;
        rtoSeries1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");
        rtoSeries1.columns.template.fillOpacity = 0.08;
        rtoSeries1.columns.template.cornerRadiusTopLeft = 20;
        rtoSeries1.columns.template.strokeWidth = 0;
        rtoSeries1.columns.template.radarColumn.cornerRadius = 20;
        rtoSeries1.defaultState.transitionDuration = 1000;

        rtoSeries2 = rtoChart.series.push(new am4charts.RadarColumnSeries());
        rtoSeries2.clustered = false;
        rtoSeries2.columns.template.strokeWidth = 0;
        rtoSeries2.columns.template.radarColumn.cornerRadius = 20;
        rtoSeries2.defaultState.transitionDuration = 1000;

        rtoLabel = rtoChart.seriesContainer.createChild(am4core.Label);
        rtoLabel.text = "[bold]RTO[/]";
        rtoLabel.horizontalCenter = "middle";
        rtoLabel.verticalCenter = "middle";
        rtoLabel.fontSize = 12;
    }
    // Add data
    let currentRTO = (valueDatarto?.currentRTO !== "NA" && valueDatarto?.currentRTO !== "0" && valueDatarto?.currentRTO !== "" && valueDatarto?.currentRTO !== null && valueDatarto?.currentRTO !== undefined) ? `${valueDatarto?.currentRTO}` : '00:00'
    configuredRTO = (valueDatarto?.configuredRTO !== "" && valueDatarto?.configuredRTO !== null && valueDatarto?.configuredRTO !== undefined) ? `${valueDatarto?.configuredRTO}` : 'NA'
    estimatedRTO = (valueDatarto?.estimatedRTO !== "" && valueDatarto?.estimatedRTO !== null && valueDatarto?.estimatedRTO !== undefined) ? `${valueDatarto?.estimatedRTO}` : 'NA'
  
    rtoChart.data = [{
        "category": "Computed " + timeConverterRto(valueDatarto?.currentRTO, 'value'),
        "value": timeConverterRto(valueDatarto?.currentRTO, 'chart'),
        "full": Number(configuredRTO)
    },
        {
        "category": "Estimated " + estimatedRTO + " Min",
        "value": estimatedRTO,
        "full": Number(configuredRTO)
    } , {
        "category": "Agreed " + configuredRTO + " Min",
        "value": configuredRTO,
        "full": Number(configuredRTO)
    }];
   
    let colorrtovalue = +timeConverterRto(valueDatarto?.currentRTO, "chart") < +estimatedRTO ? am4core.color("#41c200") : +timeConverterRto(valueDatarto?.currentRTO, 'chart') > +configuredRTO ? am4core.color("#dc3545") : (+estimatedRTO < +timeConverterRto(valueDatarto?.currentRTO, 'chart') && +timeConverterRto(valueDatarto?.currentRPO, "chart") < +configuredRTO) ? am4core.color("#ff9632") : am4core.color("#e0e0e0")
    
    rtoChart.colors.list = [
        
        colorrtovalue,
        am4core.color("#07cedb"),
        am4core.color("#946eff")
    ];

    // Create axes
    rtoCategoryAxis.dataFields.category = "category";
    rtoCategoryAxis.renderer.labels.template.horizontalCenter = "right";
    rtoCategoryAxis.renderer.labels.template.adapter.add("fill", function (fill, target) {
        return (target.dataItem.index >= 0) ? chart.colors.getIndex(target.dataItem.index) : fill;
    });

    rtoValueAxis.min = 0;
    rtoValueAxis.max = Number(configuredRTO);

    // Create series
    rtoSeries1.dataFields.valueX = "full";
    rtoSeries1.dataFields.categoryY = "category";

    rtoSeries2.dataFields.valueX = "value";
    rtoSeries2.dataFields.categoryY = "category";
    rtoSeries2.columns.template.tooltipText = "{category}";

    rtoSeries2.columns.template.adapter.add("fill", function (fill, target) {
        return chart.colors.getIndex(target.dataItem.index);
    });
}