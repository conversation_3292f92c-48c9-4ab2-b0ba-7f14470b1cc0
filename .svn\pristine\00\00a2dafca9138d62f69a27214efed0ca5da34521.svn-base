﻿namespace ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;

public class
    GetDrReadyByBusinessServiceIdQueryHandler : IRequestHandler<GetDrReadyByBusinessServiceIdQuery,
        DrReadyByBusinessServiceIdVm>
{
    private readonly IDrReadyRepository _drReadyRepository;
    private readonly IMapper _mapper;

    public GetDrReadyByBusinessServiceIdQueryHandler(IMapper mapper, IDrReadyRepository drReadyRepository)
    {
        _mapper = mapper;
        _drReadyRepository = drReadyRepository;
    }

    public async Task<DrReadyByBusinessServiceIdVm> Handle(GetDrReadyByBusinessServiceIdQuery request,
        CancellationToken cancellationToken)
    {
        if (request.BusinessServiceId.IsNotNullOrWhiteSpace())
        {
            var drReadyDto = await _drReadyRepository.GetDrReadyByBusinessServiceId(request.BusinessServiceId);

            Guard.Against.NullOrDeactive(drReadyDto, nameof(Domain.Entities.DrReady),
                new NotFoundException(nameof(Domain.Entities.DrReady), request.BusinessServiceId));

            return _mapper.Map<DrReadyByBusinessServiceIdVm>(drReadyDto);
        }
        else
        {
            var drReadyDto = await _drReadyRepository.ListAllAsync();

            return _mapper.Map<DrReadyByBusinessServiceIdVm>(drReadyDto);
        }
    }
}