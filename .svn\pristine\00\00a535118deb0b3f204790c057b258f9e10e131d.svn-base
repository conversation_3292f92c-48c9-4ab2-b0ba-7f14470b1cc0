﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ReplicationJobFilterSpecification : Specification<ReplicationJob>
{
    public ReplicationJobFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("templatename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.TemplateName.Contains(stringItem.Replace("templatename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("nodename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.NodeName.Contains(stringItem.Replace("nodename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("solutiontype=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.SolutionType.Contains(stringItem.Replace("solutiontype=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("status=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Status.Contains(stringItem.Replace("status=", "",
                            StringComparison.InvariantCultureIgnoreCase)));

                    else if (stringItem.Contains("state=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.State.StartsWith(stringItem.Replace("state=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.NodeName.Contains(searchString) ||
                    p.Status.Contains(searchString) || p.State.Contains(searchString) ||
                    p.TemplateName.Contains(searchString) || p.SolutionType.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}