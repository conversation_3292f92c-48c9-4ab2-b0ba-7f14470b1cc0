using ContinuityPatrol.Application.Features.RsyncOption.Events.Update;

namespace ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;

public class UpdateRsyncOptionCommandHandler : IRequestHandler<UpdateRsyncOptionCommand, UpdateRsyncOptionResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IRsyncOptionRepository _rsyncOptionRepository;

    public UpdateRsyncOptionCommandHandler(IMapper mapper, IRsyncOptionRepository rsyncOptionRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _rsyncOptionRepository = rsyncOptionRepository;
        _publisher = publisher;
    }

    public async Task<UpdateRsyncOptionResponse> Handle(UpdateRsyncOptionCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _rsyncOptionRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.RsyncOption), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateRsyncOptionCommand), typeof(Domain.Entities.RsyncOption));

        await _rsyncOptionRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateRsyncOptionResponse
        {
            Message = Message.Update("Rsync Options", eventToUpdate.Name),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new RsyncOptionUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}