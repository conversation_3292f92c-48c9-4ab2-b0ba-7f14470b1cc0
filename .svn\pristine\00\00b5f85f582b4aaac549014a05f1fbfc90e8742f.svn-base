﻿using ContinuityPatrol.Application.Features.AlertMaster.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertMaster.Events;

public class AlertMasterPaginatedEventTests : IClassFixture<AlertMasterFixture>, IClassFixture<UserActivityFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly AlertMasterPaginatedEventHandler _handler;

    public AlertMasterPaginatedEventTests(AlertMasterFixture alertMasterFixture, UserActivityFixture userActivityFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockAlertMasterPaginatedEventLogger = new Mock<ILogger<AlertMasterPaginatedEventHandler>>();

        _mockUserActivityRepository = new Mock<IUserActivityRepository>();

        _mockUserActivityRepository =
            AlertMasterRepositoryMocks.CreateAlertMasterEventRepository(_userActivityFixture.UserActivities);

        _handler = new AlertMasterPaginatedEventHandler(mockLoggedInUserService.Object,
            mockAlertMasterPaginatedEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_PaginateAlertMasterEventPaginated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_alertMasterFixture.AlertMasterPaginatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_alertMasterFixture.AlertMasterPaginatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}