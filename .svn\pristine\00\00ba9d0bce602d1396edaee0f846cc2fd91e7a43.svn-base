using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberAirGapLogsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<List<CyberAirGapLogListVm>>> GetCyberAirGapLogs()
    {
        Logger.LogDebug("Get All CyberAirGapLogs");

        return Ok(await Mediator.Send(new GetCyberAirGapLogListQuery()));
    }

    [HttpGet("{id}", Name = "GetCyberAirGapLog")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberAirGapLogDetailVm>> GetCyberAirGapLogById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGapLog Id");

        Logger.LogDebug($"Get CyberAirGapLog Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberAirGapLogDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Cyber.View)]
 public async Task<ActionResult<PaginatedResult<CyberAirGapLogListVm>>> GetPaginatedCyberAirGapLogs([FromQuery] GetCyberAirGapLogPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in CyberAirGapLog Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Cyber.Create)]
    public async Task<ActionResult<CreateCyberAirGapLogResponse>> CreateCyberAirGapLog([FromBody] CreateCyberAirGapLogCommand createCyberAirGapLogCommand)
    {
        Logger.LogDebug($"Create CyberAirGapLog '{createCyberAirGapLogCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateCyberAirGapLog), await Mediator.Send(createCyberAirGapLogCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Cyber.Edit)]
    public async Task<ActionResult<UpdateCyberAirGapLogResponse>> UpdateCyberAirGapLog([FromBody] UpdateCyberAirGapLogCommand updateCyberAirGapLogCommand)
    {
        Logger.LogDebug($"Update CyberAirGapLog '{updateCyberAirGapLogCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateCyberAirGapLogCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Cyber.Delete)]
    public async Task<ActionResult<DeleteCyberAirGapLogResponse>> DeleteCyberAirGapLog(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberAirGapLog Id");

        Logger.LogDebug($"Delete CyberAirGapLog Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteCyberAirGapLogCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsCyberAirGapLogNameExist(string cyberAirGapLogName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(cyberAirGapLogName, "CyberAirGapLog Name");

     Logger.LogDebug($"Check Name Exists Detail by CyberAirGapLog Name '{cyberAirGapLogName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetCyberAirGapLogNameUniqueQuery { Name = cyberAirGapLogName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


