using AutoFixture;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Delete;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetList;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNames;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.PluginManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PluginManagerFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<PluginManager> PluginManagers { get; set; }
    public List<PluginManager> InvalidPluginManagers { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<PluginManagerListVm> PluginManagerListVm { get; }
    public PluginManagerDetailVm PluginManagerDetailVm { get; }
    public PaginatedResult<PluginManagerListVm> PluginManagerPaginatedListVm { get; }
    public List<PluginManagerNameVm> PluginManagerNameListVm { get; }

    // Commands
    public CreatePluginManagerCommand CreatePluginManagerCommand { get; set; }
    public UpdatePluginManagerCommand UpdatePluginManagerCommand { get; set; }
    public DeletePluginManagerCommand DeletePluginManagerCommand { get; set; }

    // Queries
    public GetPluginManagerDetailQuery GetPluginManagerDetailQuery { get; set; }
    public GetPluginManagerListQuery GetPluginManagerListQuery { get; set; }
    public GetPluginManagerPaginatedListQuery GetPluginManagerPaginatedListQuery { get; set; }
    public GetPluginManagerNameQuery GetPluginManagerNameQuery { get; set; }
    public GetPluginManagerNameUniqueQuery GetPluginManagerNameUniqueQuery { get; set; }

    // Responses
    public CreatePluginManagerResponse CreatePluginManagerResponse { get; set; }
    public UpdatePluginManagerResponse UpdatePluginManagerResponse { get; set; }
    public DeletePluginManagerResponse DeletePluginManagerResponse { get; set; }

    public PluginManagerFixture()
    {
        try
        {
            // Create test data using AutoFixture
            PluginManagers = AutoPluginManagerFixture.Create<List<PluginManager>>();
            InvalidPluginManagers = AutoPluginManagerFixture.Create<List<PluginManager>>();
            UserActivities = AutoPluginManagerFixture.Create<List<UserActivity>>();

            // Set invalid plugin managers to inactive
            foreach (var invalidPlugin in InvalidPluginManagers)
            {
                invalidPlugin.IsActive = false;
            }

            // Commands
            CreatePluginManagerCommand = AutoPluginManagerFixture.Create<CreatePluginManagerCommand>();
            UpdatePluginManagerCommand = AutoPluginManagerFixture.Create<UpdatePluginManagerCommand>();
            DeletePluginManagerCommand = AutoPluginManagerFixture.Create<DeletePluginManagerCommand>();

            // Set command IDs to match existing entities
            if (PluginManagers.Any())
            {
                UpdatePluginManagerCommand.Id = PluginManagers.First().ReferenceId;
                DeletePluginManagerCommand.Id = PluginManagers.First().ReferenceId;
            }

            // Queries
            GetPluginManagerDetailQuery = AutoPluginManagerFixture.Create<GetPluginManagerDetailQuery>();
            GetPluginManagerListQuery = AutoPluginManagerFixture.Create<GetPluginManagerListQuery>();
            GetPluginManagerPaginatedListQuery = AutoPluginManagerFixture.Create<GetPluginManagerPaginatedListQuery>();
            GetPluginManagerNameQuery = AutoPluginManagerFixture.Create<GetPluginManagerNameQuery>();
            GetPluginManagerNameUniqueQuery = AutoPluginManagerFixture.Create<GetPluginManagerNameUniqueQuery>();

            // Set query IDs to match existing entities
            if (PluginManagers.Any())
            {
                GetPluginManagerDetailQuery.Id = PluginManagers.First().ReferenceId;
                GetPluginManagerNameUniqueQuery.Name = PluginManagers.First().Name;
            }

            // Responses
            CreatePluginManagerResponse = AutoPluginManagerFixture.Create<CreatePluginManagerResponse>();
            UpdatePluginManagerResponse = AutoPluginManagerFixture.Create<UpdatePluginManagerResponse>();
            DeletePluginManagerResponse = AutoPluginManagerFixture.Create<DeletePluginManagerResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            PluginManagers = new List<PluginManager>();
            InvalidPluginManagers = new List<PluginManager>();
            UserActivities = new List<UserActivity>();
            CreatePluginManagerCommand = new CreatePluginManagerCommand();
            UpdatePluginManagerCommand = new UpdatePluginManagerCommand();
            DeletePluginManagerCommand = new DeletePluginManagerCommand();
            GetPluginManagerDetailQuery = new GetPluginManagerDetailQuery();
            GetPluginManagerListQuery = new GetPluginManagerListQuery();
            GetPluginManagerPaginatedListQuery = new GetPluginManagerPaginatedListQuery();
            GetPluginManagerNameQuery = new GetPluginManagerNameQuery();
            GetPluginManagerNameUniqueQuery = new GetPluginManagerNameUniqueQuery();
            CreatePluginManagerResponse = new CreatePluginManagerResponse();
            UpdatePluginManagerResponse = new UpdatePluginManagerResponse();
            DeletePluginManagerResponse = new DeletePluginManagerResponse();
        }

        // Configure View Models
        PluginManagerListVm = new List<PluginManagerListVm>
        {
            new PluginManagerListVm
            {
                Id = "PM_001",
                CompanyId = "COMP_001",
                Name = "Database Backup Plugin",
                Properties = "{\"type\": \"backup\", \"database\": \"postgresql\", \"schedule\": \"daily\", \"compression\": true}",
                Description = "Automated database backup plugin with compression support",
                Version = "2.1.0"
            },
            new PluginManagerListVm
            {
                Id = "PM_002",
                CompanyId = "COMP_001",
                Name = "File Replication Plugin",
                Properties = "{\"type\": \"replication\", \"source\": \"local\", \"target\": \"remote\", \"encryption\": true}",
                Description = "Secure file replication plugin with encryption",
                Version = "1.5.3"
            },
            new PluginManagerListVm
            {
                Id = "PM_003",
                CompanyId = "COMP_001",
                Name = "Monitoring Alert Plugin",
                Properties = "{\"type\": \"monitoring\", \"alerts\": [\"email\", \"sms\"], \"threshold\": \"critical\"}",
                Description = "Multi-channel monitoring and alerting plugin",
                Version = "3.0.1"
            },
            new PluginManagerListVm
            {
                Id = "PM_004",
                CompanyId = "COMP_002",
                Name = "Security Audit Plugin",
                Properties = "{\"type\": \"security\", \"audit\": \"compliance\", \"reports\": \"weekly\", \"standards\": [\"SOX\", \"GDPR\"]}",
                Description = "Comprehensive security auditing and compliance plugin",
                Version = "4.2.0"
            },
            new PluginManagerListVm
            {
                Id = "PM_005",
                CompanyId = "COMP_001",
                Name = "Data Migration Plugin",
                Properties = "{\"type\": \"migration\", \"source\": \"legacy\", \"target\": \"cloud\", \"validation\": true}",
                Description = "Enterprise data migration plugin with validation",
                Version = "1.8.2"
            }
        };

        PluginManagerDetailVm = new PluginManagerDetailVm
        {
            Id = "PM_001",
            Name = "Database Backup Plugin",
            Properties = "{\"type\": \"backup\", \"database\": \"postgresql\", \"schedule\": \"daily\", \"compression\": true}",
            Description = "Automated database backup plugin with compression support",
            Version = "2.1.0"
        };

        PluginManagerPaginatedListVm = new PaginatedResult<PluginManagerListVm>
        {
            Data = PluginManagerListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        PluginManagerNameListVm = new List<PluginManagerNameVm>
        {
            new PluginManagerNameVm { Id = "PM_001", Name = "Database Backup Plugin" },
            new PluginManagerNameVm { Id = "PM_002", Name = "File Replication Plugin" },
            new PluginManagerNameVm { Id = "PM_003", Name = "Monitoring Alert Plugin" },
            new PluginManagerNameVm { Id = "PM_004", Name = "Security Audit Plugin" },
            new PluginManagerNameVm { Id = "PM_005", Name = "Data Migration Plugin" }
        };

        // Configure AutoMapper for PluginManager mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PluginManagerProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoPluginManagerFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize PluginManager entity
            fixture.Customize<PluginManager>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Plugin")
                .With(b => b.CompanyId, "COMP_TEST")
                .With(b => b.Properties, "{\"type\": \"test\", \"status\": \"active\"}")
                .With(b => b.Description, "Test plugin description")
                .With(b => b.Version, "1.0.0"));

            // Customize CreatePluginManagerCommand
            fixture.Customize<CreatePluginManagerCommand>(c => c
                .With(b => b.Name, "New Test Plugin")
                .With(b => b.CompanyId, "COMP_NEW")
                .With(b => b.Properties, "{\"type\": \"new\", \"status\": \"active\"}")
                .With(b => b.Description, "New test plugin description")
                .With(b => b.Version, "1.0.0"));

            // Customize UpdatePluginManagerCommand
            fixture.Customize<UpdatePluginManagerCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Plugin")
                .With(b => b.Properties, "{\"type\": \"updated\", \"status\": \"active\"}")
                .With(b => b.Description, "Updated test plugin description")
                .With(b => b.Version, "2.0.0"));

            // Customize DeletePluginManagerCommand
            fixture.Customize<DeletePluginManagerCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetPluginManagerDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetPluginManagerPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetPluginManagerNameUniqueQuery>(c => c
                .With(b => b.Name, "Test Plugin")
                .With(b => b.PluginId, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreatePluginManagerResponse>(c => c
                .With(b => b.PluginId, Guid.NewGuid().ToString())
                .With(b => b.Message, "PluginManager has been created successfully"));

            fixture.Customize<UpdatePluginManagerResponse>(c => c
                .With(b => b.PluginId, Guid.NewGuid().ToString())
                .With(b => b.Message, "PluginManager has been updated successfully"));

            fixture.Customize<DeletePluginManagerResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "PluginManager has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "PluginManager")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
