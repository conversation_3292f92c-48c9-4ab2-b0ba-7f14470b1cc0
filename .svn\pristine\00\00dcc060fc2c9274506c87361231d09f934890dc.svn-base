﻿namespace ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;

public class GetLicenseManagerDetailQueryHandler : IRequestHandler<GetLicenseManagerDetailQuery, LicenseManagerDetailVm>
{
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;

    public GetLicenseManagerDetailQueryHandler(IMapper mapper, ILicenseManagerRepository licenseManagerRepository)
    {
        _mapper = mapper;
        _licenseManagerRepository = licenseManagerRepository;
    }

    public async Task<LicenseManagerDetailVm> Handle(GetLicenseManagerDetailQuery request,
        CancellationToken cancellationToken)
    {
        var licenseManager = await _licenseManagerRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(licenseManager, nameof(Domain.Entities.LicenseManager),
            new NotFoundException(nameof(Domain.Entities.LicenseManager), request.Id));

        var licenseDetail = _mapper.Map<LicenseManagerDetailVm>(licenseManager);

        return licenseDetail ?? throw new NotFoundException(nameof(Domain.Entities.LicenseManager), request.Id);
    }
}