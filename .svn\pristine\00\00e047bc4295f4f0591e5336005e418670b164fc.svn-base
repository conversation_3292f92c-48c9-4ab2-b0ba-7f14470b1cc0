﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

@Html.AntiForgeryToken()

<link href="~/css/dashboard.css" rel="stylesheet" />
<div class="page-content">
    <div class="header pb-2">
        <h6 class="page_title"><i class="cp-drift-configuration"></i><span>Drift Dashboard</span></h6> 
    </div>
    <div class="row g-3" style="height: calc(100vh - 87px);">
        <div class="col-12 d-grid" style="grid-template-rows: max-content;">
            <div class="row g-3">
                <div class="col-12 col-xl-8">
                   <div class="row g-3">
                        <div class="col-12">
                            <div class="card Card_Design_None mb-0">
                                <div class="card-header header p-2 mb-2 gap-2">
                                    <span class="fw-bold">Operational Service Summary</span>
                                    <button class="btn btn-primary btn-sm driftDetailModel px-1 py-0 rounded-1" data-bs-toggle="modal" data-bs-target="#staticBackdrop">Detail View</button>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row row-cols-3 g-3 align-items-center h-100">
                                        <div class="col mt-2">
                                            <div class="d-flex">
                                                <div class="position-relative impactDetails" role="button" type="Server">
                                                    <img class="me-1" src="/img/charts_img/datacenter/operational_service.svg" type="Server" height="30" loading="lazy" alt="operational_service" />
                                                </div>
                                                <span>
                                                    <span class="fs-7 text-muted fw-semibold">Total Operational Services</span>
                                                    <span class="d-flex align-items-center serverdone">
                                                        <span class="fw-semibold fs-4 totalOperationalServices">0</span> <br>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col mt-2">
                                            <div class="d-flex">
                                                <div class="position-relative impactDetails" role="button" type="Database">
                                                    <img class="me-1" src="/img/charts_img/datacenter/non_conflicted.svg" type="Database" height="30" loading="lazy" alt="non_conflicted" />
                                                </div>
                                                <span>
                                                    <span class="fs-7 text-muted fw-semibold">Non Conflicted</span>
                                                    <span class="d-flex align-items-center databasedone">
                                                        <span class="fw-semibold fs-4 nonConflictedServices">0</span><span class="mx-2 fs-8 nonConflictedCount align-middle bg-success-subtle text-success badge">0%</span>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col mt-2">
                                            <div class="d-flex">
                                                <div class="position-relative impactDetails" role="button" type="Replication">
                                                    <img class="me-1" src="/img/charts_img/datacenter/conflicted.svg" type="Replication" height="30" loading="lazy" alt="conflicted" />
                                                </div>
                                                <span>
                                                    <span class="fs-7 text-muted fw-semibold">Conflicted</span>
                                                    <span class="d-flex align-items-center replicationdone">
                                                        <span class="fw-semibold fs-4 conflictedServices">0</span><span class="mx-2 fs-8 conflictedCount align-middle bg-danger-subtle text-danger badge">0%</span>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card Card_Design_None h-100" style="max-height: -webkit-fill-available;">
                                <div class="card-header header p-2 gap-2">
                                    <span class="fw-bold">Operational Service Conflict Score</span>
                                </div>
                                <div class="card-body py-0">
                                    <div class="d-flex h-100">
                                        <div id="ResourceConflictScore" style="height:100%; width:100%;"></div>
                                        <div class="align-self-center">
                                            <div>
                                                <span><small class="cp-single-dot me-1" style="color:#695edc;"></small>Non&nbsp;conflicted&nbsp;</span>
                                                <span class="px-3 nonConflictpercent fs-6 d-block fw-bold"></span>
                                            </div>
                                            <div>
                                                <span>
                                                    <small class="cp-single-dot me-1" style="color:#dd1d3e"></small>Conflicted&nbsp;
                                                </span>
                                                <span class="px-3 Conflictpercent fs-6 d-block fw-bold"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="card Card_Design_None h-100" style="max-height: -webkit-fill-available;">
                                <div class="card-header header p-2 gap-2">
                                    <span class="card-title">Baseline vs Conflicts</span>
                                </div>
                                <div class="card-body p-0">
                                    <div id="BusinessServiceHealthSummaryChart" style="width:100%; height:100%;"></div>
                                </div>
                            </div>
                        </div>
                   </div>
                </div>
                <div class="col-4">
                    <div class="card Card_Design_None">
                        <div class="card-header header p-2 gap-2">
                            <span class="fw-bold">Resource Status</span>
                            <span role="button" title="Resource Status" class="ms-1 cursor-pointer text-secondary driftResourceModel" status="all"> <i class="cp-full-screen "></i></span>

                        </div>
                        <div class="card-body pt-1 d-grid">
                            <div class="row row-cols-1 row-cols-lg-2 g-3 mb-3">
                                <div class="col">
                                    <div class="Drift_Blue_Gradient h-100">
                                        <div class="p-3 d-grid h-100">
                                            <span class="fs-5 text-end totalResources">0</span>
                                            <span class=""><i class="fs-4 cp-business-view-dashboard-icon"></i></span>
                                            <span class="fs-5">Total Resources</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="Drift_Green_Gradient h-100">
                                        <div class="p-3 d-grid h-100">
                                            <span class="fs-5 text-end driftEnabledResources">0</span>
                                            <span class="mt-2"><i class="fs-4 cp-drift-parameter-list"></i></span>
                                            <span class="fs-5">Drift Enabled</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row row-cols-1 row-cols-lg-2 g-3">
                                <div class="col">
                                    <div class="Drift_Pink_Gradient h-100">
                                        <div class="p-3 d-grid h-100">
                                            <span class="fs-5 text-end nonConflictedResources"  status="False">0</span>
                                            <span class=""><i class="fs-4 cp-success"></i></span>
                                            <span class="fs-5">Non conflicted</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="Drift_Orange_Gradient h-100">
                                        <div class="p-3 d-grid h-100">
                                            <span class="fs-5 text-end conflictedResources  driftResourceModel" role="button" status="True">0</span>
                                            <span class=""><i class="fs-4 cp-warning"></i></span>
                                            <span class="fs-5">Conflicted</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                 <div class='Notification'>
                         <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
                              <div class='d-flex'>
                                       <div class='toast-body'>
                                           <span id="alertClass" class='success-toast'>
                                            <i id="icon" class='cp-check toast_icon'></i>
                                            </span>
                                           <span id="message">
                                              
                                           </span>
                                      </div>
                                     <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
                              </div>
                         </div>
                 </div>
            </div>
 
            <div class="row g-3">
                <div class="col">
                    <div class="card Card_Design_None h-100 mb-0" style="max-height: -webkit-fill-available;">
                        <div id="carouselExampleIndicators" class="carousel slide" data-bs-ride="carousel">
                            <div class="card-header pb-0 card-title">
                                Resource Summary
                            </div>
                            <div class="card-body p-2">
                                <div class="carousel-inner">
                                    <div class="carousel-item active">
                                        <div class="card Card_Design_None mb-0">
                                            <div class="card-header pt-0 px-0 card-title"><i class="cp-server me-2"></i>Server<span class="fw-semibold mx-2 totalServerCount">0</span><span class="align-middle bg-danger-subtle text-danger badge totalServerConflictedCount">0 Conflicted</span></div>
                                            <div class="card-body p-0 class" style="height:120px; overflow-y:auto;">
                                                <ul class="list-group serverResourceSummary">
                                                    
                                                </ul>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="carousel-item">
                                        <div class="card Card_Design_None mb-0">
                                            <div class="card-header fw-bold pt-0 px-0 card-title"><i class="cp-database me-2"></i>Database<span class="fw-semibold mx-2 totalDatabaseCount">0</span><span class="align-middle bg-danger-subtle text-danger badge totalDatabaseConflictedCount">0 Conflicted</span></div>
                                            <div class="card-body p-0 class" style="height:120px; overflow-y:auto;">
                                                <ul class="list-group databaseResourceSummary">
                                                   

                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="carousel-item">
                                        <div class="card Card_Design_None mb-0">
                                            <div class="card-header fw-bold pt-0 px-0 card-title"><i class="cp-application me-2"></i>Application<span class="fw-semibold mx-2 totalApplicationCount">0</span><span class="align-middle bg-danger-subtle text-danger badge totalApplicationConflictedCount">0 Conflicted</span></div>
                                            <div class="card-body p-0 class" style="height:120px; overflow-y:auto;">
                                                <ul class="list-group  applicationResourceSummary">
                                                  
                                                </ul>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="carousel-indicators mt-0">
                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="0" class="bg-primary active" aria-current="true" aria-label="Slide 1"></button>
                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="1" class="bg-primary" aria-label="Slide 2"></button>
                                    <button type="button" data-bs-target="#carouselExampleIndicators" data-bs-slide-to="2" class="bg-primary" aria-label="Slide 3"></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card Card_Design_None h-100 mb-0" style="max-height: -webkit-fill-available;">
                        <div class="card-header header p-2 gap-2">
                            <span class="fw-bold">Conflict Overview<small class="text-secondary ms-2 fw-semibold">( Last 07 Days )</small></span>
                        </div>
                        <div class="card-body ps-0">
                            <div id="ServiceAnalyticsChart" class="text-center" style="width:100%; height:100%;"></div>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card Card_Design_None h-100 mb-0">
                        <div class="card-header header p-2 gap-2">
                            <span class="fw-bold">Mismatch Categories</span>
                            <span role="button" title="Mismatch Categories" class="ms-1 cursor-pointer text-secondary driftMismatchModel" status="all"> <i class="cp-full-screen "></i></span>
                        </div>
                        <div class="card-body pt-0">
                            <div id="ComponentFailure" style="height:100%; width:100%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog  modal-dialog-scrollable mb-0 modal-dialog-centered" style="max-width:1000px">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-drift-configuration"></i><span>Drift Detailed View</span></h6>
                <div class="d-flex align-items-center"><button type="button" id="BtnBusinessViewDownload" class="d-flex btn btn-sm btn-primary fs-10 p-1 " title="Report"><i class="cp-donut-chart me-1 fs-7"></i>Report</button>
                    <button type="button" class="btn-close ms-2 " data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
                </div>
            </div>
            <div class="modal-body py-1" style="max-height: calc(100vh - 69px); overflow-y: auto;">
                <div class="row g-3">
                    <div class="col-12 col-lg-3 d-grid" style="z-index: 9999;">
                        <div class="card shadow-sm">
                            <div class="card-title card-header p-2 bg-transparent border-0">Operational Services</div>
                        <div class="card-body p-2">
                                <div class="input-group mb-3 w-auto">
                                   @*  <div class="input-group-text"> *@
                                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off">
                                    
                                   @*  </div> *@
                                   @*  <div class="input-group-text px-2"><span role="button" id="collapse"><i title="Collapse" class="fs-6 text-primary cp-circle-downarrow"></i></span></div> *@
                                    <div class="input-group-text">
                                        <div class="dropdown">
                                            <i type="button" class="cp-filter" title="Filter" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside"></i>
                                            <form class="dropdown-menu p-0">
                                                <div class="accordion accordion-flush filter-accordion" id="accordionFlushExample">
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header">
                                                            <button id="btnAll" class="btn btn-sm btn-tresprent collapsed fw-bold  filterItem" type="button" category="all" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                                All
                                                            </button>
                                                        </h2>
                                                        <div id="flush-collapseOne" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                            <div class="accordion-body p-0">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header">
                                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                                                                Status
                                                            </button>
                                                        </h2>
                                                        <div id="flush-collapseTwo" class="accordion-collapse collapse" data-bs-parent="#accordionFlushExample">
                                                            <div class="accordion-body p-0">
                                                                <ul class="list-group list-group-flush">
                                                                    <li class="list-group-item filterItem" role="button" category="good" id="filterItem"><i class="cp-success me-2 text-success"></i>Non Conflicted</li>
                                                                    <li class="list-group-item filterItem" role="button" category="partial" id="filterItem"><i class="cp-warning me-2 text-warning"></i>Partialy Conflicted</li>
                                                                    <li class="list-group-item filterItem" role="button" category="bad" id="filterItem"><i class="cp-error me-2 text-danger"></i>Conflicted</li>
                                                                    <li class="list-group-item filterItem" role="button" category="null" id="filterItem"><i class="cp-not-started me-2 text-warning"></i>Not Configured</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <i class="text-primary ms-2 cp-circle-rightarrow" id="expandAction" role="button" title="Collapse &amp; Expand"></i>
                                </div>
                                <div class="tree-menu DriftTreeMenu" style="max-height: calc(100vh - 202px); overflow-y: auto;">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-9 infraDetailsInfraWiseData">
                        <div class="card shadow-sm">
                            <div class="card-header card-title header p-2 border-0 bg-transparent">
                                <span>Snapshot Overview (<span class="text-primary InfraProfile"></span>)</span>
                                <span>Last Monitored Date : <small class="fw-medium text-primary" id="prDate"></small></span>
                               @*  <div class="input-group w-auto">
                                    <input type="search" id="search-inpsnapShot" class="form-control" placeholder="Search" autocomplete="off">

                                </div> *@
                            </div>
                            <div class="card-body p-2 pt-0" style="max-height: calc(100vh - 385px);overflow-y: auto;">
                                <div class="row g-2 mx-0 dynamicSnapDetails">
                                    <div class="col details" style=" ">
                                        <div class="card border mb-0">
                                            <div class="card-header border-0 Blue_Gradient card-title header sticky-top text-white pb-4">
                                                <div class="card-title  d-grid">
                                                    <div>Base Snap</div>
                                                    <small class="fw-medium" id="baseDate"></small>
                                                </div>
                                                <div class="d-grid text-end">
                                                    <small class="fw-medium" id="baseipaddress"></small>
                                                    <small class="fw-medium" id="basehostname"></small>
                                                </div>
                                            </div>
                                            <div class="card-body p-0" style="font-size:11px">
                                                <div class="accordion accordion-flush baseSnapDetails" id="accordionFlushExample">
                                                   
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col details">
                                        <div class="card border mb-0">
                                            <div class="card-header align-items-start border-0 Pink_Gradient card-title header sticky-top text-white pb-4">
                                                <div class="card-title pt-1  d-grid">
                                                    <div>PR Snap</div>
                                                </div>
                                                <div class="d-grid pt-1 text-end">
                                                    <i role="button" title="Make as Basesnap" class="cp-horizontal-dots snabTab d-none" id="snabTab"></i>
                                                    <small class="fw-medium" id="pripaddress"></small>
                                                    <small class="fw-medium" id="prhostname"></small>
                                                </div>
                                            </div>
                                            <div class="card-body p-0" style="font-size:11px">
                                                <div class="accordion accordion-flush prSnapDetails" id="accordionFlushExample">
                                                    
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col details multiSnapDetails">
                                       
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-3">
                            <div class="col-5">
                                <div class="card shadow-sm ">
                                    <div class="card-header card-title p-2 bg-transparent border-0">Changes Summary</div>
                                    <div class="card-body p-2 pt-0">
                                        <div id="ChangesSummaryChart" style="width:100%; height:190px;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-7">
                                <div class="card shadow-sm ">
                                    <div class="card-header card-title p-2 bg-transparent border-0">Drift Category</div>
                                    <div class="card-body p-2 pt-0">
                                        <div id="DriftCategoryChart" style="width:100%; height:190px;"></div>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-9 d-none driftNoData d-flex align-items-center justify-content-center"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Resource Modal-->
<div class="modal fade" id="ResourceModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl  modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-view-dashboard-icon"></i><span>Resource Status</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="datatable table  no-footer align-middle " style="width:100%">
                    <thead class="position-sticky top-0 z-3">
                        <tr>
                            <th>Sr.No</th>
                            <th>InfraObject Name</th>
                            <th>Category Name</th>
                            <th>Parameter Name</th>
                            <th>Drift Enable</th>
                            <th>Conflict Status</th>
                            <th>Remarks</th>
                            
                        </tr>
                    </thead>
                    <tbody id="ResourceTableDetails">
                    </tbody>
                </table>


            </div>

        </div>
    </div>
</div>


<!-- Mismatch Category Modal-->
<div class="modal fade" id="MismatchCategoryModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl  modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><i class="cp-business-view-dashboard-icon"></i><span>Mismatch Categories</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <table class="datatable table  no-footer align-middle " style="width:100%">
                    <thead class="position-sticky top-0 z-3">
                        <tr>
                            <th>Sr.No</th>
                            <th>InfraObject Name</th>
                            <th>Count Mismatch</th>
                            <th>Version Mismatch</th>
                            <th>Config Mismatch</th>
                            <th>Policy Mismatch</th>
                        </tr>
                    </thead>
                    <tbody id="MismatchCategoryDetails">
                    </tbody>
                </table>


            </div>

        </div>
    </div>
</div>




<!-- Modal -->
<div class="modal fade" id="BaselineModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="page_title"><span>Make as Base  Snap ?</span></h6>
                <button type="button" class="btn-close ms-2 " data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
            </div>
            <div class="modal-body currentSnap">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>

                <button type="button" class="btn btn-primary BaseSnapSave">Save</button>
            </div>
        </div>
    </div>
</div>
 
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>

<script>
    // $(function () {
    //     // Hide all lists except the outermost.
    //     $('ul.tree ul').show();
    //     $('.tree li > ul').each(function (i) {
    //         var $subUl = $(this);
    //         var $parentLi = $subUl.parent('li');
    //         var $toggleIcon = '<i class="js-toggle-icon">-</i>';

    //         $parentLi.addClass('has-children');

    //         $parentLi.prepend($toggleIcon).find('.js-toggle-icon').on('click', function () {
    //             $(this).text($(this).text() == '-' ? '+' : '-');
    //             $subUl.slideToggle('fast');
    //         });
    //     });
    // });
</script>








<script>
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Create chart instance
    var chart = am4core.create("DriftCategoryChart", am4charts.RadarChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    chart.padding(-10, -10, -25, -30)
    // Add data
    chart.data = [{
        "category": "Count Mismatch",
        "value": 80,
        "full": 100,
    }, {
        "category": "Config Mismatch",
        "value": 35,
        "full": 100,
    }, {
        "category": "Version Mismatch",
        "value": 92,
        "full": 100,
        "color": am4core.color("#ffc060")
    }, {
        "category": "Policy Mismatch",
        "value": 68,
        "full": 100,
        "color": am4core.color("#ffc060")
    }];

    // Make chart not full circle
    chart.startAngle = -90;
    chart.endAngle = 180;
    chart.innerRadius = am4core.percent(50);

    // Set number format
    chart.numberFormatter.numberFormat = "#.#'%'";


    // Create axes
    var categoryAxis = chart.yAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "category";
    categoryAxis.renderer.grid.template.location = 0;
    categoryAxis.renderer.grid.template.strokeOpacity = 0;
    categoryAxis.renderer.labels.template.horizontalCenter = "right";
    categoryAxis.renderer.labels.template.fontWeight = 500;
    categoryAxis.renderer.labels.template.adapter.add("fill", function (fill, target) {
        return (target.dataItem.index >= 0) ? chart.colors.getIndex(target.dataItem.index) : fill;
    });
    categoryAxis.renderer.minGridDistance = 10;

    var valueAxis = chart.xAxes.push(new am4charts.ValueAxis());
    valueAxis.renderer.grid.template.strokeOpacity = 0;
    valueAxis.min = 0;
    valueAxis.max = 100;
    valueAxis.strictMinMax = true;

    // Hide the percentage label
    valueAxis.renderer.labels.template.disabled = true;

    // Create series
    var series1 = chart.series.push(new am4charts.RadarColumnSeries());
    series1.dataFields.valueX = "full";
    series1.dataFields.categoryY = "category";
    series1.clustered = false;
    series1.columns.template.fill = new am4core.InterfaceColorSet().getFor("alternativeBackground");
    series1.columns.template.fillOpacity = 0.08;
    series1.columns.template.cornerRadiusTopLeft = 20;
    series1.columns.template.strokeWidth = 0;
    series1.columns.template.radarColumn.cornerRadius = 20;

    var series2 = chart.series.push(new am4charts.RadarColumnSeries());
    series2.dataFields.valueX = "value";
    series2.dataFields.categoryY = "category";
    series2.clustered = false;
    series2.columns.template.strokeWidth = 0;
    series2.columns.template.tooltipText = "{category}: [bold]{value}[/]";
    series2.columns.template.radarColumn.cornerRadius = 20;



    series2.columns.template.adapter.add("fill", function (fill, target) {

        if (target.dataItem.categoryY == "Count Mismatch") {
            return am4core.color("#de5fa9"); // Red color for values greater than 50
        }
        else if (target.dataItem.categoryY == "Config Mismatch") {
            return am4core.color("#cc61e3"); // Green color for values 50 or less
        }
        else if (target.dataItem.categoryY == "Version Mismatch") {
            return am4core.color("#636feb"); // Green color for values 50 or less
        }
        else if (target.dataItem.categoryY == "Policy Mismatch") {
            return am4core.color("#5db7e1"); // Green color for values 50 or less
        }
        else {
            return fill; // Green color for values 50 or less
        }
    });

    // Add cursor
    chart.cursor = new am4charts.RadarCursor();

    chart.legend = new am4charts.Legend();
    chart.legend.position = "right";
    chart.legend.labels.template.text = "[font-size:12px ]{name}";
    chart.legend.labels.template.fill = am4core.color("#6c757d");
    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
    chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    var markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 10;
    markerTemplate.height = 10;


    series2.events.on("dataitemsvalidated", function () {
        var data = [];
        series2.dataItems.each(function (dataItem) {
            data.push({ name: dataItem.categoryY, fill: dataItem.column.fill, seriesDataItem: dataItem })
        })

        chart.legend.data = data;
        chart.legend.itemContainers.template.events.on("toggled", function (event) {
            var seriesDataItem = event.target.dataItem.dataContext.seriesDataItem;
            if (event.target.isActive) {
                seriesDataItem.hide(series2.interpolationDuration, 0, 0, ["valueX"]);
            }
            else {
                seriesDataItem.show(series2.interpolationDuration, 0, ["valueX"]);
            }
        })
    })
</script>

<script>
    var chart = am4core.create("ChangesSummaryChart", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }

    chart.data = [
        {
            "country": "Planned",
            "litres": 30,
            "color": am4core.color("#5cd17f")
        }, {
            "country": "Unplanned",
            "litres": 60,
            "color": am4core.color("#f76161")
        }];

    //chart.radius = am4core.percent(70);
    //chart.innerRadius = am4core.percent(40);
    chart.startAngle = 160;
    chart.endAngle = 380;

    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.propertyFields.fill = "color";
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;

    // Let's cut a hole in our Pie chart the size of 40% the radius
    chart.innerRadius = am4core.percent(65);
    chart.padding(-0, -0, -0, -0);
    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;

    // Add a legend
    chart.legend = new am4charts.Legend();
    chart.legend.position = "bottom";
    //chart.legend.valueLabels.template.disabled = true;
    chart.legend.labels.template.text = "[font-size:12px ]{name}";
    chart.legend.labels.template.fill = am4core.color("#6c757d");
    chart.legend.itemContainers.template.padding(8, 0, 0, 0);
    chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    var markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 10;
    markerTemplate.height = 10;

    let label4 = chart.seriesContainer.createChild(am4core.Label);
    label4.text = `[bold]${60}[/] \n [font-size:12px] \n Total Changes [/]`;
    label4.horizontalCenter = "middle";
    label4.verticalCenter = "middle";
    label4.textAlign = "middle";
    label4.fontSize = 16;
</script>
<script src="~/js/drift/Drift Dashboard/DriftDashboardDetails.js"></script>
