using ContinuityPatrol.Application.Features.CyberComponentMapping.Events.Delete;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Commands.Delete;

public class DeleteCyberComponentMappingCommandHandler : IRequestHandler<DeleteCyberComponentMappingCommand,
    DeleteCyberComponentMappingResponse>
{
    private readonly ICyberComponentMappingRepository _cyberComponentMappingRepository;
    private readonly IPublisher _publisher;

    public DeleteCyberComponentMappingCommandHandler(ICyberComponentMappingRepository cyberComponentMappingRepository,
        IPublisher publisher)
    {
        _cyberComponentMappingRepository = cyberComponentMappingRepository;

        _publisher = publisher;
    }

    public async Task<DeleteCyberComponentMappingResponse> Handle(DeleteCyberComponentMappingCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _cyberComponentMappingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.CyberComponentMapping),
            new NotFoundException(nameof(Domain.Entities.CyberComponentMapping), request.Id));

        eventToDelete.IsActive = false;

        await _cyberComponentMappingRepository.UpdateAsync(eventToDelete);

        var response = new DeleteCyberComponentMappingResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.CyberComponentMapping), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new CyberComponentMappingDeletedEvent { Name = eventToDelete.Name },
            cancellationToken);

        return response;
    }
}