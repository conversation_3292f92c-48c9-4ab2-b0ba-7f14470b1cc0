﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace ContinuityPatrol.Domain.Entities
{
    public class SchedulerWorkflowActionResults : AuditableEntity
    {
        public string InfraObjectSchedulerLogsId { get; set; }
        public string NodeId { get; set; }
        public string NodeName { get; set; }
        public string WorkflowId { get; set; }
        public string WorkflowName { get; set; }
        public string InfraObjectId { get; set; }
        public string InfraObjectName { get; set; }
        public string StepId { get; set; }
        public string WorkflowActionId {  get; set; }
        public string WorkflowActionName { get;set; }
        public string Status { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Message { get; set; }

    }
}
