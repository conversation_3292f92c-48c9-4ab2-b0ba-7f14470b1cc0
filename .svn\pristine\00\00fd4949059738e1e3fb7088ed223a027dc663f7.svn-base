using ContinuityPatrol.Application.Features.BackUpLog.Events.Create;

namespace ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;

public class CreateBackUpLogCommandHandler : IRequestHandler<CreateBackUpLogCommand, CreateBackUpLogResponse>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateBackUpLogCommandHandler(IMapper mapper, IBackUpLogRepository backUpLogRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _backUpLogRepository = backUpLogRepository;
    }

    public async Task<CreateBackUpLogResponse> Handle(CreateBackUpLogCommand request,
        CancellationToken cancellationToken)
    {
        var backUpLog = _mapper.Map<Domain.Entities.BackUpLog>(request);

        backUpLog = await _backUpLogRepository.AddAsync(backUpLog);

        var response = new CreateBackUpLogResponse
        {
            Message = Message.Create(nameof(Domain.Entities.BackUpLog), backUpLog.DatabaseName),

            Id = backUpLog.ReferenceId
        };

        await _publisher.Publish(new BackUpLogCreatedEvent { Name = backUpLog.DatabaseName }, cancellationToken);

        return response;
    }
}