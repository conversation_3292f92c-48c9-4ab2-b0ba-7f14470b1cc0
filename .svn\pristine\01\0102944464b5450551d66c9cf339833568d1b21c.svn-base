﻿using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Events.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicDashboardMap.Commands;

public class UpdateDynamicDashboardMapCommandHandlerTests
{
    private readonly Mock<IDynamicDashboardMapRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IPublisher> _publisherMock;
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly UpdateDynamicDashboardMapCommandHandler _handler;

    public UpdateDynamicDashboardMapCommandHandlerTests()
    {
        _repositoryMock = DynamicDashboardMapRepositoryMocks.UpdateDynamicDashboardMapRepository();
        _mapperMock = new Mock<IMapper>();
        _userRepositoryMock = new Mock<IUserRepository>();
        _publisherMock = new Mock<IPublisher>();

        _handler = new UpdateDynamicDashboardMapCommandHandler(
            _mapperMock.Object,
            _repositoryMock.Object,
            _publisherMock.Object,
            _userRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldUpdateEntity_WhenFound()
    {
        var command = new UpdateDynamicDashboardMapCommand
        {
            Id = "map-id-123",
            UserName = "Updated Name"
        };

        var entity = new Domain.Entities.DynamicDashboardMap
        {
            ReferenceId = command.Id,
            UserName = "Old Name"
        };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity,
            typeof(UpdateDynamicDashboardMapCommand), typeof(Domain.Entities.DynamicDashboardMap)));
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicDashboardMap>())).ReturnsAsync(entity);

        var result = await _handler.Handle(command, CancellationToken.None);

        result.Id.Should().Be(command.Id);
        result.Message.Should().Contain("DynamicDashboardMap");
        _publisherMock.Verify(p => p.Publish(It.IsAny<DynamicDashboardMapUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = "not-found-id" };
        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync((Domain.Entities.DynamicDashboardMap)null!);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldPublishEvent_WhenUpdateSuccessful()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = "map-xyz", UserName = "EventName" };
        var entity = new Domain.Entities.DynamicDashboardMap { ReferenceId = "map-xyz", UserName = "Old" };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity,
            typeof(UpdateDynamicDashboardMapCommand), typeof(Domain.Entities.DynamicDashboardMap)));
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicDashboardMap>())).ReturnsAsync(entity);

        await _handler.Handle(command, CancellationToken.None);

        _publisherMock.Verify(p =>
            p.Publish(It.Is<DynamicDashboardMapUpdatedEvent>(e => e.Name == command.UserName), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenUpdateFails()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = "map-err", UserName = "Fails" };
        var entity = new Domain.Entities.DynamicDashboardMap { ReferenceId = "map-err", UserName = "Old" };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity,
            typeof(UpdateDynamicDashboardMapCommand), typeof(Domain.Entities.DynamicDashboardMap)));
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicDashboardMap>()))
            .ThrowsAsync(new Exception("Update failed"));

        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        _publisherMock.Verify(p => p.Publish(It.IsAny<DynamicDashboardMapUpdatedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldCallMapper_And_UpdateOnce()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = "map-check", UserName = "CheckUpdate" };
        var entity = new Domain.Entities.DynamicDashboardMap { ReferenceId = "map-check", UserName = "Before" };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicDashboardMap>())).ReturnsAsync(entity);

        await _handler.Handle(command, CancellationToken.None);

        _mapperMock.Verify(m => m.Map(command, entity,
            typeof(UpdateDynamicDashboardMapCommand), typeof(Domain.Entities.DynamicDashboardMap)), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(entity), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenIdIsNull()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = null! };
        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.DynamicDashboardMap)null!);

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldRespectCancellationToken()
    {
        var command = new UpdateDynamicDashboardMapCommand { Id = "cancel-map", UserName = "CancelTest" };
        var entity = new Domain.Entities.DynamicDashboardMap { ReferenceId = "cancel-map" };
        var tokenSource = new CancellationTokenSource();
        var token = tokenSource.Token;

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity,
            typeof(UpdateDynamicDashboardMapCommand), typeof(Domain.Entities.DynamicDashboardMap)));
        _repositoryMock.Setup(r => r.UpdateAsync(It.IsAny<Domain.Entities.DynamicDashboardMap>())).ReturnsAsync(entity);

        var result = await _handler.Handle(command, token);

        result.Id.Should().Be(command.Id);
        _publisherMock.Verify(p => p.Publish(It.IsAny<DynamicDashboardMapUpdatedEvent>(), token), Times.Once);
    }
}
