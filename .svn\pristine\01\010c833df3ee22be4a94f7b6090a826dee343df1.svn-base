﻿namespace ContinuityPatrol.Application.Features.Form.Queries.GetType;

public class GetFormTypeQueryHandler : IRequestHandler<GetFormTypeQuery, List<FormTypeVm>>
{
    private readonly IFormRepository _formRepository;
    private readonly IMapper _mapper;

    public GetFormTypeQueryHandler(IMapper mapper, IFormRepository formRepository)
    {
        _mapper = mapper;
        _formRepository = formRepository;
    }

    public async Task<List<FormTypeVm>> Handle(GetFormTypeQuery request, CancellationToken cancellationToken)
    {
        var formType = (await _formRepository.GetFormType(request.Type)).ToList();

        return formType.Count <= 0 ? new List<FormTypeVm>() : _mapper.Map<List<FormTypeVm>>(formType);
    }
}