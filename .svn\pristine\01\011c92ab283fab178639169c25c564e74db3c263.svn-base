﻿using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetNames;

public class
    GetWorkflowCategoryNameQueryHandler : IRequestHandler<GetWorkflowCategoryNameQuery, List<WorkflowCategoryNameVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowCategoryRepository _workflowCategoryRepository;

    public GetWorkflowCategoryNameQueryHandler(IMapper mapper, IWorkflowCategoryRepository workflowCategoryRepository)
    {
        _mapper = mapper;
        _workflowCategoryRepository = workflowCategoryRepository;
    }

    public async Task<List<WorkflowCategoryNameVm>> Handle(GetWorkflowCategoryNameQuery request,
        CancellationToken cancellationToken)
    {
        var workflowAction = await _workflowCategoryRepository.GetWorkflowCategoryNames();

        return _mapper.Map<List<WorkflowCategoryNameVm>>(workflowAction);
    }
}