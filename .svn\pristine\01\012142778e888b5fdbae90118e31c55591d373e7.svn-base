﻿using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Create;
using ContinuityPatrol.Application.Features.ReportSchedule.Commands.Update;
using ContinuityPatrol.Application.Features.ReportSchedule.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.ReportScheduleModel;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.ReportSchedule.Event.View;
using ContinuityPatrol.Web.Attributes;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;

namespace ContinuityPatrol.Web.Areas.Report.Controllers;

[Area("Report")]
public class ReportSchedulerController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<ReportSchedulerController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;

    public ReportSchedulerController(IPublisher publisher, IMapper mapper, ILogger<ReportSchedulerController> logger, IDataProvider provider)
    {
        _publisher = publisher;
        _mapper = mapper;
        _logger = logger;
        _dataProvider = provider;
    }
    [EventCode(EventCodes.ReportScheduler.List)]
    public async Task<IActionResult> List()
    {
        await _publisher.Publish(new ReportScheduleViewedEvent());
        var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceNames();

        var businessServiceNames = businessServiceList.Select(x => new SelectListItem
        {
            Value = x.Id,
            Text = x.Name,
        }).ToList();

        var reportViewModel = new ReportScheduleViewModel
        {
            BusinessServices = businessServiceNames,
        };

        return View(reportViewModel);
    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.Pagination)]
    public async Task<JsonResult> GetPagination(GetReportSchedulePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in ReportScheduler");
        try
        {
            var paginationList = await _dataProvider.ReportSchedule.GetPaginatedReportScheduleList(query);
            _logger.LogDebug("Successfully retrieved pagination list for ReportScheduler");
            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetReportScheduleById)]
    public async Task<JsonResult> GetReportScheduleById(string reportId)
    {

        _logger.LogDebug("Entering GetReportScheduleById method in ReportScheduler.");

        if (reportId.IsNullOrWhiteSpace())
        {
            return Json(new { Success = false, Message = "ReportId is not valid format", ErrorCode = 0 });
        }

        try
        {
            var reportScheduleDetails = await _dataProvider.ReportSchedule.GetReportScheduleById(reportId);
            _logger.LogDebug($"Successfully retrieved report schedule details by Id {reportId}");
            return Json(new { Success = true, data = reportScheduleDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while retrieving the report schedule details by Id.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetReportScheduleExecutionById)]
    public async Task<JsonResult> GetReportScheduleExecutionById(string reportId)
    {

        _logger.LogDebug("Entering GetReportScheduleExecutionById method in ReportScheduler");

        if (reportId.IsNullOrWhiteSpace())
        {
            return Json(new { Success = false, Message = "ReportId is not valid format", ErrorCode = 0 });
        }

        try
        {
            var reportScheduleDetails = await _dataProvider.ReportSchedule.GetReportScheduleExecutionById(reportId);
            _logger.LogDebug($"Successfully retrieved report schedule execution details by Id {reportId}");
            return Json(new { Success = true, data = reportScheduleDetails });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while retrieving the report schedule details by Id.", ex);
            return ex.GetJsonException();
        }

    }
    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetUserGroup)]
    public async Task<JsonResult> GetUserGroup()
    {
        _logger.LogDebug("Entering GetUserGroup method in ReportScheduler");
        try
        {
            var userGroupList = await _dataProvider.UserGroup.GetUserGroupList();
            _logger.LogDebug("Successfully retrieved user group list in ReportScheduler");
            return Json(new { Success = true, data = userGroupList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while retrieving the user group list.", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetUsers)]
    public async Task<JsonResult> GetUsers()
    {
        _logger.LogDebug("Entering GetUsers method in ReportScheduler");
        try
        {
            var userNames = await _dataProvider.User.GetUsers();
            _logger.LogDebug("Successfully retrieved user list in ReportScheduler");
            return Json(new { Success = true, data = userNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page retrieving the user list .", ex);
            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetMonitorTypeByInfraObject)]
    public async Task<JsonResult> GetMonitorTypeByInfraObject()
    {
        _logger.LogDebug("Entering GetMonitorTypeByInfraObject method in ReportScheduler");
        try
        {
            var infraList = await _dataProvider.DashboardView.GetDashboardNames();
            var filteredInfraList = infraList.Where(x => !string.IsNullOrEmpty(x.MonitorType)).ToList();

            return Json(new { Success = true, data = filteredInfraList });

        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while retrieving the monitorType and infraObject.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetInfraObjectList)]
    public async Task<JsonResult> GetInfraObjectList()
    {
        _logger.LogDebug("Entering GetInfraObjectList method in ReportScheduler");
        try
        {
            var infraList = await _dataProvider.InfraObject.GetInfraObjectList();
            _logger.LogDebug("Successfully retrieved infraObject list in ReportScheduler");
            return Json(new { success = true, data = infraList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while retrieving the infraObject list.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetBusinessFunction)]
    public async Task<JsonResult> GetBusinessFunction(string id)
    {
        _logger.LogDebug("Entering GetBusinessFunction method in ReportScheduler");
        if (string.IsNullOrWhiteSpace(id))
        {
            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var businessFunctionNames = await _dataProvider.BusinessFunction.GetBusinessFunctionNamesByBusinessServiceId(id);
                _logger.LogDebug($"Successfully retrieved businessFunction names by businessServiceId {id} in ReportScheduler");
                return Json(new { Success = true, data = businessFunctionNames });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on report schedule page while retrieving the businessFunction names by businessServiceId.", ex);
                return ex.GetJsonException();
            }
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetInfraBusinessFunction)]
    public async Task<JsonResult> GetInfraBusinessFunction(string id)
    {
        _logger.LogDebug("Entering GetInfraBusinessFunction method in ReportScheduler");
        if (string.IsNullOrWhiteSpace(id))
        {
            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var businessFunctionByInfra = await _dataProvider.InfraObject.GetInfraObjectByBusinessFunctionId(id);
                _logger.LogDebug($"Successfully retrieved infraObject by businessFunctionId {id} in ReportScheduler");
                return Json(new { Success = true, data = businessFunctionByInfra });
            }
            catch (Exception ex)
            {
                _logger.Exception($"An error occurred on report schedule page while retrieving infraObject by businessFunctionId.", ex);
                return ex.GetJsonException();
            }
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.GetInfraJob)]
    public async Task<JsonResult> GetInfraJob(string id)
    {
        _logger.LogDebug("Entering GetInfraJob method in ReportScheduler");
        if (string.IsNullOrWhiteSpace(id))
        {
            return Json(new { Success = false, Message = "Id is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var jobByInfra = await _dataProvider.JobService.GetJobsByInfraObjectId(id);
                _logger.LogDebug($"Successfully retrieved MonitorJobs by infraObjectId {id} in ReportScheduler");
                return Json(new { Success = true, data = jobByInfra });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on report schedule page while  retrieving monitorJobs by infraObjectId.", ex);
                return ex.GetJsonException();
            }
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.ReportScheduler.CreateOrUpdate)]
    public async Task<IActionResult> SaveOrUpdate(ReportScheduleViewModel reportObject)
    {
        _logger.LogDebug("Entering SaveOrUpdate method in ReportScheduler");
        var reportId = Request.Form["id"].ToString();

        try
        {
            if (reportId.IsNullOrWhiteSpace())
            {
                var createReportSchedule = _mapper.Map<CreateReportScheduleCommand>(reportObject);
                var result = await _dataProvider.ReportSchedule.CreateAsync(createReportSchedule);
                _logger.LogDebug($"Creating ReportScheduler '{createReportSchedule.ReportName}'");
                return Json(new { Success = true, data = result });
            }
            else
            {
                var updateReportSchedule = _mapper.Map<UpdateReportScheduleCommand>(reportObject);
                var result = await _dataProvider.ReportSchedule.UpdateAsync(updateReportSchedule);
                _logger.LogDebug($"Updating ReportSchedule '{updateReportSchedule.ReportName}'");
                return Json(new { Success = true, data = result });

            }

        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on report schedule page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on report schedule page while processing the request for create or update.", ex);
            return ex.GetJsonException();

        }

    }

    [Authorize(Policy = Permissions.Reports.Delete)]
    [EventCode(EventCodes.ReportScheduler.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in ReportScheduler");
        try
        {
            var report = await _dataProvider.ReportSchedule.DeleteAsync(id);
            _logger.LogDebug($"Deleting ReportScheduler Details by Id '{id}'");
            TempData.NotifySuccess(report.Message);
            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on report schedule.", ex);
            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }

    }


    [HttpGet]
    [EventCode(EventCodes.ReportScheduler.IsExist)]
    public async Task<bool> IsReportNameExist(string reportName, string id)
    {
        _logger.LogDebug("Entering IsReportNameExist method in ReportScheduler");
        try
        {
            var nameExist = await _dataProvider.ReportSchedule.IsReportScheduleNameExist(reportName, id);
            _logger.LogDebug("Returning result for IsReportNameExist on ReportScheduler");
            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on report schedule page while checking if report schedule name exists for : {reportName}.", ex);

            return false;
        }
    }

}
