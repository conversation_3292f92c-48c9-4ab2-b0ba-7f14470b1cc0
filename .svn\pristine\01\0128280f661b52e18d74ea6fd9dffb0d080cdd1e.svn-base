﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.Template.Commands.Create;

public class CreateTemplateCommand : IRequest<CreateTemplateResponse>
{
    public string Name { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    public string Properties { get; set; }
    public string Icon { get; set; }
    public string Version { get; set; }
    public string Type { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryTypeId { get; set; }
    public string ReplicationCategoryTypeName { get; set; }
    public string SubTypeId { get; set; }
    public string SubTypeName { get; set; }
    public string Description { get; set; }
    public string ActionType { get; set; }
    public string Comments { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}