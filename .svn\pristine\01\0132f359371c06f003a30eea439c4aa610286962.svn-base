﻿using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Queries;

public class GetBusinessFunctionNameUniqueQueryHandlerTests : IClassFixture<BusinessFunctionFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private Mock<IBusinessFunctionRepository> _mockBusinessFunctionRepository;
    private readonly GetBusinessFunctionNameUniqueQueryHandler _handler;

    public GetBusinessFunctionNameUniqueQueryHandlerTests(BusinessFunctionFixture businessFunctionFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.GetBusinessFunctionNameUniqueRepository(_businessFunctionFixture.BusinessFunctions);

        _handler = new GetBusinessFunctionNameUniqueQueryHandler(_mockBusinessFunctionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_BusinessFunctionName_Exist()
    {
        _businessFunctionFixture.BusinessFunctions[0].Name = "PRS";
        _businessFunctionFixture.BusinessFunctions[0].IsActive = true;

        var result = await _handler.Handle(new GetBusinessFunctionNameUniqueQuery { BusinessFunctionName = _businessFunctionFixture.BusinessFunctions[0].Name, BusinessFunctionId = _businessFunctionFixture.BusinessFunctions[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_BusinessFunctionNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetBusinessFunctionNameUniqueQuery { BusinessFunctionName = "Virtusa", BusinessFunctionId = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_BusinessFunctionName_NotMatch()
    {
        var result = await _handler.Handle(new GetBusinessFunctionNameUniqueQuery { BusinessFunctionName = "SEP", BusinessFunctionId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OneTime()
    {
        await _handler.Handle(new GetBusinessFunctionNameUniqueQuery(), CancellationToken.None);

        _mockBusinessFunctionRepository.Verify(x => x.IsBusinessFunctionNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessFunctionRepository = BusinessFunctionRepositoryMocks.GetBusinessFunctionEmptyRepository();

        var result = await _handler.Handle(new GetBusinessFunctionNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}