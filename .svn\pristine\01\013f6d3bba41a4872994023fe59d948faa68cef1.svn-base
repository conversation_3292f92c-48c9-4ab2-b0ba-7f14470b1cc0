﻿using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using AuthenticationException = ContinuityPatrol.Shared.Core.Exceptions.AuthenticationException;
using ValidationException = ContinuityPatrol.Shared.Core.Exceptions.ValidationException;

namespace ContinuityPatrol.Shared.Infrastructure.Middlewares;

public class ExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionMiddleware> _logger;

    public ExceptionMiddleware(RequestDelegate next, ILogger<ExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }
    public async Task Invoke(HttpContext context)
    {
        try
        {
            _logger.LogInformation($"{context.Request.Method} : {context.Request.Path}");

            await _next(context);
        }
        catch (ValidationException ex)
        {
            foreach (var error in ex.ValidationErrors)
            {
                _logger.LogError($"Validation Error: {error}");
            }

            await ConvertException(context, ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(GetErrorMessage(ex));

            await ConvertException(context, ex);
        }
    }

    private string GetErrorMessage(Exception exception)
    {
        var message = "Exception Message : " + exception.Message + ".";

        if (exception.InnerException != null)
        {
            message = " Inner Exception : " + exception.InnerException.Message;
        }

        return $"************ {message} ************";
    }

    private Task ConvertException(HttpContext context, Exception exception)
    {
        var httpStatusCode = HttpStatusCode.InternalServerError;

        context.Response.ContentType = "application/json";

        var result = string.Empty;

        switch (exception)
        {
            case ValidationException validationException:
                httpStatusCode = HttpStatusCode.BadRequest;
                result = JsonConvert.SerializeObject(new ValidationResponse { Success = false, Message = "Invalid Parameter(s).", ValidationErrors = validationException.ValidationErrors });
                break;
            case BadRequestException badRequestException:
                httpStatusCode = HttpStatusCode.BadRequest;
                result = JsonConvert.SerializeObject(new BaseResponse { Success = false, Message = badRequestException.Message });
                break;
            case NotFoundException:
                httpStatusCode = HttpStatusCode.NotFound;
                break;
            case AuthenticationException authenticationException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = JsonConvert.SerializeObject(new BaseResponse { Success = false, Message = authenticationException.Message, ErrorCode = authenticationException.ErrorCode });
                break;
            case InvalidArgumentException:
                httpStatusCode = HttpStatusCode.BadRequest;
                break;
            case SecurityTokenExpiredException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = JsonConvert.SerializeObject(new BaseResponse { Success = false, Message = "Token_Expired", ErrorCode = 4005 });
                break;
            case TokenExpiredException tokenExpiredException:
                httpStatusCode = HttpStatusCode.Unauthorized;
                result = JsonConvert.SerializeObject(new BaseResponse { Success = false, Message = tokenExpiredException.Message, ErrorCode = 4005 });
                break;
            case not null:
                httpStatusCode = HttpStatusCode.BadRequest;
                break;
        }

        context.Response.StatusCode = (int)httpStatusCode;

        if (result != string.Empty) return context.Response.WriteAsync(result);
        if (exception != null) result = JsonConvert.SerializeObject(new BaseResponse { Success = false, Message = exception.GetMessage()});

        return context.Response.WriteAsync(result);
    }
}
