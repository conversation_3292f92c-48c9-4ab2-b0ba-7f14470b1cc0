using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberComponentMappingRepository : BaseRepository<CyberComponentMapping>, ICyberComponentMappingRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberComponentMappingRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("Name must be provided.", nameof(name));
        }

        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.Name == name);
        }

        var matchingItems = await Entities
            .Where(e => e.Name == name)
            .ToListAsync();

        return matchingItems.Unique(id);
    }
}
