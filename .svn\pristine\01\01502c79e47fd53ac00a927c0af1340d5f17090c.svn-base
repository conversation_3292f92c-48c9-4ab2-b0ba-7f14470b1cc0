﻿using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetList;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.UserActivity.Queries.GetStartTimeEndTimeByUserId;
using ContinuityPatrol.Domain.ViewModels.UserActivityModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class UserActivitiesController : CommonBaseController
{
    [HttpGet]
    public async Task<ActionResult<List<UserActivityListVm>>> GetUserActivities()
    {
        Logger.LogInformation("Get All User Activities");

        //return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllUserActivitiesCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetUserActivityListQuery()), CacheExpiry));

        return Ok(await  Mediator.Send(new GetUserActivityListQuery()));
    }

    [HttpPost]
    public async Task<ActionResult<CreateUserActivityResponse>> CreateUserActivity([FromBody] CreateUserActivityCommand createUserActivityCommand)
    {
        Logger.LogInformation($"Create User Activity '{createUserActivityCommand.LoginName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateUserActivity), await Mediator.Send(createUserActivityCommand));
    }

    [HttpGet, Route("by/{loginName}")]
    public async Task<ActionResult<List<UserActivityLoginNameVm>>> GetLoginName(string loginName)
    {
        Guard.Against.NullOrWhiteSpace(loginName, "User Activity LoginName");

        Logger.LogInformation($"Get User Activity Detail by LoginName '{loginName}'");

        return Ok(await Mediator.Send(new GetUserActivityLoginNameQuery { LoginName = loginName }));
    }

    [HttpGet("startTime-endTime")]
    public async Task<ActionResult<List<UserActivityListVm>>> GetStartTimeEndTimeByUser(string? loginName, string createDate, string lastModifiedDate)
    {
        Logger.LogDebug("Get All Configuration By CreatedDate");

        return Ok(await Mediator.Send(new GetStartTimeEndTimeByUserIdQuery { LoginName = loginName, StartDate = createDate, EndDate = lastModifiedDate }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllUserActivitiesCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllUserActivitiesNameCacheKey };

        ClearCache(cacheKeys);
    }
}
