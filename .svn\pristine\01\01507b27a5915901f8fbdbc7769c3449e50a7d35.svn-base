﻿namespace ContinuityPatrol.Domain.ViewModels.LicenseHistoryModel;

public class LicenseHistoryViewModel
{
    public string Id { get; set; }
    public string LicenseId { get; set; }
    public string PONumber { get; set; }
    public string CompanyId { get; set; }
    public string CompanyName { get; set; }
    public string CPHostName { get; set; }
    public string ServerCount { get; set; }
    public string DatabaseCount { get; set; }
    public string ReplicationCount { get; set; }
    public string IPAddress { get; set; }
    public string MACaddress { get; set; }
    public string LicenseKey { get; set; }
    public string Validity { get; set; }
    public string ExpiryDate { get; set; }
    public string UpdaterId { get; set; }
    public string ParentPONumber { get; set; }
    public bool IsState { get; set; }
}