﻿using ContinuityPatrol.Application.Features.Job.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Job.Queries;

public class GetJobNameUniqueQueryHandlerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _jobFixture;

    private Mock<IJobRepository> _mockJobRepository;

    private readonly GetJobNameUniqueQueryHandler _handler;

    public GetJobNameUniqueQueryHandlerTests(JobFixture jobFixture)
    {
        _jobFixture = jobFixture;

        _mockJobRepository = JobRepositoryMocks.GetJobNameUniqueRepository(_jobFixture.Jobs);

        _handler = new GetJobNameUniqueQueryHandler(_mockJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_InfraObjectName_Exist()
    {
        _jobFixture.Jobs[0].Name = "Job_Infra";

        var result = await _handler.Handle(new GetJobNameUniqueQuery { Name = _jobFixture.Jobs[0].Name, Id = _jobFixture.Jobs[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Call_IsInfraObjectNameExist_OneTime()
    {
        await _handler.Handle(new GetJobNameUniqueQuery(), CancellationToken.None);

        _mockJobRepository.Verify(x => x.IsJobNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_infraObjectName_NotMatch()
    {
        var result = await _handler.Handle(new GetJobNameUniqueQuery { Name = "Job_Infra", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockJobRepository = JobRepositoryMocks.GetJobEmptyRepository();

        var result = await _handler.Handle(new GetJobNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}