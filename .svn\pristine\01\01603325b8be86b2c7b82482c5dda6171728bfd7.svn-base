﻿using ContinuityPatrol.Application.Features.Job.Commands.Create;
using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class JobManagementController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IMapper _mapper;
    private readonly ILogger<JobManagementController> _logger;
    private readonly IDataProvider _provider;

    public JobManagementController(IPublisher publisher, IMapper mapper, ILogger<JobManagementController> logger, IDataProvider provider)
    {
        _publisher = publisher;
        _logger = logger;
        _mapper = mapper;
        _provider = provider;
    }
    [EventCode(EventCodes.JobManagement.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in job management.");
        try
        {
            await _publisher.Publish(new JobPaginatedEvent());
            var TemplateName = new Dictionary<string, string>();

            var jobView = await _provider.JobService.GetJobPaginatedList(new GetJobPaginatedListQuery());
            var templateName = await _provider.Template.GetTemplateList();

            var monitoringTemplates = templateName.SelectMany(item => item.TemplateListVm).Where(item1 => item1.ActionType == "Monitoring").ToDictionary(item1 => item1.Id, item1 => item1.Name);

            var infraObjects = await _provider.InfraObject.GetInfraObjectNames();
            var groupPolicies = await _provider.GroupPolicy.GetGroupPolicies();
            var replicationTypeList = await _provider.ComponentType.GetComponentTypeListByName("Replication");
   
            var replicationTypeDictionary = new Dictionary<string, string>();

            foreach (var item in replicationTypeList)
            {
                var value = JsonConvert.DeserializeObject<dynamic>(item.Properties);
                var name = value!.SelectToken("name").ToString();

                //replicationTypes.Add(name);
                var id = item.Id;
                if (id != null && name != null)
                {
                    replicationTypeDictionary[name] = item.Id;
                }
            }

            var jobModel = new JobViewModel
            {
                PaginatedJob = jobView,
                Template = monitoringTemplates,
                ReplicationTypes = replicationTypeDictionary,
                InfraObjectNameVms = infraObjects,
                GroupPolicies = groupPolicies
            };
            _logger.LogDebug($"Successfully retrieved list in job management.");

            return View(jobModel);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on job management page while retrieving the job management list.", ex);

            TempData.Set(new NotificationMessage(NotificationType.Error, ex.GetMessage()));
            
            return View("List");
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.JobManagement.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(JobViewModel jobModel)
    {
        BaseResponse result;
        _logger.LogDebug("Entering CreateOrUpdate method in job management.");
        try
        {
            if (string.IsNullOrEmpty(jobModel.Id))
            {
                var jobCommand = _mapper.Map<CreateJobCommand>(jobModel);

                _logger.LogDebug($"Creating job management '{jobCommand.Name}'");

                result = await _provider.JobService.CreateAsync(jobCommand);
            }
            else
            {
                var jobCommand = _mapper.Map<UpdateJobCommand>(jobModel);

                _logger.LogDebug($"Updating job management '{jobCommand.Name}'");

                result = await _provider.JobService.UpdateAsync(jobCommand);
            }

                _logger.LogDebug("CreateOrUpdate operation completed successfully in job management, returning view.");

            return RouteToPostView(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation Error on management {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on job management page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.JobManagement.ResetJobStatus)]
    public async Task<IActionResult> ResetJobStatus(JobViewModel jobModel)
    {
        _logger.LogDebug("Entering ResetJobStatus method in job management.");
        try
        {
            jobModel.Status = "Pending";
            var jobCommand = _mapper.Map<UpdateJobCommand>(jobModel);
            var result = await _provider.JobService.UpdateAsync(jobCommand);

            _logger.LogDebug($"Successfully reset job status to {jobModel.Status} in  job management.");

            return Json(new { Success = true, data = result });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on job management page while reset job status.",ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.JobManagement.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in job management.");
        try
        {
            var job = await _provider.JobService.DeleteAsync(id);

            _logger.LogDebug($"Successfully Deleted record by id {id} in job management.");
            return RouteToPostView(job);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on job management page while Deleting the record.",ex);
            return ex.GetJsonException();
        }
    }


    [HttpGet]
    [EventCode(EventCodes.JobManagement.IsExist)]
    public async Task<bool> IsJobNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsJobNameExist method in job management.");
        try
        {
            var isJobnameExits = await _provider.JobService.IsJobNameExist(name, id);

            _logger.LogDebug($"Successfully retrieved name exist detail in job management.");

            return isJobnameExits;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on job management page while retrieving the name exist detail.",ex);
            return false;
        }
    }

    private IActionResult RouteToPostView(BaseResponse result)
    {
        _logger.LogDebug("Entering RouteToPostView method in job management.");
        try
        {
            TempData.Set(result.Success
            ? new NotificationMessage(NotificationType.Success, result.Message)
            : new NotificationMessage(NotificationType.Error, result.Message)
            );

            return RedirectToAction("List", "JobManagement", new { Area = "Manage" });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on job management page while redirect to list.",ex);

            return ex.GetJsonException();
        }
    }
    [EventCode(EventCodes.JobManagement.Pagination)]
    public async Task<JsonResult> GetPagination(GetJobPaginatedListQuery query)
  {
        _logger.LogDebug("Entering GetPagination method in job management.");
        try
        {
            var paginationList = await _provider.JobService.GetJobPaginatedList(query);

            _logger.LogDebug($"Successfully retrieved pagination list in job management.");

            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on job management page while retrieving the pagination list.",ex);

            return ex.GetJsonException();
        }

    }

    [HttpGet]
    [EventCode(EventCodes.JobManagement.GetGroupPolicyNames)]
    public async Task<IActionResult> GetGroupPolicyNames()
    {
        _logger.LogDebug("Entering GetGroupPolicyNames method in job management.");
        try
        {
            var groupPolicies = await _provider.GroupPolicy.GetGroupPolicyNames();

            _logger.LogDebug($"Successfully retrieved group policy names in job management.");

            return Json(groupPolicies);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on job management page while retrieving group policy names.",ex);
            return ex.GetJsonException();
        }

    }
    [HttpPut]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.JobManagement.UpdateJobState)]
    public async Task<IActionResult>UpdateJobState(UpdateJobStateCommand updateJobStateCommand)
    {
        _logger.LogDebug("Entering UpdateJobState method in job management.");
        try
        {
            var getState = await _provider.JobService.UpdateJobState(updateJobStateCommand);

            _logger.LogDebug($"Successfully updated job state in job management.");

            return Json(new { Success = true, data = getState });
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on job management page while updating the job state.",ex);

            return ex.GetJsonException();
        }

    }
    [EventCode(EventCodes.JobManagement.GetInfraObjectListByReplicationTypeId)]
    public async Task<JsonResult> GetInfraObjectListByReplicationTypeId(string replicationTypeId)
    {
        _logger.LogDebug("Entering GetInfraObjectListByReplicationTypeId method in job management.");

        if (string.IsNullOrWhiteSpace(replicationTypeId))
        {

            return Json(new { Success = false, Message = "replicationTypeId is not valid format", ErrorCode = 0 });
        }
        else
        {
            try
            {
                var result = await _provider.InfraObject.GetInfraObjectListByReplicationTypeId(replicationTypeId);

                _logger.LogDebug($"Successfully retrieved infra object list by replication type id {replicationTypeId} in job management.");

                return Json(new { Success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.Exception($"An error occurred on job management page while retrieving infra object list by replication type id.",ex);

                return ex.GetJsonException();
            }
        }
    }

}
