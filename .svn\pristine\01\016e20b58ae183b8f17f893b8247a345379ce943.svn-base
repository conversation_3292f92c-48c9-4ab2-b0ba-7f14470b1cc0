﻿using ContinuityPatrol.Infrastructure.Impl;
using ContinuityPatrol.Shared.Infrastructure.Hubs;
using Newtonsoft.Json;
using Seq.Api.Client;

namespace ContinuityPatrol.Infrastructure.UnitTests.Impl;

public class SeqServiceTests
{
    private readonly Mock<IConfiguration> _configMock;
    private readonly Mock<IHubContext<LogHub>> _hubContextMock;
    private readonly Mock<ILogger<SeqService>> _loggerMock;
    private readonly SeqService _seqService;

    public SeqServiceTests()
    {
        _configMock = new Mock<IConfiguration>();
        _hubContextMock = new Mock<IHubContext<LogHub>>();
        _loggerMock = new Mock<ILogger<SeqService>>();

        // PROPERLY mock configuration with valid URLs
        var serverUrlSection = new Mock<IConfigurationSection>();
        serverUrlSection.Setup(x => x.Value).Returns("https://valid-seq-server.com");

        var usernameSection = new Mock<IConfigurationSection>();
        usernameSection.Setup(x => x.Value).Returns("encrypted_user");

        var passwordSection = new Mock<IConfigurationSection>();
        passwordSection.Setup(x => x.Value).Returns("encrypted_pass");

        var pathSection = new Mock<IConfigurationSection>();
        pathSection.Setup(x => x.Value).Returns("/valid/path");

        _configMock.Setup(x => x.GetSection("SeqConfig:ServerUrl"))
                 .Returns(serverUrlSection.Object);
        _configMock.Setup(x => x.GetSection("SeqConfig:Username"))
                 .Returns(usernameSection.Object);
        _configMock.Setup(x => x.GetSection("SeqConfig:Password"))
                 .Returns(passwordSection.Object);
        _configMock.Setup(x => x.GetSection("SeqConfig:path"))
                 .Returns(pathSection.Object);

        _seqService = new SeqService(_configMock.Object, _hubContextMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task LoginAsync_SuccessfulLogin_SetsConnectionProperties()
    {
        // Arrange
        var mockHttp = new Mock<HttpMessageHandler>();
        mockHttp.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Headers = { { "Set-Cookie", "Seq-Session=token; Path=/; Domain=valid-seq-server.com" } }
            });

        // Inject the mock HTTP handler
        var httpClient = new HttpClient(mockHttp.Object);
        var options = new RestClientOptions("https://valid-seq-server.com");
        var restClient = new RestClient(httpClient, options);

        // Use reflection to inject the client
        var clientField = typeof(SeqService).GetField("_client",
            BindingFlags.NonPublic | BindingFlags.Instance);
        clientField!.SetValue(_seqService, restClient);

        // Act
        await _seqService.LoginAsync();

        // Assert
        Assert.True((bool)typeof(SeqService)
            .GetField("_isConnected", BindingFlags.NonPublic | BindingFlags.Instance)!
            .GetValue(_seqService)!);
    }


    [Fact]
    public async Task LoginAsync_FailedLogin_LogsError()
    {
        // Arrange
        var mockHttp = new Mock<HttpMessageHandler>();
        
        mockHttp.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.Unauthorized
            });

        // Act
        await _seqService.LoginAsync();

        // Assert
        Assert.False(GetPrivateField<bool>("_isConnected"));

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Unable to establish seq connection")),
                It.IsAny<Exception>(),
                It.Is<Func<It.IsAnyType, Exception, string>>((v, t) => true)!),
            Times.Once);
    }

    [Fact]
    public async Task LogoutAsync_WithValidSession_LogsOutSuccessfully()
    {
        // Arrange
        // First login to set the session
        var mockHttp = new Mock<HttpMessageHandler>();
        mockHttp.Protected()
            .SetupSequence<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Headers = { { "Set-Cookie", "Seq-Session=token; Path=/; Domain=test-seq-server.com" } }
            })
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK
            });

        var httpClient = new HttpClient(mockHttp.Object);
        var options = new RestClientOptions("https://test-seq-server.com");
        var restClient = new RestClient(httpClient, options);

        var clientField = typeof(SeqService).GetField("_client",
            BindingFlags.NonPublic | BindingFlags.Instance);
        clientField!.SetValue(_seqService, restClient);

        await _seqService.LoginAsync();

        // Act
        await _seqService.LogoutAsync();

        // Assert
        Assert.Null((string)typeof(SeqService)
            .GetField("_authCookie", BindingFlags.NonPublic | BindingFlags.Instance)!
            .GetValue(_seqService)!);
        Assert.False((bool)typeof(SeqService)
            .GetField("_isConnected", BindingFlags.NonPublic | BindingFlags.Instance)!
            .GetValue(_seqService)!);
    }

    [Fact]
    public async Task GetSeqLogsByGroupIdAsync_WithValidSession_ReturnsLogs()
    {
        // Arrange
        var mockHttp = new Mock<HttpMessageHandler>();
        mockHttp.Protected()
            .SetupSequence<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Headers = { { "Set-Cookie", "Seq-Session=token; Path=/; Domain=test-seq-server.com" } }
            })
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(JsonConvert.SerializeObject(new LogResponse
                {
                    Rows = new List<List<object>>
                    {
                        new()
                        {
                            DateTime.UtcNow.ToString("o"),
                            DateTime.UtcNow.ToString("o"),
                            JsonConvert.SerializeObject(new LogHubVm
                            {
                                Message = "Test message",
                                Level = "Information"
                            })
                        }
                    }
                }))
            });

        var httpClient = new HttpClient(mockHttp.Object);
        var options = new RestClientOptions("https://test-seq-server.com");
        var restClient = new RestClient(httpClient, options);

        var clientField = typeof(SeqService).GetField("_client",
            BindingFlags.NonPublic | BindingFlags.Instance);
        clientField!.SetValue(_seqService, restClient);

        await _seqService.LoginAsync();

        // Act
        var result = await _seqService.GetSeqLogsByGroupIdAsync("test-group");

        // Assert
        Assert.NotEmpty(result);
        Assert.Equal("Test message", result[0].Message);
    }


    [Fact]
    public Task ConvertToFormat_ConvertsUtcToLocalTime()
    {
        // Arrange
        var testDate = DateTime.UtcNow;

        // Act
        var result = _seqService.ConvertToFormat(testDate);

        // Assert
        Assert.Contains(testDate.ToLocalTime().ToString("dd-MM-yyyy"), result);
        return Task.CompletedTask;
    }

    [Fact]
    public async Task LoginAsync_WhenSeqApiExceptionThrown_ShouldLogErrorAndSetIsConnectedFalse()
    {
        // Arrange
        var mockHttp = new Mock<HttpMessageHandler>();
        var exception = new SeqApiException("Simulated failure", HttpStatusCode.InternalServerError);

        mockHttp.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(exception);

        var httpClient = new HttpClient(mockHttp.Object);
        var restClient = new RestClient(httpClient, new RestClientOptions("https://valid-seq-server.com"));

        typeof(SeqService).GetField("_client", BindingFlags.NonPublic | BindingFlags.Instance)!
            .SetValue(_seqService, restClient);

        // Act
        await _seqService.LoginAsync();

        // Assert
        Assert.False(GetPrivateField<bool>("_isConnected"));

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) =>
                    v.ToString()!.Contains("LoginAsync SeqApiException")),
                It.IsAny<SeqApiException>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);
    }





    //[Fact]
    //public void Constructor_SetsUpRestClientWithCertificateValidationCallback()
    //{
    //    // Arrange
    //    var mockConfig = new Mock<IConfiguration>();
    //    var configSection = new Mock<IConfigurationSection>();
    //    configSection.Setup(x => x.Value).Returns("https://test-seq-server.com");
    //    mockConfig.Setup(x => x.GetSection("SeqConfig:ServerUrl")).Returns(configSection.Object);

    //    var mockHubContext = new Mock<IHubContext<LogHub>>();
    //    var mockLogger = new Mock<ILogger<SeqService>>();

    //    // Act
    //    var service = new SeqService(mockConfig.Object, mockHubContext.Object, mockLogger.Object);

    //    // Assert
    //    // Use reflection to get the private _client field
    //    var clientField = typeof(SeqService).GetField("_client", BindingFlags.NonPublic | BindingFlags.Instance);
    //    var client = (RestClient)clientField!.GetValue(service)!;

    //    // Get the HttpClient from the RestClient
    //    var httpClientField = typeof(RestClient).GetField("_httpClient", BindingFlags.NonPublic | BindingFlags.Instance);
    //    var httpClient = (HttpClient)httpClientField!.GetValue(client)!;

    //    // Get the HttpClientHandler from the HttpClient
    //    var handlerField = typeof(HttpClient).GetField("_handler", BindingFlags.NonPublic | BindingFlags.Instance);
    //    var handler = (HttpClientHandler)handlerField!.GetValue(httpClient)!;

    //    // Verify the callback is set and works as expected
    //    Assert.NotNull(handler.ServerCertificateCustomValidationCallback);

    //    // Test that the callback always returns true regardless of input
    //    var result = handler.ServerCertificateCustomValidationCallback(
    //        new HttpRequestMessage(),
    //        new X509Certificate2(),
    //        new X509Chain(),
    //        SslPolicyErrors.None);

    //    Assert.True(result);

    //    result = handler.ServerCertificateCustomValidationCallback(
    //        new HttpRequestMessage(),
    //        new X509Certificate2(),
    //        new X509Chain(),
    //        SslPolicyErrors.RemoteCertificateChainErrors);

    //    Assert.True(result);
    //}

    //[Fact]
    //public async Task LoginAsync_ExceptionThrown_ShouldLogError()
    //{
    //    // Arrange: simulate exception
    //    var mockHttp = new Mock<HttpMessageHandler>();
    //    mockHttp.Protected()
    //        .Setup<Task<HttpResponseMessage>>("SendAsync",
    //            ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
    //        .ThrowsAsync(new Exception("network error"));

    //    var restClient = new RestClient(new HttpClient(mockHttp.Object), new RestClientOptions("https://valid-seq-server.com"));

    //    typeof(SeqService).GetField("_client", BindingFlags.NonPublic | BindingFlags.Instance)!
    //        .SetValue(_seqService, restClient);

    //    // Act
    //    await _seqService.LoginAsync();

    //    // Assert
    //    Assert.False(GetPrivateField<bool>("_isConnected"));

    //    _loggerMock.Verify(
    //        x => x.Log(
    //            LogLevel.Error,
    //            It.IsAny<EventId>(),
    //            It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("LoginAsync Exception")),
    //            It.IsAny<Exception>(),
    //            It.IsAny<Func<It.IsAnyType, Exception, string>>()!), Times.AtLeastOnce);
    //}




    private void SetupSuccessfulLogin()
    {
        SetPrivateField("_isConnected", true);
        SetPrivateField("_authCookie", "valid_token");
        SetPrivateField("_domain", "valid-seq-server.com");
        SetPrivateField("_path", "/");
    }

    private T GetPrivateField<T>(string fieldName)
    {
        var field = typeof(SeqService).GetField(fieldName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return ((T)field?.GetValue(_seqService)!);
    }

    private void SetPrivateField<T>(string fieldName, T value)
    {
        var field = typeof(SeqService).GetField(fieldName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        field.SetValue(_seqService, value);
    }
}