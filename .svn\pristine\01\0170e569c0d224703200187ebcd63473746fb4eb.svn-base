using ContinuityPatrol.Domain.ViewModels.CyberComponentMappingModel;

namespace ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetList;

public class
    GetCyberComponentMappingListQueryHandler : IRequestHandler<GetCyberComponentMappingListQuery,
        List<CyberComponentMappingListVm>>
{
    private readonly ICyberComponentMappingRepository _cyberComponentMappingRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentMappingListQueryHandler(IMapper mapper,
        ICyberComponentMappingRepository cyberComponentMappingRepository)
    {
        _mapper = mapper;
        _cyberComponentMappingRepository = cyberComponentMappingRepository;
    }

    public async Task<List<CyberComponentMappingListVm>> Handle(GetCyberComponentMappingListQuery request,
        CancellationToken cancellationToken)
    {
        var cyberComponentMappings = await _cyberComponentMappingRepository.ListAllAsync();

        if (cyberComponentMappings.Count <= 0) return new List<CyberComponentMappingListVm>();

        return _mapper.Map<List<CyberComponentMappingListVm>>(cyberComponentMappings);
    }
}