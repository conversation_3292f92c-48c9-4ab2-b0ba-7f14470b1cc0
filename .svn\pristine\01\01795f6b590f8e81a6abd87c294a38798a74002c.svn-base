﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class LicenseInfoFilterSpecification : Specification<LicenseInfo>
{
    public LicenseInfoFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("Licenseid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.LicenseId.Contains(stringItem.Replace("Licenseid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("ipaddress=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityField.Contains(stringItem.Replace("ipaddress=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("businessserviceid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BusinessServiceId.Contains(stringItem.Replace("businessserviceid=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("type=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityName.Contains(stringItem.Replace("name=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("entityid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EntityId.Contains(stringItem.Replace("entityid=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.ReferenceId.Contains(searchString) || p.EntityField.Contains(searchString) ||
                                p.LicenseId.Contains(searchString) || p.EntityId.Contains(searchString) ||
                                p.EntityName.Contains(searchString) || p.Type.Contains(searchString) ||
                                p.BusinessServiceId.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.EntityName != null;
        }
    }
}