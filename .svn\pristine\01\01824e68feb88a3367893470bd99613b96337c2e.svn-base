using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DataLagRepositoryMocks
{
    /// <summary>
    /// Creates a comprehensive mock IDataLagRepository for CRUD operations
    /// Purpose: DataLag manages business service data lag monitoring and infrastructure object tracking
    /// </summary>
    public static Mock<IDataLagRepository> CreateDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        // ListAllAsync - Returns only active entities
        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(dataLags.Where(x => x.IsActive).ToList());

        // GetByReferenceIdAsync - Returns active entity by reference ID
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => dataLags.FirstOrDefault(x => x.ReferenceId == id && x.IsActive));

        // AddAsync - Adds new DataLag entity
        mockRepository.Setup(repo => repo.AddAsync(It.IsAny<DataLag>()))
            .ReturnsAsync((DataLag dataLag) =>
            {
                dataLag.Id = new Fixture().Create<int>();
                dataLag.ReferenceId = new Fixture().Create<Guid>().ToString();
                dataLags.Add(dataLag);
                return dataLag;
            });

        // UpdateAsync - Updates existing DataLag entity
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DataLag>()))
            .ReturnsAsync((DataLag dataLag) =>
            {
                var index = dataLags.FindIndex(item => item.ReferenceId == dataLag.ReferenceId);
                if (index >= 0)
                {
                    dataLags[index] = dataLag;
                }
                return dataLag;
            });

        // DeleteAsync - Performs soft delete by setting IsActive to false
        mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<DataLag>()))
            .ReturnsAsync((DataLag dataLag) =>
            {
                var existingDataLag = dataLags.FirstOrDefault(x => x.ReferenceId == dataLag.ReferenceId);
                if (existingDataLag != null)
                {
                    existingDataLag.IsActive = false;
                }
                return dataLag;
            });

        // GetDataLagByBusinessServiceId - Returns DataLag by business service ID
        mockRepository.Setup(repo => repo.GetDataLagByBusinessServiceId(It.IsAny<string>()))
            .ReturnsAsync((string businessServiceId) =>
                dataLags.FirstOrDefault(x => x.BusinessServiceId == businessServiceId && x.IsActive));

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for update operations
    /// </summary>
    public static Mock<IDataLagRepository> UpdateDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = CreateDataLagRepository(dataLags);

        // Override UpdateAsync for specific update behavior
        mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DataLag>()))
            .ReturnsAsync((DataLag dataLag) =>
            {
                var index = dataLags.FindIndex(item => item.ReferenceId == dataLag.ReferenceId);
                if (index >= 0)
                {
                    dataLags[index] = dataLag;
                }
                return dataLag;
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for delete operations (soft delete)
    /// </summary>
    public static Mock<IDataLagRepository> DeleteDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = CreateDataLagRepository(dataLags);

        // Override GetByReferenceIdAsync to include inactive entities for delete operations
        mockRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((string id) => dataLags.FirstOrDefault(x => x.ReferenceId == id));

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository that returns empty results
    /// </summary>
    public static Mock<IDataLagRepository> GetDataLagEmptyRepository()
    {
        return CreateDataLagRepository(new List<DataLag>());
    }

    /// <summary>
    /// Creates a mock repository for business service filtering
    /// </summary>
    public static Mock<IDataLagRepository> GetDataLagByBusinessServiceRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.GetDataLagByBusinessServiceId(It.IsAny<string>()))
            .ReturnsAsync((string businessServiceId) =>
            {
                if (string.IsNullOrEmpty(businessServiceId))
                    return null;

                return dataLags.FirstOrDefault(x => 
                    x.BusinessServiceId == businessServiceId && x.IsActive);
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for business service name validation
    /// </summary>
    public static Mock<IDataLagRepository> GetDataLagBusinessServiceValidationRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(dataLags.Where(x => x.IsActive).ToList());

        mockRepository.Setup(repo => repo.GetDataLagByBusinessServiceId(It.IsAny<string>()))
            .ReturnsAsync((string businessServiceId) =>
                dataLags.FirstOrDefault(x => x.BusinessServiceId == businessServiceId && x.IsActive));

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for high-impact business functions
    /// </summary>
    public static Mock<IDataLagRepository> GetHighImpactDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags with high impact (BFImpact > threshold)
                return dataLags.Where(x => x.IsActive && x.BFImpact > 5).ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for infrastructure threshold monitoring
    /// </summary>
    public static Mock<IDataLagRepository> GetInfraThresholdDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags where infrastructure exceeds threshold
                return dataLags.Where(x => x.IsActive && x.InfraExceed > x.InfraThreshold).ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for business function availability monitoring
    /// </summary>
    public static Mock<IDataLagRepository> GetBFAvailabilityDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags with low business function availability
                var lowAvailabilityThreshold = 0.8; // 80%
                return dataLags.Where(x => x.IsActive && 
                    x.TotalBusinessFunction > 0 &&
                    (double)x.BFAvailable / x.TotalBusinessFunction < lowAvailabilityThreshold).ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for critical infrastructure monitoring
    /// </summary>
    public static Mock<IDataLagRepository> GetCriticalInfraDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags with critical infrastructure issues
                return dataLags.Where(x => x.IsActive && 
                    (x.InfraNotAvailable > 0 || x.InfraUnderConfigured > 0)).ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for business service performance analysis
    /// </summary>
    public static Mock<IDataLagRepository> GetPerformanceAnalysisDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags sorted by performance metrics
                return dataLags.Where(x => x.IsActive)
                    .OrderByDescending(x => x.BFAvailable + x.InfraAvailable)
                    .ThenBy(x => x.BFImpact + x.InfraImpact)
                    .ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for data lag trend analysis
    /// </summary>
    public static Mock<IDataLagRepository> GetTrendAnalysisDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = new Mock<IDataLagRepository>();

        mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(() =>
            {
                // Return DataLags with trend indicators
                return dataLags.Where(x => x.IsActive)
                    .Where(x => x.BFExceed > 0 || x.InfraExceed > 0)
                    .OrderByDescending(x => x.BFExceed + x.InfraExceed)
                    .ToList();
            });

        return mockRepository;
    }

    /// <summary>
    /// Creates a mock repository for comprehensive business service monitoring
    /// </summary>
    public static Mock<IDataLagRepository> GetComprehensiveMonitoringDataLagRepository(List<DataLag> dataLags)
    {
        var mockRepository = CreateDataLagRepository(dataLags);

        // Add specialized monitoring methods
        mockRepository.Setup(repo => repo.GetDataLagByBusinessServiceId(It.IsAny<string>()))
            .ReturnsAsync((string businessServiceId) =>
            {
                var dataLag = dataLags.FirstOrDefault(x => x.BusinessServiceId == businessServiceId && x.IsActive);
                
                // Simulate real-time monitoring updates
                if (dataLag != null)
                {
                    // Update availability metrics (simulation)
                    dataLag.BFAvailable = Math.Max(0, dataLag.BFAvailable);
                    dataLag.InfraAvailable = Math.Max(0, dataLag.InfraAvailable);
                }
                
                return dataLag;
            });

        return mockRepository;
    }
}
