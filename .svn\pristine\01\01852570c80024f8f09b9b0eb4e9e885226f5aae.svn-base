﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Update;

public class FormUpdatedEventHandler : INotificationHandler<FormUpdatedEvent>
{
    private readonly ILogger<FormUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormUpdatedEventHandler(ILoggedInUserService userService, ILogger<FormUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Form '{updatedEvent.FormName}' updated successfully."
        };
        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Form '{updatedEvent.FormName}' updated successfully.");
    }
}