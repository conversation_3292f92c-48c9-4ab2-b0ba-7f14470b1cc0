﻿using ContinuityPatrol.Domain.ViewModels.BusinessServiceHealthStatusModel;

namespace ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetList;

public class GetBusinessServiceHealthStatusListQueryHandler : IRequestHandler<GetBusinessServiceHealthStatusListQuery,
    List<BusinessServiceHealthStatusListVm>>
{
    private readonly IBusinessServiceHealthStatusRepository _businessServiceHealthRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceHealthStatusListQueryHandler(
        IBusinessServiceHealthStatusRepository businessServiceHealthRepository, IMapper mapper)
    {
        _businessServiceHealthRepository = businessServiceHealthRepository;
        _mapper = mapper;
    }

    public async Task<List<BusinessServiceHealthStatusListVm>> Handle(GetBusinessServiceHealthStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var businessServiceHealth = await _businessServiceHealthRepository.ListAllAsync();

        return businessServiceHealth.Count <= 0
            ? new List<BusinessServiceHealthStatusListVm>()
            : _mapper.Map<List<BusinessServiceHealthStatusListVm>>(businessServiceHealth);
    }
}