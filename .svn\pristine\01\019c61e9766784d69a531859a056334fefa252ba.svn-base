﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Events.Create;

public class WorkflowOperationGroupCreatedEventHandler : INotificationHandler<WorkflowOperationGroupCreatedEvent>
{
    private readonly IGlobalSettingRepository _globalSettingRepository;
    private readonly ILogger<WorkflowOperationGroupCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;
    private readonly IWorkflowDrCalenderRepository _workflowDrCalenderRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;

    public WorkflowOperationGroupCreatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowOperationGroupCreatedEventHandler> logger, IUserActivityRepository userActivityRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository,
        IWorkflowDrCalenderRepository workflowDrCalenderRepository, IGlobalSettingRepository globalSettingRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _workflowDrCalenderRepository = workflowDrCalenderRepository;
        _globalSettingRepository = globalSettingRepository;
    }

    public async Task Handle(WorkflowOperationGroupCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            $"Workflow Profile '{createdEvent.ProfileName}' execution '{createdEvent.RunMode}' started successfully.");

        foreach (var action in createdEvent.Actions)
        {
            var workflowProfileInfoDtl =
                await _workflowProfileInfoRepository.GetWorkflowProfileInfoByWorkflowIdAndInfraObjectId(
                    action.WorkflowId, action.InfraObjectId);
            var userActivity = new Domain.Entities.UserActivity
            {
                UserId = _userService.UserId,
                LoginName = _userService.LoginName,
                CompanyId = _userService.CompanyId,
                RequestUrl = _userService.RequestedUrl,
                HostAddress = _userService.IpAddress,
                Action = $"{ActivityType.Execute} Workflow Profile execution",
                Entity = "Workflow Profile execution",
                ActivityType = ActivityType.Execute.ToString(),
                ActivityDetails =
                    $"Workflow Profile '{createdEvent.ProfileName}' execution '{createdEvent.RunMode}' started successfully." +
                    $"OperationType:'{workflowProfileInfoDtl.WorkflowType}', Workflow Name: '{action.WorkflowName}', Action Mode: '{action.ActionMode}'."
            };

            await _userActivityRepository.AddAsync(userActivity);

            _logger.LogInformation(
                $"Workflow Profile '{createdEvent.ProfileName}' execution '{createdEvent.RunMode}' started successfully." +
                $"OperationType:'{workflowProfileInfoDtl.WorkflowType}', Workflow Name: '{action.WorkflowName}', Action Mode: '{action.ActionMode}'.");
        }

        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("DR Calander");

        if (globalSetting is not null && globalSetting.GlobalSettingValue.ToLower() == "true")
            if (createdEvent.IsDrCalendar)
                await _workflowDrCalenderRepository.AddAsync(new Domain.Entities.WorkflowDrCalender
                {
                    CompanyId = _userService.CompanyId,
                    UserName = _userService.LoginName,
                    ProfileId = createdEvent.ProfileId,
                    ProfileName = createdEvent.ProfileName,
                    Description = "",
                    DrCalenderId = createdEvent.DrCalendarId,
                    WorkflowOperationId = createdEvent.WorkflowOperationId,
                    Status = "Pending",
                    Properties = ""
                });
    }
}