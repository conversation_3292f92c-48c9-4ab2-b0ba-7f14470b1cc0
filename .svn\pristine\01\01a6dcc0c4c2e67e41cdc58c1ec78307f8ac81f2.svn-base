﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class HeatMapStatusViewRepository:BaseRepository<HeatMapStatusView>,IHeatMapStatusViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public HeatMapStatusViewRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService):base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
            
    }
    public override async Task<IReadOnlyList<HeatMapStatusView>> ListAllAsync()
    {
        var businessServices = base.QueryAll(businessService => businessService.IsActive);

        var businessServiceDto = MapHeatMapStatus(businessServices);

        return _loggedInUserService.IsAllInfra
            ? await businessServiceDto.ToListAsync()
            : AssignedBusinessServices(businessServiceDto);
    }

    public override async Task<HeatMapStatusView> GetByReferenceIdAsync(string id)
    {
        var result = base.GetByReferenceId(id,
           dataLags => dataLags.ReferenceId.Equals(id));

        var heatMap = MapHeatMapStatus(result);

        return _loggedInUserService.IsAllInfra
            ? await heatMap.FirstOrDefaultAsync()
            : AssignedBusinessServices(heatMap).OrderByDescending(x => x.Id).FirstOrDefault();
    }
    public async Task<PaginatedResult<HeatMapStatusView>> GetHeatMapStatusType(string type, int pageNumber, int pageSize, Specification<HeatMapStatusView> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await (_loggedInUserService.IsAllInfra
            ? MapHeatMapStatus(Entities
                .Specify(productFilterSpec)
                .Where(x => x.HeatmapType.Equals(type))
                .DescOrderById())
            : MapHeatMapStatus(GetPaginatedAssignedBusinessServicesByHeatMap(Entities
                .Specify(productFilterSpec)
                .Where(x => x.HeatmapType.Equals(type))
                .DescOrderById())))
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public async Task<List<HeatMapStatusView>> GetHeatMapStatusTypeAsync(string type, bool isAffected)
    {
        var heatMap = base.FilterBy(x => x.HeatmapType.Equals(type) && x.IsAffected.Equals(isAffected));

        return _loggedInUserService.IsAllInfra
            ? await heatMap.ToListAsync()
            : AssignedInfraObjectIds(heatMap).ToList();
    }


    public IQueryable<HeatMapStatusView> GetHeatMapStatusType(string type)
    {
        var heatMap = base.FilterBy(x => x.HeatmapType.Equals(type));

        var heatMapDto = MapHeatMapStatus(heatMap);

        return _loggedInUserService.IsAllInfra
            ? heatMapDto
         : GetPaginatedAssignedBusinessServicesByHeatMap(heatMapDto);
    }

    public async Task<List<HeatMapStatusView>> GetHeatMapStatusesByBusinessServiceIdAndType(string businessServiceId, string type, bool isAffected)
    {
        var heatMapStatus = base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId)
                            && x.HeatmapType.Equals(type) && x.IsAffected.Equals(isAffected));

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatus.ToListAsync()
            : AssignedInfraObjectIds(heatMapStatus).ToList();
    }

    public async Task<List<HeatMapStatusView>> GetHeatMapStatusByEntityId(string entityId)
    {
        var heatMapStatus = base.FilterBy(x => x.EntityId.Equals(entityId));

        var heatMapDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapDto.ToListAsync()
            : AssignedBusinessServices(heatMapDto).ToList();
    }
    public async Task<List<HeatMapStatusView>> GetHeatMapStatusByEntityIds(List<string> entityIds)
    {
        var heatMapStatus = base.FilterBy(x => entityIds.Contains(x.EntityId));

        var heatMapDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapDto.ToListAsync()
            : AssignedBusinessServices(heatMapDto).ToList();
    }


    public async Task<List<HeatMapStatusView>> GetImpactDetail()
    {
        var heatMap = base.FilterBy(x => x.IsAffected.Equals(true) && x.IsActive);

        var heatMapDto = MapHeatMapStatus(heatMap);

        return _loggedInUserService.IsAllInfra
            ? await heatMapDto.ToListAsync()
            : AssignedBusinessServices(heatMapDto).ToList();
    }
    public async Task<List<HeatMapStatusView>> GetHeatMapByInfraObjectIds(List<string> infraIds)
    {
        var heatMapStatus = base.FilterBy(infra => infraIds.Contains(infra.InfraObjectId));

        var heatMapStatusDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatusDto.ToListAsync()
            : AssignedInfraObjects(heatMapStatusDto).ToList();

    }
    public async Task<List<HeatMapStatusView>> GetHeatMapByInfraObjectId(string infraObjectId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "InfraObjectId", "InfraObjectId cannot be invalid");

        var heatMapStatus = base.FilterBy(infra => infra.InfraObjectId.Equals(infraObjectId));

        var heatMapStatusDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatusDto.ToListAsync()
            : AssignedInfraObjects(heatMapStatusDto).ToList();
    }

    public async Task<List<HeatMapStatusView>> GetHeatMapDetailByInfraObjectId(string infraObjectId, string heatMapType)
    {
        var heatMapStatus = base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.HeatmapType.Trim().ToLower().Equals(heatMapType.Trim().ToLower()));

        var heatMapStatusDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatusDto.ToListAsync()
            : AssignedInfraObjects(heatMapStatusDto).ToList();
    }

    public async Task<HeatMapStatusView> GetHeatMapDetailByInfraObjectAndEntityId(string infraObjectId, string entityId)
    {
        var heatMapStatus = GetByInfraObjectIdAsync(infraObjectId, entityId,
            infraObject => infraObject.InfraObjectId.Equals(infraObjectId) && infraObject.EntityId.Equals(entityId));

        var heatMapStatusDto = MapHeatMapStatus(heatMapStatus);

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatusDto.FirstOrDefaultAsync()
            : GetByInfraObjectId(heatMapStatusDto.FirstOrDefault());
    }

    public async Task<List<HeatMapStatusView>> GetHeatMapListByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "BusinessServiceId", "BusinessServiceId cannot be invalid");

        var businessServices = base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId));

        var heatMapStatusDto = MapHeatMapStatus(businessServices);

        return _loggedInUserService.IsParent
            ? await heatMapStatusDto.ToListAsync()
            : AssignedBusinessServices(heatMapStatusDto).ToList();
    }

    public async Task<List<HeatMapStatusView>> GetByHeatMapTypeAndBusinessServiceIds(string type, List<string> businessServiceIds)
    {
        var businessServices = base.FilterBy(x => x.HeatmapType.Equals(type) && businessServiceIds.Contains(x.BusinessServiceId))
            .Select(x => new HeatMapStatusView
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                BusinessServiceId = x.BusinessServiceId,
                BusinessServiceName = x.BusinessServiceName,
                HeatmapType = x.HeatmapType,
                IsAffected = x.IsAffected,
            });

        return _loggedInUserService.IsParent
            ? await businessServices.ToListAsync()
            : AssignedBusinessServices(businessServices).ToList();
    }

    public async Task<ImpactDetailVm> GroupHeatMapTypeByServiceId(string businessServiceId)
    {
        var businessServices = businessServiceId.IsNullOrWhiteSpace()
            ? _loggedInUserService.IsAllInfra
                ? base.FilterBy(x => x.IsAffected)
                : AssignedInfraObjectIds(base.FilterBy(x => x.IsAffected))
            : _loggedInUserService.IsAllInfra
                ? base.FilterBy(x => x.BusinessServiceId.Equals(businessServiceId) && x.IsAffected)
                : AssignedInfraObjectIds(base.FilterBy(x =>
                    x.BusinessServiceId.Equals(businessServiceId) && x.IsAffected));

        var heatMapStatusDto = await businessServices
            .GroupBy(x => x.BusinessServiceId)
            .Select(x => new ImpactDetailVm
            {
                ServerDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("server")),
                ReplicationDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("replication")),
                //NetworkDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("network")),
                // ApplicationDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("application")),
                // StorageDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("storage")),
                DatabaseDownCount = x.Count(heatMapStatus => heatMapStatus.HeatmapType.Trim().ToLower().Equals("database"))

            }).FirstOrDefaultAsync();

        return heatMapStatusDto;

    }





    public async Task<List<HeatMapStatusView>> GetHeatMapListByBusinessFunctionId(string businessFunctionId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessFunctionId, "BusinessFunctionId",
            "BusinessFunctionId cannot be invalid");

        var businessFunctions = base.FilterBy(x => x.BusinessFunctionId.Equals(businessFunctionId));

        var heatMapStatusDto = MapHeatMapStatus(businessFunctions);

        return _loggedInUserService.IsAllInfra
            ? await heatMapStatusDto.ToListAsync()
            : AssignedBusinessFunctions(heatMapStatusDto).ToList();
    }

    //Filters

    public IQueryable<HeatMapStatusView> AssignedInfraObjectIds(IQueryable<HeatMapStatusView> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(businessService => businessService.AssignedBusinessFunctions)
            .SelectMany(businessFunction => businessFunction.AssignedInfraObjects)
            .Select(infraObject => infraObject.Id);

        return infraObjects.Where(infraObject => assignedInfraObjectIds.Contains(infraObject.InfraObjectId));
    }


    public IReadOnlyList<HeatMapStatusView> AssignedBusinessServices(IQueryable<HeatMapStatusView> businessServices)
    {
        var services = new List<HeatMapStatusView>();

        foreach (var businessService in businessServices)
            if (AssignedEntity.AssignedBusinessServices.Count > 0)
                services.AddRange(from assignedBusinessService in AssignedEntity.AssignedBusinessServices
                                  where businessService.BusinessServiceId == assignedBusinessService.Id
                                  select businessService);
        return services;
    }

    public IReadOnlyList<HeatMapStatusView> AssignedBusinessFunctions(IQueryable<HeatMapStatusView> businessFunctions)
    {
        var functions = new List<HeatMapStatusView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        foreach (var businessFunction in businessFunctions)
            if (assignedBusinessFunctions.Count > 0)
                functions.AddRange(from assignedBusinessFunction in assignedBusinessFunctions
                                   where businessFunction.BusinessFunctionId == assignedBusinessFunction.Id
                                   select businessFunction);
        return functions;
    }

    public IReadOnlyList<HeatMapStatusView> AssignedInfraObjects(IQueryable<HeatMapStatusView> infraObjects)
    {
        var infraObjectList = new List<HeatMapStatusView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity.AssignedBusinessServices.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity.AssignedBusinessServices)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                                         where infraObject.InfraObjectId == assignedInfraObject.Id
                                         select infraObject);
        return infraObjectList;
    }

    public IQueryable<HeatMapStatusView> GetByInfraObjectIdAsync(string id, string entityId,
        Expression<Func<HeatMapStatusView, bool>> expression = null)
    {
        return _loggedInUserService.IsParent
            ? Entities.Where(x => x.InfraObjectId.Equals(id) && x.EntityId.Equals(entityId))
            : FilterBy(expression);
    }

    //private IQueryable<HeatMapStatus> AssignedBusinessServicesIds(IQueryable<HeatMapStatus> businessServices)
    //{
    //    var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

    //    return businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));
    //}

    public HeatMapStatusView GetByInfraObjectId(HeatMapStatusView infraObject)
    {
        var services = AssignedEntity.AssignedBusinessServices
            .SelectMany(assignedBusinessService => assignedBusinessService.AssignedBusinessFunctions)
            .SelectMany(assignedBusinessFunction => assignedBusinessFunction.AssignedInfraObjects)
            .Where(assignedInfraObjects => infraObject?.InfraObjectId == assignedInfraObjects.Id)
            .Select(_ => infraObject).SingleOrDefault();

        return services;
    }
    private IQueryable<HeatMapStatusView> GetPaginatedAssignedBusinessServicesByHeatMap(IQueryable<HeatMapStatusView> businessServices)
    {
        var assignedServiceIds = AssignedEntity.AssignedBusinessServices.Select(s => s.Id);

        return businessServices.Where(s => assignedServiceIds.Contains(s.BusinessServiceId));
    }
    private IQueryable<HeatMapStatusView> MapHeatMapStatus(IQueryable<HeatMapStatusView> heatmap)
    {
        var mappedHeatMapStatus = heatmap.Select(data => new
        {
            HeatMapStatus = data,
            BusinessService = _dbContext.BusinessServices.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessServiceId)),
            BusinessFunction = _dbContext.BusinessFunctions.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.BusinessFunctionId)),
            InfraObject = _dbContext.InfraObjects.Active().AsNoTracking().FirstOrDefault(x => x.ReferenceId.Equals(data.InfraObjectId)),
        });

        var mappedHeatMapStatusQuery = mappedHeatMapStatus.Select(result => new HeatMapStatusView
        {
            Id = result.HeatMapStatus.Id,
            ReferenceId = result.HeatMapStatus.ReferenceId,
            BusinessServiceId = result.BusinessService.ReferenceId,
            BusinessServiceName = result.BusinessService.Name,
            BusinessFunctionId = result.BusinessFunction.ReferenceId,
            BusinessFunctionName = result.BusinessFunction.Name,
            InfraObjectId = result.InfraObject.ReferenceId,
            InfraObjectName = result.InfraObject.Name,
            EntityId = result.HeatMapStatus.EntityId,
            HeatmapType = result.HeatMapStatus.HeatmapType,
            HeatmapStatus = result.HeatMapStatus.HeatmapStatus,
            IsAffected = result.HeatMapStatus.IsAffected,
            ErrorMessage = result.HeatMapStatus.ErrorMessage,
            Properties = result.HeatMapStatus.Properties,
            IsActive = result.HeatMapStatus.IsActive,
            CreatedBy = result.HeatMapStatus.CreatedBy,
            CreatedDate = result.HeatMapStatus.CreatedDate,
            LastModifiedBy = result.HeatMapStatus.LastModifiedBy,
            LastModifiedDate = result.HeatMapStatus.LastModifiedDate,
        });

        return mappedHeatMapStatusQuery;
    }
} 
