using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;

public class GetDynamicSubDashboardPaginatedListQueryHandler : IRequestHandler<GetDynamicSubDashboardPaginatedListQuery,
    PaginatedResult<DynamicSubDashboardListVm>>
{
    private readonly IDynamicSubDashboardRepository _dynamicSubDashboardRepository;
    private readonly IMapper _mapper;

    public GetDynamicSubDashboardPaginatedListQueryHandler(IMapper mapper,
        IDynamicSubDashboardRepository dynamicSubDashboardRepository)
    {
        _mapper = mapper;
        _dynamicSubDashboardRepository = dynamicSubDashboardRepository;
    }

    public async Task<PaginatedResult<DynamicSubDashboardListVm>> Handle(
        GetDynamicSubDashboardPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new DynamicSubDashboardFilterSpecification(request.SearchString);

        var queryable = await _dynamicSubDashboardRepository.PaginatedListAllAsync(request.PageNumber,request.PageSize,productFilterSpec, request.SortColumn, request.SortOrder);

        var dynamicSubDashboardList = _mapper.Map<PaginatedResult<DynamicSubDashboardListVm>>(queryable);
           
        return dynamicSubDashboardList;
        //var queryable = _dynamicSubDashboardRepository.GetPaginatedQuery();

        //var productFilterSpec = new DynamicSubDashboardFilterSpecification(request.SearchString);

        //var dynamicSubDashboardList = await queryable
        //    .Specify(productFilterSpec)
        //    .Select(m => _mapper.Map<DynamicSubDashboardListVm>(m))
        //    .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        //return dynamicSubDashboardList;
    }
}