﻿using ContinuityPatrol.Application.Features.BusinessFunction.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessFunction.Events;

public class DeleteBusinessFunctionEventTests : IClassFixture<BusinessFunctionFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly BusinessFunctionDeletedEventHandler _handler;

    public DeleteBusinessFunctionEventTests(BusinessFunctionFixture businessFunctionFixture, UserActivityFixture userActivityFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockBusinessFunctionEventLogger = new Mock<ILogger<BusinessFunctionDeletedEventHandler>>();

        _mockUserActivityRepository = BusinessFunctionRepositoryMocks.CreateBusinessFunctionEventRepository(_userActivityFixture.UserActivities);

        _handler = new BusinessFunctionDeletedEventHandler(mockLoggedInUserService.Object, mockBusinessFunctionEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteBusinessFunctionEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_businessFunctionFixture.BusinessFunctionDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessFunctionFixture.BusinessFunctionDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}