namespace ContinuityPatrol.Application.Features.DynamicDashboardWidget.Queries.GetNameUnique;

public class
    GetDynamicDashboardWidgetNameUniqueQueryHandler : IRequestHandler<GetDynamicDashboardWidgetNameUniqueQuery, bool>
{
    private readonly IDynamicDashboardWidgetRepository _dynamicDashboardWidgetRepository;

    public GetDynamicDashboardWidgetNameUniqueQueryHandler(
        IDynamicDashboardWidgetRepository dynamicDashboardWidgetRepository)
    {
        _dynamicDashboardWidgetRepository = dynamicDashboardWidgetRepository;
    }

    public async Task<bool> Handle(GetDynamicDashboardWidgetNameUniqueQuery request,
        CancellationToken cancellationToken)
    {
        return await _dynamicDashboardWidgetRepository.IsNameExist(request.Name, request.Id);
    }
}