namespace ContinuityPatrol.Domain.ViewModels.IncidentManagementModel;

public record IncidentManagementListVm
{
    public string Id { get; set; }
    public string IncidentName { get; set; }
    public DateTime IncidentTime { get; set; }
    public DateTime IncidentRecoveryTime { get; set; }
    public int Status { get; set; }
    public string InfraId { get; set; }
    public string InfraComponentId { get; set; }
    public string InfraComponentType { get; set; }
    public string SourceId { get; set; }
    public string SourceTypId { get; set; }
    public int Flag1 { get; set; }
    public int Flag2 { get; set; }
    public int Flag3 { get; set; }
    public string IncidentCode { get; set; }
    public string IncidentComment { get; set; }
    public string AppProcess { get; set; }
    public string JobName { get; set; }
}