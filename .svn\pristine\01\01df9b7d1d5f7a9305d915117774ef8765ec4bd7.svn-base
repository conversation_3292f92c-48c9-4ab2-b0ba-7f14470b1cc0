using ContinuityPatrol.Application.Features.DriftJob.Commands.Create;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Update;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DriftJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DriftJobProfile : Profile
{
    public DriftJobProfile()
    {
        CreateMap<DriftJob, DriftJobListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DriftJob, DriftJobDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DriftJob, CreateDriftJobCommand>().ReverseMap();
        CreateMap<DriftJob, DriftJobViewModel>().ReverseMap();

        CreateMap<CreateDriftJobCommand, DriftJobViewModel>().ReverseMap();
        CreateMap<UpdateDriftJobCommand, DriftJobViewModel>().ReverseMap();


        CreateMap<CreateDriftJobCommand, DriftJobListVm>().ReverseMap();
        CreateMap<UpdateDriftJobCommand, DriftJobListVm>().ReverseMap();
        CreateMap<UpdateDriftJobCommand, DriftJob>().ForMember(x => x.Id, y => y.Ignore());


        CreateMap<PaginatedResult<DriftJob>, PaginatedResult<DriftJobListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}