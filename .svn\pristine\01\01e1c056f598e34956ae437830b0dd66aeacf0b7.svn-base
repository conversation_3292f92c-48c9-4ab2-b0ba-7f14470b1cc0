﻿namespace ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByBusinessServiceId;

public class
    GetDRReadyLogByBusinessServiceIdQueryHandler : IRequestHandler<GetDRReadyLogByBusinessServiceIdQuery,
        DRReadyLogByBusinessServiceIdVm>
{
    private readonly IDrReadyLogRepository _dRReadyLogRepository;
    private readonly IMapper _mapper;

    public GetDRReadyLogByBusinessServiceIdQueryHandler(IMapper mapper, IDrReadyLogRepository dRReadyLogRepository)
    {
        _mapper = mapper;
        _dRReadyLogRepository = dRReadyLogRepository;
    }

    public async Task<DRReadyLogByBusinessServiceIdVm> Handle(GetDRReadyLogByBusinessServiceIdQuery request,
        CancellationToken cancellationToken)
    {
        var dRReadyLog = await _dRReadyLogRepository.GetDrReadyLogByBusinessServiceId(request.BusinessServiceId);

        Guard.Against.NullOrDeactive(dRReadyLog, nameof(Domain.Entities.DRReadyLog),
            new NotFoundException(nameof(Domain.Entities.DRReadyLog), request.BusinessServiceId));

        var dRReadyLogDetailDto = _mapper.Map<DRReadyLogByBusinessServiceIdVm>(dRReadyLog);

        return dRReadyLogDetailDto ??
               throw new NotFoundException(nameof(Domain.Entities.DRReadyLog), request.BusinessServiceId);
    }
}