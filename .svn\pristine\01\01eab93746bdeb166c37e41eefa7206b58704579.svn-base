﻿using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.PostgresMonitorStatus.Queries
{
    public class GetPostgresMonitorStatusDetailQueryHandlerTests
    {
        private readonly Mock<IPostgresMonitorStatusRepository> _mockPostgresMonitorStatusRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetPostgresMonitorStatusDetailQueryHandler _handler;

        public GetPostgresMonitorStatusDetailQueryHandlerTests()
        {
            _mockPostgresMonitorStatusRepository = new Mock<IPostgresMonitorStatusRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetPostgresMonitorStatusDetailQueryHandler(_mockPostgresMonitorStatusRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnPostgresMonitorStatusDetailVm_WhenPostgresMonitorStatusExists()
        {
            var request = new GetPostgresMonitorStatusDetailQuery { Id = "valid-id" };

            var postgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = "valid-id",
                Type = "Type1",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfra",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };

            var expectedVm = new PostgresMonitorStatusDetailVm
            {
                Id = "valid-id",
                Type = "Type1",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfra",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };

            _mockPostgresMonitorStatusRepository.Setup(r => r.GetByReferenceIdAsync(request.Id)).ReturnsAsync(postgresMonitorStatus);
            _mockMapper.Setup(m => m.Map<PostgresMonitorStatusDetailVm>(postgresMonitorStatus)).Returns(expectedVm);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedVm.Id, result.Id);
            Assert.Equal(expectedVm.Type, result.Type);
            Assert.Equal(expectedVm.InfraObjectId, result.InfraObjectId);
            Assert.Equal(expectedVm.InfraObjectName, result.InfraObjectName);
            Assert.Equal(expectedVm.WorkflowId, result.WorkflowId);
            Assert.Equal(expectedVm.WorkflowName, result.WorkflowName);
            Assert.Equal(expectedVm.Properties, result.Properties);
            Assert.Equal(expectedVm.ConfiguredRPO, result.ConfiguredRPO);
            Assert.Equal(expectedVm.DataLagValue, result.DataLagValue);

            _mockPostgresMonitorStatusRepository.Verify(r => r.GetByReferenceIdAsync(request.Id), Times.Once);
            _mockMapper.Verify(m => m.Map<PostgresMonitorStatusDetailVm>(postgresMonitorStatus), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenPostgresMonitorStatusNotFound()
        {
            var request = new GetPostgresMonitorStatusDetailQuery { Id = "invalid-id" };

            _mockPostgresMonitorStatusRepository
                .Setup(r => r.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync((Domain.Entities.PostgresMonitorStatus)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(request, CancellationToken.None));

            Assert.Contains("PostgresMonitorStatus", exception.Message);
            Assert.Contains("invalid-id", exception.Message);
        }


        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenPostgresMonitorStatusIsInactive()
        {
            var request = new GetPostgresMonitorStatusDetailQuery { Id = "inactive-id" };

            var postgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = "inactive-id",
                Type = "Type1",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfra",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5",
                IsActive = false
            };

            _mockPostgresMonitorStatusRepository
                .Setup(r => r.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync(postgresMonitorStatus);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(request, CancellationToken.None));

            // ✅ Verifying exception content without needing extra properties
            Assert.Contains("PostgresMonitorStatus", exception.Message);
            Assert.Contains("inactive-id", exception.Message);
            Assert.Contains("not found", exception.Message);
        }


        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenMappingFails()
        {
            var request = new GetPostgresMonitorStatusDetailQuery { Id = "valid-id" };

            var postgresMonitorStatus = new Domain.Entities.PostgresMonitorStatus
            {
                ReferenceId = "valid-id",
                Type = "Type1",
                InfraObjectId = "infra123",
                InfraObjectName = "TestInfra",
                WorkflowId = "workflow123",
                WorkflowName = "TestWorkflow",
                Properties = "TestProperties",
                ConfiguredRPO = "10",
                DataLagValue = "5"
            };

            _mockPostgresMonitorStatusRepository
                .Setup(r => r.GetByReferenceIdAsync(request.Id))
                .ReturnsAsync(postgresMonitorStatus);

            _mockMapper
                .Setup(m => m.Map<PostgresMonitorStatusDetailVm>(postgresMonitorStatus))
                .Returns((PostgresMonitorStatusDetailVm)null); // Simulate mapping failure

            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(request, CancellationToken.None));

            // ✅ Validate message instead of Source
            Assert.Contains("PostgresMonitorStatus", exception.Message);
            Assert.Contains("valid-id", exception.Message);
            Assert.Contains("not found", exception.Message); // or "is not found" if that’s the wording
        }

    }
}
