﻿
  
    am4core.useTheme(am4themes_animated);
    // Themes end

    // Create chart instance
    var chart = am4core.create("BusinessFunctionsChart", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data
    chart.data = [{
        "country": "Exceeded",
        "litres": 201.1
    }, {
        "country": "Unconfigured",
        "litres": 165.8
    }, {
        "country": "Not Available",
        "litres": 60
    }, {
        "country": "Threshold",
        "litres": 50
    }];

    // Set inner radius
    chart.innerRadius = am4core.percent(65);

    // Add and configure Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.ticks.template.disabled = true;
    pieSeries.labels.template.disabled = true;
    pieSeries.slices.template.stroke = am4core.color("#fff");
    pieSeries.slices.template.strokeWidth = 5;
    pieSeries.slices.template.strokeOpacity = 5;
    pieSeries.slices.template.cornerRadius = 20;
    pieSeries.slices.template.innerCornerRadius = 20;
    // This creates initial animation
    pieSeries.hiddenState.properties.opacity = 1;
    pieSeries.hiddenState.properties.endAngle = -90;
    pieSeries.hiddenState.properties.startAngle = -90;

    pieSeries.colors.list = [
        am4core.color("#f0df47"),
        am4core.color("#3fa2ee"),
        am4core.color("#f32f41"),
        am4core.color("#d9d9d9")
    ];

    let label = pieSeries.createChild(am4core.Label);
    label.text = "Total 08";
    label.horizontalCenter = "middle";
    label.verticalCenter = "middle";
    label.fontSize = 14;

    // Change the padding values
    chart.padding(-5, -20, -15, -20)
chart.legend = new am4charts.Legend();
chart.legend.itemContainers.template.padding(8, 0, 0, 0);

    // Add a legend
    chart.legend.position = "left";
chart.legend.valueLabels.template.disabled = true;
chart.legend.itemContainers.template.clickable = false;
chart.legend.itemContainers.template.focusable = false;
    chart.legend.labels.template.text = "[font-size:10px ]{name}";
chart.legend.labels.template.fill = am4core.color("#6c757d");
    chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
    var markerTemplate = chart.legend.markers.template;
    markerTemplate.width = 10;
    markerTemplate.height = 10;

