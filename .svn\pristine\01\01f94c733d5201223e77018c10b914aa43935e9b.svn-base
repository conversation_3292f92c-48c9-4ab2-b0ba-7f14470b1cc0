using ContinuityPatrol.Application.Features.LoadBalancer.Events.Delete;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.LoadBalancer.Events;
public class LoadBalancerDeletedEventHandlerTests
{
    private readonly Mock<ILoggedInUserService> _mockUserService;
    private readonly Mock<ILogger<LoadBalancerDeletedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly LoadBalancerDeletedEventHandler _handler;

    public LoadBalancerDeletedEventHandlerTests()
    {
        _mockUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<LoadBalancerDeletedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();

        _mockUserService.SetupGet(x => x.UserId).Returns("test-user-id");
        _mockUserService.SetupGet(x => x.LoginName).Returns("test-login");
        _mockUserService.SetupGet(x => x.CompanyId).Returns("test-company-id");
        _mockUserService.SetupGet(x => x.RequestedUrl).Returns("/api/loadbalancer/delete");
        _mockUserService.SetupGet(x => x.IpAddress).Returns("***********");

        _handler = new LoadBalancerDeletedEventHandler(
            _mockUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldLogAndAddUserActivity_WhenEventIsHandled()
    {
        // Arrange
        var deletedEvent = new LoadBalancerDeletedEvent
        {
            Name = "LB-Test",
            TypeCategory = "Public"
        };

        // Act
        await _handler.Handle(deletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == "test-user-id" &&
            ua.LoginName == "test-login" &&
            ua.CompanyId == "test-company-id" &&
            ua.RequestUrl == "/api/loadbalancer/delete" &&
            ua.HostAddress == "***********" &&
            ua.Action == $"{ActivityType.Delete} {Modules.LoadBalancer}" &&
            ua.Entity == Modules.LoadBalancer.ToString() &&
            ua.ActivityType == ActivityType.Delete.ToString() &&
            ua.ActivityDetails == "Public ' LB-Test' deleted successfully."
        )), Times.Once);


    }
}