using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Drift;

public class DriftCategoryMasterService : IDriftCategoryMasterService
{
    private readonly IBaseClient _client;

    public DriftCategoryMasterService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<List<DriftCategoryMasterListVm>> GetDriftCategoryMasterList()
    {
        var request = new RestRequest("api/v6/driftcategorymasters");

        return await _client.GetFromCache<List<DriftCategoryMasterListVm>>(request, "GetDriftCategoryMasterList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDriftCategoryMasterCommand createDriftCategoryMasterCommand)
    {
        var request = new RestRequest("api/v6/driftcategorymasters", Method.Post);

        request.AddJsonBody(createDriftCategoryMasterCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftCategoryMasterCommand updateDriftCategoryMasterCommand)
    {
        var request = new RestRequest("api/v6/driftcategorymasters", Method.Put);

        request.AddJsonBody(updateDriftCategoryMasterCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/driftcategorymasters/{id}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<DriftCategoryMasterDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/driftcategorymasters/{id}");

        return await _client.Get<DriftCategoryMasterDetailVm>(request);
    }
    #region NameExist
    public async Task<bool> IsDriftCategoryMasterNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/driftcategorymasters/name-exist?driftcategorymasterName={name}&id={id}");

        return await _client.Get<bool>(request);
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftCategoryMasterListVm>> GetPaginatedDriftCategoryMasters(GetDriftCategoryMasterPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/driftcategorymasters/paginated-list");

        return await _client.Get<PaginatedResult<DriftCategoryMasterListVm>>(request);
    }
    #endregion
}
