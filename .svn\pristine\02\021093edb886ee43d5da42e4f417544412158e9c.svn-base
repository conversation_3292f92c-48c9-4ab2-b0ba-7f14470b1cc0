﻿using Microsoft.AspNetCore.Mvc.Rendering;
using System.DirectoryServices.AccountManagement;
using System.DirectoryServices.ActiveDirectory;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Shared.Services.Infrastructure;

public class DomainService : IDomainService
{
    [SupportedOSPlatform("windows")]
    public async Task<List<SelectListItem>> GetDomains()
    {
        var domains = (from System.DirectoryServices.ActiveDirectory.Domain d in Forest.GetCurrentForest().Domains select new SelectListItem(d.Name, d.Name)).ToList();

        return await Task.FromResult(domains);
    }

    [SupportedOSPlatform("windows")]
    public async Task<List<SelectListItem>> GetDomainUsers(string domainName)
    {
        var searcher = new UserPrincipal(new PrincipalContext(ContextType.Domain, domainName));
        var pS = new PrincipalSearcher(searcher);

        var users = pS.FindAll().Select(u => (UserPrincipal)u).ToList();

        var domainUserName = users.Select(u => new SelectListItem(u.SamAccountName, u.SamAccountName)).ToList();

        return await Task.FromResult(domainUserName);
    }
}