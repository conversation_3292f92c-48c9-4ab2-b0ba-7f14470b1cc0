using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class BackUpLogFixture
{
    public List<BackUpLogListVm> BackUpLogListVm { get; }
    public PaginatedResult<BackUpLogListVm> PaginatedBackUpLogListVm { get; }
    public BackUpLogDetailVm BackUpLogDetailVm { get; }
    public CreateBackUpLogCommand CreateBackUpLogCommand { get; }
    public UpdateBackUpLogCommand UpdateBackUpLogCommand { get; }
    public GetBackUpLogPaginatedListQuery GetBackUpLogPaginatedListQuery { get; }

    public BackUpLogFixture()
    {
        var fixture = new Fixture();

        BackUpLogListVm = fixture.Create<List<BackUpLogListVm>>();
        PaginatedBackUpLogListVm = fixture.Create<PaginatedResult<BackUpLogListVm>>();
        BackUpLogDetailVm = fixture.Create<BackUpLogDetailVm>();
        CreateBackUpLogCommand = fixture.Create<CreateBackUpLogCommand>();
        UpdateBackUpLogCommand = fixture.Create<UpdateBackUpLogCommand>();
        GetBackUpLogPaginatedListQuery = fixture.Create<GetBackUpLogPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
