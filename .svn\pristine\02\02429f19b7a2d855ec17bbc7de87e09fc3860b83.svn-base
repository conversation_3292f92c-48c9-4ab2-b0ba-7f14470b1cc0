﻿namespace ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Queries.GetByType;

public class GetSVCMssqlMonitorLogDetailByTypeQueryHandler : IRequestHandler<GetSVCMssqlMonitorLogDetailByTypeQuery,
    List<SVCMssqlMonitorLogDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly ISVCMssqlMonitorLogRepository _svcMssqlMonitorLogRepository;

    public GetSVCMssqlMonitorLogDetailByTypeQueryHandler(ISVCMssqlMonitorLogRepository svcMssqlMonitorLogRepository,
        IMapper mapper)
    {
        _svcMssqlMonitorLogRepository = svcMssqlMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<List<SVCMssqlMonitorLogDetailByTypeVm>> Handle(GetSVCMssqlMonitorLogDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var svcMssqlMonitorLogList = await _svcMssqlMonitorLogRepository.GetDetailByType(request.Type);

        return svcMssqlMonitorLogList.Count <= 0
            ? new List<SVCMssqlMonitorLogDetailByTypeVm>()
            : _mapper.Map<List<SVCMssqlMonitorLogDetailByTypeVm>>(svcMssqlMonitorLogList);
    }
}