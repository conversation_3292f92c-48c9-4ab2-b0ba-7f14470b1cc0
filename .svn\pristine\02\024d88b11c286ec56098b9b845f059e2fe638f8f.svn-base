﻿using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetByDynamicDashboardId;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicSubDashboard.Queries;

public class GetByDynamicDashBoardIdListQueryHandlerTests
{
    private readonly IMapper _mapper;
    private readonly Mock<IDynamicSubDashboardRepository> _repositoryMock;
    private readonly GetByDynamicDashBoardIdListQueryHandler _handler;

    public GetByDynamicDashBoardIdListQueryHandlerTests()
    {
        // AutoMapper configuration
        var config = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Domain.Entities.DynamicSubDashboard, DynamicSubDashboardListVm>();
        });
        _mapper = config.CreateMapper();

        // Mock data
        var dashboardId = "dashboard-001";
        var dashboards = new List<Domain.Entities.DynamicSubDashboard>
        {
            new Domain.Entities.DynamicSubDashboard { Name = "SubDash1", DynamicDashBoardId = dashboardId },
            new Domain.Entities.DynamicSubDashboard { Name = "SubDash2", DynamicDashBoardId = dashboardId },
            new Domain.Entities.DynamicSubDashboard { Name = "OtherDash", DynamicDashBoardId = "dashboard-002" }
        };

        _repositoryMock = DynamicSubDashboardRepositoryMocks.GetByDynamicDashBoardIdListRepository(dashboards);
        _handler = new GetByDynamicDashBoardIdListQueryHandler(_mapper, _repositoryMock.Object);
    }

    [Fact]
    public async Task Handle_Should_Return_FilteredMappedList_When_DashboardIdExists()
    {
        // Arrange
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "dashboard-001" };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, item => Assert.Equal("dashboard-001", item.DynamicDashBoardId));
    }

    [Fact]
    public async Task Handle_Should_Return_EmptyList_When_DashboardIdNotFound()
    {
        // Arrange
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "non-existing-id" };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Handle_Should_Return_Items_When_Id_Case_Is_Different()
    {
        // Arrange
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "DASHBOARD-001" };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count > 0);
    }

    [Fact]
    public async Task Handle_Should_ThrowException_When_RepositoryThrows()
    {
        // Arrange
        var failingRepo = new Mock<IDynamicSubDashboardRepository>();
        failingRepo.Setup(r => r.GetByDashboardIdAsync(It.IsAny<string>()))
                   .ThrowsAsync(new Exception("Database failure"));

        var handler = new GetByDynamicDashBoardIdListQueryHandler(_mapper, failingRepo.Object);
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "dashboard-001" };

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Should_Return_EmptyList_When_RepositoryReturnsNull()
    {
        // Arrange
        var nullRepo = new Mock<IDynamicSubDashboardRepository>();
        nullRepo.Setup(r => r.GetByDashboardIdAsync(It.IsAny<string>()))
                .ReturnsAsync((List<Domain.Entities.DynamicSubDashboard>)null!);

        var handler = new GetByDynamicDashBoardIdListQueryHandler(_mapper, nullRepo.Object);
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "dashboard-001" };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Handle_Should_MapPropertiesCorrectly()
    {
        // Arrange
        var query = new GetByDynamicDashBoardIdListQuery { DynamicDashboardId = "dashboard-001" };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.All(result, vm =>
        {
            Assert.False(string.IsNullOrWhiteSpace(vm.Name));
            Assert.Equal("dashboard-001", vm.DynamicDashBoardId);
        });
    }
}
