using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class DataSyncOptionsProfile : Profile
{
    public DataSyncOptionsProfile()
    {
        CreateMap<DataSyncOptions, DataSyncOptionsListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSyncOptions, DataSyncOptionsDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<DataSyncOptions, CreateDataSyncOptionsCommand>().ReverseMap();
        CreateMap<DataSyncOptions, DataSyncOptionsViewModel>().ReverseMap();

        CreateMap<CreateDataSyncOptionsCommand, DataSyncOptionsViewModel>().ReverseMap();
        CreateMap<UpdateDataSyncOptionsCommand, DataSyncOptionsViewModel>().ReverseMap();

        CreateMap<UpdateDataSyncOptionsCommand, DataSyncOptions>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<DataSyncOptions>, PaginatedResult<DataSyncOptionsListVm>>().ForMember(x => x.Data, y => y.MapFrom(src=>src.Data));
    }
}