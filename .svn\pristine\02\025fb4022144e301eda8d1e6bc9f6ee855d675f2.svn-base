﻿using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetCyberAlertCount;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberAlert.Queries;

public class GetCyberAlertCountTests:IClassFixture<CyberAlertFixture>
{
    private readonly CyberAlertFixture _cyberAlertFixture;
    private readonly Mock<ICyberAlertRepository> _mockCyberAlertRepository;
    private readonly GetCyberAlertCountQueryHandler _handler;

    public GetCyberAlertCountTests(CyberAlertFixture cyberAlertFixture)
    {
        _cyberAlertFixture = cyberAlertFixture;
        _mockCyberAlertRepository = CyberRepositoryMocks.CreateCyberAlertRepository(_cyberAlertFixture.CyberAlerts);

        _handler = new GetCyberAlertCountQueryHandler(_mockCyberAlertRepository.Object);
    }
    [Fact]
    public void Constructor_Should_InitializeHandler_When_ValidRepositoryProvided()
    {
        // Act & Assert
        _handler.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Should_ReturnCyberAlertCountVm_When_ValidQueryProvided()
    {
        // Arrange
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<CyberAlertCountVm>();
    }

    [Fact]
    public async Task Handle_Should_ReturnCorrectTotalCount_When_AlertsExist()
    {
        // Arrange
        var query = new GetCyberAlertCountQuery();
        var expectedTotalCount = _cyberAlertFixture.CyberAlerts.Count(x => x.IsActive);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(expectedTotalCount);
    }

    [Fact]
    public async Task Handle_Should_ReturnZeroTotalCount_When_NoActiveAlertsExist()
    {
        // Arrange
        var emptyAlertsList = new List<Domain.Entities.CyberAlert>();
        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(emptyAlertsList);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(0);
        result.AlertSeverityCount.ShouldBeEmpty();
    }
    

    [Fact]
    public async Task Handle_Should_CallRepositoryListAllAsync_When_QueryExecuted()
    {
        // Arrange
        var query = new GetCyberAlertCountQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockCyberAlertRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_ReturnDictionaryWithCorrectStructure_When_AlertsExist()
    {
        // Arrange
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.AlertSeverityCount.ShouldNotBeNull();
        result.AlertSeverityCount.ShouldBeOfType<Dictionary<string, int>>();

        // Verify all values are non-negative
        result.AlertSeverityCount.Values.ShouldAllBe(count => count >= 0);

        // Verify total count matches sum of severity counts
        var sumOfSeverityCounts = result.AlertSeverityCount.Values.Sum();
        result.TotalCount.ShouldBe(sumOfSeverityCounts);
    }


    [Fact]
    public async Task Handle_Should_PropagateRepositoryException_When_RepositoryThrows()
    {
        // Arrange
        var mockRepository = new Mock<ICyberAlertRepository>();
        mockRepository.Setup(x => x.ListAllAsync())
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldBe("Database connection failed");
    }

    [Fact]
    public async Task Handle_Should_ProcessConcurrentRequests_When_MultipleQueriesExecuted()
    {
        // Arrange
        var queries = Enumerable.Range(1, 5).Select(_ => new GetCyberAlertCountQuery()).ToList();

        // Act
        var tasks = queries.Select(query => _handler.Handle(query, CancellationToken.None));
        var results = await Task.WhenAll(tasks);

        // Assert
        results.Length.ShouldBe(5);
        results.ShouldAllBe(result => result != null);
        results.ShouldAllBe(result => result.TotalCount >= 0);
        results.ShouldAllBe(result => result.AlertSeverityCount != null);

        // Verify all results are consistent
        var firstResult = results[0];
        results.ShouldAllBe(result => result.TotalCount == firstResult.TotalCount);

        // Verify repository was called for each request
        _mockCyberAlertRepository.Verify(x => x.ListAllAsync(), Times.Exactly(5));
    }

    [Fact]
    public async Task Handle_Should_GroupAlertsBySeverity_When_AlertsWithDifferentSeveritiesExist()
    {
        // Arrange
        var alertsWithSeverities = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "High", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Medium", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "High", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Low", IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithSeverities);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.AlertSeverityCount.ShouldNotBeNull();
        result.AlertSeverityCount.ShouldNotBeEmpty();

        // Use ContainsKey instead of ShouldContainKey
        result.AlertSeverityCount.ContainsKey("critical").ShouldBeTrue();
        result.AlertSeverityCount["critical"].ShouldBe(2);

        result.AlertSeverityCount.ContainsKey("high").ShouldBeTrue();
        result.AlertSeverityCount["high"].ShouldBe(2);

        result.AlertSeverityCount.ContainsKey("medium").ShouldBeTrue();
        result.AlertSeverityCount["medium"].ShouldBe(1);

        result.AlertSeverityCount.ContainsKey("low").ShouldBeTrue();
        result.AlertSeverityCount["low"].ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Should_ConvertSeverityToLowerCase_When_ProcessingAlerts()
    {
        // Arrange
        var alertsWithMixedCaseSeverities = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "CRITICAL", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "HIGH", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "High", IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithMixedCaseSeverities);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.AlertSeverityCount.ContainsKey("critical").ShouldBeTrue();
        result.AlertSeverityCount["critical"].ShouldBe(3);

        result.AlertSeverityCount.ContainsKey("high").ShouldBeTrue();
        result.AlertSeverityCount["high"].ShouldBe(2);

        // Verify no uppercase keys exist
        result.AlertSeverityCount.Keys.ShouldAllBe(key => key == key.ToLower());
    }

    [Fact]
    public async Task Handle_Should_HandleSingleSeverityType_When_AllAlertsHaveSameSeverity()
    {
        // Arrange
        var alertsWithSameSeverity = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithSameSeverity);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(3);
        result.AlertSeverityCount.Count.ShouldBe(1);
        result.AlertSeverityCount.ContainsKey("critical").ShouldBeTrue();
        result.AlertSeverityCount["critical"].ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Should_IgnoreInactiveAlerts_When_ProcessingCounts()
    {
        // Arrange
        var alertsWithInactive = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = false }, // Inactive
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "High", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "High", IsActive = false } // Inactive
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithInactive);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(2); // Only active alerts
        result.AlertSeverityCount.ContainsKey("critical").ShouldBeTrue();
        result.AlertSeverityCount["critical"].ShouldBe(1);
        result.AlertSeverityCount.ContainsKey("high").ShouldBeTrue();
        result.AlertSeverityCount["high"].ShouldBe(1);
    }


    [Fact]
    public async Task Handle_Should_HandleLargeSeverityVariety_When_ManyDifferentSeveritiesExist()
    {
        // Arrange
        var severityTypes = new[] { "Critical", "High", "Medium", "Low", "Info", "Warning", "Error", "Debug" };
        var alertsWithVariedSeverities = severityTypes.SelectMany(severity =>
            Enumerable.Range(1, 3).Select(i => new Domain.Entities.CyberAlert
            {
                ReferenceId = Guid.NewGuid().ToString(),
                Severity = severity,
                IsActive = true
            })
        ).ToList();

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithVariedSeverities);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(24); // 8 severities × 3 alerts each
        result.AlertSeverityCount.Count.ShouldBe(8);

        foreach (var severity in severityTypes)
        {
            var lowerSeverity = severity.ToLower();
            result.AlertSeverityCount.ContainsKey(lowerSeverity).ShouldBeTrue();
            result.AlertSeverityCount[lowerSeverity].ShouldBe(3);
        }
    }

    [Fact]
    public async Task Handle_Should_HandleSpecialCharactersInSeverity_When_SeverityContainsSpecialChars()
    {
        // Arrange
        var alertsWithSpecialSeverities = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical-High", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Level_1", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Alert@Priority", IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithSpecialSeverities);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(3);
        result.AlertSeverityCount.ContainsKey("critical-high").ShouldBeTrue();
        result.AlertSeverityCount.ContainsKey("level_1").ShouldBeTrue();
        result.AlertSeverityCount.ContainsKey("alert@priority").ShouldBeTrue();
    }
    [Fact]
    public async Task Handle_Should_HandleEmptyAndWhitespaceSeverity_When_AlertsHaveInvalidSeverity()
    {
        // Arrange - Only test empty and whitespace, not null
        var alertsWithInvalidSeverity = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "Critical", IsActive = true },
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = "", IsActive = true },
        new() { ReferenceId = Guid.NewGuid().ToString(), Severity = "   ", IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithInvalidSeverity);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.TotalCount.ShouldBe(3);
        result.AlertSeverityCount.ContainsKey("critical").ShouldBeTrue();
        result.AlertSeverityCount["critical"].ShouldBe(1);

        // The handler should group empty and whitespace severities
        result.AlertSeverityCount.ShouldNotBeEmpty();
        result.AlertSeverityCount.Values.Sum().ShouldBe(result.TotalCount);
    }

    [Fact]
    public async Task Handle_Should_ThrowNullReferenceException_When_SeverityIsNull()
    {
        // Arrange
        var alertsWithNullSeverity = new List<Domain.Entities.CyberAlert>
    {
        new () { ReferenceId = Guid.NewGuid().ToString(), Severity = null, IsActive = true }
    };

        var mockRepository = CyberRepositoryMocks.CreateCyberAlertRepository(alertsWithNullSeverity);
        var handler = new GetCyberAlertCountQueryHandler(mockRepository.Object);
        var query = new GetCyberAlertCountQuery();

        // Act & Assert
        await Should.ThrowAsync<NullReferenceException>(
            () => handler.Handle(query, CancellationToken.None));
    }

    
}