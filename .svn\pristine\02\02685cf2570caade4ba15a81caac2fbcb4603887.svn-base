using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Job.Commands.Create;
using ContinuityPatrol.Application.Features.Job.Commands.Delete;
using ContinuityPatrol.Application.Features.Job.Commands.RescheduleJob;
using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobStatus;
using ContinuityPatrol.Application.Features.Job.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Job.Queries.GetJobsByInfraObjectId;
using ContinuityPatrol.Application.Features.Job.Queries.GetList;
using ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class JobsControllerTests : IClassFixture<JobFixture>
{
    private readonly JobFixture _fixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly JobsController _controller;

    public JobsControllerTests(JobFixture fixture)
    {
        _fixture = fixture;
        
        var testBuilder = new ControllerTestBuilder<JobsController>();
        _controller = testBuilder.CreateController(
            _ => new JobsController(),
            out _mediatorMock);
    }

    #region CreateJob Tests

    [Fact]
    public async Task CreateJob_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _fixture.CreateJobCommand;
        var expectedResponse = _fixture.CreateJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateJobResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains("created successfully", response.Message);
    }

    [Fact]
    public async Task CreateJob_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        CreateJobCommand nullCommand = null!;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.CreateJob(nullCommand));
    }

    [Fact]
    public async Task CreateJob_WithException_ThrowsException()
    {
        // Arrange
        var command = _fixture.CreateJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new Exception("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _controller.CreateJob(command));
    }

    #endregion

    #region UpdateJob Tests

    [Fact]
    public async Task UpdateJob_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _fixture.UpdateJobCommand;
        var expectedResponse = _fixture.UpdateJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateJobResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("updated successfully", response.Message);
    }

    [Fact]
    public async Task UpdateJob_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        UpdateJobCommand nullCommand = null!;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.UpdateJob(nullCommand));
    }

    [Fact]
    public async Task UpdateJob_WithNotFoundException_ThrowsNotFoundException()
    {
        // Arrange
        var command = _fixture.UpdateJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("Job not found for",command.Type));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateJob(command));
    }

    #endregion

    #region DeleteJob Tests

    [Fact]
    public async Task DeleteJob_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();
        var expectedResponse = _fixture.DeleteJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteJobCommand>(c => c.Id == jobId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteJob(jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteJobResponse>(okResult.Value);
        Assert.True(response.Success);
        Assert.Contains("deleted successfully", response.Message);
    }

    [Fact]
    public async Task DeleteJob_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteJob("invalid-guid"));
    }

    [Fact]
    public async Task DeleteJob_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteJob(string.Empty));
    }

    [Fact]
    public async Task DeleteJob_WithNotFoundException_ThrowsNotFoundException()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteJobCommand>(c => c.Id == jobId), default))
            .ThrowsAsync(new NotFoundException("Job not found for",jobId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.DeleteJob(jobId));
    }

    #endregion

    #region RescheduleJob Tests

    [Fact]
    public async Task RescheduleJob_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _fixture.RescheduleJobCommand;
        var expectedResponse = _fixture.RescheduleJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.RescheduleJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<RescheduleJobResponse>(createdResult.Value);
        Assert.True(response.Success);
        Assert.Contains("rescheduled successfully", response.Message);
    }

    [Fact]
    public async Task RescheduleJob_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        RescheduleJobCommand nullCommand = null!;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.RescheduleJob(nullCommand));
    }

    #endregion

    #region GetJobs Tests

    [Fact]
    public async Task GetJobs_ReturnsOkResult()
    {
        // Arrange
        var expectedList = _fixture.JobListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetJobListQuery>(), default))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var jobs = Assert.IsType<List<JobListVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, jobs.Count);
    }

    [Fact]
    public async Task GetJobs_WithEmptyResult_ReturnsEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetJobListQuery>(), default))
            .ReturnsAsync(new List<JobListVm>());

        // Act
        var result = await _controller.GetJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var jobs = Assert.IsType<List<JobListVm>>(okResult.Value);
        Assert.Empty(jobs);
    }

    #endregion

    #region GetJobById Tests

    [Fact]
    public async Task GetJobById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();
        var expectedDetail = _fixture.JobDetailVm;
        expectedDetail.Id = jobId;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetJobDetailQuery>(q => q.Id == jobId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetJobById(jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var jobDetail = Assert.IsType<JobDetailVm>(okResult.Value);
        Assert.Equal(jobId, jobDetail.Id);
    }

    [Fact]
    public async Task GetJobById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetJobById("invalid-guid"));
    }

    [Fact]
    public async Task GetJobById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetJobById(string.Empty));
    }

    [Fact]
    public async Task GetJobById_WithNotFoundException_ThrowsNotFoundException()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetJobDetailQuery>(q => q.Id == jobId), default))
            .ThrowsAsync(new NotFoundException("Job not found for",jobId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetJobById(jobId));
    }

    #endregion

    #region GetJobsByInfraObjectId Tests

    [Fact]
    public async Task GetJobsByInfraObjectId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var expectedJobs = _fixture.JobsByInfraObjectIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetJobsByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(expectedJobs);

        // Act
        var result = await _controller.GetJobsByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var jobs = Assert.IsType<List<JobsByInfraObjectIdVm>>(okResult.Value);
        Assert.Equal(expectedJobs.Count, jobs.Count);
    }

    [Fact]
    public async Task GetJobsByInfraObjectId_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetJobsByInfraObjectId("invalid-guid"));
    }

    [Fact]
    public async Task GetJobsByInfraObjectId_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetJobsByInfraObjectId(string.Empty));
    }

    #endregion

    #region GetPaginatedJobs Tests

    [Fact]
    public async Task GetPaginatedJobs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = new GetJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10
        };

        var expectedData = _fixture.JobListVm;
        var paginatedResult = PaginatedResult<JobListVm>.Success(
            data: expectedData,
            count: expectedData.Count,
            page: query.PageNumber,
            pageSize: query.PageSize
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetJobPaginatedListQuery>(), default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedJobs = Assert.IsType<PaginatedResult<JobListVm>>(okResult.Value);
        Assert.Equal(expectedData.Count, paginatedJobs.Data.Count);
        Assert.Equal(1, paginatedJobs.CurrentPage);
        Assert.Equal(10, paginatedJobs.PageSize);
    }

    [Fact]
    public async Task GetPaginatedJobs_WithEmptyResult_ReturnsEmptyPaginatedResult()
    {
        // Arrange
        var query = new GetJobPaginatedListQuery { PageNumber = 1, PageSize = 10 };
        var emptyResult = PaginatedResult<JobListVm>.Success(
            data: new List<JobListVm>(),
            count: 0,
            page: 1,
            pageSize: 10
        );

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetJobPaginatedListQuery>(), default))
            .ReturnsAsync(emptyResult);

        // Act
        var result = await _controller.GetPaginatedJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedJobs = Assert.IsType<PaginatedResult<JobListVm>>(okResult.Value);
        Assert.Empty(paginatedJobs.Data);
        Assert.Equal(0, paginatedJobs.TotalCount);
    }

    #endregion

    #region UpdateJobState Tests

    [Fact]
    public async Task UpdateJobState_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _fixture.UpdateJobStateCommand;
        var expectedResponse = _fixture.UpdateJobStateResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateJobStateResponse>(okResult.Value);
        Assert.True(response.Success);
    }

    [Fact]
    public async Task UpdateJobState_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        UpdateJobStateCommand nullCommand = null!;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.UpdateJobState(nullCommand));
    }

    #endregion

    #region UpdateJobStatus Tests

    [Fact]
    public async Task UpdateJobStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _fixture.UpdateJobStatusCommand;
        var expectedResponse = _fixture.UpdateJobStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateJobStatusResponse>(okResult.Value);
        Assert.True(response.Success);
    }

    [Fact]
    public async Task UpdateJobStatus_WithNullCommand_ThrowsArgumentNullException()
    {
        // Arrange
        UpdateJobStatusCommand nullCommand = null!;

        // Act & Assert
        await Assert.ThrowsAsync<NullReferenceException>(() => _controller.UpdateJobStatus(nullCommand));
    }

    #endregion
}