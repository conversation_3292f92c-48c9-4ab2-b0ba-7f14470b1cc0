﻿using ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicDashboard.Queries.GetNameUnique;

public class GetDynamicDashboardNameUniqueQueryHandlerTests
{
    [Fact]
    public async Task Handle_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var expectedResult = true;
        var mockRepo = DynamicDashboardRepositoryMocks.GetDynamicDashboardNameUniqueRepository(expectedResult);
        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = "DashboardX",
            Id = "321"
        };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        mockRepo.Verify(repo => repo.IsNameExist("DashboardX", "321"), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var expectedResult = false;
        var mockRepo = DynamicDashboardRepositoryMocks.GetDynamicDashboardNameUniqueRepository(expectedResult);
        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = "NonExistingDashboard",
            Id = "654"
        };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        mockRepo.Verify(repo => repo.IsNameExist("NonExistingDashboard", "654"), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnFalse_WhenNameIsEmpty()
    {
        // Arrange
        var expectedResult = false;
        var mockRepo = DynamicDashboardRepositoryMocks.GetDynamicDashboardNameUniqueRepository(expectedResult);
        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = string.Empty,
            Id = "dashboard-id"
        };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        mockRepo.Verify(repo => repo.IsNameExist(string.Empty, "dashboard-id"), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnFalse_WhenIdIsNull()
    {
        // Arrange
        var expectedResult = false;
        var mockRepo = DynamicDashboardRepositoryMocks.GetDynamicDashboardNameUniqueRepository(expectedResult);
        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = "DashboardWithNullId",
            Id = null!
        };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        mockRepo.Verify(repo => repo.IsNameExist("DashboardWithNullId", null), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldPassCorrectParameters_ToRepository()
    {
        // Arrange
        var mockRepo = new Mock<IDynamicDashboardRepository>();
        mockRepo.Setup(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(false);
        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);

        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = "DashboardZ",
            Id = "abc-321"
        };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        mockRepo.Verify(x => x.IsNameExist("DashboardZ", "abc-321"), Times.Once);
        result.Should().BeFalse();
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
    {
        // Arrange
        var mockRepo = new Mock<IDynamicDashboardRepository>();
        mockRepo.Setup(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new InvalidOperationException("DB error"));

        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery
        {
            Name = "CrashTest",
            Id = "id-crash"
        };

        // Act
        Func<Task> act = async () => await handler.Handle(query, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>()
                 .WithMessage("DB error");
    }

    [Fact]
    public async Task Handle_ShouldCallRepositoryOnce_WhenValidInput()
    {
        // Arrange
        var mockRepo = new Mock<IDynamicDashboardRepository>();
        mockRepo.Setup(r => r.IsNameExist("ValidDashboard", "id-001")).ReturnsAsync(true);

        var handler = new GetDynamicDashboardNameUniqueQueryHandler(mockRepo.Object);
        var query = new GetDynamicDashboardNameUniqueQuery { Name = "ValidDashboard", Id = "id-001" };

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        mockRepo.Verify(r => r.IsNameExist("ValidDashboard", "id-001"), Times.Once);
    }
}
