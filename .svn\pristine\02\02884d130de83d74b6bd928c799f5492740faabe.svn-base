﻿using ContinuityPatrol.Application.Features.UserGroup.Events.Delete;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.UserGroup.Events
{
    public class DeleteUserGroupEventTests
    {
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly Mock<ILogger<UserGroupDeleteEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly UserGroupDeleteEventHandler _handler;

        public DeleteUserGroupEventTests()
        {
            _mockUserService = new Mock<ILoggedInUserService>();
            _mockLogger = new Mock<ILogger<UserGroupDeleteEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();

            _handler = new UserGroupDeleteEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_AddsUserActivity_AndLogsInformation_WhenEventIsHandled()
        {
            var notification = new UserGroupDeleteEvent { GroupName = "Test Group" };

            _mockUserService.Setup(service => service.UserId).Returns("User123");
            _mockUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockUserService.Setup(service => service.CompanyId).Returns("Company123");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(notification, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "User123" &&
                activity.LoginName == "TestUser" &&
                activity.CompanyId == "Company123" &&
                activity.RequestUrl == "http://example.com" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Action.Contains("Delete") &&
                activity.Entity == Modules.Company.ToString() &&
                activity.ActivityType == ActivityType.Delete.ToString() &&
                activity.ActivityDetails.Contains(notification.GroupName)
            )), Times.Once);

            _mockLogger.Verify(logger =>
                logger.LogInformation(It.Is<string>(msg => msg.Contains(notification.GroupName))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNoException_WhenAllDependenciesWorkAsExpected()
        {
            var notification = new UserGroupDeleteEvent { GroupName = "Sample Group" };
            _mockUserService.Setup(service => service.UserId).Returns("User123");

            var cancellationToken = CancellationToken.None;

            var exception = await Record.ExceptionAsync(() =>
                _handler.Handle(notification, cancellationToken));

            Assert.Null(exception);
        }
    }
}
