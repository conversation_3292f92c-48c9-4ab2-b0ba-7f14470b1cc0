﻿using ContinuityPatrol.Application.Features.ServerType.Commands.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Validators;

public class UpdateServerTypeCommandValidatorTests
{
    private readonly Mock<IServerTypeRepository> _mockRepo;
    private readonly UpdateServerTypeCommandValidator _validator;

    public UpdateServerTypeCommandValidatorTests()
    {
        _mockRepo = new Mock<IServerTypeRepository>();
        _validator = new UpdateServerTypeCommandValidator(_mockRepo.Object);
    }

    [Fact]
    public async Task Should_Fail_When_Name_Is_Empty()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = ""
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Name is required");
    }

    [Fact]
    public async Task Should_Fail_When_Name_Is_Invalid()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "!nv@lid"
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Please Enter Valid Name");
    }

    [Fact]
    public async Task Should_Fail_When_Name_Too_Short()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "AB"
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Name should contain between 3 to 30 characters.");
    }

    [Fact]
    public async Task Should_Fail_When_Name_Too_Long()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = new string('A', 31)
        };

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Name should contain between 3 to 30 characters.");
    }

    [Fact]
    public async Task Should_Fail_When_Name_Already_Exists()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "ExistingName"
        };

        _mockRepo.Setup(r => r.IsServerTypeNameExist(command.Name, command.Id)).ReturnsAsync(true);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldHaveValidationErrorFor(c => c)
              .WithErrorMessage("A same name already exists");
    }

    //[Fact]
    //public async Task Should_Fail_When_Id_Is_Invalid_Guid()
    //{
    //    var command = new UpdateServerTypeCommand
    //    {
    //        Id = "",  // Invalid Guid
    //        Name = "ValidName"
    //    };

    //    var result = await Assert.ThrowsAsync<ArgumentException>(() => _validator.TestValidateAsync(command));
    //    Assert.Contains("ServerType Id", result.Message);
    //}

    [Fact]
    public async Task Should_Pass_When_All_Fields_Are_Valid()
    {
        var command = new UpdateServerTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Valid_Name"
        };

        _mockRepo.Setup(r => r.IsServerTypeNameExist(command.Name, command.Id)).ReturnsAsync(false);

        var result = await _validator.TestValidateAsync(command);

        result.ShouldNotHaveAnyValidationErrors();
    }
}