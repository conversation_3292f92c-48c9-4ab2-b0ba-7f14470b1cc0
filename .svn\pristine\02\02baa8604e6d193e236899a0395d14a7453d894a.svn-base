﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;


namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowOperationRepository : BaseRepository<WorkflowOperation>, IWorkflowOperationRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowOperationRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<WorkflowOperation>> ListAllAsync()
    {
        return _loggedInUserService.IsParent
            ? await base.ListAllAsync()
            : await FindByFilterAsync(workflowOperation => workflowOperation.CompanyId.Equals(_loggedInUserService.CompanyId));
    }

    public async Task<IReadOnlyList<WorkflowOperation>> GetFilterListAsync()
    {
        var operation = await (_loggedInUserService.IsParent
            ? Entities.AsNoTracking()
            : Entities.AsNoTracking().Where(workflowOperation => workflowOperation.CompanyId.Equals(_loggedInUserService.CompanyId)))
            .DescOrderById().Select(x => new WorkflowOperation
            {
                Id = x.Id,
                ReferenceId = x.ReferenceId,
                ProfileId = x.ProfileId,
                ProfileName = x.ProfileName,
                Status = x.Status,
                StartTime = x.StartTime
            }).ToListAsync();
        return operation;
    }
    public async Task<WorkflowOperation> GetLastWorkflowOperation()
    {
        var workflowOperation = await (_loggedInUserService.IsParent
            ? _dbContext.WorkflowOperations
                .Where(x => !x.Status.Equals("Running") && !x.Status.Equals("Pending"))
            : _dbContext.WorkflowOperations
                .Where(x => !x.Status.Equals("Running") && !x.Status.Equals("Pending")
                    && x.CompanyId.Equals(_loggedInUserService.CompanyId)))
                .AsNoTracking().Active()
                .OrderByDescending(x => x.LastModifiedDate).FirstOrDefaultAsync();

        return workflowOperation;

    }
    public async Task<(List<WorkflowOperationDrDrillVm> WorkflowOperationDrDrillVm, List<string> InfraIds)> GetDrillDetailsByBusinessServiceId(string businessServiceId)
    {
        var status = new[] { "Aborted", "Completed" };

        var workflowOperationGroups =
            await _dbContext.WorkflowOperationGroups
            .AsNoTracking()
            .Active()
            .Where(x => status.Contains(x.Status) && x.BusinessServiceId.Equals(businessServiceId))
            .GroupBy(x => x.WorkflowOperationId)
            .Select(g => new
            {
                WorkflowOperationId = g.Key,
                WorkflowOperationGroupItems = g.Select(x => new WorkflowOperationGroup
                {
                    WorkflowId = x.WorkflowId,
                    WorkflowName = x.WorkflowName,
                    InfraObjectId = x.InfraObjectId,
                    InfraObjectName = x.InfraObjectName,
                    Status = x.Status,
                    CreatedDate = x.CreatedDate,
                    LastModifiedDate = x.LastModifiedDate
                }).ToList()
            })
            .ToListAsync();

        if (workflowOperationGroups.Count == 0)
        {
            return (new List<WorkflowOperationDrDrillVm>(), new List<string>());
        }

        var infraIds = workflowOperationGroups.SelectMany(g => g.WorkflowOperationGroupItems.Select(x => x.InfraObjectId)).Distinct().ToList();

        var workflowOperationIds = workflowOperationGroups.Select(g => g.WorkflowOperationId).Distinct();

        var workflowOperations = await _dbContext.WorkflowOperations
            .AsNoTracking()
            .Active()
            .Where(x => workflowOperationIds.Contains(x.ReferenceId))
            .ToDictionaryAsync(x => x.ReferenceId);

        var operation = workflowOperationGroups
            .Select(g =>
            {
                if (!workflowOperations.TryGetValue(g.WorkflowOperationId, out var workflowOperation))
                {
                    return null;
                }

                return new WorkflowOperationDrDrillVm
                {
                    ProfileId = workflowOperation.ProfileId,
                    ProfileName = workflowOperation.ProfileName,
                    StartTime = workflowOperation.StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndTime = workflowOperation.EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    TotalTime = workflowOperation.EndTime - workflowOperation.StartTime,
                    WorkflowOperationId = workflowOperation.ReferenceId,
                    Status = workflowOperation.Status,
                    WorkflowOperationGroupDrDrillVms = g.WorkflowOperationGroupItems.Select(x => new WorkflowOperationGroupDrDrillVm
                    {
                        WorkflowId = x.WorkflowId,
                        WorkflowName = x.WorkflowName,
                        InfraObjectId = x.InfraObjectId,
                        InfraObjectName = x.InfraObjectName,
                        Status = x.Status,
                        StartTime = x.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        EndTime = x.LastModifiedDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        TotalTime = x.LastModifiedDate - x.CreatedDate

                    }).ToList()
                };
            })
            .Where(x => x != null)
            .ToList();

        return (operation, infraIds);
    }


    public override Task<WorkflowOperation> GetByReferenceIdAsync(string id)
    {
        return _loggedInUserService.IsParent
            ? base.GetByReferenceIdAsync(id)
            : Task.FromResult(FindByFilterAsync(workflowOperation =>
                workflowOperation.ReferenceId.Equals(id) &&
                workflowOperation.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }

    public Task<WorkflowOperation> GetByReferenceIdAndRunMode(string id, string runMode)
    {
        return _loggedInUserService.IsParent
            ? Task.FromResult(FindByFilterAsync(workflowOperation => workflowOperation.ReferenceId.Equals(id) && workflowOperation.RunMode.Equals(runMode)).Result.SingleOrDefault())
            : Task.FromResult(FindByFilterAsync(workflowOperation =>
                workflowOperation.ReferenceId.Equals(id) && workflowOperation.RunMode.Equals(runMode) &&
                workflowOperation.CompanyId.Equals(_loggedInUserService.CompanyId)).Result.SingleOrDefault());
    }


    public async Task<List<WorkflowOperation>> GetWorkflowOperationNames()
    {
        return !_loggedInUserService.IsParent
            ? await _dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowOperation
                { ReferenceId = x.ReferenceId, ProfileId = x.ProfileId, ProfileName = x.ProfileName })
                .OrderBy(x => x.ProfileName)
                .ToListAsync()
            : await _dbContext.WorkflowOperations.Active()
                .Select(x => new WorkflowOperation
                { ReferenceId = x.ReferenceId, ProfileId = x.ProfileId, ProfileName = x.ProfileName })
                .OrderBy(x => x.ProfileName)
                .ToListAsync();
    }

    public override IQueryable<WorkflowOperation> GetPaginatedQuery()
    {
        return _loggedInUserService.IsParent
            ? SelectToWorkflowOperation(Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id))
            : SelectToWorkflowOperation(Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .AsNoTracking()
                .OrderByDescending(x => x.Id));
    }

    public async Task<List<WorkflowOperation>> GetWorkflowOperationByRunningStatus()
    {
        var status = new[] { "Pending", "Running" };

        return !_loggedInUserService.IsParent
            ? await SelectToWorkflowOperation(_dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => status.Contains(x.Status) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId))
                ).ToListAsync()
            : await SelectToWorkflowOperation(_dbContext.WorkflowOperations.Active()
                .Where(x => status.Contains(x.Status))).ToListAsync();
    }

    public async Task<List<WorkflowOperation>> GetWorkflowOperationByOperationId(string operationId)
    {
        return await base.FilterBy(x => x.ReferenceId.Equals(operationId)).ToListAsync();
    }

    public async Task<List<WorkflowOperation>> GetWorkflowOperationByRunningUserId(List<string> userId)
    {
        var status = new[] { "Pending", "Running" };

        return _loggedInUserService.IsParent
            ? await SelectToWorkflowOperation(_dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => status.Contains(x.Status) && userId.Contains(x.CreatedBy))).ToListAsync()
            : await SelectToWorkflowOperation(_dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => status.Contains(x.Status) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId) && userId.Contains(x.CreatedBy)))
                .ToListAsync();
    }

    public async Task<List<WorkflowOperation>> GetWorkflowOperationByRunningUsers()
    {
        var status = new[] { "Pending", "Running" };

        return _loggedInUserService.IsParent
            ? await _dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => status.Contains(x.Status))
                .Select(x => new WorkflowOperation { UserName = x.UserName, CreatedBy = x.CreatedBy })
                .ToListAsync()
            : await _dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => status.Contains(x.Status) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new WorkflowOperation { UserName = x.UserName, CreatedBy = x.CreatedBy })
                .ToListAsync();
    }


    public async Task<List<WorkflowOperation>> GetDescriptionByStartTimeAndEndTime(string startDate, string endDate)
    {
        return _loggedInUserService.IsParent
            ? await _dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => x.Status.Trim().ToLower().Contains("success") &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.CreatedDate.Date <= endDate.ToDateTime())
                .Select(x => new WorkflowOperation
                { ReferenceId = x.ReferenceId, Description = x.Description, StartTime = x.StartTime, RunMode = x.RunMode })
                .ToListAsync()
            : await _dbContext.WorkflowOperations.AsNoTracking().Active()
                .Where(x => x.Status.Trim().ToLower().Contains("success") &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.CreatedDate.Date <= endDate.ToDateTime())
                .Select(x => new WorkflowOperation
                { ReferenceId = x.ReferenceId, Description = x.Description, StartTime = x.StartTime, RunMode = x.RunMode })
                .ToListAsync();
    }

    public async Task<List<WorkflowOperation>> GetWorkflowOperationByProfileId(string profileId)
    {
        var status = new[] { "Pending", "Running" };

        return await SelectToWorkflowOperation(_dbContext.WorkflowOperations.AsNoTracking().Where(x =>
            x.ProfileId.Equals(profileId) && status.Contains(x.Status))).ToListAsync();
    }


    public async Task<List<WorkflowOperation>> GetWorkflowOperationByProfileIdAndStatus(string profileId, string status)
    {
        return await SelectToWorkflowOperation(_dbContext.WorkflowOperations.AsNoTracking().Where(x =>
            x.ProfileId.Equals(profileId) && x.Status.Trim().ToLower().Equals(status))).ToListAsync();
    }

    private IQueryable<WorkflowOperation> SelectToWorkflowOperation(IQueryable<WorkflowOperation> query)
    {
        return query.Select(x => new WorkflowOperation
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            ProfileId = x.ProfileId,
            ProfileName = x.ProfileName,
            Status = x.Status,
            Description = x.Description,
            StartTime = x.StartTime,
            EndTime = x.EndTime,
            UserName = x.UserName,
            RunMode = x.RunMode,
            IsDrCalendar = x.IsDrCalendar
        });
    }
}