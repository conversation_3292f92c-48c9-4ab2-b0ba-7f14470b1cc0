﻿using ContinuityPatrol.Infrastructure.Contract;

namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.SendEmail;

public class SendEmailCommandHandler : IRequestHandler<SendEmailCommand, SendEmailResponse>
{
    private readonly IConfiguration _config;
    private readonly IEmailService _emailService;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;

    public SendEmailCommandHandler(IEmailService emailService, ISmtpConfigurationRepository smtpConfigurationRepository,
        IConfiguration config)
    {
        _emailService = emailService;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _config = config;
    }

    public async Task<SendEmailResponse> Handle(SendEmailCommand request, CancellationToken cancellationToken)
    {
        var emailSettings = (await _smtpConfigurationRepository.ListAllAsync()).LastOrDefault();

        if (emailSettings is null) throw new InvalidException("please configure smtp.");

        var version = _config.GetValue<string>("CP:Version");

        // string body;


        //    if (request.Type.ToLower().Trim().Equals("serverdown"))
        //        request.Emails.ForEach(email =>
        //        {
        //            var userName = Regex.Split(email.ToMail, @"@");

        //            body =
        //                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <link rel=\"preconnect\" href=\"\" />\r\n    <link rel=\"preconnect\" href=\"\" crossorigin />\r\n    <link href=\"\" rel=\"stylesheet\" />\r\n    <title>Server down!</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 14px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <!--  -->\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <h2 style=\"color: red;text-align: center;font-size:2rem;\">Server down!</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:Server_Down\r\n                        alt=\"Isometric_Orange_Warning\" style=\"height: 210px;\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p style=\"text-align: center;line-height: 0px;\">Dear<b style=\" text-transform: uppercase;\"> {userName[0]},</b></p>\r\n                    <p style=\"text-align: center;\">{request.ErrorMessage}\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <table style=\"margin: auto;\r\n                  \r\n                    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px;\r\n                    background-color: #007BFF; padding: 0.375rem 0.75rem;border-radius: 0.25rem;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td ><a href=\"\" style=\"text-decoration: none;color:#fff;\">Contact Us</a></td>\r\n                              \r\n                            </tr>\r\n                           \r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {DateTime.Now.Year}-{DateTime.Now.Year + 1}\r\n                        <a href=\"\" style=\"color: gray;\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n<!--  -->\r\n\r\n\r\n\r\n\r\n\r\n</body>\r\n\r\n</html>";

        //            var imageNames = new List<string>
        //            {
        //                "abstract.png",
        //                "cp_logo.png",
        //                "Server_Down.png"
        //            };

        //            SendEmailAsync(emailSettings, email, body, imageNames, "Server Down!.", "ServerDown");
        //        });

        //    else if (request.Type.ToLower().Trim().Equals("serverup"))
        //        request.Emails.ForEach(email =>
        //        {
        //            var userName = Regex.Split(email.ToMail, @"@");

        //            body =
        //                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <link rel=\"preconnect\" href=\"\" />\r\n    <link rel=\"preconnect\" href=\"\" crossorigin />\r\n    <link href=\"\" rel=\"stylesheet\" />\r\n    <title>Server UP</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 14px;\r\n            line-height: 28px;\r\n        }}\r\n    </style>\r\n</head>\r\n\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n    <!--  -->\r\n    <table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n        <thead>\r\n            <tr>\r\n                <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"width: 100%;\" />\r\n                </th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr>\r\n                <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n                    <h2 style=\"color: #008000;text-align: center;font-size:2rem;\">Server UP</h2>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"text-align: center;\"><img src=cid:Server_UP alt=\"Isometric_Orange_Warning\"\r\n                        style=\"height: 210px;\" />\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td>\r\n\r\n                    <p style=\"text-align: center;line-height: 0px;\">Dear<b style=\" text-transform: uppercase;\">\r\n                            {userName[0]},</b></p>\r\n                    <p style=\"text-align: center;\">{request.ErrorMessage}\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n           \r\n            <tr>\r\n                <td>\r\n                    <p><b>Best regards,</b><br />Continuity Patrol</p>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td style=\"background-color: #ededed;\">\r\n                    <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                        Continuity Patrol Version {version} © {DateTime.Now.Year}-{DateTime.Now.Year + 1}\r\n                        <a href=\"\" style=\"color: gray;\">Perpetuuiti</a> - All Rights\r\n                        Reserved\r\n                    </p>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    <!--  -->\r\n\r\n\r\n\r\n\r\n\r\n</body>\r\n\r\n</html>";

        //            var imageNames = new List<string>
        //            {
        //                "abstract.png",
        //                "cp_logo.png",
        //                "Server_UP.png"
        //            };

        //            SendEmailAsync(emailSettings, email, body, imageNames, "Server Up!.", "ServerUp");
        //        });
        //    else if (request.Type.ToLower().Trim().Equals("datalagexceed"))
        //        request.Emails.ForEach(email =>
        //        {
        //            var userName = Regex.Split(email.ToMail, @"@");

        //            body =
        //                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <link rel=\"preconnect\" href=\"\" />\r\n    <link rel=\"preconnect\" href=\"\" crossorigin />\r\n    <link href=\"\" rel=\"stylesheet\" />\r\n    <title>Database down!</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 14px;\r\n            line-height: 28px;\r\n        }}\r\n       \r\n    </style>\r\n</head>\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n<!--  -->\r\n<table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n    <thead>\r\n        <tr>\r\n            <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"width: 100%;\" />\r\n            </th>\r\n        </tr>\r\n    </thead>\r\n    <tbody>\r\n        <tr>\r\n            <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n        </tr>\r\n        <tr>\r\n            <td >\r\n                <h2 style=\"color: red;text-align: center;font-size:2rem;\">DataLag Exceed!</h2>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"text-align: center;\"><img src=cid:DataLag_Exceed alt=\"Isometric_Orange_Warning\" style=\"height: 210px;\"/>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n\r\n                <p style=\"text-align: center;line-height: 0px;\"><b style=\" text-transform: uppercase;\">Dear {userName[0]},</b></p>\r\n                <p style=\"text-align: center;\"{request.ErrorMessage}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n                <p ><b>Best regards,</b><br />Continuity Patrol</p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"background-color: #ededed;\">\r\n                <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                    Continuity Patrol Version {version} © {DateTime.Now.Year}-{DateTime.Now.Year + 1}\r\n                    <a href=\"/\" style=\"color: gray;\">Perpetuuiti</a> - All Rights\r\n                    Reserved\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n\r\n\r\n\r\n\r\n\r\n  \r\n</body>\r\n\r\n</html>";

        //            var imageNames = new List<string>
        //            {
        //                "abstract.png",
        //                "cp_logo.png",
        //                "DataLag_Exceed.png"
        //            };
        //            SendEmailAsync(emailSettings, email, body, imageNames, "DataLag Exceed!.", "DatalagExceed");
        //        });
        //    else if (request.Type.ToLower().Trim().Equals("databasedown"))
        //        request.Emails.ForEach(email =>
        //        {
        //            var userName = Regex.Split(email.ToMail, @"@");

        //            body =
        //                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <link rel=\"preconnect\" href=\"\" />\r\n    <link rel=\"preconnect\" href=\"\" crossorigin />\r\n    <link href=\"\" rel=\"stylesheet\" />\r\n    <title>Database down!</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 14px;\r\n            line-height: 28px;\r\n        }}\r\n       \r\n    </style>\r\n</head>\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n<!--  -->\r\n<table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n    <thead>\r\n        <tr>\r\n            <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"width: 100%;\" />\r\n            </th>\r\n        </tr>\r\n    </thead>\r\n    <tbody>\r\n        <tr>\r\n            <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n        </tr>\r\n        <tr>\r\n            <td >\r\n                <h2 style=\"color: red;text-align: center;font-size:2rem;\">Database down!</h2>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"text-align: center;\"><img src=cid:Database_Down alt=\"Isometric_Orange_Warning\" style=\"height: 210px;\"/>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n\r\n                <p style=\"text-align: center;line-height: 0px;\"><b style=\" text-transform: uppercase;\">Dear {userName[0]},</b></p>\r\n                <p style=\"text-align: center;\">{request.ErrorMessage}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n                <p ><b>Best regards,</b><br />Continuity Patrol</p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"background-color: #ededed;\">\r\n                <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                    Continuity Patrol Version {version} © {DateTime.Now.Year}-{DateTime.Now.Year + 1}\r\n                    <a href=\"\" style=\"color: gray;\">Perpetuuiti</a> - All Rights\r\n                    Reserved\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n\r\n\r\n\r\n\r\n\r\n  \r\n</body>\r\n\r\n</html>";

        //            var imageNames = new List<string>
        //            {
        //                "abstract.png",
        //                "cp_logo.png",
        //                "Database_Down.png"
        //            };
        //            SendEmailAsync(emailSettings, email, body, imageNames, "Database Down!.", "DatabaseDown");
        //        });
        //    else if (request.Type.ToLower().Trim().Equals("actionfail"))
        //        request.Emails.ForEach(email =>
        //        {
        //            var userName = Regex.Split(email.ToMail, @"@");

        //            body =
        //                $"<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n    <link rel=\"preconnect\" href=\"\" />\r\n    <link rel=\"preconnect\" href=\"\" crossorigin />\r\n    <link href=\"\" rel=\"stylesheet\" />\r\n    <title>Action Failed</title>\r\n    <style type=\"text/css\">\r\n        p {{\r\n            font-size: 14px;\r\n            line-height: 28px;\r\n        }}\r\n       \r\n    </style>\r\n</head>\r\n<body style=\" font-family: 'Poppins', sans-serif;\">\r\n<!--  -->\r\n<table class=\"table\" style=\"width: 650px;margin: auto;\">\r\n    <thead>\r\n        <tr>\r\n            <th><img src=cid:abstract alt=\"Abstract.svg\" style=\"width: 100%;\" />\r\n            </th>\r\n        </tr>\r\n    </thead>\r\n    <tbody>\r\n        <tr>\r\n            <td><img src=cid:cp_logo alt=\"CP_Logo\" style=\"height: 40px;\" /></td>\r\n        </tr>\r\n        <tr>\r\n            <td >\r\n                <h2 style=\"color: red;text-align: center;font-size:2rem;\">Action failed</h2>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"text-align: center;\"><img src=cid:Action_fail alt=\"Isometric_Orange_Warning\" style=\"height: 210px;\"/>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n\r\n                <p style=\"text-align: center;line-height: 0px;\"><b style=\" text-transform: uppercase;\">Dear {userName[0]},</b></p>\r\n                <p style=\"text-align: center;\">{request.ErrorMessage}\r\n                </p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td>\r\n                <p ><b>Best regards,</b><br />Continuity Patrol</p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td style=\"background-color: #ededed;\">\r\n                <p style=\"color: gray;font-size: 10px;text-align: center;\">\r\n                    Continuity Patrol Version {version} © {DateTime.Now.Year}-{DateTime.Now.Year + 1}\r\n                    <a href=\"/\" style=\"color: gray;\">Perpetuuiti</a> - All Rights\r\n                    Reserved\r\n                </p>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n\r\n\r\n\r\n\r\n\r\n  \r\n</body>\r\n\r\n</html>";

        //            var imageNames = new List<string>
        //            {
        //                "abstract.png",
        //                "cp_logo.png",
        //                "Action_fail.png"
        //            };

        //            SendEmailAsync(emailSettings, email, body, imageNames, "Action Fail!.", "ActionFail");
        //        });
        //    else
        //        throw new Exception("Requested mail type not found.");

        var response = new SendEmailResponse
        {
            Message = "Email Sent Successfully!."
        };

        return response;
    }

    //public Task<bool> SendEmailAsync(Domain.Entities.SmtpConfiguration smtpConfiguration, Email email, string body,
    //    List<string> imageNames, string subject, string errorType)
    //{
    //    var htmlView = HtmlEmailBuilder.BuildHtmlView(body, imageNames, errorType);

    //    return _emailService.SendEmail(new EmailDto
    //    {
    //        From = smtpConfiguration.UserName,
    //        Port = smtpConfiguration.Port,
    //        EnableSSL = smtpConfiguration.EnableSSL,
    //        Password = smtpConfiguration.Password,
    //        SmtpHost = smtpConfiguration.SmtpHost,
    //        To = email.ToMail,
    //        Subject = subject,
    //        HtmlBody = htmlView,
    //        IsPasswordLess = smtpConfiguration.IsPasswordLess
    //    });
    //}
}