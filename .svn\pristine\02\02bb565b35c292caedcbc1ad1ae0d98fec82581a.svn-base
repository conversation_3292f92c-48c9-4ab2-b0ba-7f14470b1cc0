﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.Create;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.PageSolutionMapping.Commands
{
    public class CreatePageSolutionMappingTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPageSolutionMappingRepository> _mockPageSolutionMappingRepository;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly CreatePageSolutionMappingCommandHandler _handler;

        public CreatePageSolutionMappingTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockPageSolutionMappingRepository = new Mock<IPageSolutionMappingRepository>();
            _mockPublisher = new Mock<IPublisher>();
            _handler = new CreatePageSolutionMappingCommandHandler(_mockMapper.Object, _mockPageSolutionMappingRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnResponse_WhenValidRequestIsProvided()
        {
            var request = new CreatePageSolutionMappingCommand
            {
                Name = "TestPageSolution",
                PageBuilderName = "TestPageBuilder"
            };

            var mappedEntity = new Domain.Entities.PageSolutionMapping
            {
                Name = "TestPageSolution",
                PageBuilderName = "TestPageBuilder",
                ReferenceId = Guid.NewGuid().ToString()
            };

            _mockMapper
                .Setup(m => m.Map<Domain.Entities.PageSolutionMapping>(request))
                .Returns(mappedEntity);

            _mockPageSolutionMappingRepository
                .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.PageSolutionMapping>()))
                .ReturnsAsync(mappedEntity);

            var expectedResponse = new CreatePageSolutionMappingResponse
            {
                Message = Message.Create(nameof(PageSolutionMapping), mappedEntity.Name),
                Id = mappedEntity.ReferenceId
            };

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedResponse.Message, result.Message);
            Assert.Equal(expectedResponse.Id, result.Id);

            _mockMapper.Verify(m => m.Map<Domain.Entities.PageSolutionMapping>(request), Times.Once);
            _mockPageSolutionMappingRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.PageSolutionMapping>()), Times.Once);
            _mockPublisher.Verify(p => p.Publish(It.IsAny<PageSolutionMappingCreatedEvent>(), CancellationToken.None), Times.Once);
        }
    }
}
