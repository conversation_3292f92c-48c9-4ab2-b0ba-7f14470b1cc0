using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Update;

public class ApprovalMatrixRequestUpdatedEventHandler : INotificationHandler<ApprovalMatrixRequestUpdatedEvent>
{
    private readonly ILogger<ApprovalMatrixRequestUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ApprovalMatrixRequestUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<ApprovalMatrixRequestUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(ApprovalMatrixRequestUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} ApprovalMatrixRequest",
            Entity = "ApprovalMatrixRequest",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"ApprovalMatrixRequest '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ApprovalMatrixRequest '{updatedEvent.Name}' updated successfully.");
    }
}