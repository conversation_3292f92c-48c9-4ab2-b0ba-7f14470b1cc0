﻿using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Infrastructure.Contract;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Database.Commands.Create;

public class CreateDatabaseCommandValidator : AbstractValidator<CreateDatabaseCommand>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly ILicenseValidationService _licenseValidationService;
    private readonly IServerRepository _serverRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;

    public CreateDatabaseCommandValidator(IDatabaseRepository databaseRepository, IServerRepository serverRepository,
        ISiteRepository siteRepository, ILicenseValidationService licenseValidationService,
        ILicenseManagerRepository licenseManagerRepository, ISiteTypeRepository siteTypeRepository)
    {
        _databaseRepository = databaseRepository;
        _serverRepository = serverRepository;
        _siteRepository = siteRepository;
        _licenseValidationService = licenseValidationService;
        _licenseManagerRepository = licenseManagerRepository;
        _siteTypeRepository = siteTypeRepository;


        RuleFor(p => p)
            .Must(IsValidGUID)
            .WithMessage("Invalid id");

        RuleFor(p => p.LicenseKey)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(IsLicenseExpiredAsync)
            .WithMessage("The license key has expired.");

        RuleFor(p => p)
            .MustAsync(IsLicenseActiveAsync)
            .WithMessage("License is in 'InActive' state");

        RuleFor(p => p)
            .MustAsync(ValidateLicenseCountAsync)
            .WithMessage("Database count Reached maximum limit.");

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s-])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.DatabaseType)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .Matches(@"^[a-zA-Z\d]+([_\s\-\.][a-zA-Z\d]+)*$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.BusinessServiceName)
            .NotEmpty().WithMessage("Select Operational Service.")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s\-]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid Operational Service")
            .NotNull()
            .Length(3, 100).WithMessage("Operational Service should contain between 3 to 100 characters.");

        RuleFor(p => p.ServerName)
            .NotEmpty().WithMessage("Select {PropertyName}.")
            .NotNull()
            .Matches(@"^([a-zA-Z\d]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Must(GetJsonProperties.IsValidJson)
            .WithMessage("{PropertyName} must be a valid Json string.");

        RuleFor(p => p.ModeType)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p)
            .MustAsync(DatabaseNameUnique)
            .WithMessage("A same Name already exist.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("Select {PropertyName}")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();
        //RuleFor(p=>p)
        //    .MustAsync(IsValidDatabaseSID)
        //    .WithMessage("Invalid Database SID");

        //RuleFor(p => p.DatabaseConnectivity)
        //    .NotEmpty().WithMessage("Select Database Connectivity")
        //    .NotNull();
    }

    private async Task<bool> DatabaseNameUnique(CreateDatabaseCommand p, CancellationToken cancellationToken)
    {
        return !await _databaseRepository.IsDatabaseNameUnique(p.Name);
    }

    private async Task<bool> IsLicenseExpiredAsync(CreateDatabaseCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.LicenseId, "License Id");

        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId);

        return await _licenseValidationService.IsLicenseExpired(licenseManager.ExpiryDate);
    }

    private async Task<bool> IsLicenseActiveAsync(CreateDatabaseCommand p, CancellationToken cancellationToken)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;

        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                         throw new InvalidException("License is null or empty.");

        return licenseDtl.IsState;
    }

    private bool IsValidGUID(CreateDatabaseCommand createDatabaseCommand)
    {
        Guard.Against.InvalidGuidOrEmpty(createDatabaseCommand.LicenseId, "License id");
        Guard.Against.InvalidGuidOrEmpty(createDatabaseCommand.BusinessServiceId, "Operational service id");
        Guard.Against.InvalidGuidOrEmpty(createDatabaseCommand.ServerId, "Server id");
        Guard.Against.InvalidGuidOrEmpty(createDatabaseCommand.DatabaseTypeId, "Database type id");
        return true;
    }

    private async Task<bool> ValidateLicenseCountAsync(CreateDatabaseCommand p, CancellationToken cancellationToken)
    {
        if (p.LicenseId.IsNullOrWhiteSpace()) return false;

        var server = await _serverRepository.GetByReferenceIdAsync(p.ServerId);

        Guard.Against.NullOrDeactive(server, nameof(Domain.Entities.Server),
            new NotFoundException(nameof(Domain.Entities.Server), p.ServerId));

        var site = await _siteRepository.GetByReferenceIdAsync(server.SiteId);

        Guard.Against.NullOrDeactive(site, nameof(Domain.Entities.Site),
            new NotFoundException(nameof(Domain.Entities.Site), server.SiteId));

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

        Guard.Against.NullOrDeactive(siteType, nameof(Domain.Entities.SiteType),
            new NotFoundException(nameof(Domain.Entities.SiteType), site.TypeId));

        var index = await _siteTypeRepository.GetSiteTypeIndexByIdAsync(siteType.ReferenceId);

        var licenseManager = await _licenseManagerRepository.GetLicenseDetailByIdAsync(p.LicenseId) ??
                             throw new InvalidException("License is null or empty.");

        var siteVm = await _siteRepository.GetSiteBySiteTypeId(siteType.ReferenceId);

        var siteIds = siteVm.Select(x => x.ReferenceId).ToList();

        var isDatabase = GetJsonProperties.GetJsonValueAsBool(licenseManager.Properties, "isDatabase");

        if (isDatabase)
        {
            var isDatabaseType = await _licenseValidationService.IsDatabaseTypeAsync(licenseManager.Properties, siteType.Category.ToLower(), p.DatabaseTypeId);

            var customCount = isDatabaseType.Success 
                ? await _databaseRepository.GetCountByTypeAndLicenseKey(p.LicenseId, p.DatabaseTypeId, siteIds)
                : await _databaseRepository.GetCountByTypeIdsAndLicenseId(p.LicenseId, isDatabaseType.TypeIds, siteIds);

            var customTypeCount =
                await _licenseValidationService.IsDatabaseTypeLicenseCountExitMaxLimit(licenseManager, siteType, p.DatabaseTypeId, customCount, index);

            return customTypeCount;
        }

        var databaseCount = await _databaseRepository.GetDatabaseCountByLicenseKey(p.LicenseId, siteIds);

        var licenseDtl =
            await _licenseValidationService.IsDatabaseLicenseCountExitMaxLimit(licenseManager, siteType, databaseCount,
                index);

        return licenseDtl;
    }

    
    private bool IsValidJsonObject(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }

    private Task<bool> IsValidDatabaseSID(CreateDatabaseCommand p, CancellationToken token)
    {
      
        var sid = GetJsonProperties.GetJsonDatabaseSidValue(p.Properties);

        return Task.FromResult(sid is not null
            ? new Regex(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$").IsMatch(sid)
            : true);
    }
}