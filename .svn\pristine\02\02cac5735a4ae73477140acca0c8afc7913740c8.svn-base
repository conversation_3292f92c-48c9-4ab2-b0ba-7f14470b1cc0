﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Events.Paginated;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CGExecution.Events;

public class CGExecutionPaginatedEventHandlerTests : IClassFixture<CGExecutionReportFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CGExecutionReportFixture _cgExecutionFixture;
    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly CGExecutionPaginatedEventHandler _handler;

    public CGExecutionPaginatedEventHandlerTests(CGExecutionReportFixture cgExecutionFixture, UserActivityFixture userActivityFixture)
    {
        _cgExecutionFixture = cgExecutionFixture;
        _userActivityFixture = userActivityFixture;

        var mockLogger = new Mock<ILogger<CGExecutionPaginatedEventHandler>>();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockUserActivityRepository = CGExecutionReportRepositoryMocks.CreateCGExecutionReportEventRepository(_userActivityFixture.UserActivities);

        _handler = new CGExecutionPaginatedEventHandler(mockLoggedInUserService.Object, mockLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CGExecutionPaginatedEventTriggered()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        // Act
        var result = _handler.Handle(_cgExecutionFixture.CGExecutionPaginatedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Act
        await _handler.Handle(_cgExecutionFixture.CGExecutionPaginatedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
