﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixRequest.Events.Withdraw;

public class WithdrawApprovalMatrixRequestedEventHandler : INotificationHandler<WithdrawApprovalMatrixRequestedEvent>
{
    private readonly ILogger<WithdrawApprovalMatrixRequestedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WithdrawApprovalMatrixRequestedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<WithdrawApprovalMatrixRequestedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(WithdrawApprovalMatrixRequestedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Withdraw} ApprovalMatrixRequest",
            Entity = "ApprovalMatrixRequest",
            ActivityType = ActivityType.Withdraw.ToString(),
            ActivityDetails = $"Approval Matrix Request '{updatedEvent.Name}' has been withdraw successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Approval Matrix Request '{updatedEvent.Name}' has been withdraw successfully.");
    }
}