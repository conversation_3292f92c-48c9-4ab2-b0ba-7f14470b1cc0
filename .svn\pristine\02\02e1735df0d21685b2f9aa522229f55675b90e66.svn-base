﻿@{
    ViewData["Title"] = "DataLagStatusReport";
}

@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }


<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("DataLagDocumentViewer").Height("1150px").Bind(new DataLagStatusReport(ViewData["DataLagStatusReportData"].ToString()))

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">DataLag Status Report</h6>
        </div>
    </div>
</div>
<div class="card">
    <div>

        <div class="mt-3">
            <div class="rounded card bg-light">
                <div class="card-header text-primary fw-semibold border-bottom">
                    Report Details
                </div>
                <div class="p-0 card-body">
                    <div class="rounded-0 list-group">
                        <div class="d-flex justify-content-between px-3 py-2 border-top-0 list-group-item">
                            <div class="me-auto d-flex align-items-center">
                                <i class="cp-server-role"></i>
                                <span class="ms-1 align-middle">Report Date</span>
                            </div>
                            21-09-2023 11:01:04
                        </div>
                        <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                            <div class="me-auto d-flex align-items-center">
                                <span class="ms-1 align-middle text-success d-flex">
                                    <span class="badge bg-success status_warning"></span>&nbsp;
                                    DataLag &lt;= Configured RPO
                                </span>
                            </div>
                            <div class="d-flex align-items-center ">
                                <div>
                                    <span class="badge status_warning"
                                          style="background-color: var(--bs-indigo);"></span>
                                </div>
                                <div class="ms-2 mt-1" style="color: var(--bs-indigo);">
                                    Threshold
                                </div>
                            </div>
                        </li>
                        <li class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                            <div class="me-auto d-flex align-items-center">
                                <span class="ms-1 align-middle text-danger d-flex">
                                    <span class="badge bg-danger status_warning"></span>&nbsp;
                                    DataLag &lt;= Configured RPO
                                </span>
                            </div>
                            <div class="d-flex align-items-center ">
                                <div>
                                    <span class="badge status_warning bg-warning"></span>
                                </div>
                                <div class="ms-2 mt-1">Not Available</div>
                            </div>
                        </li>

                    </div>
                </div>
            </div>

        </div>
        <div class="mt-3 prebuildreportcustom">
            <div class="d-flex justify-content-center mt-2">24 HOURS-DATALAG DETAILS</div>
            <div>
                <div class="mt-3 mb-1 row">
                    <div class="col-12">
                        <div class="bg-secondary rounded-0 text-light mt-1">
                            <h6 class="Report-Header">
                                Business Service Name-Desc
                                &nbsp;&nbsp;&nbsp; BusinessService_Development
                            </h6>
                        </div>
                    </div>

                </div>
              
                <div class="table-responsive">
                    <table id="table-to-xls" class="table table-sm">
                        <thead class="bg-light text-center">
                            <tr>
                                <th>Sr.No</th>
                                <th>InfraObject</th>
                                <th>0.00</th>
                                <th>1.00</th>
                                <th>2.00</th>
                                <th>3.00</th>
                                <th>4.00</th>
                                <th>5.00</th>
                                <th>6.00</th>
                                <th>7.00</th>
                                <th>8.00</th>
                                <th>9.00</th>
                                <th>10.00</th>
                                <th>11.00</th>
                                <th>12.00</th>
                                <th>13.00</th>
                                <th>14.00</th>
                                <th>15.00</th>
                                <th>16.00</th>
                                <th>17.00</th>
                                <th>18.00</th>
                                <th>19.00</th>
                                <th>20.00</th>
                                <th>21.00</th>
                                <th>22.00</th>
                                <th>23.00</th>
                            </tr>
                        </thead>
                        <tbody id="tableDataone">
                        </tbody>
                    </table>
                </div>
            </div>
            <div>
                <div class="mt-3 mb-1 row">
                    <div class="col-12">
                        <div class="bg-secondary rounded-0 text-light mt-1">
                            <h6 class="Report-Header">
                                Business Service Name-Desc
                                &nbsp;&nbsp;&nbsp; Test_BS_New_01
                            </h6>
                        </div>
                    </div>
                  
                </div>
                <div class="table-responsive">
                    <table id="table-to-xls" class="table table-sm">
                        <thead class="bg-light text-center">
                            <tr>
                                <th>Sr.No</th>
                                <th>InfraObject</th>
                                <th>0.00</th>
                                <th>1.00</th>
                                <th>2.00</th>
                                <th>3.00</th>
                                <th>4.00</th>
                                <th>5.00</th>
                                <th>6.00</th>
                                <th>7.00</th>
                                <th>8.00</th>
                                <th>9.00</th>
                                <th>10.00</th>
                                <th>11.00</th>
                                <th>12.00</th>
                                <th>13.00</th>
                                <th>14.00</th>
                                <th>15.00</th>
                                <th>16.00</th>
                                <th>17.00</th>
                                <th>18.00</th>
                                <th>19.00</th>
                                <th>20.00</th>
                                <th>21.00</th>
                                <th>22.00</th>
                                <th>23.00</th>
                            </tr>
                        </thead>
                        <tbody id="tableDatatwo">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>

<script src="~/js/report-charts/datalag_status_report.js"></script>*@