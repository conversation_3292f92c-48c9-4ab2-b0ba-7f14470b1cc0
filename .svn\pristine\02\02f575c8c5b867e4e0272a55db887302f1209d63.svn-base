﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetWorkflowServiceStatus;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowServiceStatusQueryHandlerTests
{
    private readonly Mock<ILoadBalancerRepository> _mockRepo = new();
    private readonly Mock<IWindowsService> _mockWindowsService = new();
    private readonly GetWorkflowServiceStatusQueryHandler _handler;

    public GetWorkflowServiceStatusQueryHandlerTests()
    {
        _handler = new GetWorkflowServiceStatusQueryHandler(_mockRepo.Object, _mockWindowsService.Object);
    }

    [Fact]
    public async Task Handle_ReturnsSuccess_WhenServicesAreConfigured()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string> { "Node1" },
                InActiveNodes = new List<string>(),
                Message = "All good"
            });

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("Workflow service 'Node1' is running!.", result.ActiveNodes);
    }

    [Fact]
    public async Task Handle_ReturnsFailure_WhenNoNodesConfigured()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Monitor" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string>(),
                InActiveNodes = new List<string>(),
                Message = "Nothing found"
            });

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Monitor node service not configured!.", result.Message);
    }

    [Fact]
    public async Task Handle_ThrowsException_WhenLoadBalancerNotConfigured()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Monitor" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null!);

        // Act & Assert
        var ex = await _handler.Handle(query, CancellationToken.None);

        Assert.Equal("Load Balancer not configured!.", ex.Message);
    }

    [Fact]
    public async Task Handle_ThrowsException_WhenConnectionFails()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(false);

        // Act & Assert
        var ex = await _handler.Handle(query, CancellationToken.None);

        Assert.Equal("Load Balancer is configured but not connected!.", ex.Message);
    }

    [Fact]
    public async Task Handle_ThrowsException_WhenConnectionOff_StateFails()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = false
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(false);

        // Act & Assert
        var ex = await _handler.Handle(query, CancellationToken.None);

        Assert.Equal("The Load Balancer has been configured, but it is currently in the 'off' state.", ex.Message);
    }


    [Fact]
    public async Task Handle_TrimsAndNormalizesType_Correctly()
    {
        var query = new GetWorkflowServiceStatusQuery { Type = "  Monitor  " };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string> { "NodeX" },
                InActiveNodes = new List<string>(),
                Message = "Fine"
            });

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.True(result.Success);
        Assert.Contains("Monitor service 'NodeX' is running!.", result.ActiveNodes);
    }

    [Fact]
    public async Task Handle_ReturnsBothActiveAndInactiveNodes()
    {
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string> { "NodeA" },
                InActiveNodes = new List<string> { "NodeB" },
                Message = "Partial"
            });

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.True(result.Success);
        Assert.Contains("Workflow service 'NodeA' is running!.", result.ActiveNodes);
        Assert.Contains("Workflow service 'NodeB' is not start.", result.InActiveNodes);
    }


    [Fact]
    public async Task Handle_UsesFallbackNodeConfig_WhenAllTypeIsNull()
    {
        var query = new GetWorkflowServiceStatusQuery { Type = "workflow" };

        _mockRepo.SetupSequence(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync((Domain.Entities.LoadBalancer)null!);

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("WorkflowService", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "***********",
                Port = 6000,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string> { "WNode1" },
                InActiveNodes = new List<string>(),
                Message = "Fallback Config OK"
            });

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.True(result.Success);
        Assert.Contains("Workflow service 'WNode1' is running!.", result.ActiveNodes);
    }

    [Fact]
    public async Task Handle_ReturnsFailure_WhenUnexpectedExceptionOccurs()
    {
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory(It.IsAny<string>(), It.IsAny<string>()))
            .ThrowsAsync(new Exception("Unexpected DB failure"));

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.False(result.Success);
        Assert.Equal("Unexpected DB failure", result.Message);
        Assert.Null(result.ActiveNodes);
        Assert.Null(result.InActiveNodes);
    }


    [Fact]
    public async Task Handle_ReturnsFailure_WhenCheckWindowsServiceReturnsNull()
    {
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync((ServiceResponse)null!);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.False(result.Success);
        Assert.Null(result.ActiveNodes);
        Assert.Null(result.InActiveNodes);
        //Assert.Null(result.InActiveSeqNodes);
    }

    [Fact]
    public async Task Handle_ReturnsCorrectMessages_WhenNodeNamesHaveStatusFlags()
    {
        // Arrange
        var query = new GetWorkflowServiceStatusQuery { Type = "Workflow" };

        _mockRepo.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", "LoadBalancer"))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080,
                TypeCategory = "CP Node",
                IsNodeStatus = true
            });

        _mockWindowsService
            .Setup(x => x.IsTcpClientConnectionSuccessfulAsync(It.IsAny<string>(), It.IsAny<int>()))
            .ReturnsAsync(true);

        _mockWindowsService
            .Setup(x => x.CheckWindowsService(It.IsAny<string>()))
            .ReturnsAsync(new ServiceResponse
            {
                ActiveNodes = new List<string>
                {
                    "Node1,true",  // should result in running + true
                    "Node2,false"  // should result in marked as 'off' + false
                },
                InActiveNodes = new List<string>(),
                Message = "Sample Message"
            });

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.ActiveNodes!.Count == 2);
        Assert.Contains("Workflow service 'Node1' is running!.", result.ActiveNodes);
        Assert.Contains("Workflow service 'Node2' is running, but its state is marked as 'off'.", result.ActiveNodes);
        Assert.False(result.Success); // Because one is false
        Assert.Empty(result.InActiveNodes);
    }
}