﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Update;

public class UpdateWorkflowCommand : IRequest<UpdateWorkflowResponse>
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Properties { get; set; }
    public bool IsLock { get; set; }
    public bool IsPublish { get; set; }

    [JsonIgnore] public string Version { get; set; }

    public string Comments { get; set; }

    public override string ToString()
    {
        return $"Name: {Name}; Id:{Id};";
    }
}