﻿namespace ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;

public class CreateAlertNotificationCommand : IRequest<CreateAlertNotificationResponse>
{
    public int AlertCategoryId { get; set; }
    public string AlertType { get; set; }
    public string AlertCode { get; set; }
    public int AlertSentCount { get; set; }
    public string InfraObjectId { get; set; }
    public string EntityId { get; set; }
    public int PositiveAlertCount { get; set; }

    public override string ToString()
    {
        return $"Alert Type: {AlertType};";
    }
}