﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DrReadyStatusFilterSpecification : Specification<DRReadyStatus>
{
    public DrReadyStatusFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.BusinessServiceName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessservicename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.BusinessServiceName.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.BusinessServiceName.Contains(searchString);
            }
        }
    }
}