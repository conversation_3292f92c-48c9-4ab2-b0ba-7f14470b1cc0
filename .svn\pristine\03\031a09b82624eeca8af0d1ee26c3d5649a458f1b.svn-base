﻿using ContinuityPatrol.Shared.Tests.Mocks;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Base;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Concurrent;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class SolutionHistoryControllerShould
    {
        private readonly SolutionHistoryController _controller;

        public SolutionHistoryControllerShould()
        {
            _controller = new SolutionHistoryController();
            _controller.ControllerContext = new ControllerContextMocks().Default();
        }

        // ===== BASIC FUNCTIONALITY TESTS =====

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void List_ReturnsViewWithNoModel()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Model);
        }

        [Fact]
        public void List_ReturnsDefaultViewName()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.ViewName); // Default view name (null means it uses the action name)
        }

        [Fact]
        public void List_DoesNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.List());
            Assert.Null(exception);
        }

        // ===== CONTROLLER STRUCTURE AND INHERITANCE TESTS =====

        [Fact]
        public void Controller_ShouldInheritFromBaseController()
        {
            // Assert
            Assert.IsAssignableFrom<BaseController>(_controller);
        }

        [Fact]
        public void Controller_ShouldImplementIBase()
        {
            // Assert
            Assert.IsAssignableFrom<IBase>(_controller);
        }

        [Fact]
        public void Controller_ShouldInheritFromController()
        {
            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(_controller);
        }

        // ===== ATTRIBUTE TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldInheritAuthorizeAttribute()
        {
            // Arrange
            var baseControllerType = typeof(BaseController);

            // Act
            var authorizeAttribute = baseControllerType.GetCustomAttributes(typeof(AuthorizeAttribute), false)
                .Cast<AuthorizeAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(authorizeAttribute);
        }

        [Fact]
        public void List_ShouldNotHaveSpecificAttributes()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");

            // Act
            var httpGetAttribute = method.GetCustomAttributes(typeof(HttpGetAttribute), false).FirstOrDefault();
            var httpPostAttribute = method.GetCustomAttributes(typeof(HttpPostAttribute), false).FirstOrDefault();
            var validateAntiForgeryTokenAttribute = method.GetCustomAttributes(typeof(ValidateAntiForgeryTokenAttribute), false).FirstOrDefault();

            // Assert
            Assert.Null(httpGetAttribute); // No explicit HttpGet needed for default GET action
            Assert.Null(httpPostAttribute);
            Assert.Null(validateAntiForgeryTokenAttribute);
        }

        // ===== MULTIPLE INVOCATION TESTS =====

        [Fact]
        public void List_CanBeCalledMultipleTimes()
        {
            // Act
            var result1 = _controller.List() as ViewResult;
            var result2 = _controller.List() as ViewResult;
            var result3 = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.NotNull(result3);
            Assert.IsType<ViewResult>(result1);
            Assert.IsType<ViewResult>(result2);
            Assert.IsType<ViewResult>(result3);
        }

        [Fact]
        public void List_ReturnsConsistentResults()
        {
            // Act
            var result1 = _controller.List() as ViewResult;
            var result2 = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.Equal(result1.GetType(), result2.GetType());
            Assert.Equal(result1.Model, result2.Model); // Both should be null
            Assert.Equal(result1.ViewName, result2.ViewName); // Both should be null (default)
        }

        // ===== PERFORMANCE AND THREAD SAFETY TESTS =====

        [Fact]
        public void List_ExecutesQuickly()
        {
            // Arrange
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            var result = _controller.List();

            // Assert
            stopwatch.Stop();
            Assert.True(stopwatch.ElapsedMilliseconds < 100, "List method should execute quickly");
            Assert.NotNull(result);
        }

        [Fact]
        public void List_IsThreadSafe()
        {
            // Arrange
            var tasks = new List<Task<IActionResult>>();
            var numberOfTasks = 10;

            // Act
            for (int i = 0; i < numberOfTasks; i++)
            {
                tasks.Add(Task.Run(() => _controller.List()));
            }

            Task.WaitAll(tasks.ToArray());

            // Assert
            foreach (var task in tasks)
            {
                Assert.NotNull(task.Result);
                Assert.IsType<ViewResult>(task.Result);
            }
        }

        // ===== CONTROLLER CONTEXT TESTS =====

        [Fact]
        public void Controller_ShouldHaveValidControllerContext()
        {
            // Assert
            Assert.NotNull(_controller.ControllerContext);
            Assert.NotNull(_controller.ControllerContext.HttpContext);
        }

        [Fact]
        public void Controller_ShouldHaveValidHttpContext()
        {
            // Assert
            Assert.NotNull(_controller.HttpContext);
            Assert.IsType<DefaultHttpContext>(_controller.HttpContext);
        }

        [Fact]
        public void Controller_ShouldHaveValidRequest()
        {
            // Assert
            Assert.NotNull(_controller.Request);
            Assert.NotNull(_controller.Request.Headers);
        }

        [Fact]
        public void Controller_ShouldHaveValidResponse()
        {
            // Assert
            Assert.NotNull(_controller.Response);
            Assert.NotNull(_controller.Response.Headers);
        }

        // ===== INHERITED PROPERTIES TESTS =====

        [Fact]
        public void Controller_ShouldHaveLoggedInNameProperty()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.LoggedInName);
            Assert.Null(exception); // Should not throw exception
        }

        [Fact]
        public void Controller_ShouldHaveLoggedInUserIdProperty()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.LoggedInUserId);
            Assert.Null(exception); // Should not throw exception
        }

        [Fact]
        public void Controller_ShouldHaveIsUserAuthenticatedProperty()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.IsUserAuthenticated);
            Assert.Null(exception); // Should not throw exception
        }

        [Fact]
        public void Controller_ShouldHaveMessageProperty()
        {
            // Act
            _controller.Message = "Test Message";

            // Assert
            Assert.Equal("Test Message", _controller.Message);
        }

        // ===== ROUTING TESTS =====

        [Fact]
        public void Controller_ShouldHaveCorrectRouting()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void List_ShouldBeAccessibleViaDefaultRoute()
        {
            // Arrange
            var actionName = nameof(_controller.List);

            // Assert
            Assert.Equal("List", actionName);
        }

        // ===== MEMORY AND RESOURCE TESTS =====

        [Fact]
        public void Controller_ShouldBeDisposable()
        {
            // Arrange
            var controller = new SolutionHistoryController();

            // Act & Assert
            Assert.IsAssignableFrom<IDisposable>(controller);
        }

        [Fact]
        public void Controller_ShouldDisposeWithoutException()
        {
            // Arrange
            var controller = new SolutionHistoryController();

            // Act & Assert
            var exception = Record.Exception(() => controller.Dispose());
            Assert.Null(exception);
        }

        // ===== ADDITIONAL COMPREHENSIVE TESTS =====

        [Fact]
        public void Controller_ShouldHaveParameterlessConstructor()
        {
            // Act & Assert
            var exception = Record.Exception(() => new SolutionHistoryController());
            Assert.Null(exception);
        }

        [Fact]
        public void Controller_ShouldCreateMultipleInstancesIndependently()
        {
            // Act
            var controller1 = new SolutionHistoryController();
            var controller2 = new SolutionHistoryController();

            // Assert
            Assert.NotNull(controller1);
            Assert.NotNull(controller2);
            Assert.NotSame(controller1, controller2);
        }

        [Fact]
        public void List_ShouldReturnViewResultWithCorrectType()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            Assert.IsAssignableFrom<IActionResult>(result);
        }

        [Fact]
        public void List_ShouldNotModifyControllerState()
        {
            // Arrange
            var initialMessage = _controller.Message;
            var initialModelState = _controller.ModelState.IsValid;

            // Act
            _controller.List();

            // Assert
            Assert.Equal(initialMessage, _controller.Message);
            Assert.Equal(initialModelState, _controller.ModelState.IsValid);
        }

        [Fact]
        public void Controller_ShouldHaveCorrectNamespace()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.Equal("ContinuityPatrol.Web.Areas.Admin.Controllers", controllerType.Namespace);
        }

        [Fact]
        public void Controller_ShouldBePublicClass()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.True(controllerType.IsPublic);
            Assert.True(controllerType.IsClass);
            Assert.False(controllerType.IsAbstract);
            Assert.False(controllerType.IsSealed);
        }

        [Fact]
        public void List_ShouldBePublicMethod()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");

            // Assert
            Assert.NotNull(method);
            Assert.True(method.IsPublic);
            Assert.False(method.IsStatic);
            Assert.False(method.IsAbstract);
            Assert.False(method.IsVirtual);
        }

        [Fact]
        public void List_ShouldHaveCorrectReturnType()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");

            // Assert
            Assert.NotNull(method);
            Assert.Equal(typeof(IActionResult), method.ReturnType);
        }

        [Fact]
        public void List_ShouldHaveNoParameters()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");

            // Assert
            Assert.NotNull(method);
            Assert.Empty(method.GetParameters());
        }

        [Fact]
        public void Controller_ShouldNotHaveGenericParameters()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.False(controllerType.IsGenericType);
            Assert.False(controllerType.IsGenericTypeDefinition);
        }

        [Fact]
        public void Controller_ShouldInheritCorrectBaseClass()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.Equal(typeof(BaseController), controllerType.BaseType);
        }

        [Fact]
        public void List_ShouldNotThrowExceptionWithNullControllerContext()
        {
            // Arrange
            var controller = new SolutionHistoryController();
            // Note: ControllerContext is null by default

            // Act & Assert
            var exception = Record.Exception(() => controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public void Controller_ShouldHaveCorrectAssembly()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.Contains("ContinuityPatrol.Web", controllerType.Assembly.FullName);
        }

        [Fact]
        public void List_ShouldReturnSameTypeConsistently()
        {
            // Act
            var result1 = _controller.List();
            var result2 = _controller.List();
            var result3 = _controller.List();

            // Assert
            Assert.Equal(result1.GetType(), result2.GetType());
            Assert.Equal(result2.GetType(), result3.GetType());
            Assert.All(new[] { result1, result2, result3 }, r => Assert.IsType<ViewResult>(r));
        }

        [Fact]
        public void Controller_ShouldImplementIActionResultInterface()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.IsAssignableFrom<IActionResult>(result);
        }

        [Fact]
        public void Controller_ShouldHaveValidToStringMethod()
        {
            // Act
            var toStringResult = _controller.ToString();

            // Assert
            Assert.NotNull(toStringResult);
            Assert.Contains("SolutionHistoryController", toStringResult);
        }

        [Fact]
        public void Controller_ShouldHaveValidGetHashCodeMethod()
        {
            // Arrange
            var controller1 = new SolutionHistoryController();
            var controller2 = new SolutionHistoryController();

            // Act
            var hashCode1 = controller1.GetHashCode();
            var hashCode2 = controller2.GetHashCode();

            // Assert
            Assert.IsType<int>(hashCode1);
            Assert.IsType<int>(hashCode2);
            // Different instances should have different hash codes
            Assert.NotEqual(hashCode1, hashCode2);
        }

        [Fact]
        public void Controller_ShouldHaveValidEqualsMethod()
        {
            // Arrange
            var controller1 = new SolutionHistoryController();
            var controller2 = new SolutionHistoryController();

            // Act & Assert
            Assert.True(controller1.Equals(controller1)); // Same instance
            Assert.False(controller1.Equals(controller2)); // Different instances
            Assert.False(controller1.Equals(null)); // Null comparison
            Assert.False(controller1.Equals("string")); // Different type
        }

        // ===== SECURITY AND AUTHORIZATION TESTS =====

        [Fact]
        public void Controller_ShouldInheritAuthorizationFromBaseController()
        {
            // Arrange
            var baseType = typeof(BaseController);
            var authorizeAttributes = baseType.GetCustomAttributes(typeof(AuthorizeAttribute), true);

            // Assert
            Assert.NotEmpty(authorizeAttributes);
        }

        [Fact]
        public void Controller_ShouldNotHaveAllowAnonymousAttribute()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var allowAnonymousAttribute = controllerType.GetCustomAttribute<AllowAnonymousAttribute>();

            // Assert
            Assert.Null(allowAnonymousAttribute);
        }

        [Fact]
        public void List_ShouldNotHaveAllowAnonymousAttribute()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");
            var allowAnonymousAttribute = method.GetCustomAttribute<AllowAnonymousAttribute>();

            // Assert
            Assert.Null(allowAnonymousAttribute);
        }

        [Fact]
        public void Controller_ShouldBeInAdminArea()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var areaAttribute = controllerType.GetCustomAttribute<AreaAttribute>();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        // ===== INTEGRATION AND COMPATIBILITY TESTS =====

        [Fact]
        public void Controller_ShouldBeCompatibleWithMvcFramework()
        {
            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(_controller);
            Assert.IsAssignableFrom<ControllerBase>(_controller);
        }

        [Fact]
        public void Controller_ShouldSupportModelBinding()
        {
            // Assert
            Assert.NotNull(_controller.ModelState);
            Assert.True(_controller.ModelState.IsValid);
        }

        [Fact]
        public void Controller_ShouldSupportTempData()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.TempData);
            Assert.Null(exception);
        }

        [Fact]
        public void Controller_ShouldSupportViewData()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.ViewData);
            Assert.Null(exception);
        }

        [Fact]
        public void Controller_ShouldSupportViewBag()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.ViewBag);
            Assert.Null(exception);
        }

        // ===== ERROR HANDLING AND RESILIENCE TESTS =====

        [Fact]
        public void List_ShouldHandleHighConcurrency()
        {
            // Arrange
            var tasks = new List<Task<IActionResult>>();
            var numberOfConcurrentCalls = 100;

            // Act
            for (int i = 0; i < numberOfConcurrentCalls; i++)
            {
                tasks.Add(Task.Run(() => _controller.List()));
            }

            Task.WaitAll(tasks.ToArray());

            // Assert
            Assert.All(tasks, task =>
            {
                Assert.NotNull(task.Result);
                Assert.IsType<ViewResult>(task.Result);
            });
        }

        [Fact]
        public void Controller_ShouldHandleRepeatedInstantiation()
        {
            // Arrange & Act
            var controllers = new List<SolutionHistoryController>();
            for (int i = 0; i < 50; i++)
            {
                controllers.Add(new SolutionHistoryController());
            }

            // Assert
            Assert.All(controllers, controller =>
            {
                Assert.NotNull(controller);
                Assert.IsType<SolutionHistoryController>(controller);
            });

            // Cleanup
            controllers.ForEach(c => c.Dispose());
        }

        [Fact]
        public void List_ShouldBeIdempotent()
        {
            // Act
            var result1 = _controller.List() as ViewResult;
            var result2 = _controller.List() as ViewResult;
            var result3 = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.NotNull(result3);

            // All results should have the same characteristics
            Assert.Equal(result1.Model, result2.Model);
            Assert.Equal(result2.Model, result3.Model);
            Assert.Equal(result1.ViewName, result2.ViewName);
            Assert.Equal(result2.ViewName, result3.ViewName);
        }

        // ===== METADATA AND REFLECTION TESTS =====

        [Fact]
        public void Controller_ShouldHaveCorrectMethodCount()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var publicMethods = controllerType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.DeclaredOnly);

            // Assert
            Assert.Single(publicMethods); // Only List method should be declared
            Assert.Equal("List", publicMethods[0].Name);
        }

        [Fact]
        public void Controller_ShouldNotHaveCustomAttributes()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var customAttributes = controllerType.GetCustomAttributes(false)
                .Where(attr => attr.GetType() != typeof(AreaAttribute))
                .ToList();

            // Assert
            Assert.Empty(customAttributes);
        }

        [Fact]
        public void List_ShouldNotHaveCustomAttributes()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);
            var method = controllerType.GetMethod("List");
            var customAttributes = method.GetCustomAttributes(false);

            // Assert
            Assert.Empty(customAttributes);
        }

        [Fact]
        public void Controller_ShouldHaveCorrectAccessModifiers()
        {
            // Arrange
            var controllerType = typeof(SolutionHistoryController);

            // Assert
            Assert.True(controllerType.IsPublic);
            Assert.False(controllerType.IsSealed);
            Assert.False(controllerType.IsAbstract);
            Assert.False(controllerType.IsInterface);
        }

        // ===== ADVANCED SCENARIO TESTS =====

        [Fact]
        public void Controller_ShouldWorkWithDifferentHttpMethods()
        {
            // Arrange
            var controller = new SolutionHistoryController();
            var context = new ControllerContextMocks().Default();

            // Test with different HTTP methods
            context.HttpContext.Request.Method = "GET";
            controller.ControllerContext = context;

            // Act & Assert
            var result = controller.List();
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void Controller_ShouldMaintainStateAcrossMultipleCalls()
        {
            // Arrange
            _controller.Message = "Initial Message";

            // Act
            var result1 = _controller.List();
            _controller.Message = "Updated Message";
            var result2 = _controller.List();

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.Equal("Updated Message", _controller.Message);
        }

        [Fact]
        public void List_ShouldNotAffectHttpContext()
        {
            // Arrange
            var originalStatusCode = _controller.Response.StatusCode;
            var originalHeaders = _controller.Response.Headers.Count;

            // Act
            _controller.List();

            // Assert
            Assert.Equal(originalStatusCode, _controller.Response.StatusCode);
            Assert.Equal(originalHeaders, _controller.Response.Headers.Count);
        }

        [Fact]
        public void Controller_ShouldSupportCustomControllerContext()
        {
            // Arrange
            var controller = new SolutionHistoryController();
            var customContext = new ControllerContextMocks().Default();
            controller.ControllerContext = customContext;

            // Act
            var result = controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void List_ShouldWorkWithEmptyModelState()
        {
            // Arrange
            _controller.ModelState.Clear();

            // Act
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            Assert.True(_controller.ModelState.IsValid);
        }

        [Fact]
        public void List_ShouldWorkWithInvalidModelState()
        {
            // Arrange
            _controller.ModelState.AddModelError("TestKey", "Test Error");

            // Act
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
            Assert.False(_controller.ModelState.IsValid);
        }

        [Fact]
        public void Controller_ShouldHandleNullTempData()
        {
            // Arrange
            var controller = new SolutionHistoryController();
            // TempData is null by default

            // Act & Assert
            var exception = Record.Exception(() => controller.List());
            Assert.Null(exception);
        }

        [Fact]
        public void Controller_ShouldSupportViewDataDictionary()
        {
            // Act
            _controller.ViewData["TestKey"] = "TestValue";
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("TestValue", _controller.ViewData["TestKey"]);
        }

        [Fact]
        public void Controller_ShouldSupportViewBagDynamicProperties()
        {
            // Act
            _controller.ViewBag.TestProperty = "TestValue";
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("TestValue", _controller.ViewBag.TestProperty);
        }

        // ===== PERFORMANCE BENCHMARKING TESTS =====

        [Fact]
        public void List_ShouldExecuteWithinPerformanceThreshold()
        {
            // Arrange
            var iterations = 1000;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            // Act
            for (int i = 0; i < iterations; i++)
            {
                _controller.List();
            }

            // Assert
            stopwatch.Stop();
            var averageTime = stopwatch.ElapsedMilliseconds / (double)iterations;
            Assert.True(averageTime < 1.0, $"Average execution time {averageTime}ms should be less than 1ms");
        }

        [Fact]
        public void Controller_ShouldHaveMinimalMemoryFootprint()
        {
            // Arrange
            var initialMemory = GC.GetTotalMemory(true);
            var controllers = new List<SolutionHistoryController>();

            // Act
            for (int i = 0; i < 100; i++)
            {
                controllers.Add(new SolutionHistoryController());
            }

            var afterCreationMemory = GC.GetTotalMemory(false);

            // Cleanup
            controllers.Clear();
            GC.Collect();
            GC.WaitForPendingFinalizers();
            var afterCleanupMemory = GC.GetTotalMemory(true);

            // Assert
            var memoryIncrease = afterCreationMemory - initialMemory;
            var memoryRecovered = afterCreationMemory - afterCleanupMemory;

            Assert.True(memoryIncrease > 0, "Memory should increase when creating controllers");
            Assert.True(memoryRecovered > 0, "Memory should be recovered after cleanup");
        }

        // ===== EDGE CASE TESTS =====

        [Fact]
        public void Controller_ShouldHandleExtremelyLongRunningOperations()
        {
            // Arrange
            var cancellationTokenSource = new CancellationTokenSource();
            cancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(5)); // Safety timeout

            // Act
            var task = Task.Run(() =>
            {
                var results = new List<IActionResult>();
                while (!cancellationTokenSource.Token.IsCancellationRequested)
                {
                    results.Add(_controller.List());
                    if (results.Count >= 10000) break; // Prevent infinite loop
                }
                return results;
            }, cancellationTokenSource.Token);

            // Assert
            Assert.True(task.Wait(TimeSpan.FromSeconds(10)));
            Assert.NotNull(task.Result);
            Assert.True(task.Result.Count > 0);
        }

        [Fact]
        public void List_ShouldBeReentrant()
        {
            // Arrange
            var results = new ConcurrentBag<IActionResult>();
            var tasks = new List<Task>();

            // Act
            for (int i = 0; i < 50; i++)
            {
                tasks.Add(Task.Run(() =>
                {
                    for (int j = 0; j < 10; j++)
                    {
                        results.Add(_controller.List());
                    }
                }));
            }

            Task.WaitAll(tasks.ToArray());

            // Assert
            Assert.Equal(500, results.Count);
            Assert.All(results, result => Assert.IsType<ViewResult>(result));
        }

        [Fact]
        public void Controller_ShouldWorkAfterMultipleDisposeAndRecreate()
        {
            // Act & Assert
            for (int i = 0; i < 10; i++)
            {
                var controller = new SolutionHistoryController();
                var result = controller.List();
                Assert.NotNull(result);
                Assert.IsType<ViewResult>(result);
                controller.Dispose();
            }
        }
    }
}
