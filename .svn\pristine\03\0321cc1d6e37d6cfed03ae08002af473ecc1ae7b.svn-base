﻿using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.MYSQLMonitorStatus.Queries;
public class GetMYSQLMonitorStatusDetailByTypeQueryHandlerTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IMysqlMonitorStatusRepository> _repositoryMock;
    private readonly GetMYSQLMonitorStatusDetailByTypeQueryHandler _handler;

    public GetMYSQLMonitorStatusDetailByTypeQueryHandlerTests()
    {
        var data = new List<Domain.Entities.MYSQLMonitorStatus>();
        var datas = new Domain.Entities.MYSQLMonitorStatus
        {
            ReferenceId = "ref-001",
            WorkflowName = "Test message",
            IsActive = true
        };
        _mapperMock = new Mock<IMapper>();
        _repositoryMock = new Mock<IMysqlMonitorStatusRepository>();
        _repositoryMock = MYSQLMonitorStatusRepositoryMocks.GetMYSQLMonitorStatusDetailByTypeRepository(data);
        _handler = new GetMYSQLMonitorStatusDetailByTypeQueryHandler(_repositoryMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedList_WhenDataExists()
    {
        // Arrange
        var type = "MySQL";
        var dbList = new List<Domain.Entities.MYSQLMonitorStatus>
            {
                new Domain.Entities.MYSQLMonitorStatus
                {
                    ReferenceId = "123", Type = type, InfraObjectId = "INFRA1", InfraObjectName = "NodeA"
                }
            };

        var vmList = new List<MYSQLMonitorStatusDetailByTypeVm>
            {
                new MYSQLMonitorStatusDetailByTypeVm
                {
                    Id = "123", Type = type, InfraObjectId = "INFRA1", InfraObjectName = "NodeA"
                }
            };

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(dbList);
        _mapperMock.Setup(m => m.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(dbList)).Returns(vmList);

        var request = new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be("123");

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(dbList), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsEmpty()
    {
        // Arrange
        var type = "MySQL";
        var dbList = new List<Domain.Entities.MYSQLMonitorStatus>();

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(dbList);

        var request = new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(It.IsAny<List<Domain.Entities.MYSQLMonitorStatus>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsNull()
    {
        // Arrange
        var type = "MySQL";

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync((List<Domain.Entities.MYSQLMonitorStatus>)null!);

        var request = new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(It.IsAny<List<Domain.Entities.MYSQLMonitorStatus>>()), Times.Never);
    }
    [Fact]
    public async Task Handle_ShouldThrowArgumentNullException_WhenRequestIsNull()
    {
        // Act
        Func<Task> act = async () => await _handler.Handle(null!, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NullReferenceException>();
    }
    [Fact]
    public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
    {
        // Arrange
        var type = "MySQL";
        var request = new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type };

        _repositoryMock.Setup(r => r.GetDetailByType(type))
            .ThrowsAsync(new InvalidOperationException("DB Failure"));

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<InvalidOperationException>()
            .WithMessage("DB Failure");

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldThrowException_WhenMapperReturnsNull()
    {
        // Arrange
        var type = "MySQL";
        var dbList = new List<Domain.Entities.MYSQLMonitorStatus>
        {
            new Domain.Entities.MYSQLMonitorStatus { ReferenceId = "001", Type = type }
        };

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(dbList);
        _mapperMock.Setup(m => m.Map<List<MYSQLMonitorStatusDetailByTypeVm>>(dbList)).Returns((List<MYSQLMonitorStatusDetailByTypeVm>)null!);

        var request = new GetMYSQLMonitorStatusDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().BeNull();
    }

}