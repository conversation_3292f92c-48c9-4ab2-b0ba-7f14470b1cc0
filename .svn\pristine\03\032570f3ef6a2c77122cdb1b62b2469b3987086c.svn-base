﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Import;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Lock;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetWorkflowActionByNodeId;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using Microsoft.AspNetCore.Mvc;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowActionService
{
    Task<List<WorkflowActionNameVm>> GetWorkflowActionNames();
    Task<List<WorkflowActionListVm>> GetWorkflowActionList();
    Task<BaseResponse> CreateAsync(CreateWorkflowActionCommand createWorkflowActionCommand);
    Task<BaseResponse> SaveAsWorkflowAction(SaveAsWorkflowActionCommand saveAsWorkflowActionCommand);
    Task<BaseResponse> UpdateAsync(UpdateWorkflowActionCommand updateWorkflowActionCommand);
    Task<BaseResponse> DeleteAsync(string workflowActionId);
    Task<WorkflowActionDetailVm> GetByReferenceId(string id);
    Task<bool> IsWorkflowActionNameExist(string name, string id);
    Task<List<GetWorkflowActionByNodeIdVm>> GetWorkflowActionByNodeId(string nodeId);
    Task<PaginatedResult<WorkflowActionListVm>> GetPaginatedWorkflowAction(GetWorkflowActionPaginatedListQuery query);
    Task<BaseResponse> UpdateWorkflowActionLock(UpdateWorkflowActionLockCommand workflowActionLock);
    Task<BaseResponse> ImportWorkflowActionAsync(ImportWorkflowActionCommand importWorkflowActionCommand);
    Task<WorkflowActionDetailVm> GetWorkflowActionByName(string name);
}