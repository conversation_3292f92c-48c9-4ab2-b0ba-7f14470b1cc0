﻿using ContinuityPatrol.Application.Features.UserRole.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Validators;

public class UpdateUserRoleValidatorTests
{
    private readonly Mock<IUserRoleRepository> _mockUserRoleRepository;
    private UpdateUserRoleCommandValidator validator;
	public List<Domain.Entities.UserRole> UserRoles { get; set; }

    public UpdateUserRoleValidatorTests()
    {
        UserRoles = new Fixture().Create<List<Domain.Entities.UserRole>>();

        _mockUserRoleRepository = UserRoleRepositoryMocks.UpdateUserRoleRepository(UserRoles);
    }

    //Role

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Role_With_Empty(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Role_With_IsNull(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = null;
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Role_With_MinimumRange(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "DB";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Role_With_MaximumRange(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLKABCDEFGHIJKLMNOPQRSTUVWXYZ_ZYXWVUTSRQPONMLK";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Please enter a valid RGB color value.");
		
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "  CTS  ";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_DoubleSpace_InFront(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "  CTS";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_DoubleSpace_InBack(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "CTS  ";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_TripleSpace_InBetween(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "CTS   India";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_SpecialCharacters_InFront(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "%$#%CTS";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_SpecialCharacters_InBack(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "CTS@$@$";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_SpecialCharacters_InBetween(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "CTS@$@$&^India";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_SpecialCharacters_Only(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "^%$#@#$";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_UnderScore_InFront(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "_CTS";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_UnderScore_InBack(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "CTS_";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_UnderScore_InFront_AndBack(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "_CTS_";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_Numbers_InFront(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "987CTS";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "_453CTS_";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoUserRoleData]
    public async Task Verify_UpdateUserRoleCommandValidator_Valid_Role_With_With_Numbers_Only(UpdateUserRoleCommand updateUserRoleCommand)
    {
        validator = new UpdateUserRoleCommandValidator(_mockUserRoleRepository.Object);

        updateUserRoleCommand.Role = "87486512564";
        updateUserRoleCommand.Id = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateUserRoleCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.UserRole.UserRoleValidRequired, validateResult.Errors[0].ErrorMessage);
    }
}