﻿namespace ContinuityPatrol.Infrastructure.Contract;

public interface IJobScheduler
{
    //Task ScheduleJob<T>(string jobId, Dictionary<string, string> dynamicData) where T : IJob;
    Task ScheduleJob(string jobId, Dictionary<string, string> dynamicData);
    Task StartAsync();
    Task StopAsync();
    Task DeleteAsync(string jobId);
    Task ScheduleSeqServiceJob(string jobId, Dictionary<string, string> dynamicData);
    Task DeleteSeqJobs(List<string> jobId);
    Task DeleteAllSeqJobs();
    Task ScheduleDeleteJobAndSeqLog(string jobId, Dictionary<string, object> dynamicData);
    Task DeleteSeqJobs(string jobId);
}