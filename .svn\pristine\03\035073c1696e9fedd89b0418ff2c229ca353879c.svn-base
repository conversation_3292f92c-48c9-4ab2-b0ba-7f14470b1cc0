﻿using ContinuityPatrol.Application.Features.LogViewer.Events.Update;
using FluentAssertions;

namespace ContinuityPatrol.Application.UnitTests.Features.LogViewer.Events
{
    public class UpdateLogViewerEventHandlerTests
    {
        private readonly Mock<ILogger<LogViewerUpdatedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly LogViewerUpdatedEventHandler _handler;

        public UpdateLogViewerEventHandlerTests()
        {
            _loggerMock = new Mock<ILogger<LogViewerUpdatedEventHandler>>();
            _userActivityRepoMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _userServiceMock.SetupGet(u => u.UserId).Returns("User123");
            _userServiceMock.SetupGet(u => u.LoginName).Returns("TestUser");
            _userServiceMock.SetupGet(u => u.CompanyId).Returns("Comp001");
            _userServiceMock.SetupGet(u => u.RequestedUrl).Returns("/logviewer/update");
            _userServiceMock.SetupGet(u => u.IpAddress).Returns("127.0.0.1");

            _handler = new LogViewerUpdatedEventHandler(_loggerMock.Object, _userActivityRepoMock.Object,
                _userServiceMock.Object);
        }

        [Fact]
        public async Task Handle_Should_Log_And_Save_UserActivity()
        {
            // Arrange
            var updatedEvent = new LogViewerUpdatedEvent { Name = "UpdatedLog" };
            Domain.Entities.UserActivity captured = null;

            _userActivityRepoMock
                .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => captured = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(updatedEvent, CancellationToken.None);

            // Assert
            captured.Should().NotBeNull();
            captured.UserId.Should().Be("User123");
            captured.LoginName.Should().Be("TestUser");
            captured.CompanyId.Should().Be("Comp001");
            captured.RequestUrl.Should().Be("/logviewer/update");
            captured.HostAddress.Should().Be("127.0.0.1");
            captured.Entity.Should().Be("LogViewer");
            captured.Action.Should().Be("Update LogViewer");
            captured.ActivityType.Should().Be("Update");
            captured.ActivityDetails.Should().Be(" LogViewer 'UpdatedLog' updated successfully.");

            _userActivityRepoMock.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);

            _loggerMock.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, _) => v.ToString().Contains("LogViewer 'UpdatedLog' updated successfully")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task Handle_Should_NotThrow_When_OptionalUserInfo_Null()
        {
            // Arrange
            var evt = new LogViewerUpdatedEvent { Name = "LogX" };

            _userServiceMock.SetupGet(u => u.UserId).Returns((string)null);
            _userServiceMock.SetupGet(u => u.LoginName).Returns((string)null);
            _userServiceMock.SetupGet(u => u.RequestedUrl).Returns((string)null);
            _userServiceMock.SetupGet(u => u.IpAddress).Returns((string)null);
            _userServiceMock.SetupGet(u => u.CompanyId).Returns((string)null);

            _userActivityRepoMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ReturnsAsync(new Domain.Entities.UserActivity());

            // Act & Assert
            var exception = await Record.ExceptionAsync(() => _handler.Handle(evt, CancellationToken.None));
            exception.Should().BeNull();
        }

        [Fact]
        public async Task Handle_Should_Work_If_Name_Is_Empty()
        {
            // Arrange
            var @event = new LogViewerUpdatedEvent { Name = string.Empty };

            var loggerMock = new Mock<ILogger<LogViewerUpdatedEventHandler>>();
            var repoMock = new Mock<IUserActivityRepository>();
            var userServiceMock = new Mock<ILoggedInUserService>();

            userServiceMock.Setup(x => x.UserId).Returns("U002");
            userServiceMock.Setup(x => x.LoginName).Returns("EmptyUser");
            userServiceMock.Setup(x => x.CompanyId).Returns("Comp02");
            userServiceMock.Setup(x => x.RequestedUrl).Returns("/logviewer/update");
            userServiceMock.Setup(x => x.IpAddress).Returns("***********");

            repoMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ReturnsAsync(new Domain.Entities.UserActivity());

            var handler = new LogViewerUpdatedEventHandler(loggerMock.Object, repoMock.Object, userServiceMock.Object);

            // Act
            var exception = await Record.ExceptionAsync(() => handler.Handle(@event, CancellationToken.None));

            // Assert
            Assert.Null(exception);
            repoMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

    }
}

