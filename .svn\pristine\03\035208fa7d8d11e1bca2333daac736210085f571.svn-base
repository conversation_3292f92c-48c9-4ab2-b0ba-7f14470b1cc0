﻿using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
using System.Drawing;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
using Newtonsoft.Json;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate;
   
    [SupportedOSPlatform("windows")]
    public partial class BusinessServiceOverviewReport: DevExpress.XtraReports.UI.XtraReport
    {
      public static List<BusinessViewPaginatedList> dashboardBusinessOverviewDetails = new List<BusinessViewPaginatedList>();

      public string username;
      private readonly ILogger<ServiceAvailabilityController> _logger;
      bool IsNotNullOrEmpty(string value) => !string.IsNullOrEmpty(value);
    public BusinessServiceOverviewReport(string data,string reportGeneratedName)
    {
        try
            {
            _logger = ServiceAvailabilityController._logger;
            username = reportGeneratedName;
             dashboardBusinessOverviewDetails = JsonConvert.DeserializeObject<List<BusinessViewPaginatedList>>(data);
            var businessViewReport = dashboardBusinessOverviewDetails;
                businessViewReport.ForEach(x =>
                {
                    if (new[] { x?.CurrentRPO, x?.RPOThreshold, x?.ConfiguredRPO }.All(IsNotNullOrEmpty))
                    {
                        var parts = x.CurrentRPO.Replace("+","").Split(':');
                        var part = parts[0].Split('.',' ');
                        var Mins = part.Length > 1 ? $"{int.Parse(part[0]) * 60 + int.Parse(parts[1])}" : part[0];
                        x.CurrentRPO = x.CurrentRPO == "00:00:00" || x.CurrentRPO == "-" ? "0 Mins" : x.CurrentRPO;
                        if (x.CurrentRPO.Length > 0 && x.CurrentRPO != "-")
                        {
                            if (x.CurrentRPO == "0 Mins")
                            {
                                x.CurrentRPO = "0 Mins";
                            }
                            else
                            {
                               // x.CurrentRPO = Mins == "00" && parts[1] == "00" ? $"{int.Parse(parts[2].Split(".")[0])} sec" : Mins == "00" ? $"{int.Parse(parts[1])} mins" : $"{int.Parse(Mins) * 60 + int.Parse(parts[1])} mins";
                                x.CurrentRPO = Mins == "00" && parts[1] == "00" ? $"{int.Parse(parts[2].Split(".")[0])} sec" : Mins == "00" ? $"{int.Parse(parts[1])} mins" : Mins == "N/A" || parts[1] == "N/A" ? "N/A mins" : $"{int.Parse(Mins) * 60 + int.Parse(parts[1])} mins";

                            }
                        }
                        
                        x.Percentage = $"({Math.Round((double.Parse(x.RPOThreshold) * 100) / double.Parse(x.ConfiguredRPO))}%)";
                    }
                });
                businessViewReport.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x.CurrentRTO))
                    {
                        var parts = x.CurrentRTO.Replace("+", "").Split(':');
                        var part = parts[0].Split('.',' ');
                        var Mins = part.Length > 1 ? $"{int.Parse(part[0]) * 60 + int.Parse(parts[1])}" : part[0];
                        x.CurrentRTO = x.CurrentRTO == "00:00:00" || x.CurrentRTO == "-" ? "-" : x.CurrentRTO;
                        if (x.CurrentRTO.Length > 0 && x.CurrentRTO != "-")
                        {
                            if (x.CurrentRTO == "N/A")
                            {
                                x.CurrentRTO = "N/A";
                            }
                            else
                            {
                                // x.CurrentRTO = Mins == "00" && parts[1] == "00" ? $"{int.Parse(parts[2].Split(".")[0])} sec" : Mins == "00" ? $"{int.Parse(parts[1])} mins" : $"{int.Parse(Mins) * 60 + int.Parse(parts[1])} mins";
                                x.CurrentRTO = Mins == "00" && parts[1] == "00" ? (int.Parse(parts[2].Split(".")[0]) == 0 ? "NaN sec" : $"{int.Parse(parts[2].Split(".")[0])} sec") : Mins == "00" ? $"{int.Parse(parts[1])} mins" : $"{int.Parse(Mins) * 60 + int.Parse(parts[1])} mins";
                            }

                        }
                    }
                });
                InitializeComponent();
                ClientCompanyLogo();
                this.DataSource = businessViewReport;
                tableCell8.BeforePrint += tableCell_SerialNumber_BeforePrint;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Dashboard operationalservice summary overview Report. The error message : " + ex.Message); throw; }
        }
        private void xrPageInfo1_BeforePrint(object sender, CancelEventArgs e)
        {

        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;
            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try { 
            _username.Text = "Report Generated By: " + username.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Dashboard operationalservice summary overview Report's User name. The error message : " + ex.Message); throw; }
        }

        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try { 
            var builder = new ConfigurationBuilder()
                 .SetBasePath(Directory.GetCurrentDirectory())
                 .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

            IConfigurationRoot configuration = builder.Build();
            var version = configuration["CP:Version"];
            xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the Dashboard operationalservice summary overview Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in Dashboard operationalservice summary report" + ex.Message.ToString());
            }
        }
    }

