using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Commands.UpdateStatus;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberAirGapStatusService : BaseClient, ICyberAirGapStatusService
{
    public CyberAirGapStatusService(IConfiguration config, IAppCache cache, ILogger<CyberAirGapStatusService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<CyberAirGapStatusListVm>> GetCyberAirGapStatusList()
    {
        var request = new RestRequest("api/v6/cyberairgapstatuss");

        return await GetFromCache<List<CyberAirGapStatusListVm>>(request, "GetCyberAirGapStatusList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberAirGapStatusCommand createCyberAirGapStatusCommand)
    {
        var request = new RestRequest("api/v6/cyberairgapstatuss", Method.Post);

        request.AddJsonBody(createCyberAirGapStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberAirGapStatusCommand updateCyberAirGapStatusCommand)
    {
        var request = new RestRequest("api/v6/cyberairgapstatuss", Method.Put);

        request.AddJsonBody(updateCyberAirGapStatusCommand);

        return await Put<BaseResponse>(request);
    } 
    public async Task<BaseResponse> UpdateStatusAsync(UpdateAirGapStatusCommand  updateAirGapStatusCommand)
    {
        var request = new RestRequest("api/v6/cyberairgapstatuss/updatestatus", Method.Put);

        request.AddJsonBody(updateAirGapStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cyberairgapstatuss/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<CyberAirGapStatusDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cyberairgapstatuss/{id}");

        return await Get<CyberAirGapStatusDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsCyberAirGapStatusNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/cyberairgapstatuss/name-exist?cyberairgapstatusName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<CyberAirGapStatusListVm>> GetPaginatedCyberAirGapStatuss(GetCyberAirGapStatusPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/cyberairgapstatuss/paginated-list");

      return await Get<PaginatedResult<CyberAirGapStatusListVm>>(request);
  }
   #endregion
}
