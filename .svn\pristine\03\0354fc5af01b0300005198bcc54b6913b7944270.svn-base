﻿using ContinuityPatrol.Application.Features.Site.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SiteModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Site.Queries;

public class GetSiteListQueryHandlerTests : IClassFixture<SiteFixture>
{
    private readonly SiteFixture _siteFixture;

    private Mock<ISiteRepository> _mockSiteRepository;

    private readonly GetSiteListQueryHandler _handler;

    public GetSiteListQueryHandlerTests(SiteFixture siteFixture)
    {
        _siteFixture = siteFixture;

        _mockSiteRepository = SiteRepositoryMocks.GetSiteRepository(_siteFixture.Sites);

        _handler = new GetSiteListQueryHandler(_siteFixture.Mapper, _mockSiteRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_SitesCount()
    {
        var result = await _handler.Handle(new GetSiteListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<SiteListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_SitesList()
    {
        var result = await _handler.Handle(new GetSiteListQuery(), CancellationToken.None);
        result.ShouldBeOfType<List<SiteListVm>>();
        result[0].Id.ShouldBe(_siteFixture.Sites[0].ReferenceId);
        result[0].Name.ShouldBe(_siteFixture.Sites[0].Name);
        result[0].CompanyId.ShouldBe(_siteFixture.Sites[0].CompanyId);
        result[0].CompanyName.ShouldBe(_siteFixture.Sites[0].CompanyName);
        result[0].Location.ShouldBe(_siteFixture.Sites[0].Location);
        result[0].Type.ShouldBe(_siteFixture.Sites[0].Type);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockSiteRepository = SiteRepositoryMocks.GetSiteEmptyRepository();

        var handler = new GetSiteListQueryHandler(_siteFixture.Mapper, _mockSiteRepository.Object);

        var result = await handler.Handle(new GetSiteListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSiteListQuery(), CancellationToken.None);

        _mockSiteRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}