using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Delete;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetList;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class AdPasswordExpireService : BaseService,IAdPasswordExpireService
{
    public AdPasswordExpireService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<AdPasswordExpireListVm>> GetAdPasswordExpireList()
    {
        Logger.LogInformation("Get All AdPasswordExpires");

        return await Mediator.Send(new GetAdPasswordExpireListQuery());
    }

    public async Task<AdPasswordExpireDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordExpire Id");

        Logger.LogInformation($"Get AdPasswordExpire Detail by Id '{id}'");

        return await Mediator.Send(new GetAdPasswordExpireDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateAdPasswordExpireCommand createAdPasswordExpireCommand)
    {
        Logger.LogInformation($"Create AdPasswordExpire '{createAdPasswordExpireCommand}'");

        return await Mediator.Send(createAdPasswordExpireCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateAdPasswordExpireCommand updateAdPasswordExpireCommand)
    {
        Logger.LogInformation($"Update AdPasswordExpire '{updateAdPasswordExpireCommand}'");

        return await Mediator.Send(updateAdPasswordExpireCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AdPasswordExpire Id");

        Logger.LogInformation($"Delete AdPasswordExpire Details by Id '{id}'");

        return await Mediator.Send(new DeleteAdPasswordExpireCommand { Id = id });
    }
     #region NameExist
 public async Task<bool> IsAdPasswordExpireNameExist(string name, string? id)
 {
     Guard.Against.NullOrWhiteSpace(name, "AdPasswordExpire Name");

     Logger.LogInformation($"Check Name Exists Detail by AdPasswordExpire Name '{name}' and Id '{id}'");

     return await Mediator.Send(new GetAdPasswordExpireNameUniqueQuery { Name = name, Id = id });
 }
    #endregion

     #region Paginated
public async Task<PaginatedResult<AdPasswordExpireListVm>> GetPaginatedAdPasswordExpires(GetAdPasswordExpirePaginatedListQuery query)
{
    Logger.LogInformation("Get Searching Details in AdPasswordExpire Paginated List");

    return await Mediator.Send(query);
}
     #endregion
}
