﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopyJob.Queries;

public class GetRoboCopyJobDetailQueryHandlerTests
{
    private readonly Mock<IRoboCopyJobRepository> _mockRoboCopyJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetRoboCopyJobDetailQueryHandler _handler;

    public GetRoboCopyJobDetailQueryHandlerTests()
    {
        _mockRoboCopyJobRepository = new Mock<IRoboCopyJobRepository>();
        _mockMapper = new Mock<IMapper>();

        _handler = new GetRoboCopyJobDetailQueryHandler(
            _mockRoboCopyJobRepository.Object,
            _mockMapper.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldReturnRoboCopyJobDetail_WhenRoboCopyJobExists()
    {
        var query = new GetRoboCopyJobDetailQuery { Id = Guid.NewGuid().ToString() };
        var roboCopyJob = new Domain.Entities.RoboCopyJob
        {
            Id = 1,
            ReplicationName = "TestReplication"
        };

        var expectedResult = new RoboCopyJobDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            ReplicationName = roboCopyJob.ReplicationName
        };

        _mockRoboCopyJobRepository
            .Setup(repo => repo.GetByReferenceIdAsync(query.Id))
            .ReturnsAsync(roboCopyJob);

        _mockMapper.Setup(mapper => mapper.Map<RoboCopyJobDetailVm>(roboCopyJob))
            .Returns(expectedResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(expectedResult.Id, result.Id);
        Assert.Equal(expectedResult.ReplicationName, result.ReplicationName);

        _mockRoboCopyJobRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);

        _mockMapper.Verify(mapper => mapper.Map<RoboCopyJobDetailVm>(roboCopyJob), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenRoboCopyJobNotFound()
    {
        var query = new GetRoboCopyJobDetailQuery { Id = Guid.NewGuid().ToString() };

        _mockRoboCopyJobRepository
            .Setup(repo => repo.GetByReferenceIdAsync(query.Id))
            .ReturnsAsync((Domain.Entities.RoboCopyJob)null);

        var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
            _handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldContain("RoboCopyJob");
    }
}
