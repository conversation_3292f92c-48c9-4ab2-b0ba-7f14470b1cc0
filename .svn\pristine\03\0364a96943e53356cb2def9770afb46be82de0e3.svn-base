﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class TemplateRepositoryMocks
{
    public static Mock<ITemplateRepository> CreateTemplateRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();
        templateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(templates);
        templateRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => templates.SingleOrDefault(x => x.ReferenceId == i));
        templateRepository.Setup(repo => repo.AddAsync(It.IsAny<Template>())).ReturnsAsync(
            (Template template) =>
            {
                template.Id = new Fixture().Create<int>();
                template.ReferenceId = new Fixture().Create<Guid>().ToString();
                templates.Add(template);
                return template;

            });

        return templateRepository;
    }

    public static Mock<ITemplateRepository> UpdateTemplateRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();

        templateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(templates);

        templateRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => templates.SingleOrDefault(x => x.ReferenceId == i));

        templateRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Template>())).ReturnsAsync((Template template) =>
        {
            var index = templates.FindIndex(item => item.ReferenceId == template.ReferenceId);
            templates[index] = template;
            return template;
        });

        return templateRepository;
    }

    public static Mock<ITemplateRepository> DeleteTemplateRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();
        templateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(templates);

        templateRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => templates.SingleOrDefault(x => x.ReferenceId == i));

        templateRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Template>())).ReturnsAsync((Template template) =>
        {
            var index = templates.FindIndex(item => item.ReferenceId == template.ReferenceId);
            template.IsActive = false;
            templates[index] = template;

            return template;
        });

        return templateRepository;
    }

    public static Mock<ITemplateRepository> GetTemplateRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();

        templateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(templates);

        templateRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => templates.SingleOrDefault(x => x.ReferenceId == i));

        return templateRepository;
    }

    public static Mock<ITemplateRepository> GetTemplateEmptyRepository()
    {
        var templateRepository = new Mock<ITemplateRepository>();

        templateRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Template>());

        return templateRepository;
    }

    public static Mock<ITemplateRepository> GetTemplateNamesRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();

        templateRepository.Setup(repo => repo.GetTemplateNames()).ReturnsAsync(templates);

        return templateRepository;
    }

    public static Mock<ITemplateRepository> GetPaginatedTemplateRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();

        var queryableNode = templates.BuildMock();

        templateRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableNode);

        return templateRepository;
    }

    public static Mock<ITemplateRepository> GetTemplateNameUniqueRepository(List<Template> templates)
    {
        var templateRepository = new Mock<ITemplateRepository>();

        templateRepository.Setup(repo => repo.IsTemplateNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => templates.Exists(x => x.Name == i && x.ReferenceId == j));

        return templateRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateTemplateEventRepository(List<UserActivity> userActivities)
    {
        var templateEventRepository = new Mock<IUserActivityRepository>();

        templateEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync((UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return templateEventRepository;
    }
}
