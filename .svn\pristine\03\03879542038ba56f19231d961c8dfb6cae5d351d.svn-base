﻿let globalBusinessServiceData = null;
let globalMapData = [];
let globalMapDataLatLong = [];
let globalinfraData = [];
let impactImage = '<img src="../../img/isomatric/No-Data-Found/Solution_Diagram_No_Data_Found.svg" class="Card_NoData_Img" style="margin-left:75px;margin-top:30px">';
let globalbusinessServiceId = "";

(async () => {
    await overallBusinessData();
    await overallImpactList();
    await GetServiceTopology(globalbusinessServiceId);
    await businessservicewiseImpactDetails(globalbusinessServiceId);
})();

$("#search-inp-businessCategory").on('keyup',function () {
    
    var filter = $(this).val();
    $(".businessDataItem").each(function () {

        var $i = 0;
        $(this).find(".businessDataName").each(function () {

            var splitText = $(this).text()
            if (splitText.search(new RegExp(filter, "i")) >= 0) {
                $i++;
            }
        });
        if ($i > 0) {
            $(this).closest(".businessDataItem").show();
        } else {
            $(this).closest(".businessDataItem").hide();
        }
    });
})



$(".impactDetails").on('click',function (e) {
    let TypeServer = e.target.getAttribute("type")
    let dcIcon = TypeServer.toLowerCase() == 'application' ? "cp-application" : TypeServer.toLowerCase() == 'database' ? "cp-database" : TypeServer.toLowerCase() == 'storage' ? "cp-storage" : TypeServer.toLowerCase() == 'replication' ? "cp-replication-on" : TypeServer.toLowerCase() == 'network' ? "cp-network" : "cp-server"
    $(".page_titleheatmap").text(TypeServer + " Heat Map Details")
    $.ajax({
        url: "/Dashboard/ServiceAvailability/GetImpactDetailByHeatMapStatusType",
        data: {
            businessServiceId: globalbusinessServiceId,
            HeatmapType: TypeServer,
            __RequestVerificationToken:gettoken()
        },
        dataType: "json",
        traditional: true,
        type: 'POST',
        success: function (data) {
            
            $("#HeatmapTable").empty()
            let html = '<thead>' 
              html+=  '<tr>' 
            html +=  '<th>Business&nbsp;Service</th>'
            html += '<th>InfraObjects</th>'
            html += '<th>Component Name</th>'
            html += '<th>Type</th>'
            TypeServer.toLowerCase() == 'application' ? html += '' : TypeServer.toLowerCase() == 'database' ? html += '<th>Database Name</th>' : TypeServer.toLowerCase() == 'storage' ? html += '' : TypeServer.toLowerCase() == 'replication' ? html += '<th>Replication Type</th>' : TypeServer.toLowerCase() == 'network' ? html += '' : html += '<th>IP Addresss/HostName</th>'
            html += '<th>Status</th>'
            html += '<th>Remarks</th>'
            html += '</tr>'
            html += '</thead>'
            html += '<tbody id="Heatmapbody">'
            data.getHeatMapStatusByType.forEach(function (data) {
                let propertices = JSON.parse(data.properties)
                let ReplicationName = ''
                
                if (TypeServer.toLowerCase() == 'replication') {
                      ReplicationName = propertices?.ReplicationName == null ? 'NA' : propertices?.ReplicationName
                }
                html += '<tr class="align-middle" name=' + data?.businessServiceName + ' >' 
                html += '<td class="align-middle d-flex align-items-center"  title="' + data?.businessServiceName + '"><i class="cp-business-service me-1"></i>' + data?.businessServiceName + '</td>' 
                html += '<td class="align-middle"  title="' + data?.infraObjectName + '">' + data?.infraObjectName + '</td>' 
                html += '<td class="align-middle" title="' + propertices?.ComponentName + '">' + propertices?.ComponentName + '</td>' 
                html += '<td class="align-middle d-flex align-items-center" title="' + propertices?.Type + '"><i class="' + dcIcon + ' me-1"> </i>' + propertices?.Type + '</td>' 
                TypeServer.toLowerCase() == 'application' ? html += '' : TypeServer.toLowerCase() == 'database' ? html += '<td class="align-middle" title="' + propertices?.DatabaseName + '">' + propertices?.DatabaseName + '</td>' : TypeServer.toLowerCase() == 'storage' ? "" : TypeServer.toLowerCase() == 'replication' ? html += '<td class="align-middle" title="' + ReplicationName + '">' + ReplicationName + '</td>' : TypeServer.toLowerCase() == 'network' ? "" : html += '<td class="align-middle" title="' + propertices?.IpAddress + '">' + propertices?.IpAddress + '</td>' 
                html+= '<td class="text-danger align-middle"  title="' + data?.heatmapStatus + '">' + data?.heatmapStatus + '</td>' 
                html += '<td class="text-truncate align-middle" title="' + data?.errorMessage + '">' + data?.errorMessage + '</td>' 
                    //'<td>' + replicationStatus + '</td>' +
                html += '</tr>';
               
            })
            html += '</tbody>';
            $("#HeatmapTable").append(html)
        }
    })
    $("#impactModalModal").modal("show")
});

async function overallBusinessData() {
    await $.ajax({
        url: "/Dashboard/ResiliencyMapping/GetBusinessServiceList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (res) {
            globalBusinessServiceData = res?.data;
            populateHTML(globalBusinessServiceData);
        },
        error: function (xhr, error, res) {
            errorNotification(res);
        }
    })
}
let topologyData = []

async function GetServiceTopology(businessServiceId) {
    topologyData = []
    let xParent = 8;
    let yParent = 15;
    let xchild = 20;
    let flagStatus = false
    await $.ajax({
        url: "/Dashboard/ResiliencyMapping/GetBusinessServiceTopology",
        dataType: 'text',
        traditional: true,
        type: 'GET',
        data: { businessServiceId: businessServiceId }, 
        success: function (res) {
            $("#DCMapping").empty()
            let data = JSON.parse(res)
            if (data?.success) { 
                if (data.data.length != 0) {
                    for (let i = 0; i < data.data.length; i++) {
                        let siteNo = i + 1
                        let drOperationStatus = data.data[i].infraObjectTopologyLists[0]?.drOperationStatus
                        let status = drOperationStatus == "2" ? 'cp-off-arrow fs-5 text-primary' : 'cp-on-arrow fs-5 text-primary'
                        topologyData = []
                        if (data.data[i].serverTopologyLists.length != 0) {
                            flagStatus = true
                            let objectData = {}
                            objectData.siteType = data.data[i].siteType
                            objectData.siteName = data.data[i].siteName
                            objectData.fixed = true
                            objectData.x = am4core.percent(xParent)
                            objectData.y = am4core.percent(yParent)
                            result = [];
                            let image = ""
                            let titttleName = ""
                            data.data[i].serverTopologyLists.forEach(function (a) {
                                if (!this[a.roleType] && !this[a.serverType]) {
                                    if (a.roleType.toLowerCase() == "application") {
                                        image = "../../img/charts_img/DataCenter/application.svg"
                                        titttleName = "Application"
                                    }
                                    else if (a.roleType.toLowerCase() == "database") {
                                        image = "../../img/charts_img/DataCenter/database.svg"
                                        titttleName = "Database"
                                    }
                                    else {
                                        image = "../../img/charts_img/DataCenter/Network.svg"
                                        titttleName = "Network"
                                    }
                                    this[a.roleType] = { roleType: a.roleType, serverType: a.serverType, image: image, titttleName: titttleName };
                                    result.push(this[a.roleType]);
                                }
                            }, Object.create(null));

                            objectData.children = []
                            for (let i = 0; i < result.length; i++) {
                                objectData.name = result[i].roleType
                                objectData.image = result[i].image
                                objectData.children.push({ name: result[i].serverType, image: result[i].image, titttleName: result[i].titttleName, x: am4core.percent(xchild), y: am4core.percent(yParent), fixed: true })
                                xchild = xchild + 15
                            }
                            topologyData.push(objectData)
                            //xParent = xParent + 4
                            yParent = yParent + 40
                            //$(".fooderData").show()
                            //$(".fooderData").addClass("d-flex")
                            if (topologyData.length != 0) {
                                for (let j = 0; j < topologyData.length; j++) {
                                    let html = ''
                                    html += '<div class="col-auto">'
                                    html += '<div class="card border mb-0">'
                                    html += '<div class="card-header p-2 pb-0 fw-semibold"><span>' + data.data[i].siteName + '</span></div>'
                                    html += '<div class="card-body p-2 d-flex align-items-center justify-content-between gap-2">'
                                    if (topologyData[j].children.length) {
                                        for (let i = 0; i < topologyData[j].children.length; i++) {
                                            html += '<div>'
                                            html += '<div class="d-flex align-items-center justify-content-between gap-2">'
                                            html += '<img src="' + topologyData[j].children[i].image + '" height="35"/>'
                                            let no = i + 1
                                            if (topologyData[j].children.length != no) {
                                                html += '<i class="cp-horizontal-dots" onclick="impactModalLabel()"></i>'
                                            }
                                            html += '</div >'
                                            html += '<span>' + topologyData[j].children[i].titttleName + '</span>'
                                            html += '</div >'
                                        }
                                    }
                                    html += '</div >'
                                    html += '</div >'
                                    html += '</div >'
                                    if (siteNo != data.data.length) {
                                        html += "<div class='col-auto'><i style='cursor:pointer;pointer-events: auto;' class='" + status + "' infraDetails='" + encodeURIComponent(JSON.stringify(data.data[i].infraObjectTopologyLists)) + "' onclick='impactModalLabel(this)'></i></div>"
                                    }
                                    html += '<div  class="col-auto">'
                                    html += '</div>'
                                    $("#DCMapping").append(html)
                                }
                            }
                            //DCMapping()
                        }
                        else {
                            let htmlnoImage = ''
                            htmlnoImage += '<div class="col-auto">'
                            htmlnoImage += '<div class="card border mb-0">'
                            htmlnoImage += '<div class="card-header p-2 pb-0 fw-semibold"><span>' + data.data[i].siteName + '</span></div>'
                            htmlnoImage += '<div class="card-body p-2 d-flex align-items-center justify-content-between gap-2">'
                            htmlnoImage += '<div>'
                            htmlnoImage += '<div class="d-grid text-center">'
                            htmlnoImage += '<img src="../../img/isomatric/no_data_found.svg" height="35" class="mx-auto"/>'
                            htmlnoImage += '<span>No Data Found</span>'
                            htmlnoImage += '</div >'
                            htmlnoImage += '</div >'
                            htmlnoImage += '</div >'
                            htmlnoImage += '</div >'
                            htmlnoImage += '</div >'
                            if (siteNo != data.data.length) {
                                htmlnoImage += "<div class='col-auto'><i style='cursor:pointer;pointer-events: auto;' class='" + status + "' infraDetails='" + encodeURIComponent(JSON.stringify(data.data[i].infraObjectTopologyLists)) + "' onclick='impactModalLabel(this)'></i></div>"
                            }
                            $("#DCMapping").append(htmlnoImage)
                        }
                    }
                }
                else {
                    $(".fooderData").removeClass("d-flex")
                    $(".fooderData").hide()
                    $("#DCMapping").append(impactImage)
                }
            }
            else {
                $(".fooderData").removeClass("d-flex")
                $(".fooderData").hide()
                $("#DCMapping").append(impactImage)
            }
        }
    })
}

function impactModalLabel(data) {
    $("#Tobologybody").empty()
    let infraData = JSON.parse(decodeURIComponent(data.getAttribute("infraDetails")))
    infraData.forEach(function (data) {
        let Status = data.drOperationStatus == 2 ? 'cp-off-arrow text-primary' : 'cp-on-arrow text-primary'
        let html = '<tr title="' + data?.infraObjectName + '" name=' + data?.infraObjectName + ' status=' + Status + ' onclick="onloadMapData(this)">' +
            '<td class="text-light" title="' + data?.infraObjectName + '"><i class="cp-book text-primary me-2"></i>' + data?.infraObjectName + '</td>' +
            '<td class="fw-semibold">PR<i class="mx-2 ' + Status + '"></i>DR</td>' +
            //'<td>' + replicationStatus + '</td>' +
            '</tr>';
        $("#Tobologybody").append(html)
    })
    $("#impactTopologyModalLabel").modal("show")
}

async function businessservicewiseImpactDetails(businessServiceId) {

    await $.ajax({
        url: "/Dashboard/ResiliencyMapping/GetImpactDetailCount",
        dataType: 'text',
        traditional: true,
        type: 'GET',
        data: { businessServiceId: businessServiceId },
        success: function (res) {
            
            let resCount = JSON.parse(res);           
          

            updateOperationalService("#serverdata", resCount.data.serverDownCount, resCount.data.serverTotalCount, ".totalServer", ".serverTotalCount", ".serverDownCount",'.serverdone');
            updateOperationalService("#applicationdata", resCount.data.applicationDownCount, resCount.data.applicationTotalCount, ".totalApplication", ".applicationTotalCount", ".applicationDownCount",".applicationdone");
            updateOperationalService("#databasedata", resCount.data.databaseDownCount, resCount.data.databaseTotalCount, ".totalDatabase", ".databaseTotalCount", ".databaseDownCount",'.databasedone');
            updateOperationalService("#replicationdata", resCount.data.replicationDownCount, resCount.data.replicationTotalCount, ".totalReplication", ".replicationTotalCount", ".replicationDownCount",".replicationdone");
            updateOperationalService("#storagedata", resCount.data.storageDownCount, resCount.data.storageTotalCount, ".totalStorage", ".storageTotalCount", ".storageDownCount",".storagedone");
            updateOperationalService("#networkdata", resCount.data.networkDownCount, resCount.data.networkTotalCount, ".totalnetwork", ".networkTotalCount", ".networkDownCount",".networkdone");

            //if (resCount.data.serverDownCount == 0 && resCount.data.serverTotalCount != 0) {
            //    $("#serverdata").hide()
            //    $(".serverTotalCount").text("(" + resCount.data.serverTotalCount + ")")
            //}
            //else if (resCount.data.serverDownCount == 0 && resCount.data.serverTotalCount == 0) {
            //    $("#serverdata").hide()
            //    $(".totalServer").hide()
            //}
            //else {
            //    $("#serverdata").show()
            //    $(".serverDownCount").text(resCount.data.serverDownCount)
            //    $(".serverTotalCount").text("(" + resCount.data.serverTotalCount + ")")
            //}


            //if (resCount.data.applicationDownCount == 0 && resCount.data.applicationTotalCount != 0) {
            //    $("#applicationdata").hide()
            //    $(".applicationTotalCount").text("(" + resCount.data.applicationTotalCount + ")")
            //}
            //else if (resCount.data.applicationDownCount == 0 && resCount.data.applicationTotalCount == 0) {
            //    $("#applicationdata").hide()
            //    $(".totalApplication").hide()
            //}
            //else {
            //    $("#applicationdata").show()
            //    $(".applicationDownCount").text(resCount.data.applicationDownCount)
            //    $(".applicationTotalCount").text("(" + resCount.data.applicationTotalCount + ")")
            //}


            //if (resCount.data.databaseDownCount == 0 && resCount.data.databaseTotalCount != 0) {
            //    $("#databasedata").hide()
            //    $(".databaseTotalCount").text("(" + resCount.data.databaseTotalCount + ")")
            //}
            //else if (resCount.data.databaseDownCount == 0 && resCount.data.databaseTotalCount == 0) {
            //    $("#databasedata").hide()
            //    $(".totalDatabase").hide()
            //}
            //else {
            //    $("#databasedata").show()
            //    $(".databaseDownCount").text(resCount.data.databaseDownCount)
            //    $(".databaseTotalCount").text("(" + resCount.data.databaseTotalCount + ")")
            //}


            //if (resCount.data.replicationDownCount == 0 && resCount.data.replicationTotalCount != 0) {
            //    $("#replicationdata").hide()
            //    $(".replicationTotalCount").text("(" + resCount.data.replicationTotalCount + ")")
            //}
            //else if (resCount.data.replicationDownCount == 0 && resCount.data.replicationTotalCount == 0) {
            //    $("#replicationdata").hide()
            //    $(".totalReplication").hide()
            //}
            //else {
            //    $("#replicationdata").show()
            //    $(".replicationDownCount").text(resCount.data.replicationDownCount)
            //    $(".replicationTotalCount").text("(" + resCount.data.replicationTotalCount + ")")
            //}


            //if (resCount.data.storageDownCount == 0 && resCount.data.storageTotalCount != 0) {
            //    $("#storagedata").hide()
            //    $(".storageTotalCount").text("(" + resCount.data.storageTotalCount + ")")
            //}
            //else if (resCount.data.storageDownCount == 0 && resCount.data.storageTotalCount == 0) {
            //    $("#storagedata").hide()
            //    $(".totalStorage").hide()
            //}
            //else {
            //    $("#storagedata").show()
            //    $(".storageDownCount").text(resCount.data.storageDownCount)
            //    $(".storageTotalCount").text("(" + resCount.data.storageTotalCount + ")")
            //}


            //if (resCount.data.networkDownCount == 0 && resCount.data.networkTotalCount != 0) {
            //    $("#networkdata").hide()
            //    $(".networkTotalCount").text("(" + resCount.data.networkTotalCount + ")")
            //}
            //else if (resCount.data.networkDownCount == 0 && resCount.data.networkTotalCount == 0) {
            //    $("#networkdata").hide()
            //    $(".totalnetwork").hide()
            //}
            //else {
            //    $("#networkdata").show()
            //    $(".networkDownCount").text(resCount.data.networkDownCount)
            //    $(".networkTotalCount").text("(" + resCount.data.networkTotalCount + ")")
            //}

            resCount.data.applicationDownCount ? $(".applicationDownCount").addClass("text-danger") : $(".applicationDownCount").removeClass("text-danger")
            resCount.data.databaseDownCount ? $(".databaseDownCount").addClass("text-danger") : $(".databaseDownCount").removeClass("text-danger")
            resCount.data.storageDownCount ? $(".storageDownCount").addClass("text-danger") : $(".storageDownCount").removeClass("text-danger")
            resCount.data.replicationDownCount ? $(".replicationDownCount").addClass("text-danger") : $(".replicationDownCount").removeClass("text-danger")
            resCount.data.networkDownCount ? $(".networkDownCount").addClass("text-danger") : $(".networkDownCount").removeClass("text-danger")
            resCount.data.serverDownCount ? $(".serverDownCount").addClass("text-danger") : $(".serverDownCount").removeClass("text-danger")

            resCount.data.applicationDownCount ? $("#applicationIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#applicationIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
            resCount.data.databaseDownCount ? $("#databaseIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#databaseIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
            resCount.data.storageDownCount ? $("#storageIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#storageIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
            resCount.data.replicationDownCount ? $("#replicationIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#replicationIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
            resCount.data.networkDownCount ? $("#networkIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#networkIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
            resCount.data.serverDownCount ? $("#serverIcon").addClass("cp-single-dot fs-9 text-danger me-1") : $("#serverIcon").removeClass("cp-single-dot fs-9 text-danger me-1")
        },
        error: function (xhr, error, res) {
            errorNotification(res);
            console.error('Error:', error);
        }
    })
}


function updateOperationalService(resource, downCount, totalCount, container, totalCountClass, downcountClass, hideDetails) {
    $(downcountClass).text("")
    $(totalCountClass).text("")
    if (downCount == 0 && totalCount != 0) {
        $(resource).hide();
        $(totalCountClass).text("(" + totalCount + ")")
        $(hideDetails).removeClass("d-none");
    } else if (downCount == 0 && totalCount == 0) {
        $(resource).hide();
        $(container).hide();
        $(hideDetails).addClass("d-none");

    } else {
        $(resource).show();
        $(downcountClass).text(downCount);
        $(totalCountClass).text("(" + totalCount + ")")
        $(hideDetails).removeClass("d-none");
    }
}

async function overallImpactList() {
    await $.ajax({
        url: "/Dashboard/ResiliencyMapping/GetImpactList",
        dataType: "json",
        traditional: true,
        type: 'GET',
        success: function (res) {
            $(".User_Profile_Timeline").empty()

            if (res.data.length != 0) {
                res.data.forEach((data) => {
                    let icon = data.entity == "Database" ? 'cp-database' : 'cp-server'
                    let timelineIcon = data.status == "Up" ? 'item-icon' : 'item-icon'
                    let html = ""
                    html += "<div class='user-profile-timeline-container'>"
                    html += "<ul class='tl'>"
                    html += "<li>"
                    html += "<div class='" + timelineIcon + "'></div>"
                    html += "<div class='item-text'>"
                    html += "<div class='item-title w-100'>"
                    html += "<div class='d-flex align-items-start gap-2'>"
                    data.status == "Up" ? html += "<i class='text-success " + icon + " mt-1'></i >" : html += "<i class='text-danger " + icon + " mt-1'></i>"
                    html += "<div class='d-grid gap-1 w-100'>"
                    html += "<div class='d-flex justify-content-between'>"
                    html += "<span class='list-title'>" + data.action + "</span>"
                    data.status == "Up" ? html += "<span class='text-success'>" + data.entity + " Up</span>" : html += "<span class='text-danger'>" + data.entity + " Down</span>"
                    html += "</div>"
                    html += "<small class='text-light fs-8'>" + data.lastModifiedDate + "</small>"
                    html += "</div>"
                    html += "</div>"
                    html += "</div>"
                    html += "</div>"
                    html += "</li>"
                    html += "</ul>"
                    html += "</div>"
                    $(".User_Profile_Timeline").append(html)
                })
            }
            else {

                $(".User_Profile_Timeline").append(impactImage)
            }
        },
        error: function (xhr, error, res) {
            errorNotification(res);
            console.error('Error:', error);
        }
    })
}

function populateHTML(data) {
    let busService = [];

    for (let i = 0; i < data.length; i++) {

        // Business Service
        let businessServiceId = data[i]?.businessServiceId;
        let businessServiceName = data[i]?.businessServiceName;
        let businessSiteProperties = encodeURIComponent(data[i]?.siteProperties);
        let status = data[i]?.status;
        let statusInfo = getStatusInfo(status);

        // Business Function
        let businessFunctions = data[i]?.getDcMappingBusinessFunctionListVms;
        let businessFunctionHtml = '';

        if (businessFunctions && businessFunctions?.length > 0) {
            for (let j = 0; j < businessFunctions.length; j++) {
                let businessFunction = businessFunctions[j];
                let infraObjectsHtml = '';

                // Infra Objects
                let infraObjects = businessFunction?.getDcMappingInfraObjectListVms;

                if (infraObjects && infraObjects?.length > 0) {
                    busService.push(data[i]);
                    infraObjectsHtml = infraObjects?.map(infraObject => {
                        globalinfraData.push(infraObject)
                        let replicationStatus = getReplicationStatusHtml(infraObject);
                        let Status = infraObject.drOperationStatus == "2" ? 'cp-off-arrow' : 'cp-on-arrow'
                        return '<tr title="' + infraObject?.infraObjectName + '" name=' + infraObject?.infraObjectName + ' status=' + Status + ' onclick="onloadMapData(this)">' +
                            '<td class="text-light businessDataName" title="' + infraObject?.infraObjectName + '"><i class="cp-book text-primary me-2"></i>' + infraObject?.infraObjectName + '</td>' +
                            '<td class="fw-semibold">PR<i class="mx-2 ' + Status + ' text-primary"></i>DR</td>' +
                            //'<td>' + replicationStatus + '</td>' +
                            '</tr>';
                    }).join('');
                } else {
                    infraObjectsHtml = '<tr><td colspan="3"><span style="color:red">No Infra Objects found</span></td></tr>';
                }

                businessFunctionHtml += '<div class="mb-2">' +
                    '<i class="cp-business-function circle bg-primary me-1 fs-7"></i>' +
                    '<span class="fw-semibold businessDataName" title="' + businessFunction.businessFunctionName + '">' + businessFunction.businessFunctionName + '</span>' +
                    '<div class="table-container ps-4" style="max-height: 140px; overflow-y: auto;">' +
                    '<table class="table table-sm mt-2">' +
                    '<thead>' +
                    '<tr onclick="onloadMapData(this)">' +
                    '<th class="" title="InfraObjects">InfraObjects</th>' +
                    '<th class="" title="Site">Site</th>' +
                    //'<th class="" title="Replication">Replication</th>' +
                    '</tr>' +
                    '</thead>' +
                    '<tbody>' +
                    infraObjectsHtml +
                    '</tbody>' +
                    '</table>' +
                    '</div>' +
                    '</div>';
            }

        } else {
            businessFunctionHtml = '<img src="../../img/isomatric/no_data_found.svg" class="Card_NoData_Img">';
        }
        var BusinessServiceHtml =
            '<div class="accordion-item businessDataItem">' +
            '<h2 class="accordion-header">' +
            '<div class="accordion-button" role="button" id="' + businessServiceId + '" value="' + businessServiceName + '" propertices="' + businessSiteProperties + '" onclick="GetBusinessServiceData(this)"  data-bs-toggle="collapse" data-bs-target="#collapse-' + businessServiceId + '" aria-expanded="false" aria-controls="collapse-' + businessServiceId + '">' +
            '<div class="d-flex align-items-center justify-content-between w-100">' +
            '<div><i class="cp-business-service me-2"></i><span class="align-middle businessDataName" title="' + businessServiceName + '">' + businessServiceName + '</span></div>' +
            '<small class="align-items-center d-flex"  style="height: fit-content">' +
            '<i class="' + statusInfo.statusIcon + ' text-' + statusInfo.statusClass + '"></i>' + statusInfo.status +
            '</span>' +
            '</div>' +
            '</div>' +
            '</h2>' +
            '<div id="collapse-' + businessServiceId + '" class="accordion-collapse collapse p-2 bs" BS_Id="' + businessServiceId + '">' +
            '<div class="accordion-body p-0">' +
            businessFunctionHtml +
            '</div>' +
            '</div>' +
            '</div>';
        $("#accordionExample").append(BusinessServiceHtml);
    }

    if (busService?.length > 0) {
        $('.collapse').on('show.bs.collapse', function () {
            $('.collapse.show').not(this).collapse('hide');
        });

        $(`.bs[BS_Id='${busService[0].businessServiceId}']`).addClass('show');
        $(`.accordion-button[id='${busService[0].businessServiceId}']`).addClass('Active-Card');

        let mappingData = JSON.parse(busService[0].siteProperties)
        $(".siteCount").text(Object.keys(mappingData).length)
        $(".businessServiceCount").text(busService?.length)

        globalbusinessServiceId = busService[0].businessServiceId
        chartdata(mappingData)
        //let selectedBS = globalBusinessServiceData.find(service => service.businessServiceId === busService_Id[0]);
        // DCMapping(busService[0]);
    }
}

function onloadMapData(data) {
    let name = data.getAttribute("name")
    let title = data.getAttribute("title")
    let status = data.getAttribute("status")
}

//Selected Business Service Data
function GetBusinessServiceData(data) {
    let id = data.id
    let propertics = data.getAttribute("propertices")
    let mappingDatasingle = JSON.parse(decodeURIComponent(propertics))
    $(".siteCount").text(Object.keys(mappingDatasingle).length)
    $('.accordion-button').removeClass('Active-Card');
    $('#' + id).addClass('Active-Card');
    chartdata(mappingDatasingle)
    globalbusinessServiceId = data.id
    businessservicewiseImpactDetails(id)
    GetServiceTopology(id)
    //let selectedBS = globalBusinessServiceData.find(service => service.businessServiceId === id);
}

function chartdata(mappingData) {
    $(".cardSiteDetails").empty()
    globalMapData = []
    globalMapDataLatLong = []
    let siteImage = ""
    for (let x in mappingData) {
        let mapData = {}
        let latlong = {}
        let siteHtml = ""
        mapData.title = mappingData[x].Location
        mapData.latitude = parseFloat(mappingData[x].Lat)
        mapData.longitude = parseFloat(mappingData[x].Lng)
        latlong.latitude = parseFloat(mappingData[x].Lat)
        latlong.longitude = parseFloat(mappingData[x].Lng)
        mapData.Id = mappingData[x].Id
        if (x.toLowerCase() == "prsite") {
            mapData.CircleColor = "#f28a2e"
            mapData.color = "#f28a2e"
            siteImage = '<img class="mt-1" src="../../img/input_icons/service_available.svg" width="15" />'
        }
        else if (x.toLowerCase() == "drsite") {
            mapData.CircleColor = "#6f42c1"
            mapData.color = "#6f42c1"
            siteImage = '<img class="mt-1" src="../../img/input_icons/business_functions.svg" width="15" />'
        }
        else if (x.toLowerCase() == "neardrsite") {
            mapData.CircleColor = "#00d3d1"
            mapData.color = "#00d3d1"
            siteImage = '<img class="mt-1" src="../../img/input_icons/dr_ready.svg" width="15" />'
        }
        else if (x.toLowerCase() == "customsite") {
            mapData.CircleColor = "#ffc107"
            mapData.color = "#ffc107"
            siteImage = '<img class="mt-1" src="../../img/input_icons/alerts.svg" width="15" />'
        }
        else {
            mapData.CircleColor = "#d63384"
            mapData.color = "#d63384"
            siteImage = '<img class="mt-1" src="../../img/input_icons/incidents.svg" width="15" />'
        }
        mapData.radius = 10
        globalMapData.push(mapData)
        globalMapDataLatLong.push(latlong)
        siteHtml += '<div class="col-auto d-flex gap-2 align-items-start" >'
        siteHtml += '<div class="card shadow mb-2">'
        siteHtml += '<div class="card-body p-2 d-flex align-items-start gap-2">'
        siteHtml += siteImage
        siteHtml += '<div>'
        siteHtml += '<span class="fs-8 fw-semibold">' + x + '</span>'
        siteHtml += '<div class="">'
        siteHtml += '<small class="text-light">' + mappingData[x].Location + '</small>'
        //siteHtml += '<span class="text-danger small px-1 rounded-pill ms-1" style="background-color:#f8d7da59;">1<i class="cp-dashboard-down fs-8 fw-semibold ms-1"></i></span>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        siteHtml += '</div>'
        $(".cardSiteDetails").append(siteHtml)
    }
    DCMapChart(globalMapData, globalMapDataLatLong)
}

//Business Service Status
function getStatusInfo(status) {
    let result = {};

    if (status && status.toLowerCase().replace(/\s/g, "") === "available") {
        result.statusIcon = "cp-success me-1";
        result.statusClass = "success";
        result.status = status;
    } else if (status && status.toLowerCase().replace(/\s/g, "") === "majorimpact") {
        result.statusIcon = "cp-error me-1";
        result.statusClass = "danger";
        result.status = status;
    } else {
        result.statusIcon = "cp-warning me-1";
        result.statusClass = "warning";
        result.status = "Not Available";
    }
    return result;
}

//Replication status
function getReplicationStatusHtml(infraObject) {
    return infraObject?.state === "Active" ? '<i class="cp-replication-on" title="Active"></i>' : '<i class="cp-replication-Off" title="Maintenance"></i>';
}

function DCMapping(SiteProperty) {
    //debugger
    //let mappingData = [JSON.parse(SiteProperty.siteProperties)]
    //let siteMapping = []
    //mappingData.forEach((data) => {
    //    name: 'Appipr',
    //    count: 5,
    //    location: 'London',
    //    fixed: true,
    //    parentLocation: '\u21E3',
    //    x: am4core.percent(x),
    //    y: am4core.percent(yParent),
    //    value: 10,
    //    image: "../../img/charts_img/DataCenter/application.svg"
    //    let siteData = {}
    //    siteData.name=
    //})
    am4core.useTheme(am4themes_animated);
    am4core.options.autoSetClassName = true;
    // Create chart
    var chart = am4core.create("DCMapping", am4plugins_forceDirected.ForceDirectedTree);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    chart.chartContainer.width = am4core.percent(100);
    chart.chartContainer.height = am4core.percent(100)

    // Create series
    var series = chart.series.push(
        new am4plugins_forceDirected.ForceDirectedSeries()
    );

    var customSites = [];
    let x = 8;
    let yParent = 15;

    //series.data = topologyData
    series.data = [
        {
            name: 'Appipr' + '(10/100)',
            id: '1',
            class: "customClass1",
            fixed: true,
            parentLocation: '\u21E3',
            x: am4core.percent(x),
            y: am4core.percent(yParent),
            image: "../../img/charts_img/DataCenter/application.svg",
            children: [
                {
                    name: 'Tts',
                    fixed: true,
                    x: am4core.percent(20),
                    y: am4core.percent(yParent),
                    value: 10,
                    //  imageSmall:"/assets/img/charts_img/DataCenter/near_dr.svg",
                    image: "../../img/charts_img/DataCenter/Network.svg",
                    children: []
                },
                {
                    name: 'Dbnet pr',
                    fixed: true,
                    x: am4core.percent(35),
                    y: am4core.percent(yParent),
                    value: 10,
                    //  imageSmall:"/assets/img/charts_img/DataCenter/near_dr.svg",
                    link: ["Dbnet pr2", 'Dbnet pr3'],
                    image: "../../img/charts_img/DataCenter/database.svg",
                    children: [
                    ]
                }]
        },
        {
            name: 'Appipr2',
            id: 'Appipr2',
            location: 'London',
            fixed: true,
            drLocation: '\u21E1',
            x: am4core.percent(x),
            y: am4core.percent(60),
            value: 10,
            image: "../../img/charts_img/DataCenter/application.svg",
            children: [
                {
                    name: 'Tts',
                    location: 'London',
                    fixed: true,
                    x: am4core.percent(20),
                    y: am4core.percent(60),
                    value: 10,
                    image: "../../img/charts_img/DataCenter/Network.svg",
                    children: []
                },
                {
                    name: 'Dbnet pr2',
                    location: 'London',
                    id: "Dbnet pr2",
                    fixed: true,
                    x: am4core.percent(35),
                    y: am4core.percent(60),
                    value: 10,
                    image: "../../img/charts_img/DataCenter/database.svg",
                    children: [
                    ]
                }]
        },
        {
            name: 'appipr3',
            location: 'london',
            id: 'appipr3',
            fixed: true,
            customelocation: "\u21e1",
            x: am4core.percent(50),
            y: am4core.percent(35),
            value: 10,
            image: "../../img/charts_img/datacenter/application.svg",
            children: [
                {
                    name: 'tts3',
                    location: 'london',
                    fixed: true,
                    x: am4core.percent(65),
                    y: am4core.percent(35),
                    value: 10,
                    image: "../../img/charts_img/datacenter/network.svg",
                    children: []
                },
                {
                    name: 'dbnet pr3',
                    location: 'london',
                    fixed: true,
                    x: am4core.percent(80),
                    y: am4core.percent(35),
                    value: 10,
                    id: "dbnet pr3",
                    image: "../../img/charts_img/datacenter/database.svg",
                    children: [
                    ]
                }]
        },
    ]

    series.nodes.template.events.off("hit", series.nodeClick);

    // Set up data fields
    series.dataFields.value = "value";
    series.dataFields.fixed = "fixed";
    series.dataFields.name = "name";
    series.dataFields.class = "class";
    series.dataFields.children = "children";
    series.dataFields.id = "name";
    series.dataFields.linkWith = "link";
    series.links.template.propertyFields.userClassName = "lineClass"
    series.manyBodyStrength = -14;

    // Add labels
    series.nodes.template.label.text = "{name}";
    series.nodes.template.label.valign = "bottom";
    series.nodes.template.label.dy = -22;

    series.nodes.template.label.fill = am4core.color("#000");
    series.nodes.template.label.dy = -12;
    series.nodes.template.tooltipText = "[font-size: 14px;]{name}\n[/]  [font-size: 14px;]{location} [/]\n";
    series.fontSize = 11;
    series.minRadius = 10;
    series.maxRadius = 10;
    series.tooltip.disabled = false;

    series.nodes.template.circle.fill = am4core.color("#fff");
    series.nodes.template.circle.strokeWidth = 6;
    series.nodes.template.outerCircle.filters.push(new am4core.DropShadowFilter());

    series.dataFields.fixed = "fixed";
    series.nodes.template.propertyFields.x = "x";
    series.nodes.template.propertyFields.y = "y";

    // Configure icons
    var icon = series.nodes.template.createChild(am4core.Image);
    icon.propertyFields.href = "image";
    icon.horizontalCenter = "middle";
    icon.verticalCenter = "middle";
    icon.width = 40;
    icon.height = 40;
    series.centerStrength = 0.6;
    series.centerStrength = 0.6;

    var icons = series.nodes.template.createChild(am4core.Image);
    icons.propertyFields.href = "imageSmall";
    icons.horizontalCenter = "middle";
    icons.verticalCenter = "middle";
    icons.width = 40;
    icons.height = 40; /* child label */
    series.centerStrength = 0.6;

    //  series.centerStrength = 0.6;
    let linkTemplate = series.nodes.template;
    linkTemplate.strokeOpacity = 0;

    let parentX = series.data[0].x._value;
    let childX = series.data[0]?.children[0]?.x._value;
    let sValue = series.data[0]?.children[1]?.x._value;

    let midpointX = (parentX + childX) / 2;
    let midpointY = -((parentX + sValue));

    let linkContentLeft = linkTemplate.createChild(am4core.Label);
    linkContentLeft.text = "{parentLocation}";
    linkContentLeft.fontSize = 20;
    linkContentLeft.dx = 40;
    linkContentLeft.dy = 28;
    linkContentLeft.fill = am4core.color("#ed770d");

    let linkContentRight = linkTemplate.createChild(am4core.Label);
    linkContentRight.text = "{drLocation}";
    linkContentRight.fontSize = 20;
    linkContentRight.dx = 130;
    linkContentRight.dy = -50;
    linkContentRight.fill = am4core.color("#ed770d");

    let linkContentBottom = linkTemplate.createChild(am4core.Label);
    linkContentBottom.text = "{customeLocation}";
    linkContentBottom.fontSize = 20;
    linkContentBottom.dx = -10;
    linkContentBottom.dy = 35;
    linkContentBottom.fill = am4core.color("#21b046");
};

//$(function () {
//    DCMapping()
//})