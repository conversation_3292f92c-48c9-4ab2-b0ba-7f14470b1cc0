﻿ @using ContinuityPatrol.Domain.ViewModels.UserGroupModel;

@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-teams"></i><span>User Group List</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" ><i class="cp-filter" title="Filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="GroupName=" id="groupname">
                                        <label class="form-check-label" for="groupname">
                                            Group Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="Usernames=" id="Users">
                                        <label class="form-check-label" for="Users">
                                            Users
                                        </label>
                                    </div>
                                </li>

                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#CreateModal" id="Create"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="tblusrgroup" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Group Name</th>
                        <th>Group Description</th>
                        <th>Users</th>
                        <th class="Action-th">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @* @{
                        int i = 1;
                    }
                    @foreach (var usergroup in Model.PaginatedUserGroups.Data)
                    {
                        <tr>
                            <td>
                                <span>@i</span>
                            </td>
                            <td>

                                @usergroup.GroupName
                            </td>
                            <td>@usergroup.GroupDescription</td>
                            <td>@usergroup.UserNames</td>
                            <td class="Action-th">
                                <div class="dropdown">
                                    <i class="cp-horizontal-dots me-2" role="button" data-bs-toggle="dropdown"></i>
                                    <ul class="dropdown-menu">

                                        <li>
                                            @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit == true)
                                            {
                                                <a role="button" class="dropdown-item edit-button" data-usergroup='@Json.Serialize(usergroup)'>
                                                    <i class="cp-edit me-2"></i>Edit
                                                </a>
                                            }
                                            else
                                            {
                                                <a role="button" class="dropdown-item btn-disabled">
                                                    <i class="cp-edit me-2"></i>Edit
                                                </a>
                                            }

                                        </li>
                                        <li>
                                            @if (ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete == true)
                                            {
                                                <a role="button" class="dropdown-item delete-button" data-usergroup-id="@usergroup.Id" data-usergroup-name="@usergroup.GroupName" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                                    <i class="cp-Delete me-2"></i>Delete
                                                </a>
                                            }
                                            else
                                            {
                                                <a role="button" class="dropdown-item btn-disabled">
                                                    <i class="cp-Delete me-2"></i>Delete
                                                </a>
                                            }
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        i++;
                    } *@
                </tbody>
            </table>
        </div>
    </div>
</div>


<div id="AdminCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.CreateAndEdit" aria-hidden="true"></div>
<div id="AdminDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Admin.Delete" aria-hidden="true"></div>
<!--Modal Create-->
<div class="modal fade" id="CreateModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <partial name="Configuration" model="@Model"/>
</div> 

<!--Modal Delete-->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>

 @section Scripts
    {
    <partial name="_ValidationScriptsPartial" />
}

<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script>
    $('select').on('select2:closing', () => {
        $('.select2-selection').width('auto');
        var $choice = $('.select2-selection__choice');
        $choice.first().show();
        $choice.slice(4).hide();
        $choice.eq(5).after(`<li class='select2-selection__choice select2-selection__choice_more'>...</li>`);
    });
</script>
<script src="~/js/usergroup.js"></script>
