﻿using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Create;
using ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Update;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class RpoSlaDeviationReportService :  IRpoSlaDeviationReportService
{
    private readonly IBaseClient _client;
    public RpoSlaDeviationReportService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<BaseResponse> CreateAsync(CreateRpoSlaDeviationReportCommand createRpoSlaDeviationReportCommand)
    {
        var request = new RestRequest("api/v6/rposladeviationreports", Method.Post);

        request.AddJsonBody(createRpoSlaDeviationReportCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateRpoSlaDeviationReportCommand updateRpoSlaDeviationReportCommand)
    {
        var request = new RestRequest("api/v6/rposladeviationreports", Method.Put);

        request.AddJsonBody(updateRpoSlaDeviationReportCommand);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string rpoSlaDeviationReportId)
    {
        var request = new RestRequest($"api/v6/rposladeviationreports/{rpoSlaDeviationReportId}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }
}