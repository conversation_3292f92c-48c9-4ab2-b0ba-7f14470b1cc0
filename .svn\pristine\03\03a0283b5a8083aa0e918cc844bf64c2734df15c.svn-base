﻿using ContinuityPatrol.Application.Hubs;

namespace ContinuityPatrol.Application.UnitTests.Hubs;

public class WorkflowHubTests
{
    [Fact]
    public async Task UpdateWorkflowOperation_Should_Send_To_Others()
    {
        // Arrange
        var mockClients = new Mock<IHubCallerClients>();
        var mockOthers = new Mock<IClientProxy>();

        var groupWorkflow = new GroupWorkflowVm
        {
            Id = "wf-1",
            CurrentActionName = "StartAction",
            Status = "Running",
            ProgressStatus = "50%",
            ActionMode = "Auto",
            IsResume = 0,
            IsReExecute = 1,
            IsPause = 0,
            IsAbort = 0,
            WaitToNext = 0,
            ConditionalOperation = 1,
            IsWorkflowCompleted = false
        };

        mockClients.Setup(x => x.Others).Returns(mockOthers.Object);

        var hub = new WorkflowHub
        {
            Clients = mockClients.Object
        };

        // Act
        await hub.UpdateWorkflowOperation(groupWorkflow);

        // Assert
        mockOthers.Verify(x =>
                x.SendCoreAsync(
                    "updateWorkflowOperation",
                    It.Is<object[]>(args => args.Length == 1 && args[0] == groupWorkflow),
                    default),
            Times.Once);
    }

    [Fact]
    public async Task SendMessage_Should_Send_To_Others()
    {
        // Arrange
        var mockClients = new Mock<IHubCallerClients>();
        var mockOthers = new Mock<IClientProxy>();
        var message = "Test Message";

        mockClients.Setup(x => x.Others).Returns(mockOthers.Object);

        var hub = new WorkflowHub
        {
            Clients = mockClients.Object
        };

        // Act
        await hub.SendMessage(message);

        // Assert
        mockOthers.Verify(x =>
                x.SendCoreAsync(
                    "sendMessage",
                    It.Is<object[]>(args => args.Length == 1 && (string)args[0] == message),
                    default),
            Times.Once);
    }
    [Fact]
    public void GroupWorkflowVm_Should_Set_And_Get_All_Properties()
    {
        // Arrange
        var vm = new GroupWorkflowVm
        {
            Id = "G1",
            CurrentActionName = "Action1",
            Status = "Running",
            ProgressStatus = "50%",
            ActionMode = "Auto",
            IsResume = 1,
            IsReExecute = 0,
            IsPause = 0,
            IsAbort = 0,
            WaitToNext = 10,
            ConditionalOperation = 1,
            IsWorkflowCompleted = true
        };

        // Act & Assert
        Assert.Equal("G1", vm.Id);
        Assert.Equal("Action1", vm.CurrentActionName);
        Assert.Equal("Running", vm.Status);
        Assert.Equal("50%", vm.ProgressStatus);
        Assert.Equal("Auto", vm.ActionMode);
        Assert.Equal(1, vm.IsResume);
        Assert.Equal(0, vm.IsReExecute);
        Assert.Equal(0, vm.IsPause);
        Assert.Equal(0, vm.IsAbort);
        Assert.Equal(10, vm.WaitToNext);
        Assert.Equal(1, vm.ConditionalOperation);
        Assert.True(vm.IsWorkflowCompleted);
    }
}