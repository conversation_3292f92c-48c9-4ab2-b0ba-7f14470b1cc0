﻿using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Events.ResetPassword;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Commands
{
    public class ResetPasswordTests
    {
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
        private readonly Mock<ILogger<ResetPasswordCommandHandler>> _mockLogger;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IPublisher> _mockPublisher;
        private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;
        private readonly Mock<IUserLoginRepository> _mockUserLoginRepository;
        private readonly Mock<IUserRepository> _mockUserRepository;
        private readonly ResetPasswordCommandHandler _handler;

        public ResetPasswordTests()
        {
            _mockEmailService = new Mock<IEmailService>();
            _mockGlobalSettingRepository = new Mock<IGlobalSettingRepository>();
            _mockLogger = new Mock<ILogger<ResetPasswordCommandHandler>>();
            _mockMapper = new Mock<IMapper>();
            _mockPublisher = new Mock<IPublisher>();
            _mockSmtpConfigurationRepository = new Mock<ISmtpConfigurationRepository>();
            _mockUserLoginRepository = new Mock<IUserLoginRepository>();
            _mockUserRepository = new Mock<IUserRepository>();

            _handler = new ResetPasswordCommandHandler(
                _mockMapper.Object,
                _mockEmailService.Object,
                _mockUserRepository.Object,
                _mockUserLoginRepository.Object,
                _mockPublisher.Object,
                _mockSmtpConfigurationRepository.Object,
                _mockLogger.Object,
                _mockGlobalSettingRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Return_Success_When_ResetPassword_Is_Valid()
        {
            var request = new ResetPasswordCommand
            {
                UserId = "user123",
                LoginName = "testuser",
                Password = "newPassword123",
                Email = "<EMAIL>"
            };

            var user = new Domain.Entities.User
            {
                ReferenceId = "user123",
                LoginName = "testuser",
                LoginPassword = "oldPassword123",
                IsLock = true,
                IsReset = false,
                LoginType = "local"
            };

            var userLogin = new Domain.Entities.UserLogin
            {
                UserId = "user123",
                InvalidLoginAttempt = 0
            };

            _mockUserRepository.Setup(u => u.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync(user);
            _mockUserLoginRepository.Setup(ul => ul.GetUserLoginByUserId(It.IsAny<string>())).ReturnsAsync(userLogin);
            _mockGlobalSettingRepository.Setup(g => g.GlobalSettingBySettingKey(It.IsAny<string>())).ReturnsAsync(new Domain.Entities.GlobalSetting { GlobalSettingValue = "true" });
            _mockSmtpConfigurationRepository.Setup(s => s.ListAllAsync()).ReturnsAsync(new List<Domain.Entities.SmtpConfiguration> { new Domain.Entities.SmtpConfiguration() });
            _mockMapper.Setup(m => m.Map<EmailDto>(It.IsAny<object>())).Returns(new EmailDto());
            _mockEmailService.Setup(e => e.SendEmail(It.IsAny<EmailDto>())).Returns(ToString);
            _mockPublisher.Setup(p => p.Publish(It.IsAny<ResetPasswordUpdatedEvent>(), It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("Email sent successfully!.", result.Message);
            _mockEmailService.Verify(e => e.SendEmail(It.IsAny<EmailDto>()), Times.Once);
            _mockUserRepository.Verify(u => u.UpdateAsync(It.IsAny<Domain.Entities.User>()), Times.Once);
            _mockUserLoginRepository.Verify(ul => ul.UpdateAsync(It.IsAny<Domain.Entities.UserLogin>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Throw_InvalidException_When_EmailNotificationIsDisabled()
        {
            var request = new ResetPasswordCommand
            {
                UserId = "user123",
                LoginName = "testuser",
                Password = "newPassword123",
                Email = "<EMAIL>"
            };

            _mockGlobalSettingRepository.Setup(g => g.GlobalSettingBySettingKey(It.IsAny<string>())).ReturnsAsync(new Domain.Entities.GlobalSetting { GlobalSettingValue = "false" });

            var exception = await Assert.ThrowsAsync<InvalidException>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("The email notification feature is not enabled in the global settings.", exception.Message);
        }

        [Fact]
        public async Task Handle_Should_Throw_InvalidException_When_SmtpConfigurationIsMissing()
        {
            var request = new ResetPasswordCommand
            {
                UserId = "user123",
                LoginName = "testuser",
                Password = "newPassword123",
                Email = "<EMAIL>"
            };

            _mockGlobalSettingRepository.Setup(g => g.GlobalSettingBySettingKey(It.IsAny<string>())).ReturnsAsync(new Domain.Entities.GlobalSetting { GlobalSettingValue = "true" });
            _mockSmtpConfigurationRepository.Setup(s => s.ListAllAsync()).ReturnsAsync(new List<Domain.Entities.SmtpConfiguration>());

            var exception = await Assert.ThrowsAsync<InvalidException>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("please configure smtp.", exception.Message);
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFoundException_When_UserDoesNotExist()
        {
            var request = new ResetPasswordCommand
            {
                UserId = "nonExistentUser",
                LoginName = "testuser",
                Password = "newPassword123",
                Email = "<EMAIL>"
            };

            _mockGlobalSettingRepository.Setup(g => g.GlobalSettingBySettingKey(It.IsAny<string>())).ReturnsAsync(new Domain.Entities.GlobalSetting { GlobalSettingValue = "true" });
            _mockSmtpConfigurationRepository.Setup(s => s.ListAllAsync()).ReturnsAsync(new List<Domain.Entities.SmtpConfiguration> { new Domain.Entities.SmtpConfiguration() });
            _mockUserRepository.Setup(u => u.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((Domain.Entities.User)null);

            var exception = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("User not found.", exception.Message);
        }

        [Fact]
        public async Task Handle_Should_Throw_InvalidPasswordException_When_LoginNamesDoNotMatch()
        {
            var request = new ResetPasswordCommand
            {
                UserId = "user123",
                LoginName = "testuser",
                Password = "newPassword123",
                Email = "<EMAIL>"
            };

            var user = new Domain.Entities.User
            {
                ReferenceId = "user123",
                LoginName = "testuser",
                LoginPassword = "oldPassword123",
                IsLock = true,
                IsReset = false,
                LoginType = "local"
            };

            _mockUserRepository.Setup(u => u.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync(user);
            _mockGlobalSettingRepository.Setup(g => g.GlobalSettingBySettingKey(It.IsAny<string>())).ReturnsAsync(new Domain.Entities.GlobalSetting { GlobalSettingValue = "true" });
            _mockSmtpConfigurationRepository.Setup(s => s.ListAllAsync()).ReturnsAsync(new List<Domain.Entities.SmtpConfiguration> { new Domain.Entities.SmtpConfiguration() });

            var exception = await Assert.ThrowsAsync<InvalidPasswordException>(() => _handler.Handle(request, CancellationToken.None));
            Assert.Equal("The user is not valid.", exception.Message);
        }
    }
}
