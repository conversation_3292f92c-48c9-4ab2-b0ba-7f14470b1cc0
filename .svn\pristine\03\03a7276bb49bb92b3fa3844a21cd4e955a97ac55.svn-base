﻿namespace ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;

public class
    GetDataSetColumnsColumnNameQueryHandler : IRequestHandler<GetDataSetColumnsColumnNameQuery,
        List<DataSetColumnsColumnNameVm>>
{
    private readonly IDataSetColumnsRepository _dataSetColumnsRepository;
    private readonly IMapper _mapper;

    public GetDataSetColumnsColumnNameQueryHandler(IMapper mapper, IDataSetColumnsRepository dataSetColumnsRepository)
    {
        _mapper = mapper;
        _dataSetColumnsRepository = dataSetColumnsRepository;
    }

    public async Task<List<DataSetColumnsColumnNameVm>> Handle(GetDataSetColumnsColumnNameQuery request,
        CancellationToken cancellationToken)
    {
        var dataSetColumns =
            await _dataSetColumnsRepository.GetColumnsByDatabaseNameAndTableName(request.DBName, request.TableName);

        var dataSetColumnsDto = _mapper.Map<List<DataSetColumnsColumnNameVm>>(dataSetColumns);

        return dataSetColumnsDto;
    }
}