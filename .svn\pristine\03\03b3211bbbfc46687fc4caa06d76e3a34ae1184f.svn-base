namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;

public class DynamicDashboardMapDetailVm
{
    public int Id { get; set; }
    public string ReferenceId { get; set; }
    public string DashBoardSubId { get; set; }
    public string DashBoardSubName { get; set; }
    public string UserId { get; set; }
    public string UserName { get; set; }
    public string RoleId { get; set; }
    public string RoleName { get; set; }
    public string Type { get; set; }
    public bool IsDefault { get; set; }
    public bool IsView { get; set; }
    public string Url { get; set; }
}