﻿namespace ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetMSSQLMonitorStatusByInfraObjectId;

public class
    GetMSSQLMonitorStatusByInfraObjectIdQueryHandler : IRequestHandler<GetMSSQLMonitorStatusByInfraObjectIdQuery,
        string>
{
    private readonly IMapper _mapper;
    private readonly IMssqlMonitorStatusRepository _mssqlMonitorStatusRepository;

    public GetMSSQLMonitorStatusByInfraObjectIdQueryHandler(IMapper mapper,
        IMssqlMonitorStatusRepository mssqlMonitorStatusRepository)
    {
        _mapper = mapper;
        _mssqlMonitorStatusRepository = mssqlMonitorStatusRepository;
    }

    public async Task<string> Handle(GetMSSQLMonitorStatusByInfraObjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var mssqlMonitorStatus =
            await _mssqlMonitorStatusRepository.GetMssqlMonitorStatusByInfraObjectIdAsync(request.InfraObjectId);

        Guard.Against.NullOrDeactive(mssqlMonitorStatus, nameof(Domain.Entities.MSSQLMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.MSSQLMonitorStatus), request.InfraObjectId));

        var mssqlMonitorStatusDetailDto = mssqlMonitorStatus.ReferenceId;

        return mssqlMonitorStatusDetailDto;
    }
}