﻿let userData = {}, isCreate = false, isLoginName = false, JSONDataForClickEditButton = "", editedLoginID = "", dataTable = "", jsonData = "", loginNameEdit = 0;
let userRole = $('#userRole').text().trim();
let userRoleValue = $('#userRoleValue').text().trim();
let loggedInUserId = $('#loggedInUserId').text().trim();

const userManageURL = {
    createOrUpdate: "Admin/User/CreateOrUpdate", 
    getPagination: "/Admin/User/GetPagination",
    nameExist: "Admin/User/IsLoginNameExist",
    userRole: "/Admin/User/GetUserRoleList",
    domainGroups: "Admin/User/GetDomainGroups",
    infraObjectList: "Admin/User/GetAllInfraObjectList",
    domainUsers: "Admin/User/GetDomainUsers",
    domains:"Admin/User/GetDomains"
};

const generateRandomString = (length) => Array.from({ length }, () => String.fromCharCode(65 + Math.floor(Math.random() * 26) + (Math.random() < 0.5 ? 32 : 0))).join('');
let createPerm = $("#userCreate").attr("data-create-permission") || "false";
let deletePerm = $("#userDelete").attr("data-delete-permission") || "false";

let permission = {
    createPermission: createPerm.toLowerCase(),
    deletePermission: deletePerm.toLowerCase()
};
   
    $('#Label_error').Text = "";
    $('#searchStringDomain,#usernameDomain').hide();
    $("#userWorkflowAll").prop("disabled", false);

    btnCrudEnable('userReset')
    btnCrudEnable('confirmDeleteButton')  
    
    if (permission.createPermission == "false") {
        $("#btnCreate").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }  
    let selectedValues = [];
    dataTable = $('#userTable').DataTable(
        {
            language: {
                decimal: ",",
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
                },
                infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "Sortable": true,
            "order": [],
            fixedColumns: {
                left: 1,
                right: 1
            },
            //retrieve: true,
            "ajax": {
                "type": "GET",
                "url": userManageURL.getPagination,
                "dataType": "json",
                "data": function (d) {
                    let sortIndex = (d?.order && d.order[0]) ? d.order[0].column : ''; 
                    let sortValue = sortIndex === 1 ? "loginName" : sortIndex === 2 ? "companyName" : sortIndex === 3 ? "loginType" :
                        sortIndex === 4 ? "roleName" : sortIndex === 5 ? "email" : sortIndex === 6 ? "loginDate" : sortIndex === 7 ? "status" : "";
                    "";
                    let orderValue = (d?.order && d.order[0]) ? d.order[0].dir : 'asc';
                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues.length === 0 ? $('#userSearch').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {
                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json?.data?.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                }
            },
            "error": function (xhr, status, error) {
                if (error.status === 401) {
                    window.location.assign('/Account/Logout')
                }
            },
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    }
                },
                {
                    "data": "loginName", "name": "Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            var loginName = row.loginName;
                            var nameSplit = loginName.split(/[ _]+/);
                            var initials = nameSplit.length > 1 ?
                                nameSplit[0].trim().substring(0, 1) + nameSplit[1].trim().substring(0, 1).toUpperCase() :
                                nameSplit[0].trim().substring(0, 1).toUpperCase();
                            return `
                               <span class="Avatar_Logo position-relative">
                                    <span class="position-absolute translate-middle p-1 ${row.isLoggedIn
                                    ? "bg-success"
                                    : "bg-secondary"
                                } border border-2 border-light rounded-circle" style="right: 8px; top: 4px !important;" 
                                      title="${row.isLoggedIn ? "Logged In" : "Not Logged In"}">
                                      <span class="visually-hidden">New alerts</span>
                                    </span>
                                <span class="Icon" id="username" title="${initials}">${initials}</span></span>
                                <span style="max-width:80px" class="text-truncate" title="${loginName}">${loginName}</span>`;
                        } else {
                            return data;
                        }
                    }
                },
                {
                    "data": "companyName", "name": "Company Name", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="align-middle" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "loginType", "name": "Authentication", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="align-middle" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "roleName", "name": "Role Type", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            if (row.roleLogo === null || row.roleLogo === '' || row.roleLogo === 'NA') {
                                return `<span class="badge text-bg-primary align-middle" title="${data || 'NA'}" style="text-align: center;">${data || 'NA'}</span>`;
                            }
                            else {
                                return `<span class="badge align-middle" title="${data || 'NA'}" style="background: ${row.roleLogo}; text-align: center;">${data || 'NA'}</span>`;
                            }
                        }
                        return data;
                    }
                },
                {
                    "data": "email", "name": "Email", "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="mt-1" title="' + (data || 'NA') + '">' + (data || 'NA') + '</span>';
                        }
                        return data;
                    }
                },
              
                {
                    "data": "loginDate", "name": "Login Data", "autoWidth": true,
                    "render": function (data, type, row) {
                        let login_date = data ? data : "NA"
                        if (type === 'display') {
                            return '<span class="mt-1" title="' + login_date + '">' + login_date + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": null,
                    "name": "State",
                    "autoWidth": true,
                    "orderable": false,
                    render: function (data, type, row) {

                        return ` 
                                <span> 
                                   <i class="cp-active-inactive text-${row.isLock ? 'danger' : 'success'}" title="${row.isLock ? 'In-Active' : 'Active'}"></i >
                                </span > `;

                    }
                },
                {
                    "orderable": false,
                    "render": (data, type, row) => {
                        const isEditAllowed = permission?.createPermission === "true";
                        const isDeleteAllowed = permission?.deletePermission === "true";
                        const role = row?.roleName;
                        const isLock = row?.isLock;
                        const userId = row?.id;
                        const loginName = row?.loginName;
                        const email = row?.userInfo?.email;
                        const encodedUser = btoa(JSON.stringify(row));

                        const lockBtn = `<span role="button" title="${isLock ? 'UnLock' : 'Lock'}" ${isLock ? `class="reset-button" data-bs-toggle="modal" data-bs-target="#ResetModal" data-user-id="${userId}" data-user-name="${loginName}" data-user-email="${email}"`
                                : `class="reset-button icon-disabled"`}> <i class="${isLock ? 'cp-open-lock' : 'cp-lock'}"></i> </span>`;

                        const getEditBtn = (enabled) => enabled
                            ? `<span role="button" title="Edit" class="edit-button" data-user='${encodedUser}'><i class="cp-edit"></i></span>`
                            : `<span role="button" title="Edit" class="icon-disabled"><i class="cp-edit"></i></span>`;

                        const getDeleteBtn = (enabled) => enabled
                            ? `<span role="button" title="Delete" class="delete-button" data-user-id="${userId}" data-user-name="${loginName}" data-bs-toggle="modal" data-bs-target="#userDeleteModal"><i class="cp-Delete"></i></span>`
                            : `<span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i></span>`;

                        let canEdit = isEditAllowed;
                        let canDelete = isDeleteAllowed;

                        if (userRoleValue === "SuperAdmin" && role === "SuperAdmin") {
                            canDelete = false;
                        } else if (userRoleValue === "SiteAdmin" && role === "SiteAdmin") {
                            canDelete = false;
                        } else if (userRoleValue === "Administrator") {
                            if (role === "SuperAdmin") {
                                canEdit = false;
                                canDelete = false;
                            } else if (role === "Administrator") {
                                canDelete = false;
                            }
                        }

                        return `<div class="d-flex align-items-center gap-2">${getEditBtn(canEdit)}${getDeleteBtn(canDelete)}${lockBtn}</div>`;
                    }
                }
            ],

            "columnDefs": [
                {
                    "targets": [1, 2, 5, 6],
                    "className": "truncate"
                }
            ],
            "rowCallback": function (row, data, index) {
                var api = this.api();
                var startIndex = api.context[0]._iDisplayStart;
                var counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },

            "createdRow": function (row) {
                $(row).find(".truncate").each(function () {
                    $(this).attr("title", this.innerText);
                });
            },
            "drawCallback": function (settings) {

                //const randomColor = () => {
                //    return "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0').toUpperCase();
                //}
                //const namelist = document.querySelectorAll("#username");
                //namelist.forEach((name) => {
                //    name.style.backgroundColor = randomColor();
                //})

                var table = $('#userTable').DataTable();
                var pageInfo = table?.page?.info();

                const userColorPallete = ['#00CCFF', '#0066FF', '#CC3399', '#FF9900', '#99CC00', '#3399FF', '#993399',
                    '#339966', '#993333', '#009900', '#000099', '#666633', '#FF3300', '#6600CC', '#C65F00', '#009974',
                    '#BEBE00', '#CC0000', '#9100C5', '#020057', '#949C3C', '#00CB82', '#418E8A', '#0099CC', '#3D50FF'
                ]

                const namelist = document.querySelectorAll("#username");
                let startIndex = pageInfo?.start;

                namelist.forEach((name, index) => {

                    let colorIndex = (startIndex + index) % userColorPallete.length;

                    if (userColorPallete[colorIndex]) {
                        name.style.backgroundColor = userColorPallete[colorIndex];
                        colorIndex++;
                    }

                });
            }

        });

    $('#userSearch').attr('autocomplete', 'off');

    $(document).on('input', '#userSearch', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        $('.filterSearch').each(function () {

            if ($(this).is(':checked')) {
                var checkboxValue = $(this).val();
                var inputValue = $('#userSearch').val();
                selectedValues.push(checkboxValue + inputValue);
            }
        });

        dataTable.ajax.reload(function (json) {

            if (json?.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500));

    var value = $('#userLoginType').val();
    ValidateLoginType(value);
 


