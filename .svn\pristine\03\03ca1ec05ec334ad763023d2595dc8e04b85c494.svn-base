﻿using ContinuityPatrol.Application.Features.GroupPolicy.Event.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;

public class CreateGroupPolicyCommandHandler : IRequestHandler<CreateGroupPolicyCommand, CreateGroupPolicyResponse>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateGroupPolicyCommandHandler(IMapper mapper, ILoggedInUserService loggedInUserService,
        IGroupPolicyRepository groupPolicyRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _groupPolicyRepository = groupPolicyRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateGroupPolicyResponse> Handle(CreateGroupPolicyCommand request,
        CancellationToken cancellationToken)
    {
        var groupPolicy = _mapper.Map<Domain.Entities.GroupPolicy>(request);

        groupPolicy.CompanyId = _loggedInUserService.CompanyId;

        groupPolicy = await _groupPolicyRepository.AddAsync(groupPolicy);

        var response = new CreateGroupPolicyResponse
        {
            Message = Message.Create("Group Node Policy", groupPolicy.GroupName),

            GroupPolicyId = groupPolicy.ReferenceId
        };

        await _publisher.Publish(new GroupPolicyCreatedEvent { GroupPolicyName = groupPolicy.GroupName },
            cancellationToken);

        return response;
    }
}