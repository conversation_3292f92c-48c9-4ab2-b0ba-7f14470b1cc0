﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetPaginatedList;

public class GetMSSQLAlwaysOnMonitorLogsPaginatedListQueryHandler : IRequestHandler<
    GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery, PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>
{
    private readonly IMapper _mapper;
    private readonly IMssqlAlwaysOnMonitorLogsRepository _mssqlAlwaysOnMonitorLogsRepository;

    public GetMSSQLAlwaysOnMonitorLogsPaginatedListQueryHandler(
        IMssqlAlwaysOnMonitorLogsRepository mssqlAlwaysOnMonitorLogsRepository, IMapper mapper)
    {
        _mssqlAlwaysOnMonitorLogsRepository = mssqlAlwaysOnMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>> Handle(
        GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery request, CancellationToken cancellationToken)
    {
        var queryable = _mssqlAlwaysOnMonitorLogsRepository.GetPaginatedQuery();

        var productFilterSpec = new MsSqlAlwaysOnMonitorLogsFilterSpecification(request.SearchString);

        var monitorLog = await queryable
            .Specify(productFilterSpec)
            .Select(m => _mapper.Map<MssqlAlwaysOnMonitorLogsListVm>(m))
            .ToPaginatedListAsync(request.PageNumber, request.PageSize);

        return monitorLog;
    }
}