﻿namespace ContinuityPatrol.Application.Features.UserLogin.Queries.GetNameUnique;

public class GetUserLoginNameUniqueQueryHandler : IRequestHandler<GetUserLoginNameUniqueQuery, bool>
{
    private readonly IMapper _mapper;
    private readonly IUserRepository _userRepository;

    public GetUserLoginNameUniqueQueryHandler(IMapper mapper, IUserRepository userRepository)
    {
        _mapper = mapper;
        _userRepository = userRepository;
    }

    public async Task<bool> Handle(GetUserLoginNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _userRepository.IsUserNameExist(request.LoginName, request.UserId);
    }
}