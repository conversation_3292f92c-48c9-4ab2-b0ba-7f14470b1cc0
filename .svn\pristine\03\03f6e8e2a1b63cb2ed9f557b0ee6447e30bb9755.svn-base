﻿
@{
    ViewData["Title"] = "InfraObjectConfigurationReport";
}

@using DevExpress.AspNetCore
@using ContinuityPatrol.Web.Areas.Report.ReportTemplate

<style>
    .form-group {
        margin-left: 550px;
        margin-top: 60px;
    }
</style>
@section Styles
    {
    <link href="~/css/viewer.part.bundle.css" rel="stylesheet" asp-append-version="true" />
    <link href="~/css/thirdparty.bundle.css" rel="stylesheet" asp-append-version="true" />
    }
@section HeaderScripts
    {
    <script src="~/js/common/thirdparty.bundle.js" asp-append-version="true"></script>
    <script src="~/js/common/viewer.part.bundle.js" asp-append-version="true"></script>
   }

<div class="card card-custom gutter-b">
    <div class="card-body p-0">
        <div id="reportContainer">
            @Html.DevExpress().WebDocumentViewer("InfraObjectConfigurationDocumentViewer").Height("1150px").Bind(new InfraObjectConfigurationReport(ViewData["InfraObjectConfigurationReportData"].ToString()))

        </div>
    </div>
</div>

@*<div class="row mt-3">
    <div class="col">
        <img src="/img/logo/cplogo.svg" height="35" />
    </div>
    <div class="col text-end">
        <img src="/img/logo/pts_logo.png" height="35" />
    </div>
    <div class="col-12">
        <div class="bg-secondary rounded-0 text-light mt-1">
            <h6 class="Report-Header text-center">InfraObject Configuration Report</h6>
        </div>
    </div>
</div>
<div class="card">
    <div>
        <div class="row mt-3 ">
            <div class="col">
                <div class="card bg-light  h-100">
                    <div class="card-header text-primary fw-bold border-bottom">
                        PR Details
                    </div>
                    <div class="p-0 card-body">
                        <div class="rounded-0 list-group">
                            <div class="d-flex justify-content-between px-3 py-2 border-top-0 list-group-item">
                                <div class="me-auto d-flex align-items-center">
                                    <i class="cp-server-role"></i>
                                    <span class="ms-1 align-middle">InfraObject Name</span>
                                </div>
                                MSSSQLAlwaysON_Infra
                            </div>
                            <li class="d-flex justify-content-between px-3 py-2 list-group-item">
                                <div class="me-auto d-flex align-items-center">
                                    <i class="cp-production-server-ip"></i>
                                    <span class="ms-1 align-middle">
                                        PR Server IP/Host
                                        Name
                                    </span>
                                </div>
                                ***********
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                                <div class="me-auto d-flex align-items-center">
                                    <i class="cp-server-type"></i>
                                    <span class="ms-1 align-middle">PR Server Name</span>
                                </div>
                                MSSQlAlwaysON_PRServer
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2 list-group-item">
                                <div class="me-auto d-flex align-items-center">
                                    <i class="cp-server-type"></i>
                                    <span class="ms-1 align-middle">PR Server DB Name</span>
                                </div>
                                MSSQLAlwaysON_PRDB
                            </li>
                            <li class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                                <div class="me-auto d-flex align-items-center">
                                    <i class="cp-server-role"></i>
                                    <span class="ms-1 align-middle">
                                        InfraObject Current
                                        Status
                                    </span>
                                </div>
                                Active
                            </li>
                        </div>
                    </div>
                </div>
            </div>
       
            <div class="col">
                    <div class="card bg-light h-100">
                    <div class ="card-header text-primary fw-bold border-bottom">
                         DR Details   
                        </div>
                        <div class="p-0 card-body">
                            <div class="rounded-0 list-group">
                            <li class="d-flex justify-content-between px-3 py-2 border-top-0 list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <i class="cp-production-server-ip"></i>
                                        <span class="ms-1 align-middle">
                                            DR Server IP/Host
                                            Name
                                        </span>
                                    </div>
                                    ***********
                                </li>
                            <li class="d-flex justify-content-between px-3 py-2  list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <i class="cp-server-type"></i>
                                        <span class="ms-1 align-middle">DR Server Name</span>
                                    </div>
                                    MSSQlAlwaysON_PRServer
                                </li>
                                <li class="d-flex justify-content-between px-3 py-2 border-0 list-group-item">
                                    <div class="me-auto d-flex align-items-center">
                                        <i class="cp-server-type"></i>
                                        <span class="ms-1 align-middle">DR Server DB Name</span>
                                    </div>
                                    MSSQLAlwaysON_PRDB
                                </li>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
           
       
    </div>
</div>
*@

