﻿namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;

public record GetInfraObjectDetailByIdVm
{
    public string InfraObjectId { get; set; }
    public string Name { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string State { get; set; }
    public bool IsDrift { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryTypeId { get; set; }
    public string ReplicationCategoryType { get; set; }
    public string SubTypeId { get; set; }
    public string NodeProperties { get; set; }
    public int ReplicationStatus { get; set; }
    public int DROperationStatus { get; set; }
    public string Reason { get; set; }
    public string TypeName { get; set; }
    public string SubType { get; set; }
    public string ServerProperties { get; set; }
    public string DatabaseProperties { get; set; }
    public string ReplicationProperties { get; set; }
    public string SiteProperties { get; set; }
    public List<ServerDto> ServerDto { get; set; } = new();
    public List<DatabaseDto> DatabaseDto { get; set; } = new();
    public List<MonitorServiceDto> MonitorServiceDto { get; set; } = new();
}

public record ServerDto
{
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string ServerType { get; set; }
    public string RoleType { get; set; }
    public string OsType { get; set; }
    public string IPAddress { get; set; }
    public string Status { get; set; }
    public string HostName { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string SiteId { get; set; }
    public string Location { get; set; }
    public string Type { get; set; }
    public string ConnectViaHostName { get; set; }
    public bool CurrentPr { get; set; }
}

public record DatabaseDto
{
    public string DatabaseId { get; set; }
    public string DatabaseName { get; set; }
    public string Version { get; set; }
    public string Status { get; set; }
    public string DatabaseType { get; set; }
    public string Type { get; set; }
    public string Sid { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
}

public class MonitorServiceDto
{
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string IpAddress { get; set; }
    public string ServerType { get; set; }
    public string Status { get; set; }
    public string IsServiceUpdate { get; set; }
    public string FailedActionId { get; set; }
    public string FailedActionName { get; set; }
    public string Type { get; set; }
}