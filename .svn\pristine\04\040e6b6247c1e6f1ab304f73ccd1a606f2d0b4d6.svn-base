﻿namespace ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Create;

public class
    CreateStateMonitorLogCommandHandler : IRequestHandler<CreateStateMonitorLogCommand, CreateStateMonitorLogResponse>
{
    private readonly IMapper _mapper;
    private readonly IStateMonitorLogRepository _stateMonitorLogRepository;

    public CreateStateMonitorLogCommandHandler(IStateMonitorLogRepository stateMonitorLogRepository, IMapper mapper)
    {
        _stateMonitorLogRepository = stateMonitorLogRepository;
        _mapper = mapper;
    }

    public async Task<CreateStateMonitorLogResponse> Handle(CreateStateMonitorLogCommand request,
        CancellationToken cancellationToken)
    {
        var stateMonitorLog = _mapper.Map<Domain.Entities.StateMonitorLog>(request);

        stateMonitorLog = await _stateMonitorLogRepository.AddAsync(stateMonitorLog);

        var response = new CreateStateMonitorLogResponse
        {
            Message = "State Monitor Log created Successfully.",
            Id = stateMonitorLog.ReferenceId
        };
        return response;
    }
}