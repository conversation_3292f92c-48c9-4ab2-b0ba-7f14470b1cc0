﻿namespace ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetMYSQLMonitorStatusByInfraObjectId;

public class
    GetMYSQLMonitorStatusByInfraObjectIdQueryHandler : IRequestHandler<GetMYSQLMonitorStatusByInfraObjectIdQuery,
        string>
{
    private readonly IMapper _mapper;
    private readonly IMysqlMonitorStatusRepository _mysqlMonitorStatusRepository;

    public GetMYSQLMonitorStatusByInfraObjectIdQueryHandler(IMapper mapper,
        IMysqlMonitorStatusRepository mysqlMonitorStatusRepository)
    {
        _mapper = mapper;
        _mysqlMonitorStatusRepository = mysqlMonitorStatusRepository;
    }

    public async Task<string> Handle(GetMYSQLMonitorStatusByInfraObjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var mysqlMonitorStatus =
            await _mysqlMonitorStatusRepository.GetMysqlMonitorStatusByInfraObjectIdAsync(request.InfraObjectId);

        Guard.Against.NullOrDeactive(mysqlMonitorStatus, nameof(Domain.Entities.MYSQLMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.MYSQLMonitorStatus), request.InfraObjectId));

        var mysqlMonitorStatusDetailDto = mysqlMonitorStatus.ReferenceId;

        return mysqlMonitorStatusDetailDto;
    }
}