﻿using ContinuityPatrol.Application.Features.LicenseInfo.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseInfo.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.LicenseInfo.Events;

public class LicenseInfoCreatedEventHandlerTests : IClassFixture<LicenseInfoFixture>
{
    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly LicenseInfoCreatedEventHandler _handler;

    public LicenseInfoCreatedEventHandlerTests(LicenseInfoFixture licenseInfoFixture)
    {
        _licenseInfoFixture = licenseInfoFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockLicenseInfoCreatedEventLogger = new Mock<ILogger<LicenseInfoCreatedEvent>>();

        _mockUserActivityRepository = LicenseInfoRepositoryMocks.CreateLicenseInfoEventRepository(_licenseInfoFixture.UserActivities);

        _handler = new LicenseInfoCreatedEventHandler(mockLoggedInUserService.Object, mockLicenseInfoCreatedEventLogger.Object, _mockUserActivityRepository.Object);

    }
    [Fact]
    public void AssignValues_ShouldSetAllPropertiesCorrectly()
    {
        // Arrange
        var command = new CreateLicenseInfoCommand
        {
            LicenseId = "LIC-001",
            PONumber = "PO12345",
            CompanyId = "COMP-789",
            BusinessServiceId = "BS-456",
            BusinessServiceName = "Database Backup",
            Category = "Infrastructure",
            EntityId = "ENT-123",
            EntityName = "Main Data Center",
            Entity = "DC-001",
            Type = "Internal",
            IpAddress = "************",
            Logo = "logo.png"
        };

        // Assert
        Assert.Equal("LIC-001", command.LicenseId);
        Assert.Equal("PO12345", command.PONumber);
        Assert.Equal("COMP-789", command.CompanyId);
        Assert.Equal("BS-456", command.BusinessServiceId);
        Assert.Equal("Database Backup", command.BusinessServiceName);
        Assert.Equal("Infrastructure", command.Category);
        Assert.Equal("ENT-123", command.EntityId);
        Assert.Equal("Main Data Center", command.EntityName);
        Assert.Equal("DC-001", command.Entity);
        Assert.Equal("Internal", command.Type);
        Assert.Equal("************", command.IpAddress);
        Assert.Equal("logo.png", command.Logo);

        // ToString assertion
        Assert.Equal("Name: Main Data Center;", command.ToString());
    }
    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateLicenseInfoEventCreated()
    {
        _licenseInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_licenseInfoFixture.LicenseInfoCreatedEvent, CancellationToken.None);

        result.Equals(_licenseInfoFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_licenseInfoFixture.LicenseInfoCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_CreateLicenseInfoEventCreated()
    {
        _licenseInfoFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_licenseInfoFixture.LicenseInfoCreatedEvent, CancellationToken.None);

        result.Equals(_licenseInfoFixture.UserActivities[0].Id);

        result.Equals(_licenseInfoFixture.LicenseInfoCreatedEvent.EntityName);

        await Task.CompletedTask;
    }
}
