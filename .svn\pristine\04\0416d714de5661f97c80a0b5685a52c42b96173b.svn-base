﻿namespace ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Create;

public class
    CreateDb2HaDrMonitorStatusCommandHandler : IRequestHandler<CreateDb2HaDrMonitorStatusCommand,
        CreateDb2HaDrMonitorStatusResponse>
{
    private readonly IDb2HaDrMonitorStatusRepository _db2HaDrMonitorStatusRepository;
    private readonly IMapper _mapper;

    public CreateDb2HaDrMonitorStatusCommandHandler(IMapper mapper,
        IDb2HaDrMonitorStatusRepository db2HaDrMonitorStatusRepository)
    {
        _mapper = mapper;
        _db2HaDrMonitorStatusRepository = db2HaDrMonitorStatusRepository;
    }

    public async Task<CreateDb2HaDrMonitorStatusResponse> Handle(CreateDb2HaDrMonitorStatusCommand request,
        CancellationToken cancellationToken)
    {
        var db2HaDrMonitorStatus = _mapper.Map<Domain.Entities.Db2HaDrMonitorStatus>(request);

        db2HaDrMonitorStatus = await _db2HaDrMonitorStatusRepository.AddAsync(db2HaDrMonitorStatus);

        var response = new CreateDb2HaDrMonitorStatusResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Db2HaDrMonitorStatus), db2HaDrMonitorStatus.ReferenceId),

            Id = db2HaDrMonitorStatus.ReferenceId
        };

        return response;
    }
}