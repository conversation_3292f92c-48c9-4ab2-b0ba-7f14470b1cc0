﻿using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.MSSQLAlwaysOnMonitorStatus.Queries;

public class GetMssqlAlwaysOnMonitorStatusListQueryHandlerTests : IClassFixture<MssqlAlwaysOnMonitorStatusFixture>
{
    private readonly MssqlAlwaysOnMonitorStatusFixture _mssqlAlwaysOnMonitorStatusFixture;

    private Mock<IMssqlAlwaysOnMonitorStatusRepository> _mockMssqlAlwaysOnMonitorStatusRepository;

    private readonly GetMSSQLAlwaysOnMonitorStatusListQueryHandler _handler;

    public GetMssqlAlwaysOnMonitorStatusListQueryHandlerTests(MssqlAlwaysOnMonitorStatusFixture mssqlAlwaysOnMonitorStatusFixture)
    {
        _mssqlAlwaysOnMonitorStatusFixture = mssqlAlwaysOnMonitorStatusFixture;

        _mockMssqlAlwaysOnMonitorStatusRepository = MssqlAlwaysOnMonitorStatusRepositoryMocks.GetMssqlAlwaysOnMonitorStatusRepository(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses);

        _handler = new GetMSSQLAlwaysOnMonitorStatusListQueryHandler(_mockMssqlAlwaysOnMonitorStatusRepository.Object, _mssqlAlwaysOnMonitorStatusFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_Valid_InfraObjectInfosList()
    {
        var result = await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<MSSQLAlwaysOnMonitorStatusListVm>>();

        result[0].Id.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].ReferenceId);
        result[0].Type.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].Type);
        result[0].InfraObjectId.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].InfraObjectName);
        result[0].WorkflowId.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].WorkflowName);
        result[0].Properties.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].Properties);
        result[0].ConfiguredRPO.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].ConfiguredRPO);
        result[0].DataLagValue.ShouldBe(_mssqlAlwaysOnMonitorStatusFixture.MssqlAlwaysOnMonitorStatuses[0].DataLagValue);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockMssqlAlwaysOnMonitorStatusRepository = MssqlAlwaysOnMonitorStatusRepositoryMocks.GetMssqlAlwaysOnMonitorStatusEmptyRepository();

        var handler = new GetMSSQLAlwaysOnMonitorStatusListQueryHandler(_mockMssqlAlwaysOnMonitorStatusRepository.Object, _mssqlAlwaysOnMonitorStatusFixture.Mapper);

        var result = await handler.Handle(new GetMSSQLAlwaysOnMonitorStatusListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetMSSQLAlwaysOnMonitorStatusListQuery(), CancellationToken.None);

        _mockMssqlAlwaysOnMonitorStatusRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}