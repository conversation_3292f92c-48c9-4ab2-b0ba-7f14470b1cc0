using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentDaily.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentDailyModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class IncidentDailyFixture : IDisposable
{
    public CreateIncidentDailyCommand CreateIncidentDailyCommand { get; }
    public UpdateIncidentDailyCommand UpdateIncidentDailyCommand { get; }
    public List<IncidentDailyListVm> IncidentDailyListVm { get; }
    public IncidentDailyDetailVm IncidentDailyDetailVm { get; }
    public PaginatedResult<IncidentDailyListVm> PaginatedIncidentDailyListVm { get; }
    public GetIncidentDailyPaginatedQuery GetIncidentDailyPaginatedQuery { get;}

    public IncidentDailyFixture()
    {
        var fixture = new Fixture();

        CreateIncidentDailyCommand = fixture.Create<CreateIncidentDailyCommand>();
        UpdateIncidentDailyCommand = fixture.Create<UpdateIncidentDailyCommand>();
        IncidentDailyListVm = fixture.Create<List<IncidentDailyListVm>>();
        IncidentDailyDetailVm = fixture.Create<IncidentDailyDetailVm>();
        PaginatedIncidentDailyListVm = fixture.Create<PaginatedResult<IncidentDailyListVm>>();
        GetIncidentDailyPaginatedQuery = fixture.Create<GetIncidentDailyPaginatedQuery>();
    }

    public void Dispose()
    {

    }
}
