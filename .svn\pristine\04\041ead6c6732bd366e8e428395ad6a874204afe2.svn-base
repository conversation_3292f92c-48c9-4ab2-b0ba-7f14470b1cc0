using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetByConfig;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetList;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BackUp.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class BackUpsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<BackUpListVm>>> GetBackUps()
    {
        Logger.LogDebug("Get All BackUps");

        return Ok(await Mediator.Send(new GetBackUpListQuery()));
    }

    [HttpGet("{id}", Name = "GetBackUp")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<BackUpDetailVm>> GetBackUpById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUp Id");

        Logger.LogDebug($"Get BackUp Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetBackUpDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Admin.View)]
 public async Task<ActionResult<PaginatedResult<BackUpListVm>>> GetPaginatedBackUps([FromQuery] GetBackUpPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in BackUp Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateBackUpResponse>> CreateBackUp([FromBody] CreateBackUpCommand createBackUpCommand)
    {
        Logger.LogDebug($"Create BackUp '{createBackUpCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateBackUp), await Mediator.Send(createBackUpCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateBackUpResponse>> UpdateBackUp([FromBody] UpdateBackUpCommand updateBackUpCommand)
    {
        Logger.LogDebug($"Update BackUp '{updateBackUpCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateBackUpCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteBackUpResponse>> DeleteBackUp(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BackUp Id");

        Logger.LogDebug($"Delete BackUp Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteBackUpCommand { Id = id }));
    }

    [HttpGet("get-config")]  
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<GetByConfigDetailVm>> GetBackUpByConfig()
    {
        Logger.LogDebug($"Get BackUp Detail by Config");

        return Ok(await Mediator.Send(new GetByConfigDetailQuery()));
    }
    [Route("backup-execute"), HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<BackUpExecuteResponse>> ExecuteBackUp([FromBody] BackUpExecuteCommand backUpExecuteCommand)
    {
        Logger.LogDebug($"Execute BackUp '{backUpExecuteCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(backUpExecuteCommand));
    }

    #region NameExist
    [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsBackUpNameExist(string backUpName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(backUpName, "BackUp Name");

     Logger.LogDebug($"Check Name Exists Detail by BackUp Name '{backUpName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetBackUpNameUniqueQuery { Name = backUpName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


