﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowProfileInfo.Events.Update;

public class WorkflowProfileInfoUpdatedEventHandler : INotificationHandler<WorkflowProfileInfoUpdatedEvent>
{
    private readonly ILogger<WorkflowProfileInfoUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowProfileInfoUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowProfileInfoUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowProfileInfoUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Action = $"{ActivityType.Update} {Modules.WorkflowProfile}",
            Entity = Modules.WorkflowProfile.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails =
                $"Workflow Profile '{updatedEvent.WorkflowName}' workflow updated in {updatedEvent.WorkflowProfileName} successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation(
            $"Workflow Profile '{updatedEvent.WorkflowName}' workflow updated to {updatedEvent.WorkflowProfileName}");
    }
}