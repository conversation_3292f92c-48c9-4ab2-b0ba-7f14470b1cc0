﻿using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertMaster.Queries;

public class GetAlertMasterDetailQueryHandlerTests : IClassFixture<AlertMasterFixture>
{
    private readonly AlertMasterFixture _alertMasterFixture;

    private readonly Mock<IAlertMasterRepository> _mockAlertMasterRepository;

    private readonly GetAlertMasterDetailQueryHandler _handler;

    public GetAlertMasterDetailQueryHandlerTests(AlertMasterFixture alertMasterFixture)
    {
        _alertMasterFixture = alertMasterFixture;

        _mockAlertMasterRepository = AlertMasterRepositoryMocks.GetAlertMasterRepository(_alertMasterFixture.AlertMasters);

        _handler = new GetAlertMasterDetailQueryHandler(_mockAlertMasterRepository.Object, _alertMasterFixture.Mapper);

        _alertMasterFixture.AlertMasters[0].ReferenceId = "70038ab5-f781-4b05-a274-b798d69975cb";
        _alertMasterFixture.AlertMasters[1].ReferenceId = "006f4ef1-1160-4a61-8605-73d0c5720abd";
        _alertMasterFixture.AlertMasters[2].ReferenceId = "b0793398-f936-4e98-a456-6a25e3c6598d";

    }


    [Fact]
    public async Task Handle_Return_AlertMasterDetails_When_ValidAlertMasterId()
    {
        var result = await _handler.Handle(new GetAlertMasterDetailQuery { Id = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<AlertMasterDetailVm>();

        result.Id.ShouldBe(_alertMasterFixture.AlertMasters[0].ReferenceId);
        result.AlertId.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertId);
        result.AlertType.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertType);
        result.AlertMessage.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertMessage);
        result.RecoveryId.ShouldBe(_alertMasterFixture.AlertMasters[0].RecoveryId);
        result.AlertPriority.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertPriority);
        result.AlertFrequency.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertFrequency);
        result.AlertSendTime.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertSendTime);
        result.IsSendEmail.ShouldBe(_alertMasterFixture.AlertMasters[0].IsSendEmail);
        result.IsSendSMS.ShouldBe(_alertMasterFixture.AlertMasters[0].IsSendSMS);
        result.AlertName.ShouldBe(_alertMasterFixture.AlertMasters[0].AlertName);
        result.EscMatId.ShouldBe(_alertMasterFixture.AlertMasters[0].EscMatId);
        result.IsAcknowledgement.ShouldBe(_alertMasterFixture.AlertMasters[0].IsAcknowledgement);
        result.ExceptionId.ShouldBe(_alertMasterFixture.AlertMasters[0].ExceptionId);
        result.IsAlertActive.ShouldBe(_alertMasterFixture.AlertMasters[0].IsAlertActive);
        result.DBType.ShouldBe(_alertMasterFixture.AlertMasters[0].DBType);
        result.IsFullDB.ShouldBe(_alertMasterFixture.AlertMasters[0].IsFullDB);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidAlertId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetAlertMasterDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetAlertMasterDetailQuery { Id = _alertMasterFixture.AlertMasters[0].ReferenceId }, CancellationToken.None);

        _mockAlertMasterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}