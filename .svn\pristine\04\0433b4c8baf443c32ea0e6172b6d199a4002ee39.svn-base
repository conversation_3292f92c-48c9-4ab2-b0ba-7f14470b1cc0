﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthStatus.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthStatus.Queries;

public class GetBusinessServiceHealthStatusDetailQueryHandlerTests : IClassFixture<BusinessServiceHealthStatusFixture>
{
    private readonly BusinessServiceHealthStatusFixture _businessServiceHealthStatusFixture;

    private readonly Mock<IBusinessServiceHealthStatusRepository> _businessServiceHealthStatusRepositoryMock;

    private readonly GetBusinessServiceHealthStatusDetailQueryHandler _handler;

    public GetBusinessServiceHealthStatusDetailQueryHandlerTests(BusinessServiceHealthStatusFixture businessServiceHealthStatusFixture)
    {
        _businessServiceHealthStatusFixture = businessServiceHealthStatusFixture;
    
        _businessServiceHealthStatusRepositoryMock = BusinessServiceHealthStatusRepositoryMocks.GetBusinessServiceHealthStatusRepository(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList);
        
        _handler = new GetBusinessServiceHealthStatusDetailQueryHandler(_businessServiceHealthStatusRepositoryMock.Object, _businessServiceHealthStatusFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceHealthStatusDetails_When_ValidBusinessServiceHealthStatusId()
    {
        var result = await _handler.Handle(new GetBusinessServiceHealthStatusDetailQuery
                { Id = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<BusinessServiceHealthStatusDetailVm>();

        result.Id.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId);
        result.ConfiguredCount.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ConfiguredCount);
        result.DRReadyCount.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].DRReadyCount);
        result.DRNotReadyCount.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].DRNotReadyCount);
        result.ProblemState.ShouldBe(_businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ProblemState);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessServiceHealthStatusId()
    {
        var result = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetBusinessServiceHealthStatusDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        result.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceHealthStatusDetailQuery
        { Id = _businessServiceHealthStatusFixture.BusinessServiceHealthStatusList[0].ReferenceId }, CancellationToken.None);

        _businessServiceHealthStatusRepositoryMock.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}