﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
@Html.AntiForgeryToken()

<link href="~/css/dashboard.css" rel="stylesheet" />

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px;background-color: #f5f5f7;">
        <h6 class="page_title" title=" MSSQL AlwaysOn Monitoring">
            <i class="cp-monitoring"></i><span>
                MSSQL AlwaysOn Detail Monitoring:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div id="noDataimg" class="monitor_pages mb-2" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 104px);">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6">
                <div class="card Card_Design_None mb-2">
                    @* <div title="MSSQL AlwaysOn Availability Group Summary" class="card-header">MSSQL AlwaysOn Availability Group Summary</div> *@
                    <div class="card-body pt-0 p-2">
                        <table id="availability" class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Availability Group Summary">Availability Group Summary</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th title="DR" class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td title="Instance Name" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-instance-name me-1 fs-6"></i>Instance Name
                                    </td>
                                    <td class="text-truncate"> <span id="PR_Instance_Name"></span></td>
                                    <td class="text-truncate"> <span id="DR_Instance_Name"></span></td>
                                </tr>
                                <tr>
                                    <td title="Availability Group Name" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-form-name me-1 fs-6"></i>Availability Group Name
                                    </td>
                                    <td class="text-truncate">@* <i class="text-primary cp-name  me-1 fs-6"></i> *@<span id="PR_Availability_Group_Name"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-name  me-1 fs-6"></i> *@<span id="DR_Availability_Group_Name"></span></td>
                                </tr>
                                <tr>
                                    <td title="Availability Group Role" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-user-role me-1 fs-6"></i>Availability Group Role
                                    </td>
                                    <td class="text-truncate"><span id="PR_Availability_Group_Role"></span></td>
                                    <td class="text-truncate"><span id="DR_Availability_Group_Role"></span></td>
                                </tr>
                                <tr>
                                    <td title="Replication Mode" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-archive-mode me-1 fs-6"></i>Replication Mode
                                    </td>
                                    <td class="text-truncate"><span class="text-truncate d-inline-block" style="max-width:90%" id="PR_Availability_Mode"></span></td>
                                    <td class="text-truncate"><span class="text-truncate d-inline-block" style="max-width:90%" id="DR_Availability_Mode"></span></td>
                                </tr>
                                <tr>
                                    <td title="Failover Mode Setting" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-left-right me-1 fs-6"></i>Failover Mode Setting
                                    </td>
                                    <td class="text-truncate"><span id="PR_Failover_Mode_Setting"></span></td>
                                    <td class="text-truncate"><span id="DR_Failover_Mode_Setting"></span></td>
                                </tr>
                                <tr>
                                    <td title="Primary/Secondary Role Allow Connections" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-cluster-database me-1 fs-6"></i>Primary/Secondary Role Allow Connections
                                    </td>
                                    <td class="text-truncate">@* <i class="text-primary cp-cluster-database  me-1 fs-6 "></i> *@<span id="PR_Role_Allow_Connections"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-cluster-database  me-1 fs-6"></i> *@<span id="DR_Role_Allow_Connections"></span></td>
                                </tr>
                                <tr>
                                    <td title="Availability Group Operational State" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-online me-1 fs-6"></i>Availability Group Operational State
                                    </td>
                                    <td class="text-truncate"><span id="PR_Availability_Group_Operational_State"></span></td>
                                    <td class="text-truncate"><span id="DR_Availability_Group_Operational_State"></span></td>
                                </tr>
                                <tr>
                                    <td title="Availability Group Connected State" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-connected me-1 fs-6"></i>Availability Group Connected State
                                    </td>
                                    <td class="text-truncate"><span id="PR_Availability_Group_Connnected_State"></span></td>
                                    <td class="text-truncate"><span id="DR_Availability_Group_Connected_State"></span></td>
                                </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="row">
                    <div class="d-grid col-lg-12 ">
                        <div class="card Card_Design_None mb-2">
                            <div class="card-header card-title" title="Solution Diagram">Solution Diagram</div>
                            <div class="d-grid card-body">
                                <div id="Solution_Diagram" class="w-100 h-100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-2">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    @* <div class="card-header" title="Database Level Summary">Database Level Summary</div> *@
                    <div class="card-body pt-0 p-2">
                        <table id="dbLevelSummary" class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th title="Database Level Summary">Database Level Summary</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th title="DR" class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td title="Database Name" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-database me-1 fs-6"></i>Database Name
                                    </td>
                                    <td class="text-truncate"><span id="PR_Database_Name"></span></td>
                                    <td class="text-truncate"><span id="DR_Database_Name"></span></td>
                                </tr>
                                <tr>
                                    <td title="Database Synchronization State" class="fw-semibold text-truncate">
                                        <span>
                                            <i class="text-secondary cp-mysql-data me-1 fs-6"></i>Database Synchronization State
                                        </span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_DataBase_Synchroniztion_State"></span></td>
                                    <td class="text-truncate"><span id="DR_DataBase_Synchroniztion_State"></span></td>
                                </tr>
                                <tr>
                                    <td title="Database Synchronization Health State" class="fw-semibold text-truncate">
                                        <span>
                                            <i class="text-secondary cp-mysql-data me-1 fs-6"></i>Database Synchronization Health State
                                        </span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_DataBase_Synchroniztion_Health_Status"></span></td>
                                    <td class="text-truncate"><span id="DR_DataBase_Synchroniztion_Health_Status"></span></td>

                                </tr>
                                <tr>
                                    <td title="Database State" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-mysql-data me-1 fs-6"></i>Database State
                                    </td>
                                    <td class="text-truncate"><span id="PR_DataBase_State"></span></td>
                                    <td class="text-truncate"><span id="DR_DataBase_State"></span></td>
                                </tr>
                                <tr>
                                    <td title="Data Synchronization State on Availability" class="fw-semibold text-truncate">
                                        <span>
                                            <i class="text-secondary cp-mysql-data me-1 fs-6"></i>Data Synchronization State on Availability
                                        </span>
                                    </td>
                                    <td class="text-truncate"><span id="PR_DataSync_State_Availability_Database"></span></td>
                                    <td class="text-truncate"><span id="DR_DataSync_State_Availability_Database"></span></td>
                                </tr>
                                <tr>
                                    <td title="Endpoint Port Number" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-endpoint-port-number me-1 fs-6"></i>Endpoint Port Number
                                    </td>
                                    <td class="text-truncate">@* <i class="text-primary cp-endpoint-port-number  me-1 fs-6"></i> *@<span id="PR_Endpoint_Port_Number"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-endpoint-port-number  me-1 fs-6"></i> *@<span id="DR_Endpoint_Port_Number"></span></td>
                                </tr>

                            </tbody>
                        </table>

                    </div>
                </div>
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>
                           @*  <thead>
                                <tr>
                                    <th title="Service / Process / Workflow Name">Service / Process / Workflow Name</th>
                                    <th class="text-primary" title="Server IP/HostName">Server IP/HostName</th>
                                    <th class="text-info" title="Status">Status</th>
                                </tr>
                            </thead> *@
                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6">
                <div class="card Card_Design_None  h-100">
                    @* <div class="card-header" title="Database Replication LSN Summary">Database Replication LSN Summary</div> *@
                    <div class="card-body pt-0 p-2">
                        <table id="dbReplicaLSN" class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th class="text-truncate" title="Database Replication LSN Summary">Database Replication LSN Summary</th>
                                    <th title="Primary" class="text-primary">Primary</th>
                                    <th title="DR" class="text-info dynamicSite-header">DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td title="Last Send LSN" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Send LSN
                                    </td>
                                    <td class="text-truncate"><span id="PR_Last_Sent_LSN"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Sent_LSN"></span></td>
                                </tr>
                                <tr>
                                    <td title="Last Received LSN" class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Received LSN
                                    </td>
                                    <td class="text-truncate"><span id="PR_Last_Received_LSN"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Received_LSN"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Redone LSN</td>
                                    <td class="text-truncate"><span id="PR_Last_Redone_LSN"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Redone_LSN"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Commit LSN
                                    </td>
                                    <td class="text-truncate"><span id="PR_Last_Commit_LSN"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Commit_LSN"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Last Hardened LSN
                                    </td>
                                    <td class="text-truncate"><span id="PR_Last_Hardened_Lsn"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Hardened_Lsn"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-table-clock me-1 fs-6"></i>Last Sent Time</td>
                                    <td class="text-truncate"><span id="PR_Last_Sent_Time"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Sent_Time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-table-clock me-1 fs-6"></i>Last Received Time</td>
                                    <td class="text-truncate"><span id="PR_Last_Received_Time"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Received_Time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-table-clock me-1 fs-6"></i>Last Redone Time</td>
                                    <td class="text-truncate"><span id="PR_Last_Redone_Time"></span></td>
                                    <td class="text-truncate"><span id="DR_Last_Redone_Time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-table-clock me-1 fs-6"></i>Last Commit Time</td>
                                    <td class="text-truncate">@* <i class="text-primary cp-table-clock me-1 fs-6"></i> *@<span id="PR_Last_Commit_Time"></span></td>
                                    <td class="text-truncate">@* <i class="text-primary cp-table-clock me-1 fs-6"></i> *@ <span id="DR_Last_Commit_Time"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-log-file-name me-1 fs-6"></i>Log Sent Queue Size (KB)</td>
                                    <td class="text-truncate"><span id="PR_Log_Send_Queue_Size"></span></td>
                                    <td class="text-truncate"><span id="DR_Log_Send_Queue_Size"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-last-copied-transaction me-1 fs-6"></i>Redo Queue Size (KB)</td>
                                    <td class="text-truncate"><span id="PR_Redo_Queue_Size"></span></td>
                                    <td class="text-truncate"><span id="DR_Redo_Queue_Size"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"> <i class="text-secondary cp-apply-lag me-1 fs-6"></i>DataLag (HH:MM )</td>
                                    <td class="text-truncate"> @* <i class="cp-time text-primary me-1 fs-6"></i> *@ <span id="PR_Datalag"></span></td>
                                    @*<td id="drDatalag">NA</td>*@
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
@* <script src="~/js/monitoring/mssqlalwayson.js"></script> *@
<script src="~/js/Monitoring/MonitoringMSSQLAlwaysOn.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>