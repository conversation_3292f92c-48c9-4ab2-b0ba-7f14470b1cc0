﻿using ContinuityPatrol.Domain.ViewModels.OracleRACMonitorStatusModel;

namespace ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Queries.GetList;

public class
    GetOracleRACStatusListQueryHandler : IRequestHandler<GetOracleRACStatusListQuery,
        List<OracleRACMonitorStatusListVm>>
{
    private readonly IMapper _mapper;
    private readonly IOracleRacMonitorStatusRepository _oracleRacMonitorStatusRepository;

    public GetOracleRACStatusListQueryHandler(IOracleRacMonitorStatusRepository oracleRacMonitorStatusRepository,
        IMapper mapper)
    {
        _oracleRacMonitorStatusRepository = oracleRacMonitorStatusRepository;
        _mapper = mapper;
    }

    public async Task<List<OracleRACMonitorStatusListVm>> Handle(GetOracleRACStatusListQuery request,
        CancellationToken cancellationToken)
    {
        var oracleMonitorStatus = await _oracleRacMonitorStatusRepository.ListAllAsync();

        return oracleMonitorStatus.Count <= 0
            ? new List<OracleRACMonitorStatusListVm>()
            : _mapper.Map<List<OracleRACMonitorStatusListVm>>(oracleMonitorStatus);
    }
}