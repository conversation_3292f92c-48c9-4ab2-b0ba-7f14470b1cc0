﻿using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.OracleMonitorLogs.Queries;
public class GetOracleMonitorLogsDetailByTypeQueryHandlerTests
{
    private readonly Mock<IOracleMonitorLogsRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetOracleMonitorLogsDetailByTypeQueryHandler _handler;

    public GetOracleMonitorLogsDetailByTypeQueryHandlerTests()
    {
        var logs = new List<Domain.Entities.OracleMonitorLogs>();
        var log = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = "ref-001",
            WorkflowName = "Test message",
            IsActive = true
        };
        _repositoryMock = new Mock<IOracleMonitorLogsRepository>();
        _mapperMock = new Mock<IMapper>();
        _repositoryMock = OracleMonitorLogsRepositoryMocks.GetOracleMonitorLogsDetailByTypeRepository(logs);
        _handler = new GetOracleMonitorLogsDetailByTypeQueryHandler(_repositoryMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedList_WhenLogsExist()
    {
        // Arrange
        var type = "Oracle";
        var logs = new List<Domain.Entities.OracleMonitorLogs>
            {
                new Domain.Entities.OracleMonitorLogs { ReferenceId = "id1", Type = "Oracle" }
            };

        var expectedVmList = new List<OracleMonitorLogsDetailByTypeVm>
            {
                new OracleMonitorLogsDetailByTypeVm { Id = "id1", Type = "Oracle" }
            };

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(logs);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(logs)).Returns(expectedVmList);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be("id1");

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(logs), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsEmpty()
    {
        // Arrange
        var type = "Oracle";
        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(new List<Domain.Entities.OracleMonitorLogs>());

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(It.IsAny<List<Domain.Entities.OracleMonitorLogs>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsNull()
    {
        // Arrange
        var type = "Oracle";
        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync((List<Domain.Entities.OracleMonitorLogs>)null!);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(It.IsAny<List<Domain.Entities.OracleMonitorLogs>>()), Times.Never);
    }
    [Fact]
    public async Task Handle_ShouldReturnMultipleMappedResults_WhenMultipleLogsExist()
    {
        // Arrange
        var type = "Oracle";
        var logs = new List<Domain.Entities.OracleMonitorLogs>
        {
            new() { ReferenceId = "id1", Type = "Oracle" },
            new() { ReferenceId = "id2", Type = "Oracle" }
        };

        var expectedVms = new List<OracleMonitorLogsDetailByTypeVm>
        {
            new() { Id = "id1", Type = "Oracle" },
            new() { Id = "id2", Type = "Oracle" }
        };

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(logs);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(logs)).Returns(expectedVms);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull().And.HaveCount(2);
        result.Select(r => r.Id).Should().BeEquivalentTo(new[] { "id1", "id2" });

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(logs), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldRespectCaseSensitivity_WhenTypeDiffers()
    {
        // Arrange
        var requestType = "oracle"; // lowercase
        var logs = new List<Domain.Entities.OracleMonitorLogs>(); // simulate repository returning none for lowercase type

        _repositoryMock.Setup(r => r.GetDetailByType(requestType)).ReturnsAsync(logs);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = requestType };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(requestType), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(It.IsAny<List<Domain.Entities.OracleMonitorLogs>>()), Times.Never);
    }
    [Fact]
    public async Task Handle_ShouldRespectCancellationToken()
    {
        // Arrange
        var cts = new CancellationTokenSource();
        var token = cts.Token;
        var type = "Oracle";
        var logs = new List<Domain.Entities.OracleMonitorLogs>
        {
            new() { ReferenceId = "id1", Type = "Oracle" }
        };
        var mapped = new List<OracleMonitorLogsDetailByTypeVm>
        {
            new() { Id = "id1", Type = "Oracle" }
        };

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(logs);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorLogsDetailByTypeVm>>(logs)).Returns(mapped);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, token);

        // Assert
        result.Should().NotBeNull().And.HaveCount(1);
        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldNotThrow_WhenQueryTypeIsEmptyString()
    {
        // Arrange
        var type = string.Empty;
        var logs = new List<Domain.Entities.OracleMonitorLogs>();

        _repositoryMock.Setup(r => r.GetDetailByType(type)).ReturnsAsync(logs);

        var request = new GetOracleMonitorLogsDetailByTypeQuery { Type = type };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.GetDetailByType(type), Times.Once);
    }
    [Fact]
    public void OracleMonitorLogsDetailByTypeVm_Should_Assign_All_Properties_Correctly()
    {
        // Arrange & Act
        var vm = new OracleMonitorLogsDetailByTypeVm
        {
            Id = "detail001",
            Type = "RAC",
            InfraObjectId = "infra789",
            InfraObjectName = "Oracle RAC Cluster",
            WorkflowId = "wf999",
            WorkflowName = "Log Monitoring",
            Properties = "{\"Status\":\"Healthy\"}",
            ConfiguredRPO = "30min",
            DataLagValue = "25min"
        };

        // Assert
        Assert.Equal("detail001", vm.Id);
        Assert.Equal("RAC", vm.Type);
        Assert.Equal("infra789", vm.InfraObjectId);
        Assert.Equal("Oracle RAC Cluster", vm.InfraObjectName);
        Assert.Equal("wf999", vm.WorkflowId);
        Assert.Equal("Log Monitoring", vm.WorkflowName);
        Assert.Equal("{\"Status\":\"Healthy\"}", vm.Properties);
        Assert.Equal("30min", vm.ConfiguredRPO);
        Assert.Equal("25min", vm.DataLagValue);
    }

}
