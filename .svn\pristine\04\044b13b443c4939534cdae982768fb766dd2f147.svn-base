﻿namespace ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetByType;

public class GetPostgresMonitorLogsDetailByTypeQueryHandler : IRequestHandler<GetPostgresMonitorLogsDetailByTypeQuery,
    List<PostgresMonitorLogsDetailByTypeVm>>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorLogsRepository _postgresMonitorLogsRepository;

    public GetPostgresMonitorLogsDetailByTypeQueryHandler(IPostgresMonitorLogsRepository postgresMonitorLogsRepository,
        IMapper mapper)
    {
        _postgresMonitorLogsRepository = postgresMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<List<PostgresMonitorLogsDetailByTypeVm>> Handle(GetPostgresMonitorLogsDetailByTypeQuery request,
        CancellationToken cancellationToken)
    {
        var postgresMonitorLogs = await _postgresMonitorLogsRepository.GetDetailByType(request.Type);

        return postgresMonitorLogs.Count <= 0
            ? new List<PostgresMonitorLogsDetailByTypeVm>()
            : _mapper.Map<List<PostgresMonitorLogsDetailByTypeVm>>(postgresMonitorLogs);
    }
}