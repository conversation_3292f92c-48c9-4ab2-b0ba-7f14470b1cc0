﻿using ContinuityPatrol.Application.Features.CredentialProfile.Events.Update;
using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;

public class
    UpdateCredentialProfileCommandHandler : IRequestHandler<UpdateCredentialProfileCommand,
        UpdateCredentialProfileResponse>
{
    private readonly ICredentialProfileRepository _credentialProfileRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateCredentialProfileCommandHandler(IMapper mapper,
        ICredentialProfileRepository credentialProfileRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _credentialProfileRepository = credentialProfileRepository;
    }

    public async Task<UpdateCredentialProfileResponse> Handle(UpdateCredentialProfileCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _credentialProfileRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.CredentialProfile), request.Id);

        request.Properties = GetJsonProperties.PasswordEncryption(request.Properties);

        _mapper.Map(request, eventToUpdate, typeof(UpdateCredentialProfileCommand),
            typeof(Domain.Entities.CredentialProfile));

        await _credentialProfileRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateCredentialProfileResponse
        {
            Message = Message.Update(nameof(Domain.Entities.CredentialProfile), eventToUpdate.Name),

            CredentialProfileId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new CredentialProfileUpdatedEvent { CredentialProfileName = eventToUpdate.Name },
            cancellationToken);

        return response;
    }
}