﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Create;
using ContinuityPatrol.Application.Features.CGExecutionReport.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.CGExecutionReportModel;

namespace ContinuityPatrol.Application.Mappings;

public class CGExecutionReportProfile :Profile
{
    public CGExecutionReportProfile()
    {
        CreateMap<RpForVmCgEnableDisableStatus, CreateCGExecutionCommand>().ReverseMap();

        CreateMap<UpdateCGExecutionCommand, RpForVmCgEnableDisableStatus>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<CreateCGExecutionCommand, CGExecutionReportViewModel>().ReverseMap();
        CreateMap<UpdateCGExecutionCommand, CGExecutionReportViewModel>().ReverseMap();
    }
   
}
