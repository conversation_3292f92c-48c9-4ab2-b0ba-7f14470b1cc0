﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetRunningUserList;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Orchestration;

public class WorkflowOperationService : BaseClient, IWorkflowOperationService
{
    public WorkflowOperationService(IConfiguration config, IAppCache cache, ILogger<WorkflowOperationService> logger) : base(config, cache, logger)
    {
    }

    public async Task<List<WorkflowOperationListVm>> GetWorkflowOperationList()
    {
        var request = new RestRequest("api/v6/workflowoperation");

        return await GetFromCache<List<WorkflowOperationListVm>>(request, "GetWorkflowOperationList");
    }

    public async Task<WorkflowOperationDetailVm> GetWorkflowOperationById(string id)
    {
        var request = new RestRequest($"api/v6/workflowoperation/{id}");

        return await Get<WorkflowOperationDetailVm>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowOperationCommand createWorkflowOperationCommand)
    {
        var request = new RestRequest("api/v6/workflowoperation", Method.Post);

        request.AddJsonBody(createWorkflowOperationCommand);

        ClearCache("GetWorkflowOperationGroupList");

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string templateId)
    {
        var request = new RestRequest($"api/v6/workflowoperation/{templateId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowOperationCommand updateWorkflowOperationCommand)
    {
        var request = new RestRequest("api/v6/workflowoperation", Method.Put);

        request.AddJsonBody(updateWorkflowOperationCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<WorkflowOperationRunningUserListVm>> GetWorkflowOperationByRunningUserList()
    {
        var request = new RestRequest("api/v6/workflowoperation/runningusers");

        return await GetFromCache<List<WorkflowOperationRunningUserListVm>>(request, "Get All WorkflowOperation Running User List");
    }

    public async Task<ProfileExecutorByBusinessServiceIdVm> GetProfileExecutorByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/workflowoperation/profileexecuted?businessServiceId={businessServiceId}");

        return await Get<ProfileExecutorByBusinessServiceIdVm>(request);
    }

    public async Task<List<GetDescriptionByStartTimeAndEndTimeListVm>> GetDescriptionByStartTimeAndEndTime(string startTime, string endTime,string? runMode)
    {
        var request = new RestRequest($"api/v6/workflowoperation/starttime-endtime?startTime={startTime} & endTime={endTime}&runMode={runMode} ");

        return await Get<List<GetDescriptionByStartTimeAndEndTimeListVm>>(request);
    }

    public async Task<PaginatedResult<WorkflowOperationListVm>> GetPaginatedWorkflowOperation(GetWorkflowOperationPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowoperation/paginated-list");

        return await Get<PaginatedResult<WorkflowOperationListVm>>(request);
    }

    public async Task<List<WorkflowOperationDrDrillVm>> GetDrDrillDetailsByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/workflowoperation/drdrilldetails?businessServiceId={businessServiceId}");

        return await Get<List<WorkflowOperationDrDrillVm>>(request);
    }
}