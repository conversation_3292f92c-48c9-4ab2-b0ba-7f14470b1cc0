﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class ReportRepository : BaseRepository<Report>, IReportRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public ReportRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) :
        base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public Task<bool> IsReportNameExist(string name, string id)
    {
        return !id.IsValidGuid()
            ? Task.FromResult(_dbContext.Reports.Any(e => e.Name.Equals(name)))
            : Task.FromResult(_dbContext.Reports.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsReportNameUnique(string name)
    {
        var matches = _dbContext.Reports.Any(e => e.Name.Equals(name));

        return Task.FromResult(matches);
    }

    public Task<List<Report>> GetReportNames()
    {
        if (!_loggedInUserService.IsParent)
            return _dbContext.Reports.Active()
                //.Where(x => x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Select(x => new Report { ReferenceId = x.ReferenceId, Name = x.Name })
                .OrderBy(x => x.Name)
                .ToListAsync();
        return _dbContext.Reports
            .Active()
            .Select(x => new Report { ReferenceId = x.ReferenceId, Name = x.Name })
            .OrderBy(x => x.Name)
            .ToListAsync();
    }
}