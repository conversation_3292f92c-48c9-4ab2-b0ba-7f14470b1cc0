{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "None",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "None",
      "Microsoft.EntityFrameworkCore": "None"
    }
  },
  "Twilio": {
    "AccountSid": "**********************************",
    "AuthToken": "7b4ae07e897f2140c650c23ee6763606"
  },
  "ApiSettings": {
    "ApiUrl": "https://***********:1000/"
  },
  "WindowService": {
    "ServiceUrl": "https://localhost:7162/LoadBalancer/"
  },
  "ConnectionStrings": {
    "DBProvider": "905clI+aAqPTFEWo83NkxQ==",
    "Default": "j3c5xNRzXGXKFhDD6VArNrT33QfU/aSaS3PS7SHlKIzz5Hjiyfxx6jn6WzehjOk8kbSl/QacoepazEB2a2xkrM19zL8WDGlU1+9rBnPGbYX79w+Hy7g5gIFRpx1SFcuLzTDqiFkllDu6L9I9uYvbw/DqbeV5O/6EMWWOyvcx29jDoXaZx70EG1vwDA5eQ3yy"
  },
  //"ConnectionStrings": {
  //  //Testing URL
  //  "DBProvider": "Cjvj+C5REKpqaTGXSPTvyg==",
  //  "Default": "z85dSEgMCJSV8p7gCMQhzmKR+WSwx0hrYxID2jT/bluZOugpHWz1KR46Plzl2y4TwozLAXA3N3SCIDVvSYD9wY5ytUr6shMvXncPxaqoo82k63T2jS619DHlEFnKfpYEbPjpGXiTmbDUvlJooZXKqo1MCvxMmhJZEmuT/2eY8eI9MK0jHhj+8Pb6r280Uprf"
  //},
  "DataProvider": {
    "Default": "db"
  },
  "CacheSettings": {
    "SlidingExpiration": 60
  },
  "JwtSettings": {
    "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
    "Issuer": "http://localhost:8047",
    "Audience": "http://localhost:8047",
    "DurationInMinutes": 60
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.File",
      "Serilog.Enrichers.ClientInfo"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error"
      }
    },
    "CacheSettings": {
      "SlidingExpiration": 60
    },
    "JwtSettings": {
      "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
      "Issuer": "http://localhost:8047",
      "Audience": "http://localhost:8047",
      "DurationInMinutes": 60
    },
    "Policies": {
      "Default": "localhost"
    },
    "App": {
      "CorsOrigins": "http://localhost:3000,http://localhost:3001,http://subdomain1.localhost:3000,http://subdomain2.localhost:3000"
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\CP\\Logs\\Web\\CP_Web_log-.txt",
          "fileSizeLimitBytes": "524288000",
          "rollOnFileSizeLimit": true,
          "retainedFileCountLimit": null,
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": [
      "WithClientIp",
      "WithMachineName",
      "WithThreadId"
    ]
  },
  "RedisCacheUrl": "127.0.0.1:6379,abortConnect=false,connectTimeout=30000,responseTimeout=30000",
  "AllowedHosts": "*"
}