﻿using ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class GroupPolicyService :  IGroupPolicyService
{
    private readonly IBaseClient _client;

    public GroupPolicyService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<List<GroupPolicyListVm>> GetGroupPolicies()
    {
        var request = new RestRequest("api/v6/grouppolicy");

        return await  _client. GetFromCache<List<GroupPolicyListVm>>(request, "GetGroupPolicies");
    }

    public async Task<BaseResponse> CreateAsync(CreateGroupPolicyCommand createGroupPolicyCommand)
    {
        var request = new RestRequest("api/v6/grouppolicy", Method.Post);

        request.AddJsonBody(createGroupPolicyCommand);

        return await  _client. Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateGroupPolicyCommand updateGroupPolicyCommand)
    {
        var request = new RestRequest("api/v6/grouppolicy", Method.Put);

        request.AddJsonBody(updateGroupPolicyCommand);

        return await  _client. Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string formId)
    {
        var request = new RestRequest($"api/v6/grouppolicy/{formId}", Method.Delete);

        return await  _client. Delete<BaseResponse>(request);
    }
    public async Task<GetGroupPolicyDetailVm> GetGroupPolicyById(string formId)
    {
        var request = new RestRequest($"api/v6/grouppolicy/{formId}");

        return await  _client. Get<GetGroupPolicyDetailVm>(request);
    }

    public async Task<List<GroupPolicyNameVm>> GetGroupPolicyNames()
    {
        var request = new RestRequest("api/v6/grouppolicy/names");

        return await  _client. GetFromCache<List<GroupPolicyNameVm>>(request, "GetGroupPolicyNames");
    }

    public async Task<bool> IsGroupPolicyNameExist(string groupPolicyName, string? id)
    {
        var request = new RestRequest($"api/v6/grouppolicy/name-exist?groupPolicyName={groupPolicyName}&id={id}");

        return await  _client. Get<bool>(request);
    }

    public async Task<PaginatedResult<GroupPolicyListVm>> GetPaginatedGroupPolicies(GetGroupPolicyPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/grouppolicy/paginated-list");

        return await  _client. Get<PaginatedResult<GroupPolicyListVm>>(request);
    }

    public async Task<List<GroupPolicyTypeVm>> GetByType(string type)
    {
        var request = new RestRequest($"api/v6/grouppolicy/by/type?type={type}");

        return await  _client. Get<List<GroupPolicyTypeVm>>(request);
    }
}