﻿using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;

namespace ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetList;

public class GetIncidenLogsListQueryHandler : IRequestHandler<GetIncidenLogsListQuery, List<IncidentLogsListVm>>
{
    private readonly IIncidentLogsRepository _incidentLogsRepository;
    private readonly IMapper _mapper;

    public GetIncidenLogsListQueryHandler(IMapper mapper, IIncidentLogsRepository incidentLogsRepository)
    {
        _mapper = mapper;
        _incidentLogsRepository = incidentLogsRepository;
    }

    public async Task<List<IncidentLogsListVm>> Handle(GetIncidenLogsListQuery request,
        CancellationToken cancellationToken)
    {
        var incidentLogs = await _incidentLogsRepository.ListAllAsync();

        return _mapper.Map<List<IncidentLogsListVm>>(incidentLogs);
    }
}