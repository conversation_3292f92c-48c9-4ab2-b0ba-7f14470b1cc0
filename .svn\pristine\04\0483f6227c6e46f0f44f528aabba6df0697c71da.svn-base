﻿$("#versionText").hide()
$('#orchestration-dropdown').children().first().find('.nav-link').addClass('active')
$('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').parent().hide();
$('#WFLoader').hide()
let globalWorkflowArray = [];
let isRunning = false;
let runningCount = '';
let GlobalWorkflowId = "";
let GlobalWorkflowName = "";
let GlobalIsLock = "";
let GlobalIsPublish = "";
let GlobalIsVerify = false;
let GlobalIsDraft = false
let workflowSaveMode = "Save";
let totalEstimatedRto = 0
let workflowEditable = false;
let isTemplateWorkFlow = false;
let isRename = false;
let isWaitForParallelExist = true;
let updateMode = 'update';
let actionArray = [];
let groupArray = [];
let conditionalArray = [];
let globalGroupArray = [];
let unverifyActionComponents = [];
let serverExistByImport = [];

var createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
var deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();

if (createPermission == 'false') {
    $(".Workflow-Tree details").css('cursor', 'not-allowed').css('opacity', '0.5').css('pointer-events', 'none')
    $(".nodeSummaryData").removeClass('actiontype')

    $("#btnNewWorkflow, #btnWorkflowRename, #btnSaveModalOpen, #btnSaveAsModal, #btnWorkflowLock, #btnWorkflowVerify, #btnVersionManage, #workflowAttach, #btnWorkflowPublish, #btnMarkAction, #btnExtract, #btnGenerateTemplate, #btnRestoreTemplate, #btnImportWorkFlow, #btnExportWorkFlow, #btnWorkFlowReport ")
        .removeAttr('data-bs-toggle')
        .removeAttr('data-bs-target')
        .css({
            'cursor': 'not-allowed',
            'opacity': '0.5',
            'pointer-events': 'none'
        })
        .addClass('btn-disabled');
}
if (deletePermission == 'false') {
    $("#btnWorkFlowDelete")
        .removeAttr('data-bs-toggle')
        .removeAttr('data-bs-target')
        .css({
            'cursor': 'not-allowed',
            'opacity': '0.5',
            'pointer-events': 'none'
        })
        .addClass('btn-disabled');
}

let Urls = {
    "create": "ITAutomation/WorkflowConfiguration/CreateOrUpdate",
    "getWorkFlowById": "ITAutomation/WorkflowConfiguration/GetWorkflowById",
    "attachInfra": "ITAutomation/WorkflowConfiguration/AttachInfraObject",
    "checkInfraAttach": "ITAutomation/WorkflowConfiguration/CheckInfraObjectAttachByWorkflowId",
    "checkProfileAttach": "ITAutomation/WorkflowConfiguration/CheckProfileAttachedByWorkflowId",
    "GetInfraDetails": "ITAutomation/WorkflowConfiguration/GetWorkflowInfraObjectByWorkflowId",
    "DetachInfraObject": "ITAutomation/WorkflowConfiguration/DetachInfraObject",
    "workflowLock": "ITAutomation/WorkflowConfiguration/UpdateWorkflowLock",
    "workflowPublish": "ITAutomation/WorkflowConfiguration/UpdateWorkflowPublish",
    "workflowDcrypt": "ITAutomation/WorkflowConfiguration/WorkFlowDataDecrypt",
    "workflowEncrypt": "ITAutomation/WorkflowConfiguration/WorkFlowDataEncrypt",
    "createTemplate": "ITAutomation/WorkflowConfiguration/CreateTemplate",
    "workflowExecutionTemp": "ITAutomation/WorkflowConfiguration/CreateWorkflowExecutionTemp",
    "CreateWorkflowOperationGroup": "ITAutomation/WorkflowConfiguration/CreateWorkflowOperationGroup",
    "workflowSaveAs": "ITAutomation/WorkflowConfiguration/WorkflowSaveAs",
    "getTemplateList": "ITAutomation/WorkflowConfiguration/GetTemplateList",
    //"GetOperationList": "ITAutomation/WorkflowConfiguration/GetOperationList",
    "getWorkFlowList": "ITAutomation/WorkflowConfiguration/GetWorkflowList",
    "workFlowNameExist": "ITAutomation/WorkflowConfiguration/WorkFlowNameExist",
    "templateNameExist": "ITAutomation/WorkflowConfiguration/TemplateNameExist",
    "getRunBookReport": "ITAutomation/WorkflowConfiguration/GetRunBookReport",
    "getInfraObjectList": "ITAutomation/WorkflowConfiguration/GetInfraObjectList",
    "getWorkflowActionType": "ITAutomation/WorkflowConfiguration/GetWorkflowActionType",
    "GetTemplateByReplicationTypeId": "ITAutomation/WorkflowConfiguration/GetTemplateByReplicationTypeId",
    "getInfraList": "ITAutomation/WorkflowConfiguration/GetInfraList",
    "getWorkflowPrediction": "ITAutomation/WorkflowConfiguration/GetWorkflowPrediction",
    "GetInfraObjectDetailsById": "ITAutomation/WorkflowConfiguration/GetInfraObjectById",
    "GetDatabaseList": "ITAutomation/WorkflowConfiguration/GetComponentTypeByDatabaseList",
    "GetReplicationMaster": "ITAutomation/WorkflowConfiguration/GetReplicationMasterByInfraMasterName",
    "GetType": "ITAutomation/WorkflowConfiguration/GetTypeByDatabaseIdAndReplicationMasterId",
    "GetWorkflowAction": "ITAutomation/WorkflowConfiguration/GetWorkflowActionByNodeId",
    "GetWorkflowVersions": "ITAutomation/WorkflowConfiguration/GetWorkflowHistoryByWorkflowId",
    "GetInfraByReplication": "ITAutomation/WorkflowConfiguration/GetInfraObjectListByReplicationTypeId",
    "GetWorkflowByInfra": "ITAutomation/WorkflowConfiguration/GetTemplateByInfraObjectId",
    "TemplateValidation": "ITAutomation/WorkflowConfiguration/TemplateValidation",
    "GetCPLValidation": "ITAutomation/WorkflowConfiguration/ValidateCplScript",
    "GetCPSharpValidation": "ITAutomation/WorkflowConfiguration/ValidateCpScript",
    "CplScriptToCPSharp": "ITAutomation/WorkflowConfiguration/CplScriptToCPSharp",
    "GetServerByServerIPAddress": "ITAutomation/WorkflowConfiguration/GetServerByServerIPAddress",
    "GetActionByActionType": "ITAutomation/WorkflowConfiguration/GetActionByActionType",
    "UpdateWorkflowVerify": "ITAutomation/WorkflowConfiguration/UpdateWorkflowVerify",
    //"GetUserList": "ITAutomation/WorkflowConfiguration/GetUserList"
}

const disableWorkflowTools = (mode) => {
    const disableButtonId = ['btnWorkFlowDelete', 'btnFormat', 'btnWorkflowVerify', 'btnWorkflowRename', 'btnSaveModalOpen', 'btnWorkflowLock', 'workflowAttach', 'btnWorkflowPublish',
        'btnMarkAction', 'btnRun', 'btnGenerateTemplate', 'btnExportWorkFlow', 'btnWorkFlowReport', 'btnSaveAsModal', 'btnVersionManage',
        'btnExtract']
    disableButtonId.forEach(disableId => $('#' + disableId).prop('disabled', mode));
    $('#btnWorkFlowDelete i').toggleClass('text-dark', !mode).toggleClass('text-secondary', mode);
}


const clearWFFields = () => {
    workflowSaveMode = "Save"
    isTemplateWorkFlow = false
    $('#workflowActions').empty();
    $('#actionTypeLegend').addClass('d-none')
    $('#workflowTitle').text('')
    $('#btnMarkAction').html('<i class="cp-mark-action me-1"></i>Mark Action')
    $('#workflowAttach').html(`<i class='cp-url me-1'></i>Attach`)
    $('#btnWorkflowLock').html(`<i class='cp-open-lock me-1'></i>Lock`)
    $('#btnWorkflowVerify').html(`<i class='cp-freeze me-1'></i>Freeze`)
    $('#btnWorkflowPublish').html(`<i class='cp-publish me-1'></i>Publish`)
    $(".checkSaveWorkflow, .actionCheckBox, #workflowLockStatus, #workflowVerifyStatus").hide()
    $('.actionCheckBox').prop('checked', false)
    $("#versionText").hide()
    $("#serverCount, #databaseCount, #actionsCount").text(0)
    disableWorkflowTools(true)
    $('#EndWorkflowButton').hide()
    $('#option1').prop('checked', false)
    $('#option2').prop('checked', true)
    $('#workflowRunningStatus').addClass('d-none')
    $('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').parent().hide();
    runningCount = '';
    isRunning = false;
}

$("#btnSaveWorkflow").on('click', btnDebounce(async function () {

    isWaitForParallelExist = true;
    $('#AISuggestion').remove();
    let workflowname = $('#workflowName').val();
    if (!workflowEditable) {
        let workflowNameExist = await WorkflowNameValidate(workflowname, '', 'workflowName-error');
        if (workflowNameExist) {
            workflowLoader('on')
            let workflowData;
            if (workflowSaveMode === "Update") {
                workflowData = {
                    "Id": GlobalWorkflowId,
                    "name": $("#workflowName").val(),
                    "companyId": "",
                    "properties": JSON.stringify({ 'nodes': updateMode === 'compare' ? globalWorkflowArray : getWorkFlowData(), 'estimatedrto': totalEstimatedRto }),
                    "isVerify": false,
                    "isLock": GlobalIsLock,
                    "isPublish": GlobalIsPublish,
                    "IsDraft": GlobalIsDraft,
                    "version": $('#workflowVersion').text(),
                    "comments": "",
                    __RequestVerificationToken: gettoken()
                }
            } else {
                workflowData = {
                    "name": $("#workflowName").val(),
                    "companyId": "",
                    "properties": JSON.stringify({ 'nodes': workflowSaveMode == "Extract" ? globalExtractArray : getWorkFlowData(), 'estimatedrto': totalEstimatedRto }),
                    "isVerify": false,
                    "isLock": false,
                    "isPublish": false,
                    "IsDraft": false,
                    "version": "0.0.1",
                    "comments": "",
                    __RequestVerificationToken: gettoken()
                }
            }

            //if (!isWaitForParallelExist) {
            //    workflowLoader('off')
            //    notificationAlert('warning', 'WaitForParallelAction required for parallel actions')
            //    return false
            //}

            await $.ajax({
                type: "POST",
                url: RootUrl + Urls.create,
                data: workflowData,
                dataType: "json",
                traditional: true,
                success: function (result) {
                    if (result.success) {

                        if (result.data.hasOwnProperty('version')) {
                            $('#workflowVersion').text(result.data.version)
                        }
                        $(".checkSaveWorkflow").hide()
                        if (!isRename || (isRename && GlobalWorkflowName !== $("#workflowName").val())) {
                            notificationAlert("success", result.data.message)
                        } else {
                            notificationAlert("info", 'No changes were made to the workflow name.')
                        }

                        isRename = false
                        GlobalWorkflowName = $("#workflowName").val()
                        if (workflowSaveMode === "Save" || workflowSaveMode === "Extract") {
                            if (workflowSaveMode === "Extract") {
                                workflowContainer.empty()
                                loadWorkFlow(globalExtractArray)
                                $('#markAllContainer').removeClass('d-flex')
                                $('#markAllContainer').addClass('d-none')
                                $('#workflowLockStatus, #workflowVerifyStatus').hide();
                                $('#attachedInfraObjectName, #attachedInfraObjectType, #attachedProfileName').parent().hide();
                                setTimeout(() => {
                                    globalExtractArray = [];
                                }, 3000)
                            }

                            GlobalWorkflowId = result.data.workflowId
                            GlobalIsLock = false
                            GlobalIsPublish = false
                            GlobalIsVerify = false
                            $('#workflowRunningStatus').hide()
                            $('#workflowName, #workflowNameForAttach').val(GlobalWorkflowName);
                            $('#workflowTitle').text(GlobalWorkflowName).attr('title', GlobalWorkflowName)
                            $("#versionText").show()
                            $("#workflowVersion").show()
                            $("#workflowVersion").text('1.0.1');
                            $('#workflowAttach').html(`<i class='cp-url me-1'></i>Attach`)
                            $('#btnWorkflowLock').html(`<i class='cp-lock me-1'></i>Lock`)
                            $('#btnWorkflowVerify').html(`<i class='cp-freeze me-1'></i>Verify`)
                            $('#btnWorkflowPublish').html(`<i class='cp-publish me-1'></i>Publish`)
                            disableWorkflowTools(false);
                            workflowSaveMode = "Update";
                            if (isFromGenie) {
                                let text = `The <span class="text-primary text-truncate" style="max-width:110px">${GlobalWorkflowName}</span> workflow has been successfully saved.Do you want to keep developing new workflows?`
                                let InfraHtml = `<li id=${getRandomId('genie')} class="list-group-item AI-Suggestions-Option genieChatCont deletebleList">${genieImage}<div class="d-grid gap-2 AI-Suggestions-Bg"><div>${text}</div>
                                                   </div></li> <div class='d-flex gap-2 p-1 pb-0 justify-content-end deletebleList mb-3 me-4'><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieInitializeBtn'>Yes</button><button class='btn btn-sm btn-outline-primary rounded-pill' id='genieInitializeNoBtn'>No</button></div>`
                                $('#genieChatContainer .deletebleList').remove()
                                setTimeout(() => {
                                    $('#genieChatContainer').append(InfraHtml)
                                    scrollToBottom()
                                }, 1000)
                            }
                        }
                    } else {
                        errorNotification(result)
                    }
                },

            })

            workFlowData = workflowSaveMode === "Extract" ? globalExtractArray : globalWorkflowArray
            updateMode = 'update';
            globalWorkflowArray = [];
            $('.actionCheckBox, .groupCheckBox').prop('checked', false).hide()

            $('#btnMarkAction').html('<i class="cp-mark-action me-1"></i>Mark Action')
            $('#SaveWorkflowModal').modal('hide')
        }
    } else {
        let workflowNameExist = await WorkflowNameValidate(workflowname);
        if (!workflowNameExist) return false
        let wfName = $("#workflowName").val()
        $("#workflowTitle").text(wfName).attr('title', wfName)
        $('#SaveWorkflowModal').modal('hide')
        $(".checkSaveWorkflow").show();
        workflowEditable = false;
        $("#workflowModalTitle").text("Create Workflow")
        $("#btnSaveWorkflow").trigger('click')
    }
    workflowLoader('off')
}, 800))


const getWorkFlowData = (mode = '') => {
    totalEstimatedRto = 0
    globalWorkflowArray = [];
    $('#AISuggestion').remove();
    let workflowContainer
    if (mode === 'paste') {
        workflowContainer = $('#workflowActions .selectedWfContainer').parents('.ui-sortable-handle').toArray().reverse()
    } else {
        workflowContainer = $('#workflowActions').children().toArray()
    }
    removeSelection();
    // workflowContainer.each(function (idx, obj) {
    let containerLength = workflowContainer.length
    for (let i = 0; i < containerLength; i++) {
        let obj = workflowContainer[i]
        let id;
        if ($(obj).find('.parentConditionCont').length === 0 && !$(obj)[0].classList.contains('workflowCanvas')) id = obj.children[1].id;
        let details;
        if (id && id.includes('node')) {
            let decodedDetails = atob($('#' + id).attr('details'))
            details = JSON.parse(decodedDetails)
            details.actionInfo['isVerifiedAction'] = $(`#${id}`).hasClass('exclamationContainer') ? false : true;
            changeCommandScript(details, '')
            if (details.actionInfo.hasOwnProperty('IsGroup')) delete details.actionInfo['IsGroup']
            if (details.actionInfo.hasOwnProperty('IsParallel')) delete details.actionInfo['IsParallel']

            if (mode == 'template' || mode == 'templateForm') getFormInputByTemplate(details, mode)

            if ($(`#${id}`).parent().next().find('.parentConditionCont').length > 0) {
                let ConditionId = $(`#${id}`).parent().next().find('.parentConditionCont')[0].id
                let conditionDetails = $(`#${ConditionId}`)?.attr('conditionDetails') ? JSON.parse($(`#${ConditionId}`)?.attr('conditionDetails')) : []
                conditionDetails?.length && conditionDetails.forEach((d) => {
                    if (d.gotoAction.includes('node')) {
                        let decoded = atob($(`#${d.gotoAction}`).attr('details'))
                        let data = JSON.parse(decoded)
                        let index = $('#workflowActions').children().not('canvas').not('svg').not($('.parentConditionCont').parent()).index($(`#${d.gotoAction}`).parent()) + 1
                        d['stepId'] = data.stepId
                        d['index'] = index
                    } else if (d.gotoAction.includes('parallel')) {
                        let decoded = atob($(`#${d.gotoAction}`).children().first().attr('details'))
                        let data = JSON.parse(decoded)
                        let index = $('#workflowActions').children().not('canvas').not('svg').not($('.parentConditionCont').parent()).index($(`#${d.gotoAction}`).parent()) + 1
                        d['gotoAction'] = $(`#${d.gotoAction}`).children().first()[0].id
                        d['stepId'] = data.stepId
                        d['index'] = index
                    }
                    else {
                        d['stepId'] = d.gotoAction
                    }
                })

                let getElseCondition = conditionDetails.filter((d) => d.condition == "elsecondition")
                if (getElseCondition.length) {
                    details.actionInfo['failureCount'] = getElseCondition[0].failureCount
                }
                details.actionInfo['IsConditional'] = true;
                details.actionInfo['conditionDetails'] = conditionDetails

            } else {
                details.actionInfo['IsConditional'] = false;
                if (details.actionInfo.hasOwnProperty('conditionDetails')) delete details.actionInfo['conditionDetails']
            }
            calculateTotalRto(mode == 'template' ? updateProperties(details) : details)
            globalWorkflowArray.push(details)
        } else if (id && id.includes('parallel')) {
            let parallelArray = []
            $('#' + id).children().each(function (idx, child) {
                let childId = child.id;
                details = atob($('#' + childId).attr('details'))
                let parsedDetails = JSON.parse(details)
                changeCommandScript(parsedDetails, '')
                parsedDetails.actionInfo['isVerifiedAction'] = $(`#${childId}`).hasClass('exclamationContainer') ? false : true;
                parsedDetails.actionInfo['IsParallel'] = true;
                if (parsedDetails.actionInfo.hasOwnProperty('IsGroup')) delete parsedDetails.actionInfo['IsGroup']
                if (mode == 'template' || mode == 'templateForm') getFormInputByTemplate(parsedDetails, mode)

                calculateTotalRto(parsedDetails)
                parallelArray.push(mode == 'template' ? updateProperties(parsedDetails) : parsedDetails)
            })
            globalWorkflowArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': parallelArray })

            let nextObj = workflowContainer[i + 1]
            if (nextObj) {
                let id;
                if ($(nextObj).find('.parentConditionCont').length === 0 && !$(nextObj)[0].classList.contains('workflowCanvas')) id = nextObj.children[1].id;
                if (id && id.includes('node')) {
                    let decodedDetails = atob($('#' + id).attr('details'))
                    details = JSON.parse(decodedDetails)
                    if (!details?.actionInfo?.propertyData?.actionType?.toLowerCase()?.includes('parallelaction')) {
                        isWaitForParallelExist = false
                    }
                }
            } else {
                isWaitForParallelExist = false
            }

        } else if (id && id.includes('group')) {
            let groupName = obj.children[1].getAttribute('groupName')
            let groupColor = obj.children[1].getAttribute('groupColor')
            let groupArray = [];
            $('#' + id + ' .accordion-body').children().each(function (idx, child) {
                let groupChild
                if (idx !== 0) {
                    groupChild = child.children[1].id;
                } else {
                    groupChild = child.children[0].id;
                }
                if (groupChild.includes('node')) {
                    details = atob($('#' + groupChild).attr('details'))
                    // details = JSON.parse(decodedDetails)                      
                    let parsedDetails = JSON.parse(details)
                    changeCommandScript(parsedDetails, '')
                    parsedDetails.actionInfo['isVerifiedAction'] = $(`#${groupChild}`).hasClass('exclamationContainer') ? false : true;
                    parsedDetails.actionInfo['IsGroup'] = true;
                    if (parsedDetails.hasOwnProperty('IsParallel')) delete parsedDetails?.actionInfo['IsParallel']
                    if (mode == 'template' || mode == 'templateForm') getFormInputByTemplate(parsedDetails, mode)

                    calculateTotalRto(parsedDetails)
                    groupArray.push(mode == 'template' ? updateProperties(parsedDetails) : parsedDetails)
                } else if (groupChild.includes('parallel')) {
                    let groupParallelArray = []
                    $('#' + groupChild).children().each(function (idx, child) {
                        let childId = child.id;
                        let decodedDetails = atob($('#' + childId).attr('details'))
                        //details = JSON.parse(decodedDetails) 
                        //details = $('#' + childId).attr('details')
                        let parsedDetails = JSON.parse(decodedDetails)
                        changeCommandScript(parsedDetails, '')
                        parsedDetails.actionInfo['isVerifiedAction'] = $(`#${childId}`).hasClass('exclamationContainer') ? false : true;
                        parsedDetails.actionInfo['IsParallel'] = true;
                        parsedDetails.actionInfo['IsGroup'] = true;

                        if (mode == 'template' || mode == 'templateForm') getFormInputByTemplate(parsedDetails, mode)
                        calculateTotalRto(parsedDetails)
                        groupParallelArray.push(mode == 'template' ? updateProperties(parsedDetails) : parsedDetails)

                    })
                    groupArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': groupParallelArray })
                }
            });
            globalWorkflowArray.push({ 'actionInfo': { 'IsGroup': true }, 'groupName': groupName, 'groupId': id, 'groupColor': groupColor, 'groupActions': groupArray })
        }
    }

    // })
    return globalWorkflowArray
}

const getFormInputByTemplate = (details, mode = '') => {
    details?.actionInfo?.formInput?.length && details?.actionInfo?.formInput?.forEach((fi) => {
        if (mode == 'templateForm') {
            if ((fi?.type === 'text' || fi?.type === 'password') && !restoreFormInput?.includes(fi?.name)) {
                restoreFormInput.push(fi?.name)
                restoreFieldObj.push(fi)
            }
        } else if (mode == 'template' && restoreUniqueObj?.includes(fi?.name)) fi["required"] = true
    })
}

const calculateTotalRto = (details) => {
    totalEstimatedRto = totalEstimatedRto + Number(details.actionInfo.rto)
}

$('#btnSaveModalOpen').on('click', function () {
   // if (!$('.exclamationContainer').is(':visible')) {
        $("#workflowModalTitle").text("Create Workflow")
        workflowEditable = false;
        openSaveModal()
        $('.modal').modal('hide');
    //} else {
    //    notificationAlert("warning", "Validate all actions before saving")
    //}

})

const openSaveModal = () => {
    if (workflowSaveMode === "Save") {
        $('#workflowName').val('');
        $('#SaveWorkflowModal').modal('show');
    } else {
        if (workflowContainer.children().length === 0) {
            notificationAlert("info", "Atleast one action required to save")
        }
        else if ($('.checkSaveWorkflow').is(':visible')) {
            workflowSaveMode === "Update"
            if (GlobalIsVerify) {
                $('#workflowstatusModal')?.modal('show')
            } else {
                $("#btnSaveWorkflow")?.trigger('click');
            }
        } else {
            notificationAlert("info", "No changes made with this workflow")
        }
    }
}

$('#btnWfStatusSave')?.on('click', function (e) {
    e?.preventDefault();
    GlobalIsDraft = true;

    $("#btnSaveWorkflow")?.trigger('click');
    $('#workflowstatusModal')?.modal('hide')
})

$('#btnWfStatusCancel')?.on('click', function (e) {
    e?.preventDefault();
    GlobalIsDraft = false;

    if (GlobalWorkflowId) {
        loadWorkflowDiagram(GlobalWorkflowId)
    }
    $('#workflowstatusModal')?.modal('hide')
})

$('#btnCancelLoadWorkflow').on('click', function () {
    $('#workflowName-error').text('').removeClass('field-validation-error');
})

$('#btnLoadWorkFlow').on('click', async function () {
    let workflowList = [];
    $('#LoadWorkflowListModal').modal('show')
    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getWorkFlowList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                workflowList = result.data
                if (Array.isArray(workflowList) && workflowList.length > 0) {
                    $('#workflowList').empty()
                    workflowList.forEach((item) => {
                        $('#workflowList').append('<option value="' + item?.id + '">' + item?.name + '</option>')
                    })
                    $('#workflowList').val('');
                }

            } else {
                errorNotification(result)
                $('#workflowList').append('<option value="No Data"></option>')
            }
        },

    })

})

function appendDataForParallel(newData, index, groupObj = {}) {

    let encodedData = btoa(JSON.stringify(newData))
    let ColorData = $('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentColor')
    let iconData = `${$('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`

    ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;

    const newItemElement = `
        <div class='ui-sortable-handle'>
            <div class='workflowActions loadSelected ${newData.actionInfo.hasOwnProperty('isVerifiedAction') && !newData.actionInfo.isVerifiedAction ? 'exclamation-icon exclamationContainer' : ''
} ' role="button" parentId='${ newData?.actionInfo?.parentActionId || '' }' id='${ newData?.actionInfo?.uniqueId }' details='${ encodedData } '>
                <i class='${iconData} workflowIcon' style='background: ${ColorData ?? '#3562AA'}'></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'>${index + 1}</span>
                    . 
                    <span class='actionSpan' title=${newData?.actionInfo?.actionName}>${newData?.actionInfo?.actionName}</span>
                </div>
                <input type='checkbox' class='actionCheckBox' name='actionCheck' />
            </div>
        </div>`;

    dataDisplay.append(newItemElement);
    updateFilterProperties(newData, groupObj)
    updateIndexValues();
    loadActionsCount(newData)
    $('.actionCheckBox').hide();
}

$('#btnSaveLoadWorkflow').on('click', async function () {
    workFlowData = []
    let workflowId = $('#workflowList').val();
    $("#filter_property ul li").remove();

    if (workFlowList(workflowId) === "false") {
        loadWorkflowDiagram(workflowId)
    }
})

const loadWorkflowDiagram = async (workflowId) => {
    clearWFFields()
    dataDisplay.empty()
    workflowLoader('on')
    setTimeout(() => {
        if ($('#WFLoader').is(':visible')) {
            workflowLoader('off')
        }

    }, 5000)

    let data = {}
    data.workflowId = workflowId

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getWorkFlowById,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                let data = result.data
                const { isAttach, isLock, isPublish, isVerify, profileName } = data;
                dataDisplay.empty()
                $('#workflowAttach').html(`<i class='cp-${isAttach ? 'un-link' : 'url'} me-1'></i>${isAttach ? 'Detach' : 'Attach'}`)
                $('#btnWorkflowLock').html(`<i class='cp-${isLock ? 'open-lock' : 'lock'} me-1'></i>${isLock ? 'Unlock' : 'Lock'}`)
                $('#btnWorkflowVerify').html(`<i class='cp-${isVerify ? 'workflow-unverify' : 'workflow-verify'} me-1'></i>${isVerify ? 'Unverify' : 'Verify'}`)
                $('#btnWorkflowPublish').html(`<i class='cp-${isPublish ? 'non-publish' : 'publish'} me-1'></i>${isPublish ? 'Unpublish' : 'Publish'}`)
                if (profileName && profileName?.toLowerCase() !== 'na') $('#attachedProfileName').text(profileName ? profileName : 'NA').attr('title', profileName ? profileName : 'NA').parent().show();

                isLock ? $('#workflowLockStatus').show() : $('#workflowLockStatus').hide();
                isVerify ? $('#workflowVerifyStatus').show() : $('#workflowVerifyStatus').hide();
                isRunning = data?.isRunning               
                runningCount = data?.isRunning ? data?.runningCount : null
                data?.isRunning ? $('#workflowRunningStatus').removeClass('d-none') : $('#workflowRunningStatus').addClass('d-none');
                if (data?.properties) {
                    let workFlowProperties = JSON.parse(data.properties)
                    if (workFlowProperties.hasOwnProperty('nodes') && workFlowProperties.nodes.length) {
                        GlobalWorkflowId = data?.id
                        if (isAttach) attachInfraObject(data?.id)
                        GlobalWorkflowName = data?.name
                        GlobalIsLock = data?.isLock
                        GlobalIsPublish = data?.isPublish
                        GlobalIsVerify = data?.isVerify
                        GlobalIsDraft = data?.isDraft
                        $('#workflowName, #workflowNameForAttach').val(data?.name);
                        $('#workflowTitle').text(data?.name).attr('title', data?.name)
                        $("#serverCount").text(data?.serverCount)
                        $("#databaseCount").text(data?.databaseCount)
                        $("#actionsCount").text(data?.actionCount)
                        $("#versionText").show()
                        $("#workflowVersion").text(data?.version)
                        workFlowData.push(...workFlowProperties?.nodes)
                        loadWorkFlow(workFlowProperties?.nodes)
                    } else if (workFlowProperties?.length) {
                        GlobalWorkflowId = data?.id
                        GlobalWorkflowName = data?.name
                        GlobalIsLock = data?.isLock
                        GlobalIsPublish = data?.isPublish
                        GlobalIsVerify = data?.isVerify
                        GlobalIsDraft = data?.isDraft
                        if (isAttach) attachInfraObject(data?.id)
                        $('#workflowName', '#workflowNameForAttach').val(data?.name)
                        $('#workflowTitle').text(data?.name).attr('title', data?.name)
                        $("#serverCount").text(data?.serverCount)
                        $("#databaseCount").text(data?.databaseCount)
                        $("#actionsCount").text(data?.actionCount)
                        $("#versionText").show()
                        $("#workflowVersion").text(data?.version)
                        workFlowData.push(...workFlowProperties)
                        loadWorkFlow(workFlowProperties)
                    }
                }
            } else {
                errorNotification(result)
            }
        },
    })
}

(function () {
    if (sessionStorage.hasOwnProperty('WorkflowId')) {
        let WorkflowId = sessionStorage.getItem('WorkflowId')
        setTimeout(() => {
            loadWorkflowDiagram(WorkflowId)
        }, 600)

        setTimeout(() => {
            $('.nav-link').removeClass('active')
            $('#orchestration-dropdown').children().first().find('.nav-link').addClass('active')
            sessionStorage.removeItem('WorkflowId')
        }, 1500)
    }
})();

function makeParellelGroup(newData, index, valueObj, mode, serverList) {
    let childLength = newData?.children?.length
    for (let i = 0; i < childLength; i++) {

        let ColorData = $('.nodeSummaryData[nodeId=' + newData?.children[i]?.actionInfo?.nodeId + ']').attr('parentColor')
        let iconData = `${$('.nodeSummaryData[nodeId=' + newData?.children[i]?.actionInfo?.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`

        ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;

        valueObj.actionId = newData?.children[i]?.actionInfo?.uniqueId
        valueObj.actionName = newData?.children[i]?.actionInfo?.actionName
        valueObj.type = 'sequential'
        valueObj.IsParallel = true;
        actionArray.push(valueObj)

        if (mode?.toLowerCase() == 'import') {
            if (!serverExistByImport?.length) checkServerExistByImport(newData?.children[i], serverList)
            if (serverExistByImport?.length && serverExistByImport[0]?.isServerNotExist && newData?.children[i]?.actionInfo?.propertyData?.propertiesInfo) {
                newData.children[i].actionInfo.propertyData.propertiesInfo = []
            }
        }

        let encodedData = btoa(JSON.stringify(newData?.children[i]))

        const newItemElement = `
        <div class='ui-sortable-handle'>
            <div class='workflowActions forGroupParallel loadSelected ${newData.actionInfo.hasOwnProperty('isVerifiedAction') && !newData.actionInfo.isVerifiedAction ? 'exclamation-icon exclamationContainer' : ''}' role="button" parentId='${newData?.children[i]?.actionInfo?.parentActionId || ''}' id='${newData?.children[i]?.actionInfo?.uniqueId}' details='${encodedData}'>
                <i class='${iconData} workflowIcon' style='background: ${ColorData ?? '#3562AA'} '></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'>${index + 1}</span>
                    . 
                    <span class='actionSpan' title=${newData?.children[i]?.actionInfo?.actionName ?? 'NA'}>${newData?.children[i]?.actionInfo?.actionName ?? 'NA'}</span>
                </div>
                <input type='checkbox' class='actionCheckBox' name='actionCheck' />
            </div>
        </div>`;

        validateUnhandledActions(newData?.children[i])
        loadActionsCount(newData.children[i])
        dataDisplay.append(newItemElement);
        updateFilterProperties(newData.children[i])
    }

    let ParallelId = getRandomId("parallel")
    $('.forGroupParallel.loadSelected').unwrap()
    $('.forGroupParallel.loadSelected').wrapAll("<div id=" + ParallelId + " class='parallelCont loadSelected parallel-icon gap-2'></div>")
    $('#' + ParallelId).wrapAll('<div class="ui-sortable-handle"></div>')
    $('#' + ParallelId + ' .workflowActions').removeClass('loadSelected')
    updateIndexValues();
    $('.actionCheckBox').hide();
}

const conditionalAppending = (workflowValues, i, globalParallelId = '', mode = '', serverList = []) => {
    let valueObj = {};

    if (workflowValues.hasOwnProperty('groupName')) {
        let valueGroupObj = {}
        let groupId = workflowValues.groupId
        let accordionId = getRandomId("accordion")
        let parentId = getRandomId('parent')
        valueGroupObj['groupId'] = groupId;
        valueGroupObj['groupName'] = workflowValues.groupName;
        valueGroupObj['type'] = 'group';
        groupArray.push(valueGroupObj);
        workflowValues.groupActions.forEach(function (obj, idx) {
            valueObj = {}
            if (obj.hasOwnProperty('children')) {
                changeCommandScript(obj, 'load')
                makeParellelGroup(obj, i, valueObj, mode, serverList)

            } else {
                changeCommandScript(obj, 'load')

                valueObj.actionId = obj.actionInfo.uniqueId
                valueObj.actionName = obj.actionInfo.actionName
                valueObj.type = 'sequential'
                valueObj.IsParallel = false;
                actionArray.push(valueObj)

                if (mode?.toLowerCase() == 'import') {
                    if (!serverExistByImport?.length) checkServerExistByImport(obj, serverList)
                    if (serverExistByImport?.length && serverExistByImport[0]?.isServerNotExist && obj?.actionInfo?.propertyData?.propertiesInfo) {
                        obj.actionInfo.propertyData.propertiesInfo = []
                    }
                }
                validateUnhandledActions(obj);
                appendDataForParallel(obj, i, workflowValues)
            }
        })
        $("#groupName").val(workflowValues.groupName)
        $('.loadSelected').parent().wrapAll("<div id=" + groupId + " groupName=" + workflowValues.groupName + " groupColor=" + workflowValues.groupColor + " class='parentGroup'>" + GenerateGroupName(workflowValues.groupName, workflowValues.groupColor, parentId) + "</div>")
        $('#' + groupId + ' .accordion-body').append($('.loadSelected').parent())
        $('#' + groupId + ' .accordionClass').attr('id', accordionId)
        $('#' + groupId + ' .btnAccordion').attr('data-bs-target', '#' + accordionId)
        $('#' + groupId + ' .loadSelected').before(connectImage)
        $('#' + groupId + ' .loadSelected').first().prev().remove()
        $('#' + groupId + ' .groupClass').removeClass('loadSelected')
        $('#' + groupId).wrapAll('<div class="ui-sortable-handle"></div>')
        $('#' + groupId).before(connectImage)
        $('#' + groupId + ' .workflowActions').removeClass('loadSelected')
        $('.parallelCont').removeClass('loadSelected')
        $('.groupCheckBox').hide()
        globalGroupArray.push(workflowValues.groupName)
        checkGroupCount();
        if ($('#WFLoader').is(':visible')) {
            workflowLoader('off')
        }
    } else if (workflowValues.hasOwnProperty('children') || workflowValues.hasOwnProperty('parallelActions')) {
        let paralleActions = workflowValues.hasOwnProperty('children') ? workflowValues.children : workflowValues.parallelActions
        if (paralleActions.length && !paralleActions[0].actionInfo.hasOwnProperty('IsGroup')) {
            let ParallelId = globalParallelId || getRandomId("parallel")
            paralleActions.forEach(function (obj, i) {
                valueObj = {};
                changeCommandScript(obj, 'load')

                valueObj.actionId = obj.actionInfo.uniqueId
                valueObj.actionName = obj.actionInfo.actionName
                valueObj.type = 'sequential'
                valueObj.IsParallel = true;
                actionArray.push(valueObj)

                if (mode?.toLowerCase() == 'import') {
                    if (!serverExistByImport?.length) checkServerExistByImport(obj, serverList)
                    if (serverExistByImport?.length && serverExistByImport[0]?.isServerNotExist && obj?.actionInfo?.propertyData?.propertiesInfo) {
                        obj.actionInfo.propertyData.propertiesInfo = []
                    }
                }
                validateUnhandledActions(obj)
                appendDataForParallel(obj, i);
            })
            $('.loadSelected').unwrap()
            $('.loadSelected').wrapAll("<div id=" + ParallelId + " class='parallelCont loadSelected parallel-icon gap-2'></div>")
            $('#' + ParallelId).wrapAll('<div class="ui-sortable-handle"></div>')
            $('#' + ParallelId).parent().prepend(connectImage)
            $('#' + ParallelId).removeClass('loadSelected')
            $(".workflowActions").removeClass("loadSelected")
            $(".contextMenu").fadeOut(100);
            if ($('#WFLoader').is(':visible')) {
                workflowLoader('off')
            }
        }
    } else if (!workflowValues.actionInfo.hasOwnProperty('IsGroup')) {
        valueObj.actionId = workflowValues.actionInfo.uniqueId
        valueObj.actionName = workflowValues.actionInfo.actionName
        valueObj.type = 'sequential'
        valueObj.IsParallel = false;
        changeCommandScript(workflowValues, 'load')
        if (workflowValues.actionInfo.IsConditional) {
            conditionalArray.push({ 'actionId': workflowValues.actionInfo.uniqueId, condionDetail: workflowValues.actionInfo.conditionDetails })
        }

        if (mode?.toLowerCase() == 'import') {
            if (!serverExistByImport?.length) checkServerExistByImport(workflowValues, serverList)
            if (serverExistByImport?.length && serverExistByImport[0]?.isServerNotExist && workflowValues?.actionInfo?.propertyData?.propertiesInfo) {
                workflowValues.actionInfo.propertyData.propertiesInfo = []
            }
        }

        validateUnhandledActions(workflowValues)
        appendData(workflowValues)
        actionArray.push(valueObj)
        if ($('#WFLoader').is(':visible')) {
            workflowLoader('off')
        }
    }
}

const checkServerExistByImport = (workflowValues, serverList) => {

    if (serverList?.length) {

        let findServer = workflowValues?.actionInfo?.formInput?.length ? workflowValues?.actionInfo?.formInput.find((fi) => fi?.type?.toLowerCase() == 'server') : {}
        let getServerValue = findServer?.id || workflowValues?.actionInfo?.properties[findServer?.name]

        if (getServerValue) {
            const serverExists = serverList?.some(server => server?.id === getServerValue);
            const serverEntry = serverExists ? workflowValues : { ...workflowValues, isServerNotExist: true };

            serverExistByImport.push(serverEntry);
        }
    }
}

const getServerList = async () => {
    let serverData = [];

    await $.ajax({
        type: "GET",
        url: RootUrl + 'ITAutomation/WorkflowConfiguration/GetServerByRoleTypeAndServerType',
        data: { roleType: '', serverType: '' },
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {

                if (result?.data?.length) {
                    serverData = result?.data

                }

            } else {
                errorNotification(result)
            }
        },
    })

    return serverData

}

const changeCommandScript = (workflowValues, mode = 'load') => {
    workflowValues?.actionInfo?.properties && Object.keys(workflowValues?.actionInfo?.properties).forEach((key) => {
        if (key?.toLowerCase().includes('@@command')) {
            let query = workflowValues?.actionInfo?.properties[key]
            let modifiedQuery = mode == 'load' ? query.replace(/'/g, "@@##") : query.replace(/@@##/g, "'");
            workflowValues.actionInfo.properties[key] = modifiedQuery
            if (workflowValues?.actionInfo?.propertyData?.propertiesInfo?.length) {
                let filteredIndex = workflowValues?.actionInfo?.propertyData?.propertiesInfo?.findIndex(data => data && (data?.hasOwnProperty('Command') || data?.hasOwnProperty('command')))

                if (filteredIndex !== -1) {
                    if (workflowValues?.actionInfo?.propertyData?.propertiesInfo[filteredIndex]['Command']) workflowValues.actionInfo.propertyData.propertiesInfo[filteredIndex]['Command'] = modifiedQuery
                    else workflowValues.actionInfo.propertyData.propertiesInfo[filteredIndex]['command'] = modifiedQuery
                }
            }
        }
    })
}

const unhandledActionInGenie = () => {
    unverifyActionComponents.forEach((d) => {
        $(`#${d}`).addClass('exclamation-icon exclamationContainer')
    })
    notificationAlert('warning', 'Some actions in this workflow are yet to be validated')
}

const unhandledActionInImport = () => {

    $('.workflowActions').addClass('exclamation-icon exclamationContainer')
    notificationAlert('warning', 'Some actions in this workflow are yet to be validated')
}

const validateUnhandledActions = (workflowValues) => {
    let actionPropertyKeys = Object.keys(workflowValues?.actionInfo?.properties)

    if (actionPropertyKeys.includes('@@UseMasterDB')) {
        let findKey = actionPropertyKeys.findIndex(ind => ind === '@@UseMasterDB')
        actionPropertyKeys.splice(findKey, 1)
    }
    if (actionPropertyKeys.includes('@@CheckOutput')) {
        let findKey = actionPropertyKeys.findIndex(ind => ind === '@@CheckOutput')
        actionPropertyKeys.splice(findKey, 1)
    }
    if (actionPropertyKeys.includes('@@TimeOut')) {
        let findKey = actionPropertyKeys.findIndex(ind => ind === ' @@TimeOut')
        actionPropertyKeys.splice(findKey, 1)
    }

    if (actionPropertyKeys.length != workflowValues.actionInfo.formInput.length) {
        unverifyActionComponents.push(workflowValues.actionInfo.uniqueId)
    }
}

async function loadWorkFlow(workFlowProperties, mode = '', serverList = []) {
    $('#actionTypeLegend').removeClass('d-none')
    globalGroupArray = [];
    actionArray = []
    unverifyActionComponents = []
    $('#LoadWorkflowListModal').modal('hide');
    let workflowDataLength = workFlowProperties.length;
    for (let i = 0; i < workflowDataLength; ++i) {

        setTimeout(async () => {
            conditionalAppending(workFlowProperties[i], i, '', mode, serverList)
            if (i === workflowDataLength - 1) {
                setTimeout(() => {
                    if (mode == 'template' && unverifyActionComponents.length > 0) {
                        unhandledActionInGenie()
                    }
                    if (mode == 'import' && serverExistByImport?.length) {
                        if (serverExistByImport[0]?.isServerNotExist) unhandledActionInImport()
                    }
                    if (conditionalArray.length > 0) {
                        getLoadCondition()
                    }
                    //if (isRunning && runningCount?.length) {
                    //    disableCompletedAction()
                    //}
                    actionArray.splice(-1, 1)
                }, 100)
            }
        }, 40)
    }

    if (mode == 'import' || mode == 'template') {
        disableWorkflowTools(true);
        $('#btnSaveModalOpen').prop('disabled', false);

    } else {
        //if (isRunning) {
        //    disableWorkflowTools(true)
        //} else {
            disableWorkflowTools(false);
        //}
    }

    if (mode !== 'import') {
        workflowSaveMode = 'Update'
    }
    serverListCount = {}
    databaseListCount = {}
    $('#searchWorkFlow').val('');
    $('#highlight_arrow').hide();
    $('#btnMarkAction').html('<i class="cp-mark-action me-1"></i>Mark Action')
    disableSortable()
    $('#EndWorkflowButton').show()
}

const disableCompletedAction = () => {
    let splitRunningCount = runningCount && runningCount.split('/')[0]
    if (splitRunningCount && splitRunningCount !== '0') {
        $(`#workflowActions .ui-sortable-handle:lt(${splitRunningCount})`).css('pointer-events', 'none').css('opacity', '0.6')
    }

}

$('#btnNewWorkflow').on('click', function () {
    if (!$('.checkSaveWorkflow').is(':visible')) {
        clearWFFields()
        $('.spanTextAIConfig').text('Deals with interpreting different commands and is capable of executing privileged instructions.')
        $('#AI-ConfigurationModal').modal('show')
    } else {
        $('#confirmationModalWFText').html('Changes you made may not be saved. <span class="fw-bold"> Are you sure ?</span>')
        $('#confirmationWFModal').modal('show')
    }
})

$('#confirmationWFDiscard').on('click', function () {
    $('#confirmationWFModal').modal('hide')
    //clearWFFields()
})

$('#confirmationWFSave').on('click', function () {
    $('#confirmationWFModal').modal('hide')
    $('.checkSaveWorkflow').hide()
    setTimeout(() => {
        $('#btnNewWorkflow').trigger('click')
    }, 300)
})

$("#btnWorkflowRename").on('click', function () {
    $('#workflowName-error').removeClass('field-validation-error').text('');
    $('#workflowName').val($('#workflowTitle').text()).attr('title', $('#workflowTitle').text())
    workflowEditable = true;
    $("#workflowModalTitle").html('<i class="cp-rename"></i> Rename Workflow');
    $('#SaveWorkflowModal').modal('show')
    isRename = true;
})

$('#btnFormat').on('click', function () {
    dataDisplay.empty()
    loadWorkFlow(workFlowData)
})

$('#btnWorkFlowDelete').on('click', function () {
    $('#textDeleteId').val(GlobalWorkflowId)
    $("#deleteWorkflowData").text(GlobalWorkflowName).attr('title', GlobalWorkflowName)
    $('#DeleteModal').modal('show')
})

//$('#workflowList').select2({
//    dropdownParent: $('#LoadWorkflowListModal')
//});

$('#btnSaveAsModal').on('click', function () {
    $('.saveAsRestoreCopy, .sectionData, .restoreData').empty();
    if ($('.checkSaveWorkflow').is(':visible')) {
        notificationAlert('info', 'Unsaved changes in workflow')
    } else {
        restoreFormInput = []
        restoreFieldObj = []
        restoreUniqueObj = []
        $('.saveasUniqueData').empty()
        clearSaveAsInputFields()
        getUniqueFormInput(workFlowData, 'saveasUniqueData')
        disableWorkflowTools(true)
        setTimeout(() => {
            $('#SaveAsWorkflowListModal').modal('show')
            disableWorkflowTools(false)
        }, 500)
    }
});

const clearSaveAsInputFields = () => {
    let objKeys = ['saveAsWorkflowName', 'saveAstype', 'findValue', 'replaceValue', 'saveAsAppendCheckBox', 'appendText', 'Textposition']
    objKeys.forEach((key) => {
        if (key == 'saveAstype') {
            $('#' + key).val('duplicate');
            $('#' + key + '-error').text('').removeClass('field-validation-error');
        } else if (key == 'saveAsAppendCheckBox') {
            $('#' + key).prop('checked', false);
        } else if (key == 'Textposition') {
            $('#' + key).val('prefix');
        } else {
            $('#' + key).val('');
            $('#' + key + '-error').text('').removeClass('field-validation-error');
        }
    })

    $('.saveAsRestoreComponents').empty();
    $('.newComponentHeader').addClass('d-none');
    disableSaveAsFields();
};



