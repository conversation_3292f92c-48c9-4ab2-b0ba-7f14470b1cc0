﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetRunQuery;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDataSetRepository : IRepository<DataSet>
{
    Task<DataSet> GetDataSetById(string id);
    Task<List<DataSet>> GetDataSetNames();
    Task<DataSetRunQueryVm> GetTableJson(string query);
    Task<bool> IsDataSetNameUnique(string name);
    Task<bool> IsDataSetNameExist(string name, string id);
    Task<List<DataSet>> GetDataSetByTableAccessId(string tableAccessId);
}