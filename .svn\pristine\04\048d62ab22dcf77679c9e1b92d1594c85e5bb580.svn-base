﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class MonitorServiceRepository : BaseRepository<MonitorService>, IMonitorServiceRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public MonitorServiceRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(
        dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public override async Task<IReadOnlyList<MonitorService>> ListAllAsync()
    {
        var monitorServices = FilterRequiredField(base.QueryAll(monitorService => monitorService.CompanyId.Equals(_loggedInUserService.CompanyId)));

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.ToListAsync()
            : GetAssignedInfraObjects(monitorServices);
    }

    public override async Task<MonitorService> GetByReferenceIdAsync(string id)
    {
        var monitorServices = base.GetByReferenceId(id,
            monitorService => monitorService.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                              monitorService.ReferenceId.Equals(id));

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.FirstOrDefaultAsync()
            : GetInfraObjectByReferenceId(monitorServices.FirstOrDefault());
    }

    public async Task<List<MonitorService>> GetMonitorServiceNames()
    {
        var monitorServices = base
            .QueryAll(infraObject => infraObject.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Select(x => new MonitorService { ReferenceId = x.ReferenceId, InfraObjectName = x.InfraObjectName });

        return _loggedInUserService.IsAllInfra
            ? await monitorServices.ToListAsync()
            : GetAssignedInfraObjects(monitorServices).ToList();
    }
    public async Task<List<MonitorService>> GetMonitorServiceListByInfraObjectId(string infraObjectId)
    {
        var monitorService = base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId))
            .Select(x => new MonitorService
            {
               
                ServerId=x.ServerId,
                ServerName=x.ServerName,
                Status=x.Status,
                IsServiceUpdate=x.IsServiceUpdate,
                Type = x.Type

            });
        return _loggedInUserService.IsAllInfra
           ? await monitorService.ToListAsync()
           : GetAssignedInfraObjects(monitorService).ToList();

    }
    public async Task<List<MonitorService>> GetMonitorServiceByInfraObjectId(string infraObjectId)
    {
        return await _dbContext.MonitorServices.Active()
            .Where(x => x.InfraObjectId.Equals(infraObjectId)).ToListAsync();
    }

    public async Task<List<MonitorService>> GetByInfraObjectIdAndBusinessFunctionId(string infraObjectId,
        string businessServiceId)
    {
        return await _dbContext.MonitorServices
            .Where(x => x.InfraObjectId.Equals(infraObjectId) && x.BusinessServiceId.Equals(businessServiceId) &&
                        x.IsActive).ToListAsync();
    }

 
    public override async Task<PaginatedResult<MonitorService>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<MonitorService> productFilterSpec, string sortColumn, string sortOrder)
    {
        return await FilterRequiredField(Entities.Specify(productFilterSpec).DescOrderById())
           .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
        
    public override IQueryable<MonitorService> GetPaginatedQuery()
    {
        if (_loggedInUserService.IsParent)
            return Entities.Where(x => x.IsActive)
                .AsNoTracking()
                .OrderByDescending(x => x.Id);

        return Entities.Where(x => x.IsActive && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .AsNoTracking()
            .OrderByDescending(x => x.Id);
    }

    public async  Task<bool> IsMonitorServicePathExist(string servicePath, string infraObjectId, string type, string threadType, string workflowType, string workflowId, string workflowName, string serverId, string id)
    {
        if (string.IsNullOrWhiteSpace(infraObjectId) || string.IsNullOrWhiteSpace(serverId) || string.IsNullOrWhiteSpace(type))
                    return false;

        var monitors = await _dbContext.MonitorServices
            .AsNoTracking()
            .Where(e => e.InfraObjectId == infraObjectId
                     && e.ServerId == serverId
                     && e.Type == type)
            .ToListAsync();

        foreach (var monitor in monitors)
        {
            if (string.IsNullOrWhiteSpace(monitor.Properties))
                continue;

            if (!TryParseProperties(monitor.Properties, out var prop) || prop.Details == null)
                continue;
            var isWorkflow = type.ToLower().Equals("use workflow", StringComparison.OrdinalIgnoreCase);

            bool match = false;

            if (type.Equals("use workflow", StringComparison.OrdinalIgnoreCase))
            {
                if (string.IsNullOrWhiteSpace(workflowType))
                    return true;
                if (!string.Equals(prop.SubType, workflowType, StringComparison.OrdinalIgnoreCase))
                    continue;

                match = prop.Details.Any(d => d.Id == workflowId);
            }
            else
            {
                if (string.IsNullOrWhiteSpace(threadType))
                    return true;
                if (!string.Equals(prop.SubType, threadType, StringComparison.OrdinalIgnoreCase))
                    continue;

                match = prop.Details.Any(d => d.Name?.Equals(servicePath, StringComparison.OrdinalIgnoreCase) == true);
            }
            
            if (match && id.IsValidGuid() && monitor.ReferenceId == id)
                return false;

            return true;
        }

        return false;


        #region  Old Code 
        //if (!id.IsValidGuid())
        //{
        //    switch (type.ToLower())
        //    {
        //        case "use workflow":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId) && e.ServicePath.Equals(servicePath));
        //            break;
        //        case "use ps-ef":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));
        //            break;
        //        case "use wmi":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));
        //            break;
        //        case "use ssh":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));
        //            break;
        //        case "use powershell":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));
        //            break;
        //        case "use telnet":
        //            match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));
        //            break;
        //    }
        //}
        //else
        //{

        //    switch (type.ToLower())
        //    {
        //        case "use workflow":
        //            var list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId)).ToList();
        //            match = list.Unique(id);
        //            break;
        //        case "use ps-ef":
        //            list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
        //            match = list.Unique(id);
        //            break;
        //        case "use wmi":
        //            list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
        //            match = list.Unique(id);
        //            break;
        //        case "use ssh":
        //            list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
        //            match = list.Unique(id);
        //            break;
        //        case "use powershell":
        //            list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
        //            match = list.Unique(id);
        //            break;
        //        case "use telnet":
        //            list = _dbContext.MonitorServices.Where(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath)).ToList();
        //            match = list.Unique(id);
        //            break;

        //    }
        //}
        //return Task.FromResult(match);
        #endregion


    }
    public async Task<bool> IsMonitorServicePathUnique(string servicePath, string infraObjectId, string type, string threadType, string workflowType, string workflowId, string workflowName, string serverId)
    {
        #region Old Code 
        //bool match = false;
        //if (type.ToLower() == "use workflow")
        //{
        //    match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.WorkflowType.Equals(workflowType) && e.WorkflowId.Equals(workflowId));

        //}
        //else
        //{
        //    match = _dbContext.MonitorServices.Any(e => e.InfraObjectId.Equals(infraObjectId) && e.ServerId.Equals(serverId) && e.Type.Equals(type) && e.ThreadType.Equals(threadType) && e.ServicePath.Equals(servicePath));

        //}
        //return Task.FromResult(match);

        #endregion
        if (string.IsNullOrWhiteSpace(infraObjectId) || string.IsNullOrWhiteSpace(serverId) || string.IsNullOrWhiteSpace(type))
            return false;

        var monitors = await _dbContext.MonitorServices
            .AsNoTracking()
            .Where(e => e.InfraObjectId == infraObjectId
                     && e.ServerId == serverId
                     && e.Type == type)
            .ToListAsync();

        foreach (var monitor in monitors)
        {
            if (string.IsNullOrWhiteSpace(monitor.Properties))
                continue;

            if (!TryParseProperties(monitor.Properties, out var prop) || prop.Details == null)
                continue;

            bool match = false;

            if (type.Equals("use workflow", StringComparison.OrdinalIgnoreCase))
            {
                if (string.IsNullOrWhiteSpace(workflowType))
                    return true;
                if (!string.Equals(prop.SubType, workflowType, StringComparison.OrdinalIgnoreCase))
                    continue;

                match = prop.Details.Any(d => d.Id == workflowId);
            }
            else
            {
                if (string.IsNullOrWhiteSpace(threadType))
                    return true;
                if (!string.Equals(prop.SubType, threadType, StringComparison.OrdinalIgnoreCase))
                    continue;

                match = prop.Details.Any(d => d.Name?.Equals(servicePath, StringComparison.OrdinalIgnoreCase) == true);
            }

            if (match)
                return true;
        }

        return false;

    }


    private IQueryable<MonitorService>FilterRequiredField(IQueryable<MonitorService> monitors)
    {
        return monitors.Select(x => new MonitorService
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            InfraObjectId = x.InfraObjectId,
            InfraObjectName = x.InfraObjectName,
            BusinessServiceName = x.BusinessServiceName,
            BusinessServiceId = x.BusinessServiceId,
            CompanyId=x.CompanyId,
            ServerId = x.ServerId,
            ServerName = x.ServerName,
            Type = x.Type,
            //ThreadType = x.ThreadType,
            //WorkflowId = x.WorkflowId,
            //WorkflowName = x.WorkflowName,
            //WorkflowType = x.WorkflowType,
            //ServicePath = x.ServicePath,
            Status=x.Status,
           // WorkflowVersion=x.WorkflowVersion,
            IsServiceUpdate=x.IsServiceUpdate,
            NodeId=x.NodeId,
            NodeName=x.NodeName,
            //FailedActionId=x.FailedActionId,
            //FailedActionName=x.FailedActionName,
            LastModifiedDate=x.LastModifiedDate,
            LastExecutionTime=x.LastExecutionTime,
            MonitoringType = x.MonitoringType,
            Properties = x.Properties
        }); 
    }

    private static bool TryParseProperties(string json, out MonitorServicePropertyModel prop)
    {
        try
        {
            prop = JsonConvert.DeserializeObject<MonitorServicePropertyModel>(json);
            return true;
        }
        catch
        {
            prop = null;
            return false;
        }
    }
}



   

