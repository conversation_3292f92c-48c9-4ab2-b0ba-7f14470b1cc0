﻿namespace ContinuityPatrol.Domain.ViewModels.TemplateModel;

public class TemplateViewModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Properties { get; set; }
    public string Icon { get; set; }
    public string Version { get; set; }
    public string Type { get; set; }
    public string ReplicationTypeId { get; set; }
    public string ReplicationTypeName { get; set; }
    public string ReplicationCategoryTypeId { get; set; }
    public string ReplicationCategoryTypeName { get; set; }
    public string SubTypeId { get; set; }
    public string SubTypeName { get; set; }
    public string Description { get; set; }
    public string ActionType { get; set; }

    public List<TemplateListVm> Templates { get; set; }
}