﻿
const loadComponentFailureChart = (data) => {
    // Create chart instance
    var chart = am4core.create("ComponentFailure", am4charts.PieChart);
    if (chart.logo) {
        chart.logo.disabled = true;
    }
    // Add data

    if (data?.failedCount === 0) {

        chart.data = [{
            "country": "No Data",
            "litres": 1,
            "color": am4core.color("#d3d3d3") 
        }]

    } else {
        chart.data = [{
            "country": "Application",
            "litres": data?.applicationDownCount ?? 0,
            "color": am4core.color("#0479ff")
        }, {
            "country": "Database",
            "litres": data?.databaseDownCount ?? 0,
            "color": am4core.color("#ffcb00")
        }, {
            "country": "Storage",
            "litres": data?.storageDownCount ?? 0,
            "color": am4core.color("#00acef")
        }, {
            "country": "Replication",
            "litres": data?.replicationDownCount ?? 0,
            "color": am4core.color("#07db8f")
        }, {
            "country": "Network",
            "litres": data?.networkDownCount ?? 0,
            "color": am4core.color("#ff4191")
        }, {
            "country": "Server",
            "litres": data?.serverDatabaseDownCount ?? 0,
            "color": am4core.color("#8960ff")
        }];
    }

    // Add and configure Series
    var pieSeries = chart.series.push(new am4charts.PieSeries());
    pieSeries.dataFields.value = "litres";
    pieSeries.dataFields.category = "country";
    pieSeries.slices.template.propertyFields.fill = "color";

    // Let's cut a hole in our Pie chart the size of 40% the radius
    chart.innerRadius = am4core.percent(60);
    chart.padding(-10, -10, -10, -10);

    // Disable ticks and labels
    pieSeries.labels.template.disabled = true;
    pieSeries.ticks.template.disabled = true;

    // Disable tooltips
 
    if (data?.failedCount !== 0) {
        pieSeries.slices.template.tooltipText = "{category}: {value}";
    } else {
        pieSeries.slices.template.tooltipText = "";
    }
    // Add a legend
    //chart.legend = new am4charts.Legend();
    //chart.legend.position = "right";
    //chart.legend.valueLabels.template.disabled = true;
    //chart.legend.labels.template.text = "[font-size:11px {color}]{name}";

    let label = chart.seriesContainer.createChild(am4core.Label);

    if (data?.failedCount === 0) {
        label.text = `[bold]NA[/]`;
    } else {
        label.text = `[bold]${data?.failedCount}[/] \n [font-size:10px] Components \n Failed [/]`;
    }

    label.horizontalCenter = "middle";
    label.verticalCenter = "middle";
    label.textAlign = "middle";
    label.fontSize = 17;

    //var markerTemplate = chart.legend.markers.template;
    //markerTemplate.width = 20;
    //markerTemplate.height = 10;

}