﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.LicenseManager.Events.Decommission;

public class LicenseDecommissionEventHandler : INotificationHandler<LicenseDecommissionEvent>
{
    private readonly ILogger<LicenseDecommissionEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public LicenseDecommissionEventHandler(ILogger<LicenseDecommissionEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(LicenseDecommissionEvent decommissionEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.LicenseManager}",
            Entity = Modules.LicenseManager.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails =
                $"License '{decommissionEvent.PONumber}' of {decommissionEvent.EntityType} '{decommissionEvent.EntityName}' & associated data were deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"License '{decommissionEvent.PONumber}' of {decommissionEvent.EntityType} '{decommissionEvent.EntityName}' & associated data were deleted successfully.");
    }
}