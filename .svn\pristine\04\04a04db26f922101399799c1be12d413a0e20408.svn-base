﻿using ContinuityPatrol.Application.Features.Alert.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Alert.Commands;

public class UpdateAlertTests : IClassFixture<AlertFixture>
{
    private readonly AlertFixture _alertFixture;
    private readonly Mock<IAlertRepository> _mockAlertRepository;
    private readonly UpdateAlertCommandHandler _handler;

    public UpdateAlertTests(AlertFixture alertFixture)
    {
        _alertFixture = alertFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockAlertRepository = AlertRepositoryMocks.UpdateAlertRepository(_alertFixture.Alerts);

        _handler = new UpdateAlertCommandHandler(_mockAlertRepository.Object, _alertFixture.Mapper, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_ValidAlert_UpdateToAlertsRepo()
    {
        _alertFixture.UpdateAlertCommand.Id = _alertFixture.Alerts[0].ReferenceId;

        var result = await _handler.Handle(_alertFixture.UpdateAlertCommand, CancellationToken.None);

        var alert = await _mockAlertRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_alertFixture.UpdateAlertCommand.Type, alert.Type);
    }

    [Fact]
    public async Task Handle_Return_UpdateAlertResponse_When_Alert_Updated()
    {
        _alertFixture.UpdateAlertCommand.Id = _alertFixture.Alerts[0].ReferenceId;

        var result = await _handler.Handle(_alertFixture.UpdateAlertCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateAlertResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_alertFixture.UpdateAlertCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotException_When_InvalidAlertId()
    {
        _alertFixture.UpdateAlertCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_alertFixture.UpdateAlertCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsync_OnlyOnce()
    {
        _alertFixture.UpdateAlertCommand.Id = _alertFixture.Alerts[0].ReferenceId;

        await _handler.Handle(_alertFixture.UpdateAlertCommand, CancellationToken.None);

        _mockAlertRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockAlertRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Alert>()), Times.Once);
    }
}