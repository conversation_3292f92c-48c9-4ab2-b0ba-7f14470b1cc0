﻿//using AutoMapper;
//using ContinuityPatrol.Application.Features.BusinessService.Queries.GetBusinessServiceDiagramDetail;
//using ContinuityPatrol.Application.Features.DashboardView.Queries.GetBusinessViewPaginatedList;
//using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
//using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;
//using ContinuityPatrol.Domain.ViewModels.DashboardViewModel;
//using ContinuityPatrol.Domain.ViewModels.GetBusinessServiceIdByCount;
//using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
//using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
//using ContinuityPatrol.Shared.Core.Wrapper;
//using ContinuityPatrol.Shared.Services.Contract;
//using ContinuityPatrol.Shared.Services.Provider;
//using ContinuityPatrol.Web.Areas.Dashboard.Controllers;
//using Microsoft.AspNetCore.Mvc;
//using Microsoft.Extensions.Logging;
//using Moq;

//namespace ContinuityPatrol.Web.UnitTests.Areas.Dashboard.Controllers;

//public class BusinessViewNewControllerTests
//{
//    private readonly Mock<IBusinessServiceService> _mockBusinessServiceService = new();
//    private readonly Mock<IDataProvider> _mockProvider =  new ();
//    private readonly Mock<IMapper> _mockMapper = new();
//    private readonly Mock<ILogger<BusinessViewNewController>> _mockLogger = new();
//    private BusinessViewNewController _controller;

//    public BusinessViewNewControllerTests()
//    {
//        Initialize();
//    } 
//    public void Initialize()
//    { 
//        _controller = new BusinessViewNewController(
//            _mockBusinessServiceService.Object,
//            _mockProvider.Object,
//            _mockMapper.Object,
//            _mockLogger.Object
//        );
//    }

//    [Fact]
//    public async Task List_ReturnsViewResult_WithDashboardBusinessViewModel()
//    {          
//        var businessView = new PaginatedResult<BusinessViewPaginatedList>();
//        var businessServiceAvailability = new List<BusinessServiceAvailabilityListVm>();

//        _mockProvider.Setup(p => p.DashboardView.GetPaginatedBusinessViews(It.IsAny<GetBusinessViewPaginatedListQuery>()))
//            .ReturnsAsync(businessView);
//        _mockProvider.Setup(p => p.BusinessServiceAvailability.GetBusinessServiceAvailabilityList())
//            .ReturnsAsync(businessServiceAvailability);

//        var result = await _controller.List() as ViewResult;
//        var model = result?.Model as DashboardBusinessViewModel;

//        Assert.NotNull(result);
//        Assert.IsType<DashboardBusinessViewModel>(model);
//        Assert.Equal(businessView, model.GetPaginatedBusinessViews);
//        Assert.Equal(businessServiceAvailability, model.BusinessServiceAvailabilityList);
//    }

//    [Fact]
//    public async Task BusinessServiceOverview_ReturnsJsonResult_WithDashboardBusinessViewModel()
//    {
//        var businessView = new PaginatedResult<BusinessViewPaginatedList>();
//        var dashboardViewModel = new DashboardBusinessViewModel
//        {
//            GetPaginatedBusinessViews = businessView,
//        };

//        _mockProvider.Setup(p => p.DashboardView.GetPaginatedBusinessViews(It.IsAny<GetBusinessViewPaginatedListQuery>()))
//            .ReturnsAsync(businessView);
      
//        var result = await _controller.BusinessServiceOverview() as JsonResult;
//        var model = result?.Value as DashboardBusinessViewModel;

//        Assert.NotNull(result);
//        Assert.IsType<DashboardBusinessViewModel>(model);
//        Assert.Equal(businessView, model.GetPaginatedBusinessViews);
//    }


//    [Fact]
//    public async Task BusinessService_ReturnsJsonResult_WithDashboardBusinessViewModel_WhenBusinessServiceIdIsValid()
//    {

//        var businessServiceId = "testServiceId";
//        var profileExecuted = new ProfileExecutorByBusinessServiceIdVm { LastModifiedDate = DateTime.UtcNow };
//        var businessFunctionAffected = new AllCount(); 

//        var dashboardViewModel = new DashboardBusinessViewModel
//        {
//            ProfileExecute = profileExecuted,
//            GetBusinessServiceIdByCount = businessFunctionAffected, 
//        };

//        _mockProvider.Setup(p => p.WorkflowOperation.GetProfileExecutorByBusinessServiceId(businessServiceId))
//            .ReturnsAsync(profileExecuted);
//        _mockProvider.Setup(p => p.DrReadyStatus.GetBusinessServiceIdByCount(businessServiceId))
//            .ReturnsAsync(businessFunctionAffected);

//        var result = await _controller.BusinessService(businessServiceId) as JsonResult;
//        var model = result?.Value as DashboardBusinessViewModel;
      
//        Assert.NotNull(result);
//        Assert.IsType<DashboardBusinessViewModel>(model);
//        Assert.Equal(profileExecuted, model.ProfileExecute);
//        Assert.Equal(businessFunctionAffected, model.GetBusinessServiceIdByCount);
//    }


//    [Fact]
//    public async Task BusinessService_ReturnsJsonResult_Empty_WhenBusinessServiceIdIsNullOrWhiteSpace()
//    {
//        var invalidId = string.Empty;

//        var result = await _controller.BusinessService(invalidId) as JsonResult;
//        var model = result?.Value as string;

//        Assert.NotNull(result);
//        Assert.Equal(string.Empty, model);
//    }

//    [Fact]
//    public async Task ImpactDetails_ReturnsJsonResult_WithFilteredHeatMapStatusListVm()
//    {
//        var businessServiceId = "testServiceId";
//        var heatmapType = "testHeatmapType";
//        var heatmapDownDetails = new List<HeatMapStatusListVm>
//        {
//            new HeatMapStatusListVm { IsAffected = true },
//            new HeatMapStatusListVm { IsAffected = false }
//        };

//        var serviceHeatmap = new DashboardBusinessViewModel
//        {
//            GetHeatMapStatusByType = new List<HeatMapStatusListVm> { heatmapDownDetails[0] }
//        };

//        _mockProvider.Setup(p => p.HeatMapStatus.GetHeatMapStatusByType(businessServiceId, heatmapType))
//            .ReturnsAsync(heatmapDownDetails);

//        var result = await _controller.ImpactDetails(businessServiceId, heatmapType) as JsonResult;
//        var model = result?.Value as DashboardBusinessViewModel;

//        Assert.NotNull(result);
//        Assert.IsType<DashboardBusinessViewModel>(model);
//        Assert.Single(model.GetHeatMapStatusByType);
//        Assert.Equal(heatmapDownDetails[0], model.GetHeatMapStatusByType[0]);
//    }

//    [Fact]
//    public async Task ImpactDetails_ReturnsJsonResult_Empty_WhenHeatmapTypeIsNullOrWhiteSpace()
//    {
//        var businessServiceId = "testServiceId";
//        var invalidHeatmapType = string.Empty;

//        var result = await _controller.ImpactDetails(businessServiceId, invalidHeatmapType) as JsonResult;
//        var model = result?.Value as string;

//        Assert.NotNull(result);
//        Assert.Equal(string.Empty, model);
//    }

//    [Fact]
//    public async Task GetBusinessFunctionView_ReturnsJsonResult_WithBusinessFunctionView()
//    {
//        var businessFunction = "testFunction";
//        var businessFunctionView = new List<DashboardViewByBusinessFunctionIdVm>();

//        _mockProvider.Setup(p => p.DashboardView.GetDashboardViewListByBusinessFunctionId(businessFunction))
//            .ReturnsAsync(businessFunctionView);

//        var result = await _controller.GetBusinessFunctionView(businessFunction) as JsonResult;
//        var model = result?.Value as List<DashboardViewByBusinessFunctionIdVm>;

//        Assert.NotNull(result);
//        Assert.IsType<List<DashboardViewByBusinessFunctionIdVm>>(model);
//        Assert.Equal(businessFunctionView, model);
//    }

//    [Fact]
//    public async Task GetBusinessFunctionView_ReturnsJsonResult_Empty_WhenBusinessFunctionIsNullOrWhiteSpace()
//    {
//        var invalidFunction = string.Empty;

//        var result = await _controller.GetBusinessFunctionView(invalidFunction) as JsonResult;
//        var model = result?.Value as string;

//        Assert.NotNull(result);
//        Assert.Equal(string.Empty, model);
//    }

//    [Fact]
//    public async Task BusinessTreeViewList_ReturnsJsonResult_WithTreeView()
//    {
//        var businessServiceId = "testServiceId";
//        var treeView = new GetBusinessServiceDiagramDetailVm();
//        _mockBusinessServiceService.Setup(b => b.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId))
//            .ReturnsAsync(treeView);

//        var result = await _controller.BusinessTreeViewList(businessServiceId) as JsonResult;
//        var model = result?.Value;

//        Assert.NotNull(result);
//        Assert.Equal(treeView, model);
//    }

//    [Fact]
//    public async Task BusinessTreeViewList_ReturnsJsonResult_Empty_WhenBusinessServiceIdIsNullOrWhiteSpace()
//    {
//        var invalidId = string.Empty;

//        var result = await _controller.BusinessTreeViewList(invalidId) as JsonResult;
//        var model = result?.Value as string;

//        Assert.NotNull(result);
//        Assert.Equal(string.Empty, model);
//    }
//}