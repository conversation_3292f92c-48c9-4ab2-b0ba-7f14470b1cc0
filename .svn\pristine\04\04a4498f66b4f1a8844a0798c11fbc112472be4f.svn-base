﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Events.Update;

public class WorkflowCategoryUpdatedEventHandler : INotificationHandler<WorkflowCategoryUpdatedEvent>
{
    private readonly ILogger<WorkflowCategoryUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowCategoryUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowCategoryUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowCategoryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} Action Builder",
            Entity = "Action Builder",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Action Builder '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Action Builder '{updatedEvent.Name}' updated successfully.");
    }
}