﻿using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Create;
using ContinuityPatrol.Application.Features.CredentialProfile.Commands.Update;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CredentialProfile.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CredentialProfileModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICredentialProfileService
{
    Task<List<CredentialProfileNameVm>> GetCredentialProfileNames();
    Task<List<CredentialProfileListVm>> GetCredentialProfileList();
    Task<BaseResponse> CreateAsync(CreateCredentialProfileCommand createCredentialProfileCommand);
    Task<BaseResponse> UpdateAsync(UpdateCredentialProfileCommand updateCredentialProfileCommand);
    Task<BaseResponse> DeleteAsync(string credentialProfileId);
    Task<CredentialProfileDetailVm> GetByReferenceId(string credentialProfileId);
    Task<bool> IsServerNameExist(string credentialProfileName, string id);
    Task<PaginatedResult<CredentialProfileListVm>> GetPaginatedCredentialProfiles(GetCredentialProfilePaginatedListQuery query);
}