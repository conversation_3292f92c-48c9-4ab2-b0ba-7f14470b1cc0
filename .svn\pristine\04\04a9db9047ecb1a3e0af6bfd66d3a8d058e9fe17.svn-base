﻿@using ContinuityPatrol.Shared.Services.Helper;
@model ContinuityPatrol.Domain.ViewModels.LicenseManagerModel.BaseLicenseViewModel

@{
    ViewData["Title"] = "LicenseExpiredLanding";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<link href="~/css/form.css" rel="stylesheet" />
<link href="~/css/login.css" rel="stylesheet" />

@Html.AntiForgeryToken()
<div class="align-items-center d-grid card vh-100 Card_Design_None">
    <div class="card-header position-absolute top-0 end-0">
        <button class="btn btn-primary" type="button" data-bs-toggle="modal" id="requestlicense" data-bs-target="#LicenseRequest">License Request</button>
    </div>
    <div class="text-center card-body text-dark mx-auto">
        <img class="card-img" src="~/img/isomatric/license-expired.svg" alt="license_expired.svg"
             height="300">
        <h5 id="txtheader" class="mb-2 mt-4 ">Kindly contact CP Admin</h5>
        <p id="txtparagraph" class="mb-2">To Continue using product<br></p>
        <div class="input-group border-secondary-subtle border text-start" style="line-height: 2.3;">
            <div class="input-group-text ps-1"><i class="cp-license-key"></i></div>
            <select class="form-select" id="ddlLicenseKey" data-placeholder="Select License Key">
                <option></option>
                
            </select>
            @* <div class="input-group-text pe-2"><i class="cp-down-arrow fs-7"></i></div> *@
        </div>
        <a role="button" id="backToLoginLink" asp-area="" asp-controller="Account" asp-action="Logout" data-isParent="@WebHelper.UserSession.IsParent" data-isLicense=@WebHelper.UserSession.LicenseExpiry class="mt-3 btn btn-primary" title="Back to Login">Back to Login?</a>
        <button id="licenseKeyButton" type="button" title="I Have a License Key" class="mt-3 btn btn-primary" data-bs-toggle="modal" data-bs-target="#CreateModal">I Have a License Key</button>
    </div>
</div>

<!-- category Modal -->
<div class="modal fade" id="CreateModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg">
        <div class="modal-content">
            <div id="example-form" >
            <div class="modal-header">
                    <h6 class="page_title" title="Renewal License" style="color: var(--bs-dark);font-weight: var(--bs-title-font-weight); margin-bottom: 0px;display: flex;align-items: center;">
                   <i class="cp-license-manager me-1"></i><span>Renewal License</span>
                </h6>
                <button type="button"  class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="wizard-content">
                    
                        <section>
                            <div class="mb-3 form-group">
                                <div class="form-label" title="Name">License Key</div>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input type="text" class="form-control" onpaste="handlePaste()" autocomplete="off" placeholder="License Key" id="licensekey" />
                                    <input  type="hidden" id="licenseId" />
                                </div>
                                <span  id="License-error"></span>
                            </div>
                        </section>                       
                   
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-between align-items-center">
                <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
                <div class="gap-2 d-flex">
                    <button type="button" class="btn btn-secondary btn-sm" title="Cancel" data-bs-dismiss="modal" style="min-width:80px">Cancel</button>
                    <button class="btn btn-primary btn-sm" href="javascript:void(0)" title="Save" style="min-width:80px;cursor: none; pointer-events: none; opacity: 0.5;"id="RenewFunction">Save</button>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="LicenseRequest" data-bs-backdrop="static" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-lg">
        <div class="modal-content">
            <div class="modal-header pb-0 border-0">
                <h6 class="page_title" title="Authenticate"><i class="cp-license-request me-2"></i><span>License Request Configuration</span></h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="myDiv">
                        <div class="form-label">Type</div>
                        <div class="my-2 ">
                            <div class="d-flex gap-4 flex-wrap mb-4">
                                <div class="position-relative Renewal">
                                    <input class="btn-check check_license" type="radio" name="type" value="Renewal" id="option1" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option1">
                                        <i class="cp-license-renewal fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title="Renewal">Renewal</div>

                                </div>
                                <div class="position-relative Upgrade">
                                    <input class="btn-check check_license" type="radio" name="type" value="Upgrade" id="option2" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option2">
                                        <i class="cp-license-upgrade fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title=Upgrade>Upgrade</div>

                                </div>
                                <div class="position-relative AMC ">
                                    <input class="btn-check check_license" type="radio" name="type" value="AMC" id="option3" data-toggle="button" autocomplete="off">
                                    <label class="site_type btn border-secondary-subtle" for="option3">
                                        <i class="cp-AMC fs-1"></i><br>
                                    </label>
                                    <div class="text-center mt-2 d-block text-truncate" style="max-width:80px" title="AMC">AMC</div>

                                </div>

                            </div>
                            <div class="form-group d-none" id="renewdate">

                                <div>
                                    <div class="form-check form-check-inline renewtype">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox1" value="CountOnly">
                                        <label class="form-check-label" for="inlineCheckbox1">Count Only</label>
                                    </div>
                                    <div class="form-check form-check-inline renewchild">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox2" value="ExpiryDateOnly">
                                        <label class="form-check-label" for="inlineCheckbox2">Expiry Date Only</label>
                                    </div>
                                    <div class="form-check form-check-inline renewchild">
                                        <input class="form-check-input renewtype" type="checkbox" name="renewtype" id="inlineCheckbox3" value="Both">
                                        <label class="form-check-label" for="inlineCheckbox3">Both</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group d-none" id="amcRenew">

                                <div>
                                    <div class="form-check form-check-inline amcnew">
                                        <input class="form-check-input amctype" type="checkbox" name="amctype" id="chkamcNew" value="AMCNew">
                                        <label class="form-check-label" for="inlineCheckbox1">AMC New</label>
                                    </div>
                                    <div class="form-check form-check-inline amcrenewal">
                                        <input class="form-check-input amctype" type="checkbox" name="amctype" id="chkamcRenewal" value="AMCRenewal">
                                        <label class="form-check-label" for="inlineCheckbox2">AMC Renewal</label>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="myDiv2 d-none" style="word-wrap: break-word;">
                        <div class="form-label">License Key </div>
                        <div style="height:250px;overflow:auto">
                            <p class="my-2" id="encryptlicense">

                            </p>
                        </div>
                     
                    </div>
                    <span id="encryptlicensecheck"></span>
                </div>

            </div>
            <div class="modal-footer d-flex justify-content-between align-items-center pt-0 border-0">
                <small class="text-secondary"><i class=" me-1"></i></small>
                <div class="gap-2 d-flex align-items-center">
                    <span><i class="cp-copy fs-5 " title="Copy" id="copy_request" role="button"></i></span>
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-sm" id="generate">Generate</button>
                </div>
            </div>

        </div>
    </div>
</div>
@* Notification *@
<div class='Notification'>
    <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
        <div class='d-flex'>
            <div class='toast-body'>
                <span id="alertClass" class='success-toast'>
                    <i id="icon_Detail" class='cp-check toast_icon'></i>
                </span>
                <span id="notificationAlertmessage">

                </span>
            </div>
            <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
        </div>
    </div>
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
   

</script>

<script src="~/js/Admin/License/License Manager/licenseExpired.js"></script>

