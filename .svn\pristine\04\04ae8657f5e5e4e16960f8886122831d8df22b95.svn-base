﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class TeamResourceRepositoryMocks
{
    public static Mock<ITeamResourceRepository> CreateTeamResourceRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamResources);

        teamResourceRepository.Setup(repo => repo.AddAsync(It.IsAny<TeamResource>())).ReturnsAsync((TeamResource teamResource) =>
        {
            teamResource.Id = new Fixture().Create<int>();

            teamResource.ReferenceId = new Fixture().Create<Guid>().ToString();

            teamResources.Add(teamResource);

            return teamResource;
        });

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> UpdateTeamResourceRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamResources);

        teamResourceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamResources.SingleOrDefault(x => x.ReferenceId == i));

        teamResourceRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TeamResource>())).ReturnsAsync((TeamResource teamResource) =>
        {

            var index = teamResources.FindIndex(item => item.Id == teamResource.Id);

            teamResources[index] = teamResource;

            return teamResource;
        });

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> DeleteTeamResourceRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamResources);

        teamResourceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamResources.SingleOrDefault(x => x.ReferenceId == i));

        teamResourceRepository.Setup(repo => repo.UpdateAsync(It.IsAny<TeamResource>())).ReturnsAsync((TeamResource teamResource) =>
        {
            var index = teamResources.FindIndex(item => item.Id == teamResource.Id);

            teamResource.IsActive = false;

            teamResources[index] = teamResource;

            return teamResource;
        });

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetTeamResourceRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(teamResources);

        teamResourceRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => teamResources.SingleOrDefault(x => x.ReferenceId == i));

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetTeamMasterIdByTeamResourceId(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.GetTeamMasterIdByTeamResource(It.IsAny<string>())).ReturnsAsync(teamResources);

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetTeamResourceEmptyRepository()
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<TeamResource>());

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetTeamResourceNamesRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.GetTeamResourceNames()).ReturnsAsync(teamResources);

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetTeamResourceNameUniqueRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        teamResourceRepository.Setup(repo => repo.IsTeamResourceNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => teamResources.Exists(x => x.ResourceName == i && x.ReferenceId == j));

        return teamResourceRepository;
    }

    public static Mock<ITeamResourceRepository> GetPaginatedTeamResourceRepository(List<TeamResource> teamResources)
    {
        var teamResourceRepository = new Mock<ITeamResourceRepository>();

        var queryableTeamResource = teamResources.BuildMock();

        teamResourceRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableTeamResource);

        return teamResourceRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateTeamResourceEventRepository(List<UserActivity> userActivities)
    {
        var mockTeamResourceEventRepository = new Mock<IUserActivityRepository>();

        mockTeamResourceEventRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(userActivities);

        mockTeamResourceEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
            (UserActivity userActivity) =>
            {
                userActivity.Id = new Fixture().Create<int>();

                userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();

                userActivities.Add(userActivity);

                return userActivity;
            });

        return mockTeamResourceEventRepository;
    }
}
