﻿<div class="modal-dialog modal-dialog-scrollabel modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-conditional"></i><span class="mt-2">Set Condition</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body" style="min-height: 27em;">
            <div class="form-group mb-2">
                <div class="form-label"></div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-condition"></i></span>
                    <select class="form-select-modal" data-live-search="true" id="gotoActionCondition"  data-placeholder="Select Condition">
                        <option value=''>Select Condition</option>
                        <option value='ifcondition'>Success</option>
                        <option value='elsecondition'>Failure</option>
                    </select>
                </div>
                <span id="gotoDropdown-error"></span>
            </div>
            <div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input GoToCheck" name="workflowActions" value="workflowAction" id="workflowActionId" checked />
                    <label class='form-check-label'>Action</label>
                </div>
                <div class="form-check form-check-inline">
                    <input type="radio" class="form-check-input GoToCheck" name="workflowGroups" value="workflowGroup" id="workflowGroupId" />
                    <label class='form-check-label'>Group</label>
                </div>
            </div>          
            <div class="form-group" id="ActionSelectCont">
                <div class="form-label"></div>
                <div class="input-group">
                    <span class="input-group-text"><i class="cp-action-name" id="change_icon"></i></span>
                    <select class="form-select-modal select_actions" data-live-search="true" id="gotoActionList" name="operationId" data-placeholder="Select action">
                    </select>
                </div>
                <span id="workflowActionDropdown-error"></span>
            </div>
            <div class="form-group" id="failCountConainer" style="display:none;">
                <div class="form-label">
                    <small>Failure Count</small>
                </div>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="cp-time"></i>
                    </span>
                    <input type="Number" class="form-control Common-input" placeholder="Enter Failure Count" name="failureCount"
                           id="failureCount" max="99" min="1" value="" />

                </div>
                <span id="failCount-error"></span>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" id="btnCreateCondition">Create</button>
        </div>
    </div>
</div>
