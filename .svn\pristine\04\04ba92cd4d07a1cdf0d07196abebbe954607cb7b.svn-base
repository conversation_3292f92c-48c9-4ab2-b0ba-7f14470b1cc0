﻿namespace ContinuityPatrol.Domain.Entities;

public class InfraObjectSchedulerLogs : AuditableEntity
{
    public string CompanyId { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public string WorkflowTypeId { get; set; }
    public string WorkflowType { get; set; }
    public string BeforeSwitchOverWorkflowId { get; set; }
    public string BeforeSwitchOverWorkflowName { get; set; }
    public string AfterSwitchOverWorkflowId { get; set; }
    public string AfterSwitchOverWorkflowName { get; set; }
    public int ScheduleType { get; set; }
    public string CronExpression { get; set; }
    public string ScheduleTime { get; set; }
    public string Status { get; set; }
    public string NodeId { get; set; }
    public string NodeName { get; set; }
    public string State { get; set; }
    public int IsSchedule { get; set; }
    public string WorkflowVersion { get; set; }
    public string GroupPolicyId { get; set; }
    public string GroupPolicyName { get; set; }
    public string ExecutionPolicy { get; set; }
    public bool IsEnable { get; set; }
    public string LastExecutionTime { get; set; }
    [Column(TypeName = "NCLOB")] public string ExceptionMessage { get; set; }
}
