using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Application.Features.Form.Commands.Delete;
using ContinuityPatrol.Application.Features.Form.Commands.Import;
using ContinuityPatrol.Application.Features.Form.Commands.Lock;
using ContinuityPatrol.Application.Features.Form.Commands.Publish;
using ContinuityPatrol.Application.Features.Form.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Form.Commands.Update;
using ContinuityPatrol.Application.Features.Form.ImportFormPlugins;
using ContinuityPatrol.Application.Features.Form.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Form.Queries.GetList;
using ContinuityPatrol.Application.Features.Form.Queries.GetNames;
using ContinuityPatrol.Application.Features.Form.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Form.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Form.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class FormsFixture : IDisposable
{
    public CreateFormCommand CreateFormCommand { get; }
    public CreateFormResponse CreateFormResponse { get; }
    public UpdateFormCommand UpdateFormCommand { get; }
    public UpdateFormResponse UpdateFormResponse { get; }
    public DeleteFormCommand DeleteFormCommand { get; }
    public DeleteFormResponse DeleteFormResponse { get; }
    public GetFormDetailQuery GetFormDetailQuery { get; }
    public FormDetailVm FormDetailVm { get; }
    public GetFormListQuery GetFormListQuery { get; }
    public List<FormListVm> FormListVm { get; }
    public GetFormNameQuery GetFormNameQuery { get; }
    public List<FormNameVm> FormNameVm { get; }
    public GetFormNameUniqueQuery GetFormNameUniqueQuery { get; }
    public GetFormPaginatedListQuery GetFormPaginatedListQuery { get; }
    public PaginatedResult<FormListVm> FormPaginatedResult { get; }
    public GetFormTypeQuery GetFormTypeQuery { get; }
    public List<FormTypeVm> FormTypeVm { get; }
    public SaveAsFormCommand SaveAsFormCommand { get; }
    public SaveAsFormResponse SaveAsFormResponse { get; }
    public ImportPluginListCommand ImportPluginListCommand { get; }
    public ImportFormPluginResponse ImportFormPluginResponse { get; }
    public ImportFormCommand ImportFormCommand { get; }
    public ImportFormResponse ImportFormResponse { get; }
    public UpdateFormPublishCommand UpdateFormPublishCommand { get; }
    public UpdateFormPublishResponse UpdateFormPublishResponse { get; }
    public UpdateFormLockCommand UpdateFormLockCommand { get; }
    public UpdateFormLockResponse UpdateFormLockResponse { get; }

    public FormsFixture()
    {
        CreateFormCommand = AutoFormsFixture.Create<CreateFormCommand>();
        CreateFormResponse = AutoFormsFixture.Create<CreateFormResponse>();
        UpdateFormCommand = AutoFormsFixture.Create<UpdateFormCommand>();
        UpdateFormResponse = AutoFormsFixture.Create<UpdateFormResponse>();
        DeleteFormCommand = AutoFormsFixture.Create<DeleteFormCommand>();
        DeleteFormResponse = AutoFormsFixture.Create<DeleteFormResponse>();
        GetFormDetailQuery = AutoFormsFixture.Create<GetFormDetailQuery>();
        FormDetailVm = AutoFormsFixture.Create<FormDetailVm>();
        GetFormListQuery = AutoFormsFixture.Create<GetFormListQuery>();
        FormListVm = AutoFormsFixture.CreateMany<FormListVm>(3).ToList();
        GetFormNameQuery = AutoFormsFixture.Create<GetFormNameQuery>();
        FormNameVm = AutoFormsFixture.CreateMany<FormNameVm>(3).ToList();
        GetFormNameUniqueQuery = AutoFormsFixture.Create<GetFormNameUniqueQuery>();
        GetFormPaginatedListQuery = AutoFormsFixture.Create<GetFormPaginatedListQuery>();
        FormPaginatedResult = AutoFormsFixture.Create<PaginatedResult<FormListVm>>();
        GetFormTypeQuery = AutoFormsFixture.Create<GetFormTypeQuery>();
        FormTypeVm = AutoFormsFixture.CreateMany<FormTypeVm>(3).ToList();
        SaveAsFormCommand = AutoFormsFixture.Create<SaveAsFormCommand>();
        SaveAsFormResponse = AutoFormsFixture.Create<SaveAsFormResponse>();
        ImportPluginListCommand = AutoFormsFixture.Create<ImportPluginListCommand>();
        ImportFormPluginResponse = AutoFormsFixture.Create<ImportFormPluginResponse>();
        ImportFormCommand = AutoFormsFixture.Create<ImportFormCommand>();
        ImportFormResponse = AutoFormsFixture.Create<ImportFormResponse>();
        UpdateFormPublishCommand = AutoFormsFixture.Create<UpdateFormPublishCommand>();
        UpdateFormPublishResponse = AutoFormsFixture.Create<UpdateFormPublishResponse>();
        UpdateFormLockCommand = AutoFormsFixture.Create<UpdateFormLockCommand>();
        UpdateFormLockResponse = AutoFormsFixture.Create<UpdateFormLockResponse>();
    }

    public Fixture AutoFormsFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture for enterprise forms scenarios
            fixture.Customize<CreateFormCommand>(c => c
                .With(x => x.Name, "Enterprise Risk Assessment Form")
                .With(x => x.CompanyId, Guid.NewGuid().ToString())
                .With(x => x.Type, "Risk Management")
                .With(x => x.Properties, "{\"sections\":[{\"title\":\"Risk Analysis\",\"fields\":[{\"name\":\"riskLevel\",\"type\":\"select\",\"options\":[\"Low\",\"Medium\",\"High\",\"Critical\"]}]}]}")
                .With(x => x.IsPublish, false)
                .With(x => x.Version, "1.0")
                .With(x => x.IsLock, false));

            fixture.Customize<UpdateFormCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Updated Enterprise Risk Assessment Form")
                .With(x => x.Type, "Risk Management")
                .With(x => x.Properties, "{\"sections\":[{\"title\":\"Updated Risk Analysis\",\"fields\":[{\"name\":\"riskLevel\",\"type\":\"select\",\"options\":[\"Low\",\"Medium\",\"High\",\"Critical\",\"Extreme\"]}]}]}")
                .With(x => x.Version, "1.1")
                .With(x => x.IsPublish, true)
                .With(x => x.IsLock, false)
                .With(x => x.IsRestore, false));

            fixture.Customize<DeleteFormCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString()));

            fixture.Customize<CreateFormResponse>(c => c
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form created successfully"));

            fixture.Customize<UpdateFormResponse>(c => c
                .With(x => x.Success, true)
                .With(x => x.Message, "Form updated successfully"));

            fixture.Customize<DeleteFormResponse>(c => c
                .With(x => x.Success, true)
                .With(x => x.Message, "Form deleted successfully")
                .With(x => x.IsActive, false));

            fixture.Customize<FormDetailVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Compliance Form")
                .With(x => x.Type, "Compliance")
                .With(x => x.Properties, "{\"sections\":[{\"title\":\"Compliance Check\",\"fields\":[{\"name\":\"complianceStatus\",\"type\":\"radio\",\"options\":[\"Compliant\",\"Non-Compliant\",\"Partially Compliant\"]}]}]}")
                .With(x => x.Version, "1.0")
                .With(x => x.IsPublish, true)
                .With(x => x.IsLock, false));

            fixture.Customize<FormListVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.CompanyId, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Business Continuity Form")
                .With(x => x.Type, "Business Continuity")
                .With(x => x.Properties, "{\"sections\":[{\"title\":\"Business Continuity\",\"fields\":[{\"name\":\"continuityLevel\",\"type\":\"select\",\"options\":[\"Low\",\"Medium\",\"High\"]}]}]}")
                .With(x => x.Version, "1.0")
                .With(x => x.IsPublish, true)
                .With(x => x.IsAttach, false)
                .With(x => x.IsLock, false)
                .With(x => x.LastModifiedDate, DateTime.UtcNow));

            fixture.Customize<FormNameVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Security Form")
                .With(x => x.IsPublish, true));

            fixture.Customize<GetFormDetailQuery>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetFormNameUniqueQuery>(c => c
                .With(x => x.FormName, "Enterprise Form")
                .With(x => x.FormId, Guid.NewGuid().ToString()));

            fixture.Customize<GetFormPaginatedListQuery>(c => c
                .With(x => x.PageNumber, 1)
                .With(x => x.PageSize, 10)
                .With(x => x.Type, "Enterprise"));

            fixture.Customize<PaginatedResult<FormListVm>>(c => c
                .With(x => x.Succeeded, true)
                .With(x => x.PageSize, 10)
                .With(x => x.CurrentPage, 1));

            fixture.Customize<GetFormTypeQuery>(c => c
                .With(x => x.Type, "Risk Management"));

            fixture.Customize<FormTypeVm>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Name, "Enterprise Risk Form")
                .With(x => x.Type, "Risk Management")
                .With(x => x.Properties, "{\"sections\":[{\"title\":\"Risk Assessment\",\"fields\":[{\"name\":\"riskLevel\",\"type\":\"select\"}]}]}")
                .With(x => x.Version, "1.0")
                .With(x => x.IsPublish, true)
                .With(x => x.IsLock, false));

            fixture.Customize<SaveAsFormCommand>(c => c
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.Name, "Copy of Enterprise Risk Assessment Form")
                .With(x => x.Version, "1.0"));

            fixture.Customize<SaveAsFormResponse>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form saved as new form successfully"));

            fixture.Customize<ImportPluginListCommand>(c => c
                .With(x => x.ImportPlugins, "{\"plugins\":[{\"name\":\"RiskPlugin\",\"version\":\"1.0\",\"type\":\"assessment\"}]}"));

            fixture.Customize<ImportFormPluginResponse>(c => c
                .With(x => x.Success, true)
                .With(x => x.Message, "Form plugins imported successfully"));

            fixture.Customize<ImportFormCommand>(c => c
                .With(x => x.Forms, new List<FormListCommand>
                {
                    new FormListCommand
                    {
                        Id = Guid.NewGuid().ToString(),
                        Name = "Imported Enterprise Form",
                        Type = "Risk Management",
                        Properties = "{\"sections\":[{\"title\":\"Imported Risk Analysis\",\"fields\":[{\"name\":\"riskLevel\",\"type\":\"select\"}]}]}",
                        Version = "1.0",
                        IsPublish = false,
                        IsLock = false,
                        IsRestore = false
                    }
                })
                .With(x => x.FormTypeCategories, new List<FormTypeCategoryListCommand>())
                .With(x => x.ComponentTypes, new List<ComponentTypeListCommand>()));

            fixture.Customize<ImportFormResponse>(c => c
                .With(x => x.Success, true)
                .With(x => x.Message, "Forms imported successfully"));

            fixture.Customize<UpdateFormPublishCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.IsPublish, true));

            fixture.Customize<UpdateFormPublishResponse>(c => c
                .With(x => x.FormId, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form published successfully"));

            fixture.Customize<UpdateFormLockCommand>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.IsLock, true));

            fixture.Customize<UpdateFormLockResponse>(c => c
                .With(x => x.Id, Guid.NewGuid().ToString())
                .With(x => x.Success, true)
                .With(x => x.Message, "Form locked successfully"));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}
