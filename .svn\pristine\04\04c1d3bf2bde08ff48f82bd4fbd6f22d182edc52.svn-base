﻿using ContinuityPatrol.Application.Features.UserInfo.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;

namespace ContinuityPatrol.Application.UnitTests.Features.UserInfo.Queries
{
    public class GetUserInfoListQueryHandlerTests
    {
        private readonly Mock<IUserInfoRepository> _mockUserInfoRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetUserInfoListQueryHandler _handler;

        public GetUserInfoListQueryHandlerTests()
        {
            _mockUserInfoRepository = new Mock<IUserInfoRepository>();
            _mockMapper = new Mock<IMapper>();
            _handler = new GetUserInfoListQueryHandler(_mockMapper.Object, _mockUserInfoRepository.Object);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoUserInfoExists()
        {
            _mockUserInfoRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(new List<Domain.Entities.UserInfo>());

            var result = await _handler.Handle(new GetUserInfoListQuery(), CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task Handle_ReturnsMappedUserInfoList_WhenUserInfoExists()
        {
            var userInfos = new List<Domain.Entities.UserInfo>
            {
                new Domain.Entities.UserInfo { Id = 2, UserName = "Jane Doe" },
                new Domain.Entities.UserInfo { Id = 1, UserName = "John Doe" }
            };

            var userInfoVms = new List<UserInfoListVm>
            {
                new UserInfoListVm { UserId = Guid.NewGuid().ToString(), UserName = "Jane Doe" },
                new UserInfoListVm { UserId = Guid.NewGuid().ToString(), UserName = "John Doe" }
            };

            _mockUserInfoRepository
                .Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userInfos);

            _mockMapper
                .Setup(mapper => mapper.Map<List<UserInfoListVm>>(userInfos))
                .Returns(userInfoVms);

            var result = await _handler.Handle(new GetUserInfoListQuery(), CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(userInfoVms.Count, result.Count);
            Assert.Equal(userInfoVms[0].UserId, result[0].UserId);
            Assert.Equal(userInfoVms[1].UserId, result[1].UserId);
        }
    }
}
