using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAirGapLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAirGapLogModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberAirGapLogService : ICyberAirGapLogService
{
    private readonly IBaseClient _client;

    public CyberAirGapLogService(IBaseClient client)
    {
        _client = client;

    }

    public async Task<List<CyberAirGapLogListVm>> GetCyberAirGapLogList()
    {
        var request = new RestRequest("api/v6/cyberairgaplogs");

        return await  _client. GetFromCache<List<CyberAirGapLogListVm>>(request, "GetCyberAirGapLogList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberAirGapLogCommand createCyberAirGapLogCommand)
    {
        var request = new RestRequest("api/v6/cyberairgaplogs", Method.Post);

        request.AddJsonBody(createCyberAirGapLogCommand);

        return await  _client. Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberAirGapLogCommand updateCyberAirGapLogCommand)
    {
        var request = new RestRequest("api/v6/cyberairgaplogs", Method.Put);

        request.AddJsonBody(updateCyberAirGapLogCommand);

        return await  _client. Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cyberairgaplogs/{id}", Method.Delete);

        return await  _client. Delete<BaseResponse>(request);
    }

    public async Task<CyberAirGapLogDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cyberairgaplogs/{id}");

        return await  _client. Get<CyberAirGapLogDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsCyberAirGapLogNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/cyberairgaplogs/name-exist?cyberairgaplogName={name}&id={id}");

     return await  _client. Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<CyberAirGapLogListVm>> GetPaginatedCyberAirGapLogs(GetCyberAirGapLogPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/cyberairgaplogs/paginated-list");

      return await  _client. Get<PaginatedResult<CyberAirGapLogListVm>>(request);
  }
   #endregion
}
