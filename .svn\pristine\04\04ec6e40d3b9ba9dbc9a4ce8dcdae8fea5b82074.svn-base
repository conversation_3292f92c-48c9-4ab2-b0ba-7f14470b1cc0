﻿using ContinuityPatrol.Application.Features.ServerSubType.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerSubType.Events;


public class CreateServerSubTypeEventTests : IClassFixture<ServerSubTypeFixture>, IClassFixture<UserActivityFixture>
{
    private readonly ServerSubTypeFixture _serverSubTypeFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly ServerSubTypeCreatedEventHandler _handler;

    public CreateServerSubTypeEventTests(ServerSubTypeFixture serverSubTypeFixture, UserActivityFixture userActivityFixture)
    {
        _serverSubTypeFixture = serverSubTypeFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockServerSubTypeEventLogger = new Mock<ILogger<ServerSubTypeCreatedEventHandler>>();

        _mockUserActivityRepository = ServerSubTypeRepositoryMocks.CreateServerSubTypeEventRepository(_userActivityFixture.UserActivities);

        _handler = new ServerSubTypeCreatedEventHandler(mockLoggedInUserService.Object, mockServerSubTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateServerSubTypeEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_serverSubTypeFixture.ServerSubTypeCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_serverSubTypeFixture.ServerSubTypeCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
