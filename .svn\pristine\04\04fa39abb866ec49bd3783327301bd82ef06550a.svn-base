using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Update;

public class CyberAirGapLogUpdatedEventHandler : INotificationHandler<CyberAirGapLogUpdatedEvent>
{
    private readonly ILogger<CyberAirGapLogUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAirGapLogUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CyberAirGapLogUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(CyberAirGapLogUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} CyberAirGapLog",
            Entity = "CyberAirGapLog",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"CyberAirGapLog '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CyberAirGapLog '{updatedEvent.Name}' updated successfully.");
    }
}