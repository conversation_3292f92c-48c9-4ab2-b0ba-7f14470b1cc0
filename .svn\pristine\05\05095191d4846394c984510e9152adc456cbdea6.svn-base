﻿using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Commands;

public class CreateTemplateTests : IClassFixture<TemplateFixture>, IClassFixture<TemplateHistoryFixture>
{
    private readonly TemplateFixture _templateFixture;
    private readonly TemplateHistoryFixture _templateHistoryFixture;
    private readonly Mock<ITemplateRepository> _mockTemplateRepository;
    private readonly Mock<ILoggedInUserService> _mockLogInUserService;
    private readonly Mock<IVersionManager> _mockVersionManager;
    private readonly Mock<ITemplateHistoryRepository> _mockTemplateHistoryRepository;
    private readonly CreateTemplateCommandHandler _handler;

    public CreateTemplateTests(TemplateFixture templateFixture, TemplateHistoryFixture templateHistoryFixture)
    {
        _templateFixture = templateFixture;

        _templateHistoryFixture = templateHistoryFixture;

        _mockLogInUserService = new Mock<ILoggedInUserService>();
        _mockLogInUserService.Setup(x => x.CompanyId).Returns("b7c5d56b-afdb-47f8-a15d-bd1a033d3cce");
        _mockLogInUserService.Setup(x => x.LoginName).Returns("test_user");
        _mockLogInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());

        _mockVersionManager = new Mock<IVersionManager>();
        _mockVersionManager.Setup(vm => vm.GetVersion(It.IsAny<string>())).ReturnsAsync("1.0.0");

        _mockTemplateRepository = TemplateRepositoryMocks.CreateTemplateRepository(_templateFixture.Templates);

        _mockTemplateHistoryRepository = TemplateHistoryRepositoryMocks.CreateTemplateHistoryRepository(_templateHistoryFixture.TemplateHistories);

        _handler = new CreateTemplateCommandHandler(_templateFixture.Mapper, _mockTemplateRepository.Object, _mockLogInUserService.Object, _mockVersionManager.Object, 
            _mockTemplateHistoryRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_Template()
    {
        var initialCount = (await _mockTemplateRepository.Object.ListAllAsync()).Count;

        await _handler.Handle(_templateFixture.CreateTemplateCommand, CancellationToken.None);

        var updatedTemplates = await _mockTemplateRepository.Object.ListAllAsync();

        updatedTemplates.Count.ShouldBe(initialCount + 1);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulTemplateResponse_When_AddValidTemplate()
    {
        var result = await _handler.Handle(_templateFixture.CreateTemplateCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateTemplateResponse));

        result.TemplateId.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_templateFixture.CreateTemplateCommand, CancellationToken.None);

        _mockTemplateRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Template>()), Times.Once);
    }
}
