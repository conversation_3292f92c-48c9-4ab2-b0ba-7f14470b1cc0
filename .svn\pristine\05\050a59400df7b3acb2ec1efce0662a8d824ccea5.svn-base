﻿using ContinuityPatrol.Application.Features.UserLogin.Commands.ClearSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.CreateSession;
using ContinuityPatrol.Application.Features.UserLogin.Commands.Update;
using ContinuityPatrol.Application.Features.UserLogin.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class UserLoginService : BaseService, IUserLoginService
{
    public UserLoginService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<UserLoginDetailVm> GetUserInfoByUserId(string userId)
    {
        Guard.Against.InvalidGuidOrEmpty(userId, "UserLogin UserId");

        Logger.LogDebug($"Get UserLogin Details by UserId '{userId}'");

        return await Mediator.Send(new GetUserLoginDetailQuery { UserId = userId });
    }

    public async Task<BaseResponse> UpdateAsync(UpdateUserLoginCommand updateUserLogin)
    {
        Logger.LogDebug($"Update UserLogin '{updateUserLogin.UserId}'");

        return await Mediator.Send(updateUserLogin);
    }

    public async Task<CreateSessionUserLoginResponse> CreateDatabaseSession(string userId,string sessionId)
    {
        var command = new CreateSessionUserLoginCommand
        {
            UserId = userId,
            SessionId = sessionId
        };

        Logger.LogDebug($"Create Session UserLogin id'{command.UserId}'");

        return await Mediator.Send(command);
    }

    public async Task<ClearSessionUserLoginResponse> ClearDatabaseSession(string userId)
    {
        var command = new ClearSessionUserLoginCommand { UserId = userId };

        Logger.LogDebug($"Clear Session UserLogin id'{command.UserId}'");

        return await Mediator.Send(command);
    }
}