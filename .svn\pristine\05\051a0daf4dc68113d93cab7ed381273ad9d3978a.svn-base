﻿using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using FluentValidation.TestHelper;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Validators;

public class CreateAlertReceiverValidatorTests
{
    private readonly Mock<IAlertReceiverRepository> _mockAlertReceiverRepository;

    public CreateAlertReceiverValidatorTests()
    {
        var alertReceiver = new Fixture().Create<List<Domain.Entities.AlertReceiver>>();

        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.CreateAlertReceiverRepository(alertReceiver);
    }

    //Name

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_InAlertReceiver_WithEmpty(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name = "";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Name).WithErrorMessage("Name is required.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_InAlertReceiver_IsNull(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name = null;

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Name).WithErrorMessage("Name is required.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_InAlertReceiver_MinimumRange(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name = "QR";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Name should contain between 3 to 100 characters.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_InAlertReceiver_MaximumRange(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name = new string('A', 101);

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Name should contain between 3 to 100 characters.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_InAlertReceiver_ValidFormat(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name = "#InvalidName";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Name)
              .WithErrorMessage("Please enter valid Name");
    }

    // EmailAddress Tests

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_EmailAddress_InvalidFormat(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.EmailAddress = "invalid-email";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.EmailAddress)
              .WithErrorMessage("Enter the valid Email Address.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_EmailAddress_ValidFormat(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.EmailAddress = "<EMAIL>";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldNotHaveValidationErrorFor(c => c.EmailAddress);
    }

    // Properties Tests

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Properties_InvalidJson(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Properties = "InvalidJson";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.Properties)
              .WithErrorMessage("Properties must be a valid json string.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Properties_ValidJson(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Properties = "{\"key\": \"value\"}";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldNotHaveValidationErrorFor(c => c.Properties);
    }

    // MobileNumber Tests

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_MobileNumber_InvalidFormat(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.MobileNumber = "12345";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldHaveValidationErrorFor(c => c.MobileNumber)
              .WithErrorMessage("Enter the valid Mobile Number.");
    }

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_MobileNumber_ValidFormat(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.MobileNumber = "+911234567890";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldNotHaveValidationErrorFor(c => c.MobileNumber);
    }

    // Unique Name Test

    [Theory]
    [AutoAlertReceiverData]
    public async Task Verify_Create_Name_IsUnique(CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        var validator = new CreateAlertReceiverCommandValidator(_mockAlertReceiverRepository.Object);

        createAlertReceiverCommand.Name ="PTS";

        var result = await validator.TestValidateAsync(createAlertReceiverCommand);

        result.ShouldNotHaveValidationErrorFor(c => c.Name);
    }
}