﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Publish;

public class FormPublishEventHandler : INotificationHandler<FormPublishEvent>
{
    private readonly ILogger<FormPublishEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormPublishEventHandler(ILogger<FormPublishEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormPublishEvent formPublishEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{(formPublishEvent.IsPublish ? ActivityType.Publish : ActivityType.UnPublish)} {Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = formPublishEvent.IsPublish
                ? ActivityType.Publish.ToString()
                : ActivityType.UnPublish.ToString(),
            ActivityDetails =
                $"Form Builder '{formPublishEvent.FormName}' {(formPublishEvent.IsPublish ? "publish" : "UnPublish")} successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation(
            $"Form Builder '{formPublishEvent.FormName}' {(formPublishEvent.IsPublish ? "Publish" : "UnPublish")} successfully.");
    }
}