﻿using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.MenuBuilder.Validators
{
    public class CreateMenuBuilderCommandValidatorTests
    {
        private readonly CreateMenuBuilderCommandValidator _validator;
        private readonly Mock<IMenuBuilderRepository> _menuBuilderRepoMock;

        public CreateMenuBuilderCommandValidatorTests()
        {
            _menuBuilderRepoMock = new Mock<IMenuBuilderRepository>();
            _validator = new CreateMenuBuilderCommandValidator(_menuBuilderRepoMock.Object);
        }

        [Fact]
        public async Task Should_Not_Have_Error_When_Name_Is_Valid_And_Unique()
        {
            _menuBuilderRepoMock
                .Setup(r => r.IsNameExist("UniqueMenu", null))
                .ReturnsAsync(false); 

            var command = new CreateMenuBuilderCommand { Name = "UniqueMenu" };
            var result = await _validator.TestValidateAsync(command);
            result.ShouldNotHaveValidationErrorFor(c => c.Name);
        }

        [Fact]
        public async Task Should_Not_Have_Errors_When_Model_Is_Valid_And_All_Properties_Touched()
        {
            _menuBuilderRepoMock
                .Setup(r => r.IsNameExist("MainMenu", null))
                .ReturnsAsync(false); 

            var command = new CreateMenuBuilderCommand
            {
                Name = "MainMenu",
                Properties = "Key:Value;Style:Bold",
                State = "Active"
            };

            var result = await _validator.TestValidateAsync(command);

            result.ShouldNotHaveAnyValidationErrors();

            Assert.Equal("MainMenu", command.Name);
            Assert.Equal("Key:Value;Style:Bold", command.Properties);
            Assert.Equal("Active", command.State);
        }
    }
}
