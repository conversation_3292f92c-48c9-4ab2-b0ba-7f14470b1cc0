﻿namespace ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;

public class GetGroupPolicyNameQueryHandler : IRequestHandler<GetGroupPolicyNameQuery, List<GroupPolicyNameVm>>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;
    private readonly IMapper _mapper;

    public GetGroupPolicyNameQueryHandler(IGroupPolicyRepository groupPolicyRepository, IMapper mapper)
    {
        _groupPolicyRepository = groupPolicyRepository;
        _mapper = mapper;
    }

    public async Task<List<GroupPolicyNameVm>> Handle(GetGroupPolicyNameQuery request,
        CancellationToken cancellationToken)
    {
        var groupPolicy = await _groupPolicyRepository.GetGroupPolicyNames();

        var groupPolicyDto = _mapper.Map<List<GroupPolicyNameVm>>(groupPolicy);

        return groupPolicyDto;
    }
}