﻿using System.Reflection;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Validators;

public class UpdateRoboCopyCommandValidatorTests
{
    private readonly UpdateRoboCopyCommandValidator _validator;
    private readonly Mock<IRoboCopyRepository> _mockRepo;

    public UpdateRoboCopyCommandValidatorTests()
    {
        _mockRepo = new Mock<IRoboCopyRepository>();
        _mockRepo.Setup(repo => repo.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
                 .ReturnsAsync(false); // Simulate unique name

        _validator = new UpdateRoboCopyCommandValidator(_mockRepo.Object);
    }

    [Fact]
    public async Task Should_Have_Error_When_Name_Is_Empty()
    {
        var command = new UpdateRoboCopyCommand { Name = "", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Name);
    }

    [Fact]
    public async Task Should_Have_Error_When_Name_Is_Invalid_Format()
    {
        var command = new UpdateRoboCopyCommand { Name = "Invalid@Name", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Name);
    }

    [Fact]
    public async Task Should_Have_Error_When_ReplicationType_Is_Empty()
    {
        var command = new UpdateRoboCopyCommand { ReplicationType = "", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.ReplicationType);
    }

    [Fact]
    public async Task Should_Have_Error_When_Properties_Is_Invalid_Json()
    {
        var command = new UpdateRoboCopyCommand { Properties = "InvalidJson", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Properties);
    }

    [Fact]
    public async Task Should_Not_Have_Error_When_Properties_Is_Valid_Json_Object()
    {
        var command = new UpdateRoboCopyCommand { Properties = "{\"key\":\"value\"}", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldNotHaveValidationErrorFor(c => c.Properties);
    }

    [Fact]
    public async Task Should_Not_Have_Error_When_Properties_Is_Valid_Json_Array()
    {
        var command = new UpdateRoboCopyCommand { Properties = "[{\"key\":\"value\"}]", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldNotHaveValidationErrorFor(c => c.Properties);
    }

    [Fact]
    public async Task Should_Not_Have_Error_When_Properties_Is_Empty_String()
    {
        var command = new UpdateRoboCopyCommand { Properties = "", Id = Guid.NewGuid().ToString().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldNotHaveValidationErrorFor(c => c.Properties); // Skipped by .When
    }

    [Fact]
    public async Task Should_Not_Have_Error_When_Properties_Is_Whitespace()
    {
        var command = new UpdateRoboCopyCommand { Properties = "   ", Id = Guid.NewGuid().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldNotHaveValidationErrorFor(c => c.Properties); // Skipped by .When
    }

    [Fact]
    public async Task Should_Have_Error_When_Properties_Is_Malformed_Json()
    {
        var command = new UpdateRoboCopyCommand { Properties = "{\"key\":value}", Id = Guid.NewGuid().ToString().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Properties);
    }

    [Fact]
    public async Task Should_Have_Error_When_Properties_StartsWith_Brackets_But_Invalid()
    {
        var command = new UpdateRoboCopyCommand { Properties = "{InvalidJson}", Id = Guid.NewGuid().ToString().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Properties);
    }

    [Fact]
    public async Task Should_Have_Error_When_Properties_Has_No_Curly_Or_Square_Brackets()
    {
        var command = new UpdateRoboCopyCommand { Properties = "plain text", Id = Guid.NewGuid().ToString().ToString() };
        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Properties);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void IsValidJson_ShouldReturnFalse_WhenInputIsNullOrWhiteSpace(string input)
    {
        // Use reflection to access the private static method
        var method = typeof(UpdateRoboCopyCommandValidator)
            .GetMethod("IsValidJson", BindingFlags.NonPublic | BindingFlags.Static);

        Assert.NotNull(method);

        var result = (bool)method.Invoke(null, new object[] { input });

        Assert.False(result);
    }

    [Fact]
    public async Task Should_Pass_When_All_Fields_Are_Valid_And_Name_Is_Unique()
    {
        var id = Guid.NewGuid().ToString();
        var command = new UpdateRoboCopyCommand
        {
            Id = id,
            Name = "Valid_Name1",
            ReplicationType = "Type1",
            Properties = "{\"a\":\"b\"}"
        };

        _mockRepo.Setup(repo => repo.IsNameExist(command.Name, id)).ReturnsAsync(false);

        var result = await _validator.TestValidateAsync(command);
        result.ShouldNotHaveAnyValidationErrors();
    }

}