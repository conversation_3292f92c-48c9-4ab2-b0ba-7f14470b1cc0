using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.CreateValidator;
using ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

namespace ContinuityPatrol.Application.Mappings;

public class BulkImportOperationProfile : Profile
{
    public BulkImportOperationProfile()
    {
        CreateMap<BulkImportOperation, BulkImportOperationListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<BulkImportOperation, BulkImportOperationDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<BulkImportOperation, CreateBulkImportOperationCommand>().ReverseMap();
        CreateMap<BulkImportOperation, BulkImportOperationViewModel>().ReverseMap();

        CreateMap<CreateBulkImportOperationCommand, BulkImportOperationViewModel>().ReverseMap();
        CreateMap<UpdateBulkImportOperationCommand, BulkImportOperationViewModel>().ReverseMap();

        CreateMap<UpdateBulkImportOperationCommand, BulkImportOperation>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<CreateBulkImportValidatorCommandList, BulkImportValidatorCommand>();

        CreateMap<BulkImportOperation, BulkImportOperationRunningListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}