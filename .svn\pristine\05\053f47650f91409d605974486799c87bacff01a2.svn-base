﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Events.Update;

public class ReplicationJobUpdatedEventHandler : INotificationHandler<ReplicationJobUpdatedEvent>
{
    private readonly ILogger<ReplicationJobUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReplicationJobUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<ReplicationJobUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ReplicationJobUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.Replication.ToString(),
            Action = $"{ActivityType.Update} {Modules.Replication}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Replication Job'{updatedEvent.ReplicationJobName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Replication Job '{updatedEvent.ReplicationJobName}' updated successfully.");
    }
}