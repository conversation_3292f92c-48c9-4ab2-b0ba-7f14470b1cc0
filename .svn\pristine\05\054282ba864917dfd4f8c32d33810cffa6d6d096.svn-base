﻿using ContinuityPatrol.Application.Features.WorkflowOperation.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperation.Events;

public class WorkflowOperationCreatedEventHandlerTests : IClassFixture<WorkflowOperationFixture>
{
    private readonly WorkflowOperationFixture _workflowOperationFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly WorkflowOperationCreatedEventHandler _handler;

    public WorkflowOperationCreatedEventHandlerTests(WorkflowOperationFixture workflowOperationFixture)
    {
        _workflowOperationFixture = workflowOperationFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockWorkflowOperationEventLogger = new Mock<ILogger<WorkflowOperationCreatedEventHandler>>();

        _mockUserActivityRepository = WorkflowOperationRepositoryMocks.CreateWorkflowOperationEventRepository(_workflowOperationFixture.UserActivities);

        _handler = new WorkflowOperationCreatedEventHandler(mockLoggedInUserService.Object, mockWorkflowOperationEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateWorkflowOperationEventCreated()
    {
        _workflowOperationFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowOperationFixture.WorkflowOperationCreatedEvent, CancellationToken.None);

        result.Equals(_workflowOperationFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_workflowOperationFixture.WorkflowOperationCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_CreateWorkflowOperationEventCreated()
    {
        _workflowOperationFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_workflowOperationFixture.WorkflowOperationCreatedEvent, CancellationToken.None);

        result.Equals(_workflowOperationFixture.UserActivities[0].Id);

        result.Equals(_workflowOperationFixture.WorkflowOperationCreatedEvent.Description);

        await Task.CompletedTask;
    }
}