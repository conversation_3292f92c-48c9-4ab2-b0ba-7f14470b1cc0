using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Create;
using ContinuityPatrol.Application.Features.CyberSnaps.Commands.Update;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.CyberSnaps.Queries.GetPowerMax;
using ContinuityPatrol.Domain.ViewModels.CyberSnapsModel;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberSnapsService : BaseClient, ICyberSnapsService
{
    public CyberSnapsService(IConfiguration config, IAppCache cache, ILogger<CyberSnapsService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<CyberSnapsListVm>> GetCyberSnapsList()
    {
        var request = new RestRequest("api/v6/cybersnapss");

        return await GetFromCache<List<CyberSnapsListVm>>(request, "GetCyberSnapsList");
    }
    public async Task<List<CyberSnapsListVm>> GetCyberSnapsByStorageGroupNameAndLinkedStatus(string storageGroupName, string linkedStatus, string? snapshotName)
    {
        var api=(storageGroupName.IsNotNullOrWhiteSpace() && linkedStatus.IsNotNullOrWhiteSpace()?
            $"api/v6/cybersnaps/by/cybersnapsdtl?storageGroupName={storageGroupName}&linkedStatus={linkedStatus}&snapshotName{snapshotName}"
            :storageGroupName.IsNullOrWhiteSpace()?
            $"api/v6/cybersnaps/by/cybersnapsdtl?linkedStatus={linkedStatus}&snapshotName{snapshotName}"
            :$"api/v6/cybersnaps/by/cybersnapsdtl?storageGroupName={storageGroupName}&snapshotName{snapshotName}");

        var request = new RestRequest(api);

        return await Get<List<CyberSnapsListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberSnapsCommand createCyberSnapsCommand)
    {
        var request = new RestRequest("api/v6/cybersnapss", Method.Post);

        request.AddJsonBody(createCyberSnapsCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberSnapsCommand updateCyberSnapsCommand)
    {
        var request = new RestRequest("api/v6/cybersnapss", Method.Put);

        request.AddJsonBody(updateCyberSnapsCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cybersnapss/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<CyberSnapsDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cybersnapss/{id}");

        return await Get<CyberSnapsDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsCyberSnapsNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/cybersnapss/name-exist?cybersnapsName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<CyberSnapsListVm>> GetPaginatedCyberSnapss(GetCyberSnapsPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/cybersnapss/paginated-list");

      return await Get<PaginatedResult<CyberSnapsListVm>>(request);
  }

    #endregion

    public async Task<List<PowerMaxDetailVm>> GetPowerMaxMonitorStatus(string? name, bool isSnap)
    {
        var request = new RestRequest($"api/v6/cybersnapss/powerMax?name={name}&isSnap={isSnap}");
        return await Get<List<PowerMaxDetailVm>>(request);
    }
}
