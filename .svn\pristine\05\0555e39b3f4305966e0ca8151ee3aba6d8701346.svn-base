using ContinuityPatrol.Application.Features.Archive.Commands.Create;
using ContinuityPatrol.Application.Features.Archive.Events.Create;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.Archive.Commands;
public class CreateArchiveCommandHandlerTests
{
    private readonly Mock<IArchiveRepository> _archiveRepositoryMock;
    private readonly Mock<IJobScheduler> _jobSchedulerMock;
    private readonly Mock<ILoggedInUserService> _loggedInUserServiceMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<ILoadBalancerRepository> _loadBalancerRepositoryMock;
    private readonly Mock<IPublisher> _publisherMock;
    private readonly CreateArchiveCommandHandler _handler;

    public CreateArchiveCommandHandlerTests()
    {
        _archiveRepositoryMock = new Mock<IArchiveRepository>();
        _jobSchedulerMock = new Mock<IJobScheduler>();
        _loggedInUserServiceMock = new Mock<ILoggedInUserService>();
        _mapperMock = new Mock<IMapper>();
        _loadBalancerRepositoryMock = new Mock<ILoadBalancerRepository>();
        _publisherMock = new Mock<IPublisher>();

        _handler = new CreateArchiveCommandHandler(
            _mapperMock.Object,
            _archiveRepositoryMock.Object,
            _publisherMock.Object,
            _loggedInUserServiceMock.Object,
            _loadBalancerRepositoryMock.Object,
            _jobSchedulerMock.Object
        );
    }

    [Fact]
    public async Task Handle_ValidRequest_ShouldCreateArchiveAndScheduleJob()
    {
        // Arrange
        var command = new CreateArchiveCommand { ArchiveProfileName = "DailyBackup" };
        var mappedEntity = new Domain.Entities.Archive { ArchiveProfileName = "DailyBackup", ReferenceId = "ref-001" };
        var savedEntity = mappedEntity;

        _loggedInUserServiceMock.Setup(x => x.CompanyId).Returns("company-001");

        _mapperMock.Setup(x => x.Map<Domain.Entities.Archive>(command)).Returns(mappedEntity);

        _archiveRepositoryMock.Setup(x => x.AddAsync(mappedEntity)).ReturnsAsync(savedEntity);

        _loadBalancerRepositoryMock.Setup(x => x.GetNodeConfigurationByTypeAndTypeCategory("ALL", ServiceType.LoadBalancer.ToString()))
            .ReturnsAsync(new Domain.Entities.LoadBalancer
            {
                ConnectionType = "http",
                IPAddress = "127.0.0.1",
                Port = 8080
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("ref-001", result.Id);
        Assert.Contains("DailyBackup", result.Message);

        _archiveRepositoryMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Archive>()), Times.Once);
        _publisherMock.Verify(p => p.Publish(It.IsAny<ArchiveCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        _jobSchedulerMock.Verify(j => j.ScheduleJob("ref-001", It.Is<Dictionary<string, string>>(d => d["url"].Contains("/LoadBalancer/ArchiveLog/ref-001"))), Times.Once);
    }
    [Fact]
    public void CreateArchiveCommand_Should_Set_All_Properties_Correctly()
    {
        // Arrange
        var command = new CreateArchiveCommand
        {
            CompanyId = "company-123",
            TableNameProperties = "{\"Table1\": \"Value1\"}",
            ArchiveProfileName = "Profile A",
            Count = 10,
            CronExpression = "0 0 * * *",
            ScheduleTime = "2025-07-15T12:00:00Z",
            ScheduleType = 1,
            BackUpType = "Full",
            Type = "Database",
            ClearBackup = "Yes",
            NodeId = "node-999",
            NodeName = "Node-A"
        };

        // Assert
        Assert.Equal("company-123", command.CompanyId);
        Assert.Equal("{\"Table1\": \"Value1\"}", command.TableNameProperties);
        Assert.Equal("Profile A", command.ArchiveProfileName);
        Assert.Equal(10, command.Count);
        Assert.Equal("0 0 * * *", command.CronExpression);
        Assert.Equal("2025-07-15T12:00:00Z", command.ScheduleTime);
        Assert.Equal(1, command.ScheduleType);
        Assert.Equal("Full", command.BackUpType);
        Assert.Equal("Database", command.Type);
        Assert.Equal("Yes", command.ClearBackup);
        Assert.Equal("node-999", command.NodeId);
        Assert.Equal("Node-A", command.NodeName);
    }
}
