﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IWorkflowProfileRepository : IRepository<WorkflowProfile>
{
    Task<List<WorkflowProfile>> GetWorkflowProfileNames();
    Task<bool> IsWorkflowProfileNameExist(string name, string id);
    Task<bool> IsWorkflowProfileNameUnique(string name);
    Task<List<WorkflowProfile>> GetByProfileIdAsync(List<string> id);
}