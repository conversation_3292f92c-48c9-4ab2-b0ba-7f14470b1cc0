﻿using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.OracleMonitorStatus.Queries;
public class GetOracleMonitorStatusPaginatedListQueryHandlerTests
{
    private readonly Mock<IOracleMonitorStatusRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetOracleMonitorStatusPaginatedListQueryHandler _handler;

    public GetOracleMonitorStatusPaginatedListQueryHandlerTests()
    {
        _repositoryMock = new Mock<IOracleMonitorStatusRepository>();
        _mapperMock = new Mock<IMapper>();
        _handler = new GetOracleMonitorStatusPaginatedListQueryHandler(_repositoryMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task Handle_Should_Return_PaginatedList_When_Data_Exists()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Page",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var domainResult = PaginatedResult<Domain.Entities.OracleMonitorStatus>.Success(
            new List<Domain.Entities.OracleMonitorStatus>
            {
                new Domain.Entities.OracleMonitorStatus { ReferenceId = "1", Type = "Test Page", IsActive = true }
            }, 1, 1, 10
        );

        var mappedResult = PaginatedResult<OracleMonitorStatusListVm>.Success(
            new List<OracleMonitorStatusListVm>
            {
                new OracleMonitorStatusListVm { Id = "1", Type = "Test Page" }
            }, 1, 1, 10
        );

        _repositoryMock.Setup(r =>
                r.PaginatedListAllAsync(query.PageNumber, query.PageSize, It.IsAny<OracleMonitorStatusFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m =>
                m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult))
            .Returns(mappedResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal("1", result.Data[0].Id);
        Assert.Equal("Test Page", result.Data[0].Type);
        _repositoryMock.Verify(r => r.PaginatedListAllAsync(1, 10, It.IsAny<OracleMonitorStatusFilterSpecification>(), "Name", "asc"), Times.Once);
        _mapperMock.Verify(m => m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Return_Empty_List_When_No_Data()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "NoResults",
            SortColumn = "Name",
            SortOrder = "desc"
        };

        var domainResult = PaginatedResult<Domain.Entities.OracleMonitorStatus>.Success(new List<Domain.Entities.OracleMonitorStatus>(), 0, 1, 10);
        var mappedResult = PaginatedResult<OracleMonitorStatusListVm>.Success(new List<OracleMonitorStatusListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r =>
                r.PaginatedListAllAsync(query.PageNumber, query.PageSize, It.IsAny<OracleMonitorStatusFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m =>
                m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult))
            .Returns(mappedResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
    }

    [Fact]
    public async Task Handle_Should_Work_With_Different_Sort_And_Search_Values()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 2,
            PageSize = 5,
            SearchString = "custom",
            SortColumn = "Type",
            SortOrder = "desc"
        };

        var domainResult = PaginatedResult<Domain.Entities.OracleMonitorStatus>.Success(new List<Domain.Entities.OracleMonitorStatus>(), 0, 2, 5);
        var mappedResult = PaginatedResult<OracleMonitorStatusListVm>.Success(new List<OracleMonitorStatusListVm>(), 0, 2, 5);

        _repositoryMock.Setup(r =>
                r.PaginatedListAllAsync(query.PageNumber, query.PageSize, It.IsAny<OracleMonitorStatusFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m =>
                m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult))
            .Returns(mappedResult);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Equal(2, result.CurrentPage);
        Assert.Equal(5, result.PageSize);
    }
    [Fact]
    public async Task Handle_Should_Throw_Exception_When_Repository_Fails()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "error",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                query.PageNumber,
                query.PageSize,
                It.IsAny<OracleMonitorStatusFilterSpecification>(),
                query.SortColumn,
                query.SortOrder))
            .ThrowsAsync(new System.Exception("Repository failed"));

        // Act & Assert
        var ex = await Assert.ThrowsAsync<System.Exception>(() =>
            _handler.Handle(query, CancellationToken.None));

        Assert.Equal("Repository failed", ex.Message);
    }
    [Fact]
    public async Task Handle_Should_Throw_Exception_When_Mapper_Fails()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var domainResult = PaginatedResult<Domain.Entities.OracleMonitorStatus>.Success(
            new List<Domain.Entities.OracleMonitorStatus> { new Domain.Entities.OracleMonitorStatus { ReferenceId = "1", Type = "Test" } },
            1, 1, 10);

        _repositoryMock.Setup(r =>
                r.PaginatedListAllAsync(query.PageNumber, query.PageSize,
                    It.IsAny<OracleMonitorStatusFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult))
            .Throws(new AutoMapperMappingException("Mapping failed"));

        // Act & Assert
        var ex = await Assert.ThrowsAsync<AutoMapperMappingException>(() =>
            _handler.Handle(query, CancellationToken.None));

        Assert.Equal("Mapping failed", ex.Message);
    }
    [Fact]
    public async Task Handle_Should_Respect_CancellationToken()
    {
        // Arrange
        var query = new GetOracleMonitorStatusPaginatedListQuery()
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "token",
            SortColumn = "Type",
            SortOrder = "desc"
        };

        var token = new CancellationTokenSource().Token;

        var domainResult = PaginatedResult<Domain.Entities.OracleMonitorStatus>.Success(new List<Domain.Entities.OracleMonitorStatus>(), 0, 1, 10);
        var mappedResult = PaginatedResult<OracleMonitorStatusListVm>.Success(new List<OracleMonitorStatusListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r =>
                r.PaginatedListAllAsync(query.PageNumber, query.PageSize,
                    It.IsAny<OracleMonitorStatusFilterSpecification>(), query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<OracleMonitorStatusListVm>>(domainResult))
            .Returns(mappedResult);

        // Act
        var result = await _handler.Handle(query, token);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
        Assert.Empty(result.Data);
    }
}