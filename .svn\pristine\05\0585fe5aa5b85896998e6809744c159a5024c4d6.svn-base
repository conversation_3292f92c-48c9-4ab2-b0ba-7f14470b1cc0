﻿using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;

namespace ContinuityPatrol.Application.Mappings;

public class UserInfraObjectProfile : Profile
{
    public UserInfraObjectProfile()
    {
        CreateMap<BusinessService, GetUserInfraObjectByBusinessServiceVm>().ReverseMap();
        CreateMap<BusinessFunction, GetUserInfraObjectByBusinessServiceVm>().ReverseMap();
        CreateMap<UserInfraObject, GetUserInfraObjectByUserIdVm>().ReverseMap();
        CreateMap<UserInfraObject, GetByUserIdAndPropertiesVm>().ReverseMap();

        CreateMap<BusinessService, AssignedBusinessServices>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name));

        CreateMap<BusinessFunction, AssignedBusinessFunctions>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.BusinessServiceId, opt => opt.MapFrom(src => src.BusinessServiceId));

        CreateMap<InfraObject, AssignedInfraObjects>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.BusinessFunctionId, opt => opt.MapFrom(src => src.BusinessFunctionId));

        //CreateMap<AssignedBusinessServices, GetUserInfraObjectByBusinessServiceVm>()
        //.ForMember(dest => dest.AssignedBusinessServices, opt => opt.MapFrom(src => new List<AssignedBusinessServices> { src }));
    }
}