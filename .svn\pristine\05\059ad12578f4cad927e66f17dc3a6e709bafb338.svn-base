﻿using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Events.Paginated;
using ContinuityPatrol.Application.Features.AdPasswordExpire.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Create;
using ContinuityPatrol.Application.Features.AdPasswordJob.Commands.Update;
using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;


namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class ADPasswordExpireController : Controller
{
    private readonly ILogger<ADPasswordExpireController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;

    public ADPasswordExpireController(IMapper mapper, ILogger<ADPasswordExpireController> logger, IDataProvider dataProvider, IPublisher publisher)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = dataProvider;
        _publisher = publisher;
    }
    [EventCode(EventCodes.AdPasswordExpire.List)]
    public async  Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in AdPasswordExpire");

        await _publisher.Publish(new AdPasswordExpirePaginatedEvent());

        return View();
    }

    [HttpGet]
    [EventCode(EventCodes.AdPasswordExpire.Pagination)]
    public async Task<JsonResult> GetPagination(GetAdPasswordExpirePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in AdPasswordExpire");
        try
        {
            var adPasswordExpireList = await _dataProvider.AdPasswordExpire.GetPaginatedAdPasswordExpires(query);
            _logger.LogDebug("Successfully retrieved AdPasswordExpire paginated list on AdPasswordExpire page");
            return Json(new { success = true, data = adPasswordExpireList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on AdPasswordExpire page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.AdPasswordExpire.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(AdPasswordExpireViewModel adPasswordExpireViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in AdPasswordExpire");

        var adPasswordExpireId = Request.Form["id"].ToString();

        try
        {
            if (adPasswordExpireId.IsNullOrWhiteSpace())
            {
                var adPasswordExpireCreateCommand = _mapper.Map<CreateAdPasswordExpireCommand>(adPasswordExpireViewModel);
                var response = await _dataProvider.AdPasswordExpire.CreateAsync(adPasswordExpireCreateCommand);
                _logger.LogDebug($"Creating ADPasswordExpire '{adPasswordExpireCreateCommand.UserName}'.");   
                return Json(new { success = true, data = response });
            }
            else
            {
                var adPasswordExpireUpdateCommand = _mapper.Map<UpdateAdPasswordExpireCommand>(adPasswordExpireViewModel);
                var response = await _dataProvider.AdPasswordExpire.UpdateAsync(adPasswordExpireUpdateCommand);
                _logger.LogDebug($"Updating ADPasswordExpire '{adPasswordExpireUpdateCommand.UserName}'.");                
                return Json(new { success = true, data = response });
            }
           
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on AdPasswordExpire page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }        
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    [EventCode(EventCodes.AdPasswordExpire.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in AdPasswordExpire");
        try
        {
            var response = await _dataProvider.AdPasswordExpire.DeleteAsync(id);

            TempData.NotifySuccess(response.Message);

            _logger.LogDebug("Successfully deleted record in AdPasswordExpire");

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());
            _logger.Exception("An error occurred while deleting record on AdPasswordExpire.", ex);
            return RedirectToAction("List");
        }
    }

    [HttpGet]
    [EventCode(EventCodes.AdPasswordExpire.GetServerByUserName)]
    public async Task<IActionResult> GetServerByUserName(string userName, string osType)
    {
        _logger.LogDebug("Entering GetServerByUserName method in BulkCredential");
        try
        {
            if (userName == null)
            {
                return Json(new List<object>());
            }
            else
            {
                var server = await _dataProvider.Server.GetServerByUserName(userName, osType, false);
                var windowsServers = server.Where(x => x.OSType.Equals("Windows", StringComparison.OrdinalIgnoreCase)).ToList();
                _logger.LogDebug($"Successfully retrieved server list by username '{userName}' and osType '{osType}' in bulk credential");

                return Json(windowsServers);

            }
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on bulk credential while retrieving server list by username and osType.", ex);

            return Json(new { success = false, message = ex.GetJsonException() });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.AdPasswordExpire.AdJobCreateOrAdJobUpdate)]
    public async Task<IActionResult> AdJobCreateOrAdJobUpdate(CreateAdPasswordJobCommand createAdPasswordJobCommand ,UpdateAdPasswordJobCommand updateAdPasswordJobCommand)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in AdPasswordExpire");
        var adPasswordExpireId = Request.Form["id"].ToString();
        try
        {
            if (adPasswordExpireId.IsNullOrWhiteSpace())
            {
                var adPasswordCreateCommand = _mapper.Map<CreateAdPasswordJobCommand>(createAdPasswordJobCommand);
                var response = await _dataProvider.AdPasswordJob.CreateAsync(adPasswordCreateCommand);
                _logger.LogDebug($"Creating ADPasswordExpire '{adPasswordCreateCommand.DomainServer}'.");               
                return Json(new { success = true, data = response });
            }
            else
            {
                var adPasswordUpdateCommand = _mapper.Map<UpdateAdPasswordJobCommand>(updateAdPasswordJobCommand);
                var response = await _dataProvider.AdPasswordJob.UpdateAsync(adPasswordUpdateCommand);
                _logger.LogDebug($"Updating ADPasswordExpire '{adPasswordUpdateCommand.DomainServer}'.");               
                return Json(new { success = true, data = response });
            }           
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on AdPasswordExpire page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }  
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    [EventCode(EventCodes.AdPasswordExpire.DeleteAd)]
    public async Task<IActionResult> DeleteAd(string id)
    {
        _logger.LogDebug("Entering Delete method in SiteType");
        try
        {
            _logger.LogDebug($"Deleting SiteType Details by Id '{id}'");

            var response = await _dataProvider.AdPasswordJob.DeleteAsync(id);

            TempData.NotifySuccess(response.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred while deleting record on SiteType.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }


    [HttpGet]
    [EventCode(EventCodes.AdPasswordExpire.GetServerRoleTypeAndServerType)]
    public async Task<IActionResult> GetServerRoleTypeAndServerType()
    {
        _logger.LogDebug("Entering GetServerRoleTypeAndServerType method in Ad Password Expire Page.");

        try
        {
            //var getDNSServerList = await _dataProvider.ServerType.GetServerTypeList();

            //var filteredDNS = getDNSServerList?.Where(x => x.Name.Equals("DNS",StringComparison.OrdinalIgnoreCase))
            //    .ToList();

            var dnsServerList = await _dataProvider.ServerType.GetServerTypeListByName("DNS");

            if (dnsServerList.Count > 0)
            {
                var serverRole = await _dataProvider.Server.GetByRoleTypeAndServerType(dnsServerList[0]?.Id, null);

                _logger.LogDebug($"Successfully retrieved server type List by server Type in Ad Password Expire Page.");

                return Json(new { Success = true, data = serverRole });
            }
            _logger.LogDebug($"Successfully retrieved server type List by server Type in Ad Password Expire Page.");

            return Json(new { Success = false, message = "No DNS server found" });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on  Password Expire Page  while retrieving the server type list by serverType.", ex);

            return ex.GetJsonException();
        }

    }



    [HttpGet]
    [EventCode(EventCodes.AdPasswordExpire.GetSchedulerPagination)]
    public async Task<JsonResult> GetSchedulerPagination(GetAdPasswordJobPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in AdPasswordExpire");
        try
        {
            var adPasswordExpireList = await _dataProvider.AdPasswordJob.GetPaginatedAdPasswordJobs(query);
            _logger.LogDebug("Successfully retrieved AdPasswordExpire paginated list on AdPasswordExpire page");
            return Json(new { success = true, data = adPasswordExpireList.Data });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on AdPasswordExpire page while processing the pagination request.", ex);
            return ex.GetJsonException();
        }
    }





}
