﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.VeritasCluster.Events.PaginatedView;

public class VeritasClusterPaginatedEventHandler : INotificationHandler<VeritasClusterPaginatedEvent>
{
    private readonly ILogger<VeritasClusterPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public VeritasClusterPaginatedEventHandler(ILogger<VeritasClusterPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(VeritasClusterPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.VeritasCluster.ToString(),
            Action = $"{ActivityType.View} {Modules.VeritasCluster}",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Veritas Cluster viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Veritas Cluster viewed");
    }
}