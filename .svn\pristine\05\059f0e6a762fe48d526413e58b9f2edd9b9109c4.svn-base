using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class TemplateHistoryRepositoryTests : IClassFixture<DatabaseFixture>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly TemplateHistoryRepository _repository;
    private readonly TemplateHistoryFixture _fixture;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public TemplateHistoryRepositoryTests(DatabaseFixture databaseFixture)
    {
        _dbContext = databaseFixture.DbContext;
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        _repository = new TemplateHistoryRepository(_dbContext, _mockLoggedInUserService.Object);
        _fixture = new TemplateHistoryFixture();

        // Setup default mock behavior
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns("COMPANY_123");
        _mockLoggedInUserService.Setup(x => x.IsAuthenticated).Returns(true);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
    }

    #region GetTemplateHistoryById Tests

    [Fact]
    public async Task GetTemplateHistoryById_ShouldReturnHistoriesForTemplate_WhenUserIsParent()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var history1 = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Test Template",
            companyId: "COMPANY_123",
            version: "1.0",
            isActive: true
        );
        var history2 = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Test Template",
            companyId: "COMPANY_456",
            version: "2.0",
            isActive: true
        );
        var history3 = _fixture.CreateTemplateHistory(
            templateId: Guid.NewGuid().ToString(),
            templateName: "Different Template",
            companyId: "COMPANY_123",
            version: "1.0",
            isActive: true
        );

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, h => Assert.Equal(templateId, h.TemplateId));
        Assert.Contains(result, h => h.CompanyId == "COMPANY_123");
        Assert.Contains(result, h => h.CompanyId == "COMPANY_456");

        // Should be ordered by Id descending
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldReturnHistoriesForCompany_WhenUserIsNotParent()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var history1 = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Test Template",
            companyId: "COMPANY_123",
            version: "1.0",
            isActive: true
        );
        var history2 = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Test Template",
            companyId: "COMPANY_456",
            version: "2.0",
            isActive: true
        );
        var history3 = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Test Template",
            companyId: "COMPANY_123",
            version: "3.0",
            isActive: true
        );

        await _repository.AddAsync(history1);
        await _repository.AddAsync(history2);
        await _repository.AddAsync(history3);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, h => Assert.Equal(templateId, h.TemplateId));
        Assert.All(result, h => Assert.Equal("COMPANY_123", h.CompanyId));

        // Should be ordered by Id descending
        for (int i = 0; i < result.Count - 1; i++)
        {
            Assert.True(result[i].Id >= result[i + 1].Id);
        }
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldReturnEmpty_WhenTemplateIdNotFound()
    {
        // Arrange
        await ClearDatabase();

        var history = _fixture.CreateTemplateHistory(
            templateId: Guid.NewGuid().ToString(),
            isActive: true
        );
        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateHistoryById(Guid.NewGuid().ToString());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldOnlyReturnActiveHistories()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var activeHistory = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Active Template",
            companyId: "COMPANY_123",
            isActive: true
        );
        var inactiveHistory = _fixture.CreateTemplateHistory(
            templateId: templateId,
            templateName: "Inactive Template",
            companyId: "COMPANY_123",
            isActive: false
        );

        await _dbContext.TemplateHistory.AddRangeAsync(activeHistory, inactiveHistory);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Active Template", result[0].TemplateName);
        Assert.True(result[0].IsActive);
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldReturnAllProperties()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var history = _fixture.CreateTemplateHistory(
            companyId: "COMPANY_TEST",
            loginName: "<EMAIL>",
            templateId: templateId,
            templateName: "Complete Test Template",
            description: "Complete test description",
            type: "WORKFLOW",
            actionType: "CREATE",
            version: "2.1.0",
            icon: "template-icon.png",
            properties: "{\"type\": \"template\", \"status\": \"completed\", \"details\": {\"steps\": 5}}",
            replicationTypeId: "REPL_001",
            replicationTypeName: "Test Replication",
            updaterId: "UPDATER_001",
            comments: "Complete test comments",
            isActive: true
        );

        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        var resultHistory = result[0];

        Assert.Equal("COMPANY_TEST", resultHistory.CompanyId);
        Assert.Equal("<EMAIL>", resultHistory.LoginName);
        Assert.Equal(templateId, resultHistory.TemplateId);
        Assert.Equal("Complete Test Template", resultHistory.TemplateName);
        Assert.Equal("Complete test description", resultHistory.Description);
        Assert.Equal("WORKFLOW", resultHistory.Type);
        Assert.Equal("CREATE", resultHistory.ActionType);
        Assert.Equal("2.1.0", resultHistory.Version);
        Assert.Equal("template-icon.png", resultHistory.Icon);
        Assert.Equal("{\"type\": \"template\", \"status\": \"completed\", \"details\": {\"steps\": 5}}", resultHistory.Properties);
        Assert.Equal("REPL_001", resultHistory.ReplicationTypeId);
        Assert.Equal("Test Replication", resultHistory.ReplicationTypeName);
        Assert.Equal("UPDATER_001", resultHistory.UpdaterId);
        Assert.Equal("Complete test comments", resultHistory.Comments);
        Assert.True(resultHistory.IsActive);
        Assert.NotNull(resultHistory.ReferenceId);
        Assert.True(resultHistory.Id > 0);
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldHandleComplexProperties()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var complexProperties = new Dictionary<string, object>
        {
            {"type", "template"},
            {"status", "completed"},
            {"metadata", new Dictionary<string, object>
                {
                    {"executionTime", "00:05:30"},
                    {"resourcesUsed", new List<string> {"CPU", "Memory", "Disk"}},
                    {"performance", new Dictionary<string, object>
                        {
                            {"throughput", "1000 ops/sec"},
                            {"latency", "50ms"}
                        }
                    }
                }
            },
            {"workflow", new Dictionary<string, object>
                {
                    {"steps", new List<Dictionary<string, object>>
                        {
                            new Dictionary<string, object> {{"step", 1}, {"action", "validate"}},
                            new Dictionary<string, object> {{"step", 2}, {"action", "process"}},
                            new Dictionary<string, object> {{"step", 3}, {"action", "complete"}}
                        }
                    }
                }
            }
        };

        var history = _fixture.CreateTemplateHistoryWithProperties(complexProperties);
        history.TemplateId = templateId;

        await _repository.AddAsync(history);

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.NotNull(result[0].Properties);
        Assert.Contains("template", result[0].Properties);
        Assert.Contains("completed", result[0].Properties);
        Assert.Contains("metadata", result[0].Properties);
        Assert.Contains("workflow", result[0].Properties);
    }

    [Fact]
    public async Task GetTemplateHistoryById_ShouldReturnEmpty_WhenNoHistoriesForCompany()
    {
        // Arrange
        await ClearDatabase();

        var templateId = Guid.NewGuid().ToString();
        var historyDifferentCompany = _fixture.CreateTemplateHistory(
            templateId: templateId,
            companyId: "COMPANY_456",
            isActive: true
        );
        var inactiveHistorySameCompany = _fixture.CreateTemplateHistory(
            templateId: templateId,
            companyId: "COMPANY_123",
            isActive: false
        );
        await _dbContext.TemplateHistory.AddRangeAsync(historyDifferentCompany, inactiveHistorySameCompany);
        _dbContext.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        // Act
        var result = await _repository.GetTemplateHistoryById(templateId);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task TemplateHistoryRepository_ShouldHandleComplexScenarios()
    {
        // Arrange
        await ClearDatabase();

        var templateId1 = Guid.NewGuid().ToString();
        var templateId2 = Guid.NewGuid().ToString();

        var histories = new List<TemplateHistory>
        {
            _fixture.CreateTemplateHistory(
                templateId: templateId1,
                templateName: "Template 1",
                companyId: "COMPANY_123",
                version: "1.0",
                type: "WORKFLOW",
                actionType: "CREATE"
            ),
            _fixture.CreateTemplateHistory(
                templateId: templateId1,
                templateName: "Template 1",
                companyId: "COMPANY_123",
                version: "2.0",
                type: "WORKFLOW",
                actionType: "UPDATE"
            ),
            _fixture.CreateTemplateHistory(
                templateId: templateId2,
                templateName: "Template 2",
                companyId: "COMPANY_456",
                version: "1.0",
                type: "FORM",
                actionType: "CREATE"
            )
        };

        foreach (var history in histories)
        {
            await _repository.AddAsync(history);
        }

        // Act & Assert - Parent user
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var template1Histories = await _repository.GetTemplateHistoryById(templateId1);
        Assert.Equal(2, template1Histories.Count);
        Assert.All(template1Histories, h => Assert.Equal("Template 1", h.TemplateName));

        var template2Histories = await _repository.GetTemplateHistoryById(templateId2);
        Assert.Single(template2Histories);
        Assert.Equal("Template 2", template2Histories[0].TemplateName);

        // Act & Assert - Non-parent user
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var companyTemplate1Histories = await _repository.GetTemplateHistoryById(templateId1);
        Assert.Equal(2, companyTemplate1Histories.Count);
        Assert.All(companyTemplate1Histories, h => Assert.Equal("COMPANY_123", h.CompanyId));

        var companyTemplate2Histories = await _repository.GetTemplateHistoryById(templateId2);
        Assert.Empty(companyTemplate2Histories);
    }

    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.TemplateHistory.RemoveRange(_dbContext.TemplateHistory);
        await _dbContext.SaveChangesAsync();
    }
}
