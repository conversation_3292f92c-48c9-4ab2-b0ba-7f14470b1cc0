using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Moq;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using Microsoft.EntityFrameworkCore;
using ContinuityPatrol.Application.Specifications;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class InfraReplicationMappingRepositoryTests : IClassFixture<InfraReplicationMappingFixture>, IDisposable
{
    private readonly InfraReplicationMappingFixture _infraReplicationMappingFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly InfraReplicationMappingRepository _repository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;

    public InfraReplicationMappingRepositoryTests(InfraReplicationMappingFixture infraReplicationMappingFixture)
    {
        _infraReplicationMappingFixture = infraReplicationMappingFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _repository = new InfraReplicationMappingRepository(_dbContext, _mockLoggedInUserService.Object);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.InfraReplicationMappings.RemoveRange(_dbContext.InfraReplicationMappings);
        await _dbContext.SaveChangesAsync();
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ExecutesWithoutError()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var companyId = "COMPANY_123";

        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);
        _mockLoggedInUserService.Setup(x => x.IsAllInfra).Returns(true);

        // Act
        var result = await _repository.GetByReferenceIdAsync(referenceId);

        // Assert
        // Since this is a view, we can only test that the method executes without error
        // and returns null when no data exists
        Assert.Null(result);
    }
    #region GetInfraReplicationMappingByDatabaseId Tests

    [Fact]
    public async Task PaginatedListAllAsync_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        var listValue = _infraReplicationMappingFixture.InfraReplicationMappingPaginationList;
        foreach (var val in listValue)
            val.IsActive = true;
        await _dbContext.InfraReplicationMappings.AddRangeAsync(listValue);
        await _dbContext.SaveChangesAsync();

        string? searchString = null;
        var spec = new InfraReplicationMappingFilterSpecification(searchString);

        // Act
        var result = await _repository.PaginatedListAllAsync(
            pageNumber: 1,
            pageSize: 20,
            specification: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );
    }
    [Fact]
    public async Task GetInfraReplicationMappingByDatabaseId_ReturnsMatchingMappings_WhenBothIdsMatch()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_123";
        var replicationMasterId = "REPL_456";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Test Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Primary",
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Test Database 2",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Secondary",
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_999", // Different database ID
                DatabaseName = "Different Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Primary",
                Properties = "{}",
                IsActive = true
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByDatabaseId(databaseId, replicationMasterId);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, mapping => 
        {
            Assert.Equal(databaseId, mapping.DatabaseId);
            Assert.Equal(replicationMasterId, mapping.ReplicationMasterId);
            Assert.True(mapping.IsActive);
        });
    }

    [Fact]
    public async Task GetInfraReplicationMappingByDatabaseId_ReturnsEmpty_WhenNoMatchingMappings()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_NONEXISTENT";
        var replicationMasterId = "REPL_NONEXISTENT";

        var infraReplicationMapping = new InfraReplicationMapping
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DatabaseId = "DB_123",
            DatabaseName = "Test Database",
            ReplicationMasterId = "REPL_456",
            ReplicationMasterName = "Test Replication Master",
            Type = "Primary",
            Properties = "{}",
            IsActive = true
        };

        await _dbContext.InfraReplicationMappings.AddAsync(infraReplicationMapping);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByDatabaseId(databaseId, replicationMasterId);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraReplicationMappingByDatabaseId_ExcludesInactiveMappings()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_123";
        var replicationMasterId = "REPL_456";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Active Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Primary",
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Inactive Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Secondary",
                Properties = "{}",
                IsActive = false // Inactive
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetInfraReplicationMappingByDatabaseId(databaseId, replicationMasterId);

        // Assert
        Assert.Single(result);
        Assert.Equal("Active Database", result.First().DatabaseName);
        Assert.True(result.First().IsActive);
    }

    #endregion

    #region GetInfraReplicationMappingByType Tests

    [Fact]
    public async Task GetInfraReplicationMappingByType_ReturnsMatchingMappings_WhenTypeExists()
    {
        // Arrange
        await ClearDatabase();
        var type = "Primary";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_123",
                DatabaseName = "Primary Database 1",
                ReplicationMasterId = "REPL_456",
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_789",
                DatabaseName = "Primary Database 2",
                ReplicationMasterId = "REPL_101",
                ReplicationMasterName = "Another Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_999",
                DatabaseName = "Secondary Database",
                ReplicationMasterId = "REPL_202",
                ReplicationMasterName = "Secondary Replication Master",
                Type = "Secondary", // Different type
                Properties = "{}",
                IsActive = true
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByType(type);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, mapping => 
        {
            Assert.Equal(type, mapping.Type);
            Assert.True(mapping.IsActive);
        });
    }

    [Fact]
    public async Task GetInfraReplicationMappingByType_ReturnsEmpty_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var nonExistentType = "NonExistentType";

        var infraReplicationMapping = new InfraReplicationMapping
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DatabaseId = "DB_123",
            DatabaseName = "Test Database",
            ReplicationMasterId = "REPL_456",
            ReplicationMasterName = "Test Replication Master",
            Type = "Primary",
            Properties = "{}",
            IsActive = true
        };

        await _dbContext.InfraReplicationMappings.AddAsync(infraReplicationMapping);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByType(nonExistentType);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetInfraReplicationMappingByType_ExcludesInactiveMappings()
    {
        // Arrange
        await ClearDatabase();
        var type = "Primary";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_123",
                DatabaseName = "Active Primary Database",
                ReplicationMasterId = "REPL_456",
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_789",
                DatabaseName = "Inactive Primary Database",
                ReplicationMasterId = "REPL_101",
                ReplicationMasterName = "Another Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = false // Inactive
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetInfraReplicationMappingByType(type);

        // Assert
        Assert.Single(result);
        Assert.Equal("Active Primary Database", result.First().DatabaseName);
        Assert.True(result.First().IsActive);
    }

    [Fact]
    public async Task GetInfraReplicationMappingByType_IsCaseSensitive()
    {
        // Arrange
        await ClearDatabase();
        var type = "Primary";

        var infraReplicationMapping = new InfraReplicationMapping
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DatabaseId = "DB_123",
            DatabaseName = "Test Database",
            ReplicationMasterId = "REPL_456",
            ReplicationMasterName = "Test Replication Master",
            Type = type,
            Properties = "{}",
            IsActive = true
        };

        await _dbContext.InfraReplicationMappings.AddAsync(infraReplicationMapping);
        await _dbContext.SaveChangesAsync();

        // Act
        var resultExactCase = await _repository.GetInfraReplicationMappingByType("Primary");
        var resultDifferentCase = await _repository.GetInfraReplicationMappingByType("primary");

        // Assert
        Assert.Single(resultExactCase); // Exact case should match
        Assert.Empty(resultDifferentCase); // Different case should not match (case sensitive)
    }

    #endregion

    #region GetTypeByDatabaseIdAndReplicationMasterId Tests

    [Fact]
    public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsMatchingMappings_WhenAllParametersMatch()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_123";
        var replicationMasterId = "REPL_456";
        var type = "Primary";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Matching Database 1",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Matching Database 2",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Different Type Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = "Secondary", // Different type
                Properties = "{}",
                IsActive = true
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, mapping =>
        {
            Assert.Equal(databaseId, mapping.DatabaseId);
            Assert.Equal(replicationMasterId, mapping.ReplicationMasterId);
            Assert.Equal(type, mapping.Type);
            Assert.True(mapping.IsActive);
        });
    }

    [Fact]
    public async Task GetTypeByDatabaseIdAndReplicationMasterId_ReturnsEmpty_WhenNoMatchingMappings()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_NONEXISTENT";
        var replicationMasterId = "REPL_NONEXISTENT";
        var type = "NonExistentType";

        var infraReplicationMapping = new InfraReplicationMapping
        {
            ReferenceId = Guid.NewGuid().ToString(),
            DatabaseId = "DB_123",
            DatabaseName = "Test Database",
            ReplicationMasterId = "REPL_456",
            ReplicationMasterName = "Test Replication Master",
            Type = "Primary",
            Properties = "{}",
            IsActive = true
        };

        await _dbContext.InfraReplicationMappings.AddAsync(infraReplicationMapping);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

        // Assert
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetTypeByDatabaseIdAndReplicationMasterId_ExcludesInactiveMappings()
    {
        // Arrange
        await ClearDatabase();
        var databaseId = "DB_123";
        var replicationMasterId = "REPL_456";
        var type = "Primary";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Active Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = databaseId,
                DatabaseName = "Inactive Database",
                ReplicationMasterId = replicationMasterId,
                ReplicationMasterName = "Test Replication Master",
                Type = type,
                Properties = "{}",
                IsActive = false // Inactive
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
         _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetTypeByDatabaseIdAndReplicationMasterId(databaseId, replicationMasterId, type);

        // Assert
        Assert.Single(result);
        Assert.Equal("Active Database", result.First().DatabaseName);
        Assert.True(result.First().IsActive);
    }

    #endregion

    #region GetInfraReplicationMappingByComponentId Tests

    [Fact]
    public async Task GetInfraReplicationMappingByComponentId_ReturnsMatchingMappings_WhenDatabaseIdMatches()
    {
        // Arrange
        await ClearDatabase();
        var componentId = "COMP_123";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = componentId, // Matches component ID
                DatabaseName = "Matching Database",
                ReplicationMasterId = "REPL_456",
                ReplicationMasterName = "Test Replication Master",
                Type = "Primary",
                Properties = "{}",
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_999", // Different database ID
                DatabaseName = "Different Database",
                ReplicationMasterId = "REPL_789",
                ReplicationMasterName = "Another Replication Master",
                Type = "Secondary",
                Properties = "{}",
                IsActive = true
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByComponentId(componentId);

        // Assert
        Assert.Single(result);
        Assert.Equal(componentId, result.First().DatabaseId);
        Assert.Equal("Matching Database", result.First().DatabaseName);
    }

    [Fact]
    public async Task GetInfraReplicationMappingByComponentId_ReturnsMatchingMappings_WhenPropertiesContainComponentId()
    {
        // Arrange
        await ClearDatabase();
        var componentId = "COMP_123";

        var infraReplicationMappings = new List<InfraReplicationMapping>
        {
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_456",
                DatabaseName = "Properties Match Database",
                ReplicationMasterId = "REPL_456",
                ReplicationMasterName = "Test Replication Master",
                Type = "Primary",
                Properties = $"{{\"componentId\":\"{componentId}\",\"other\":\"value\"}}", // Contains component ID
                IsActive = true
            },
            new InfraReplicationMapping
            {
                ReferenceId = Guid.NewGuid().ToString(),
                DatabaseId = "DB_999",
                DatabaseName = "No Match Database",
                ReplicationMasterId = "REPL_789",
                ReplicationMasterName = "Another Replication Master",
                Type = "Secondary",
                Properties = "{\"other\":\"value\"}", // Does not contain component ID
                IsActive = true
            }
        };

        await _dbContext.InfraReplicationMappings.AddRangeAsync(infraReplicationMappings);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetInfraReplicationMappingByComponentId(componentId);

        // Assert
        Assert.Single(result);
        Assert.Equal("Properties Match Database", result.First().DatabaseName);
        Assert.Contains(componentId, result.First().Properties);
    }

    #endregion
}
