﻿using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.SvcMsSqlMonitorStatus.Queries.GetSvcMsSqlMonitorStatusByInfraObjectId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SvcMsSqlMonitorStatus.Queries
{
    public class SvcMsSqlMonitorStatusByInfraObjectIdQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISvcMsSqlMonitorStatusRepository> _mockSvcMsSqlMonitorStatusRepository;
        private readonly SvcMsSqlMonitorStatusByInfraObjectIdQueryHandler _handler;

        public SvcMsSqlMonitorStatusByInfraObjectIdQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSvcMsSqlMonitorStatusRepository = new Mock<ISvcMsSqlMonitorStatusRepository>();
            _handler = new SvcMsSqlMonitorStatusByInfraObjectIdQueryHandler(_mockMapper.Object, _mockSvcMsSqlMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnReferenceId_WhenMatchingInfraObjectIdExists()
        {
            var infraObjectId = Guid.NewGuid().ToString();
            var referenceId = Guid.NewGuid().ToString();

            var svcMsSqlMonitorStatus = new Domain.Entities.SvcMsSqlMonitorStatus
            {
                InfraObjectId = infraObjectId,
                ReferenceId = referenceId,
                Type = "Active"
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync(svcMsSqlMonitorStatus);

            var query = new SvcMsSqlMonitorStatusByInfraObjectIdQuery
            {
                InfraObjectId = infraObjectId
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(referenceId, result);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
        }
        [Fact]
        public void SvcMsSqlMonitorStatusDetailByTypeQuery_ShouldAssignTypeCorrectly()
        {
            // Arrange & Assign
            var query = new SvcMsSqlMonitorStatusDetailByTypeQuery
            {
                Type = "SQLMonitor"
            };

            // Assert
            Assert.Equal("SQLMonitor", query.Type);
        }
        [Fact]
        public void SvcMsSqlMonitorStatusByInfraObjectIdVm_ShouldAssignIdCorrectly()
        {
            // Arrange & Assign
            var vm = new SvcMsSqlMonitorStatusByInfraObjectIdVm
            {
                Id = "status-123"
            };

            // Assert
            Assert.Equal("status-123", vm.Id);
        }

       

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenInfraObjectIdNotFound()
        {
            var infraObjectId = Guid.NewGuid().ToString();

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync((Domain.Entities.SvcMsSqlMonitorStatus)null);

            var query = new SvcMsSqlMonitorStatusByInfraObjectIdQuery
            {
                InfraObjectId = infraObjectId
            };

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldVerifyRepositoryCall()
        {
            var infraObjectId = Guid.NewGuid().ToString();
            var referenceId = Guid.NewGuid().ToString();

            var svcMsSqlMonitorStatus = new Domain.Entities.SvcMsSqlMonitorStatus
            {
                InfraObjectId = infraObjectId,
                ReferenceId = referenceId,
                Type = "Active"
            };

            _mockSvcMsSqlMonitorStatusRepository.Setup(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId))
                .ReturnsAsync(svcMsSqlMonitorStatus);

            var query = new SvcMsSqlMonitorStatusByInfraObjectIdQuery
            {
                InfraObjectId = infraObjectId
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            _mockSvcMsSqlMonitorStatusRepository.Verify(repo => repo.GetSvcMsSqlMonitorStatusByInfraObjectId(infraObjectId), Times.Once);
            Assert.Equal(referenceId, result);
        }
    }
}
