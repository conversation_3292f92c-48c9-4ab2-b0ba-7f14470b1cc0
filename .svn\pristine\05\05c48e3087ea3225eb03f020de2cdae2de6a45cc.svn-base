﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetPaginatedList;

public class GetWorkflowActionPaginatedListQueryHandler : IRequestHandler<GetWorkflowActionPaginatedListQuery,
    PaginatedResult<WorkflowActionListVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowActionRepository _workflowActionRepository;

    public GetWorkflowActionPaginatedListQueryHandler(IMapper mapper,
        IWorkflowActionRepository workflowActionRepository)
    {
        _mapper = mapper;
        _workflowActionRepository = workflowActionRepository;
    }

    public async Task<PaginatedResult<WorkflowActionListVm>> Handle(GetWorkflowActionPaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new WorkflowActionFilterSpecification(request.SearchString);

        var queryable = await _workflowActionRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);

        var workflowActionLists = _mapper.Map<PaginatedResult<WorkflowActionListVm>>(queryable);
        
        return workflowActionLists;
    }
}