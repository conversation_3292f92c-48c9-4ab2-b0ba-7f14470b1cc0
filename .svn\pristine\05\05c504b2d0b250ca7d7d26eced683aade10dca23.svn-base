﻿using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowExecutionEventLog.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoWorkflowExecutionEventLogDataAttribute : AutoDataAttribute
{
    public AutoWorkflowExecutionEventLogDataAttribute() : base(() =>
    {
        var fixture = new Fixture();

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<CreateWorkflowExecutionEventLogCommand>(p => p.LoginName, 10));

        fixture.Customizations.Add(
            new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowExecutionEventLogCommand>(p => p.Id, 10));


        return fixture;

    })
    {

    }
}