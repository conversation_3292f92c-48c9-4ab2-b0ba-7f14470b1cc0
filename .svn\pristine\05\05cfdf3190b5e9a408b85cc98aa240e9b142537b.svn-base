﻿using ContinuityPatrol.Application.Features.BusinessServiceHealthLog.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceHealthLog.Commands;

public class CreateBusinessServiceHealthLogTests : IClassFixture<BusinessServiceHealthLogFixture>
{
    private readonly BusinessServiceHealthLogFixture _businessServiceHealthLogFixture;
    private readonly Mock<IBusinessServiceHealthLogRepository> _mockBusinessServiceHealthLogRepository;
    private readonly CreateBusinessServiceHealthLogCommandHandler _handler;

    public CreateBusinessServiceHealthLogTests(BusinessServiceHealthLogFixture businessServiceHealthLogFixture)
    {
        _businessServiceHealthLogFixture = businessServiceHealthLogFixture;

        _mockBusinessServiceHealthLogRepository = BusinessServiceHealthLogRepositoryMocks.CreateBusinessServiceHealthLogRepository(_businessServiceHealthLogFixture.BusinessServiceHealthLogs);

        _handler = new CreateBusinessServiceHealthLogCommandHandler(_mockBusinessServiceHealthLogRepository.Object, _businessServiceHealthLogFixture.Mapper);
    }

    [Fact]
    public async Task Handle_IncreaseBusinessServiceHealthLogCount_When_BusinessServiceHealthLogCreated()
    {
        await _handler.Handle(_businessServiceHealthLogFixture.CreateBusinessServiceHealthLogCommand, CancellationToken.None);

        var allCategories = await _mockBusinessServiceHealthLogRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_businessServiceHealthLogFixture.BusinessServiceHealthLogs.Count);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceHealthLogResponse_When_BusinessServiceHealthLogCreated()
    {
        var result = await _handler.Handle(_businessServiceHealthLogFixture.CreateBusinessServiceHealthLogCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateBusinessServiceHealthLogResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceHealthLogFixture.CreateBusinessServiceHealthLogCommand, CancellationToken.None);

        _mockBusinessServiceHealthLogRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.BusinessServiceHealthLog>()), Times.Once);
    }
}