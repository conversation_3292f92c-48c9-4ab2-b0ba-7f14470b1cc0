namespace ContinuityPatrol.Application.Features.SiteLocation.Queries.GetNameUnique;

public class GetSiteLocationNameUniqueQueryHandler : IRequestHandler<GetSiteLocationNameUniqueQuery, bool>
{
    private readonly ISiteLocationRepository _siteLocationRepository;

    public GetSiteLocationNameUniqueQueryHandler(ISiteLocationRepository siteLocationRepository)
    {
        _siteLocationRepository = siteLocationRepository;
    }

    public async Task<bool> Handle(GetSiteLocationNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _siteLocationRepository.IsNameExist(request.Name, request.Id);
    }
}