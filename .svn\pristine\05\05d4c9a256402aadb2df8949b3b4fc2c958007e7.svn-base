using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalVariable.Validators;

public class UpdateGlobalVariableValidatorTests : IClassFixture<GlobalVariableFixture>
{
    private readonly Mock<IGlobalVariableRepository> _mockGlobalVariableRepository;

    private readonly GlobalVariableFixture _globalVariableFixture;

    public UpdateGlobalVariableValidatorTests(GlobalVariableFixture globalVariableFixture)
    {
        _globalVariableFixture = globalVariableFixture;

        var globalVariables = new Fixture().Create<List<Domain.Entities.GlobalVariable>>();

        _mockGlobalVariableRepository = GlobalVariableRepositoryMocks.UpdateGlobalVariableRepository(globalVariables);
    }

    [Fact]
    public void Constructor_ShouldInitializeWithRepository()
    {
        // Arrange & Act
        var validator = new UpdateGlobalVariableCommandValidator(_mockGlobalVariableRepository.Object);

        // Assert
        validator.ShouldNotBeNull();
    }

    [Theory]
    [AutoGlobalVariableData]
    public async Task Verify_Update_ValidCommand_ShouldPass(UpdateGlobalVariableCommand updateGlobalVariableCommand)
    {
        // Arrange
        var validator = new UpdateGlobalVariableCommandValidator(_mockGlobalVariableRepository.Object);

        updateGlobalVariableCommand.VariableName = "TestVariable";
        updateGlobalVariableCommand.VariableValue = "TestValue";
        updateGlobalVariableCommand.Id = Guid.NewGuid().ToString();

        // Act
        var validateResult = await validator.ValidateAsync(updateGlobalVariableCommand, CancellationToken.None);

        // Assert
        validateResult.IsValid.ShouldBeTrue();
    }

    [Theory]
    [AutoGlobalVariableData]
    public async Task Verify_Update_EmptyCommand_ShouldPass(UpdateGlobalVariableCommand updateGlobalVariableCommand)
    {
        // Arrange
        var validator = new UpdateGlobalVariableCommandValidator(_mockGlobalVariableRepository.Object);

        // Act
        var validateResult = await validator.ValidateAsync(updateGlobalVariableCommand, CancellationToken.None);

        // Assert - Since the validator has no rules, it should pass even with empty/default values
        validateResult.IsValid.ShouldBeTrue();
    }

}
