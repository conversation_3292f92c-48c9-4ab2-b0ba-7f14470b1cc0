﻿using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Events.Update;

namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Update;

public class UpdateEscalationMatrixLevelCommandHandler : IRequestHandler<UpdateEscalationMatrixLevelCommand,
    UpdateEscalationMatrixLevelResponse>
{
    private readonly IEscalationMatrixLevelRepository _escalationMatrixLevelRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateEscalationMatrixLevelCommandHandler(IMapper mapper,
        IPublisher publisher, IEscalationMatrixLevelRepository escalationMatrixLevelRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _escalationMatrixLevelRepository = escalationMatrixLevelRepository;
    }

    public async Task<UpdateEscalationMatrixLevelResponse> Handle(UpdateEscalationMatrixLevelCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _escalationMatrixLevelRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.EscalationMatrixLevel), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateEscalationMatrixCommand),
            typeof(Domain.Entities.EscalationMatrixLevel));

        await _escalationMatrixLevelRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateEscalationMatrixLevelResponse
        {
            Message = Message.Update(nameof(Domain.Entities.EscalationMatrixLevel), eventToUpdate.EscLevName),

            Id = eventToUpdate.ReferenceId
        };
        await _publisher.Publish(new EscalationMatrixLevelUpdatedEvent { EscLevName = eventToUpdate.EscLevName },
            cancellationToken);

        return response;
    }
}