﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_DashboardLayout.cshtml";
}
<link href="~/css/OneViewDashboard.css" rel="stylesheet" />

@Html.AntiForgeryToken()
<div class="page-content position-relative chartOne">

    <div id="spinnerOverlayChartOne">
        <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="col-12">
        <h6 class="page_title mb-2">
            <i class="cp-one-view"></i><span>One View</span>
        </h6>
    </div>
    <div class="col-12">
        <div class="row g-3">
            <div class="col-12 col-lg-3 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header header">
                        <span class="card-title">DR Readiness Status</span>
                        <span>Readiness Score <span class="fw-semibold fs-7 ReadinessScorePercentage">0%</span></span>
                    </div>
                    <div class="card-body d-flex justify-content-between py-1">
                        <div class="d-grid gap-2 overallSites align-content-start w-50" style="height: calc(50vh - 132px); overflow: auto;">
                        </div>
                        <div class="w-50">
                            <div class="card Card_Design_None mb-0 h-100">
                                <div class="h-100" id="DR_ReadinessStatus"></div>
                                <div class="d-flex justify-content-center gap-2">
                                    <div class="d-grid align-items-center gap-1">
                                        <small class="fw-semibold">DR Ready</small>
                                        <div class="d-flex align-items-center gap-2">
                                            <i class="cp-success text-primary"></i>
                                            <span class="card-title DRReadyCount">0</span>
                                        </div>
                                    </div>
                                    <div class="vr"></div>
                                    <div class="d-grid align-items-center gap-1">
                                        <small class="fw-semibold">Not Ready</small>
                                        <div class="d-flex align-items-center gap-2">
                                            <i class="cp-error text-danger"></i>
                                            <span class="card-title DRNotReadyCount">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- <div id="spinnerOverlayChartOne">
                            <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>-->
                    </div>
                    <div class="card-footer border-top">
                        <div class="small fw-semibold"><span>Last DR Drill Perfomance</span></div>
                        <div class="d-flex gap-2 rounded-2 bg-white p-2 border mt-1 DRDrillPerfomanceView">
                            <i class="cp-single-dot fs-10 mt-1 " id="dotStatus"></i>
                            <div class="flex-fill lastDrillData">
                                <div class="d-flex gap-2">
                                    <small class="" id="lastDateDrillPerformance"></small>
                                    @* <span class="lastDateDrillStatus fw-normal ms-auto" style="line-height: 0.8;">View</span> *@
                                </div>
                                <div class="fw-semibold" id="lastDateDrillName"></div>
                                <div class="d-flex align-items-center">
                                    <span class="text-secondary">Drill Duration : <span class="text-dark fw-semibold" id="lastDrillDuration"></span></span>
                                    <span class="badge text-bg-success rounded-pill fw-normal ms-auto" id="lastDateDrillStatus"></span>
                                    @*  <small class="ms-auto text-secondary" id="lastExpectedDrillMinute">Expected:15Mins</small> *@
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div id="carouselExampleDark" class="carousel slide carousel-dark" data-bs-ride="carousel">
                    <!--position-relative opacity-75 chartTwo-->
                    <div class="card Card_Design_None mb-0">
                        <div class="card-header card-title pb-0">Automated Risk Mitigation & Recommendations</div>
                        <div class="card-body pt-0 pb-1">
                            <div class="carousel-inner">
                                <div class="carousel-item active">
                                    <div class="">Backup Frequency</div>
                                    <div class="d-flex align-items-center">
                                        <div id="BackupFrequencyChart" style="height: calc(50vh - 190px);"></div>
                                        <span style="width: 50%;">Your Backup performance is <span class="text-danger">25%</span> this may lead to High- Risk System</span>
                                    </div>
                                    @* <div class="d-flex justify-content-between align-items-center mt-1">
                                        <span>Here are some tips on how to improve your score</span>
                                        <button class="btn btn-outline-primary btn-sm py-0 rounded-1">Details</button>
                                    </div> *@
                                </div>
                                <div class="carousel-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="">Failover Drills</span><span class="text-secondary">Failover Drills will affect your </span><span class="text-danger">RTO Compliance</span>
                                    </div>
                                    <div id="FailoverDrillsChart" style="height: calc(50vh - 190px);"></div>
                                </div>
                                <div class="carousel-item">
                                    <div class="">Backup Frequency</div>
                                    <div id="CyberSecurityChart" style="height: calc(50vh - 190px);"></div>
                                   @*  <div class="d-flex justify-content-between align-items-center mt-1">
                                        <span>Here are some tips on how to improve your score</span>
                                        <button class="btn btn-outline-primary btn-sm py-0 rounded-1">Details</button>
                                    </div> *@
                                </div>
                            </div>
                        </div>

                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#carouselExampleDark" data-bs-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>

                    <!--  <div id="spinnerOverlayChartTwo">
                        <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>-->
                </div>
            </div>
            <div class="col-12 col-lg-6">
                <div class="card Card_Design_None position-relative bg-transparent mb-0">
                    <div class="card-body p-0">
                        <!--opacity-75 chartThree-->
                        <div id="DCMappingChart" style="height: calc(100vh - 108px); width: 100%;"></div>
                    </div>
                    <div class="card-footer position-absolute bottom-0 w-100 p-2 bg-white">
                        <div class="d-flex align-items-center justify-content-between mb-1">
                            <div class="d-flex align-items-center site_collapse siteGroup" onclick="GetTotalSiteDetailsForOneViewList('','collapseExample')" tapstatus="Total Sites" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <div class="card mb-0">
                                    <div class="card-body p-2">
                                        <i class="cp-physical-drsite text-primary fs-5"></i>
                                    </div>
                                </div>
                                <div class="ms-1">
                                    <small class="text-secondary">Total Sites</small>
                                    <h6 class="mb-0"><span class="SiteConfigured" id="totalSiteConfigured">0</span></h6>
                                </div>
                            </div>
                            <div class="d-flex align-items-center site_collapse siteGroup" onclick="GetTotalSiteDetailsForOneViewList('PR','collapseExample')" tapstatus="PR Site" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <div class="card mb-0">
                                    <div class="card-body p-2">
                                        <i class="cp-list-prsite text-primary fs-5"></i>
                                    </div>
                                </div>
                                <div class="ms-1">
                                    <small class="text-secondary">PR</small>
                                    <h6 class="mb-0"><span class="SiteConfigured" id="totalPRProfiles">0</span></h6>
                                </div>
                            </div>
                            <div class="d-flex align-items-center site_collapse siteGroup" onclick="GetTotalSiteDetailsForOneViewList('DR','collapseExample')" tapstatus="DR Site" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <div class="card mb-0">
                                    <div class="card-body p-2">
                                        <i class="cp-prsites text-primary fs-5"></i>
                                    </div>
                                </div>
                                <div class="ms-1">
                                    <small class="text-secondary">DR</small>
                                    <h6 class="mb-0"><span class="SiteConfigured" id="totalDRProfiles">0</span></h6>
                                </div>
                            </div>
                            @*   
                            <div class="d-flex align-items-center site_collapse siteGroup" onclick="GetTotalSiteDetailsForOneViewList('NearDr','collapseExample')" tapstatus="Near DR Site" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <div class="card mb-0">
                                    <div class="card-body p-2">
                                        <i class="cp-list-neardrsite text-primary fs-5"></i>
                                    </div>
                                </div>
                                <div class="ms-1">
                                    <small class="text-secondary">Near DR</small>
                                    <h6 class="mb-0"><span class="SiteConfigured" id="totalNearDRProfiles">0</span></h6>
                                </div>
                            </div>
                            *@
                            <div class="d-flex align-items-center site_collapse siteGroup" onclick="GetTotalSiteDetailsForOneViewList('Custom','collapseExample')" tapstatus="Custom DR Site" role="button" aria-expanded="false" aria-controls="collapseExample">
                                <div class="card mb-0">
                                    <div class="card-body p-2">
                                        <i class="cp-next-drsite text-primary fs-5"></i>
                                    </div>
                                </div>
                                <div class="ms-1">
                                    <small class="text-secondary">Custom DR</small>
                                    <h6 class="mb-0"><span class="SiteConfigured" id="totalCustomDRProfiles">0</span></h6>
                                </div>
                            </div>
                        </div>
                        <div class="collapse w-100 collapseToggle" id="collapseExample">
                            <div class="card mb-0">
                                <div class="card-header bg-primary text-white header px-2 py-1">
                                    <div class="card-title" id="siteTitle">Total Sites<span class="mx-1">-</span><label id="totalSiteTitleCount">0</label></div>
                                    <span>Chennai</span>
                                </div>
                                <div class="card-body">
                                    <div class="row row-cols-4 g-3 align-items-center h-100" id="sitedetails">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <div id="spinnerOverlayChartThree">
                        <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>-->
                </div>
            </div>
            <div class="col-12 col-lg-3 d-grid" style="grid-auto-rows: min-content;">
                <div class="card Card_Design_None mb-2" style="height: fit-content; min-height:150px;">
                    <!--position-relative opacity-75 chartFour-->
                    <div class="card-header header pb-0">
                        <span class="card-title">Automaton & Orchestration</span><span>Total Workflow <span class="fs-6 fw-semibold totalWorkflowCount">0</span></span>
                    </div>
                    <div class="card-body p-0">
                        <div class="h-100" id="AutomatonOrchestrationChart"></div>
                    </div>

                    <!--  <div id="spinnerOverlayChartFour">
                        <div class="spinner-border text-primary" role="status" style="width: 2.5rem; height: 2.5rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>-->
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">RTO & RPO Breach</div>
                    <div class="card-body pt-0 d-grid">
                        <div class="card-group gap-3 mb-3">
                            <div class="card Card_Design_None rounded-2" style="background-color: #f2f6fe;">
                                <div class="card-body p-2 pb-0">
                                    <div class="d-flex align-items-center">
                                        <div class="card mb-0">
                                            <div class="card-body p-1">
                                                <i class="cp-RPO text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <span class="fs-7 ms-2">RPO Achieved</span>
                                    </div>
                                </div>
                                <div class="card-footer p-2 py-1 d-flex align-items-center gap-1">
                                    <span class="fs-6" id="RPOAchieved">0</span><i class="cp-up-linearrow text-success align-middle"></i>
                                </div>
                            </div>
                            <div class="card Card_Design_None rounded-2" style="background-color: #f2f6fe;">
                                <div class="card-body p-2 pb-0">
                                    <div class="d-flex align-items-center">
                                        <div class="card mb-0">
                                            <div class="card-body p-1">
                                                <i class="cp-RTO text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <span class="fs-7 ms-2">RTO Achieved</span>
                                    </div>
                                </div>
                                <div class="card-footer p-2 py-1 d-flex align-items-center gap-1">
                                    <span class="fs-6" id="RTOAchieved">0</span><i class="cp-up-linearrow text-success align-middle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="card-group gap-3">
                            <div class="card Card_Design_None rounded-2" style="background-color: #f2f6fe;">
                                <div class="card-body p-2 pb-0">
                                    <div class="d-flex align-items-center">
                                        <div class="card mb-0">
                                            <div class="card-body p-1">
                                                <i class="cp-exceeded text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <span class="fs-7 ms-2">RPO Exceeded</span>
                                    </div>
                                </div>
                                <div class="card-footer p-2 py-1 d-flex align-items-center gap-1">
                                    <span class="fs-6" id="RPOExceeded">0</span><i class="cp-down-linearrow text-danger align-middle"></i>
                                </div>
                            </div>
                            <div class="card Card_Design_None rounded-2" style="background-color: #f2f6fe;">
                                <div class="card-body p-2 pb-0">
                                    <div class="d-flex align-items-center">
                                        <div class="card mb-0">
                                            <div class="card-body p-1">
                                                <i class="cp-exceeded text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <span class="fs-7 ms-2">RTO Exceeded</span>
                                    </div>
                                </div>
                                <div class="card-footer p-2 py-1 d-flex align-items-center gap-1">
                                    <span class="fs-6" id="RTOExceeded">0</span><i class="cp-down-linearrow text-danger align-middle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card Card_Design_None new-blue-shades-color mb-0" style="grid-template-rows: max-content;">
                    <div class="card-header pb-0 header">
                        <span class="card-title">DR Drill Calendar</span>
                        <div class="time d-flex align-items-center">
                            <span id="hour">00</span>:<span id="minutes">00</span><small class="text-secondary ms-1" id="period">AM</small>
                        </div>
                    </div>
                    <div class="card-body pt-0">
@*                         <div>
                            <div class="d-flex align-items-center gap-2">
                                
                                <div class="vr"></div>
                                <div class="date d-grid">
                                    <small class="text-secondary" id="fullDay"></small>
                                    <div>
                                        <span class="fw-semibold" id="fullDate"></span>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="vr mx-2"></div> *@
                        <div class="pb-1">Future Events</div>
                        <div class='wrapper'>
                            <div class='carousel' id="futureEvents">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/lib/amcharts4/maps.js"></script>
<script src="~/lib/amcharts4/countries2.js"></script>
<script src="~/lib/amcharts4/worldIndiaLow.js"></script>
<script src="~/js/dashboard/oneview/dcmappingchart.js"></script>
<script src="~/js/dashboard/oneview/resiliencemapping.js"></script>
@*
<script src="~/js/dashboard/oneview/totalsite.js"></script>
*@
<script src="~/js/Dashboard/OneView/DRReadinessPieChart.js"></script>
<script src="~/js/Dashboard/OneView/BackupFrequencyChart.js"></script>
<script src="~/js/Dashboard/OneView/FailoverDrillsChart.js"></script>
<script src="~/js/Dashboard/OneView/CyberSecurityChart.js"></script>
<script src="~/js/Dashboard/OneView/AutomatonOrchestrationPieChart.js"></script>
