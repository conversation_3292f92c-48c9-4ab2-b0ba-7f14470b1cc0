﻿using ContinuityPatrol.Domain.ViewModels.PluginManagerHistoryModel;

namespace ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetList;

public class
    GetPluginManagerHistoryListQueryHandler : IRequestHandler<GetPluginManagerHistoryListQuery,
        List<PluginManagerHistoryListVm>>
{
    private readonly IMapper _mapper;
    private readonly IPluginManagerHistoryRepository _pluginManagerHistoryRepository;

    public GetPluginManagerHistoryListQueryHandler(IMapper mapper,
        IPluginManagerHistoryRepository pluginManagerHistoryRepository)
    {
        _mapper = mapper;
        _pluginManagerHistoryRepository = pluginManagerHistoryRepository;
    }

    public async Task<List<PluginManagerHistoryListVm>> Handle(GetPluginManagerHistoryListQuery request,
        CancellationToken cancellationToken)
    {
        var pluginManagerHistoryList = (await _pluginManagerHistoryRepository.ListAllAsync()).ToList();

        return pluginManagerHistoryList.Count <= 0
            ? new List<PluginManagerHistoryListVm>()
            : _mapper.Map<List<PluginManagerHistoryListVm>>(pluginManagerHistoryList);
    }
}