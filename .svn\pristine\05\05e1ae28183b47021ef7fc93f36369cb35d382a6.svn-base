﻿using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowId;
using ContinuityPatrol.Application.Features.WorkflowHistory.Queries.GetWorkflowHistoryByWorkflowIdAndVersion;
using ContinuityPatrol.Services.Db.Impl.Orchestration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Orchestration;

public class WorkflowHistoryServiceTests : BaseServiceTestSetup<WorkflowHistoryService>, IClassFixture<WorkflowHistoryServiceFixture>
{
    private readonly WorkflowHistoryServiceFixture _fixture;

    public WorkflowHistoryServiceTests(WorkflowHistoryServiceFixture fixture)
    {
        InitializeService(accessor => new WorkflowHistoryService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetWorkflowHistoryList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowHistoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.WorkflowHistoryList);

        var result = await ServiceUnderTest.GetWorkflowHistoryList();

        Assert.Equal(_fixture.WorkflowHistoryList.Count, result.Count);
    }

    [Fact]
    public async Task GetWorkflowHistoryByWorkflowId_Should_Return_Data()
    {
        var workflowId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetWorkflowHistoryByWorkflowIdQuery>(q => q.WorkflowId == workflowId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.WorkflowHistoryByWorkflowIdList);

        var result = await ServiceUnderTest.GetWorkflowHistoryByWorkflowId(workflowId);

        Assert.Equal(_fixture.WorkflowHistoryByWorkflowIdList.Count, result.Count);
    }

    [Fact]
    public async Task GetPaginatedWorkflowHistories_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await ServiceUnderTest.GetPaginatedWorkflowHistories(_fixture.PaginatedQuery);

        Assert.Equal(_fixture.PaginatedResult.Data.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetWorkflowHistoryByWorkflowIdAndVersion_Should_Return_Data()
    {
        var workflowId = Guid.NewGuid().ToString();
        var version = "v1";

        MediatorMock.Setup(m => m.Send(
                It.Is<GetWorkflowHistoryByWorkflowIdAndVersionQuery>(q =>
                    q.WorkflowId == workflowId && q.Version == version),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.WorkflowHistoryByWorkflowIdAndVersionVm);

        var result = await ServiceUnderTest.GetWorkflowHistoryByWorkflowIdAndVersion(workflowId, version);

        Assert.NotNull(result);
        Assert.Equal(_fixture.WorkflowHistoryByWorkflowIdAndVersionVm.WorkflowId, result.WorkflowId);
    }
}
