﻿using ContinuityPatrol.Application.Features.NodeWorkflowExecution.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.NodeWorkflowExecution.Commands;

public class CreateNodeWorkflowExecutionTests : IClassFixture<NodeWorkflowExecutionFixture>
{
    private readonly NodeWorkflowExecutionFixture _nodeWorkflowExecutionFixture;

    private readonly Mock<INodeWorkflowExecutionRepository> _mockNodeWorkflowExecutionRepository;

    private readonly CreateNodeWorkflowExecutionCommandHandler _handler;


    public CreateNodeWorkflowExecutionTests(NodeWorkflowExecutionFixture nodeWorkflowExecutionFixture)
    {
        _nodeWorkflowExecutionFixture = nodeWorkflowExecutionFixture;

        //var mockPublisher = new Mock<IPublisher>();

        _mockNodeWorkflowExecutionRepository = NodeWorkflowExecutionRepositoryMocks.CreateNodeWorkflowExecutionRepository(_nodeWorkflowExecutionFixture.NodeWorkflowExecutions);

        _handler = new CreateNodeWorkflowExecutionCommandHandler(_nodeWorkflowExecutionFixture.Mapper, _mockNodeWorkflowExecutionRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_Increase_Count_When_AddValid_NodeWorkflowExecution()
    {
        await _handler.Handle(_nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionCommand, CancellationToken.None);

        var allCategories = await _mockNodeWorkflowExecutionRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_nodeWorkflowExecutionFixture.NodeWorkflowExecutions.Count);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulNodeWorkflowExecutionResponse_When_AddValidNodeWorkflowExecution()
    {
        var result = await _handler.Handle(_nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateNodeWorkflowExecutionResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_ExecuteAddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_nodeWorkflowExecutionFixture.CreateNodeWorkflowExecutionCommand, CancellationToken.None);

        _mockNodeWorkflowExecutionRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.NodeWorkflowExecution>()), Times.Once);
    }
}