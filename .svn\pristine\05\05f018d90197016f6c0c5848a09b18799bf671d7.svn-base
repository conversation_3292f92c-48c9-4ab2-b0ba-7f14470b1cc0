﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;
using System.Linq.Expressions;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AlertRepositoryMocks
{
    public static Mock<IAlertRepository> CreateAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.AddAsync(It.IsAny<Alert>())).ReturnsAsync(
            (Alert alert) =>
            {
                alert.Id = new Fixture().Create<int>();

                alert.ReferenceId = new Fixture().Create<Guid>().ToString();

                alerts.Add(alert);

                return alert;
            });
        return alertRepository;
    }

    public static Mock<IAlertRepository> UpdateAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Alert>())).ReturnsAsync((Alert alert) =>
        {
            var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

            alerts[index] = alert;

            return alert;

        });
        return alertRepository;
    }

    public static Mock<IAlertRepository> DeleteAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();
        
        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Alert>())).ReturnsAsync(
            (Alert alert) =>
            {
                var index = alerts.FindIndex(item => item.ReferenceId == alert.ReferenceId);

                alert.IsActive = false;

                alerts[index] = alert;

                return alert;
            });
        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo=>repo.GetAlertByUserLastAlertId(It.IsAny<int>() )).ReturnsAsync((int i) => alerts.Where(x=>x.Id == i).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertListByStartOfWeekRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(alerts);

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertListFilterByDateRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertListFilterByDate(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(alerts);

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertByInfraObjectIdRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertByInfraObjectId(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => alerts.Where(x => x.InfraObjectId == i && x.EntityId == j).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetLastAlertDetailRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => alerts.SingleOrDefault(x => x.ReferenceId == i));

        alertRepository.Setup(repo => repo.GetAlertByUserLastAlertId( It.IsAny<int>())).ReturnsAsync((int i) => alerts.Where(x => x.Id == i).ToList());

        alertRepository.Setup(repo => repo.GetAlertByUserLastInfraObjectId(It.IsAny<string>(), It.IsAny<DateTime>())).ReturnsAsync((string i, DateTime j) => alerts.Where(x => x.InfraObjectId == i && x.CreatedDate == j).ToList());

        return alertRepository;
    }

    public static Mock<IAlertRepository> GetAlertEmptyRepository()
    {
        var alertEmptyRepository = new Mock<IAlertRepository>();

        alertEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<Alert>());

        return alertEmptyRepository;
    }

    public static Mock<IAlertRepository> GetPaginatedAlertRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<AlertFilterSpecification>(),
                It.IsAny<Expression<Func<Alert, bool>>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, AlertFilterSpecification spec, Expression<Func<Alert, bool>> filter, string sortColumn, string sortOrder) =>
            {
                var filteredAlerts = alerts.AsQueryable();
                
                if (filter != null)
                {
                    filteredAlerts = filteredAlerts.Where(filter);
                }

                if (spec?.Criteria != null)
                {
                    filteredAlerts = filteredAlerts.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    sortColumn = sortColumn.ToLowerInvariant();

                    filteredAlerts = sortOrder?.ToLowerInvariant() == "desc"
                        ? sortColumn switch
                        {
                            "severity" => filteredAlerts.OrderByDescending(a => a.Severity),
                            "type" => filteredAlerts.OrderByDescending(a => a.Type),
                            "jobname" => filteredAlerts.OrderByDescending(a => a.JobName),
                            _ => filteredAlerts.OrderByDescending(a => a.Id)
                        }
                        : sortColumn switch
                        {
                            "severity" => filteredAlerts.OrderBy(a => a.Severity),
                            "type" => filteredAlerts.OrderBy(a => a.Type),
                            "jobname" => filteredAlerts.OrderBy(a => a.JobName),
                            _ => filteredAlerts.OrderBy(a => a.Id)
                        };
                }

                var totalCount = filteredAlerts.Count();

                var paginated = filteredAlerts
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                var paginatedResult = PaginatedResult<Alert>.Success(paginated, totalCount, pageNumber, pageSize);

                var severityCounts = paginated
                    .GroupBy(a => a.Severity)
                    .ToDictionary(g => g.Key, g => g.Count());

                return (paginatedResult, severityCounts);
            });

        return alertRepository;       
    }

    public static Mock<IAlertRepository> GetAlertByClientAlertIdRepository(List<Alert> alerts)
    {
        var alertRepository = new Mock<IAlertRepository>();

        alertRepository.Setup(repo => repo.GetAlertByClientAlertId(It.IsAny<string>())).ReturnsAsync(alerts);

        return alertRepository;
    }


}