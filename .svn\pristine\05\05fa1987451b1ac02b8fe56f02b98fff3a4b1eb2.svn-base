﻿namespace ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetByFormTypeId;

public class
    GetFormTypeCategoryByFormTypeIdQueryHandler : IRequestHandler<GetFormTypeCategoryByFormTypeIdQuery,
        FormTypeCategoryByFormTypeIdVm>
{
    private readonly IFormRepository _formRepository;
    private readonly IFormTypeCategoryRepository _formTypeCategoryRepository;
    private readonly IMapper _mapper;

    public GetFormTypeCategoryByFormTypeIdQueryHandler(IFormTypeCategoryRepository formTypeCategoryRepository,
        IFormRepository formRepository, IMapper mapper)
    {
        _formTypeCategoryRepository = formTypeCategoryRepository;
        _formRepository = formRepository;
        _mapper = mapper;
    }

    public async Task<FormTypeCategoryByFormTypeIdVm> Handle(GetFormTypeCategoryByFormTypeIdQuery request,
        CancellationToken cancellationToken)
    {
        Domain.Entities.FormTypeCategory formCategory;

        formCategory = request.Version.IsNullOrEmpty()
            ? await _formTypeCategoryRepository.GetFormTypeCategoryByFormTypeId(request.FormTypeId)
            : await _formTypeCategoryRepository.GetFormTypeCategoryByFormTypeIdAndVersion(request.FormTypeId,
                request.Version);

        Guard.Against.NullOrDeactive(formCategory, nameof(Domain.Entities.FormTypeCategory),
            new NotFoundException(nameof(Domain.Entities.FormTypeCategory), request.FormTypeId));

        var forms = await _formRepository.GetByReferenceIdAsync(formCategory.FormId);

        Guard.Against.NullOrDeactive(forms, nameof(Domain.Entities.Form),
            new NotFoundException(nameof(Domain.Entities.Form), formCategory.FormId));

        formCategory.Properties = forms.Properties;

        return _mapper.Map<FormTypeCategoryByFormTypeIdVm>(formCategory);
    }
}