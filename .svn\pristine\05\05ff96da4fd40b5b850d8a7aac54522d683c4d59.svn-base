using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class RoboCopyProfile : Profile
{
    public RoboCopyProfile()
    {
        CreateMap<RoboCopy, RoboCopyListVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<RoboCopy, RoboCopyDetailVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<RoboCopy, CreateRoboCopyCommand>().ReverseMap();
        CreateMap<RoboCopy, RoboCopyViewModel>().ReverseMap();

        CreateMap<CreateRoboCopyCommand, RoboCopyViewModel>().ReverseMap();
        CreateMap<UpdateRoboCopyCommand, RoboCopyViewModel>().ReverseMap();

        CreateMap<UpdateRoboCopyCommand, RoboCopy>().ForMember(x => x.Id, y => y.Ignore());
        CreateMap<PaginatedResult<RoboCopy>,PaginatedResult<RoboCopyListVm>>()
              .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));

    }
}