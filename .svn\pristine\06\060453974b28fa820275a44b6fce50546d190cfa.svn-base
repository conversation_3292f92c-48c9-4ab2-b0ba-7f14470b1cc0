using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class CyberJobManagementFilterSpecification : Specification<CyberJobManagement>
{
    public CyberJobManagementFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.Name != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("airgap=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.AirgapName.Contains(stringItem.Replace("airgap=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("workflow=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.WorkflowName.Contains(stringItem.Replace("workflow=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("solution=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.SolutionName.Contains(stringItem.Replace("solution=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.AirgapName.Contains(searchString) ||
                    p.WorkflowName.Contains(searchString) || p.SolutionName.Contains(searchString);
            }
        }
    }
}