﻿using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.ApprovalMatrixUsersModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrixUsers.Queries;
public class GetApprovalMatrixUsersPaginatedListQueryHandlerTests
{
    private readonly Mock<IApprovalMatrixUsersRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetApprovalMatrixUsersPaginatedListQueryHandler _handler;

    public GetApprovalMatrixUsersPaginatedListQueryHandlerTests()
    {
        _repositoryMock = new Mock<IApprovalMatrixUsersRepository>();
        _mapperMock = new Mock<IMapper>();
        _handler = new GetApprovalMatrixUsersPaginatedListQueryHandler(_mapperMock.Object, _repositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnData_WhenTypeIsProvided()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            Type = "Admin",
            PageNumber = 1,
            PageSize = 10,
            SortColumn = "UserName",
            SortOrder = "asc"
        };
        var domainResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(
            new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);

        var viewModelResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(
            new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.GetApprovalMatrixUserByType(
                query.Type, query.PageNumber, query.PageSize,
                It.IsAny<ApprovalMatrixUsersFilterSpecification>(),
                query.SortColumn, query.SortOrder))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(domainResult))
            .Returns(viewModelResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Should().NotBeNull();
        result.Should().BeSameAs(viewModelResult);
    }
    [Fact]
    public async Task Handle_ShouldReturnData_WhenTypeIsNull()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            Type = null,
            PageNumber = 1,
            PageSize = 10,
            SortColumn = "UserName",
            SortOrder = "desc"
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
            query.PageNumber, query.PageSize,
            It.IsAny<ApprovalMatrixUsersFilterSpecification>(),
            query.SortColumn, query.SortOrder)).ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult))
            .Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Should().BeEquivalentTo(vmResult);
    }
    [Fact]
    public async Task Handle_ShouldWork_WhenSearchStringIsEmpty()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            SearchString = "",
            PageNumber = 1,
            PageSize = 10
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                It.IsAny<int>(), It.IsAny<int>(), It.IsAny<ApprovalMatrixUsersFilterSpecification>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult))
            .Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Should().NotBeNull();
    }
    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsNothing()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            Type = null,
            PageNumber = 1,
            PageSize = 10
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
            query.PageNumber, query.PageSize,
            It.IsAny<ApprovalMatrixUsersFilterSpecification>(),
            query.SortColumn, query.SortOrder)).ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult)).Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.TotalCount.Should().Be(0);
    }
    [Fact]
    public async Task Handle_ShouldCallMapperOnce()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery();

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(It.IsAny<int>(), It.IsAny<int>(),
                It.IsAny<ApprovalMatrixUsersFilterSpecification>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult)).Returns(vmResult);

        await _handler.Handle(query, CancellationToken.None);

        _mapperMock.Verify(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldCallPaginatedListAll_WhenTypeIsNull()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            Type = null,
            PageNumber = 2,
            PageSize = 5,
            SortColumn = "UserId",
            SortOrder = "desc"
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 2, 5);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 2, 5);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                2, 5, It.IsAny<ApprovalMatrixUsersFilterSpecification>(), "UserId", "desc"))
            .ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult)).Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

      //  result.PageNumber.Should().Be(2);
        result.PageSize.Should().Be(5);
    }
    [Fact]
    public async Task Handle_ShouldNotFail_WhenSortColumnIsNull()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            SortColumn = null,
            SortOrder = "asc",
            PageNumber = 1,
            PageSize = 10
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                1, 10, It.IsAny<ApprovalMatrixUsersFilterSpecification>(), null, "asc"))
            .ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult)).Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Should().NotBeNull();
    }
    [Fact]
    public async Task Handle_ShouldNotFail_WhenSortOrderIsNull()
    {
        var query = new GetApprovalMatrixUsersPaginatedListQuery
        {
            SortColumn = "UserName",
            SortOrder = null,
            PageNumber = 1,
            PageSize = 10
        };

        var entityResult = PaginatedResult<Domain.Entities.ApprovalMatrixUsers>.Success(new List<Domain.Entities.ApprovalMatrixUsers>(), 0, 1, 10);
        var vmResult = PaginatedResult<ApprovalMatrixUsersListVm>.Success(new List<ApprovalMatrixUsersListVm>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                1, 10, It.IsAny<ApprovalMatrixUsersFilterSpecification>(), "UserName", null))
            .ReturnsAsync(entityResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<ApprovalMatrixUsersListVm>>(entityResult)).Returns(vmResult);

        var result = await _handler.Handle(query, CancellationToken.None);

        result.Should().NotBeNull();
    }

}
