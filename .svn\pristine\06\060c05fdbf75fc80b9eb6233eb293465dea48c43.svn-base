using ContinuityPatrol.Application.Features.DynamicDashboard.Events.Delete;

namespace ContinuityPatrol.Application.Features.DynamicDashboard.Commands.Delete;

public class
    DeleteDynamicDashboardCommandHandler : IRequestHandler<DeleteDynamicDashboardCommand,
        DeleteDynamicDashboardResponse>
{
    private readonly IDynamicDashboardRepository _dynamicDashboardRepository;
    private readonly IPublisher _publisher;

    public DeleteDynamicDashboardCommandHandler(IDynamicDashboardRepository dynamicDashboardRepository,
        IPublisher publisher)
    {
        _dynamicDashboardRepository = dynamicDashboardRepository;

        _publisher = publisher;
    }

    public async Task<DeleteDynamicDashboardResponse> Handle(DeleteDynamicDashboardCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _dynamicDashboardRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DynamicDashboard),
            new NotFoundException(nameof(Domain.Entities.DynamicDashboard), request.Id));

        eventToDelete.IsActive = false;

        await _dynamicDashboardRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDynamicDashboardResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DynamicDashboard), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DynamicDashboardDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}