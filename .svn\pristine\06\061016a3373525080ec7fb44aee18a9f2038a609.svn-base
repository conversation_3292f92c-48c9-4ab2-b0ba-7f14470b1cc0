using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

/// <summary>
/// Fixture class for CyberAlert module testing
/// Provides test data setup and AutoFixture configuration for comprehensive unit testing
/// Purpose: CyberAlert manages security alerts and notifications for cyber threats and incidents
/// </summary>
public class CyberAlertFixture : IDisposable
{
    public List<CyberAlert> CyberAlerts { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateCyberAlertCommand CreateCyberAlertCommand { get; set; }
    public UpdateCyberAlertCommand UpdateCyberAlertCommand { get; set; }
    public DeleteCyberAlertCommand DeleteCyberAlertCommand { get; set; }
    
    public IMapper Mapper { get; set; }

    public CyberAlertFixture()
    {
        // Initialize manual test data with known values for reliable testing
        CyberAlerts = new List<CyberAlert>
        {
            new()
            {
                ReferenceId = Guid.NewGuid().ToString(),
               
                Severity = "High",
                
                Type = "Intrusion",
               
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalAlerts = AutoCyberAlertFixture.CreateMany<CyberAlert>(2).ToList();
            CyberAlerts.AddRange(additionalAlerts);
            
            UserActivities = AutoCyberAlertFixture.CreateMany<UserActivity>(3).ToList();
            CreateCyberAlertCommand = AutoCyberAlertFixture.Create<CreateCyberAlertCommand>();
            UpdateCyberAlertCommand = AutoCyberAlertFixture.Create<UpdateCyberAlertCommand>();
            DeleteCyberAlertCommand = AutoCyberAlertFixture.Create<DeleteCyberAlertCommand>();
           
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateCyberAlertCommand = new CreateCyberAlertCommand();
            UpdateCyberAlertCommand = new UpdateCyberAlertCommand();
            DeleteCyberAlertCommand = new DeleteCyberAlertCommand();
          
        }

        // Configure AutoMapper for CyberAlert mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<CyberAlertProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }
    public Fixture AutoCyberAlertFixture
    {
        get
        {
            var fixture = new Fixture();
            
            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations for commands
           // fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateCyberAlertCommand>(p => p.Title, 200));
            fixture.Customize<CreateCyberAlertCommand>(c => c
                
                );

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateCyberAlertCommand>(p => p.Title, 200));
            fixture.Customize<UpdateCyberAlertCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<DeleteCyberAlertCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            // CyberAlert entity customizations
            fixture.Customize<CyberAlert>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true));

            // UserActivity customization for CyberAlert
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "CyberAlert")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test cyber alert activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
