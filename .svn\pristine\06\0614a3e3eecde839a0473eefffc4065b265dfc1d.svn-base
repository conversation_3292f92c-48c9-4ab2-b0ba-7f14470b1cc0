﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;

public class CreateWorkflowOperationCommandValidator : AbstractValidator<CreateWorkflowOperationCommand>
{
    private readonly List<string> _allowedRunMode = new() { "Dry Run", "Execution" };
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public CreateWorkflowOperationCommandValidator(IWorkflowOperationRepository workflowOperationRepository)
    {
        _workflowOperationRepository = workflowOperationRepository;

        RuleFor(p => p.ProfileName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 200).WithMessage("{PropertyName} should contain between 3 to 200 characters.");

        //RuleFor(p => p.Description)
        //  .MaximumLength(250).WithMessage("InfraObject {PropertyName} Maximum 250 characters.")
        //  .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
        //  .WithMessage("{PropertyName} contains invalid characters.")
        //  .When(p => p.Description.IsNotNullOrWhiteSpace());

        RuleFor(p => p.RunMode)
            .NotEmpty().WithMessage("Select {PropertyName}")
            .Matches(@"^[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]$").WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Must(value => value != null && _allowedRunMode.Any(mode =>
                string.Equals(mode.Replace(" ", "").ToLower(), value.Replace(" ", "").ToLower())))
            .WithMessage("{PropertyName} is invalid.");

        RuleFor(p => p.ProfileId)
            .NotEmpty().WithMessage("ProfileId is required.")
            .Must(value => Guid.TryParse(value, out _)).WithMessage("ProfileId must be a valid GUID.");
    }
}