﻿using ContinuityPatrol.Application.Features.DataSet.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSet.Queries;

public class GetDataSetDetailQueryHandlerTests : IClassFixture<DataSetFixture>
{
    private readonly DataSetFixture _dataSetFixture;

    private readonly Mock<IDataSetRepository> _mockDataSetRepository;

    private readonly GetDataSetDetailQueryHandler _handler;

    public GetDataSetDetailQueryHandlerTests(DataSetFixture dataSetFixture)
    {
        _dataSetFixture = dataSetFixture;

        _mockDataSetRepository = DataSetRepositoryMocks.GetDataSetRepository(_dataSetFixture.DataSets);

        _handler = new GetDataSetDetailQueryHandler(_dataSetFixture.Mapper, _mockDataSetRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_DataSetDetails_When_Valid()
    {
        var result = await _handler.Handle(new GetDataSetDetailQuery { Id = _dataSetFixture.DataSets[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<DataSetDetailVm>();
        result.Id.ShouldBe(_dataSetFixture.DataSets[0].ReferenceId);
        result.DataSetName.ShouldBe(_dataSetFixture.DataSets[0].DataSetName);
        result.Description.ShouldBe(_dataSetFixture.DataSets[0].Description);
        result.StoredQuery.ShouldBe(_dataSetFixture.DataSets[0].StoredQuery);
        result.PrimaryTableName.ShouldBe(_dataSetFixture.DataSets[0].PrimaryTableName);
        result.PrimaryTablePKColumn.ShouldBe(_dataSetFixture.DataSets[0].PrimaryTablePKColumn);
        result.QueryType.ShouldBe(_dataSetFixture.DataSets[0].QueryType);
        result.StoredProcedureName.ShouldBe(_dataSetFixture.DataSets[0].StoredProcedureName);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidDataSetId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetDataSetDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetDataSetDetailQuery { Id = _dataSetFixture.DataSets[0].ReferenceId }, CancellationToken.None);

        _mockDataSetRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}