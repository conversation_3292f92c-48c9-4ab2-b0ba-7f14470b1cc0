﻿//using ContinuityPatrol.Application.Features.HeatMapLog.Queries.GetType;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;

//namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapLog.Queries;

//public class GetHeatMapLogTypeQueryHandlerTests : IClassFixture<HeatMapLogFixture>
//{
//    private readonly HeatMapLogFixture _heatMapLogFixture;

//    private Mock<IHeatMapLogRepository> _heatMapLogRepositoryMock;

//    private readonly GetHeatMapLogTypeQueryHandler _handler;

//    public GetHeatMapLogTypeQueryHandlerTests(HeatMapLogFixture heatMapLogFixture)
//    {
//        _heatMapLogFixture = heatMapLogFixture;
    
//        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.GetHeatMapLogTypeRepository(_heatMapLogFixture.HeatMapLogs);
        
//        _handler = new GetHeatMapLogTypeQueryHandler(_heatMapLogFixture.Mapper, _heatMapLogRepositoryMock.Object);
//    }

//    [Fact]
//    public async Task Handle_Return_Valid_HeatMapLogType()
//    {
//        var result = await _handler.Handle(new GetHeatMapLogTypeQuery { Type = _heatMapLogFixture.HeatMapLogs[0].HeatmapType }, CancellationToken.None);
        
//        result.ShouldBeOfType<List<HeatMapLogTypeVm>>();

//        result[0].Id.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].ReferenceId);
//        result[0].BusinessServiceId.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessServiceId);
//        result[0].BusinessServiceName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessServiceName);
//        result[0].BusinessFunctionId.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessFunctionId);
//        result[0].BusinessFunctionName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].BusinessFunctionName);
//        result[0].InfraObjectId.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].InfraObjectId);
//        result[0].InfraObjectName.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].InfraObjectName);
//        result[0].EntityId.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].EntityId);
//        result[0].HeatmapType.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].HeatmapType);
//        result[0].HeatmapStatus.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].HeatmapStatus);
//        result[0].IsAffected.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].IsAffected);
//        result[0].ErrorMessage.ShouldBe(_heatMapLogFixture.HeatMapLogs[0].ErrorMessage);
//    }

//    [Fact]
//    public async Task Handle_ReturnEmptyType_When_NoRecords()
//    {
//        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.GetHeatMapLogEmptyRepository();

//        var handler = new GetHeatMapLogTypeQueryHandler(_heatMapLogFixture.Mapper, _heatMapLogRepositoryMock.Object);

//        var result = await handler.Handle(new GetHeatMapLogTypeQuery { Type = _heatMapLogFixture.HeatMapLogs[0].HeatmapType }, CancellationToken.None);
        
//        result.Count.ShouldBe(0);
//    }

//    [Fact]
//    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
//    {
//        await _handler.Handle(new GetHeatMapLogTypeQuery { Type = _heatMapLogFixture.HeatMapLogs[0].HeatmapType }, CancellationToken.None);

//        _heatMapLogRepositoryMock.Verify(x => x.GetHeatMapLogType(It.IsAny<string>()), Times.Once);
//    }
//}