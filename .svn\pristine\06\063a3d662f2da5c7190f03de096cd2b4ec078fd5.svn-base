﻿using ContinuityPatrol.Application.Features.DynamicSubDashboard.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DynamicSubDashboardModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicSubDashboard.Queries
{
    public class GetDynamicSubDashboardPaginatedListQueryHandlerTests
    {
        private readonly Mock<IDynamicSubDashboardRepository> _repositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetDynamicSubDashboardPaginatedListQueryHandler _handler;

        public GetDynamicSubDashboardPaginatedListQueryHandlerTests()
        {
            _repositoryMock = DynamicSubDashboardRepositoryMocks.GetPaginatedDynamicSubDashboardRepository();
            _mapperMock = new Mock<IMapper>();

            var paginatedEntityResult = new PaginatedResult<Domain.Entities.DynamicSubDashboard>
            {
                Data = new List<Domain.Entities.DynamicSubDashboard>
                {
                    new Domain.Entities.DynamicSubDashboard { Id = 1, ReferenceId = "REF-123", Name = "SubDashboard 1" },
                    new Domain.Entities.DynamicSubDashboard { Id = 2, ReferenceId = "REF-456", Name = "SubDashboard 2" }
                },
                TotalCount = 2,
                PageSize = 10,
                Succeeded = true
            };

            var paginatedVmResult = new PaginatedResult<DynamicSubDashboardListVm>
            {
                Data = new List<DynamicSubDashboardListVm>
                {
                    new DynamicSubDashboardListVm { Name = "SubDashboard 1" },
                    new DynamicSubDashboardListVm { Name = "SubDashboard 2" }
                },
                TotalCount = 2,
                PageSize = 10,
                Succeeded = true
            };

            _mapperMock.Setup(m => m.Map<PaginatedResult<DynamicSubDashboardListVm>>(It.IsAny<PaginatedResult<Domain.Entities.DynamicSubDashboard>>()))
                       .Returns(paginatedVmResult);

            _handler = new GetDynamicSubDashboardPaginatedListQueryHandler(_mapperMock.Object, _repositoryMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedPaginatedResult()
        {
            // Arrange
            var query = new GetDynamicSubDashboardPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = string.Empty,
                SortColumn = "Name",
                SortOrder = "asc"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Data.Should().HaveCount(2);
            result.TotalCount.Should().Be(2);
            result.Succeeded.Should().BeTrue();

            _repositoryMock.Verify(repo => repo.PaginatedListAllAsync(
                query.PageNumber, query.PageSize,
                It.IsAny<DynamicSubDashboardFilterSpecification>(),
                query.SortColumn, query.SortOrder), Times.Once);

            _mapperMock.Verify(m => m.Map<PaginatedResult<DynamicSubDashboardListVm>>(It.IsAny<PaginatedResult<Domain.Entities.DynamicSubDashboard>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnEmptyResult_WhenRepositoryReturnsEmptyList()
        {
            // Arrange
            var emptyResult = new PaginatedResult<Domain.Entities.DynamicSubDashboard>
            {
                Data = new List<Domain.Entities.DynamicSubDashboard>(),
                TotalCount = 0,
                PageSize = 10,
                Succeeded = true
            };

            _repositoryMock.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<DynamicSubDashboardFilterSpecification>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(emptyResult);

            _mapperMock.Setup(m => m.Map<PaginatedResult<DynamicSubDashboardListVm>>(emptyResult))
                       .Returns(new PaginatedResult<DynamicSubDashboardListVm>
                       {
                           Data = new List<DynamicSubDashboardListVm>(),
                           TotalCount = 0,
                           PageSize = 10,
                           Succeeded = true
                       });

            var query = new GetDynamicSubDashboardPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 10,
                SearchString = "",
                SortColumn = "Name",
                SortOrder = "asc"
            };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Data.Should().BeEmpty();
            result.TotalCount.Should().Be(0);
        }

        [Fact]
        public async Task Handle_ShouldUseDefaultSort_WhenSortColumnOrOrderMissing()
        {
            var query = new GetDynamicSubDashboardPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 5,
                SearchString = "sub",
                SortColumn = null,
                SortOrder = null
            };

            var result = await _handler.Handle(query, CancellationToken.None);

            result.Should().NotBeNull();
            result.Data.Should().HaveCountGreaterThan(0);
        }

        [Fact]
        public async Task Handle_ShouldThrow_WhenRepositoryThrowsException()
        {
            // Arrange
            _repositoryMock.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<DynamicSubDashboardFilterSpecification>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ThrowsAsync(new Exception("Database failure"));

            var query = new GetDynamicSubDashboardPaginatedListQuery
            {
                PageNumber = 1,
                PageSize = 5,
                SearchString = "",
                SortColumn = "Name",
                SortOrder = "asc"
            };

            // Act
            Func<Task> act = async () => await _handler.Handle(query, CancellationToken.None);

            // Assert
            await act.Should().ThrowAsync<Exception>().WithMessage("*Database failure*");
        }
    }
}
