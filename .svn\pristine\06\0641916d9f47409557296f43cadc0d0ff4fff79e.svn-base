﻿using ContinuityPatrol.Application.Features.TeamResource.Events.Delete;

namespace ContinuityPatrol.Application.Features.TeamResource.Commands.Delete;

public class DeleteTeamResourceCommandHandler : IRequestHandler<DeleteTeamResourceCommand, DeleteTeamResourceResponse>
{
    private readonly IPublisher _publisher;
    private readonly ITeamResourceRepository _teamResourceRepository;

    public DeleteTeamResourceCommandHandler(ITeamResourceRepository teamResourceRepository, IPublisher publisher)
    {
        _teamResourceRepository = teamResourceRepository;
        _publisher = publisher;
    }

    public async Task<DeleteTeamResourceResponse> Handle(DeleteTeamResourceCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _teamResourceRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.TeamResource),
            new NotFoundException(nameof(Domain.Entities.TeamResource), request.Id));

        eventToDelete.IsActive = false;

        await _teamResourceRepository.UpdateAsync(eventToDelete);

        var response = new DeleteTeamResourceResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.TeamResource), eventToDelete.TeamMasterName),

            IsActive = eventToDelete.IsActive
        };
        await _publisher.Publish(new TeamResourceDeletedEvent { TeamMasterName = eventToDelete.TeamMasterName },
            cancellationToken);

        return response;
    }
}