﻿using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.InfraSummaryEvents.Update;

public class DatabaseInfraSummaryUpdatedEventHandler : INotificationHandler<DatabaseInfraSummaryUpdatedEvent>
{
    private readonly IInfraSummaryRepository _infraSummaryRepository;
    private readonly ILogger<DatabaseInfraSummaryUpdatedEventHandler> _logger;

    public DatabaseInfraSummaryUpdatedEventHandler(ILogger<DatabaseInfraSummaryUpdatedEventHandler> logger,
        IInfraSummaryRepository infraSummaryRepository)
    {
        _logger = logger;
        _infraSummaryRepository = infraSummaryRepository;
    }

    public async Task Handle(DatabaseInfraSummaryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var infraSummaryPreviousDatabaseType =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                updatedEvent.PreviousDatabaseType, updatedEvent.PreviousBusinessServiceId, updatedEvent.CompanyId);

        var infraSummaryCurrentDbType =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

        if (infraSummaryCurrentDbType is null)
        {
            if (infraSummaryPreviousDatabaseType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousDatabaseType);

                await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                {
                    EntityName = Modules.Database.ToString(),
                    Type = updatedEvent.CurrentDatabaseType,
                    TypeId = updatedEvent.CurrentDatabaseTypeId,
                    Logo = updatedEvent.Logo,
                    Count = 1
                });
            }
            else
            {
                infraSummaryPreviousDatabaseType.Count -= 1;

                await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                {
                    EntityName = Modules.Database.ToString(),
                    Type = updatedEvent.CurrentDatabaseType,
                    TypeId = updatedEvent.CurrentDatabaseTypeId,
                    Logo = updatedEvent.Logo,
                    Count = 1
                });
            }
        }
        else if (!infraSummaryPreviousDatabaseType.Type.Equals(updatedEvent.CurrentDatabaseType) &&
                 infraSummaryCurrentDbType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentDbType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousDatabaseType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Database.ToString(),
                        Type = updatedEvent.CurrentDatabaseType,
                        TypeId = updatedEvent.CurrentDatabaseTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Database.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousDatabaseType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Database.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }
        else if (infraSummaryPreviousDatabaseType.Type.Equals(updatedEvent.CurrentDatabaseType) &&
                 !infraSummaryCurrentDbType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentDbType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousDatabaseType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Database.ToString(),
                        Type = updatedEvent.CurrentDatabaseType,
                        TypeId = updatedEvent.CurrentDatabaseTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Database.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousDatabaseType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Database.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }
        else if (infraSummaryPreviousDatabaseType.Type.Equals(updatedEvent.CurrentDatabaseType) &&
                 infraSummaryCurrentDbType.BusinessServiceId.Equals(updatedEvent.PreviousBusinessServiceId) &&
                 infraSummaryCurrentDbType.CompanyId.Equals(updatedEvent.CompanyId))
        {
            if (infraSummaryPreviousDatabaseType.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);

                if (infraSummary is null)
                {
                    await _infraSummaryRepository.AddAsync(new Domain.Entities.InfraSummary
                    {
                        EntityName = Modules.Database.ToString(),
                        Type = updatedEvent.CurrentDatabaseType,
                        TypeId = updatedEvent.CurrentDatabaseTypeId,
                        Logo = updatedEvent.Logo,
                        Count = 1,
                        BusinessServiceId = updatedEvent.BusinessServiceId,
                        CompanyId = updatedEvent.CompanyId
                    });
                }
                else
                {
                    infraSummary.EntityName = Modules.Database.ToString();
                    infraSummary.Logo = updatedEvent.Logo;
                    infraSummary.Count += 1;
                    infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                    infraSummary.CompanyId = updatedEvent.CompanyId;
                    await _infraSummaryRepository.UpdateAsync(infraSummary);
                }
            }
            else
            {
                infraSummaryPreviousDatabaseType.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummaryPreviousDatabaseType);

                var infraSummary = await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(
                    updatedEvent.CurrentDatabaseType, updatedEvent.BusinessServiceId, updatedEvent.CompanyId);
                infraSummary.EntityName = Modules.Database.ToString();
                infraSummary.Logo = updatedEvent.Logo;
                infraSummary.Count += 1;
                infraSummary.BusinessServiceId = updatedEvent.BusinessServiceId;
                infraSummary.CompanyId = updatedEvent.CompanyId;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }
        }


        _logger.LogInformation($"InfraSummary '{updatedEvent.CurrentDatabaseType}' updated successfully.");
    }
}