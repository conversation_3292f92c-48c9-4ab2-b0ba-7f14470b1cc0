﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Database.Events.PaginatedView;

public class DatabasePaginatedEventHandler : INotificationHandler<DatabasePaginatedEvent>
{
    private readonly ILogger<DatabasePaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DatabasePaginatedEventHandler(ILoggedInUserService userService,
        ILogger<DatabasePaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DatabasePaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.Database}",
            Entity = Modules.Database.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Database viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Database viewed");
    }
}