using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class ResiliencyReadyWorkflowScheduleLogRepositoryTests : IClassFixture<ResiliencyReadyWorkflowScheduleLogFixture>, IDisposable
{
    private readonly ResiliencyReadyWorkflowScheduleLogFixture _resiliencyReadyWorkflowScheduleLogFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly ResiliencyReadyWorkflowScheduleLogRepository _repository;

    public ResiliencyReadyWorkflowScheduleLogRepositoryTests(ResiliencyReadyWorkflowScheduleLogFixture resiliencyReadyWorkflowScheduleLogFixture)
    {
        _resiliencyReadyWorkflowScheduleLogFixture = resiliencyReadyWorkflowScheduleLogFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new ResiliencyReadyWorkflowScheduleLogRepository(_dbContext);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        var existingLogs = await _dbContext.Set<ResiliencyReadyWorkflowScheduleLog>().ToListAsync();
        _dbContext.Set<ResiliencyReadyWorkflowScheduleLog>().RemoveRange(existingLogs);
        await _dbContext.SaveChangesAsync();
    }

    #region GetResiliencyReadyWorkflowScheduleLogById Tests

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectTuple_WhenIdExists()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = 123,
            CurrentActionName = "TestAction",
            WorkflowId = "WORKFLOW_123",
            ScheduleTime = "2024-01-01 10:00:00",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(123, result.Item1);
        Assert.Equal("TestAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnDefaultTuple_WhenIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(Guid.NewGuid().ToString());

        // Assert
        Assert.Equal(0, result.Item1);
        Assert.Null(result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnFirstMatch_WhenMultipleExist()
    {
        // Arrange
        await ClearDatabase();
        var referenceId = Guid.NewGuid().ToString();
        var logs = new List<ResiliencyReadyWorkflowScheduleLog>
        {
            new() 
            { 
                ReferenceId = referenceId, 
                ConditionActionId = 100,
                CurrentActionName = "FirstAction",
                WorkflowId = "WORKFLOW_1",
                IsActive = true 
            },
            new() 
            { 
                ReferenceId = referenceId, 
                ConditionActionId = 200,
                CurrentActionName = "SecondAction",
                WorkflowId = "WORKFLOW_2",
                IsActive = true 
            }
        };

        foreach (var log in logs)
        {
            await _repository.AddAsync(log);
        }

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(referenceId);

        // Assert
        // Should return the first match (order may vary based on database implementation)
        Assert.True(result.Item1 == 100 || result.Item1 == 200);
        Assert.True(result.Item2 == "FirstAction" || result.Item2 == "SecondAction");
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldHandleNullReferenceId()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(null);

        // Assert
        Assert.Equal(0, result.Item1);
        Assert.Null(result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldHandleEmptyReferenceId()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(string.Empty);

        // Assert
        Assert.Equal(0, result.Item1);
        Assert.Null(result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WithNullCurrentActionName()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = 456,
            CurrentActionName = null,
            WorkflowId = "WORKFLOW_456",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(456, result.Item1);
        Assert.Null(result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WithZeroConditionActionId()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = 0,
            CurrentActionName = "ZeroAction",
            WorkflowId = "WORKFLOW_ZERO",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(0, result.Item1);
        Assert.Equal("ZeroAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WithNegativeConditionActionId()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = -100,
            CurrentActionName = "NegativeAction",
            WorkflowId = "WORKFLOW_NEGATIVE",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(-100, result.Item1);
        Assert.Equal("NegativeAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldHandleSpecialCharactersInCurrentActionName()
    {
        // Arrange
        await ClearDatabase();
        var specialActionName = "Action_@#$%^&*()_+{}|:<>?[]\\;',./";
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = 789,
            CurrentActionName = specialActionName,
            WorkflowId = "WORKFLOW_SPECIAL",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(789, result.Item1);
        Assert.Equal(specialActionName, result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldHandleLongCurrentActionName()
    {
        // Arrange
        await ClearDatabase();
        var longActionName = new string('A', 1000); // 1000 character string
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = 999,
            CurrentActionName = longActionName,
            WorkflowId = "WORKFLOW_LONG",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(999, result.Item1);
        Assert.Equal(longActionName, result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WithMaxIntConditionActionId()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = int.MaxValue,
            CurrentActionName = "MaxAction",
            WorkflowId = "WORKFLOW_MAX",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(int.MaxValue, result.Item1);
        Assert.Equal("MaxAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WithMinIntConditionActionId()
    {
        // Arrange
        await ClearDatabase();
        var log = new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = Guid.NewGuid().ToString(),
            ConditionActionId = int.MinValue,
            CurrentActionName = "MinAction",
            WorkflowId = "WORKFLOW_MIN",
            IsActive = true
        };

        await _repository.AddAsync(log);

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(log.ReferenceId);

        // Assert
        Assert.Equal(int.MinValue, result.Item1);
        Assert.Equal("MinAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var targetReferenceId = Guid.NewGuid().ToString();
        var logs = new List<ResiliencyReadyWorkflowScheduleLog>();

        // Create 100 logs with different reference IDs
        for (int i = 0; i < 100; i++)
        {
            logs.Add(new ResiliencyReadyWorkflowScheduleLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                ConditionActionId = i,
                CurrentActionName = $"Action_{i}",
                WorkflowId = $"WORKFLOW_{i}",
                IsActive = true
            });
        }

        // Add the target log
        logs.Add(new ResiliencyReadyWorkflowScheduleLog
        {
            ReferenceId = targetReferenceId,
            ConditionActionId = 999,
            CurrentActionName = "TargetAction",
            WorkflowId = "TARGET_WORKFLOW",
            IsActive = true
        });

        foreach (var log in logs)
        {
            await _repository.AddAsync(log);
        }

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById(targetReferenceId);

        // Assert
        Assert.Equal(999, result.Item1);
        Assert.Equal("TargetAction", result.Item2);
    }

    [Fact]
    public async Task GetResiliencyReadyWorkflowScheduleLogById_ShouldReturnCorrectValues_WhenDatabaseIsEmpty()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetResiliencyReadyWorkflowScheduleLogById("ANY_ID");

        // Assert
        Assert.Equal(0, result.Item1);
        Assert.Null(result.Item2);
    }

    #endregion
}
