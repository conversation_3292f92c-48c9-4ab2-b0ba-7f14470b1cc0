using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class RtoRepository : BaseRepository<Rto>, IRtoRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public RtoRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService)
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }


    public async Task<Rto> GetRtoByBusinessServiceId(string businessServiceId)
    {
        return await SelectRto(_dbContext.Rtos
            .Active()
            .AsNoTracking())
            .FirstOrDefaultAsync(x => x.BusinessServiceId.Equals(businessServiceId));
    }

    private IQueryable<Rto> SelectRto(IQueryable<Rto> rtos)
    {
        return rtos.Select(x => new Rto
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            BusinessServiceId = x.BusinessServiceId,
            BusinessServiceName = x.BusinessServiceName,
            TotalBusinessFunction = x.TotalBusinessFunction,
            BFAvailable  =x.BFAvailable,
            BFImpact  = x.BFImpact,
            BFUnderRTO =x.BFUnderRTO,
            BFAboveRTO =x.BFAboveRTO,
            BFNotAvailable =x.BFNotAvailable,
            BFDrillNotExecuted = x.BFDrillNotExecuted,
            TotalInfraObject =x.TotalInfraObject,
            InfraAvailable = x.InfraAvailable,
            InfraImpact = x.InfraImpact,
            InfraUnderRTO = x.InfraUnderRTO,
            InfraAboveRTO = x.InfraAboveRTO,
            InfraNotAvailable = x.InfraNotAvailable,
            InfraDrillNotExecuted = x.InfraDrillNotExecuted
        });
    }


    //public Task<bool> IsNameExist(string name, string id)
    //{
    //    return Task.FromResult(!id.IsValidGuid()
    //        ? Entities.Any(e => e.Name.Equals(name))
    //        : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    //}
}