﻿@model ContinuityPatrol.Domain.ViewModels.LoadBalancerModel.LoadBalancerViewModel

@Html.AntiForgeryToken()

<div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
        <form>
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" alt="Delete Img" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p>
                    You want to delete the <span class="font-weight-bolder text-primary" id="deleteData"></span>
                    data?
                </p>
                <input asp-for="Id" type="hidden" id="textDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmDeleteButton">Yes</button>
            </div>
        </form>
    </div>
</div>
