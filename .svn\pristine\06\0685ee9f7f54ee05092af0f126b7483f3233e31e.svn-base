﻿namespace ContinuityPatrol.Application.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCore(this IServiceCollection services)
    {
        var executingAssembly = Assembly.GetExecutingAssembly();

        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(executingAssembly));

        services.AddAutoMapper(executingAssembly);

        services.AddValidatorsFromAssembly(executingAssembly);

        return services;
    }
}