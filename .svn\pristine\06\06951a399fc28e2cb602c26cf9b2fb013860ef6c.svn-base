﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Events.IsAttached;

public class AirGapAttachedEventHandler : INotificationHandler<AirGapAttachedEvent>
{
    private readonly ILogger<AirGapAttachedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public AirGapAttachedEventHandler(ILogger<AirGapAttachedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(AirGapAttachedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.CyberAirGap.ToString(),
            Action = $"{ActivityType.Update} {Modules.CyberAirGap}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails =
                $"AirGap '{updatedEvent.Name}' '{(updatedEvent.IsAttached ? "Attached" : "Detached")}'  successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"AirGap '{updatedEvent.Name}' '{(updatedEvent.IsAttached ? "Attached" : "Detached")}' successfully.");
    }
}