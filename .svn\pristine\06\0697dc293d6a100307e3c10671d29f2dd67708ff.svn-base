﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DashboardViewLogFilterSpecification : Specification<DashboardViewLog>
{
    public DashboardViewLogFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("businessServiceName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BusinessServiceName.Contains(stringItem.Replace("businessServiceName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("infraObjectName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.InfraObjectName.Contains(stringItem.Replace("infraObjectName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("businessFunctionName=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.BusinessFunctionName.Contains(stringItem.Replace("businessFunctionName=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("monitortype=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.MonitorType.Contains(stringItem.Replace("monitortype=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("state=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.State.Contains(stringItem.Replace("state=", "", StringComparison.OrdinalIgnoreCase)));
            }

            else
            {
                Criteria = p =>
                    p.BusinessServiceName.Contains(searchString) || p.InfraObjectName.Contains(searchString) ||
                    p.BusinessFunctionName.Contains(searchString) || p.MonitorType.Contains(searchString) ||
                    p.State.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.BusinessServiceName != null;
        }
    }
}