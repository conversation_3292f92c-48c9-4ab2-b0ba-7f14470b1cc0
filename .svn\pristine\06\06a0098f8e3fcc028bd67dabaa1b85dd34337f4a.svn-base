﻿using ContinuityPatrol.Application.Features.TeamResource.Events.Delete;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Events
{
    public class DeleteTeamResourceEventTests
    {
        private readonly Mock<ILogger<TeamResourceDeletedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamResourceDeletedEventHandler _handler;

        public DeleteTeamResourceEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamResourceDeletedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamResourceDeletedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var deletedEvent = new TeamResourceDeletedEvent { TeamMasterName = "TestTeamResource" };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-resource");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(deletedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-resource" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamResource.ToString() &&
                activity.Action == $"{ActivityType.Delete} {Modules.TeamResource}" &&
                activity.ActivityType == ActivityType.Delete.ToString() &&
                activity.ActivityDetails == " Team Resource 'TestTeamResource' deleted successfully."
            )), Times.Once);

        }

        
    }
}
