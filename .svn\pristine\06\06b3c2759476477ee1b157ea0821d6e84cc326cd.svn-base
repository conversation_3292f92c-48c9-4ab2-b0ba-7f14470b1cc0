﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.SmtpConfigurationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Queries;

public class GetSmtpConfigurationListQueryHandlerTests : IClassFixture<SmtpConfigurationFixture>
{
    private readonly SmtpConfigurationFixture _smtpConfigurationFixture;

    private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;

    private readonly GetSmtpConfigurationListQueryHandler _handler;

    public GetSmtpConfigurationListQueryHandlerTests(SmtpConfigurationFixture smtpConfigurationFixture)
    {
        _smtpConfigurationFixture = smtpConfigurationFixture;

        _mockSmtpConfigurationRepository = SmtpConfigurationRepositoryMocks.GetSmtpConfigurationRepository(_smtpConfigurationFixture.SmtpConfigurations);

        _handler = new GetSmtpConfigurationListQueryHandler(_smtpConfigurationFixture.Mapper, _mockSmtpConfigurationRepository.Object);
        
        _smtpConfigurationFixture.SmtpConfigurations[2].UserName = "rD7N1eVaZPFrr8tsoKJ8m7vKdUuASxHnnZ6ytacs+KU=$nbTPa0AMdFK/ccxYst2rkrlOt3xm3B9hFayBtPxE2zvaE5b5XnS+WbkA7jE=";
    }

    [Fact]
    public async Task Handle_Return_Valid_SmtpConfigurationsDetail()
    {
        var result = await _handler.Handle(new GetSmtpConfigurationListQuery(), CancellationToken.None);
        
        result.ShouldBeOfType<SmtpConfigurationListVm>();

        result.Id.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].ReferenceId);
        result.CompanyId.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].CompanyId);
        result.SmtpHost.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].SmtpHost);
        result.Port.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].Port);
        result.UserName.ShouldBe("<EMAIL>");
        result.Password.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].Password);
        result.EnableSSL.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].EnableSSL);
        result.IsBodyHTML.ShouldBe(_smtpConfigurationFixture.SmtpConfigurations[2].IsBodyHTML);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetSmtpConfigurationListQuery(), CancellationToken.None);

        _mockSmtpConfigurationRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}