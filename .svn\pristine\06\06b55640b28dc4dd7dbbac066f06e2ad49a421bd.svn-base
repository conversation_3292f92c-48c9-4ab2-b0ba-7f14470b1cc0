﻿@using ContinuityPatrol.Shared.Core.Domain
@using ContinuityPatrol.Shared.Core.Enums;
@using ContinuityPatrol.Shared.Core.Extensions
@using ContinuityPatrol.Shared.Services.Helper;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using ContinuityPatrol.Web.TagHelpers
@using Microsoft.Extensions.Configuration

@inject IConfiguration Configuration

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="UTF-8">
    <title>Continuity Patrol</title>
    <link rel="shortcut icon" href="~/img/logo/CP_fav.svg">
    
    <link rel="stylesheet" href="~/bundles/css/main.bundle.min.css" asp-append-version="true" />
    
    <link href="~/css/Style.css" rel="stylesheet" />
    <link href="~/fonts/cp-icons/cp-icon.css" rel="stylesheet" />
    <link href="~/lib/bootstrap/css/bootstrap-icons.min.css" rel="stylesheet" />

    <link href="~/lib/datatables/css/datatables.bootstrap5.min.css" rel="stylesheet" />
    <link href="~/lib/bootstrap-select/css/select2.min.css" rel="stylesheet" />
    <link href="~/css/theme.css" rel="stylesheet" />
    <link href="~/fonts/San-Francisco-Pro/SFPro-Font.css" rel="stylesheet" />
    <link href="~/css/Table.css" rel="stylesheet" />
    <link href="~/css/form.css" rel="stylesheet" />
    <link href="~/css/highlight.css" rel="stylesheet" />

    @* Chatbot Style *@
    <link href="~/css/chatbot.css" rel="stylesheet" />
    <link href="~/lib/selectize.js/css/selectize.min.css" rel="stylesheet" />

    <link href="~/lib/datatables/css/rowreorder.datatables.min.css" rel="stylesheet" />

    <script src="~/bundles/js/main.bundle.min.js" asp-append-version="true"></script>
    <style>
        #licenseToast .toast-body {
            max-height: 500px;
            overflow-y: auto;
        }

        #draggable {
            width: 10px;
            bottom: 10px;
            height: 10px;
            position: absolute;
            cursor: pointer;
            right: 0%;
        }
    </style>

</head>
<body class="vh-100 position-relative" id="container">
    <mini-profiler />


    @if (WebHelper.UserSession.RoleName == UserRole.SiteAdmin.ToString())
    {
        <partial name="_SiteAdminLayout" />
    }
    else
    {
        <partial name="_NavBarPartial" />
    }

    <div class="d-flex">
        @{
            var message = TempData.Get<NotificationMessage>();

            @if (message is not null)
            {
                <notification class-name=@message.NotificationType.ToLower() message=@message.Message></notification>
                <script>
                    $(document).ready(function () {
                        $('#mytoastr').toast({ delay: 3000 });
                        $('#mytoastr').toast('show');
                    });
                </script>
                TempData.Remove("Message");
            }
        }

        @RenderBody()
        @await RenderSectionAsync("Scripts", required: false)
        @await RenderSectionAsync("HeaderScripts", required: false)
        @await RenderSectionAsync("Styles", required: false)
    </div>
    <footer class="text-center fixed-bottom p-1">

        @{

            var isCoe = Configuration.GetValue<string>("Release:isCOE");
        }

        @if (@isCoe != null)
        {
            <small>Continuity Patrol Version <span>@WebHelper.UserSession.CpVersion</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
        }
        else
        {
            <small>Continuity Patrol Version <span>@WebHelper.UserSession.CpVersion</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
        }
    </footer>

    <div id="draggable">
        <div class="Chatbot" id="Chat_Bot" chatBoturl="@WebHelper.UserSession.ChatBotRUrl" signalRurl="@WebHelper.UserSession.SignalRUrl">
            <div class="list-container active">
                <button class="more-button btnChatBotIcon" aria-label="Menu Button" onclick="chatGeneration(event)">
                    <i class="cp-chat-bot fs-4 "></i>
                </button>
                <div class="card more-button-list" id="chatContainerDiv" style="display:none;">
                    <div class="card-header header chatheader border-bottom-0 h6 text-white p-2">
                        <div class="d-flex align-items-center" id="botHeaderImage">
                            <img src="~/img/Profile-img/lisa.png" class="me-2" alt="Lisa BOT" title="Lisa BOT" />
                            <div class="d-grid fw-normal">
                                <small>Chat with</small>
                                <span>Lisa</span>
                            </div>
                        </div>
                        <div data-bs-theme="dark">
                            <i id="btnChatContainerMinimize" type="button" class="cp-subtract fs-6 me-3"></i>
                            <button id="btnChatContainerClose" type="button" class="btn-close fs-7" aria-label="Close"></button>
                        </div>
                    </div>
                    <div class="card-body" style="max-height: 450px; overflow-y:auto;" id="parentChatContainer">
                        <ul class="p-0 mb-0 d-flex flex-column" id="chatBotContainer">
                        </ul>
                    </div>
                    <div class="card-footer bg-white border-top-0">
                        <div class="input-group Chat-Input border-0 gap-2">
                            <input type="text" class="form-control" id="chatBotInput" placeholder="Ask me something" />
                            <span role="button" type="submit" class="input-group-addon" id="chatBotSubmitBtn">
                                <i class="cp-send-message ms-1"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @*<div class="toast-container position-fixed bottom-0 end-0 p-2 mb-5">
    <div id="licenseToast" class="toast border-0" role="alert" style="width:346px;background-color:#f1f7ff">
    <div class="toast-body position-relative">
    <button type="button" id="btntoaster" class="btn-close btn-close position-absolute opacity-100" data-userName="@WebHelper.UserSession.LoginName" data-bs-dismiss="toast" aria-label="close" style="top:10px;right:5px;height:4px"></button>
    <div class="d-flex gap-2">
    <div class="rounded-circle mt-2" style="width:50px;height:50px"><img src="~/img/logo/rounded-logo.svg" class="img-fluid rounded-circle"></div>
    <div class="d-flex flex-column align-items-baseline">
    <div class="fs-7 mt-2 ms-3" id="licenseText">

    </div>
    </div>
    <div><img src="~/img/isomatric/toast-notification-vector.svg" class="img-fluid rounded-circle"></div>
    </div>

    </div>
    </div>
    </div> *@
    <div class="toast-container position-fixed bottom-0 end-0 p-2 mb-5">
        <div id="licenseToast" class="toast text-bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true" style="width:480px">
            <div class="toast-header d-flex align-items-center">
                <div class="fs-6 d-flex align-items-center gap-2">
                    <i class="cp-warning fs-5"></i> Your License will Expires !
                </div>
                <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="licenseText">
            </div>
        </div>
    </div>

    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="liveToast" class="toast bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true" style="width:396px">
            <div class="toast-body position-relative">
                <button type="button" id="btntoaster" class="btn-close btn-close-white position-absolute opacity-100" data-userName="@WebHelper.UserSession.LoginName" data-bs-dismiss="toast" aria-label="close" style="top:10px;right:5px;height:4px"></button>

                <div class="d-flex align-items-center gap-2">
                    <div class="rounded-circle" style="width:50px;height:50px"><img src="~/img/logo/rounded-logo.svg" class="rounded-circle" width="50"></div>
                    <div class="d-flex flex-column align-items-baseline" style="max-width:75%;">
                        <h6 class="text-white" style="word-wrap: break-word;word-break: break-all;">
                            Currently, drill is executing
                        </h6>
                    </div>
                    <div><img src="~/img/isomatric/toast-notification-vector.svg" class="rounded-circle" width="100"></div>
                </div>

            </div>
        </div>
    </div>



    @* Air-Gap On - Off *@
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="airGabToast" class="toast border-start border-0 border-5 border-danger" role="alert" aria-live="assertive" aria-atomic="true" style="width:396px">
            <div class="toast-body position-relative">
                <button type="button" id="btnAirGabtoaster" class="btn-close position-absolute opacity-100" data-userName="@WebHelper.UserSession.LoginName" data-bs-dismiss="toast" aria-label="close" style="top:10px;right:5px;height:4px"></button>

                <div class="d-flex align-items-center gap-2">
                    <div class="rounded-circle d-flex align-items-center justify-content-center" style="width:40px;height:40px">
                        <i class="cp-air-gap text-danger fs-2"></i>
                    </div>
                    <div class="d-flex flex-column align-items-baseline ms-2">
                        <h6>Airgap 2 is closed</h6>
                        @*   <small>Cyber Valut to Clean Room</small> *@
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (WebHelper.UserSession.RoleName == "SuperAdmin" || WebHelper.UserSession.RoleName == "Administrator" || ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.View)
    {
        <div class="toast-container position-fixed top-0 end-0 p-3 ">
            <div id="ConfigurationReq" class="toast bg-light border-0" role="alert" aria-live="assertive" aria-atomic="true" style="width:376px" data-bs-autohide="false">
                <div class="toast-body position-relative">
                    <button type="button" id="btntoaster_CR" class="btn-close position-absolute opacity-100" data-bs-dismiss="toast" aria-label="close" style="top:10px;right:10px;height:4px"></button>

                    <div class="d-flex align-items-center gap-2">
                        <div class="rounded-circle" style="width:50px;height:50px"><img src="~/img/logo/rounded-logo.svg" class="img-fluid rounded-circle"></div>
                        <div class="d-flex flex-column align-items-baseline mt-2">
                            <h6><span id="configurationText"></span> is not configured</h6>
                            <p class="text-primary">Click here to <a class="text-decoration-underline" role="button" id="redirect">configure</a></p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    }
    <div class="toast-container position-fixed top-0 end-0 p-3 ">
        <div id="timeliveToast" class="toast border-0 bg-white" role="alert" aria-live="assertive" aria-atomic="true" style="width:450px">
            <div class="toast-body position-relative">
                <button type="button" class="btn-close position-absolute" data-bs-dismiss="toast" aria-label="Close" style="top:10px;right:5px;height:4px"></button>
                <div class="d-flex align-items-center gap-2">
                    <div class="rounded-circle"><img src="~/img/input_Icons/timer-icon.svg" class="img-fluid rounded-circle" width="40"></div>
                    <div class="d-flex flex-column align-items-baseline"><h6 class="fw-bold mb-1">Screen Timing</h6> <span class="text-muted fs-7">Your screen will be close in</span></div>
                    <div>
                        <div class="clock d-flex gap-3">
                            <div class="d-flex">
                                <div>
                                    <div class="digit tenmin">
                                        <span class="base"></span>
                                        <div class="flap over front"></div>
                                        <div class="flap over back"></div>
                                        <div class="flap under"></div>
                                    </div>

                                    <div class="digit min">
                                        <span class="base"></span>
                                        <div class="flap over front"></div>
                                        <div class="flap over back"></div>
                                        <div class="flap under"></div>
                                    </div>
                                    <p class="mb-0">Minutes</p>
                                </div>
                            </div>

                            <div class="d-flex">
                                <div>
                                    <div class="digit tensec">
                                        <span class="base"></span>
                                        <div class="flap over front"></div>
                                        <div class="flap over back"></div>
                                        <div class="flap under"></div>
                                    </div>

                                    <div class="digit sec">
                                        <span class="base"></span>
                                        <div class="flap over front"></div>
                                        <div class="flap over back"></div>
                                        <div class="flap under"></div>
                                    </div>
                                    <p class="mb-0">Seconds</p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    @* Notification *@
    <div class='Notification'>
        <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
            <div class='d-flex'>
                <div class='toast-body'>
                    <span id="alertClass" class='success-toast'>
                        <i id="icon_Detail" class='cp-check toast_icon'></i>
                    </span>
                    <span id="notificationAlertmessage">

                    </span>
                </div>
                <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
            </div>
        </div>
    </div>


    @* Session Expiring Modal *@
    <div class="modal fade" id="sessionNotifyModal" tabindex=" -1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <form>
                    <div class="modal-header p-0 border-bottom-0">
                        <img class="delete-img" src="~/img/isomatric/session_time_out.svg" />
                    </div>
                    <div class="modal-body text-center pt-0">
                        <div class="d-flex flex-column gap-1">
                            <div class="fs-6 fw-semibold">Session Expiring!</div>
                            <div class="fs-7">Your session will expire in</div>
                            <div class="fw-bold fs-6"><i class="cp-time text-danger me-2 blinkingEffect" id="sessionExpiredIcon"></i><span id="timeRemainingText"></span></div>
                            <div class="fs-7">Would you like to continue this session ?</div>
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center border-top-0">
                        <button type="button" class="btn btn-secondary btn-sm" id="btnSessionExpireLogout">Logout</button>
                        <button type="submit" class="btn btn-primary btn-sm" data-bs-dismiss="modal" id="confirmStayLogedButton">Continue</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <script src="~/lib/bootstrap-select/js/select2.min.js"></script>
    <script src="~/lib/datatables/js/jquery.datatables.min.js"></script>
    <script src="~/lib/datatables/js/datatables.bootstrap5.min.js"></script>
    <script src="~/lib/jquery-ui/jquery-ui.min.js"></script>

    @* <script src="~/js/common/signalr.min.js"></script> *@
    <script src="~/lib/types.js/dist/typed.umd.js"></script>
    @* <script src="~/js/IT Automation/WorkflowConfiguration/CopyClipBoard.js"></script> *@
    <script src="~/lib/selectize.js/js/selectize.min.js"></script>
    @* <script src="~/js/IT Automation/WorkflowConfiguration/highlight.js"></script> *@
    <script src="~/lib/dompurify/dist/purify.min.js"></script>
    <script src="~/js/common/signalr.js"></script>
    <script src="~/js/common/common.js"></script>
    <script src="~/js/common/configurationrequired.js"></script>
    @* <script src="~/js/common/fliptimernotifiction.js"></script> *@
   

    <script src="~/lib/datatables/js/datatables.rowreorder.min.js"></script>
    <script src="~/lib/datatables/js/colresizable-1.6.min.js"></script>


    <script src="~/js/common/chatbot.js"></script>
   
    <script>
        var RootUrl = '@Url.Content("~/")';
    </script>
    <script>
        //for showing Time script
        function checkTime(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }

        function startTime() {
            var today = new Date();
            var month = today.getMonth() + 1;
            var date = today.getDate() + "/" + month + "/" + today.getFullYear() + " ";
            var h = today.getHours();
            var m = today.getMinutes();
            var s = today.getSeconds();
            // add a zero in front of numbers<10
            m = checkTime(m);
            s = checkTime(s);
            document.getElementById('time').innerHTML = date + "  " + h + ":" + m + ":" + s;
            t = setTimeout(function () {
                startTime()
            }, 500);
        }
        startTime();
    </script>
    <script>

        $(document).ready(function () {
            function disableBack() { window.history.forward() }

            window.onload = disableBack();
            window.onpageshow = function (evt) { if (evt.persisted) disableBack() }
        });

    </script>

    <script>
        $(document).ready(function () {
           
            var isDragging = false;
            var offsetX, offsetY;

            // Mouse Down Event to start dragging
            $('#draggable').on('mousedown', function (e) {
                isDragging = true;
                offsetX = e.clientX - $(this).offset().left;
                offsetY = e.clientY - $(this).offset().top;
                $(this).css('cursor', 'move');
            });

            // Mouse Move Event to drag the div
            $(document).on('mousemove', function (e) {
                if (isDragging) {
                    var left = e.clientX - offsetX;
                    var top = e.clientY - offsetY;

                    // Constrain movement inside container bounds
                    var containerOffset = $('#container').offset();
                    var containerWidth = $('#container').width();
                    var containerHeight = $('#container').height();

                    // Ensure the div stays within the container
                    left = Math.max(containerOffset.left, Math.min(left, containerOffset.left + containerWidth - $('#draggable').width()));
                    top = Math.max(containerOffset.top, Math.min(top, containerOffset.top + containerHeight - $('#draggable').height()));

                    $('#draggable').css({
                        left: left - containerOffset.left,
                        top: top - containerOffset.top
                    });
                }
            });

            // Mouse Up Event to stop dragging
            $(document).on('mouseup', function () {
                isDragging = false;
                $('#draggable').css('cursor', 'pointer');
            });
        });
    </script>
</body>
</html>
