﻿using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetNameUnique;

namespace ContinuityPatrol.Application.UnitTests.Features.RsyncOption.Queries;

public class GetRsyncOptionNameUniqueQueryHandlerTests
{
    private readonly Mock<IRsyncOptionRepository> _mockRsyncOptionRepository;
    private readonly GetRsyncOptionNameUniqueQueryHandler _handler;

    public GetRsyncOptionNameUniqueQueryHandlerTests()
    {
        _mockRsyncOptionRepository = new Mock<IRsyncOptionRepository>();
        _handler = new GetRsyncOptionNameUniqueQueryHandler(_mockRsyncOptionRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnsTrue_WhenNameExists()
    {
        var query = new GetRsyncOptionNameUniqueQuery { Name = "TestOption", Id = Guid.NewGuid().ToString() };
        _mockRsyncOptionRepository
            .Setup(repo => repo.IsNameExist(query.Name, query.Id))
            .ReturnsAsync(true);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.True(result);
        _mockRsyncOptionRepository.Verify(repo => repo.IsNameExist(query.Name, query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsFalse_WhenNameDoesNotExist()
    {
        var query = new GetRsyncOptionNameUniqueQuery { Name = "NonExistingOption", Id = Guid.NewGuid().ToString() };
        _mockRsyncOptionRepository
            .Setup(repo => repo.IsNameExist(query.Name, query.Id))
            .ReturnsAsync(false);

        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.False(result);
        _mockRsyncOptionRepository.Verify(repo => repo.IsNameExist(query.Name, query.Id), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowsException_WhenRepositoryThrowsException()
    {
        var query = new GetRsyncOptionNameUniqueQuery { Name = "TestOption", Id = Guid.NewGuid().ToString() };
        _mockRsyncOptionRepository
            .Setup(repo => repo.IsNameExist(query.Name, query.Id))
            .ThrowsAsync(new Exception("Repository error"));

        var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
        Assert.Equal("Repository error", exception.Message);
    }
}
