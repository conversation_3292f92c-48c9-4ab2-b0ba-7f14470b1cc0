﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Queries;

public class GetServerTypePaginatedListQueryHandlerTests : IClassFixture<ServerTypeFixture>
{
    private readonly ServerTypeFixture _serverTypeFixture;
    private readonly Mock<IMapper> _mockMapper;

    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;
    private readonly GetServerTypePaginatedListQueryHandler _handler;

    public GetServerTypePaginatedListQueryHandlerTests(ServerTypeFixture serverTypeFixture)
    {
        _mockMapper = new Mock<IMapper>();
        _serverTypeFixture = serverTypeFixture;

        _serverTypeFixture.ServerTypes[0].Name = "Server_Type";

        _serverTypeFixture.ServerTypes[1].Name = "Testing_Server";

        _mockServerTypeRepository = ServerTypeRepositoryMocks.GetPaginatedServerTypeRepository(_serverTypeFixture.ServerTypes);

        _handler = new GetServerTypePaginatedListQueryHandler(_serverTypeFixture.Mapper, _mockServerTypeRepository.Object);
    }


    [Fact]
    public async Task Handle_Return_PaginatedServerTypes_When_QueryStringMatch()
    {
        // Arrange
        var serverTypes = new List<Domain.Entities.ServerType>
    {
        new Domain.Entities.ServerType { ReferenceId = "1", Name = "Testing_Server" }
    };

        var paginatedServerTypes = new PaginatedResult<Domain.Entities.ServerType>
        {
            Data = serverTypes,
            TotalCount = serverTypes.Count,
            PageSize = 10,
            Succeeded = true
        };
        _mockServerTypeRepository
            .Setup(x => x.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<Domain.Entities.ServerType>>(), // <--- changed this line
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(paginatedServerTypes);


        var serverTypeListVm = new List<ServerTypeListVm>
    {
        new ServerTypeListVm { Id = "1", Name = "Testing_Server" }
    };

        var paginatedVmResult = new PaginatedResult<ServerTypeListVm>
        {
            Data = serverTypeListVm,
            TotalCount = 1,
            PageSize = 10,
            Succeeded = true
        };
        _mockMapper
            .Setup(x => x.Map<PaginatedResult<ServerTypeListVm>>(It.IsAny<PaginatedResult<Domain.Entities.ServerType>>()))
            .Returns(paginatedVmResult);

        var query = new GetServerTypePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Test"
        };

        var handler = new GetServerTypePaginatedListQueryHandler(_mockMapper.Object,_mockServerTypeRepository.Object );

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ServerTypeListVm>>();
        result.TotalCount.ShouldBe(1);
        result.Data[0].ShouldBeOfType<ServerTypeListVm>();
        result.Data[0].Name.ShouldBe("Testing_Server");
    }



}
