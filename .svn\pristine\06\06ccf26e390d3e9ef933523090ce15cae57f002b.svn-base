﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteLocation.Validators;

public class UpdateSiteLocationCommandValidatorTests
{
    private readonly Mock<ISiteLocationRepository> _siteLocationRepositoryMock;
    private readonly UpdateSiteLocationCommandValidator _validator;

    public UpdateSiteLocationCommandValidatorTests()
    {
        _siteLocationRepositoryMock = new Mock<ISiteLocationRepository>();
        _siteLocationRepositoryMock
            .Setup(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false); // Default: unique

        _validator = new UpdateSiteLocationCommandValidator(_siteLocationRepositoryMock.Object);
    }

    private UpdateSiteLocationCommand GetValidCommand() => new()
    {
        Id = Guid.NewGuid().ToString(),
        City = "Chennai",
        CityAscii = "Chennai",
        Country = "India",
        Lat = "13.0827",
        Lng = "80.2707"
    };

    [Fact]
    public async Task Should_Pass_For_Valid_Command()
    {
        var command = GetValidCommand();
        var result = await _validator.TestValidateAsync(command);
        result.IsValid.Should().BeTrue();
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("  ")]
    public async Task Should_Fail_When_City_Is_Empty_Or_Null(string city)
    {
        var command = GetValidCommand();
        command.City = city;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.City);
    }

    [Theory]
    [InlineData("A")]
    [InlineData("AB")]
    [InlineData("CityNameThatIsTooLongToBeAcceptedByTheSystemBecauseItHasOver100Characters_012345678901234567890123456789012345678901234567890")]
    public async Task Should_Fail_When_City_Length_Invalid(string city)
    {
        var command = GetValidCommand();
        command.City = city;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.City);
    }

    [Theory]
    [InlineData("123@abc")]
    [InlineData("$$$")]
    [InlineData("123")]
    public async Task Should_Fail_When_City_Has_Invalid_Format(string city)
    {
        var command = GetValidCommand();
        command.City = city;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.City);
    }

    [Fact]
    public async Task Should_Fail_When_City_Is_Not_Unique()
    {
        _siteLocationRepositoryMock
            .Setup(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(true); // duplicate exists

        var command = GetValidCommand();

        var result = await _validator.TestValidateAsync(command);
        result.IsValid.Should().BeFalse();
        result.Errors.Should().ContainSingle(x => x.ErrorMessage == "A same city already exists");
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Should_Fail_When_Lat_Is_Empty_Or_Null(string lat)
    {
        var command = GetValidCommand();
        command.Lat = lat;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Lat);
    }

    [Theory]
    [InlineData("abc")]
    [InlineData("12.12.12")]
    [InlineData("13..07")]
    public async Task Should_Fail_When_Lat_Invalid_Format(string lat)
    {
        var command = GetValidCommand();
        command.Lat = lat;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Lat);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task Should_Fail_When_Lng_Is_Empty_Or_Null(string lng)
    {
        var command = GetValidCommand();
        command.Lng = lng;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Lng);
    }

    [Theory]
    [InlineData("abc")]
    [InlineData("80.27.27.11")]
    public async Task Should_Fail_When_Lng_Invalid_Format(string lng)
    {
        var command = GetValidCommand();
        command.Lng = lng;

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Lng);
    }


    [Fact]
    public async Task Should_Fail_When_CityAscii_Invalid_Format()
    {
        var command = GetValidCommand();
        command.CityAscii = "123$";

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.CityAscii);
    }

    [Fact]
    public async Task Should_Fail_When_Country_Invalid_Format()
    {
        var command = GetValidCommand();
        command.Country = "##India";

        var result = await _validator.TestValidateAsync(command);
        result.ShouldHaveValidationErrorFor(c => c.Country);
    }
}
