﻿namespace ContinuityPatrol.Application.Features.WorkflowPrediction.Queries.GetNextPossibleId;

public class GetWorkflowPredictionListByNextPossibleIdQueryHandler : IRequestHandler<
    GetWorkflowPredictionListByNextPossibleIdQuery, List<WorkflowPredictionListByNextPossibleIdVm>>
{
    private readonly IMapper _mapper;
    private readonly IWorkflowPredictionRepository _workflowPredictionRepository;

    public GetWorkflowPredictionListByNextPossibleIdQueryHandler(IMapper mapper,
        IWorkflowPredictionRepository workflowPredictionRepository)
    {
        _mapper = mapper;
        _workflowPredictionRepository = workflowPredictionRepository;
    }

    public async Task<List<WorkflowPredictionListByNextPossibleIdVm>> Handle(
        GetWorkflowPredictionListByNextPossibleIdQuery request,
        CancellationToken cancellationToken)
    {
        var workflowPredictions =
            await _workflowPredictionRepository.GetWorkflowPredictionByNextPossibleId(request.NextPossibleId);

        var workflowPredictionsDto = _mapper.Map<List<WorkflowPredictionListByNextPossibleIdVm>>(workflowPredictions);

        return workflowPredictionsDto;
    }
}