using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactCategory.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.FiaImpactCategoryModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class FiaImpactCategoryProfile : Profile
{
    public FiaImpactCategoryProfile()
    {
        CreateMap<FiaImpactCategory, FiaImpactCategoryListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<FiaImpactCategory, FiaImpactCategoryDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<FiaImpactCategory, CreateFiaImpactCategoryCommand>().ReverseMap();
        CreateMap<FiaImpactCategory, FiaImpactCategoryViewModel>().ReverseMap();

        CreateMap<CreateFiaImpactCategoryCommand, FiaImpactCategoryViewModel>().ReverseMap();
        CreateMap<UpdateFiaImpactCategoryCommand, FiaImpactCategoryViewModel>().ReverseMap();

        CreateMap<UpdateFiaImpactCategoryCommand, FiaImpactCategory>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<PaginatedResult<FiaImpactCategory>, PaginatedResult<FiaImpactCategoryListVm>>()
        .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}