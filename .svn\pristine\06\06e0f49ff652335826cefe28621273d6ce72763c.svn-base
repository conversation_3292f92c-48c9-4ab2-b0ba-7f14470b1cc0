﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.HacmpClusterModel.HacmpClusterViewModel
@using ContinuityPatrol.Domain.ViewModels.HacmpClusterModel
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel modal-lg" tabindex="-1">
    <form class="modal-content"  id="CreateForm" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-cluster-database"></i><span>HACMP Cluster Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="this.blur()"></button>
        </div>
        <div class="modal-body">
            @Html.AntiForgeryToken()
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <div class="form-label">Name</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-name"></i></span>
                            <input class="form-control" id="hacmpName" placeholder="Enter HACMP Cluster Name" maxlength="100" autocomplete="off" />
                        </div>
                        <span id="hacmpNameError"></span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <div class="form-label">Server</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-server"></i></span>
                            <select class=" form-select-modal" data-live-search="true" id="hacmpServerDrpdwn" data-placeholder="Select Server">
                                @foreach (var Server in Model.Servers)
                                {
                                    <option id="@Server.Id" value="@Server.Name">@Server.Name</option>
                                }
                            </select>
                        </div>
                        <span id="hacmpServerError"></span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <div class="form-label">LSSRC Path</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-endpoint-port-number"></i></span>
                            <input class="form-control" maxlength="500" id="hacmpLSSRCPath" placeholder="Enter LSSRC Path" autocomplete="off" />
                        </div>
                        <span  id="hacmpLSSRCPathError"></span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <div class="form-label">CLRG Info Path</div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-endpoint-port-number"></i></span>
                            <input class="form-control" maxlength="500" id="hacmpCLRGPath" placeholder="Enter CLRG Info Path" autocomplete="off" />
                        </div>
                        <span  id="hacmpCLRGPathError"></span>
                    </div>
                </div>
                <div class="col-12">
                    <div class="form-group">
                        <div class="form-label">Resource Group Name </div>
                        <div class="input-group">
                            <span class="input-group-text"><i class="cp-LDAP"></i></span>
                            <input class="form-control" id="hacmpGroupName" placeholder="Enter Resource Group Name" maxlength="100" autocomplete="off" />
                        </div>
                        <span  id="hacmpGroupNameError"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" onclick="this.blur()" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" tabindex="-1" id="hacmpSaveFunction">Save</button>
            </div>
        </div>
    </form>
</div>
