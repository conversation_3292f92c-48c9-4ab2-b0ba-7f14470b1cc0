﻿
function RtodataInfraobjects(businessDetails) {
   // console.log("businessDetails125", businessDetails)
    let RTOInfraObject = businessDetails ? businessDetails : null
    
    if (RTOInfraObject != null && RTOInfraObject.totalInfraObject != 0) {
        am4core.useTheme(am4themes_animated);
        // Themes end

        // Create chart instance
        var chart = am4core.create("RTOInfraObjectsChart", am4charts.PieChart);
        if (chart.logo) {
            chart.logo.disabled = true;
        }
        // Add data
        if (RTOInfraObject != null && RTOInfraObject.totalInfraObject != 0) {
            chart.data = [{
                "country": "Under RTO",
                "litres": RTOInfraObject.infraUnderRTO
            }, {
                "country": "Above RTO",
                "litres": RTOInfraObject.infraAboveRTO
            },  {
                "country": "Drill Not Executed",
                "litres": RTOInfraObject.infraDrillNotExecuted
            }];
        }
        else if (RTOInfraObject != null && RTOInfraObject.totalInfraObject === 0) {
            chart.data = [{
                "country": "Under RTO",
                "litres": RTOInfraObject.infraUnderRTO
            }, {
                "country": "Above RTO",
                "litres": RTOInfraObject.infraAboveRTO
            },  {
                "country": "Drill Not Executed",
                "litres": RTOInfraObject.infraDrillNotExecuted
            }];
        } else {
            chart.data = [{
                "country": "Under RTO (" + 0 + ")",
                "litres": 0
            }, {
                "country": "Above RTO (" + 0 + ")",
                "litres": 0
            },  {
                "country": "Drill Not Executed (" + 0 + ")",
                "litres": 0.1
            }];
        }

        // Set inner radius
        chart.innerRadius = am4core.percent(65);

        // Add and configure Series
        var pieSeries = chart.series.push(new am4charts.PieSeries());
        pieSeries.dataFields.value = "litres";
        pieSeries.dataFields.category = "country";
        pieSeries.ticks.template.disabled = true;
        pieSeries.labels.template.disabled = true;
        pieSeries.slices.template.stroke = am4core.color("#fff");
        pieSeries.slices.template.strokeWidth = 5;
        pieSeries.slices.template.strokeOpacity = 5;
        pieSeries.slices.template.cornerRadius = 20;
        pieSeries.slices.template.innerCornerRadius = 20;
        // This creates initial animation
        pieSeries.hiddenState.properties.opacity = 1;
        pieSeries.hiddenState.properties.endAngle = -90;
        pieSeries.hiddenState.properties.startAngle = -90;
        if (RTOInfraObject != null && RTOInfraObject.totalInfraObject != 0) {
            pieSeries.colors.list = [
                am4core.color("#10b310"),
                am4core.color("#f77586"),
                am4core.color("#d9d9d9")
                //am4core.color("#d9d9d9")
            ];
        } else {
            pieSeries.colors.list = [
                am4core.color("#d9d9d9")
            ];
        }


        let RTOInfraObjectlabel = pieSeries.createChild(am4core.Label);
        let totalBusinessInfraCount = RTOInfraObject?.totalInfraObject ?? 0
        RTOInfraObjectlabel.text = "[bold font-size:16px]" + totalBusinessInfraCount + "[/] \n [font-size:13px]Total[/]"
        RTOInfraObjectlabel.textAlign = "middle";
        RTOInfraObjectlabel.horizontalCenter = "middle";
        RTOInfraObjectlabel.verticalCenter = "middle";
        RTOInfraObjectlabel.fontSize = 14;

        // Change the padding values
        chart.padding(-5, -20, -15, -20)
        chart.legend = new am4charts.Legend();
        chart.legend.itemContainers.template.padding(8, 0, 0, 0);

        // Add a legend
        chart.legend.position = "left";
        chart.legend.valueLabels.template.disabled = true;
        chart.legend.itemContainers.template.clickable = false;
        chart.legend.itemContainers.template.focusable = false;
        //chart.legend.labels.template.text = "[font-size:10px ]{name}";
        chart.legend.labels.template.text = "[font-size:10px]{name} [bold][font-size:15px]({value})[/]";
        chart.legend.labels.template.fill = am4core.color("#6c757d");
        chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
        var markerTemplate = chart.legend.markers.template;
        markerTemplate.width = 10;
        markerTemplate.height = 10;
    } else {
        $('#RTOInfraObjectsChart')
            .css('text-align', 'center')
            .html('<img class="m-auto" src="../../img/isomatric/infraobjects_not_configured.svg" height="112" alt="No Data Isomatric" loading="lazy">');
    }
}