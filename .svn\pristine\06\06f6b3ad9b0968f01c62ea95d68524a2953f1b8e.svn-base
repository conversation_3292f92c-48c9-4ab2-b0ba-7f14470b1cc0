<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Site Type QUnit Tests</title>
    <!-- Styles -->
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/15.2.0/sinon.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- Your Logic -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Sites/Site Type/siteType.js"></script>
    <script src="/js/Configuration/Sites/Site Type/SiteTypeTestCase.js"></script>

    <style>
        #qunit-fixture {
            display: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        <!-- Elements required by tests -->
        <button id="siteTypeCreateButton"></button>
        <div id="siteTypeCreateModal" style="display:none;"></div>
        <input type="text" id="siteTypeName">
        <select id="siteTypeDropdown">
            <option value="">--Select--</option>
            <option value="Primary">Primary</option>
            <option value="DR">DR</option>
        </select>
        <span id="siteTypeNameError"></span>
        <span id="siteTypeError"></span>
        <button id="siteTypeSaveBtn"></button>
        <div id="siteTypeDeleteModal" style="display:none;"></div>
        <input type="hidden" id="siteTypeDeleteId">
        <button id="siteTypeDeleteButton"></button>
        <input type="text" id="siteTypeSearch">

        <table id="siteTypeTable">
            <thead>
                <tr>
                    <th>Sr. No.</th>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>

        <form id="siteTypeCreateForm"></form>
    </div>

    <script>
        // Global values
        var RootUrl = ""; // adjust if needed for test environment

        // QUnit helpful logs
        QUnit.config.testTimeout = 3000;
        QUnit.config.reorder = false;
        QUnit.begin(() => console.log("\u{1F501} QUnit tests starting..."));
        QUnit.done(() => console.log("\u{2705} QUnit tests completed."));
        QUnit.testStart(test => console.log(`\u{25B6}\u{FE0F} Running: ${test.name}`));
        QUnit.testDone(test => console.log(`\u{2705} Finished: ${test.name}`));
    </script>
</body>
</html>