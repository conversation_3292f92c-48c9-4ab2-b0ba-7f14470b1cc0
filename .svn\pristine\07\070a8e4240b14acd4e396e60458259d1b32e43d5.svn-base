﻿namespace ContinuityPatrol.Application.Features.ComponentType.Queries.GetDetail;

public class GetComponentTypeDetailQueryHandler : IRequestHandler<GetComponentTypeDetailQuery, ComponentTypeDetailVm>
{
    private readonly IComponentTypeRepository _componentTypeRepository;
    private readonly IMapper _mapper;

    public GetComponentTypeDetailQueryHandler(IMapper mapper, IComponentTypeRepository componentTypeRepository)
    {
        _mapper = mapper;
        _componentTypeRepository = componentTypeRepository;
    }

    public async Task<ComponentTypeDetailVm> Handle(GetComponentTypeDetailQuery request,
        CancellationToken cancellationToken)
    {
        var componentType = await _componentTypeRepository.GetComponentTypeById(request.Id);

        Guard.Against.NullOrDeactive(componentType, nameof(ComponentType),
            new NotFoundException(nameof(ComponentType), request.Id));

        var componentTypeDetailDto = _mapper.Map<ComponentTypeDetailVm>(componentType);

        return componentTypeDetailDto;
    }
}