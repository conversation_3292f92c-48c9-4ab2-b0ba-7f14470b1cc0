using ContinuityPatrol.Domain.ViewModels.CyberComponentGroupModel;

namespace ContinuityPatrol.Application.Features.CyberComponentGroup.Queries.GetList;

public class
    GetCyberComponentGroupListQueryHandler : IRequestHandler<GetCyberComponentGroupListQuery,
        List<CyberComponentGroupListVm>>
{
    private readonly ICyberComponentGroupRepository _cyberComponentGroupRepository;
    private readonly IMapper _mapper;

    public GetCyberComponentGroupListQueryHandler(IMapper mapper,
        ICyberComponentGroupRepository cyberComponentGroupRepository)
    {
        _mapper = mapper;
        _cyberComponentGroupRepository = cyberComponentGroupRepository;
    }

    public async Task<List<CyberComponentGroupListVm>> Handle(GetCyberComponentGroupListQuery request,
        CancellationToken cancellationToken)
    {
        var cyberComponentGroups = await _cyberComponentGroupRepository.ListAllAsync();

        if (cyberComponentGroups.Count <= 0) return new List<CyberComponentGroupListVm>();

        return _mapper.Map<List<CyberComponentGroupListVm>>(cyberComponentGroups);
    }
}