﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface ITemplateRepository : IRepository<Template>
{
    Task<List<Template>> GetTemplateNames();
    Task<bool> IsTemplateNameExist(string name, string id);
    Task<bool> IsTemplateNameUnique(string name);
    Task<Template> GetTemplateByReplicationTypeIdAndActionType(string replicationTypeId, string actionType);
    Task<List<Template>> GetTemplateByReplicationTypeId(string replicationTypeId);
    Task<bool> IsTemplateByReplicationTypeIdAndActionTypeNameUnique(string replicationTypeId, string actionType);
}