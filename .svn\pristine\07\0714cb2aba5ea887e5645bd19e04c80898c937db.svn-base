﻿//using ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetAvailableCount;
//using ContinuityPatrol.Shared.Core.Enums;
//using ContinuityPatrol.Shared.Core.Exceptions;
//using ContinuityPatrol.Shared.Core.Helper;

//namespace ContinuityPatrol.Application.Features.LicenseInfo.Queries.GetDetailView;
//public class GetLicenseInfoDetailViewQueryHandler : IRequestHandler<GetLicenseInfoDetailViewQuery, LicenseInfoDetailViewVm>
//{
//    private readonly ILicenseInfoRepository _licenseInfoRepository;
//    private readonly IMapper _mapper;
//    private readonly ILicenseManagerRepository _licenseManagerRepository;
//    public GetLicenseInfoDetailViewQueryHandler(ILicenseInfoRepository licenseInfoRepository, IMapper mapper, ILicenseManagerRepository licenseManagerRepository)
//    {
//        _licenseInfoRepository = licenseInfoRepository;
//        _mapper = mapper;
//        _licenseManagerRepository = licenseManagerRepository;
//    }

//    public async Task<LicenseInfoDetailViewVm> Handle(GetLicenseInfoDetailViewQuery request, CancellationToken cancellationToken)
//    {
//        var databaseLicenseInfo = await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntity(request.LicenseId, Modules.Database.ToString());
//        var serverLicenseInfo = await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntity(request.LicenseId, Modules.Server.ToString());
//        var replicationLicenseInfo = await _licenseInfoRepository.GetLicenseInfoByLicenseIdAndEntity(request.LicenseId, Modules.Replication.ToString());

//        var licenseDtl = await _licenseManagerRepository.GetLicenseDetailByIdAsync(request.LicenseId);

//        Guard.Against.NullOrDeactive(licenseDtl, nameof(Domain.Entities.LicenseInfo), new NotFoundException(nameof(Domain.Entities.LicenseInfo), request.LicenseId));

//        var licenseInfoDetailViewVm = new LicenseInfoDetailViewVm
//        {
//            DatabaseVms = _mapper.Map<List<DatabaseVm>>(databaseLicenseInfo),
//            ServerVms = _mapper.Map<List<ServerVm>>(serverLicenseInfo),
//            ReplicationVms = _mapper.Map<List<ReplicationVm>>(replicationLicenseInfo),
//            AvailableCountVm = new AvailableCountVm
//            {
//                ServerAvailableCount = int.Parse(licenseDtl.ServerCount) - serverLicenseInfo.Count(),
//                DatabaseAvailableCount = int.Parse(licenseDtl.DatabaseCount) - databaseLicenseInfo.Count(),
//                ReplicationAvailableCount = int.Parse(licenseDtl.ReplicationCount) - replicationLicenseInfo.Count(),
//            }
//        };

//        return licenseInfoDetailViewVm;
//    }

//}

