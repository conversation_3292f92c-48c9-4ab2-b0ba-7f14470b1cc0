@font-face {
  font-family: 'cp-icon';
  src:  url('fonts/cp-icon.eot?3jy17z');
  src:  url('fonts/cp-icon.eot?3jy17z#iefix') format('embedded-opentype'),
    url('fonts/cp-icon.ttf?3jy17z') format('truetype'),
    url('fonts/cp-icon.woff?3jy17z') format('woff'),
    url('fonts/cp-icon.svg?3jy17z#cp-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="cp-"], [class*=" cp-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'cp-icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cp-gui-automation:before {
  content: "\ecdf";
}
.cp-web-automation:before {
  content: "\ecdb";
}
.cp-cusotmize:before {
  content: "\ecdc";
}
.cp-custom-automation:before {
  content: "\ecdd";
}
.cp-find-by-xpath:before {
  content: "\ecde";
}
.cp-save-as:before {
  content: "\ecd4";
}
.cp-clear-text:before {
  content: "\ecd3";
}
.cp-focus:before {
  content: "\ecc9";
}
.cp-execution-startorstop:before {
  content: "\ecca";
}
.cp-login-url:before {
  content: "\eccb";
}
.cp-maximize:before {
  content: "\eccc";
}
.cp-minimize:before {
  content: "\eccd";
}
.cp-navigate-back:before {
  content: "\ecce";
}
.cp-navigate-url:before {
  content: "\eccf";
}
.cp-navigate-url-tab:before {
  content: "\ecd0";
}
.cp-page-source:before {
  content: "\ecd1";
}
.cp-switch-to-tab:before {
  content: "\ecd2";
}
.cp-click:before {
  content: "\ecd5";
}
.cp-close-window1:before {
  content: "\ecd6";
}
.cp-double-click:before {
  content: "\ecd7";
}
.cp-enter_text:before {
  content: "\ecd8";
}
.cp-execution:before {
  content: "\ecd9";
}
.cp-close-window:before {
  content: "\ecda";
}
.cp-template:before {
  content: "\ecc7";
}
.cp-action-type:before {
  content: "\ecc8";
}
.cp-airgap-group:before {
  content: "\ecc6";
}
.cp-datalog-low:before {
  content: "\ecc4";
}
.cp-datalog-high:before {
  content: "\ecc5";
}
.cp-thermometer:before {
  content: "\ecc3";
}
.cp-license-key-edit:before {
  content: "\ecc2";
}
.cp-manage-dependency-rules:before {
  content: "\ecc1";
}
.cp-workflow-verify:before {
  content: "\ecbf";
}
.cp-workflow-unverify:before {
  content: "\ecc0";
}
.cp-open-office:before {
  content: "\ecb7";
}
.cp-pdf:before {
  content: "\ecb8";
}
.cp-PPT:before {
  content: "\ecb9";
}
.cp-video:before {
  content: "\ecba";
}
.cp-word:before {
  content: "\ecbb";
}
.cp-audio:before {
  content: "\ecbc";
}
.cp-document:before {
  content: "\ecbd";
}
.cp-excel:before {
  content: "\ecbe";
}
.cp-activedirectory:before {
  content: "\ecb6";
}
.cp-resielncy-service:before {
  content: "\ecb3";
}
.cp-Workflow-service:before {
  content: "\ecb4";
}
.cp-moitoring-service:before {
  content: "\ecb5";
}
.cp-rount-dot-horizontal:before {
  content: "\ecab";
}
.cp-rount-dot-vertical:before {
  content: "\ecac";
}
.cp-split:before {
  content: "\ecad";
}
.cp-collapse:before {
  content: "\ecae";
}
.cp-running-profile:before {
  content: "\ecaf";
}
.cp-timeline-view:before {
  content: "\ecb0";
}
.cp-freeze:before {
  content: "\ecb1";
}
.cp-unfreeze:before {
  content: "\ecb2";
}
.cp-circle-open:before {
  content: "\eca6";
}
.cp-doc-file:before {
  content: "\eca7";
}
.cp-folder-close:before {
  content: "\eca8";
}
.cp-folder-open:before {
  content: "\eca9";
}
.cp-circle-close:before {
  content: "\ecaa";
}
.cp-add-user:before {
  content: "\eca3";
}
.cp-approval-list:before {
  content: "\eca4";
}
.cp-deligate:before {
  content: "\eca5";
}
.cp-backtrack:before {
  content: "\ec98";
}
.cp-backtrack-1:before {
  content: "\ec99";
}
.cp-carousel:before {
  content: "\ec9a";
}
.cp-db-hook:before {
  content: "\ec9b";
}
.cp-db-hook-1:before {
  content: "\ec9c";
}
.cp-goto:before {
  content: "\ec9d";
}
.cp-quick-response:before {
  content: "\ec9e";
}
.cp-reset:before {
  content: "\ec9f";
}
.cp-set-attribute:before {
  content: "\eca0";
}
.cp-sms:before {
  content: "\eca1";
}
.cp-ask-question:before {
  content: "\eca2";
}
.cp-company-name-2:before {
  content: "\ec91";
}
.cp-license-renewal-2:before {
  content: "\ec92";
}
.cp-new-license:before {
  content: "\ec93";
}
.cp-po-name-2:before {
  content: "\ec94";
}
.cp-subscription-2:before {
  content: "\ec95";
}
.cp-uat-2:before {
  content: "\ec96";
}
.cp-amc-2:before {
  content: "\ec97";
}
.cp-drift-manage:before {
  content: "\ec8f";
}
.cp-drift-alerts:before {
  content: "\ec90";
}
.cp-radio-selects:before {
  content: "\ebbf";
}
.cp-radio-unselects:before {
  content: "\ebc0";
}
.cp-unselects:before {
  content: "\ebc1";
}
.cp-selects:before {
  content: "\eb56";
}
.cp-play-resume:before {
  content: "\ebbd";
}
.cp-pin-point:before {
  content: "\ebbe";
}
.cp-renewal-large:before {
  content: "\ebb5";
}
.cp-upgrade-large:before {
  content: "\ebb8";
}
.cp-AMC-large:before {
  content: "\ebba";
}
.cp-planned:before {
  content: "\ebb4";
}
.cp-lowercase:before {
  content: "\ebb0";
}
.cp-numeric-characters:before {
  content: "\ebb1";
}
.cp-upgrade-assistant:before {
  content: "\ebb2";
}
.cp-password-policy:before {
  content: "\ebb3";
}
.cp-special-characters:before {
  content: "\ebb6";
}
.cp-unplanned:before {
  content: "\ebb7";
}
.cp-uppercase:before {
  content: "\ebb9";
}
.cp-file-close:before {
  content: "\ebbb";
}
.cp-hand-key:before {
  content: "\ebbc";
}
.cp-config-mismatch:before {
  content: "\eba8";
}
.cp-count-mismatch:before {
  content: "\eba9";
}
.cp-drift-conflicted:before {
  content: "\ebaa";
}
.cp-mdi_motion-outline:before {
  content: "\ebab";
}
.cp-policy-mismatch:before {
  content: "\ebac";
}
.cp-version-mismatch:before {
  content: "\ebad";
}
.cp-down-arrow-small:before {
  content: "\ebae";
}
.cp-up-arrow-small:before {
  content: "\ebaf";
}
.cp-application-name:before {
  content: "\ea87";
}
.cp-application-path:before {
  content: "\eb76";
}
.cp-application-port:before {
  content: "\eb79";
}
.cp-close-x:before {
  content: "\eb96";
}
.cp-configuration-setting:before {
  content: "\eba7";
}
.cp-schema-:before {
  content: "\ec70";
}
.cp-snap-1:before {
  content: "\ec69";
}
.cp-snap-2:before {
  content: "\ec6a";
}
.cp-snap-age:before {
  content: "\ec6b";
}
.cp-snap-file:before {
  content: "\ec6c";
}
.cp-snap-location:before {
  content: "\ec6d";
}
.cp-snap-return:before {
  content: "\ec6e";
}
.cp-down-left:before {
  content: "\ec63";
}
.cp-ram:before {
  content: "\ec64";
}
.cp-time-line-action:before {
  content: "\ec65";
}
.cp-up-right:before {
  content: "\ec66";
}
.cp-Xeon:before {
  content: "\ec67";
}
.cp-air-gap:before {
  content: "\ec68";
}
.cp-area-configuration:before {
  content: "\ec4d";
}
.cp-area-list:before {
  content: "\ec4e";
}
.cp-component-configuration:before {
  content: "\ec50";
}
.cp-component-list:before {
  content: "\ec51";
}
.cp-component-type:before {
  content: "\ec52";
}
.cp-component-variant:before {
  content: "\ec53";
}
.cp-connects:before {
  content: "\ec54";
}
.cp-cp-server-cloud:before {
  content: "\ec55";
}
.cp-db-server:before {
  content: "\ec57";
}
.cp-file-scanner:before {
  content: "\ec58";
}
.cp-file-server:before {
  content: "\ec59";
}
.cp-file-system:before {
  content: "\ec5a";
}
.cp-others:before {
  content: "\ec5b";
}
.cp-port-number:before {
  content: "\ec5c";
}
.cp-selection:before {
  content: "\ec5d";
}
.cp-air-gap-off .path1:before {
  content: "\ec5e";
  color: rgb(0, 0, 0);
}
.cp-air-gap-off .path2:before {
  content: "\ec5f";
  margin-left: -1em;
  color: rgb(220, 53, 69);
}
.cp-air-gap-on .path1:before {
  content: "\ec60";
  color: rgb(0, 0, 0);
}
.cp-air-gap-on .path2:before {
  content: "\ec61";
  margin-left: -1em;
  color: rgb(65, 194, 0);
}
.cp-area:before {
  content: "\ec62";
}
.cp-by-pass:before {
  content: "\ec4f";
}
.cp-custom-workflow-execution-1:before {
  content: "\ec56";
}
.cp-custom-workflow-execution:before {
  content: "\ec49";
}
.cp-drift-profile:before {
  content: "\ec4a";
}
.cp-drift-configurations:before {
  content: "\ec4b";
}
.cp-drift-job-management:before {
  content: "\ec4c";
}
.cp-middle-align:before {
  content: "\ec3a";
}
.cp-rectangle:before {
  content: "\ec3b";
}
.cp-redo:before {
  content: "\ec3c";
}
.cp-right-align:before {
  content: "\ec3d";
}
.cp-rotate:before {
  content: "\ec3e";
}
.cp-solid-center-align:before {
  content: "\ec3f";
}
.cp-text-box:before {
  content: "\ec40";
}
.cp-top-align:before {
  content: "\ec41";
}
.cp-undo:before {
  content: "\ec42";
}
.cp-vertical-spacing:before {
  content: "\ec43";
}
.cp-bottom-align:before {
  content: "\ec44";
}
.cp-center-align:before {
  content: "\ec45";
}
.cp-horizontal-spacing:before {
  content: "\ec46";
}
.cp-left-align:before {
  content: "\ec47";
}
.cp-line:before {
  content: "\ec48";
}
.cp-poc:before {
  content: "\ec36";
}
.cp-subscription:before {
  content: "\ec37";
}
.cp-total-po:before {
  content: "\ec38";
}
.cp-uat:before {
  content: "\ec39";
}
.cp-split-arrow:before {
  content: "\ec35";
}
.cp-authentication-success:before {
  content: "\ec33";
}
.cp-authentication-failed:before {
  content: "\ec34";
}
.cp-workflow-templates:before {
  content: "\ec32";
}
.cp-IBM-AS:before {
  content: "\ec31";
}
.cp-data-replication:before {
  content: "\ec30";
}
.cp-enables:before {
  content: "\ec2e";
}
.cp-disables:before {
  content: "\ec2f";
}
.cp-power-shell:before {
  content: "\ec22";
}
.cp-profile-name:before {
  content: "\ec23";
}
.cp-solution-type:before {
  content: "\ec24";
}
.cp-SSH_cmd:before {
  content: "\ec25";
}
.cp-WMI:before {
  content: "\ec26";
}
.cp-command-promt .path1:before {
  content: "\ec27";
  color: rgb(255, 255, 255);
}
.cp-command-promt .path2:before {
  content: "\ec28";
  margin-left: -0.9599609375em;
  color: rgb(255, 255, 255);
}
.cp-command-promt .path3:before {
  content: "\ec29";
  margin-left: -0.9599609375em;
  color: rgb(255, 255, 255);
}
.cp-command-promt .path4:before {
  content: "\ec2a";
  margin-left: -0.9599609375em;
  color: rgb(0, 0, 0);
}
.cp-command-promt .path5:before {
  content: "\ec2b";
  margin-left: -0.9599609375em;
  color: rgb(255, 255, 255);
}
.cp-drift-profile-configuration:before {
  content: "\ec2c";
}
.cp-drift-profile-list:before {
  content: "\ec2d";
}
.cp-drift_dashboard:before {
  content: "\ec18";
}
.cp-drift-configuration:before {
  content: "\ec19";
}
.cp-drift-parameter-list:before {
  content: "\ec1a";
}
.cp-handling-mode:before {
  content: "\ec1b";
}
.cp-impact-type:before {
  content: "\ec1c";
}
.cp-inbuild:before {
  content: "\ec1d";
}
.cp-iscompliance:before {
  content: "\ec1e";
}
.cp-parameter-name:before {
  content: "\ec1f";
}
.cp-severity:before {
  content: "\ec20";
}
.cp-category:before {
  content: "\ec21";
}
.cp-autonomous-mode:before {
  content: "\ec16";
}
.cp-supervising-mode:before {
  content: "\ec17";
}
.cp-ssh-private-key-path:before {
  content: "\ec12";
}
.cp-action-name:before {
  content: "\ec13";
}
.cp-license-timeline:before {
  content: "\ec14";
}
.cp-Patch:before {
  content: "\ec15";
}
.cp-license-request:before {
  content: "\ec0e";
}
.cp-AMC:before {
  content: "\ec0c";
}
.cp-license-renewal:before {
  content: "\ec0d";
}
.cp-license-upgrade:before {
  content: "\ec0f";
}
.cp-server_up_user_out:before {
  content: "\ec10";
}
.cp-server_up_user_out-1:before {
  content: "\ec11";
}
.cp-upgrade:before {
  content: "\ec0b";
}
.cp-main-frame:before {
  content: "\ec0a";
}
.cp-error-message:before {
  content: "\ec09";
}
.cp-redis-database:before {
  content: "\ec08";
}
.cp-max-time:before {
  content: "\ec07";
}
.cp-not-applicable:before {
  content: "\ec06";
}
.cp-solution-mapping:before {
  content: "\ec04";
}
.cp-solution-name:before {
  content: "\ec05";
}
.cp-partially-ready:before {
  content: "\ec03";
}
.cp-coord-Time:before {
  content: "\ebee";
}
.cp-copied_log_time:before {
  content: "\ebef";
}
.cp-copy_queue_length:before {
  content: "\ebf0";
}
.cp-differential-resync:before {
  content: "\ebf1";
}
.cp-fatal-reason:before {
  content: "\ebf2";
}
.cp-flashCopy-sequence-number:before {
  content: "\ebf3";
}
.cp-id:before {
  content: "\ebf4";
}
.cp-job-type:before {
  content: "\ebf5";
}
.cp-list-prsite1:before {
  content: "\ebf6";
}
.cp-mailbox_database:before {
  content: "\ebf7";
}
.cp-master:before {
  content: "\ebf8";
}
.cp-replayed_log_time:before {
  content: "\ebf9";
}
.cp-site-names:before {
  content: "\ebfa";
}
.cp-time-2:before {
  content: "\ebfb";
}
.cp-vm_name:before {
  content: "\ebfc";
}
.cp-asm:before {
  content: "\ebfd";
}
.cp-asynchronous:before {
  content: "\ebfe";
}
.cp-CG-Interval-Time:before {
  content: "\ebff";
}
.cp-consistency:before {
  content: "\ec00";
}
.cp-content_index_state:before {
  content: "\ec01";
}
.cp-content-index:before {
  content: "\ec02";
}
.cp-state:before {
  content: "\ebed";
}
.cp-message-alert:before {
  content: "\ebec";
}
.cp-exceeded:before {
  content: "\ebeb";
}
.cp-threshold:before {
  content: "\ebe9";
}
.cp-execution-type:before {
  content: "\ebea";
}
.cp-one-view:before {
  content: "\ebe7";
}
.cp-crown-active-inactive:before {
  content: "\ebe8";
}
.cp-city-name:before {
  content: "\ebe2";
}
.cp-country-name:before {
  content: "\ebe3";
}
.cp-latitude:before {
  content: "\ebe4";
}
.cp-longitude:before {
  content: "\ebe5";
}
.cp-site-location:before {
  content: "\ebe6";
}
.cp-goto-report:before {
  content: "\ebe1";
}
.cp-drill-error:before {
  content: "\ebe0";
}
.cp-step-mode:before {
  content: "\ebdd";
}
.cp-auto-mode:before {
  content: "\ebde";
}
.cp-simulation--mode:before {
  content: "\ebdf";
}
.cp-firewall:before {
  content: "\ebd6";
}
.cp-virtualization_new:before {
  content: "\ebd7";
}
.cp-failure_count:before {
  content: "\ebdc";
}
.cp-netapp:before {
  content: "\ebd5";
}
.cp-scan:before {
  content: "\ebd4";
}
.cp-oracle-solaris:before {
  content: "\ebc3";
}
.cp-bypass:before {
  content: "\ebd3";
}
.cp-BIA-cost:before {
  content: "\eb95";
}
.cp-BIA-template:before {
  content: "\eb97";
}
.cp-bulk-credential:before {
  content: "\eb98";
}
.cp-data-sync:before {
  content: "\eb99";
}
.cp-home-region-configuration:before {
  content: "\eb9a";
}
.cp-import-server-from-IMDB:before {
  content: "\eb9b";
}
.cp-incident-summery:before {
  content: "\eb9c";
}
.cp-incident-summery-1:before {
  content: "\eb9d";
}
.cp-profile-monitoring-configuration:before {
  content: "\eb9e";
}
.cp-robocopy:before {
  content: "\eb9f";
}
.cp-rsync-options:before {
  content: "\eba0";
}
.cp-schedule-discovery:before {
  content: "\eba1";
}
.cp-schedule-discovery-1:before {
  content: "\eba2";
}
.cp-ADM-settings:before {
  content: "\eba3";
}
.cp-app-dependent-mapping:before {
  content: "\eba4";
}
.cp-application-dependent:before {
  content: "\eba5";
}
.cp-ASM-grid-configuration:before {
  content: "\eba6";
}
.cp-connection-sucess:before {
  content: "\eb83";
}
.cp-connection-failure:before {
  content: "\eb84";
}
.cp-create:before {
  content: "\eb85";
}
.cp-decommission:before {
  content: "\eb86";
}
.cp-Diagram:before {
  content: "\eb87";
}
.cp-draft:before {
  content: "\eb88";
}
.cp-form-list:before {
  content: "\eb89";
}
.cp-delete-row-before:before {
  content: "\eb8a";
}
.cp-Delete-table:before {
  content: "\eb8b";
}
.cp-delete-row-after:before {
  content: "\eb8c";
}
.cp-insert-row-after:before {
  content: "\eb8d";
}
.cp-insert-row-before:before {
  content: "\eb8e";
}
.cp-layout:before {
  content: "\eb8f";
}
.cp-mapping:before {
  content: "\eb90";
}
.cp-page-builder:before {
  content: "\eb91";
}
.cp-saved:before {
  content: "\eb92";
}
.cp-user-privileges:before {
  content: "\eb93";
}
.cp-action-list:before {
  content: "\eb94";
}
.cp-scheduled-report:before {
  content: "\eb81";
}
.cp-manual--mode:before {
  content: "\eb82";
}
.cp-datalog:before {
  content: "\eb7b";
}
.cp-deselect:before {
  content: "\eb7c";
}
.cp-idel:before {
  content: "\eb7d";
}
.cp-not-started:before {
  content: "\eb7e";
}
.cp-replace:before {
  content: "\eb7f";
}
.cp-active-inactive:before {
  content: "\eb80";
}
.cp-affecteds:before {
  content: "\e9cf";
}
.cp-workflow:before {
  content: "\eb29";
}
.cp-workflow-not-configured:before {
  content: "\eb78";
}
.cp-drill-executed:before {
  content: "\eb6f";
}
.cp-meeting-rto:before {
  content: "\eb70";
}
.cp-rename:before {
  content: "\eb71";
}
.cp-role:before {
  content: "\eb72";
}
.cp-service-breaching:before {
  content: "\eb73";
}
.cp-workflow-executed:before {
  content: "\eb74";
}
.cp-active-alerts:before {
  content: "\eb75";
}
.cp-configured:before {
  content: "\eb77";
}
.cp-service-heatmap-database:before {
  content: "\eb6a";
}
.cp-service-heatmap-replication:before {
  content: "\eb6c";
}
.cp-business-service-overview:before {
  content: "\eb6d";
}
.cp-Stopped:before {
  content: "\eb63";
}
.cp-disconnected:before {
  content: "\eb62";
}
.cp-Idle:before {
  content: "\eb64";
}
.cp-normal:before {
  content: "\eb65";
}
.cp-offline:before {
  content: "\eb66";
}
.cp-online:before {
  content: "\eb67";
}
.cp-primary:before {
  content: "\eb68";
}
.cp-secondary:before {
  content: "\eb69";
}
.cp-connected:before {
  content: "\eb6b";
}
.cp-double-take:before {
  content: "\eb61";
}
.cp-EMC:before {
  content: "\eb5f";
}
.cp-H3PAR:before {
  content: "\eb60";
}
.cp-IBM-AIX:before {
  content: "\eb5e";
}
.cp-shiled-success:before {
  content: "\eb7a";
}
.cp-shiled-warning:before {
  content: "\eb5b";
}
.cp-shiled-error:before {
  content: "\eb5a";
}
.cp-pending:before {
  content: "\eb57";
}
.cp-RPO:before {
  content: "\eb58";
}
.cp-RTO:before {
  content: "\eb59";
}
.cp-skipped:before {
  content: "\eb5c";
}
.cp-health:before {
  content: "\eb5d";
}
.cp-select:before {
  content: "\e923";
}
.cp-colour-picker:before {
  content: "\eb52";
}
.cp-end:before {
  content: "\eb53";
}
.cp-home:before {
  content: "\eb54";
}
.cp-select-user:before {
  content: "\eb55";
}
.cp-mongo-db:before {
  content: "\e9d5";
}
.cp-managers:before {
  content: "\e9d6";
}
.cp-Operator:before {
  content: "\eaa5";
}
.cp-super-admin:before {
  content: "\eada";
}
.cp-user-role-settings:before {
  content: "\eadf";
}
.cp-administrator:before {
  content: "\eaf2";
}
.cp-IBM:before {
  content: "\eb50";
}
.cp-delay:before {
  content: "\eb37";
}
.cp-error-handing:before {
  content: "\eb38";
}
.cp-general:before {
  content: "\eb39";
}
.cp-if:before {
  content: "\eb3a";
}
.cp-loop:before {
  content: "\eb3b";
}
.cp-powershell:before {
  content: "\eb3c";
}
.cp-rule:before {
  content: "\eb3d";
}
.cp-script:before {
  content: "\eb3e";
}
.cp-security:before {
  content: "\eb3f";
}
.cp-SSH:before {
  content: "\eb40";
}
.cp-stringutility:before {
  content: "\eb41";
}
.cp-system:before {
  content: "\eb42";
}
.cp-token:before {
  content: "\eb43";
}
.cp-variables:before {
  content: "\eb44";
}
.cp-wait:before {
  content: "\eb45";
}
.cp-wmi:before {
  content: "\eb46";
}
.cp-circle-switch:before {
  content: "\eb47";
}
.cp-common:before {
  content: "\eb48";
}
.cp-conditional:before {
  content: "\eb49";
}
.cp-connect:before {
  content: "\eb4a";
}
.cp-conversion:before {
  content: "\eb4b";
}
.cp-create-Logger:before {
  content: "\eb4c";
}
.cp-data-source:before {
  content: "\eb4d";
}
.cp-workflow-dashline:before {
  content: "\eb4e";
}
.cp-workflow-dot-line:before {
  content: "\eb4f";
}
.cp-workflow-line:before {
  content: "\ea74";
}
.cp-fail-back:before {
  content: "\eb25";
}
.cp-group:before {
  content: "\eb26";
}
.cp-infoblox:before {
  content: "\eb27";
}
.cp-java-soft-layers:before {
  content: "\eb28";
}
.cp-Parallel:before {
  content: "\eb2a";
}
.cp-paste:before {
  content: "\eb2b";
}
.cp-power-cli:before {
  content: "\eb2c";
}
.cp-Soft-layers:before {
  content: "\eb2d";
}
.cp-ungroup:before {
  content: "\eb2e";
}
.cp-Unparallel:before {
  content: "\eb2f";
}
.cp-Veeam:before {
  content: "\eb30";
}
.cp-veritas-cluster:before {
  content: "\eb31";
}
.cp-compare:before {
  content: "\eb32";
}
.cp-custom:before {
  content: "\eb33";
}
.cp-cut:before {
  content: "\eb34";
}
.cp-dublicate:before {
  content: "\eb35";
}
.cp-dynamic_monitoring:before {
  content: "\eb36";
}
.cp-infra-replication-mapping:before {
  content: "\eb1a";
}
.cp-three-line:before {
  content: "\eb14";
}
.cp-folder-search:before {
  content: "\eab9";
}
.cp-hds:before {
  content: "\eb0f";
}
.cp-ms-exchange .path1:before {
  content: "\eb10";
  color: rgb(0, 0, 0);
}
.cp-ms-exchange .path2:before {
  content: "\eb11";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cp-router:before {
  content: "\eb12";
}
.cp-servers:before {
  content: "\eb13";
}
.cp-switch:before {
  content: "\eb15";
}
.cp-vmware:before {
  content: "\eb16";
}
.cp-database-refresh:before {
  content: "\eb17";
}
.cp-database-unique-name:before {
  content: "\eb18";
}
.cp-file-calender:before {
  content: "\eb19";
}
.cp-server-authentication:before {
  content: "\eb1b";
}
.cp-SRL:before {
  content: "\eb1c";
}
.cp-aix:before {
  content: "\eb1d";
}
.cp-data-switch:before {
  content: "\eb1e";
}
.cp-db2 .path1:before {
  content: "\eb1f";
  color: rgb(0, 0, 0);
}
.cp-db2 .path2:before {
  content: "\eb20";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cp-db2 .path3:before {
  content: "\eb21";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}
.cp-db2 .path4:before {
  content: "\eb22";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cp-db2 .path5:before {
  content: "\eb23";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cp-db2 .path6:before {
  content: "\eb24";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}
.cp-analytics:before {
  content: "\eb08";
}
.cp-command-center:before {
  content: "\eb09";
}
.cp-custom-server-2:before {
  content: "\eb0a";
}
.cp-database-sizes:before {
  content: "\eb0b";
}
.cp-dc-mapping:before {
  content: "\eb0c";
}
.cp-it-view:before {
  content: "\eb0d";
}
.cp-PDBs:before {
  content: "\eb0e";
}
.cp-LDAPs:before {
  content: "\ead6";
}
.cp-NTLMs:before {
  content: "\eadd";
}
.cp-SAMLs:before {
  content: "\eae8";
}
.cp-KERBOSEs:before {
  content: "\ead5";
}
.cp-custom-server-1:before {
  content: "\eab8";
}
.cp-badge:before {
  content: "\eaad";
}
.cp-custom-server-4:before {
  content: "\eabb";
}
.cp-custom-server-3:before {
  content: "\eaba";
}
.cp-affected .path1:before {
  content: "\eaa6";
  color: rgb(0, 0, 0);
}
.cp-affected .path2:before {
  content: "\eaa7";
  margin-left: -1em;
  color: rgb(218, 218, 218);
}
.cp-affected .path3:before {
  content: "\eaa8";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}
.cp-affected .path4:before {
  content: "\eaa9";
  margin-left: -1em;
  color: rgb(218, 218, 218);
}
.cp-arrow-page:before {
  content: "\eaaa";
}
.cp-authentication:before {
  content: "\eaab";
}
.cp-auxiliary-cluster-name:before {
  content: "\eaac";
}
.cp-box-double-arrow:before {
  content: "\eaae";
}
.cp-box-top-arrow:before {
  content: "\eaaf";
}
.cp-business-view-dashboard-icon:before {
  content: "\eab0";
}
.cp-carbon-instance-virtual:before {
  content: "\eab1";
}
.cp-card:before {
  content: "\eab2";
}
.cp-chain:before {
  content: "\eab3";
}
.cp-chain-settings:before {
  content: "\eab4";
}
.cp-Character-arrow:before {
  content: "\eab5";
}
.cp-compress:before {
  content: "\eab6";
}
.cp-controller-product-ID:before {
  content: "\eab7";
}
.cp-database-page:before {
  content: "\eabc";
}
.cp-database-success:before {
  content: "\eabd";
}
.cp-database-time:before {
  content: "\eabe";
}
.cp-database-warning:before {
  content: "\eabf";
}
.cp-degrade-disk:before {
  content: "\eac0";
}
.cp-disk-controller-name:before {
  content: "\eac1";
}
.cp-disk-controller:before {
  content: "\eac2";
}
.cp-executed-workflow-1:before {
  content: "\eac3";
}
.cp-executed-workflow:before {
  content: "\eac4";
}
.cp-file-c:before {
  content: "\eac5";
}
.cp-file-copy-job-execution:before {
  content: "\eac6";
}
.cp-file-edits:before {
  content: "\eac7";
}
.cp-file-size:before {
  content: "\eac8";
}
.cp-folder-file:before {
  content: "\eac9";
}
.cp-folder-server:before {
  content: "\eaca";
}
.cp-form-mapping:before {
  content: "\eacb";
}
.cp-form-name:before {
  content: "\eacc";
}
.cp-freeze-time:before {
  content: "\eacd";
}
.cp-globe-www:before {
  content: "\eace";
}
.cp-globe-settings:before {
  content: "\eacf";
}
.cp-group-policy:before {
  content: "\ead0";
}
.cp-group-policies:before {
  content: "\ead1";
}
.cp-hand1:before {
  content: "\ead2";
}
.cp-icon-park-solid_group:before {
  content: "\ead3";
}
.cp-idea-off:before {
  content: "\ead4";
}
.cp-license-key:before {
  content: "\ead7";
}
.cp-location-down:before {
  content: "\ead8";
}
.cp-machine-type:before {
  content: "\ead9";
}
.cp-master-change:before {
  content: "\eadb";
}
.cp-mirror:before {
  content: "\eadc";
}
.cp-open-lock1:before {
  content: "\eade";
}
.cp-page:before {
  content: "\eae0";
}
.cp-parent-company:before {
  content: "\eae1";
}
.cp-park-solid-group:before {
  content: "\eae2";
}
.cp-RDFG:before {
  content: "\eae3";
}
.cp-relationship-progress:before {
  content: "\eae4";
}
.cp-relationship-state:before {
  content: "\eae5";
}
.cp-replication-source:before {
  content: "\eae6";
}
.cp-retry:before {
  content: "\eae7";
}
.cp-secondary-server:before {
  content: "\eae9";
}
.cp-server-ip:before {
  content: "\eaea";
}
.cp-server-times:before {
  content: "\eaeb";
}
.cp-single-dot:before {
  content: "\eaec";
}
.cp-site-admin:before {
  content: "\eaed";
}
.cp-sms-gateway:before {
  content: "\eaee";
}
.cp-Storage-chain:before {
  content: "\eaef";
}
.cp-storage-one:before {
  content: "\eaf0";
}
.cp-Storage-three:before {
  content: "\eaf1";
}
.cp-synbase-backup-server:before {
  content: "\eaf3";
}
.cp-test-connection:before {
  content: "\eaf4";
}
.cp-thread:before {
  content: "\eaf5";
}
.cp-three-dots:before {
  content: "\eaf6";
}
.cp-three-lines:before {
  content: "\eaf7";
}
.cp-time-one:before {
  content: "\eaf8";
}
.cp-top-cloud-server:before {
  content: "\eaf9";
}
.cp-two-point:before {
  content: "\eafa";
}
.cp-un-link:before {
  content: "\eafb";
}
.cp-non-publish:before {
  content: "\eafc";
}
.cp-upload-up:before {
  content: "\eafd";
}
.cp-upload:before {
  content: "\eafe";
}
.cp-user-n:before {
  content: "\eaff";
}
.cp-user-one:before {
  content: "\eb00";
}
.cp-user-refresh:before {
  content: "\eb01";
}
.cp-user-status:before {
  content: "\eb02";
}
.cp-version:before {
  content: "\eb03";
}
.cp-volume-adjustment:before {
  content: "\eb04";
}
.cp-waveform:before {
  content: "\eb05";
}
.cp-weather-snow-flake:before {
  content: "\eb06";
}
.cp-zip:before {
  content: "\eb07";
}
.cp-moon:before {
  content: "\ea90";
}
.cp-sun:before {
  content: "\ea93";
}
.cp-check:before {
  content: "\e900";
}
.cp-close:before {
  content: "\e901";
}
.cp-equal:before {
  content: "\e902";
}
.cp-subtract:before {
  content: "\e903";
}
.cp-circle-minus:before {
  content: "\e904";
}
.cp-circle-plus:before {
  content: "\e905";
}
.cp-play:before {
  content: "\e979";
}
.cp-pause:before {
  content: "\e983";
}
.cp-play-next:before {
  content: "\e9b2";
}
.cp-play-previous:before {
  content: "\ea60";
}
.cp-thunder:before {
  content: "\e94e";
}
.cp-circle-thunder:before {
  content: "\ea8a";
}
.cp-circle-play:before {
  content: "\e906";
}
.cp-circle-pause:before {
  content: "\e907";
}
.cp-circle-playnext:before {
  content: "\e908";
}
.cp-circle-playprevious:before {
  content: "\e909";
}
.cp-Actions:before {
  content: "\e90a";
}
.cp-back:before {
  content: "\e90b";
}
.cp-front:before {
  content: "\e90c";
}
.cp-critical-level:before {
  content: "\e90d";
}
.cp-circle-arrowleft:before {
  content: "\ea97";
}
.cp-circle-arrowright:before {
  content: "\ea98";
}
.cp-off-arrow:before {
  content: "\e90e";
}
.cp-on-arrow:before {
  content: "\e90f";
}
.cp-server-down:before {
  content: "\e910";
}
.cp-server-up:before {
  content: "\e911";
}
.cp-add:before {
  content: "\e912";
}
.cp-left-arrow:before {
  content: "\e913";
}
.cp-rignt-arrow:before {
  content: "\e914";
}
.cp-up-arrow:before {
  content: "\e915";
}
.cp-down-arrow:before {
  content: "\e916";
}
.cp-circle-downarrow:before {
  content: "\eaa1";
}
.cp-circle-leftarrow:before {
  content: "\eaa2";
}
.cp-circle-rightarrow:before {
  content: "\eaa3";
}
.cp-circle-uparrow:before {
  content: "\eaa4";
}
.cp-left-doublearrow:before {
  content: "\e917";
}
.cp-right-doublearrow:before {
  content: "\e918";
}
.cp-down-doublearrow:before {
  content: "\e919";
}
.cp-up-doublearrow:before {
  content: "\e91a";
}
.cp-success-rate:before {
  content: "\ea92";
}
.cp-mark-action1:before {
  content: "\ea9f";
}
.cp-toggle:before {
  content: "\ea63";
}
.cp-down-arrow-fill:before {
  content: "\e91b";
}
.cp-up-arrow-fill:before {
  content: "\e91c";
}
.cp-left-arrow-fill:before {
  content: "\e91d";
}
.cp-right-arrow-fill:before {
  content: "\e91e";
}
.cp-logs:before {
  content: "\e91f";
}
.cp-download:before {
  content: "\e920";
}
.cp-Unpublish:before {
  content: "\eaa0";
}
.cp-filter:before {
  content: "\e921";
}
.cp-user:before {
  content: "\e922";
}
.cp-notification:before {
  content: "\e924";
}
.cp-user-profile:before {
  content: "\e925";
}
.cp-lock:before {
  content: "\e927";
}
.cp-open-lock:before {
  content: "\e99c";
}
.cp-copy:before {
  content: "\e928";
}
.cp-location:before {
  content: "\e929";
}
.cp-wifi:before {
  content: "\e926";
}
.cp-alerts-head:before {
  content: "\e92a";
}
.cp-zoom-out:before {
  content: "\e92b";
}
.cp-zoom-in:before {
  content: "\e92c";
}
.cp-edit:before {
  content: "\e92d";
}
.cp-Delete:before {
  content: "\e92e";
}
.cp-key:before {
  content: "\e92f";
}
.cp-full-screen:before {
  content: "\e9f8";
}
.cp-round-star:before {
  content: "\ea8c";
}
.cp-round-star-fill:before {
  content: "\ea8d";
}
.cp-email:before {
  content: "\e930";
}
.cp-print:before {
  content: "\e931";
}
.cp-phone:before {
  content: "\e932";
}
.cp-dots-drag:before {
  content: "\ea9d";
}
.cp-product-activated:before {
  content: "\e933";
}
.cp-calendar:before {
  content: "\e934";
}
.cp-send-message:before {
  content: "\e935";
}
.cp-search:before {
  content: "\e936";
}
.cp-disagree:before {
  content: "\e937";
}
.cp-agree:before {
  content: "\e938";
}
.cp-horizontal-dots:before {
  content: "\e939";
}
.cp-vertical-dots:before {
  content: "\e93a";
}
.cp-up-linearrow:before {
  content: "\e93b";
}
.cp-down-linearrow:before {
  content: "\e93c";
}
.cp-left-linearrow:before {
  content: "\e93d";
}
.cp-right-linearrow:before {
  content: "\e93e";
}
.cp-left-right:before {
  content: "\e93f";
}
.cp-right-left:before {
  content: "\e940";
}
.cp-Impact:before {
  content: "\e941";
}
.cp-exclamation:before {
  content: "\e942";
}
.cp-note:before {
  content: "\e943";
}
.cp-question-mark:before {
  content: "\e944";
}
.cp-warning:before {
  content: "\e945";
}
.cp-error:before {
  content: "\e946";
}
.cp-disable:before {
  content: "\e947";
}
.cp-success:before {
  content: "\e948";
}
.cp-timer-meter:before {
  content: "\ea94";
}
.cp-Unhealthy:before {
  content: "\ea95";
}
.cp-Health:before {
  content: "\e949";
}
.cp-workflow-p:before {
  content: "\ea9c";
}
.cp-matrix-list:before {
  content: "\e94a";
}
.cp-protection-mode:before {
  content: "\e94b";
}
.cp-idea:before {
  content: "\e94c";
}
.cp-solution-template:before {
  content: "\e94d";
}
.cp-shiled-on:before {
  content: "\e94f";
}
.cp-shiled-off:before {
  content: "\e950";
}
.cp-lucide-error:before {
  content: "\e951";
}
.cp-lucid:before {
  content: "\e952";
}
.cp-lucide-question-mark:before {
  content: "\e953";
}
.cp-lucide-success:before {
  content: "\e954";
}
.cp-health-success:before {
  content: "\e955";
}
.cp-health-error:before {
  content: "\e956";
}
.cp-health-warning:before {
  content: "\e957";
}
.cp-password-hide:before {
  content: "\e958";
}
.cp-password-visible:before {
  content: "\e959";
}
.cp-Manage-polygon-shape:before {
  content: "\ea9a";
}
.cp-access-manager:before {
  content: "\e95a";
}
.cp-action-builder:before {
  content: "\e95b";
}
.cp-ports:before {
  content: "\e9d0";
}
.cp-flow:before {
  content: "\e95c";
}
.cp-circle-workflow:before {
  content: "\e95d";
}
.cp-infra-omponent:before {
  content: "\e95e";
}
.cp-endpoint-port-number:before {
  content: "\e95f";
}
.cp-workflow-configuration:before {
  content: "\e960";
}
.cp-it_orchestration:before {
  content: "\e961";
}
.cp-cluster-database:before {
  content: "\e962";
}
.cp-network:before {
  content: "\e963";
}
.cp-new:before {
  content: "\e964";
}
.cp-link-off:before {
  content: "\e965";
}
.cp-link-on:before {
  content: "\e966";
}
.cp-url:before {
  content: "\e967";
}
.cp-description:before {
  content: "\e968";
}
.cp-name:before {
  content: "\e969";
}
.cp-activity-type:before {
  content: "\e96b";
}
.cp-add-new:before {
  content: "\e96c";
}
.cp-alerts:before {
  content: "\e96d";
}
.cp-amazon:before {
  content: "\e96e";
}
.cp-api:before {
  content: "\e96f";
}
.cp-application:before {
  content: "\e970";
}
.cp-apply-lag:before {
  content: "\e972";
}
.cp-form-builder:before {
  content: "\e973";
}
.cp-replication-on:before {
  content: "\e976";
}
.cp-replication-rotate:before {
  content: "\e977";
}
.cp-replication-Off:before {
  content: "\e978";
}
.cp-currency:before {
  content: "\e97a";
}
.cp-list-prsite:before {
  content: "\e97b";
}
.cp-data:before {
  content: "\e97c";
}
.cp-approval-matrix:before {
  content: "\e97d";
}
.cp-list-neardrsite:before {
  content: "\e97e";
}
.cp-database-role:before {
  content: "\e97f";
}
.cp-prsite:before {
  content: "\e980";
}
.cp-rds-name:before {
  content: "\e981";
}
.cp-database-sid:before {
  content: "\e982";
}
.cp-db-create-online:before {
  content: "\e984";
}
.cp-db-server-db-name:before {
  content: "\e985";
}
.cp-mysql-data:before {
  content: "\e986";
}
.cp-database-type:before {
  content: "\e987";
}
.cp-archive:before {
  content: "\e988";
}
.cp-infra-object:before {
  content: "\e989";
}
.cp-virtualization:before {
  content: "\e98a";
}
.cp-log-sequence:before {
  content: "\e98b";
}
.cp-backup-job-xecution:before {
  content: "\e98c";
}
.cp-log-archive-config:before {
  content: "\e98d";
}
.cp-workflow-profile:before {
  content: "\e98e";
}
.cp-settings:before {
  content: "\e98f";
}
.cp-workflow-execution:before {
  content: "\e990";
}
.cp-server-role:before {
  content: "\e991";
}
.cp-roate-settings:before {
  content: "\e992";
}
.cp-workflow-list:before {
  content: "\e993";
}
.cp-dr-readiness:before {
  content: "\e994";
}
.cp-manage-business-service:before {
  content: "\e995";
}
.cp-configure-settings:before {
  content: "\e996";
}
.cp-documentfilled:before {
  content: "\e997";
}
.cp-legend:before {
  content: "\e998";
}
.cp-configure-dataset:before {
  content: "\e999";
}
.cp-user-details:before {
  content: "\e99a";
}
.cp-notification-manager-1:before {
  content: "\e99b";
}
.cp-notification-manager:before {
  content: "\e99d";
}
.cp-book:before {
  content: "\e99f";
}
.cp-box-none:before {
  content: "\e9a0";
}
.cp-Product-id:before {
  content: "\e9a1";
}
.cp-business-service:before {
  content: "\e9a2";
}
.cp-bag:before {
  content: "\e9a3";
}
.cp-bag-settings:before {
  content: "\e9a4";
}
.cp-job-reset:before {
  content: "\ea99";
}
.cp-business-function-1:before {
  content: "\e9a7";
}
.cp-business-function-off:before {
  content: "\e9a8";
}
.cp-business-function-on:before {
  content: "\ea96";
}
.cp-business-function:before {
  content: "\e9a9";
}
.cp-instance-name:before {
  content: "\e9aa";
}
.cp-Workflow-action-result:before {
  content: "\e9ab";
}
.cp-calculator:before {
  content: "\e9ac";
}
.cp-chat-bot:before {
  content: "\e9ad";
}
.cp-chat:before {
  content: "\e9ae";
}
.cp-archive-mode:before {
  content: "\e9af";
}
.cp-cloud-server:before {
  content: "\e9b0";
}
.cp-cloud:before {
  content: "\e9b1";
}
.cp-company:before {
  content: "\e9b3";
}
.cp-condition:before {
  content: "\e9b4";
}
.cp-replication-type:before {
  content: "\e9b5";
}
.cp-configure:before {
  content: "\e9b6";
}
.cp-control-file-name:before {
  content: "\e9b7";
}
.cp-control-file-type:before {
  content: "\e9b8";
}
.cp-workflow-type:before {
  content: "\ea8f";
}
.cp-credential-profile:before {
  content: "\e9b9";
}
.cp-current-scn:before {
  content: "\e9ba";
}
.cp-dashboard:before {
  content: "\e9bb";
}
.cp-data-lag:before {
  content: "\e9bc";
}
.cp-database-access:before {
  content: "\e9bd";
}
.cp-database-size:before {
  content: "\e9be";
}
.cp-database-unique:before {
  content: "\e9bf";
}
.cp-database-updatability:before {
  content: "\e9c0";
}
.cp-database:before {
  content: "\e9c1";
}
.cp-stand-storage:before {
  content: "\e9c2";
}
.cp-active-dg-enable:before {
  content: "\e9c3";
}
.cp-production-server-db-name:before {
  content: "\e9c4";
}
.cp-virtual:before {
  content: "\e9c5";
}
.cp-folder:before {
  content: "\e9c6";
}
.cp-production-server-name:before {
  content: "\e9c7";
}
.cp-sun-ilom:before {
  content: "\e9c8";
}
.cp-storage:before {
  content: "\e9c9";
}
.cp-storage-name:before {
  content: "\e9ca";
}
.cp-server:before {
  content: "\ea5c";
}
.cp-production-server-ip:before {
  content: "\e9cb";
}
.cp-stand-server:before {
  content: "\e9cc";
}
.cp-stand-database:before {
  content: "\e9cd";
}
.cp-dataguard-status:before {
  content: "\e9ce";
}
.cp-LDAP:before {
  content: "\e9d1";
}
.cp-user_role:before {
  content: "\e9d2";
}
.cp-service:before {
  content: "\e9d3";
}
.cp-teams:before {
  content: "\e9d4";
}
.cp-user-role:before {
  content: "\eb6e";
}
.cp-ad-configuration:before {
  content: "\e9d7";
}
.cp-users-rectangle:before {
  content: "\e9d8";
}
.cp-user-hcard:before {
  content: "\ea8b";
}
.cp-third-party:before {
  content: "\e9d9";
}
.cp-NTLM:before {
  content: "\e9da";
}
.cp-datas:before {
  content: "\e9db";
}
.cp-day:before {
  content: "\e9dc";
}
.cp-db-create-file-dest:before {
  content: "\e9dd";
}
.cp-decimal:before {
  content: "\e9de";
}
.cp-derived-licence:before {
  content: "\e9df";
}
.cp-dollar:before {
  content: "\e9e0";
}
.cp-dr-calendar:before {
  content: "\e9e1";
}
.cp-dr-dubai:before {
  content: "\e9e2";
}
.cp-dr:before {
  content: "\e9e4";
}
.cp-ebdr:before {
  content: "\e9e8";
}
.cp-dataset-form:before {
  content: "\e9e5";
}
.cp-backup_data:before {
  content: "\e99e";
}
.cp-import:before {
  content: "\e96a";
}
.cp-export:before {
  content: "\e9ea";
}
.cp-insert-left:before {
  content: "\e9fb";
}
.cp-bulk-import:before {
  content: "\e9a6";
}
.cp-box-success:before {
  content: "\e9a5";
}
.cp-insert-right:before {
  content: "\e9fc";
}
.cp-duplicate-replication:before {
  content: "\e9e7";
}
.cp-drill-action-type:before {
  content: "\e9e6";
}
.cp-escalation_matrix_header-icon-3:before {
  content: "\e9e9";
}
.cp-face-Detection:before {
  content: "\e9eb";
}
.cp-fal-client:before {
  content: "\e9ec";
}
.cp-fal-server:before {
  content: "\e9ed";
}
.cp-format:before {
  content: "\e9ee";
}
.cp-generate:before {
  content: "\e9f0";
}
.cp-goldengate:before {
  content: "\e9f1";
}
.cp-hand:before {
  content: "\e9f2";
}
.cp-health-Down:before {
  content: "\e9f3";
}
.cp-health-Up:before {
  content: "\e9f4";
}
.cp-host-name:before {
  content: "\e9f5";
}
.cp-if-condition:before {
  content: "\e9f6";
}
.cp-images:before {
  content: "\e9f7";
}
.cp-infraobjects:before {
  content: "\e9f9";
}
.cp-grid:before {
  content: "\e9fa";
}
.cp-instance-id:before {
  content: "\e9fd";
}
.cp-instant-name:before {
  content: "\e9fe";
}
.cp-ip-address:before {
  content: "\e9ff";
}
.cp-job-list:before {
  content: "\ea00";
}
.cp-job-management:before {
  content: "\ea01";
}
.cp-Job-status:before {
  content: "\ea02";
}
.cp-KERBOSE:before {
  content: "\ea03";
}
.cp-label:before {
  content: "\ea04";
}
.cp-last-backup:before {
  content: "\ea05";
}
.cp-last-copied-dataandtime:before {
  content: "\ea06";
}
.cp-file-lock:before {
  content: "\ea9e";
}
.cp-alert-dashboard:before {
  content: "\ea07";
}
.cp-last-copied-transaction:before {
  content: "\ea08";
}
.cp-files:before {
  content: "\ea09";
}
.cp-file-location:before {
  content: "\ea0a";
}
.cp-prebuild-reports:before {
  content: "\ea0b";
}
.cp-approval-matrix-header-icon:before {
  content: "\ea0c";
}
.cp-edit-user:before {
  content: "\ea91";
}
.cp-remote-login:before {
  content: "\ea0d";
}
.cp-parameter-file:before {
  content: "\ea0e";
}
.cp-table-accesslist:before {
  content: "\e9e3";
}
.cp-team-member-list:before {
  content: "\ea0f";
}
.cp-replication-connect:before {
  content: "\ea10";
}
.cp-team-list:before {
  content: "\ea11";
}
.cp-custom-reports:before {
  content: "\ea12";
}
.cp-upload-file:before {
  content: "\ea13";
}
.cp-user-configuration:before {
  content: "\ea14";
}
.cp-summary:before {
  content: "\ea15";
}
.cp-report-location:before {
  content: "\ea16";
}
.cp-permission:before {
  content: "\ea17";
}
.cp-report-board:before {
  content: "\ea18";
}
.cp-note-pad:before {
  content: "\ea9b";
}
.cp-rvg-name:before {
  content: "\ea19";
}
.cp-process:before {
  content: "\ea1a";
}
.cp-standby-redo-logs:before {
  content: "\ea1b";
}
.cp-affirm:before {
  content: "\ea1c";
}
.cp-log-viewer:before {
  content: "\ea1d";
}
.cp-level-etails:before {
  content: "\ea1e";
}
.cp-license-manager:before {
  content: "\ea1f";
}
.cp-list-cloud:before {
  content: "\ea20";
}
.cp-list-drsite:before {
  content: "\ea21";
}
.cp-load-balancer:before {
  content: "\ea22";
}
.cp-log-file-name:before {
  content: "\ea23";
}
.cp-log:before {
  content: "\ea24";
}
.cp-os-type:before {
  content: "\ea25";
}
.cp-formtype:before {
  content: "\e9ef";
}
.cp-microsoft:before {
  content: "\ea26";
}
.cp-Monitor:before {
  content: "\ea27";
}
.cp-monitoring-services:before {
  content: "\ea28";
}
.cp-monitoring:before {
  content: "\ea29";
}
.cp-plugin-manager:before {
  content: "\ea2a";
}
.cp-template-store:before {
  content: "\ea2b";
}
.cp-production:before {
  content: "\ea2c";
}
.cp-display-name:before {
  content: "\ea2d";
}
.cp-mailing-system:before {
  content: "\ea2e";
}
.cp-hcl:before {
  content: "\ea2f";
}
.cp-maintenance-Mode:before {
  content: "\ea30";
}
.cp-maintenance:before {
  content: "\ea31";
}
.cp-manage:before {
  content: "\ea32";
}
.cp-manager:before {
  content: "\ea33";
}
.cp-mark-action:before {
  content: "\ea34";
}
.cp-mobile-icon:before {
  content: "\ea35";
}
.cp-mobile-otp:before {
  content: "\ea36";
}
.cp-near-dr-mu:before {
  content: "\ea37";
}
.cp-number:before {
  content: "\ea38";
}
.cp-refresh-clock:before {
  content: "\ea39";
}
.cp-refresh:before {
  content: "\ea3a";
}
.cp-ohas-status:before {
  content: "\ea3b";
}
.cp-table-clock:before {
  content: "\ea3c";
}
.cp-reminder:before {
  content: "\ea3d";
}
.cp-reload:before {
  content: "\ea3e";
}
.cp-transport-lag:before {
  content: "\ea3f";
}
.cp-transaction:before {
  content: "\ea40";
}
.cp-time:before {
  content: "\ea41";
}
.cp-speed-meter:before {
  content: "\ea42";
}
.cp-update:before {
  content: "\ea43";
}
.cp-estimated-time:before {
  content: "\e975";
}
.cp-apply-finish-time:before {
  content: "\e971";
}
.cp-open-mode:before {
  content: "\ea44";
}
.cp-open:before {
  content: "\ea45";
}
.cp-oracle-ops:before {
  content: "\ea46";
}
.cp-password-age:before {
  content: "\ea47";
}
.cp-percent:before {
  content: "\ea48";
}
.cp-physical:before {
  content: "\ea49";
}
.cp-platform-name:before {
  content: "\ea4a";
}
.cp-port:before {
  content: "\ea4b";
}
.cp-platform:before {
  content: "\ea4c";
}
.cp-manage-server:before {
  content: "\ea4d";
}
.cp-pr:before {
  content: "\ea4e";
}
.cp-preview:before {
  content: "\ea4f";
}
.cp-priority:before {
  content: "\ea50";
}
.cp-product-version-icon:before {
  content: "\ea51";
}
.cp-About-header-icon:before {
  content: "\ea52";
}
.cp-publish:before {
  content: "\ea53";
}
.cp-report-path:before {
  content: "\ea54";
}
.cp-report:before {
  content: "\ea55";
}
.cp-reset-log-change:before {
  content: "\ea56";
}
.cp-rsync:before {
  content: "\ea57";
}
.cp-run:before {
  content: "\ea58";
}
.cp-save:before {
  content: "\ea59";
}
.cp-server-cloud:before {
  content: "\ea5a";
}
.cp-server-type:before {
  content: "\ea5b";
}
.cp-switch-back:before {
  content: "\ea8e";
}
.cp-switch-over:before {
  content: "\e974";
}
.cp-sign-on-type:before {
  content: "\ea5d";
}
.cp-signal:before {
  content: "\ea5e";
}
.cp-single-sign_on:before {
  content: "\ea5f";
}
.cp-solution:before {
  content: "\ea61";
}
.cp-standby-file:before {
  content: "\ea62";
}
.cp-system-management-tool:before {
  content: "\ea64";
}
.cp-table-date:before {
  content: "\ea65";
}
.cp-table:before {
  content: "\ea66";
}
.cp-target_archieve:before {
  content: "\ea67";
}
.cp-text-area:before {
  content: "\ea68";
}
.cp-text:before {
  content: "\ea69";
}
.cp-Timeout:before {
  content: "\ea6a";
}
.cp-web:before {
  content: "\ea6b";
}
.cp-wf-profile:before {
  content: "\ea6c";
}
.cp-zip-file:before {
  content: "\ea6d";
}
.cp-hp:before {
  content: "\ea6e";
}
.cp-mssql:before {
  content: "\ea6f";
}
.cp-windows:before {
  content: "\ea70";
}
.cp-exchange:before {
  content: "\ea71";
}
.cp-soft-layer:before {
  content: "\ea72";
}
.cp-linux:before {
  content: "\ea73";
}
.cp-nutanix:before {
  content: "\ea75";
}
.cp-oracle:before {
  content: "\ea76";
}
.cp-postgres:before {
  content: "\ea77";
}
.cp-window-line:before {
  content: "\ea78";
}
.cp-mysql:before {
  content: "\ea79";
}
.cp-virtual-drsite:before {
  content: "\ea7a";
}
.cp-virtual-prsite:before {
  content: "\ea7b";
}
.cp-virtual-neardrsite:before {
  content: "\ea7c";
}
.cp-prsites:before {
  content: "\eb51";
}
.cp-physical-neardrsite:before {
  content: "\ea7d";
}
.cp-next-drsite:before {
  content: "\ea7e";
}
.cp-hcl-neardrsite:before {
  content: "\ea7f";
}
.cp-hcl-drsite:before {
  content: "\ea80";
}
.cp-hcl-prsite:before {
  content: "\ea81";
}
.cp-backup-data:before {
  content: "\ea82";
}
.cp-cloud-drsite:before {
  content: "\ea83";
}
.cp-cloud-prsite:before {
  content: "\ea84";
}
.cp-cloud-neardrsite:before {
  content: "\ea85";
}
.cp-physical-drsite:before {
  content: "\ea86";
}
.cp-custom-site-type:before {
  content: "\ea88";
}
.cp-scheduled-data:before {
  content: "\ea89";
}
.cp-cloud-connect:before {
  content: "\ebd9";
}
.cp-resiliency-readiness:before {
  content: "\ebd8";
}
.cp-cyber-recovery:before {
  content: "\ebda";
}
.cp-IT-automation:before {
  content: "\ebdb";
}
.cp-aborted:before {
  content: "\ebd1";
}
.cp-attach-escalation:before {
  content: "\ebd2";
}
.cp-ssh-private-key:before {
  content: "\ebc2";
}
.cp-advanced-filter:before {
  content: "\ebc4";
}
.cp-advanced-options:before {
  content: "\ebc5";
}
.cp-copy-option:before {
  content: "\ebc6";
}
.cp-deletion-filter-option:before {
  content: "\ebc7";
}
.cp-enable-ssh-private-key:before {
  content: "\ebc8";
}
.cp-exclude:before {
  content: "\ebc9";
}
.cp-file-not-to-delete:before {
  content: "\ebca";
}
.cp-folder-permission:before {
  content: "\ebcb";
}
.cp-include:before {
  content: "\ec80";
}
.cp-incremental-replication:before {
  content: "\ec81";
}
.cp-options-selection:before {
  content: "\ec82";
}
.cp-parallel-replication:before {
  content: "\ec83";
}
.cp-shell-prompt:before {
  content: "\ec84";
}
.cp-drift-management:before {
  content: "\ec85";
}
.cp-circle-up-linearrow:before {
  content: "\ebcc";
}
.cp-complaints:before {
  content: "\ebcd";
}
.cp-circle-down-linearrow:before {
  content: "\ebce";
}
.cp-circle-left-linearrow:before {
  content: "\ebcf";
}
.cp-circle-right-linearrow:before {
  content: "\ebd0";
}
.cp-solaris:before {
  content: "\ec6f";
}
.cp-disconnecteds:before {
  content: "\ec71";
}
.cp-foureye-approval:before {
  content: "\ec72";
}
.cp-unsync:before {
  content: "\ec73";
}
.cp-idling-disconnected:before {
  content: "\ec74";
}
.cp-error-fill:before {
  content: "\ec75";
}
.cp-note-fill:before {
  content: "\ec76";
}
.cp-question-mark-fill:before {
  content: "\ec77";
}
.cp-success-fill:before {
  content: "\ec78";
}
.cp-warning-fill:before {
  content: "\ec79";
}
.cp-check-fill:before {
  content: "\ec7a";
}
.cp-checks:before {
  content: "\ec7b";
}
.cp-uncheck:before {
  content: "\ec7c";
}
.cp-bubble-chart:before {
  content: "\ec7d";
}
.cp-DNS:before {
  content: "\ec7e";
}
.cp-donut-chart:before {
  content: "\ec7f";
}
.cp-line-chart:before {
  content: "\ec86";
}
.cp-pie-chart:before {
  content: "\ec87";
}
.cp-alerts-chart:before {
  content: "\ec88";
}
.cp-bar-chart:before {
  content: "\ec89";
}
.cp-dashboard-left-arrow:before {
  content: "\ec8a";
}
.cp-dashboard-right-arrow:before {
  content: "\ec8b";
}
.cp-dashboard-down:before {
  content: "\ec8c";
}
.cp-radio:before {
  content: "\ec8d";
}
.cp-BIA-rules:before {
  content: "\ec8e";
}
