﻿using ContinuityPatrol.Application.Features.TeamMaster.Events.Delete;

namespace ContinuityPatrol.Application.Features.TeamMaster.Commands.Delete;

public class DeleteTeamMasterCommandHandler : IRequestHandler<DeleteTeamMasterCommand, DeleteTeamMasterResponse>
{
    private readonly IPublisher _publisher;
    private readonly ITeamMasterRepository _teamMasterRepository;

    public DeleteTeamMasterCommandHandler(ITeamMasterRepository teamMasterRepository, IPublisher publisher)
    {
        _teamMasterRepository = teamMasterRepository;
        _publisher = publisher;
    }

    public async Task<DeleteTeamMasterResponse> Handle(DeleteTeamMasterCommand request,
        CancellationToken cancellationToken)
    {
        var teamMasterToDelete = await _teamMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(teamMasterToDelete, nameof(Domain.Entities.TeamMaster),
            new NotFoundException(nameof(Domain.Entities.TeamMaster), request.Id));

        teamMasterToDelete.IsActive = false;

        await _teamMasterRepository.UpdateAsync(teamMasterToDelete);

        var response = new DeleteTeamMasterResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.TeamMaster), teamMasterToDelete.GroupName),

            IsActive = teamMasterToDelete.IsActive
        };
        await _publisher.Publish(new TeamMasterDeletedEvent { GroupName = teamMasterToDelete.GroupName },
            cancellationToken);


        return response;
    }
}