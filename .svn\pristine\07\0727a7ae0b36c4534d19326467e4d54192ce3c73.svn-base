﻿namespace ContinuityPatrol.Domain.Entities;

public class Workflow : AuditableEntity
{
    public string Name { get; set; }
    public string CompanyId { get; set; }

    [Column(TypeName = "NCLOB")] public string Properties { get; set; }

    public bool IsVerify { get; set; }
    public bool IsLock { get; set; }
    public bool IsPublish { get; set; }
    public string Version { get; set; }
    public bool IsDraft { get; set; }
}