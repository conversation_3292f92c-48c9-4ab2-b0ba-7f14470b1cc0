using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class FormTypeFixture : IDisposable
{
    public List<FormType> FormTypePaginationList { get; set; }
    public List<FormType> FormTypeList { get; set; }
    public FormType FormTypeDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    public FormTypeFixture()
    {
        var fixture = new Fixture();

        FormTypeList = fixture.Create<List<FormType>>();

        FormTypePaginationList = fixture.CreateMany<FormType>(20).ToList();

        // Setup proper test data for FormTypePaginationList
        FormTypePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormTypePaginationList.ForEach(x => x.IsActive = true);
        FormTypePaginationList.ForEach(x => x.IsDelete = false);

        // Setup proper test data for FormTypeList
        FormTypeList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        FormTypeList.ForEach(x => x.IsActive = true);
        FormTypeList.ForEach(x => x.IsDelete = false);

        FormTypeDto = fixture.Create<FormType>();
        FormTypeDto.ReferenceId = Guid.NewGuid().ToString();
        FormTypeDto.IsActive = true;
        FormTypeDto.IsDelete = false;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
