namespace ContinuityPatrol.Application.Features.RoboCopy.Queries.GetDetail;

public class RoboCopyDetailVm
{
    public int Id { get; set; }
    public string ReferenceId { get; set; }
    public string Name { get; set; }
    public string ReplicationType { get; set; }
    public string Properties { get; set; }
    public bool IsActive { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public string LastModifiedBy { get; set; }
    public DateTime LastModifiedDate { get; set; }
}