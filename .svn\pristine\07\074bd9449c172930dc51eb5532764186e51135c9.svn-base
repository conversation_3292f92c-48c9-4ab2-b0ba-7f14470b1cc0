﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Events.Delete;

namespace ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Delete;

public class
    DeleteWorkflowCategoryCommandHandler : IRequestHandler<DeleteWorkflowCategoryCommand,
        DeleteWorkflowCategoryResponse>
{
    private readonly IPublisher _publisher;
    private readonly IWorkflowCategoryRepository _workflowCategoryRepository;

    public DeleteWorkflowCategoryCommandHandler(IWorkflowCategoryRepository workflowCategoryRepository,
        IPublisher publisher)
    {
        _workflowCategoryRepository = workflowCategoryRepository;
        _publisher = publisher;
    }

    public async Task<DeleteWorkflowCategoryResponse> Handle(DeleteWorkflowCategoryCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "WorkflowCategory Id");

        var eventToDelete = await _workflowCategoryRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.WorkflowCategory),
            new NotFoundException(nameof(Domain.Entities.WorkflowCategory), request.Id));

        eventToDelete.IsActive = false;

        await _workflowCategoryRepository.UpdateAsync(eventToDelete);

        var response = new DeleteWorkflowCategoryResponse
        {
            Message = Message.Delete("Workflow Category", eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };
        await _publisher.Publish(new WorkflowCategoryDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}