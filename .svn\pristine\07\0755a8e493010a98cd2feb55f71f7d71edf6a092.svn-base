﻿namespace ContinuityPatrol.Domain.Entities;

public class Company : AuditableEntity
{
    [Required] [StringLength(100)] public string Name { get; set; }

    [Required] public string DisplayName { get; set; }

    public bool IsParent { get; set; } = false;

    public string ParentId { get; set; }

    public string WebAddress { get; set; }

    [Column(TypeName = "NCLOB")] public string CompanyLogo { get; set; }

    public string LogoName { get; set; }
}