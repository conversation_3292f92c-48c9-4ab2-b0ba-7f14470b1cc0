﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IUserGroupRepository : IRepository<UserGroup>
{
    Task<List<UserGroup>> GetUserGroupNames();

    Task<bool> IsGroupNameExist(string name, string id);
    Task<bool> IsGroupNameUnique(string name);
    Task<List<UserGroup>> GetUserGroupByUserId(string userId);
}