﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Delete;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetImpactDetails;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class HeatMapStatusController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateHeatMapStatusResponse>> CreateHeatMapStatus(
        [FromBody] CreateHeatMapStatusCommand createHeatMapStatusCommand)
    {
        Logger.LogDebug($"Create HeatMapStatus '{createHeatMapStatusCommand.InfraObjectName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateHeatMapStatus), await Mediator.Send(createHeatMapStatusCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateHeatMapStatusResponse>> UpdateHeatMapStatus(
        [FromBody] UpdateHeatMapStatusCommand updateHeatMapStatusCommand)
    {
        Logger.LogDebug($"Update HeatMapStatus '{updateHeatMapStatusCommand.InfraObjectName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateHeatMapStatusCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteHeatMapStatusResponse>> DeleteHeatMapStatus(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HeatMapStatus Id");

        Logger.LogDebug($"Delete HeatMapStatus Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteHeatMapStatusCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetHeatMapStatus")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<HeatMapStatusDetailVm>> GetHeatMapStatusById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "HeatMapStatus Id");

        Logger.LogDebug($"Get HeatMapStatus Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetHeatMapStatusDetailQuery { Id = id }));
    }


    [HttpGet("GetHeatMapStatusByInfraObjectandEntityId")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<HeatMapStatusByInfraObjectandEntityIdVm>> GetHeatMapStatusByInfraObjectIdAndEntityId(
        string infraObjectId, string entityId)
    {
        Guard.Against.InvalidGuidOrEmpty(infraObjectId, "HeatMapStatus infraObjectId");
        Guard.Against.InvalidGuidOrEmpty(entityId, "HeatMapStatus entityId");

        Logger.LogDebug($"Get HeatMapStatus Detail by Id '{infraObjectId}' & '{entityId}'");

        return Ok(await Mediator.Send(new GetHeatMapStatusByInfraObjectandEntityIdQuery
        { InfraObjectId = infraObjectId, EntityId = entityId }));
    }

    [Route("by/type")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<HeatMapStatusListVm>>> GetHeatMapStatusByType(string? businessServiceId,string type, bool isAffected)
    {
        Logger.LogDebug($"Get HeatMapStatus Details by Type '{type}'");

        return Ok(await Mediator.Send(new GetHeatMapStatusTypeQuery { BusinessServiceId= businessServiceId, Type = type,IsAffected = isAffected }));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<HeatMapStatusListVm>>> GetHeatMapStatus()
    {
        Logger.LogDebug("Get All HeatMapStatus");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllHeatMapStatusCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetHeatMapStatusListQuery()), CacheExpiry));
    }

    [Route("impact-detail")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<ImpactDetailVm>>> GetImpactDetail(string? businessServiceId)
    {
        Logger.LogDebug("Get All HeatMapStatus");

        return Ok(await Mediator.Send(new GetImpactDetailQuery {BusinessServiceId = businessServiceId }));
    }

    [Route("businessService-availability")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<BusinessServiceAvailabilityVm>> GetBusinessServiceAvailability()
    {
        Logger.LogDebug("Get All BusinessServiceAvailability");

        return Ok(await Mediator.Send(new GetBusinessServiceAvailabilityQuery()));
    }

    [Route("businessFunction-availability")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<BusinessFunctionAvailabilityVm>> GetBusinessFunctionAvailability()
    {
        Logger.LogDebug("Get All BusinessFunctionAvailability");

        return Ok(await Mediator.Send(new GetBusinessFunctionAvailabilityQuery()));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<PaginatedResult<HeatMapStatusListVm>>> GetPaginatedHeatMapStatus([FromQuery] GetHeatMapStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in HeatMapStatus Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllHeatMapStatusCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}