﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;
using ContinuityPatrol.Application.Features.InfraSummary.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class InfraSummaryFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<InfraSummary> InfraSummaries { get; set; }

    public InfraSummary InfraSummary { get; set; }
    public CreateInfraSummaryCommand CreateInfraSummaryCommand { get; set; }

    public UpdateInfraSummaryCommand UpdateInfraSummaryCommand { get; set; }

    public InfraSummaryFixture()
    {
        InfraSummaries= AutoInfraSummaryFixture.Create<List<InfraSummary>>();
        InfraSummary = AutoInfraSummaryFixture.Create<InfraSummary>();
        CreateInfraSummaryCommand = AutoInfraSummaryFixture.Create<CreateInfraSummaryCommand>();

        UpdateInfraSummaryCommand = AutoInfraSummaryFixture.Create<UpdateInfraSummaryCommand>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<InfraSummaryProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoInfraSummaryFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateInfraSummaryCommand>(p => p.EntityName, 10));
            fixture.Customize<CreateInfraSummaryCommand>(c => c.With(b => b.Type, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateInfraSummaryCommand>(p => p.Type, 10));
            fixture.Customize<UpdateInfraSummaryCommand>(c => c.With(b => b.Type, 0.ToString));
            fixture.Customize<InfraSummary>(c => c.With(b => b.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {

    }
}