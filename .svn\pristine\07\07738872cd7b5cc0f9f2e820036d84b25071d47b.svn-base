﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks
{
    public class EscalationMatrixRepositoryMocks
    {
        public static Mock<IEscalationMatrixRepository> CreateEscalationMatrixRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            // Setup for AddAsync method
            mockRepo.Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.EscalationMatrix>()))
                .ReturnsAsync((Domain.Entities.EscalationMatrix matrix) =>
                {
                    matrix.Id = new Fixture().Create<int>(); // Simulate DB-generated Id
                    matrix.ReferenceId = Guid.NewGuid().ToString(); // Simulate DB-generated ReferenceId
                    return matrix;
                });

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> DeleteEscalationMatrixRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            var fakeMatrix = new Domain.Entities.EscalationMatrix
            {
                Id = new Fixture().Create<int>(),
                ReferenceId = Guid.NewGuid().ToString(),
                EscMatCode = "ESC-2025-001",
                IsActive = true,
                CreatedBy = "TestUser",
                CreatedDate = DateTime.UtcNow
            };

            // Setup for GetByReferenceIdAsync
            mockRepo.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(fakeMatrix);

            // Setup for UpdateAsync
            mockRepo.Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.EscalationMatrix>()))
                .ReturnsAsync((Domain.Entities.EscalationMatrix updated) => updated);

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> UpdateEscalationMatrixRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            var fakeMatrix = new Domain.Entities.EscalationMatrix
            {
                Id = new Fixture().Create<int>(),
                ReferenceId = Guid.NewGuid().ToString(),
                EscMatCode = "ESC-2025-002",
                IsActive = true,
                CreatedBy = "UnitTest",
                CreatedDate = DateTime.UtcNow
            };

            // Setup: Get existing matrix by ReferenceId
            mockRepo.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()))
                .ReturnsAsync(fakeMatrix);

            // Setup: Update method should return the updated object
            mockRepo.Setup(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.EscalationMatrix>()))
                .ReturnsAsync((Domain.Entities.EscalationMatrix updatedMatrix) => updatedMatrix);

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> GetEscalationMatrixRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            var fakeId = Guid.NewGuid().ToString();

            var entity = new EscalationMatrix
            {
                Id = new Fixture().Create<int>(),
                ReferenceId = fakeId,
                EscMatCode = "ESC-2025-001",
                IsActive = true,
                CreatedBy = "TestUser",
                CreatedDate = DateTime.UtcNow
            };

            mockRepo.Setup(repo => repo.GetByReferenceIdAsync(fakeId))
                    .ReturnsAsync(entity);

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> GetEscalationMatrixNameRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            var nameList = new List<EscalationMatrixNameVm>
            {
                new EscalationMatrixNameVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Matrix Level 1"
                },
                new EscalationMatrixNameVm
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "Matrix Level 2"
                }
            };

            mockRepo.Setup(repo => repo.GetNames())
                    .ReturnsAsync(nameList);

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> GetEscalationMatrixNameUniqueRepository(bool doesNameExist = false)
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            mockRepo.Setup(repo =>
                    repo.IsEscalationNameExist(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(doesNameExist);

            return mockRepo;
        }

        public static Mock<IEscalationMatrixRepository> GetPaginationEscalationMatrixRepository()
        {
            var mockRepo = new Mock<IEscalationMatrixRepository>();

            var fakeList = new List<EscalationMatrix>
            {
                new EscalationMatrix
                {
                    Id = new Fixture().Create<int>(),
                    ReferenceId = Guid.NewGuid().ToString(),
                    EscMatCode = "ESC-2025-1",
                    IsActive = true,
                    CreatedBy = "TestUser",
                    CreatedDate = DateTime.UtcNow
                },
                new EscalationMatrix
                {
                    Id = new Fixture().Create<int>(),
                    ReferenceId = Guid.NewGuid().ToString(),
                    EscMatCode = "ESC-2025-2",
                    IsActive = true,
                    CreatedBy = "TestUser",
                    CreatedDate = DateTime.UtcNow
                }
            };

            var paginatedResult = PaginatedResult<EscalationMatrix>.Success(fakeList, fakeList.Count, page: 1, pageSize: 10);

            mockRepo.Setup(repo => repo.PaginatedListAllAsync(
                    It.IsAny<int>(),                                     
                    It.IsAny<int>(),                                     
                    It.IsAny<Specification<EscalationMatrix>>(),         
                    It.IsAny<string>(),                                  
                    It.IsAny<string>()                                   
            ))
            .ReturnsAsync(paginatedResult);

            return mockRepo;
        }

        //Events

        public static Mock<IUserActivityRepository> CreateEscalationMatrixEventRepository(List<UserActivity> userActivities)
        {
            var mockRepo = new Mock<IUserActivityRepository>();

            mockRepo.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userActivities);

            mockRepo.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
                .ReturnsAsync((UserActivity userActivity) =>
                {
                    userActivity.Id = new Fixture().Create<int>();
                    userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                    userActivities.Add(userActivity);
                    return userActivity;
                });

            return mockRepo;
        }

        public static Mock<IUserActivityRepository> DeleteEscalationMatrixEventRepository(List<UserActivity> userActivities)
        {
            var mockRepo = new Mock<IUserActivityRepository>();

            // Simulate returning all existing user activities
            mockRepo.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userActivities);

            // Simulate adding a new UserActivity
            mockRepo.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
                .ReturnsAsync((UserActivity activity) =>
                {
                    activity.Id = new Fixture().Create<int>();
                    activity.ReferenceId = new Fixture().Create<Guid>().ToString();
                    userActivities.Add(activity);
                    return activity;
                });

            return mockRepo;
        }

        public static Mock<IUserActivityRepository> UpdateEscalationMatrixEventRepository(List<UserActivity> userActivities)
        {
            var mockRepository = new Mock<IUserActivityRepository>();

            // Simulate ListAllAsync for completeness (optional)
            mockRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userActivities);

            // Simulate AddAsync behavior
            mockRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
                .ReturnsAsync((UserActivity userActivity) =>
                {
                    userActivity.Id = new Fixture().Create<int>();
                    userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                    userActivities.Add(userActivity);
                    return userActivity;
                });

            return mockRepository;
        }

        public static Mock<IUserActivityRepository> PaginatedEscalationMatrixEventRepository(List<UserActivity> userActivities)
        {
            var mockRepository = new Mock<IUserActivityRepository>();

            // Optional: setup ListAllAsync for inspection
            mockRepository.Setup(repo => repo.ListAllAsync())
                .ReturnsAsync(userActivities);

            // Mock AddAsync behavior
            mockRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>()))
                .ReturnsAsync((UserActivity userActivity) =>
                {
                    userActivity.Id = new Fixture().Create<int>();
                    userActivity.ReferenceId = new Fixture().Create<Guid>().ToString();
                    userActivities.Add(userActivity);
                    return userActivity;
                });

            return mockRepository;
        }
    }
}
