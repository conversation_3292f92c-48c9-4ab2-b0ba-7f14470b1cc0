using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class DynamicSubDashboardRepository : BaseRepository<DynamicSubDashboard>, IDynamicSubDashboardRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public DynamicSubDashboardRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<DynamicSubDashboard>> GetByDashboardIdAsync(string id)
    {
       var dynamicSubDashboard =await MapDynamicSubDashBoard(base.FilterBy(x => x.DynamicDashBoardId.Equals(id)).Select(x=>new DynamicSubDashboard
       {
           Id = x.Id,
           ReferenceId = x.ReferenceId,
           Name = x.Name,
           DynamicDashBoardId = x.DynamicDashBoardId,
           DynamicDashBoardName = x.DynamicDashBoardName
       })).ToListAsync();

        return dynamicSubDashboard;
    }
    public Task<bool> IsNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.Name.Equals(name))
            : Entities.Where(e => e.Name.Equals(name)).ToList().Unique(id));
    }

    private  IQueryable<DynamicSubDashboard> MapDynamicSubDashBoard(IQueryable<DynamicSubDashboard> dynamicSubDashboards)
    {
        var subDashboard = dynamicSubDashboards.Select(data => new
        {
            DynamicSubDashboard=data,
            DynamicDashboard= _dbContext.DynamicDashboards.Active().AsNoTracking().FirstOrDefault(x=>x.ReferenceId.Equals(data.DynamicDashBoardId))
        });
        

        return subDashboard.Select(x=>new DynamicSubDashboard
        {
            Id=x.DynamicSubDashboard.Id,
            ReferenceId=x.DynamicSubDashboard.ReferenceId,
            Name=x.DynamicSubDashboard.Name,
            DynamicDashBoardId=x.DynamicSubDashboard.DynamicDashBoardId,
            DynamicDashBoardName=x.DynamicDashboard.Name?? x.DynamicSubDashboard.DynamicDashBoardName,
            IsActive=x.DynamicSubDashboard.IsActive,
            CreatedBy=x.DynamicSubDashboard.CreatedBy,
            CreatedDate=x.DynamicSubDashboard.CreatedDate,
            LastModifiedBy=x.DynamicSubDashboard.LastModifiedBy,
            LastModifiedDate =x.DynamicSubDashboard.LastModifiedDate

        });

    }


}
