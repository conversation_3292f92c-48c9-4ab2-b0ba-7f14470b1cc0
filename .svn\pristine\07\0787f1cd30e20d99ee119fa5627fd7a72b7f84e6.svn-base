﻿using ContinuityPatrol.Application.Features.ReplicationJob.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;

public class
    CreateReplicationJobCommandHandler : IRequestHandler<CreateReplicationJobCommand, CreateReplicationJobResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationJobRepository _replicationJobRepository;

    public CreateReplicationJobCommandHandler(IPublisher publisher, IMapper mapper,
        IReplicationJobRepository replicationJobRepository, ILoggedInUserService loggedInUserService)
    {
        _publisher = publisher;
        _mapper = mapper;
        _replicationJobRepository = replicationJobRepository;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateReplicationJobResponse> Handle(CreateReplicationJobCommand request,
        CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var replicatioJob = _mapper.Map<Domain.Entities.ReplicationJob>(request);

        replicatioJob.Status = "Pending";

        await _replicationJobRepository.AddAsync(replicatioJob);

        var response = new CreateReplicationJobResponse
        {
            Message = Message.Create("Replication Job", replicatioJob.Name),
            Id = replicatioJob.ReferenceId
        };
        await _publisher.Publish(new ReplicationJobCreatedEvent { ReplicationJobName = replicatioJob.Name },
            cancellationToken);
        return response;
    }
}