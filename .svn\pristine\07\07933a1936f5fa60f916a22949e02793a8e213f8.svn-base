﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Events.CyberResiliencyPagination;

public class CyberResiliencyPaginatedEventHandler : INotificationHandler<CyberResiliencyPaginatedEvent>
{
    private readonly ILogger<CyberResiliencyPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberResiliencyPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<CyberResiliencyPaginatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberResiliencyPaginatedEvent paginatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} CyberResiliency",
            Entity = "CyberResiliency",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency Dashboard viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency Dashboard viewed");
    }
}