﻿using ContinuityPatrol.Domain.ViewModels.FormModel;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;

namespace ContinuityPatrol.Application.Features.FormTypeCategory.Commands.CreateBulkImport;

public class ImportFormTypeCategoryCommand : IRequest<ImportFormTypeCategoryResponse>
{
    public List<ImportFormTypeCategoryCommands> ImportFormTypeCategoryCommands { get; set; }
   
}

public class ImportFormTypeCategoryCommands
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string FormId { get; set; }
    public string FormName { get; set; }
    public string FormTypeId { get; set; }
    public string FormTypeName { get; set; }
    public string Logo { get; set; }
    public string Version { get; set; }
    public string Properties { get; set; }
    public string FormVersion { get; set; }

    public ImportFormTypeCommand FormTypeCommand { get; set; }

    public ImportFormCommand FormCommand { get; set; }
}
public class ImportFormTypeCommand
{
    public string Id { get; set; }
    public string FormTypeName { get; set; }
    public string FormTypeLogo { get; set; }
    public bool IsDelete { get; set; }

}

public class ImportFormCommand
{
    public string Id { get; set; }

    public string Name { get; set; }

    public string Type { get; set; }

    public string Properties { get; set; }

    public string Version { get; set; }

    public bool IsPublish { get; set; }

    public bool IsLock { get; set; }

}