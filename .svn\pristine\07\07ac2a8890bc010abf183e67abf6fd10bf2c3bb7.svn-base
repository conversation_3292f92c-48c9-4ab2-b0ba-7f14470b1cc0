﻿using ContinuityPatrol.Application.Features.BusinessService.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Events;

public class CreateBusinessServiceEventTests : IClassFixture<BusinessServiceFixture>, IClassFixture<UserActivityFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly BusinessServiceCreatedEventHandler _handler;

    public CreateBusinessServiceEventTests(BusinessServiceFixture businessServiceFixture, UserActivityFixture userActivityFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockBusinessServiceEventLogger = new Mock<ILogger<BusinessServiceCreatedEventHandler>>();

        _mockUserActivityRepository = BusinessServiceRepositoryMocks.CreateBusinessServiceEventRepository(_userActivityFixture.UserActivities);

        _handler = new BusinessServiceCreatedEventHandler(mockLoggedInUserService.Object, mockBusinessServiceEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_PaginatedBusinessServiceEventPaginated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_businessServiceFixture.BusinessServiceCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_businessServiceFixture.BusinessServiceCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}