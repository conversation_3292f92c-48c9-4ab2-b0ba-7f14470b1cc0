﻿let userGroupId = '', userGroupData = '', Userlist = [], UsersNameList = [];
const exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

const userGroupURL = {
    nameExistUrl :"Admin/UserGroup/IsGroupNameExist",  
    userGroupPagination: "/Admin/UserGroup/GetPaginationList",
    userNameList:"Admin/UserGroup/GetUserNames"
}


    let permission = {
        "createPermission": String($("#AdminCreate").data("create-permission")).toLowerCase(),
        "deletePermission": String($("#AdminDelete").data("delete-permission")).toLowerCase()
    }
    if (permission.createPermission == 'false') {
        $(".btn-Usergroup-Create").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    var selectedValues = [];
    var dataTable = $('#userGroupTable').DataTable(
        {

            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next" ></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": false,
            "filter": true,
            "order": [],

            "ajax": {
                "type": "GET",
                "url": userGroupURL.userGroupPagination,
                "dataType": "json",
                "async": true,
                "data": function (d) {
                    
                    let sortIndex = (d?.order && d?.order?.length > 0) ? d?.order[0].column : '';
                    let sortValue = sortIndex === 1 ? "groupName" : sortIndex === 2 ? "groupDescription"  :
                       sortIndex === 3 ? "status" : "";
                    let orderValue = (d?.order && d?.order?.length > 0)? d.order[0]?.dir : 'asc';
                    d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                    d.pageSize = d?.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    d.sortColumn = sortValue;
                    d.SortOrder = orderValue;
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                        /*  $(".TableThead").addClass("d-none")*/

                    }
                    else {
                        $(".pagination-column").removeClass("disabled")

                    }
                    return json?.data;
                },
                "error": function (xhr, status, error) {
                    if (error?.status === 401) {
                        window.location.assign('/Account/Logout')
                    }
                },

            },

            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },

                },
                {
                    data: "groupName",
                    name: 'Group Name',
                    "autoWidth": true,
                    "render": function (data, type, row) {
                        if (type === 'display') {
                            return '<span title="' + data + '">' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "data": "groupDescription", "name": "Group Description", "autoWidth": true,

                    "render": function (data, type, row) {

                        if (type === 'display') {
                            return `<span title='${data ?? 'NA'}'>${data ?? 'NA'} </span>`
                        }
                        return data;
                    }
                },

                {
                    "data": "userProperties", "name": "userProperties", "autoWidth": true,
                    "render": function (data, type, row) {

                        let optionValues = JSON.parse(data)
                        let tableNames = optionValues?.length ? optionValues.map(o => o.loginname).join() : 'NA'
                        if (type === 'display') {
                            return '<span title="' + tableNames + '">' + tableNames + '</span>';
                        }
                        return data;
                    }
                },
                {
                    "render": function (data, type, row) {
                        if (permission.createPermission === 'true' && permission.deletePermission === "true") {
                            const isParent = row.isParent;
                            return `<div class="d-flex align-items-center  gap-2">                                       
                                <span role="button" title="Edit"  class="edit-button" data-usergroup='${JSON.stringify(row)}'>
                                    <i class="cp-edit"></i>
                                </span>
                                ${isParent ? `
                                    <span title=""  opacity:0.50;" class="delete-button ">
                                        <i class="cp-Delete"></i>
                                    </span>` :
                                    ` <span role="button" title="Delete" class="delete-button" data-usergroup-id="${row.id}" data-usergroup-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                        <i class="cp-Delete"></i>
                                    </span>`
                                }
                            </div>`;
                        }
                        else if (permission.createPermission === 'true' && permission.deletePermission === "false") {
                            return `
                       <div class="d-flex align-items-center  gap-2">
                           <span role="button" title="Edit" class="edit-button" data-usergroup='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>
                       
                                            
                       
                                            <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                  
                                            
                                </div>`;
                        }
                        else if (permission.createPermission === 'false' && permission.deletePermission === "true") {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>



                                <span role="button" title="Delete" class="delete-button" data-usergroup-id="${row.id}" data-usergroup-name="${row.groupName}" data-bs-toggle="modal" data-bs-target="#DeleteModal"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                        else {
                            return `
                            <div class="d-flex align-items-center  gap-2">
                                <span role="button" title="Edit" class="icon-disabled">
                                    <i class="cp-edit"></i>
                                </span>
                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                </span>

                            </div>`;
                        }
                    },
                    "orderable": false
                }
            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
            columnDefs: [           
                {
                    "targets": [1, 2, 3,],
                    "className": "truncate"
                },

            ],
            error: function (xhr, error, code) {
                console.log(xhr, code, error);
            },
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const nameCheckbox = $("#groupname");
        const userCheckbox = $("#User");
        const inputValue = $('#search-inp').val();
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + inputValue);
        }
        if (userCheckbox.is(':checked')) {
            selectedValues.push(userCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (e.target.value && json?.recordsFiltered === 0) {

                $('.dataTables_empty').text('No matching records found');

            }
        })
    }, 500))
    dataTable.on('draw.dt', function () {
        $('.paginate_button.page-item.previous').attr('title', 'Previous');
        $('.paginate_button.page-item.next').attr('title', 'Next');
    });

    // User List
    const getUserList = async (dataArray) => {
        await $.ajax({
            type: "GET",
            url: RootUrl + userGroupURL.userNameList,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result.success) {
                    if (result?.data && Array.isArray(result?.data) && result?.data?.length > 0) {
                        userList = result.data;
                        $('#userGroupUserId').empty();
                        let html = '';
                        userList.map((item) => {
                            html += '<option value="' + item.id + '" data-id="' + item.id + '" data-name="' + item.loginName + '">' + item.loginName + '</option>';
                        });
                        $('#userGroupUserId').append(html);
                    }
                    if (dataArray?.length > 0) {

                        $('#userGroupUserId').val(dataArray).trigger('change');
                    }
                } else {
                    errorNotification(result);
                    $('#userGroupUserId').append('<option value="No Data"></option>');
                }
            },
        });
    };
    // UserGroup Delete
    $('#userGroupTable').on('click', '.delete-button', function () {
        let usergroupId = $(this).data('usergroup-id');
        let usergroupName = $(this).data('usergroup-name');
        $("#deleteData").attr("title", usergroupName).text(usergroupName);
        $('#textDeleteId').val(usergroupId);
    });

    // UserGroup Edit
    $('#userGroupTable').on('click', '.edit-button', function () {
        userGroupData = $(this).data('usergroup');
        userGroupEdit(userGroupData);
        $('#btnUserGroupSave').text('Update')
        $('#CreateModal').modal('show');
    });
    // Name
    $('#userGroupNameId').on('input', commonDebounce(async function () {
        userGroupId = $('#userGroupId').val();
        let value = $(this).val();
        let sanitizedValue = value.replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        await validateNames(value, userGroupId, userGroupURL.nameExistUrl);
    }, 400));

    // Description
    $('#userGroupDescriptionId').on('input', async function (event) {
        let value = $(this).val();
        let sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
        $(this).val(sanitizedValue);
        if (exceptThisSymbol.includes(event.key)) {
            event.preventDefault();
        }
        let errorElement = $('#userGroupDescriptionErr');
        await validateDescription(sanitizedValue, "Should not allow more than 250 characters", errorElement);
    });

    // User
    $("#userGroupUserId").on('change', function () {
        UsersNameList = [];
        let value = $(this).val();
        let selectedOptions = $(this).find('option:selected');
        selectedOptions.each(function () {
            let id = $(this).data('id');
            let name = $(this).data('name');
            UsersNameList.push({ id: id, loginname: name });
        });
        validateDropDown(value, 'Select users', 'userGroupUserErr');
    });

    // UserGroup Save
    $("#btnUserGroupSave").on('click', async function () {
        let form = $("#CreateForm");
        let name = $('#userGroupNameId').val();
        elementDescription = $("#userGroupDescriptionId").val();
        errorElement = $('#userGroupDescriptionErr');
        let isDescription = await validateDescription(elementDescription, "Should not allow more than 250 characters", errorElement);
        let user = $('#userGroupUserId').val();
        let IsName = await validateNames(name, userGroupId,userGroupURL.nameExistUrl);
        let IsUser = await validateDropDown(user, 'Select users', 'userGroupUserErr');
        let userGroupSanitizeArray = ['userGroupNameId', 'userGroupDescriptionId', 'userGroupId', 'userGroupProperties']
        sanitizeContainer(userGroupSanitizeArray)
        setTimeout(() => {
            if (IsName && isDescription && IsUser) {
                let userProperties = JSON.stringify(UsersNameList);
                $('#userGroupProperties').val(userProperties)
                form.trigger('submit');
            }
        }, 200)
    });

    // Validation Name
    async function validateNames(value, id = null) {

        let errorElement = $('#userGroupNameErr');
        if (!value) {
            errorElement.text('Enter group name ').addClass('field-validation-error');
            return false;
        }
        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        let url = RootUrl + userGroupURL.nameExistUrl;
        let data = {};
        data.Groupname = value;
        data.id = id;

        const validationResults = [
            await SpecialCharValidate(value),
            await OnlyNumericsValidate(value),
            await ShouldNotBeginWithUnderScore(value),
            await ShouldNotBeginWithSpace(value),
            await SpaceWithUnderScore(value),
            await ShouldNotBeginWithNumber(value),
            await ShouldNotEndWithUnderScore(value),
            await ShouldNotEndWithSpace(value),
            await MultiUnderScoreRegex(value),
            await SpaceAndUnderScoreRegex(value),
            await minMaxlength(value),
            await secondChar(value),
            await IsGroupNameExist(url, data, OnError)
        ];

        return await CommonValidation(errorElement, validationResults);
    }

    async function IsGroupNameExist(url, data, errorFunc) {

        return !data.Groupname.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
    }

    // Validate Description
    async function validateDescription(value, errorMessage) {
        const errorElement = $('#userGroupDescriptionErr');

        if (value.includes('<')) {
            errorElement.text('Special characters not allowed').addClass('field-validation-error');
            return false;
        }
        if (!value) {
            errorElement.text('').removeClass('field-validation-error');        
            return true;
        } else if (value.length < 0) {
            errorElement.text(errorMessage).addClass('field-validation-error');          
            return false;
        } else if (value.length > 250) {
            errorElement.text(errorMessage).addClass('field-validation-error');        
            return false;
        }
        else {
            errorElement.text('').removeClass('field-validation-error');        
            return true;
        }
    }

    // validate DropDown
    function validateDropDown(value, errormessage, errorElements) {
        if (value.length === 0) {
            $('#' + errorElements).text(errormessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElements).text('').removeClass('field-validation-error');
            return true;
        }
    }

    // Create 
    $('#Create').on('click', async function () {
        getUserList()
        clearInputs();
    });

    // Clear 
    function clearInputs() {
        const errorElements = ['#userGroupUserErr', '#userGroupDescriptionErr', '#userGroupNameErr'];
        clearInputFields('usergroupData', errorElements);
        $('#btnUserGroupSave').text('Save');
        $('#userGroupUserId,#userGroupNameId,#userGroupDescriptionId').val('');
        $('#userGroupUserId,#userGroupDescriptionId,#userGroupNameId').text('').removeClass('field-validation-error');

    }
    function userGroupEdit(userGroupData) {

        let userArray = []
        let userProperties = JSON.parse(userGroupData.userProperties);
        for (let i = 0; i < userProperties.length; i++) {
            userArray.push(userProperties[i].id);
        }
        getUserList(userArray)
        $('#userGroupNameId').val(userGroupData.groupName);
        $('#userGroupDescriptionId').val(userGroupData.groupDescription);
        $('#userGroupId').val(userGroupData.id);

        userGroupId = userGroupData.id
        let errorElement = ['#userGroupNameErr', '#userGroupUserErr']
        errorElement.forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }


