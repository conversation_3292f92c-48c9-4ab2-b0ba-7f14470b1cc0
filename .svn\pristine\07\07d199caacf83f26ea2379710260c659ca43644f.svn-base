﻿using ContinuityPatrol.Application.Features.SiteType.Events.Create;

namespace ContinuityPatrol.Application.Features.SiteType.Commands.Create;

public class CreateSiteTypeCommandHandler : IRequestHandler<CreateSiteTypeCommand, CreateSiteTypeResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ISiteTypeRepository _siteTypeRepository;

    public CreateSiteTypeCommandHandler(IMapper mapper, ISiteTypeRepository siteTypeRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _siteTypeRepository = siteTypeRepository;
    }

    public async Task<CreateSiteTypeResponse> Handle(CreateSiteTypeCommand request, CancellationToken cancellationToken)
    {
        var siteType = _mapper.Map<Domain.Entities.SiteType>(request);

        siteType = await _siteTypeRepository.AddAsync(siteType);

        var response = new CreateSiteTypeResponse
        {
            Message = Message.Create("Site Type", siteType.Type),

            SiteTypeId = siteType.ReferenceId
        };

        await _publisher.Publish(new SiteTypeCreatedEvent { Type = siteType.Type }, cancellationToken);

        return response;
    }
}