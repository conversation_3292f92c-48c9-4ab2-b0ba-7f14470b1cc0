using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class CyberComponentRepository : BaseRepository<CyberComponent>, ICyberComponentRepository
{
    private readonly ApplicationDbContext _dbContext;

    private readonly ILoggedInUserService _loggedInUserService;

    public CyberComponentRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
    public override async Task<IReadOnlyList<CyberComponent>> ListAllAsync()
    {
        return await MapCyberComponent(base.QueryAll(x => x.IsActive)).ToListAsync();
    }
    public override async Task<CyberComponent> GetByReferenceIdAsync(string id)
    {
        var query = base.GetByReferenceId(id, x => x.ReferenceId == id);
        return await MapCyberComponent(query).FirstOrDefaultAsync();
    }
    public override async Task<PaginatedResult<CyberComponent>> PaginatedListAllAsync(int pageNumber,int pageSize,Specification<CyberComponent> specification, string sortColumn, string sortOrder)
    {
        var query = MapCyberComponent(Entities.Specify(specification).DescOrderById());
        return await query.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
 
    public async Task<List<CyberComponent>> GetCyberComponentBySiteId(string siteId)
    {
        return await MapCyberComponent(base.FilterBy(x => x.SiteId == siteId)).ToListAsync();
    }

    public async Task<bool> IsNameExist(string name, string id)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            throw new ArgumentException("Name must be provided.", nameof(name));
        }

        if (!id.IsValidGuid())
        {
            return await Entities.AnyAsync(e => e.Name == name);
        }

        var matchingItems = await Entities
            .Where(e => e.Name == name)
            .ToListAsync();

        return matchingItems.Unique(id);
    }
    private IQueryable<CyberComponent> MapCyberComponent(IQueryable<CyberComponent> cyberComponents)
    {
       return cyberComponents.Select(x => new
        {
            Site = _dbContext.Sites.FirstOrDefault(s => s.ReferenceId == x.SiteId),
            CyberComponent = x
        })
        .Select(res => new CyberComponent
        {
            Id = res.CyberComponent.Id,
            ReferenceId = res.CyberComponent.ReferenceId,
            Name = res.CyberComponent.Name,
            Description = res.CyberComponent.Description,
            SiteId = res.Site.ReferenceId ?? res.CyberComponent.SiteId,
            SiteName = res.Site.Name ?? res.CyberComponent.SiteName,
            Type = res.CyberComponent.Type,
            ServerType = res.CyberComponent.ServerType,
            ServerTypeId = res.CyberComponent.ServerTypeId,
            Properties = res.CyberComponent.Properties,
            Logo = res.CyberComponent.Logo,
            Status = res.CyberComponent.Status,
            IsActive = res.CyberComponent.IsActive,
            CreatedBy = res.CyberComponent.CreatedBy,
            CreatedDate = res.CyberComponent.CreatedDate,
            LastModifiedBy = res.CyberComponent.LastModifiedBy,
            LastModifiedDate = res.CyberComponent.LastModifiedDate
        });
    }
}
