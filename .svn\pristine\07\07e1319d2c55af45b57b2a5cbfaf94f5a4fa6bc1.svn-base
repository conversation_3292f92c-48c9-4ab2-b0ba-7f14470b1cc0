﻿namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;

public class GetSlaBreachListVm
{
    public int SlaBreachCount { get; set; }
    public int SlaNonBreachCount { get; set; }
    public int SlaMeetingRtoCount { get; set; }
    public int ActiveAlertCount { get; set; }
    public List<GetSlaImpactListVm> SlaImpactList { get; set; } = new();
}

public class GetSlaImpactListVm
{
    public DateTime Date { get; set; }
    public int ImpactCount { get; set; }
    public int NonImpactCount { get; set; }
}