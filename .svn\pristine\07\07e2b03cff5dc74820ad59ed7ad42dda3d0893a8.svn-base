using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Delete;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DataSyncJobControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DataSyncJobController _controller;
    private readonly DataSyncJobFixture _dataSyncJobFixture;

    public DataSyncJobControllerTests()
    {
        _dataSyncJobFixture = new DataSyncJobFixture();

        var testBuilder = new ControllerTestBuilder<DataSyncJobController>();
        _controller = testBuilder.CreateController(
            _ => new DataSyncJobController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDataSyncJob_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _dataSyncJobFixture.CreateDataSyncJobCommand;
        var expectedResponse = _dataSyncJobFixture.CreateDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSyncJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncJobResponse>(createdResult.Value);
        Assert.Equal("Enterprise Primary Replication job created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDataSyncJob_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _dataSyncJobFixture.UpdateDataSyncJobCommand;
        var expectedResponse = _dataSyncJobFixture.UpdateDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSyncJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncJobResponse>(okResult.Value);
        Assert.Equal("Enterprise Updated Replication job updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task DeleteDataSyncJob_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSyncJobId = Guid.NewGuid().ToString();
        var expectedResponse = _dataSyncJobFixture.DeleteDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataSyncJobCommand>(c => c.Id == dataSyncJobId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataSyncJob(dataSyncJobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataSyncJobResponse>(okResult.Value);
        Assert.Equal("Enterprise Replication job deleted successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task GetDataSyncJobs_ReturnsOkResult()
    {
        // Arrange
        var dataSyncJobList = new List<DataSyncJobListVm> { _dataSyncJobFixture.DataSyncJobListVm };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncJobListQuery>(), default))
            .ReturnsAsync(dataSyncJobList);

        // Act
        var result = await _controller.GetDataSyncJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSyncJobListVm>>(okResult.Value);
        Assert.Single(returnedList);
        Assert.Equal("Enterprise List Replication", returnedList.First().ReplicationName);
        Assert.Equal("Scheduled Synchronization", returnedList.First().ReplicationType);
        Assert.Equal("Enterprise List Site", returnedList.First().SiteName);
        Assert.Equal("Incremental", returnedList.First().ModeType);
    }

    [Fact]
    public async Task GetDataSyncJobById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var dataSyncJobId = Guid.NewGuid().ToString();
        var dataSyncJobDetail = _dataSyncJobFixture.DataSyncJobDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncJobDetailQuery>(q => q.Id == dataSyncJobId), default))
            .ReturnsAsync(dataSyncJobDetail);

        // Act
        var result = await _controller.GetDataSyncJobById(dataSyncJobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataSyncJobDetailVm>(okResult.Value);
        Assert.Equal("Enterprise Detail Replication", returnedDetail.ReplicationName);
        Assert.Equal("Continuous Synchronization", returnedDetail.ReplicationType);
        Assert.Equal("Enterprise Detail Site", returnedDetail.SiteName);
        Assert.Equal("Delta", returnedDetail.ModeType);
        Assert.Equal("/enterprise/data/detail_source", returnedDetail.SourceDirectory);
        Assert.Equal("/enterprise/data/detail_destination", returnedDetail.DestinationDirectory);
    }

    [Fact]
    public async Task GetPaginatedDataSyncJobs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _dataSyncJobFixture.GetDataSyncJobPaginatedQuery;
        var paginatedResult = new PaginatedResult<DataSyncJobListVm>
        {
            Data = new List<DataSyncJobListVm> { _dataSyncJobFixture.DataSyncJobListVm },
            CurrentPage = 1,
            PageSize = 10,
            TotalCount = 1,
            TotalPages = 1,
           
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDataSyncJobs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<DataSyncJobListVm>>(okResult.Value);
        Assert.Single(returnedResult.Data);
        Assert.Equal(1, returnedResult.CurrentPage);
        Assert.Equal(10, returnedResult.PageSize);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.False(returnedResult.HasNextPage);
    }

    #endregion

    #region Error Handling

    [Fact]
    public async Task GetDataSyncJobById_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDataSyncJobById(invalidId));
    }

    [Fact]
    public async Task DeleteDataSyncJob_WithInvalidGuid_ThrowsArgumentException()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDataSyncJob(invalidId));
    }

    [Fact]
    public async Task CreateDataSyncJob_WhenMediatorThrowsException_PropagatesException()
    {
        // Arrange
        var command = _dataSyncJobFixture.CreateDataSyncJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new InvalidOperationException("Replication configuration validation failed"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => _controller.CreateDataSyncJob(command));
        Assert.Contains("Replication configuration validation failed", exception.Message);
    }

    [Fact]
    public async Task UpdateDataSyncJob_WhenMediatorThrowsNotFoundException_PropagatesException()
    {
        // Arrange
        var command = _dataSyncJobFixture.UpdateDataSyncJobCommand;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("DataSyncJob", command.Id));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<NotFoundException>(() => _controller.UpdateDataSyncJob(command));
        Assert.Contains("DataSyncJob", exception.Message);
        Assert.Contains(command.Id, exception.Message);
    }

    #endregion

    #region ClearDataCache

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        // Act
        _controller.ClearDataCache();

        // Assert - This test verifies the method executes without throwing exceptions
        // The actual cache clearing logic is tested in integration tests
        Assert.True(true);
    }

    #endregion

   

    [Fact]
    public async Task GetDataSyncJobs_HandlesEmptyList()
    {
        // Arrange
        var emptyList = new List<DataSyncJobListVm>();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncJobListQuery>(), default))
            .ReturnsAsync(emptyList);

        // Act
        var result = await _controller.GetDataSyncJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsAssignableFrom<List<DataSyncJobListVm>>(okResult.Value);
        Assert.Empty(returnedList);
    }

    [Fact]
    public async Task CreateDataSyncJob_HandlesHighVolumeEnterpriseReplication()
    {
        // Arrange
        var highVolumeCommand = new CreateDataSyncJobCommand
        {
            ReplicationId = Guid.NewGuid().ToString(),
            ReplicationName = "Enterprise High-Volume Global Replication",
            DataSyncOptionId = Guid.NewGuid().ToString(),
            ReplicationTypeId = Guid.NewGuid().ToString(),
            ReplicationType = "High-Performance Parallel Synchronization",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise Global Data Center",
            Properties = @"{
                ""syncInterval"": ""30s"",
                ""retryCount"": 10,
                ""timeout"": ""300s"",
                ""compressionEnabled"": true,
                ""encryptionLevel"": ""AES256"",
                ""checksumValidation"": true,
                ""bandwidthLimit"": ""1Gbps""
            }",
            JobProperties = @"{
                ""priority"": ""Critical"",
                ""maxConcurrency"": 20,
                ""batchSize"": 10000,
                ""memoryLimit"": ""8GB"",
                ""diskSpaceThreshold"": ""100GB"",
                ""errorThreshold"": 0.01
            }",
            ScheduleProperties = @"{
                ""schedule"": ""0 */1 * * * *"",
                ""timezone"": ""UTC"",
                ""enabled"": true,
                ""maintenanceWindow"": ""02:00-04:00"",
                ""skipOnHolidays"": false
            }",
            SourceDirectory = "/enterprise/global/primary/data",
            DestinationDirectory = "/enterprise/global/replica/data",
            ModeType = "Real-time",
            LastSuccessfullReplTime = DateTime.UtcNow.AddSeconds(-30).ToString("yyyy-MM-dd HH:mm:ss")
        };

        var expectedResponse = new CreateDataSyncJobResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "Enterprise High-Volume Global Replication job created successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(highVolumeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSyncJob(highVolumeCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncJobResponse>(createdResult.Value);

        Assert.Equal("Enterprise High-Volume Global Replication job created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate high-volume configuration
        Assert.Equal("Enterprise High-Volume Global Replication", highVolumeCommand.ReplicationName);
        Assert.Equal("High-Performance Parallel Synchronization", highVolumeCommand.ReplicationType);
        Assert.Equal("Enterprise Global Data Center", highVolumeCommand.SiteName);
        Assert.Equal("Real-time", highVolumeCommand.ModeType);

        // Validate JSON properties contain expected values
        Assert.Contains("\"syncInterval\": \"30s\"", highVolumeCommand.Properties);
        Assert.Contains("\"compressionEnabled\": true", highVolumeCommand.Properties);
        Assert.Contains("\"encryptionLevel\": \"AES256\"", highVolumeCommand.Properties);
        Assert.Contains("\"maxConcurrency\": 20", highVolumeCommand.JobProperties);
        Assert.Contains("\"batchSize\": 10000", highVolumeCommand.JobProperties);
        Assert.Contains("\"priority\": \"Critical\"", highVolumeCommand.JobProperties);
    }

    [Fact]
    public async Task UpdateDataSyncJob_HandlesMultiSiteReplicationConfiguration()
    {
        // Arrange
        var multiSiteCommand = new UpdateDataSyncJobCommand
        {
            Id = Guid.NewGuid().ToString(),
            ReplicationId = Guid.NewGuid().ToString(),
            ReplicationName = "Enterprise Multi-Site Disaster Recovery Replication",
            DataSyncOptionId = Guid.NewGuid().ToString(),
            ReplicationTypeId = Guid.NewGuid().ToString(),
            ReplicationType = "Multi-Site Cascading Synchronization",
            SiteId = Guid.NewGuid().ToString(),
            SiteName = "Enterprise DR Coordination Site",
            Properties = @"{
                ""syncInterval"": ""2min"",
                ""retryCount"": 15,
                ""timeout"": ""600s"",
                ""cascadingEnabled"": true,
                ""failoverThreshold"": ""5min"",
                ""healthCheckInterval"": ""30s"",
                ""networkOptimization"": true
            }",
            JobProperties = @"{
                ""priority"": ""High"",
                ""maxConcurrency"": 8,
                ""batchSize"": 5000,
                ""resourceAllocation"": ""dedicated"",
                ""loadBalancing"": true,
                ""autoScaling"": true
            }",
            ScheduleProperties = @"{
                ""schedule"": ""0 */2 * * * *"",
                ""timezone"": ""UTC"",
                ""enabled"": true,
                ""coordinatedStart"": true,
                ""staggeredExecution"": ""30s""
            }",
            SourceDirectory = "/enterprise/multisite/primary",
            DestinationDirectory = "/enterprise/multisite/dr",
            ModeType = "Synchronized",
            LastSuccessfullReplTime = DateTime.UtcNow.AddMinutes(-2).ToString("yyyy-MM-dd HH:mm:ss")
        };

        var expectedResponse = new UpdateDataSyncJobResponse
        {
            Id = multiSiteCommand.Id,
            Message = "Enterprise Multi-Site Disaster Recovery Replication job updated successfully!",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(multiSiteCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSyncJob(multiSiteCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncJobResponse>(okResult.Value);

        Assert.Equal("Enterprise Multi-Site Disaster Recovery Replication job updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);

        // Validate multi-site configuration
        Assert.Equal("Enterprise Multi-Site Disaster Recovery Replication", multiSiteCommand.ReplicationName);
        Assert.Equal("Multi-Site Cascading Synchronization", multiSiteCommand.ReplicationType);
        Assert.Equal("Enterprise DR Coordination Site", multiSiteCommand.SiteName);
        Assert.Equal("Synchronized", multiSiteCommand.ModeType);

        // Validate multi-site specific properties
        Assert.Contains("\"cascadingEnabled\": true", multiSiteCommand.Properties);
        Assert.Contains("\"failoverThreshold\": \"5min\"", multiSiteCommand.Properties);
        Assert.Contains("\"networkOptimization\": true", multiSiteCommand.Properties);
        Assert.Contains("\"loadBalancing\": true", multiSiteCommand.JobProperties);
        Assert.Contains("\"autoScaling\": true", multiSiteCommand.JobProperties);
        Assert.Contains("\"coordinatedStart\": true", multiSiteCommand.ScheduleProperties);
    }

    

 

    [Fact]
    public async Task CreateDataSyncJob_HandlesRealTimeDataSynchronization()
    {
        // Arrange
        var realTimeCommand = _dataSyncJobFixture.CreateDataSyncJobCommand;
        realTimeCommand.ReplicationId = Guid.NewGuid().ToString();
        realTimeCommand.ReplicationName = "Real-Time Enterprise Data Sync";
        realTimeCommand.ReplicationType = "REAL_TIME";
        realTimeCommand.ModeType = "Production";
        realTimeCommand.ScheduleProperties = @"{
            ""scheduleType"": ""REAL_TIME"",
            ""isActive"": true,
            ""frequency"": ""continuous"",
            ""retryAttempts"": 3
        }";

        var expectedResponse = _dataSyncJobFixture.CreateDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(realTimeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDataSyncJob(realTimeCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDataSyncJobResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Real-Time Enterprise Data Sync", realTimeCommand.ReplicationName);
        Assert.Equal("REAL_TIME", realTimeCommand.ReplicationType);
        Assert.Equal("Production", realTimeCommand.ModeType);
        Assert.Contains("REAL_TIME", realTimeCommand.ScheduleProperties);
    }

    [Fact]
    public async Task GetDataSyncJobList_HandlesMultipleJobTypes()
    {
        // Arrange
        var multipleJobs = new List<DataSyncJobListVm>
        {
            new DataSyncJobListVm
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "Enterprise Batch Sync Job",
                ReplicationType = "Batch synchronization for enterprise data",
                ModeType = "BATCH",
               
            },
            new DataSyncJobListVm
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "Enterprise Real-Time Sync Job",
                ReplicationType = "Real-time synchronization for enterprise data",
                ModeType = "REAL_TIME",
                
            },
            new DataSyncJobListVm
            {
                Id = Guid.NewGuid().ToString(),
                ReplicationName = "Enterprise Scheduled Sync Job",
                ReplicationType = "Scheduled synchronization for enterprise data",
                ModeType = "SCHEDULED",
               
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataSyncJobListQuery>(), default))
            .ReturnsAsync(multipleJobs);

        // Act
        var result = await _controller.GetDataSyncJobs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<DataSyncJobListVm>>(okResult.Value);

        Assert.Equal(3, returnedList.Count);
        Assert.Contains(returnedList, j => j.ModeType == "BATCH");
        Assert.Contains(returnedList, j => j.ModeType == "REAL_TIME");
        Assert.Contains(returnedList, j => j.ModeType == "SCHEDULED");
        
    }

    [Fact]
    public async Task UpdateDataSyncJob_HandlesJobStatusTransition()
    {
        // Arrange
        var statusTransitionCommand = _dataSyncJobFixture.UpdateDataSyncJobCommand;
        statusTransitionCommand.ReplicationName = "Enterprise Status Transition Job";
        statusTransitionCommand.ModeType = "Maintenance";
        statusTransitionCommand.Properties = @"{
            ""status"": ""inactive"",
            ""description"": ""Job being transitioned to inactive status"",
            ""lastModified"": """ + DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ") + @"""
        }";

        var expectedResponse = _dataSyncJobFixture.UpdateDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(statusTransitionCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDataSyncJob(statusTransitionCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDataSyncJobResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise Status Transition Job", statusTransitionCommand.ReplicationName);
        Assert.Equal("Maintenance", statusTransitionCommand.ModeType);
        Assert.Contains("inactive", statusTransitionCommand.Properties);
    }

    [Fact]
    public async Task GetDataSyncJobDetail_HandlesComplexJobConfiguration()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();
        var complexJobDetail = _dataSyncJobFixture.DataSyncJobDetailVm;
        complexJobDetail.ReplicationName = "Complex Enterprise Multi-Source Sync";
        complexJobDetail.Properties = @"{
            ""description"": ""Complex job synchronizing data from multiple enterprise sources"",
            ""complexity"": ""high"",
            ""sources"": [""Database1"", ""Database2"", ""API1""],
            ""targets"": [""DataWarehouse"", ""AnalyticsDB""]
        }";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDataSyncJobDetailQuery>(q => q.Id == jobId), default))
            .ReturnsAsync(complexJobDetail);

        // Act
        var result = await _controller.GetDataSyncJobById(jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<DataSyncJobDetailVm>(okResult.Value);

        Assert.Equal("Complex Enterprise Multi-Source Sync", returnedDetail.ReplicationName);
        Assert.Contains("multiple enterprise sources", returnedDetail.Properties);
        Assert.Contains("DataWarehouse", returnedDetail.Properties);
    }

    [Fact]
    public async Task DeleteDataSyncJob_HandlesJobCleanup()
    {
        // Arrange
        var jobId = Guid.NewGuid().ToString();
        var expectedResponse = _dataSyncJobFixture.DeleteDataSyncJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDataSyncJobCommand>(c => c.Id == jobId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteDataSyncJob(jobId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteDataSyncJobResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Contains("successfully", returnedResponse.Message);
    }

  
}
