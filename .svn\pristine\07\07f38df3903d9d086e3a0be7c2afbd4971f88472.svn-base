﻿using ContinuityPatrol.Application.Features.BackUp.Commands.Create;
using ContinuityPatrol.Application.Features.BackUp.Commands.Execute;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUpLog.Events.PaginatedView;
using ContinuityPatrol.Application.Features.BackUpLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BackUpModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;
using Newtonsoft.Json;


namespace ContinuityPatrol.Web.Areas.Admin.Controllers;

[Area("Admin")]
public class BackupDataController : BaseController
{
    private readonly ILogger<BackupDataController> _logger;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly IPublisher _publisher;
    public BackupDataController(ILogger<BackupDataController> logger, IPublisher publisher, IMapper mapper, IDataProvider provider)
    {
        _logger = logger;
        _mapper = mapper;
        _dataProvider = provider;
        _publisher = publisher;
    }
    [EventCode(EventCodes.BackupData.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in BackUpData");

        try
        {
            await _publisher.Publish(new BackUpLogPaginatedEvent());

            _logger.LogDebug("List method completed successfully in BackUpData, returning view.");

            return View();
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on backup data while processing the list request", ex);

            return View();
        }
    }
    [EventCode(EventCodes.BackupData.GetList)]
    public async Task<IActionResult> GetList()
    {
        _logger.LogDebug("Entering GetList method in BackUpData");

        try
        {
            var backupData = await _dataProvider.BackUp.GetBackUpList();

            _logger.LogDebug("Successfully retrieved backup data");

            return Json(backupData);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on backup data while retrieving backup data.", ex);

            return Json("");
        }

    }
    [EventCode(EventCodes.BackupData.GetBackupConfig)]
    public async Task<IActionResult> GetBackupConfig()
    {
        _logger.LogDebug("Entering GetBackupConfig method in BackUpData");

        try
        {
            var backupConfig = await _dataProvider.BackUp.GetBackUpByConfig();

            _logger.LogDebug("Successfully retrieved backup configuration on backup data");

            return Json(backupConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError($"An error occurred on backup data while retrieving backup configuration. {ex.Message}");

            return Json("");
        }

    }
    [HttpGet]
    [EventCode(EventCodes.BackupData.GetPagination)]
    public async Task<JsonResult> GetPagination(GetBackUpLogPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in BackUpData");

        try
        {
            _logger.LogDebug("Successfully retrieved backup log paginated list in backup data");

            return Json(await _dataProvider.BackUpLog.GetPaginatedBackUpLogs(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on backup data page while processing the pagination request.", ex);

            return Json("");
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.BackupData.ExecuteBackUpCommand)]
    public async Task<IActionResult> ExecuteBackUpCommand([FromBody] BackUpViewModel model)
    {
        _logger.LogDebug("Entering ExecuteBackUpCommand method in BackUpData");

        try
        {
            var backUpCommandExecute = _mapper.Map<BackUpExecuteCommand>(model);

            var result = await _dataProvider.BackUp.ExecuteBackUp(backUpCommandExecute);

            _logger.LogDebug("Successfully executed backup command on backup data");

            return Json(result);
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error occurred on backup data while executing backup command. {ex.ValidationErrors.FirstOrDefault()}");

            return (Json(new { success = false, message = ex.ValidationErrors.FirstOrDefault() }));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on backup data while executing backup command", ex);

            return Json(new { success = false, message = ex.GetMessage() });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.BackupData.CheckWindowsService)]
    public async Task<JsonResult> CheckWindowsService(string type)
    {
        try
        {
            _logger.LogDebug("Starting CheckWindowsService action.");

            var workflowServiceStatus = await _dataProvider.WorkflowOperationGroup.CheckWindowsServiceConnection(type);

            return Json(workflowServiceStatus);
        }
        catch (Exception e)
        {
            _logger.Exception("Error occurred in CheckWindowsService action.", e);

            return Json(new { success = false, message = e.Message });
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.BackupData.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(BackUpViewModel backUpCommand)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in BackUpData");

        var formId = Request.Form["id"].ToString();

        try
        {
            dynamic properties = JsonConvert.DeserializeObject(backUpCommand.Properties);

            if (properties != null)
            {
                var ftpHostPassword = properties.ftphostPassword.ToString();

                var encryptedPassword = SecurityHelper.Encrypt(ftpHostPassword);

                properties.ftphostPassword = encryptedPassword;

                backUpCommand.Properties = JsonConvert.SerializeObject(properties);
            }

            if (formId.IsNullOrWhiteSpace())
            {
                var backUpCreateCommand = _mapper.Map<CreateBackUpCommand>(backUpCommand);

                var response = await _dataProvider.BackUp.CreateAsync(backUpCreateCommand);

                _logger.LogDebug($"Creating Backup data {backUpCreateCommand.DatabaseName}.");

                TempData.NotifySuccess(response.Message);
            }
            else
            {
                var backUpUpdateCommand = _mapper.Map<UpdateBackUpCommand>(backUpCommand);

                var response = await _dataProvider.BackUp.UpdateAsync(backUpUpdateCommand);

                _logger.LogDebug($"Updating backup data '{backUpCommand.DatabaseName}'");

                TempData.NotifySuccess(response.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in backup data, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation exception occurred on backup data: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on backup data while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

}