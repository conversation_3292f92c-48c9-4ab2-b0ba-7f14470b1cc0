﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence
{
    public interface IFastCopyMonitorLogsRepository : IRepository<FastCopyMonitorLog>
    {
        Task<List<FastCopyMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
    }
}
