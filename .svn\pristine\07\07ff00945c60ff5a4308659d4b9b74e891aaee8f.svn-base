using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.DynamicSubDashboard.Events.Create;

public class DynamicSubDashboardCreatedEventHandler : INotificationHandler<DynamicSubDashboardCreatedEvent>
{
    private readonly ILogger<DynamicSubDashboardCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public DynamicSubDashboardCreatedEventHandler(ILoggedInUserService userService,
        ILogger<DynamicSubDashboardCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(DynamicSubDashboardCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} DynamicSubDashboard",
            Entity = "DynamicSubDashboard",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"DynamicSubDashboard '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"DynamicSubDashboard '{createdEvent.Name}' created successfully.");
    }
}