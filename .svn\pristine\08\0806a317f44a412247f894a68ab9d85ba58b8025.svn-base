using ContinuityPatrol.Domain.ViewModels.ImpactActivityModel;

namespace ContinuityPatrol.Application.Features.ImpactActivity.Queries.GetList;

public class GetImpactActivityListQueryHandler : IRequestHandler<GetImpactActivityListQuery, List<ImpactActivityListVm>>
{
    private readonly IImpactActivityRepository _impactActivityRepository;
    private readonly IMapper _mapper;

    public GetImpactActivityListQueryHandler(IMapper mapper, IImpactActivityRepository impactActivityRepository)
    {
        _mapper = mapper;
        _impactActivityRepository = impactActivityRepository;
    }

    public async Task<List<ImpactActivityListVm>> Handle(GetImpactActivityListQuery request,
        CancellationToken cancellationToken)
    {
        var impactActivities = await _impactActivityRepository.ListAllAsync();

        var oneDay = DateTime.Today;

        var impactActivityList = impactActivities.Where(x => x.LastModifiedDate >= oneDay)
            .OrderByDescending(x => x.LastModifiedDate)
            .Take(100)
            .ToList();

        return impactActivityList.Count <= 0
            ? new List<ImpactActivityListVm>()
            : _mapper.Map<List<ImpactActivityListVm>>(impactActivityList);
    }
}