﻿using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;

public class CreateGroupPolicyCommandValidator : AbstractValidator<CreateGroupPolicyCommand>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;

    public CreateGroupPolicyCommandValidator(IGroupPolicyRepository groupPolicyRepository)
    {
        _groupPolicyRepository = groupPolicyRepository;

        RuleFor(p => p.GroupName)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Enter valid {PropertyName}.");

        RuleFor(p => p.Type)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
             .Must(p => (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals(ServiceType.MonitorService.ToLower())) ||
                       (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals(ServiceType.WorkflowService.ToLower())) ||
                       (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals(ServiceType.ResiliencyReadyService.ToLower())))
            .WithMessage("Please enter the valid {PropertyName}.");

        RuleFor(p => p.Properties)
            .Must(IsValidJsonObjcet)
            .WithMessage("{PropertyName} must be a valid Json string.");

        RuleFor(p => p)
            .MustAsync(IsNameUnique)
            .WithMessage("A Same Name Already Exists.");
    }

    private async Task<bool> IsNameUnique(CreateGroupPolicyCommand updateGroupPolicyCommand, CancellationToken token)
    {
        return !await _groupPolicyRepository.IsGroupPolicyNameUnique(updateGroupPolicyCommand.GroupName);
    }


    private bool IsValidJsonObjcet(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                var obj = JsonConvert.DeserializeObject(properties);
                return true;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}