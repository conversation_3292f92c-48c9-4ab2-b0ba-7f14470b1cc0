using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class SolutionHistoryFixture : IDisposable
{
    public List<SolutionHistory> SolutionHistoryPaginationList { get; set; }
    public List<SolutionHistory> SolutionHistoryList { get; set; }
    public SolutionHistory SolutionHistoryDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public SolutionHistoryFixture()
    {
        var fixture = new Fixture();

        SolutionHistoryList = fixture.Create<List<SolutionHistory>>();

        SolutionHistoryPaginationList = fixture.CreateMany<SolutionHistory>(20).ToList();

        SolutionHistoryPaginationList.ForEach(x => x.CompanyId = CompanyId);

        SolutionHistoryList.ForEach(x => x.CompanyId = CompanyId);

        SolutionHistoryDto = fixture.Create<SolutionHistory>();

        SolutionHistoryDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public SolutionHistory CreateSolutionHistory(
        string companyId = "COMPANY_123",
        string loginName = "testuser",
        string nodeId = "NODE_001",
        string actionId = "ACTION_001",
        string actionName = "Default Action",
        string properties = null,
        string version = "1.0",
        string updaterId = "UPDATER_001",
        string description = "Default Description",
        string comments = "Default Comments",
        bool isActive = true,
        bool isDelete = false)
    {
        return new SolutionHistory
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = companyId,
            LoginName = loginName,
            NodeId = nodeId,
            ActionId = actionId,
            ActionName = actionName,
            Properties = properties ?? "{\"type\": \"solution\", \"status\": \"active\"}",
            Version = version,
            UpdaterId = updaterId,
            Description = description,
            Comments = comments,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<SolutionHistory> CreateMultipleSolutionHistories(int count, string companyId = "COMPANY_123")
    {
        var histories = new List<SolutionHistory>();
        for (int i = 1; i <= count; i++)
        {
            histories.Add(CreateSolutionHistory(
                companyId: companyId,
                loginName: $"user{i}",
                nodeId: $"NODE_{i:D3}",
                actionId: $"ACTION_{i:D3}",
                actionName: $"Action {i}",
                version: $"1.{i}",
                updaterId: $"UPDATER_{i:D3}",
                description: $"Description for action {i}",
                comments: $"Comments for action {i}"
            ));
        }
        return histories;
    }

    public SolutionHistory CreateSolutionHistoryWithSpecificId(string referenceId, string actionName = "Test Action")
    {
        return new SolutionHistory
        {
            ReferenceId = referenceId,
            CompanyId = "COMPANY_TEST",
            LoginName = "testuser",
            NodeId = "NODE_TEST",
            ActionId = "ACTION_TEST",
            ActionName = actionName,
            Properties = "{\"test\": true}",
            Version = "1.0",
            UpdaterId = "UPDATER_TEST",
            Description = "Test Description",
            Comments = "Test Comments",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public SolutionHistory CreateSolutionHistoryForAction(string actionId, string actionName = null, string companyId = "COMPANY_123")
    {
        return CreateSolutionHistory(
            companyId: companyId,
            actionId: actionId,
            actionName: actionName ?? $"Action for {actionId}",
            description: $"History for action {actionId}",
            comments: $"Comments for action {actionId}"
        );
    }

    public List<SolutionHistory> CreateSolutionHistoriesForAction(string actionId, int count, string companyId = "COMPANY_123")
    {
        var histories = new List<SolutionHistory>();
        for (int i = 1; i <= count; i++)
        {
            histories.Add(CreateSolutionHistory(
                companyId: companyId,
                actionId: actionId,
                actionName: $"Action {actionId} - Version {i}",
                version: $"1.{i}",
                description: $"Version {i} of action {actionId}",
                comments: $"Changes in version {i}"
            ));
        }
        return histories;
    }

    public List<SolutionHistory> CreateSolutionHistoriesWithStatus(int activeCount, int inactiveCount, string companyId = "COMPANY_123")
    {
        var histories = new List<SolutionHistory>();

        for (int i = 1; i <= activeCount; i++)
        {
            histories.Add(CreateSolutionHistory(
                companyId: companyId,
                actionId: $"ACTIVE_ACTION_{i}",
                actionName: $"Active Action {i}",
                isActive: true
            ));
        }

        for (int i = 1; i <= inactiveCount; i++)
        {
            histories.Add(CreateSolutionHistory(
                companyId: companyId,
                actionId: $"INACTIVE_ACTION_{i}",
                actionName: $"Inactive Action {i}",
                isActive: false
            ));
        }

        return histories;
    }

    public SolutionHistory CreateSolutionHistoryForCompany(string companyId, string actionName = null)
    {
        return CreateSolutionHistory(
            companyId: companyId,
            actionName: actionName ?? $"Action for {companyId}",
            loginName: $"user@{companyId.ToLower()}.com",
            description: $"Solution history for company {companyId}"
        );
    }

    public List<SolutionHistory> CreateSolutionHistoriesForCompanies(List<string> companyIds)
    {
        var histories = new List<SolutionHistory>();
        foreach (var companyId in companyIds)
        {
            histories.Add(CreateSolutionHistoryForCompany(companyId));
        }
        return histories;
    }

    public SolutionHistory CreateSolutionHistoryWithProperties(Dictionary<string, object> properties)
    {
        var propertiesJson = System.Text.Json.JsonSerializer.Serialize(properties);
        return CreateSolutionHistory(properties: propertiesJson);
    }

    public SolutionHistory CreateSolutionHistoryForNode(string nodeId, string actionId = null, string companyId = "COMPANY_123")
    {
        return CreateSolutionHistory(
            companyId: companyId,
            nodeId: nodeId,
            actionId: actionId ?? $"ACTION_FOR_{nodeId}",
            actionName: $"Action for node {nodeId}",
            description: $"Solution history for node {nodeId}"
        );
    }

    public SolutionHistory CreateSolutionHistoryForUser(string loginName, string companyId = "COMPANY_123")
    {
        return CreateSolutionHistory(
            companyId: companyId,
            loginName: loginName,
            actionName: $"Action by {loginName}",
            description: $"Solution history created by {loginName}"
        );
    }

    public SolutionHistory CreateMinimalSolutionHistory()
    {
        return new SolutionHistory
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "MINIMAL_COMPANY",
            ActionId = "MINIMAL_ACTION",
            ActionName = "Minimal Action",
            Version = "1.0",
            IsActive = true,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
