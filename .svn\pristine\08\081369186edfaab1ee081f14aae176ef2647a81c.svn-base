﻿using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class PostgresMonitorLogsService : BaseService, IPostgresMonitorLogsService
{
    public PostgresMonitorLogsService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreatePostgresMonitorLogCommand createPostgresMonitorLogCommand)
    {
        Logger.LogDebug($"Create Postgres Monitor Log '{createPostgresMonitorLogCommand}'");

        return await Mediator.Send(createPostgresMonitorLogCommand);
    }

    public async Task<List<PostgresMonitorLogsListVm>> GetPostgresMonitorLogsList()
    {
        Logger.LogDebug("Get All Postgres Monitor Logs");

        return await Mediator.Send(new GetPostgresMonitorLogsListQuery());
    }

    public async Task<PostgresMonitorLogsDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Postgres MonitorLogs Detail By Id Id");

        Logger.LogDebug($"Get Postgres Detail by Id'{id}' ");

        return await Mediator.Send(new GetPostgresMonitorLogsDetailQuery { Id = id });
    }

    public async Task<PaginatedResult<PostgresMonitorLogsListVm>> GetPaginatedPostgresMonitorLogs(
        GetPostgresMonitorLogsPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in postgres MonitorLogs Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllPostgresMonitorLogsCacheKey,
            () => Mediator.Send(query));
    }

    public async Task<List<PostgresMonitorLogsDetailByTypeVm>> GetPostgresMonitorLogsDetailByTypeVm(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "Postgres MonitorLogs Detail By Type");

        Logger.LogDebug($"Get  Postgres Monitor logs Detail by Id '{type}'");

        return await Mediator.Send(new GetPostgresMonitorLogsDetailByTypeQuery { Type = type });
    }
}