﻿using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Commands.UpdateConditionActionId;
using ContinuityPatrol.Application.Features.CyberJobWorkflowScheduler.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberJobWorkflowSchedulerModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICyberJobWorkflowSchedulerService
{
    Task<BaseResponse> Update(CyberJobWorkflowSchedulerCommand schedulerCommand);
    Task<PaginatedResult<CyberJobWorkflowSchedulerListVm>> GetPaginatedCyberJobWorkflowScheduler(GetCyberJobWorkflowSchedulerPaginatedListQuery query);
}
