﻿using AutoFixture;
using ContinuityPatrol.Application.Features.FormHistory.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Admin.Controllers;
using ContinuityPatrol.Web.Attributes;
using FluentValidation.Results;
using System.Reflection;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class FormHistoryControllerShould
    {
        private FormHistoryController _controller;
        private readonly Mock<ILogger<FormHistoryController>> _mockLogger = new();
        private readonly Mock<IDataProvider> _mockDataProvider = new();
        private readonly Fixture _fixture = new();

        public FormHistoryControllerShould()
        {
            Initialize();
        }

        public void Initialize()
        {
            _controller = new FormHistoryController(
                    _mockLogger.Object,
                    _mockDataProvider.Object
                );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var controller = new FormHistoryController(
                _mockLogger.Object,
                _mockDataProvider.Object
            );

            // Assert
            Assert.NotNull(controller);
        }

        [Fact]
        public void List_ShouldReturnViewResult()
        {
            // Arrange & Act
            var result = _controller.List();

            // Assert
            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.NotNull(viewResult);
        }

        [Fact]
        public async Task GetFormHistoryByFormId_ReturnsJsonResultWithSuccess_WhenDataProviderReturnsData()
        {
            // Arrange
            var formId = "test-form-id";
            var version = "v1";
            var formHistory = new List<FormHistoryDetailVm>
            {
                new() { Id = "1", FormId = formId, Version = version, FormName = "Test Form" }
            };

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ReturnsAsync(formHistory);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            var successProperty = jsonResult.Value.GetType().GetProperty("success");
            var dataProperty = jsonResult.Value.GetType().GetProperty("data");

            Assert.True((bool)successProperty.GetValue(jsonResult.Value, null));
            Assert.Equal(formHistory, dataProperty.GetValue(jsonResult.Value, null));
        }

        [Fact]
        public async Task GetFormHistoryByFormId_ReturnsJsonResultWithSuccess_WhenVersionIsNull()
        {
            // Arrange
            var formId = "test-form-id";
            string version = null;
            var formHistory = new List<FormHistoryDetailVm>
            {
                new() { Id = "1", FormId = formId, Version = "1.0", FormName = "Test Form" }
            };

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ReturnsAsync(formHistory);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            var successProperty = jsonResult.Value.GetType().GetProperty("success");
            var dataProperty = jsonResult.Value.GetType().GetProperty("data");

            Assert.True((bool)successProperty.GetValue(jsonResult.Value, null));
            Assert.Equal(formHistory, dataProperty.GetValue(jsonResult.Value, null));
        }

        [Fact]
        public async Task GetFormHistoryByFormId_ReturnsJsonResultWithSuccess_WhenEmptyListReturned()
        {
            // Arrange
            var formId = "test-form-id";
            var version = "v1";
            var formHistory = new List<FormHistoryDetailVm>();

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ReturnsAsync(formHistory);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            var successProperty = jsonResult.Value.GetType().GetProperty("success");
            var dataProperty = jsonResult.Value.GetType().GetProperty("data");

            Assert.True((bool)successProperty.GetValue(jsonResult.Value, null));
            Assert.Equal(formHistory, dataProperty.GetValue(jsonResult.Value, null));
        }

        [Fact]
        public async Task GetFormHistoryByFormId_HandlesException_ReturnsJsonException()
        {
            // Arrange
            var formId = "test-form-id";
            var version = "v1";
            var exception = new Exception("Test exception");

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ThrowsAsync(exception);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            var baseResponse = Assert.IsType<BaseResponse>(jsonResult.Value);
            Assert.False(baseResponse.Success);
            Assert.Contains("Test exception", baseResponse.Message);
        }

        [Fact]
        public async Task GetFormHistoryByFormId_HandlesValidationException_ReturnsJsonException()
        {
            // Arrange
            var formId = "test-form-id";
            var version = "v1";
            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("FormId", "Validation failed")
            };
            var validationResult = new ValidationResult(validationFailures);
            var validationException = new ValidationException(validationResult);

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ThrowsAsync(validationException);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);

            var baseResponse = Assert.IsType<BaseResponse>(jsonResult.Value);
            Assert.False(baseResponse.Success);
            Assert.Contains("Validation failed", baseResponse.Message);
        }

        [Fact]
        public async Task GetFormHistoryByFormId_WithNullFormId_CallsDataProvider()
        {
            // Arrange
            string formId = null;
            var version = "v1";
            var formHistory = new List<FormHistoryDetailVm>();

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ReturnsAsync(formHistory);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            _mockDataProvider.Verify(dp => dp.FormHistory.GetFormHistoryById(formId, version), Times.Once);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public async Task GetFormHistoryByFormId_WithEmptyFormId_CallsDataProvider()
        {
            // Arrange
            var formId = "";
            var version = "v1";
            var formHistory = new List<FormHistoryDetailVm>();

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(formId, version))
                             .ReturnsAsync(formHistory);

            // Act
            var result = await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            _mockDataProvider.Verify(dp => dp.FormHistory.GetFormHistoryById(formId, version), Times.Once);
            var jsonResult = Assert.IsType<JsonResult>(result);
            Assert.NotNull(jsonResult.Value);
        }

        [Fact]
        public void GetFormHistoryByFormId_HasHttpPostAttribute()
        {
            // Arrange
            var method = typeof(FormHistoryController).GetMethod("GetFormHistoryByFormId");

            // Act
            var httpPostAttribute = method.GetCustomAttribute<HttpPostAttribute>();

            // Assert
            Assert.NotNull(httpPostAttribute);
        }

        [Fact]
        public void GetFormHistoryByFormId_HasValidateAntiForgeryTokenAttribute()
        {
            // Arrange
            var method = typeof(FormHistoryController).GetMethod("GetFormHistoryByFormId");

            // Act
            var validateAntiForgeryTokenAttribute = method.GetCustomAttribute<ValidateAntiForgeryTokenAttribute>();

            // Assert
            Assert.NotNull(validateAntiForgeryTokenAttribute);
        }

        [Fact]
        public void GetFormHistoryByFormId_HasAntiXssAttribute()
        {
            // Arrange
            var method = typeof(FormHistoryController).GetMethod("GetFormHistoryByFormId");

            // Act
            var antiXssAttribute = method.GetCustomAttribute<AntiXssAttribute>();

            // Assert
            Assert.NotNull(antiXssAttribute);
        }

        [Fact]
        public async Task GetFormHistoryByFormId_VerifiesDataProviderCalled_WithCorrectParameters()
        {
            // Arrange
            var formId = "specific-form-id";
            var version = "specific-version";
            var formHistory = new List<FormHistoryDetailVm>();

            _mockDataProvider.Setup(dp => dp.FormHistory.GetFormHistoryById(It.IsAny<string>(), It.IsAny<string>()))
                             .ReturnsAsync(formHistory);

            // Act
            await _controller.GetFormHistoryByFormId(formId, version);

            // Assert
            _mockDataProvider.Verify(dp => dp.FormHistory.GetFormHistoryById(formId, version), Times.Once);
            _mockDataProvider.Verify(dp => dp.FormHistory.GetFormHistoryById(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        }
    }
}
