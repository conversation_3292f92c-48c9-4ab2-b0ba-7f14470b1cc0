﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Queries;

public class GetFormTypeCategoryDetailQueryHandlerTests : IClassFixture<FormTypeCategoryFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly GetFormTypeCategoryDetailQueryHandler _handler;

    public GetFormTypeCategoryDetailQueryHandlerTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;

        _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId = Guid.NewGuid().ToString();

        var mockFormTypeCategoryRepository = FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories);
        _handler = new GetFormTypeCategoryDetailQueryHandler(mockFormTypeCategoryRepository.Object, _formTypeCategoryFixture.Mapper);
    }

    [Fact]
    public async Task Handle_ValidFormTypeCategoryId_ReturnsFormTypeCategoryDetailVm()
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var query = new GetFormTypeCategoryDetailQuery { Id = formTypeCategory.ReferenceId };

        var result = await _handler.Handle(query, CancellationToken.None);

        result.ShouldBeOfType<FormTypeCategoryDetailVm>();
        result.Id.ShouldBe(formTypeCategory.ReferenceId);
        result.Name.ShouldBe(formTypeCategory.Name);
        result.FormName.ShouldBe(formTypeCategory.FormName);
        result.FormTypeName.ShouldBe(formTypeCategory.FormTypeName);
        result.Version.ShouldBe(formTypeCategory.Version);
    }

    [Fact]
    public async Task Handle_ValidFormTypeCategoryId_CallsGetByReferenceIdAsyncOnRepository()
    {
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        var query = new GetFormTypeCategoryDetailQuery { Id = formTypeCategory.ReferenceId };

        var mockRepository = FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories);
        var handler = new GetFormTypeCategoryDetailQueryHandler(mockRepository.Object, _formTypeCategoryFixture.Mapper);

        await handler.Handle(query, CancellationToken.None);

        mockRepository.Verify(repo => repo.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_InvalidFormTypeCategoryId_ThrowsNotFoundException()
    {
        var query = new GetFormTypeCategoryDetailQuery { Id = Guid.NewGuid().ToString() };

        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_NullId_ThrowsNotFoundException()
    {
        var query = new GetFormTypeCategoryDetailQuery { Id = null };

        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_EmptyId_ThrowsNotFoundException()
    {
        // Arrange
        var query = new GetFormTypeCategoryDetailQuery { Id = string.Empty };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_InvalidGuidFormat_ThrowsNotFoundException()
    {
        // Arrange
        var query = new GetFormTypeCategoryDetailQuery { Id = "invalid-guid" };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_NonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var query = new GetFormTypeCategoryDetailQuery { Id = Guid.NewGuid().ToString() };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act & Assert
        var exception = await Should.ThrowAsync<NotFoundException>(async () =>
            await handler.Handle(query, CancellationToken.None));

        exception.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_ValidFormTypeCategoryId_ReturnsCorrectMappedProperties()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        formTypeCategory.ReferenceId = Guid.NewGuid().ToString();
        formTypeCategory.Name = "Test FormTypeCategory Name";
        formTypeCategory.FormName = "Test Form Name";
        formTypeCategory.FormTypeName = "Test FormType Name";
        formTypeCategory.Version = "{\"version\": \"1.0\"}";

        var query = new GetFormTypeCategoryDetailQuery { Id = formTypeCategory.ReferenceId };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.Name.ShouldBe("Test FormTypeCategory Name");
        result.FormName.ShouldBe("Test Form Name");
        result.FormTypeName.ShouldBe("Test FormType Name");
        result.Version.ShouldBe("{\"version\": \"1.0\"}");
    }

    [Fact]
    public async Task Handle_ValidFormTypeCategoryId_UsesAutoMapper()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        formTypeCategory.ReferenceId = Guid.NewGuid().ToString();

        var query = new GetFormTypeCategoryDetailQuery { Id = formTypeCategory.ReferenceId };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<FormTypeCategoryDetailVm>();
    }

    [Fact]
    public async Task Handle_Should_Access_All_FormTypeCategoryDetailVm_Properties_For_Coverage()
    {
        // Arrange
        var formTypeCategory = _formTypeCategoryFixture.FormTypeCategories.First();
        formTypeCategory.ReferenceId = Guid.NewGuid().ToString();
        formTypeCategory.FormId = Guid.NewGuid().ToString();
        formTypeCategory.FormTypeId = Guid.NewGuid().ToString();
        formTypeCategory.Logo = "test-logo.png";
        formTypeCategory.Properties = "{\"test\": \"properties\"}";
        formTypeCategory.FormVersion = "1.0.0";

        var query = new GetFormTypeCategoryDetailQuery { Id = formTypeCategory.ReferenceId };

        var handler = new GetFormTypeCategoryDetailQueryHandler(
            FormTypeCategoryRepositoryMocks.GetFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories).Object,
            _formTypeCategoryFixture.Mapper);

        // Act
        var result = await handler.Handle(query, CancellationToken.None);

        // Assert - Access all property getters for coverage
        result.ShouldNotBeNull();
        var id = result.Id; // Covers Id getter
        var name = result.Name; // Covers Name getter
        var formId = result.FormId; // Covers FormId getter (line 7)
        var formName = result.FormName; // Covers FormName getter
        var formTypeId = result.FormTypeId; // Covers FormTypeId getter (line 9)
        var formTypeName = result.FormTypeName; // Covers FormTypeName getter
        var logo = result.Logo; // Covers Logo getter (line 11)
        var version = result.Version; // Covers Version getter
        var properties = result.Properties; // Covers Properties getter (line 13)
        var formVersion = result.FormVersion; // Covers FormVersion getter (line 14)

        // Verify properties are accessible
        id.ShouldNotBeNull();
        formId.ShouldNotBeNull();
        formTypeId.ShouldNotBeNull();
        logo.ShouldNotBeNull();
        properties.ShouldNotBeNull();
        formVersion.ShouldNotBeNull();

        // Test property setters for coverage
        var testVm = new FormTypeCategoryDetailVm();
        testVm.Id = "test-id"; // Covers Id setter
        testVm.Name = "test-name"; // Covers Name setter
        testVm.FormId = "test-form-id"; // Covers FormId setter
        testVm.FormName = "test-form-name"; // Covers FormName setter
        testVm.FormTypeId = "test-form-type-id"; // Covers FormTypeId setter
        testVm.FormTypeName = "test-form-type-name"; // Covers FormTypeName setter
        testVm.Logo = "test-logo"; // Covers Logo setter
        testVm.Version = "test-version"; // Covers Version setter
        testVm.Properties = "test-properties"; // Covers Properties setter
        testVm.FormVersion = "test-form-version"; // Covers FormVersion setter

        // Verify setters worked correctly
        testVm.Id.ShouldBe("test-id");
        testVm.Name.ShouldBe("test-name");
        testVm.FormId.ShouldBe("test-form-id");
        testVm.FormName.ShouldBe("test-form-name");
        testVm.FormTypeId.ShouldBe("test-form-type-id");
        testVm.FormTypeName.ShouldBe("test-form-type-name");
        testVm.Logo.ShouldBe("test-logo");
        testVm.Version.ShouldBe("test-version");
        testVm.Properties.ShouldBe("test-properties");
        testVm.FormVersion.ShouldBe("test-form-version");
    }
}