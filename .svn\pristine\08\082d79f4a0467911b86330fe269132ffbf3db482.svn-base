﻿using ContinuityPatrol.Application.Features.Template.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Queries;

public class GetTemplateListQueryHandlerTests
{
    private readonly Mock<ITemplateRepository> _mockTemplateRepository;
    private readonly Mock<IJobRepository> _mockJobRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetTemplateListQueryHandler _handler;

    public GetTemplateListQueryHandlerTests()
    {
        _mockTemplateRepository = new Mock<ITemplateRepository>();
        _mockJobRepository = new Mock<IJobRepository>();
        _mockMapper = new Mock<IMapper>();

        _handler = new GetTemplateListQueryHandler(
            _mockMapper.Object,
            _mockTemplateRepository.Object,
            _mockJobRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnsGroupedTemplates_WithDeleteFlags_WhenJobsExist()
    {
        // Arrange
        var templates = new List<Domain.Entities.Template>
            {
                new Domain.Entities.Template
                {
                    Id = 1,
                    ReferenceId = "temp-1",
                    Name = "Template A",
                    Properties = "Props",
                    ReplicationTypeId = "rep-1",
                    ReplicationTypeName = "Replication One"
                },
                new Domain.Entities.Template
                {
                    Id = 2,
                    ReferenceId = "temp-2",
                    Name = "Template B",
                    Properties = "Props",
                    ReplicationTypeId = "rep-1",
                    ReplicationTypeName = "Replication One"
                }
            };

        var mappedTemplateListVm = new List<TemplateListVm>
            {
                new TemplateListVm { Id = "temp-1", Name = "Template A", Properties = "Props" },
                new TemplateListVm { Id = "temp-2", Name = "Template B", Properties = "Props" }
            };

        var jobList = new List<Domain.Entities.Job>
            {
                new Domain.Entities.Job { Id = 101, TemplateId = "temp-1" }
            };

        _mockTemplateRepository
            .Setup(x => x.ListAllAsync())
            .ReturnsAsync(templates);

        _mockMapper
            .Setup(x => x.Map<List<TemplateListVm>>(It.IsAny<List<Domain.Entities.Template>>()))
            .Returns(mappedTemplateListVm);

        _mockMapper
            .Setup(x => x.Map<List<TemplateListVm>>(It.Is<List<Domain.Entities.Template>>(list => list.All(t => t.ReplicationTypeId == "rep-1"))))
            .Returns(mappedTemplateListVm);

        _mockJobRepository
            .Setup(x => x.GetJobByIds(It.IsAny<List<string>>()))
            .ReturnsAsync(jobList);

        // Act
        var result = await _handler.Handle(new GetTemplateListQuery(), CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result[0].ReplicationTypeId.ShouldBe("rep-1");
        result[0].TemplateListVm.Count.ShouldBe(2);
        result[0].TemplateListVm.First(t => t.Id == "temp-1").IsDelete.ShouldBeTrue();
        result[0].TemplateListVm.First(t => t.Id == "temp-2").IsDelete.ShouldBeFalse();

        _mockTemplateRepository.Verify(x => x.ListAllAsync(), Times.Once);
        _mockJobRepository.Verify(x => x.GetJobByIds(It.IsAny<List<string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoTemplatesExist()
    {
        // Arrange
        _mockTemplateRepository
            .Setup(x => x.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.Template>());

        _mockMapper
            .Setup(x => x.Map<List<TemplateListVm>>(It.IsAny<List<Domain.Entities.Template>>()))
            .Returns(new List<TemplateListVm>());

        // Act
        var result = await _handler.Handle(new GetTemplateListQuery(), CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();

        _mockTemplateRepository.Verify(x => x.ListAllAsync(), Times.Once);

        // Fix: Expect GetJobByIds to be called with an empty list
        _mockJobRepository.Verify(x => x.GetJobByIds(It.Is<List<string>>(ids => ids.Count == 0)), Times.Once);
    }
    [Fact]
    public void Should_Assign_And_Assert_GetTemplateListVm()
    {
        // Arrange
        var templateListVm = new GetTemplateListVm
        {
            ReplicationTypeId = "R01",
            ReplicationTypeName = "Full Replication",
            TemplateListVm = new List<TemplateListVm>
        {
            new TemplateListVm
            {
                Id = "T001",
                Name = "Template 1"
            },
            new TemplateListVm
            {
                Id = "T002",
                Name = "Template 2"
            }
        }
        };

        // Assert
        Assert.Equal("R01", templateListVm.ReplicationTypeId);
        Assert.Equal("Full Replication", templateListVm.ReplicationTypeName);
        Assert.Equal(2, templateListVm.TemplateListVm.Count);
        Assert.Equal("T001", templateListVm.TemplateListVm[0].Id);
        Assert.Equal("Template 1", templateListVm.TemplateListVm[0].Name);
        Assert.Equal("T002", templateListVm.TemplateListVm[1].Id);
        Assert.Equal("Template 2", templateListVm.TemplateListVm[1].Name);
    }

    //[Fact]
    //public async Task Handle_SkipsNullIds_WithoutCrashing()
    //{
    //    // Arrange
    //    var templates = new List<Domain.Entities.Template>
    //        {
    //            new Domain.Entities.Template
    //            {
    //                Id = 1,
    //                ReferenceId = null, // Null ID triggers .IsNotNullOrWhiteSpace()
    //                Name = "Template X",
    //                Properties = "Props",
    //                ReplicationTypeId = "rep-2",
    //                ReplicationTypeName = "Replication Two"
    //            }
    //        };

    //    var mappedVm = new List<TemplateListVm>
    //        {
    //            new TemplateListVm { Id = null, Name = "Template X", Properties = "Props" }
    //        };

    //    _mockTemplateRepository
    //        .Setup(x => x.ListAllAsync())
    //        .ReturnsAsync(templates);

    //    _mockMapper
    //        .Setup(x => x.Map<List<TemplateListVm>>(It.IsAny<List<Domain.Entities.Template>>()))
    //        .Returns(mappedVm);

    //    _mockMapper
    //        .Setup(x => x.Map<List<TemplateListVm>>(It.Is<List<Domain.Entities.Template>>(list => list[0].ReplicationTypeId == "rep-2")))
    //        .Returns(mappedVm);

    //    // Act
    //    var result = await _handler.Handle(new GetTemplateListQuery(), CancellationToken.None);

    //    // Assert
    //    result.ShouldNotBeNull();
    //    result.Count.ShouldBe(1);
    //    result[0].TemplateListVm.First().IsDelete.ShouldBeFalse(); // No job mapping due to null Id
    //}
}