﻿using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Create;
using ContinuityPatrol.Application.Features.SolutionHistory.Commands.Update;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetList;
using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Domain.ViewModels.SolutionHistoryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;


namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class SolutionHistoryController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<SolutionHistoryListVm>>> GetSolutionHistories()
    {
        Logger.LogInformation("Get All Solution Histories");

        return Ok(await Mediator.Send(new GetSolutionHistoryListQuery()));
    }

    [HttpGet("{id}", Name = "GetSolutionHistory")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<SolutionHistoryDetailVm>> GetSolutionHistoryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Solution History Id");

        Logger.LogInformation($"Get Solution History Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetSolutionHistoryDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateSolutionHistoryResponse>> CreateSolutionHistory([FromBody] CreateSolutionHistoryCommand createSolutionHistoryCommand)
    {
        Logger.LogInformation($"Create Solution History '{createSolutionHistoryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateSolutionHistory), await Mediator.Send(createSolutionHistoryCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateSolutionHistoryResponse>> UpdateSolutionHistory([FromBody] UpdateSolutionHistoryCommand updateSolutionHistoryCommand)
    {
        Logger.LogInformation($"Update Solution History '{updateSolutionHistoryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateSolutionHistoryCommand));
    }

    [HttpGet, Route("by/{actionId}")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<SolutionHistoryByActionIdQueryVm>>> GetSolutionHistoryByActionId(string actionId)
    {
        Guard.Against.InvalidGuidOrEmpty(actionId, "Action Id");

        Logger.LogInformation($"Get Solution History Detail by ActionId '{actionId}'");

        return Ok(await Mediator.Send(new GetSolutionHistoryByActionIdQuery { ActionId = actionId }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllSolutionHistoryCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllSolutionHistoryNameCacheKey };

        ClearCache(cacheKeys);
    }
}

