﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Setting.Events.Delete;

public class SettingDeletedEventHandler : INotificationHandler<SettingDeletedEvent>
{
    private readonly ILogger<SettingDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SettingDeletedEventHandler(ILoggedInUserService userService, ILogger<SettingDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SettingDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.Setting}",
            Entity = Modules.Setting.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Setting '{deletedEvent.SKey}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Setting '{deletedEvent.SKey}' deleted successfully.");
    }
}