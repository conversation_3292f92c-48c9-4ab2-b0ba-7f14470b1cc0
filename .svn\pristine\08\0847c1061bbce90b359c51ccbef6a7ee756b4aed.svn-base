﻿using ContinuityPatrol.Application.Features.AlertInformation.Commands.Create;
using ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Commands;

public class CreateAlertInformationTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private readonly Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly CreateAlertInformationCommandHandler _handler;

    public CreateAlertInformationTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;
        
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.CreateAlertInformationRepository(_alertInformationFixture.AlertInformations);
        
        _handler = new CreateAlertInformationCommandHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper );
    }

    [Fact]
    public async Task Handle_IncreaseAlertInformationCount_When_AlertInformationCreated()
    {
        await _handler.Handle(_alertInformationFixture.CreateAlertInformationCommand, CancellationToken.None);

        var allCategories = await _mockAlertInformationRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_alertInformationFixture.AlertInformations.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateAlertInformationResponse_When_AlertInformationCreated()
    {
        var result = await _handler.Handle(_alertInformationFixture.CreateAlertInformationCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateAlertInformationResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_alertInformationFixture.CreateAlertInformationCommand, CancellationToken.None);

        _mockAlertInformationRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.AlertInformation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsExpectedId_FromRepository()
    {
        //Ensure the ID returned by the repository is reflected in the response.

        _mockAlertInformationRepository
         .Setup(repo => repo.AddAsync(It.IsAny<Domain.Entities.AlertInformation>()))
         .ReturnsAsync(new Domain.Entities.AlertInformation
         {
             ReferenceId = "123",
             // Add other properties if needed
         });

        var result = await _handler.Handle(_alertInformationFixture.CreateAlertInformationCommand, CancellationToken.None);

        result.Id.ShouldBe("123");
    }

    [Fact]
    public async Task Handle_SetsCorrectFields_InEntity()
    {
        //Verify that the created entity has expected field values before saving.

        Domain.Entities.AlertInformation? capturedEntity = null;

        _mockAlertInformationRepository
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.AlertInformation>()))
            .Callback<Domain.Entities.AlertInformation>(entity => capturedEntity = entity)
            .ReturnsAsync(new Domain.Entities.AlertInformation
            {
                Id = 1,
                // set other properties if needed
            });


        await _handler.Handle(_alertInformationFixture.CreateAlertInformationCommand, CancellationToken.None);

        capturedEntity.ShouldNotBeNull();
        capturedEntity.Type.ShouldBe(_alertInformationFixture.CreateAlertInformationCommand.Type);
        capturedEntity.Severity.ShouldBe(_alertInformationFixture.CreateAlertInformationCommand.Severity);
        capturedEntity.Code.ShouldBe(_alertInformationFixture.CreateAlertInformationCommand.Code);
        capturedEntity.AlertFrequency.ToString().ShouldBe(_alertInformationFixture.CreateAlertInformationCommand.AlertFrequency.ToString());
    }
    [Theory]
    [InlineData(0)]
    [InlineData(1)]
    [InlineData(-1)]
    [InlineData(100)]
    [InlineData(int.MinValue)]
    [InlineData(int.MaxValue)]
    public void CreateAlertInformationCommand_Should_HandleDifferentAlertFrequencies_When_VariousValuesProvided(int alertFrequency)
    {
        // Act
        var command = new CreateAlertInformationCommand
        {
            AlertFrequency = alertFrequency
        };

        // Assert
        command.AlertFrequency.ShouldBe(alertFrequency);
    }
    [Fact]
    public Task Handle_CreateAlertInformation_When_AllPropertiesProvided()
    {
        // Arrange
        var type = "Critical Alert";
        var severity = "High";
        var code = "ALT001";
        var alertFrequency = 5;

        // Act
        var command = new CreateAlertInformationCommand
        {
            Type = type,
            Severity = severity,
            Code = code,
            AlertFrequency = alertFrequency
        };

        // Assert
        command.Type.ShouldBe(type);
        command.Severity.ShouldBe(severity);
        command.Code.ShouldBe(code);
        command.AlertFrequency.ShouldBe(alertFrequency);
        return Task.CompletedTask;
    }

    [Fact]
    public void CreateAlertInformationCommand_Should_HandleNullValues_When_NullPropertiesSet()
    {
        // Act
        var command = new CreateAlertInformationCommand
        {
            Type = null,
            Severity = null,
            Code = null,
            AlertFrequency = 0
        };

        // Assert
        command.Type.ShouldBeNull();
        command.Severity.ShouldBeNull();
        command.Code.ShouldBeNull();
        command.AlertFrequency.ShouldBe(0);
    }
    [Fact]
    public void ToString_Should_ReturnCorrectFormat_When_TypeAndIdAreSet()
    {
        // Arrange
        var command = new CreateAlertInformationCommand
        {
           
            Type = "Test Alert Type",
            Severity = "High",
            Code = "TEST001",
            AlertFrequency = 5
        };

        // Act
        var result = command.ToString();

        // Assert
        result.ShouldBe("Type: Test Alert Type;");
    }

    [Fact]
    public void ToString_Should_ReturnCorrectFormat_When_TypeIsNullAndIdIsSet()
    {
        // Arrange
        var command = new CreateAlertInformationCommand
        {
           
            Type = null,
            Severity = "High",
            Code = "TEST001",
            AlertFrequency = 5
        };

        // Act
        var result = command.ToString();

        // Assert
        result.ShouldBe("Type: ;");
    }
    [Theory]
    [InlineData("System Alert", "Low", "SYS001", 1)]
    [InlineData("Database Alert", "Medium", "DB002", 10)]
    [InlineData("Network Alert", "High", "NET003", 15)]
    [InlineData("Security Alert", "Critical", "SEC004", 30)]
    public void CreateAlertInformationCommand_Should_SetProperties_When_DifferentValuesProvided(
        string type, string severity, string code, int alertFrequency)
    {
        // Act
        var command = new CreateAlertInformationCommand
        {
            Type = type,
            Severity = severity,
            Code = code,
            AlertFrequency = alertFrequency
        };

        // Assert
        command.Type.ShouldBe(type);
        command.Severity.ShouldBe(severity);
        command.Code.ShouldBe(code);
        command.AlertFrequency.ShouldBe(alertFrequency);
    }
}