﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.TeamMaster.Commands.Create;

public class CreateTeamMasterCommand : IRequest<CreateTeamMasterResponse>
{
    public string GroupName { get; set; }

    public string Description { get; set; }

    public string MemberCount { get; set; }

    [JsonIgnore] public string CreatedBy { get; set; }

    [JsonIgnore] public string LastModifiedBy { get; set; }


    public override string ToString()
    {
        return $"Group Name: {GroupName};Description: {Description};";
    }
}