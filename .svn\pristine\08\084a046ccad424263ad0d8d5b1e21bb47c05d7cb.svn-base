using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGapLog.Events.Create;

public class CyberAirGapLogCreatedEventHandler : INotificationHandler<CyberAirGapLogCreatedEvent>
{
    private readonly ILogger<CyberAirGapLogCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAirGapLogCreatedEventHandler(ILoggedInUserService userService,
        ILogger<CyberAirGapLogCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(CyberAirGapLogCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} CyberAirGapLog",
            Entity = "CyberAirGapLog",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"CyberAirGapLog '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CyberAirGapLog '{createdEvent.Name}' created successfully.");
    }
}