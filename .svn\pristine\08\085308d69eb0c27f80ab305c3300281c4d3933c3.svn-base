﻿namespace ContinuityPatrol.Application.Features.AlertInformation.Commands.Update;

public class
    UpdateAlertInformationCommandHandler : IRequestHandler<UpdateAlertInformationCommand,
        UpdateAlertInformationResponse>
{
    private readonly IAlertInformationRepository _alertInformationRepository;
    private readonly IMapper _mapper;

    public UpdateAlertInformationCommandHandler(IAlertInformationRepository alertInformationRepository, IMapper mapper)
    {
        _alertInformationRepository = alertInformationRepository;
        _mapper = mapper;
    }

    public async Task<UpdateAlertInformationResponse> Handle(UpdateAlertInformationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _alertInformationRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.AlertInformation), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateAlertInformationCommand),
            typeof(Domain.Entities.AlertInformation));

        await _alertInformationRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateAlertInformationResponse
        {
            Message = Message.Update(nameof(Domain.Entities.AlertInformation), eventToUpdate.Type),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}