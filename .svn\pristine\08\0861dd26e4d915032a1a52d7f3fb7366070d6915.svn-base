using ContinuityPatrol.Application.Features.DataLag.Events.Delete;

namespace ContinuityPatrol.Application.Features.DataLag.Commands.Delete;

public class DeleteDataLagCommandHandler : IRequestHandler<DeleteDataLagCommand, DeleteDataLagResponse>
{
    private readonly IDataLagRepository _dataLagRepository;
    private readonly IPublisher _publisher;

    public DeleteDataLagCommandHandler(IDataLagRepository dataLagRepository, IPublisher publisher)
    {
        _dataLagRepository = dataLagRepository;

        _publisher = publisher;
    }

    public async Task<DeleteDataLagResponse> Handle(DeleteDataLagCommand request, CancellationToken cancellationToken)
    {
        var eventToDelete = await _dataLagRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DataLag),
            new NotFoundException(nameof(Domain.Entities.DataLag), request.Id));

        eventToDelete.IsActive = false;

        await _dataLagRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDataLagResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DataLag), eventToDelete.BusinessServiceName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DataLagDeletedEvent { Name = eventToDelete.BusinessServiceName },
            cancellationToken);

        return response;
    }
}