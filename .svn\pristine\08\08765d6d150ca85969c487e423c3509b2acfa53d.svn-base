﻿using ContinuityPatrol.Application.Features.BusinessService.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessService.Queries;

public class GetBusinessServiceListQueryHandlerTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private Mock<IBusinessServiceRepository> _mockBusinessServiceRepository;
    private readonly GetBusinessServiceListQueryHandler _handler;

    public GetBusinessServiceListQueryHandlerTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;

        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetBusinessServiceRepository(_businessServiceFixture.BusinessServices);

        _handler = new GetBusinessServiceListQueryHandler(_businessServiceFixture.Mapper, _mockBusinessServiceRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_BusinessServicesCount()
    {
        var result = await _handler.Handle(new GetBusinessServiceListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetBusinessServiceListQuery(), CancellationToken.None);

        _mockBusinessServiceRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_BusinessServicesDetail()
    {
        var result = await _handler.Handle(new GetBusinessServiceListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<BusinessServiceListVm>>();

        result[0].Id.ShouldBe(_businessServiceFixture.BusinessServices[0].ReferenceId);
        result[0].Name.ShouldBe(_businessServiceFixture.BusinessServices[0].Name);
        result[0].CompanyId.ShouldBe(_businessServiceFixture.BusinessServices[0].CompanyId);
        result[0].CompanyName.ShouldBe(_businessServiceFixture.BusinessServices[0].CompanyName);
        result[0].Priority.ShouldBe(_businessServiceFixture.BusinessServices[0].Priority);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockBusinessServiceRepository = BusinessServiceRepositoryMocks.GetBusinessServiceEmptyRepository();

        var handler = new GetBusinessServiceListQueryHandler(_businessServiceFixture.Mapper, _mockBusinessServiceRepository.Object);

        var result = await handler.Handle(new GetBusinessServiceListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}