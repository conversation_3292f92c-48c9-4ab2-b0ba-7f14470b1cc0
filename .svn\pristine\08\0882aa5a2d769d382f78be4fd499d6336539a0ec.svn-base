﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Shared.Services.Base;

public class BaseService
{
    private readonly IHttpContextAccessor _accessor;
    public HttpContext Current => _accessor?.HttpContext;

    private IAppCache _cache;

    private ILoggedInUserService _loggedInUserService;

    private IMapper _mapperInstance;

    private IMediator _mediatorInstance;

    private ILogger<BaseService> _loggerInstance;
    protected ILogger Logger => _loggerInstance ??= Current.RequestServices.GetService<ILogger<BaseService>>();
    protected ILoggedInUserService LoggedInUserService => _loggedInUserService ??= Current.RequestServices.GetService<ILoggedInUserService>();
    public IAppCache Cache => _cache ??= Current.RequestServices.GetService<IAppCache>();
    protected IMediator Mediator => _mediatorInstance ??= Current.RequestServices.GetService<IMediator>();
    protected IMapper Mapper => _mapperInstance ??= Current.RequestServices.GetService<IMapper>();

    public BaseService(IHttpContextAccessor accessor)
    {
        _accessor = accessor;
    }
}
