using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.PageWidget.Events.Delete;

public class PageWidgetDeletedEventHandler : INotificationHandler<PageWidgetDeletedEvent>
{
    private readonly ILogger<PageWidgetDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public PageWidgetDeletedEventHandler(ILoggedInUserService userService,
        ILogger<PageWidgetDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(PageWidgetDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} PageWidget",
            Entity = "PageWidget",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"PageWidget '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"PageWidget '{deletedEvent.Name}' deleted successfully.");
    }
}