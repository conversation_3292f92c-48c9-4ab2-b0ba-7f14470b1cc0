﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class EscalationMatrixFilterSpecification : Specification<EscalationMatrix>
{
    public EscalationMatrixFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.EscMatCode != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("Matrix Code &amp; Name=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.EscMatCode.Contains(stringItem.Replace("Matrix Code &amp; Name=", "",
                            StringComparison.OrdinalIgnoreCase)));

                /*  else if (stringItem.Contains("logoname=", StringComparison.OrdinalIgnoreCase))
                      Or(p => p.LogoName.Contains(stringItem.Replace("logoname=", "",
                          StringComparison.OrdinalIgnoreCase)));*/

                /*   else if (stringItem.Contains("Matrix Create Update Details=", StringComparison.OrdinalIgnoreCase))
                       Or(p => p.CreatedDate.Contains(stringItem.Replace("Matrix Create Update Details=", "", StringComparison.OrdinalIgnoreCase)));

                   else if (stringItem.Contains("webaddress=", StringComparison.OrdinalIgnoreCase))
                       Or(p => p.WebAddress.Contains(stringItem.Replace("webaddress=", "",
                           StringComparison.OrdinalIgnoreCase)));*/
            }
            else
            {
                Criteria = p =>
                    p.EscMatCode.Contains(searchString);
            }
        }
    }
}