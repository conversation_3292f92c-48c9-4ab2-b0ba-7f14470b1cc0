﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Import;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Lock;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;
using ContinuityPatrol.Application.Features.WorkflowAction.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetDetailByName;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetNames;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetWorkflowActionByNodeId;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Admin;

public class WorkflowActionService : BaseService, IWorkflowActionService
{
    public WorkflowActionService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<WorkflowActionNameVm>> GetWorkflowActionNames()
    {
        Logger.LogDebug("Get All WorkflowAction Names");

        return await Mediator.Send(new GetWorkflowActionNameQuery());
    }

    public async Task<List<WorkflowActionListVm>> GetWorkflowActionList()
    {
        Logger.LogDebug("Get All WorkflowActions");

        return await Mediator.Send(new GetWorkflowActionListQuery());
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowActionCommand createWorkflowActionCommand)
    {
        Logger.LogDebug($"Create WorkflowAction '{createWorkflowActionCommand}'");

        return await Mediator.Send(createWorkflowActionCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowActionCommand updateWorkflowActionCommand)
    {
        Logger.LogDebug($"Update WorkflowAction '{updateWorkflowActionCommand}'");

        return await Mediator.Send(updateWorkflowActionCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowActionId)
    {
        Guard.Against.InvalidGuidOrEmpty(workflowActionId, "WorkflowAction Id");

        Logger.LogDebug($"Delete WorkflowAction Details by Id '{workflowActionId}'");

        return await Mediator.Send(new DeleteWorkflowActionCommand { Id = workflowActionId });
    }

    public async Task<WorkflowActionDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "WorkflowAction Id");

        Logger.LogDebug($"Get All WorkflowAction Detail by Id '{id}'");

        return await Mediator.Send(new GetWorkflowActionDetailQuery { Id = id });
    }

    public async Task<bool> IsWorkflowActionNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "WorkflowAction Name");

        Logger.LogDebug($"Check Name Exists Details by WorkflowAction Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetWorkflowActionNameUniqueQuery
            { WorkflowActionName = name, WorkflowActionId = id });
    }

    public async Task<List<GetWorkflowActionByNodeIdVm>> GetWorkflowActionByNodeId(string nodeId)
    {
        Guard.Against.InvalidGuidOrEmpty(nodeId, "WorkflowAction Id");

        Logger.LogDebug($"Get All WorkflowAction Detail by Node Id '{nodeId}'");

        return await Mediator.Send(new GetWorkflowActionByNodeIdQuery { Id = nodeId });
    }

    public async Task<PaginatedResult<WorkflowActionListVm>> GetPaginatedWorkflowAction(
        GetWorkflowActionPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in WorkflowAction Paginated List");

        return await Mediator.Send(query);
    }

    public async Task<BaseResponse> SaveAsWorkflowAction(SaveAsWorkflowActionCommand saveAsWorkflowActionCommand)
    {
        Logger.LogDebug($"SaveAs WorkflowAction '{saveAsWorkflowActionCommand.Name}'");

        return await Mediator.Send(saveAsWorkflowActionCommand);
    }

    public async Task<BaseResponse> UpdateWorkflowActionLock(UpdateWorkflowActionLockCommand workflowActionLock)
    {
        Logger.LogDebug($"Update Form lock '{workflowActionLock.Id}'");

        return await Mediator.Send(workflowActionLock);
    }

    public async Task<BaseResponse> ImportWorkflowActionAsync(ImportWorkflowActionCommand importWorkflowActionCommand)
    {
        Logger.LogDebug($"Import WorkflowAction '{importWorkflowActionCommand}'");

        return await Mediator.Send(importWorkflowActionCommand);
    }

    public async  Task<WorkflowActionDetailVm> GetWorkflowActionByName(string name)
    {
        Logger.LogDebug($"Get WorkflowAction Details by Name '{name}'");

        return await Mediator.Send(new GetWorkflowActionDetailByNameQuery { Name = name });
    }
}