﻿namespace ContinuityPatrol.Application.Features.ServerType.Queries.GetDetail;

public class GetServerTypeDetailQueryHandler : IRequestHandler<GetServerTypeDetailQuery, ServerTypeDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IServerTypeRepository _serverTypeRepository;

    public GetServerTypeDetailQueryHandler(IMapper mapper, IServerTypeRepository serverTypeRepository)
    {
        _mapper = mapper;
        _serverTypeRepository = serverTypeRepository;
    }

    public async Task<ServerTypeDetailVm> Handle(GetServerTypeDetailQuery request, CancellationToken cancellationToken)
    {
        var serverType = await _serverTypeRepository.GetServerTypeById(request.Id);

        Guard.Against.NullOrDeactive(serverType, nameof(Domain.Entities.ServerType),
            new NotFoundException(nameof(Domain.Entities.ServerType), request.Id));

        var serverTypeDetailDto = _mapper.Map<ServerTypeDetailVm>(serverType);

        return serverTypeDetailDto;
    }
}