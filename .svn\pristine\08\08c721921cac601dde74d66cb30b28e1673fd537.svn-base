﻿namespace ContinuityPatrol.Application.Features.ImpactAvailability.Queries.GetDetail;

public class
    GetImpactAvailabilityDetailQueryHandler : IRequestHandler<GetImpactAvailabilityDetailQuery,
        ImpactAvailabilityDetailVm>
{
    private readonly IImpactAvailabilityRepository _impactAvailabilityRepository;
    private readonly IMapper _mapper;

    public GetImpactAvailabilityDetailQueryHandler(IImpactAvailabilityRepository impactAvailabilityRepository,
        IMapper mapper)
    {
        _impactAvailabilityRepository = impactAvailabilityRepository;
        _mapper = mapper;
    }

    public async Task<ImpactAvailabilityDetailVm> Handle(GetImpactAvailabilityDetailQuery request,
        CancellationToken cancellationToken)
    {
        var impactDetail = await _impactAvailabilityRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(impactDetail, nameof(Domain.Entities.ImpactAvailability),
            new NotFoundException(nameof(Domain.Entities.ImpactAvailability), request.Id));

        var impactDetailVm = _mapper.Map<ImpactAvailabilityDetailVm>(impactDetail);

        return impactDetail == null
            ? throw new NotFoundException(nameof(Domain.Entities.ImpactAvailability), request.Id)
            : impactDetailVm;
    }
}