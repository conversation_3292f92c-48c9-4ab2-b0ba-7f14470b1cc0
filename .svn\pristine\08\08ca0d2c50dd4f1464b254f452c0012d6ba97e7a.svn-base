using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.RoboCopy.Events.Update;

public class RoboCopyUpdatedEventHandler : INotificationHandler<RoboCopyUpdatedEvent>
{
    private readonly ILogger<RoboCopyUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public RoboCopyUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<RoboCopyUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(RoboCopyUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} RoboCopyOptions",
            Entity = "RoboCopyOptions",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"RoboCopy '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"RoboCopy '{updatedEvent.Name}' updated successfully.");
    }
}