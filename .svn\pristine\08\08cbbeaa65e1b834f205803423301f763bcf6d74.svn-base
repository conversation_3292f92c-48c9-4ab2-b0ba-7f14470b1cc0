﻿namespace ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;

public class CreateBusinessServiceAvailabilityCommand : IRequest<CreateBusinessServiceAvailabilityResponse>
{
    public int TotalBusinessService { get; set; }
    public int AvailabilityUp { get; set; }
    public int AvailabilityDown { get; set; }
    public int TotalBusinessFunction { get; set; }
    public int BusinessFunctionUp { get; set; }
    public int BusinessFunctionDown { get; set; }
    public int HealthUp { get; set; }
    public int HealthDown { get; set; }
    public int DRReadynessUp { get; set; }
    public int DRReadynessDown { get; set; }
    public int TotalAlert { get; set; }
    public int AlertUp { get; set; }
    public int AlertDown { get; set; }
    public int TotalIncident { get; set; }
    public int IncidentUp { get; set; }
    public int IncidentDown { get; set; }

    public override string ToString()
    {
        return $"TotalBusiness Service: {TotalBusinessService};";
    }
}