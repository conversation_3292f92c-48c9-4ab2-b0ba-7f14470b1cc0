﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetInfraObjectConfigurationReportQueryHandlerTests
    {
        private readonly Mock<IInfraObjectRepository> _infraObjectRepositoryMock;
        private readonly Mock<IServerRepository> _serverRepositoryMock;
        private readonly Mock<INodeRepository> _nodeRepositoryMock;
        private readonly Mock<ILoggedInUserService> _loggedInUserServiceMock;
        private readonly Mock<IPublisher> _publisherMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetInfraObjectConfigurationReportQueryHandler _handler;

        public GetInfraObjectConfigurationReportQueryHandlerTests()
        {
            _infraObjectRepositoryMock = new Mock<IInfraObjectRepository>();
            _serverRepositoryMock = new Mock<IServerRepository>();
            _nodeRepositoryMock = new Mock<INodeRepository>();
            _loggedInUserServiceMock = new Mock<ILoggedInUserService>();
            _publisherMock = new Mock<IPublisher>();
            _mapperMock = new Mock<IMapper>();

            _handler = new GetInfraObjectConfigurationReportQueryHandler(
                _mapperMock.Object,
                _infraObjectRepositoryMock.Object,
                _serverRepositoryMock.Object,
                _nodeRepositoryMock.Object,
                _loggedInUserServiceMock.Object,
                _publisherMock.Object
            );
        }

        [Fact]
        public async Task Handle_ReturnsInfraReport_WhenInfraObjectExists()
        {
            var request = new GetInfraObjectConfigurationReportQuery { InfraObjectId = "test-id" };
            var infraObject = new Domain.Entities.InfraObject
            {
                //PRServerId = "pr-server-id",
                //DRServerId = "dr-server-id",
                //PRServerName = "PR Server",
                //DRServerName = "DR Server"
            };

            var prServer = new Domain.Entities.Server { Name = "PR Server", Properties = "{}" };
            var drServer = new Domain.Entities.Server { Name = "DR Server", Properties = "{}" };

            var mappedResult = new InfraObjectConfigurationReportVm
            {
               // PRServerName = new List<string> { "PR Server" },
               // DRServerName = new List<string> { "DR Server" },
                PRIPAddress = new List<string> { "********" },
                DRIPAddress = new List<string> { "********" }
            };

            _infraObjectRepositoryMock.Setup(x => x.GetByReferenceIdAsync(request.InfraObjectId))
                .ReturnsAsync(infraObject);

            _serverRepositoryMock.Setup(x => x.GetByReferenceIdAsync("pr-server-id"))
                .ReturnsAsync(prServer);

            _serverRepositoryMock.Setup(x => x.GetByReferenceIdAsync("dr-server-id"))
                .ReturnsAsync(drServer);

            _mapperMock.Setup(x => x.Map<InfraObjectConfigurationReportVm>(infraObject))
                .Returns(mappedResult);

            _loggedInUserServiceMock.Setup(x => x.LoginName).Returns("TestUser");

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal("TestUser", result.ReportGeneratedBy);
            Assert.NotNull(result.InfraObjectConfigurationReportVm);
           // Assert.Equal("PR Server", result.InfraObjectConfigurationReportVm.PRServerName[0]);
        }

        [Fact]
        public async Task Handle_ThrowsInvalidException_WhenInfraObjectDoesNotExist()
        {
            var request = new GetInfraObjectConfigurationReportQuery { InfraObjectId = "invalid-id" };

            _infraObjectRepositoryMock.Setup(x => x.GetByReferenceIdAsync(request.InfraObjectId))
                .ReturnsAsync((Domain.Entities.InfraObject)null);

            await Assert.ThrowsAsync<InvalidException>(() => _handler.Handle(request, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_PublishesReportViewedEvent()
        {
            var request = new GetInfraObjectConfigurationReportQuery { InfraObjectId = "test-id" };
            var infraObject = new Domain.Entities.InfraObject
            {
                //PRServerId = "pr-server-id",
                //DRServerId = "dr-server-id"
            };

            _infraObjectRepositoryMock.Setup(x => x.GetByReferenceIdAsync(request.InfraObjectId))
                .ReturnsAsync(infraObject);

            _serverRepositoryMock.Setup(x => x.GetByReferenceIdAsync("pr-server-id"))
                .ReturnsAsync(new Domain.Entities.Server { Name = "PR Server" });

            _serverRepositoryMock.Setup(x => x.GetByReferenceIdAsync("dr-server-id"))
                .ReturnsAsync(new Domain.Entities.Server { Name = "DR Server" });

            _mapperMock.Setup(x => x.Map<InfraObjectConfigurationReportVm>(infraObject))
                .Returns(new InfraObjectConfigurationReportVm());

            await _handler.Handle(request, CancellationToken.None);

            _publisherMock.Verify(x => x.Publish(It.IsAny<ReportViewedEvent>(), CancellationToken.None), Times.Once);
        }
    }
}
