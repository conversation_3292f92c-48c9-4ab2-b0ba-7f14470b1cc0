﻿using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Commands;

public class DeleteAlertReceiverTests : IClassFixture<AlertReceiverFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;
    private readonly Mock<IAlertReceiverRepository> _mockAlertReceiverRepository;
    private readonly Mock<IPublisher> _mockPublisher = new();
    private readonly DeleteAlertReceiverCommandHandler _handler;

    public DeleteAlertReceiverTests(AlertReceiverFixture alertReceiverFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;

        _alertReceiverFixture.AlertReceivers[0].ReferenceId = Guid.NewGuid().ToString();

        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.DeleteAlertReceiverRepository(_alertReceiverFixture.AlertReceivers);

        _handler = new DeleteAlertReceiverCommandHandler(_mockAlertReceiverRepository.Object,_mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_AlertReceiverDeleted()
    {
        var validGuid = Guid.NewGuid();

        _alertReceiverFixture.AlertReceivers[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteAlertReceiverCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteAlertReceiverResponse_When_AlertReceiverDeleted()
    {
        var validGuid = Guid.NewGuid();

        _alertReceiverFixture.AlertReceivers[0].ReferenceId = validGuid.ToString();
 
        var result = await _handler.Handle(new DeleteAlertReceiverCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType<DeleteAlertReceiverResponse>();

        result.IsActive.ShouldBeFalse();
        
        result.Message.ShouldContain("Notification Manager");
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_AlertReceiverDeleted()
    {
        var validGuid = Guid.NewGuid();

        _alertReceiverFixture.AlertReceivers[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteAlertReceiverCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var accessManager = await _mockAlertReceiverRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        accessManager.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidAlertReceiverId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteAlertReceiverCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = _alertReceiverFixture.AlertReceivers[0].ReferenceId;

        var alertReceiver = _alertReceiverFixture.AlertReceivers[0];

        _alertReceiverFixture.AlertReceivers[0].ReferenceId = Guid.NewGuid().ToString();

        _mockAlertReceiverRepository.Setup(x => x.GetByReferenceIdAsync(validGuid)).ReturnsAsync(alertReceiver); 

        await _handler.Handle(new DeleteAlertReceiverCommand { Id = validGuid }, CancellationToken.None);

        _mockAlertReceiverRepository.Verify(x => x.GetByReferenceIdAsync(validGuid), Times.Once);

        _mockAlertReceiverRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.AlertReceiver>()), Times.Once);
    }
}