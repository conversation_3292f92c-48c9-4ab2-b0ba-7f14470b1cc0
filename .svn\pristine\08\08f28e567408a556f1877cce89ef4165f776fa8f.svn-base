﻿using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowCategory.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowCategory.Queries.GetWorkflowCategoryViewList;
using ContinuityPatrol.Domain.ViewModels.WorkflowCategoryModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class WorkflowCategoryService : IWorkflowCategoryService
{
    private readonly IBaseClient _client;

    public WorkflowCategoryService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<List<WorkflowCategoryNameVm>> GetWorkflowCategoryNames()
    {
        var request = new RestRequest("api/v6/workflowcategories/names");

        return await  _client. GetFromCache<List<WorkflowCategoryNameVm>>(request, "GetWorkflowCategoryNames");
    }

    public async Task<List<WorkflowCategoryListVm>> GetWorkflowCategoryList()
    {
        var request = new RestRequest("api/v6/workflowcategories");

        return await  _client. Get<List<WorkflowCategoryListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateWorkflowCategoryCommand createWorkflowCategoryCommand)
    {
        var request = new RestRequest("api/v6/workflowcategories", Method.Post);

        request.AddJsonBody(createWorkflowCategoryCommand);

        return await  _client. Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateWorkflowCategoryCommand updateWorkflowCategoryCommand)
    {
        var request = new RestRequest("api/v6/workflowcategories", Method.Put);

        request.AddJsonBody(updateWorkflowCategoryCommand);

        return await  _client. Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string workflowCategoryId)
    {
        var request = new RestRequest($"api/v6/workflowcategories/{workflowCategoryId}", Method.Delete);

        return await  _client. Delete<BaseResponse>(request);
    }

    public async Task<WorkflowCategoryDetailVm> GetByReferenceId(string workflowCategoryId)
    {
        var request = new RestRequest($"api/v6/workflowcategories/{workflowCategoryId}");

        return await  _client. Get<WorkflowCategoryDetailVm>(request);
    }

    public async Task<bool> IsWorkflowCategoryNameExist(string name, string? id)
    {
        var request = new RestRequest($"api/v6/workflowcategories/name-exist?name={name}&id={id}");

        return await  _client. Get<bool>(request);
    }

    public async Task<PaginatedResult<WorkflowCategoryListVm>> GetPaginatedWorkflowCategories(GetWorkflowCategoryPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/workflowcategories/paginated-list");

        return await  _client. Get<PaginatedResult<WorkflowCategoryListVm>>(request);
    }

    public async Task<List<WorkflowCategoryViewListVm>> GetWorkflowCategoryViewList()
    {
        var request = new RestRequest("api/v6/workflowcategories/workflowcategoryviewlist");

        return await  _client. Get<List<WorkflowCategoryViewListVm>>(request);
    }
}