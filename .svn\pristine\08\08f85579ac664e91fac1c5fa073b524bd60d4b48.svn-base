﻿namespace ContinuityPatrol.Application.Features.IncidentDaily.Commands.Create;

public class CreateIncidentDailyCommand : IRequest<CreateIncidentDailyResponse>
{
    public string ParentBusinessServiceId { get; set; }
    public string ParentBusinessServiceName { get; set; }
    public string InfraObjectId { get; set; }
    public string InfraObjectName { get; set; }
    public int Open { get; set; }
    public int Close { get; set; }
    public int Total { get; set; }
    public DateTime IncidentDate { get; set; }
}