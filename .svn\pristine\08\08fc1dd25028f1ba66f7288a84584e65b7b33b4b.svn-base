﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;

public class GetRunBookReportQueryHandler : IRequestHandler<GetRunBookReportQuery, GetRunBookReportVm>
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly ILogger<GetRunBookReportQueryHandler> _logger;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowActionRepository _workflowActionRepository;
    private readonly IWorkflowCategoryRepository _workflowCategoryRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public GetRunBookReportQueryHandler(IMapper mapper, IWorkflowRepository workflowRepository,
        IDatabaseRepository databaseRepository, IServerRepository serverRepository,
        IWorkflowActionRepository workflowActionRepository, IReplicationRepository replicationRepository,
        ILoggedInUserService loggedInUserService, IWorkflowCategoryRepository workflowCategoryRepository,
        IPublisher publisher, ILogger<GetRunBookReportQueryHandler> logger)
    {
        _mapper = mapper;
        _workflowRepository = workflowRepository;
        _databaseRepository = databaseRepository;
        _serverRepository = serverRepository;
        _workflowActionRepository = workflowActionRepository;
        _replicationRepository = replicationRepository;
        _loggedInUserService = loggedInUserService;
        _workflowCategoryRepository = workflowCategoryRepository;
        _publisher = publisher;
        _logger = logger;
    }

    public async Task<GetRunBookReportVm> Handle(GetRunBookReportQuery request, CancellationToken cancellationToken)
    {
        var ActionName = string.Empty;
        try
        {
            _logger.LogDebug("Enter GetRunbookReportByWorkflowId Method ");
            var workflowDto = await _workflowRepository.GetByReferenceIdAsync(request.WorkflowId);
            _logger.LogDebug("Get workflow List Value Successfully");
            Guard.Against.NullOrDeactive(workflowDto, nameof(Domain.Entities.Workflow),
                new NotFoundException(nameof(Domain.Entities.Workflow), request.WorkflowId));

            // var cc = "{\"nodes\":[{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\",\"@@DRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@DRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"40b6a03b-cf6b-4b6d-8b8b-3997c1b318b8\",\"PR Server\":\"MYSQL_82\"},{\"id\":\"823c0a51-c2c5-4000-9749-297f081888dc\",\"PR Database\":\"MYSQL_82_DB\"},{\"id\":\"f0e09b4a-4db7-41dd-b169-8a65e3d36aae\",\"DR Server\":\"MYSQL_81\"},{\"id\":\"60bf5dd3-5afe-480d-990f-72ac8ce6d2d2\",\"DR Database\":\"MYSQL_81_DB\"}],\"actionType\":\" VerifySQLLogpositionMasterandSlaveServer \"},\"formInput\":[{\"label\":\"PR Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"PR Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},{\"label\":\"DR Server\",\"type\":\"server\",\"name\":\"@@DRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"DR Database\",\"type\":\"database\",\"name\":\"@@DRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"VerifySQLLogPositionInMasterAndSlaveDRPR1\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_987596385\",\"parentActionId\":\"3e1cf48b-f181-45e9-ba28-3adecf1c824c\",\"icon\":\"cp-data custom-cursor-on-hover\",\"color\":\"rgb(219, 40, 40)\",\"IsConditional\":false},\"stepId\":\"5286-1713776604301-11026\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_82\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_82_DB\"}],\"actionType\":\" SetGlobalREADONLYON \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"}],\"actionName\":\"SET GLOBAL read_only_ON_DR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"70879db7-cf53-4911-8835-d069583bf660\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_1058812632\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false},\"stepId\":\"39318-1713776700483-2761\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\",\"@@CheckOutput\":\"ON\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_82\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_82_DB\"},{\"id\":\"32c328d4-9394-46b7-b11e-efe7f18c0065\",\"Check Output\":\"ON\"}],\"actionType\":\" VerifyREADONLYOnMaster \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"}],\"actionName\":\"VerifyREADWRITEOnMasterDR1\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"10673e60-94f0-4392-8146-f5bc385f8ef2\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_439775475\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false},\"stepId\":\"31063-1713776902300-77340\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"29bcf2af-175c-4529-a5da-1365546f63dc\",\"Server\":\"MYSQL_82\"},{\"id\":\"c6c2ad43-2b8a-4f1c-89e2-7f7faf0a5f34\",\"Database\":\"MYSQL_82_DB\"}],\"actionType\":\" FlushTables \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"}],\"actionName\":\"FlushTablesInDR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"4e743548-adb2-40d6-82c9-a0296c3a3334\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_49097401\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false},\"stepId\":\"15426-1713776935866-14314\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"29bcf2af-175c-4529-a5da-1365546f63dc\",\"Server\":\"MYSQL_82\"},{\"id\":\"c6c2ad43-2b8a-4f1c-89e2-7f7faf0a5f34\",\"Database\":\"MYSQL_82_DB\"}],\"actionType\":\" FlushLogs \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"}],\"actionName\":\"FlushLogsInDR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"0f7c817f-a285-4907-b221-f591dab24b46\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_183413553\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false},\"stepId\":\"86450-1713776957746-71408\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\",\"@@DRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@DRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"40b6a03b-cf6b-4b6d-8b8b-3997c1b318b8\",\"PR Server\":\"MYSQL_82\"},{\"id\":\"823c0a51-c2c5-4000-9749-297f081888dc\",\"PR Database\":\"MYSQL_82_DB\"},{\"id\":\"f0e09b4a-4db7-41dd-b169-8a65e3d36aae\",\"DR Server\":\"MYSQL_81\"},{\"id\":\"60bf5dd3-5afe-480d-990f-72ac8ce6d2d2\",\"DR Database\":\"MYSQL_81_DB\"}],\"actionType\":\" VerifySQLLogpositionMasterandSlaveServer \"},\"formInput\":[{\"label\":\"PR Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"PR Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},{\"label\":\"DR Server\",\"type\":\"server\",\"name\":\"@@DRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"DR Database\",\"type\":\"database\",\"name\":\"@@DRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"VerifySQLLogPositionInMasterAndSlaveDRPR_1\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_8025051\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false},\"stepId\":\"43105-1713777047484-91652\"},{\"actionInfo\":{\"properties\":{\"@@DRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@DRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_81\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_81_DB\"}],\"actionType\":\" StopReplicationInSlaveServer \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@DRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@DRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"StopReplicationInSlaveServerPR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"84c34a98-a8de-4b26-ae27-b37a449873bc\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_127360365\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"82832-1713777087098-21375\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@PRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_81\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_81_DB\"}],\"actionType\":\" SetGlobalREADONLYOFF \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"SET GLOBAL read_only_OFF_PR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"94e9cb19-020e-4662-bb99-3a33db73a34a\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_1038915445\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"84694-1713777133186-95241\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@PRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\",\"@@CheckOutput\":\"OFF\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_81\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_81_DB\"},{\"id\":\"32c328d4-9394-46b7-b11e-efe7f18c0065\",\"Check Output\":\"OFF\"}],\"actionType\":\" VerifyREADONLYOnMaster \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"VerifyREADWRITEOnMasterPR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"10673e60-94f0-4392-8146-f5bc385f8ef2\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_351940037\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"51580-1713777206690-88450\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\",\"@@DRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@DRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"40b6a03b-cf6b-4b6d-8b8b-3997c1b318b8\",\"PR Server\":\"MYSQL_82\"},{\"id\":\"823c0a51-c2c5-4000-9749-297f081888dc\",\"PR Database\":\"MYSQL_82_DB\"},{\"id\":\"f0e09b4a-4db7-41dd-b169-8a65e3d36aae\",\"DR Server\":\"MYSQL_81\"},{\"id\":\"60bf5dd3-5afe-480d-990f-72ac8ce6d2d2\",\"DR Database\":\"MYSQL_81_DB\"}],\"actionType\":\" VerifySQLLogpositionMasterandSlaveServer \"},\"formInput\":[{\"label\":\"PR Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"PR Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},{\"label\":\"DR Server\",\"type\":\"server\",\"name\":\"@@DRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"DR Database\",\"type\":\"database\",\"name\":\"@@DRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"VerifySQLLogPositionInMasterAndSlaveDRPR12\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_53903033\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"72450-1713777267354-99102\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\",\"@@DRServerName\":\"50752bff-07f0-45ee-a0c9-49067322ab32\",\"@@DRDBName\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\",\"@@MasterUser\":\"slave\",\"@@MasterPassword\":\"Root@123\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"40b6a03b-cf6b-4b6d-8b8b-3997c1b318b8\",\"PR Server\":\"MYSQL_82\"},{\"id\":\"823c0a51-c2c5-4000-9749-297f081888dc\",\"PR Database\":\"MYSQL_82_DB\"},{\"id\":\"f0e09b4a-4db7-41dd-b169-8a65e3d36aae\",\"DR Server\":\"MYSQL_81\"},{\"id\":\"60bf5dd3-5afe-480d-990f-72ac8ce6d2d2\",\"DR Database\":\"MYSQL_81_DB\"},{\"id\":\"46965783-be5c-4882-a738-f5eb65271448\",\"Master_User\":\"slave\"},{\"id\":\"cc7d2df9-749f-4e9d-b0f4-edfe6d8fa658\",\"Master Password\":\"Root@123\"}],\"actionType\":\" ChangeMasterTOMasterHost \"},\"formInput\":[{\"label\":\"PR Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"PR Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},{\"label\":\"DR Server\",\"type\":\"server\",\"name\":\"@@DRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"PRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"50752bff-07f0-45ee-a0c9-49067322ab32\"},{\"label\":\"DR Database\",\"type\":\"database\",\"name\":\"@@DRDBName\",\"DatabaseType\":\"\",\"optionType\":\"PRDB\",\"optionRoleType\":\"\",\"id\":\"dfbccf34-7c5d-4bb4-8a2a-4fbf1f1712fe\"}],\"actionName\":\"ChangeMASTERTOMASTERHostDR_PR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"e6035b9b-6558-4495-bd20-e9f4aeda8082\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_593357771\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"46318-1713777435178-28015\"},{\"actionInfo\":{\"properties\":{\"@@PRServerName\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\",\"@@PRDBName\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"},\"propertyData\":{\"propertiesInfo\":[{\"id\":\"a60d4a50-dd7f-4ca8-a482-497a0bdb3a0a\",\"Server\":\"MYSQL_82\"},{\"id\":\"c33a2a99-e4ab-41dc-89c6-09fb0014c588\",\"Database\":\"MYSQL_82_DB\"}],\"actionType\":\" StartSlaveInMaster \"},\"formInput\":[{\"label\":\"Server\",\"type\":\"server\",\"name\":\"@@PRServerName\",\"ServerRole\":\"\",\"ServerType\":\"\",\"optionType\":\"DRDBServer\",\"optionRoleType\":\"Database\",\"id\":\"d272b010-d11c-43a7-83e6-a96d08f5003d\"},{\"label\":\"Database\",\"type\":\"database\",\"name\":\"@@PRDBName\",\"DatabaseType\":\"\",\"optionType\":\"DRDB\",\"optionRoleType\":\"\",\"id\":\"e62522dd-1f43-43be-861b-f6fb12331e03\"}],\"actionName\":\"StartSlaveInMasterDR\",\"description\":\"\",\"rto\":\"2\",\"actionType\":\"f614a10b-dc3c-47ce-b8dc-aaa7b1b02079\",\"email\":false,\"sms\":false,\"failureCount\":\"\",\"nodeId\":\"0b2fe336-5aff-4c33-92e0-caa67b73228c\",\"uniqueId\":\"node_147615314\",\"parentActionId\":\"62bd5298-d90c-414b-9f88-12a8ffcb4185\",\"IsConditional\":false,\"icon\":\"\",\"color\":\"\"},\"stepId\":\"53983-1713777472378-32553\"}]}";

            dynamic deserializeObject = JsonConvert.DeserializeObject(workflowDto.Properties);

            var runBookReportList = new List<GetRunBookReportListVm>();

            foreach (var wf in deserializeObject!.SelectToken("nodes"))
            {
                var runBookReportDto = new GetRunBookReportListVm();

                var properties = new Dictionary<string, string>();

                var actionInfo = wf?.SelectToken("actionInfo");

                ActionName = actionInfo?.SelectToken("actionName")?.ToString() ?? "NA";


                var children = wf?.SelectToken("children");

                var groupAction = wf?.SelectToken("groupActions");

                if (groupAction is not null)
                {
                    _logger.LogDebug("Enter Group Action Condition");

                    foreach (var group in groupAction)
                    {
                        var groupPros = new Dictionary<string, string>();

                        var groupRunBookReport = new GetRunBookReportListVm();

                        var actionInfos = group?.SelectToken("actionInfo");

                        var groupchildren = group?.SelectToken("children");

                        var groupProperties = actionInfos.SelectToken("properties");
                        if (groupchildren is not null)
                        {
                            _logger.LogDebug("Enter Groupchildren Condition");
                            foreach (var grpchild in groupchildren)
                            {
                                var groupchildPros = new Dictionary<string, string>();
                                var groupchildRunBookReport = new GetRunBookReportListVm();

                                var grpactionInfos = grpchild?.SelectToken("actionInfo");

                                var grpchildProperties = grpactionInfos.SelectToken("properties");

                                if (grpchildProperties != null)
                                {
                                    var grpProps = GetPropertyNames(grpchildProperties);
                                    foreach (var grpprop in grpProps)
                                    {
                                        var id = grpchildProperties?.SelectToken($"['{grpprop}']")?.ToString();
                                        var name = await GetNameByReferenceIdAsync(id, grpprop);

                                        if (name != null)
                                        {
                                            if (grpprop == "")
                                            {
                                            }

                                            string key = grpprop.Replace("@@", "");

                                            if (key.Contains("DependentWorkflowName"))
                                            {
                                                var keyName = key.Replace("Name", "");
                                                groupchildPros.Add(key, name ?? "NA");
                                            }

                                            if (key.ToLower().Contains("replication"))
                                            {
                                                var keyName = key.Replace("Name", "");
                                                groupchildPros.Add(key, name ?? "NA");
                                            }

                                            if (key.Contains("Server") || key.Contains("DB"))
                                            {
                                                var keyName = key.Replace("Name", "");
                                                string[] parts = name?.Split(',');
                                                if (parts?.Length > 2)
                                                {
                                                    groupchildPros.Add(key, parts[0].Trim() ?? "NA");

                                                    if (parts[3].Trim().ToLower().Equals("true"))
                                                        groupchildPros.Add($"{keyName}_HostName",
                                                            parts[2].Trim() ?? "NA");
                                                    else
                                                        groupchildPros.Add($"{keyName}_IPAddress",
                                                            parts[1].Trim() ?? "NA");

                                                    groupchildPros.Add($"{keyName}_ConnectViaHostName",
                                                        parts[3].Trim() ?? "NA");


                                                    //groupchildPros.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");
                                                    //groupchildPros.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                                    //groupchildPros.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");
                                                }
                                                else
                                                {
                                                    groupchildPros.Add(key, parts[0].Trim() ?? "NA");
                                                    groupchildPros.Add($"{keyName}_DatabaseSID",
                                                        parts[1].Trim() ?? "NA");
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var dynamicalValue = grpchildProperties?.SelectToken($"['{grpprop}']")?.ToString();

                                            string key = grpprop.Replace("@@", "");

                                            groupchildPros.Add(key, dynamicalValue ?? "NA");
                                        }
                                    }

                                    var jsonString = JsonConvert.SerializeObject(groupchildPros);

                                    groupchildRunBookReport.Properties = jsonString;

                                    groupchildRunBookReport.WorkflowActionName =
                                        grpactionInfos?.SelectToken("actionName")?.ToString() ?? "NA";
                                    groupchildRunBookReport.IsParallel =
                                        grpactionInfos?.SelectToken("IsParallel")?.ToString() ?? "NA";

                                    groupchildRunBookReport.GroupName =
                                        wf?.SelectToken("groupName")?.ToString() ?? "NA";
                                    var actionType = grpactionInfos?.SelectToken("actionType")?.ToString() ?? "NA";

                                    var workflowAction =
                                        await _workflowActionRepository.GetByReferenceIdAsync(actionType);

                                    groupchildRunBookReport.WorkflowAction = workflowAction?.ActionName ?? "NA";

                                    groupchildRunBookReport.RTO =
                                        grpactionInfos?.SelectToken("rto")?.ToString() ?? "NA";

                                    var parentActionId = grpactionInfos?.SelectToken("parentActionId")?.ToString() ??
                                                         "NA";

                                    var workflowCategory = parentActionId != null
                                        ? await _workflowCategoryRepository.GetByReferenceIdAsync(parentActionId)
                                        : null;

                                    groupchildRunBookReport.ActionType = workflowCategory?.Name ?? "NA";

                                    groupchildRunBookReport.SrNo = GetJsonProperties.GenerateId();

                                    runBookReportList.AddRange(groupchildRunBookReport);
                                }
                            }
                        }

                        if (groupProperties is not null)
                        {
                            var props = GetPropertyNames(groupProperties);

                            foreach (var prop in props)
                            {
                                var id = groupProperties?.SelectToken($"['{prop}']")?.ToString();

                                var name = await GetNameByReferenceIdAsync(id, prop);

                                if (name != null)
                                {
                                    string key = prop.Replace("@@", "");

                                    if (key.Contains("DependentWorkflowName"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        groupPros.Add(key, name ?? "NA");
                                    }

                                    if (key.ToLower().Contains("replication"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        groupPros.Add(key, name ?? "NA");
                                    }

                                    if (key.Contains("Server") || key.Contains("DB"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        string[] parts = name?.Split(',');
                                        if (parts?.Length > 2)
                                        {
                                            groupPros.Add(key, parts[0].Trim() ?? "NA");

                                            if (parts[3].Trim().ToLower().Equals("true"))
                                                groupPros.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                            else
                                                groupPros.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");

                                            groupPros.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");
                                        }
                                        else
                                        {
                                            groupPros.Add(key, parts[0].Trim() ?? "NA");
                                            groupPros.Add($"{keyName}_DatabaseSID", parts[1].Trim() ?? "NA");
                                        }
                                    }
                                }
                                else
                                {
                                    string key1 = prop.Replace("@@", "");

                                    if (!key1.Equals("DependentWorkflowAction"))
                                    {
                                        if (key1.Equals("ScriptBlock"))
                                        {
                                            var dynamicalValue = groupProperties?.SelectToken($"['{prop}']")?.ToString()
                                                .Replace("\\\\", "\\");

                                            string modifiedString = dynamicalValue.Replace("_encryptedcpl", "");

                                            var scriptBlock = SecurityHelper.Decrypt(modifiedString);

                                            string key = prop.Replace("@@", "");

                                            groupPros.Add(key, scriptBlock ?? "NA");
                                        }
                                        else
                                        {
                                            if (!key1.Equals("MasterPassword"))
                                            {
                                                var dynamicalValue = groupProperties?.SelectToken($"['{prop}']")?.ToString()
                                                    .Replace("\\\\", "\\");

                                                string key = prop.Replace("@@", "");

                                                groupPros.Add(key, dynamicalValue ?? "NA");
                                            }
                                        }
                                    }
                                }
                            }

                            var jsonString = JsonConvert.SerializeObject(groupPros);

                            groupRunBookReport.Properties = jsonString;

                            groupRunBookReport.WorkflowActionName =
                                actionInfos?.SelectToken("actionName")?.ToString() ?? "NA";

                            var actionType = actionInfos?.SelectToken("actionType")?.ToString() ?? "NA";

                            groupRunBookReport.IsParallel = actionInfos?.SelectToken("IsParallel")?.ToString() ?? "NA";

                            groupRunBookReport.GroupName = wf?.SelectToken("groupName")?.ToString() ?? "NA";

                            var workflowAction = await _workflowActionRepository.GetByReferenceIdAsync(actionType);

                            groupRunBookReport.WorkflowAction = workflowAction?.ActionName ?? "NA";

                            groupRunBookReport.RTO = actionInfos?.SelectToken("rto")?.ToString() ?? "NA";

                            var parentActionId = actionInfos?.SelectToken("parentActionId")?.ToString() ?? "NA";

                            var workflowCategory = parentActionId != null
                                ? await _workflowCategoryRepository.GetByReferenceIdAsync(parentActionId)
                                : null;

                            groupRunBookReport.ActionType = workflowCategory?.Name ?? "NA";

                            groupRunBookReport.SrNo = GetJsonProperties.GenerateId();

                            runBookReportList.AddRange(groupRunBookReport);
                        }
                    }
                }

                if (children != null)
                {
                    _logger.LogDebug("Enter child Action Condition");
                    foreach (var child in children)
                    {
                        var childPros = new Dictionary<string, string>();

                        var childRunBookReport = new GetRunBookReportListVm();

                        var actionInfos = child?.SelectToken("actionInfo");

                        var childProperties = actionInfos.SelectToken("properties");

                        if (childProperties != null)
                        {
                            var props = GetPropertyNames(childProperties);

                            foreach (var prop in props)
                            {
                                var id = childProperties?.SelectToken($"['{prop}']")?.ToString();

                                var name = await GetNameByReferenceIdAsync(id, prop);

                                if (name != null)
                                {
                                    if (prop == "")
                                    {
                                    }

                                    string key = prop.Replace("@@", "");

                                    if (key.Contains("DependentWorkflowName"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        childPros.Add(key, name ?? "NA");
                                    }

                                    if (key.ToLower().Contains("replication"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        childPros.Add(key, name ?? "NA");
                                    }

                                    if (key.Contains("Server") || key.Contains("DB"))
                                    {
                                        var keyName = key.Replace("Name", "");
                                        string[] parts = name?.Split(',');
                                        if (parts?.Length > 2)
                                        {
                                            childPros.Add(key, parts[0].Trim() ?? "NA");

                                            if (parts[3].Trim().ToLower().Equals("true"))
                                                childPros.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                            else
                                                childPros.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");

                                            childPros.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");


                                            //childPros.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");
                                            //childPros.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                            //childPros.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");
                                        }
                                        else
                                        {
                                            childPros.Add(key, parts[0].Trim() ?? "NA");
                                            childPros.Add($"{keyName}_DatabaseSID", parts[1].Trim() ?? "NA");
                                        }
                                    }
                                    //string key = prop.Replace("@@", "");

                                    //childPros.Add(key, name ?? "NA");
                                }
                                else
                                {
                                    var dynamicalValue = childProperties?.SelectToken($"['{prop}']")?.ToString();

                                    string key = prop.Replace("@@", "");

                                    childPros.Add(key, dynamicalValue ?? "NA");
                                }
                            }

                            var jsonString = JsonConvert.SerializeObject(childPros);

                            childRunBookReport.Properties = jsonString;

                            childRunBookReport.WorkflowActionName =
                                actionInfos?.SelectToken("actionName")?.ToString() ?? "NA";

                            var actionType = actionInfos?.SelectToken("actionType")?.ToString() ?? "NA";

                            childRunBookReport.IsParallel = actionInfos?.SelectToken("IsParallel")?.ToString() ?? "NA";

                            childRunBookReport.GroupName = wf?.SelectToken("groupName")?.ToString() ?? "NA";

                            var workflowAction = await _workflowActionRepository.GetByReferenceIdAsync(actionType);

                            childRunBookReport.WorkflowAction = workflowAction?.ActionName ?? "NA";

                            childRunBookReport.RTO = actionInfos?.SelectToken("rto")?.ToString() ?? "NA";

                            var parentActionId = actionInfos?.SelectToken("parentActionId")?.ToString() ?? "NA";

                            var workflowCategory = parentActionId != null
                                ? await _workflowCategoryRepository.GetByReferenceIdAsync(parentActionId)
                                : null;

                            childRunBookReport.ActionType = workflowCategory?.Name ?? "NA";

                            childRunBookReport.SrNo = GetJsonProperties.GenerateId();

                            runBookReportList.AddRange(childRunBookReport);
                        }
                    }
                }

                if (actionInfo != null)
                {
                    _logger.LogDebug("Enter actionInfo Condition");
                    var actionInfoProperties = actionInfo?.SelectToken("properties");

                    if (actionInfoProperties != null)
                    {
                        var props = GetPropertyNames(actionInfoProperties);
                        string workflowid = string.Empty;

                        foreach (var prop in props)
                        {
                            var id = actionInfoProperties?.SelectToken($"['{prop}']")?.ToString();

                            if (prop.Contains("workflow_name") || prop.Contains("WorkflowName"))
                            {
                                workflowid = string.Empty;
                                workflowid = id;
                            }
                            var name = await GetNameByReferenceIdAsync(id, prop);

                            if (name != null)
                            {
                                string key = prop.Replace("@@", "");

                                if (key.Contains("DependentWorkflowName") || key.Contains("workflow_name"))
                                {
                                    var keyName = key.Replace("Name", "");
                                    properties.Add(key, name ?? "NA");
                                }

                                if (key.ToLower().Contains("replication"))
                                {
                                    var keyName = key.Replace("Name", "");
                                    properties.Add(key, name ?? "NA");
                                }

                                if (key.Contains("Server") || key.Contains("DB"))
                                {
                                    var keyName = key.Replace("Name", "");
                                    string[] parts = name?.Split(',');
                                    if (parts?.Length > 2)
                                    {
                                        properties.Add(key, parts[0].Trim() ?? "NA");


                                        if (parts[3].Trim().ToLower().Equals("true"))
                                            properties.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                        else
                                            properties.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");

                                        properties.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");


                                        //properties.Add($"{keyName}_IPAddress", parts[1].Trim() ?? "NA");
                                        //properties.Add($"{keyName}_HostName", parts[2].Trim() ?? "NA");
                                        //properties.Add($"{keyName}_ConnectViaHostName", parts[3].Trim() ?? "NA");
                                    }
                                    else
                                    {
                                        properties.Add(key, parts[0].Trim() ?? "NA");
                                        properties.Add($"{keyName}_DatabaseSID", parts[1].Trim() ?? "NA");
                                    }
                                }
                                //string key = prop.Replace("@@", "");
                                //properties.Add(key, name ?? "NA");
                            }
                            else
                            {
                                string key1 = prop.Replace("@@", "");

                                if (!key1.Equals("DependentWorkflowAction"))
                                {
                                    if (key1.Equals("ScriptBlock"))
                                    {
                                        var dynamicalValue = actionInfoProperties?.SelectToken($"['{prop}']")?.ToString()
                                            .Replace("\\\\", "\\");

                                        string modifiedString = dynamicalValue.Replace("_encryptedcpl", "");

                                        var scriptBlock = SecurityHelper.Decrypt(modifiedString);

                                        string key = prop.Replace("@@", "");

                                        properties.Add(key, scriptBlock ?? "NA");
                                    }
                                    else
                                    {
                                        if (!key1.Equals("MasterPassword"))
                                        {
                                            var dynamicalValue = actionInfoProperties?.SelectToken($"['{prop}']")?.ToString()
                                                .Replace("\\\\", "\\");

                                            string key = prop.Replace("@@", "");

                                            properties.Add(key, dynamicalValue ?? "NA");
                                        }

                                        if (key1.Equals("MasterPassword"))
                                        {
                                            string key = prop.Replace("@@", "");

                                            properties.Add(key, "*******");
                                        }
                                    }
                                }
                                if (key1.Equals("DependentWorkflowAction"))
                                {
                                    List<string> idList = id.StartsWith("[") && id.EndsWith("]")
                                                          ? JsonConvert.DeserializeObject<List<string>>(id)
                                                          : new List<string> { id };
                                    if (!string.IsNullOrWhiteSpace(workflowid) && workflowid != "NA")
                                    {
                                        var entity = await _workflowRepository.GetByReferenceIdAsync(workflowid);
                                        workflowid = string.Empty;
                                        JObject data = JObject.Parse(entity.Properties.ToString());
                                        var Actionvalue = data["nodes"]
                                            .Where(node => idList.Contains(node["stepId"]?.ToString()))
                                            .Select(node => node["actionInfo"]?["actionName"]?.ToString())
                                            .Where(actionName => !string.IsNullOrEmpty(actionName))
                                            .ToList();
                                        properties.Add(key1, string.Join(", ", Actionvalue));
                                    }
                                }
                            }
                        }

                        var jsonString = JsonConvert.SerializeObject(properties);

                        runBookReportDto.Properties = jsonString;

                        runBookReportDto.WorkflowActionName = actionInfo?.SelectToken("actionName")?.ToString() ?? "NA";

                        var actionType = actionInfo?.SelectToken("actionType")?.ToString() ?? "NA";
                        runBookReportDto.IsParallel = actionInfo?.SelectToken("IsParallel")?.ToString() ?? "NA";

                        runBookReportDto.GroupName = wf?.SelectToken("groupName")?.ToString() ?? "NA";
                        var workflowAction = await _workflowActionRepository.GetByReferenceIdAsync(actionType);

                        runBookReportDto.WorkflowAction = workflowAction?.ActionName ?? "NA";

                        runBookReportDto.RTO = actionInfo?.SelectToken("rto")?.ToString() ?? "NA";

                        var parentActionId = actionInfo?.SelectToken("parentActionId")?.ToString() ?? "NA";

                        var workflowCategory = parentActionId != null
                            ? await _workflowCategoryRepository.GetByReferenceIdAsync(parentActionId)
                            : null;

                        runBookReportDto.ActionType = workflowCategory?.Name ?? "NA";

                        runBookReportDto.SrNo = GetJsonProperties.GenerateId();

                        runBookReportList.AddRange(runBookReportDto);
                    }
                }
            }

            GetJsonProperties.ClearAutoIncrement();

            await _publisher.Publish(
                new ReportViewedEvent { ReportName = "Run Book Report", ActivityType = ActivityType.View.ToString() },
                cancellationToken);

            return new GetRunBookReportVm
            {
                WorkflowId = workflowDto.ReferenceId,
                WorkflowName = workflowDto.Name,
                ReportGeneratedBy = _loggedInUserService.LoginName,
                Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
                GetRunBookReportListVms = runBookReportList
            };
        }
        catch (Exception ex)
        {
            _logger.LogError("Exception occurred in GetRunbookReportByWorkflowId method" + ex.Message);
            _logger.LogDebug($"Stack Trace: {ex.StackTrace}");
            _logger.LogError($"Error Occured in {ActionName} Action");
            throw;
        }
    }


    private async Task<string> GetNameByReferenceIdAsync(dynamic id, string propertyName)
    {
        if (string.IsNullOrEmpty(id) || !Guid.TryParse(id, out Guid result))
            return null;

        string resultString = null;

        if (propertyName.Contains("Server") || propertyName.Contains("DB") || propertyName.ToLower().Contains("replication"))
        {
            var entity = await _serverRepository.GetByReferenceIdAsync(id)
                         ?? await _databaseRepository.GetByReferenceIdAsync(id)
                         ?? await _replicationRepository.GetByReferenceIdAsync(id);
            if (entity != null)
            {
                if (propertyName.Contains("Server"))
                {
                    var data = entity.Properties;
                    var ipAddress = GetJsonProperties.GetIpAddressFromProperties(data);
                    var hostName = GetJsonProperties.GetHostNameFromProperties(data);
                    var connectionVia = GetJsonProperties.GetConnectionViaHostNameProperties(data);
                    resultString =
                        $"{entity.Name ?? "NA"},{ipAddress ?? "NA"},{hostName ?? "NA"},{connectionVia ?? "NA"} ";
                    return resultString;
                }

                if (propertyName.Contains("DB"))
                {
                    var data = entity.Properties;
                    var sid = GetJsonProperties.GetJsonDatabaseSidValue(data);
                    resultString = $"{entity.Name ?? "NA"},{sid ?? "NA"} ";
                    return resultString;
                }
            }

            resultString = entity?.Name ?? null;
        }

        if (propertyName.Contains("DependentWorkflowName") || propertyName.Contains("workflow_name"))
        {
            var entity = await _workflowRepository.GetByReferenceIdAsync(id);

            return entity?.Name ?? "NA";
        }

        return resultString ?? null;
    }

    private static List<string> GetPropertyNames(JObject json)
    {
        var propertyNames = new List<string>();
        foreach (var property in json.Properties()) propertyNames.Add(property.Name);
        return propertyNames;
    }
}