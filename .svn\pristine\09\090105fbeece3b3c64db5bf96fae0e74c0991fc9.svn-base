using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.IncidentManagementSummary.Events.Update;

public class IncidentManagementSummaryUpdatedEventHandler : INotificationHandler<IncidentManagementSummaryUpdatedEvent>
{
    private readonly ILogger<IncidentManagementSummaryUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public IncidentManagementSummaryUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<IncidentManagementSummaryUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(IncidentManagementSummaryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} IncidentManagementSummary",
            Entity = "IncidentManagementSummary",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"IncidentManagementSummary '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"IncidentManagementSummary '{updatedEvent.Name}' updated successfully.");
    }
}