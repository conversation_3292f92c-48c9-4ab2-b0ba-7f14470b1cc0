﻿@model ContinuityPatrol.Domain.ViewModels.ServerTypeModel.ServerTypeViewModel

@Html.AntiForgeryToken()
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel ">
    <form class="modal-content" id="createComponentForm" asp-controller="ServerType" asp-action="CreateOrUpdate" method="post" enctype="multipart/form-data">
        <div class="modal-header">
            <h6 class="page_title" title="Component Type Configuration"><i class="cp-server-role"></i><span>Component Type Configuration</span></h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" title="Close" aria-label="Close"></button>
        </div>
        <div class="modal-body mb-5">
            <div class="mb-3 ">
                <div class="form-group">
                    <label class="form-label">Type</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-form-mapping"></i></span>
                        <select asp-for="Name" class="form-select-modal" id="selectComponentType" data-live-search="true"
                                onchange="validateComponentType()" data-placeholder="Select Type">
                            <option value="">Select</option>
                            <option value="Server">Server</option>
                            <option value="Database">Database</option>
                            <option value="Replication">Replication</option>
                            <option value="Single SignOn">Single Sign-On</option>
                            <option value="Node">Node</option>
                        </select>
                    </div>
                    <span asp-validation-for="Name" id="componentTypeError"></span>
                </div>
            </div>

            <div class="mb-3 ">
                <div class="form-group">
                    <label class="form-label">Name</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input oninput="validateComponentName()" type="text" id="componentTypeName" class="form-control" maxlength="30"
                               placeholder="Enter Name" autocomplete="off" />
                        <span class="input-group-text" title="Select Component Type Icon" role="button" data-bs-toggle="collapse" href="#collapseExample"
                              aria-expanded="false" aria-controls="collapseExample" id="iconContainer">
                            <i class="cp-images" id="componentTypelogo" style="color:black"></i>
                            <input type="hidden" id="componentTypeIcon"  />
                        </span>

                    </div>
                    <span id="componentNameError"></span>
                </div>
                <div class="collapse mb-2" id="collapseExample">
                    <div class="form-label">Form Type Icon</div>
                    <div class="Category_Icon">
                        <table class="table table-bordered" id="iconchange">
                            <tbody>
                                <tr>
                                    <td><i title="Cloud" class="cp-cloud custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Amazon" class="cp-amazon custom-cursor-on-hover"></i></td>
                                    <td><i title="Softlayer" class="cp-java-soft-layers custom-cursor-on-hover"></i></td>
                                    <td><i title="Database" class="cp-data custom-cursor-on-hover"></i></td>
                                    <td><i title="MSSQL" class="cp-mssql custom-cursor-on-hover"></i></td>
                                    <td><i title="MYSQL" class="cp-mysql custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Oracle" class="cp-oracle custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Postgres" class="cp-postgres custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="IBM" class="cp-IBM custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Files" class="cp-folder-file custom-cursor-on-hover"></i></td>
                                    <td><i title="Hypervisor" class="cp-workflow-execution custom-cursor-on-hover"></i></td>
                                    <td><i title="Windows" class="cp-windows custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Mailingsystem" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Network" class="cp-network custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Firewall" class="cp-goldengate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Infoblox" class="cp-infoblox custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Router" class="cp-router custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Switch" class="cp-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="OS" class="cp-os-type custom-cursor-on-hover"></i></td>
                                    <td><i title="Linux" class="cp-linux" cursorshover="true"></i></td>
                                    <td><i title="HP" class="cp-hp custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover"></i></td>
                                    <td><i title="rsync" class="cp-rsync custom-cursor-on-hover"></i></td>
                                    <td><i title="Veeam" class="cp-Veeam custom-cursor-on-hover"></i></td>
                                    <td><i title="Storage" class="cp-stand-storage custom-cursor-on-hover"></i></td>
                                    <td><i title="hds" class="cp-hds custom-cursor-on-hover"></i></td>
                                    <td><i title="Netapp" class="cp-power-cli" cursorshover="true"></i></td>
                                    <td><i title="systemmanagementtool" class="cp-system-management-tool" cursorshover="true"></i></td>
                                </tr>

                                <tr>
                                    <td><i title="Oracle Ops Center" class="cp-microsoft custom-cursor-on-hover"></i></td>
                                    <td><i title="sun-ilom" class="cp-sun-ilom custom-cursor-on-hover"></i></td>
                                    <td><i title="veritas cluster" class="cp-veritas-cluster custom-cursor-on-hover"></i></td>
                                    <td><i title="virtualization" class="cp-virtualization custom-cursor-on-hover"></i></td>
                                    <td><i title="Aix" class="cp-aix custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="solaris" class="cp-oracle-solaris custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="vmware" class="cp-vmware custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="web" class="cp-web custom-cursor-on-hover"></i></td>
                                    <td><i title="power-cli" class="cp-power-cli custom-cursor-on-hover"></i></td>
                                    <td><i title="Workflow" class="cp-workflow custom-cursor-on-hover"></i></td>
                                    <td><i title="Replication" class="cp-replication-rotate custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Web" class="cp-web"></i> </td>
                                    <td><i title="Exchange" class="cp-exchange custom-cursor-on-hover" cursorshover="true"></i></td>
                                    <td><i title="Sunllom" class="cp-oracle-ops custom-cursor-on-hover" cursorshover="true"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Infoblox" class="cp-soft-layer custom-cursor-on-hover"></i>        </td>
                                    <td><i title="Solution" class="cp-solution custom-cursor-on-hover"></i></td>
                                    <td><i title="Token" class="cp-token custom-cursor-on-hover"></i></td>
                                    <td><i title="General" class="cp-general custom-cursor-on-hover"></i></td>
                                    <td><i title="Create-Logger" class="cp-create-Logger custom-cursor-on-hover"></i></td>
                                    <td><i title="Conditional" class="cp-conditional custom-cursor-on-hover"></i></td>
                                    <td><i title="Loop" class="cp-loop custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="System" class="cp-system custom-cursor-on-hover"></i></td>
                                    <td><i title="Delay" class="cp-delay custom-cursor-on-hover"></i></td>
                                    <td><i title="If" class="cp-if custom-cursor-on-hover"></i></td>
                                    <td><i title="circle-switch" class="cp-circle-switch custom-cursor-on-hover"></i></td>
                                    <td><i title="data-source" class="cp-data-source custom-cursor-on-hover"></i></td>
                                    <td><i title="Rule" class="cp-rule custom-cursor-on-hover"></i></td>
                                    <td><i title="Wait" class="cp-wait custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Common" class="cp-common custom-cursor-on-hover"></i></td>
                                    <td><i title="Security" class="cp-security custom-cursor-on-hover"></i></td>
                                    <td><i title="Powershell" class="cp-powershell custom-cursor-on-hover"></i></td>
                                    <td><i title="Conversion" class="cp-conversion custom-cursor-on-hover"></i></td>
                                    <td><i title="Script" class="cp-script custom-cursor-on-hover"></i></td>
                                    <td><i title="SSH" class="cp-SSH custom-cursor-on-hover"></i></td>
                                    <td><i title="Connect " class="cp-connect custom-cursor-on-hover"></i></td>
                                </tr>
                                <tr>
                                    <td><i title="Error-handing " class="cp-error-handing custom-cursor-on-hover"></i></td>
                                    <td><i title="Stringutility" class="cp-stringutility custom-cursor-on-hover"></i></td>
                                    <td><i title="Wmi" class="cp-wmi custom-cursor-on-hover"></i></td>
                                    <td><i title="Log Files" class="cp-log-file-name custom-cursor-on-hover"></i></td>
                                    <td><i title="Configure Settings" class="cp-configure-settings custom-cursor-on-hover"></i></td>
                                    <td><i title="prsite" class="cp-prsite custom-cursor-on-hover"></i></td>
                                    <td><i title="Mongo DB" class="cp-mongo-db"></i></td>
                                  
                                </tr>
                                <tr>
                                    <td><i title="Nutanix" class="cp-nutanix custom-cursor-on-hover"></i></td>
                                    <td><i title="IBM-AIX" class="cp-IBM-AIX custom-cursor-on-hover"></i></td>
                                    <td><i title="Mainframe" class="cp-fal-server custom-cursor-on-hover"></i></td>
                                    <td><i title="EBDR" class="cp-ebdr custom-cursor-on-hover"></i></td>
                                    <td><i title="Double Take" class="cp-double-take custom-cursor-on-hover"></i></td>
                                    <td><i title="H3PAR" class="cp-H3PAR custom-cursor-on-hover"></i></td>
                                    <td><i title="EMC" class="cp-EMC custom-cursor-on-hover"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>

            <div class="mb-3 hideForReplication">
                <div class="form-group">
                    <label class="form-label">Version</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-insert-left"></i></span>
                        <input type="text" id="componentTypeVersion" class="form-control" placeholder="Add Version"
                               oninput="validateComponentVersion()" autocomplete="off" onkeypress="handleKeyPress(event)" />
                        <span class="input-group-text" title="Add" role="button">
                            <i id="btnSaveProfile" role="button" class="cp-circle-plus fs-5 text-primary ms-2" onclick="addVersionData()"></i>
                        </span>                        
                    </div>
                    <span id="componentTypeVersionError"></span>
                    @* <input type="hidden" id="propertiesData" asp-for="Properties" /> *@
                    @*<span id="ComponentVersion-error"></span>*@
                </div>
            </div>

            <div class="mb-3 hideForSelectVersion">
                <div class="form-group">
                    <label class="form-label">Selected Versions</label>
                    <div class="input-group" style="height: 100px;overflow-y: auto; overflow-x:hidden;">
                        @* <input type="text" disabled id="selectedVersionDetails" class="form-control selectedVersion" />*@
                        <div id="selectedVersionDetails"></div>
                    </div>                   
                </div>
            </div>

        </div>
        <input asp-for="Id" type="hidden" id="textServerTypeId" />
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" title="Cancel" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" title="Save" id="componentTypeSaveButton">Save</button>
            </div>
        </div>
    </form>
</div>
