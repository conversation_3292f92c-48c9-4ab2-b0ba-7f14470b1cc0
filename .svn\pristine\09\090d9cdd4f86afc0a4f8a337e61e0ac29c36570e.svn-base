﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;

namespace ContinuityPatrol.Persistence.Repositories;

public class StateMonitorStatusRepository : BaseRepository<StateMonitorStatus>, IStateMonitorStatusRepository
{
    private readonly ApplicationDbContext _dbContext;

    public StateMonitorStatusRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
}