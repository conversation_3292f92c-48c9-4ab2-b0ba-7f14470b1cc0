﻿using ContinuityPatrol.Application.Features.Setting.Commands.Create;
using ContinuityPatrol.Application.Features.Setting.Commands.Update;
using ContinuityPatrol.Application.Features.Setting.Queries.GetBySKey;
using ContinuityPatrol.Application.Features.Setting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SettingModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class SettingService :  ISettingService
{
    private readonly IBaseClient _client;
    public SettingService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<BaseResponse> CreateAsync(CreateSettingCommand setting)
    {
        var request = new RestRequest("api/v6/settings", Method.Post);

        request.AddJsonBody(setting);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateSettingCommand setting)
    {
        var request = new RestRequest("api/v6/settings", Method.Put);

        request.AddJsonBody(setting);

        return await _client.Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string settingId)
    {
        var request = new RestRequest($"api/v6/settings/{settingId}", Method.Delete);

        return await _client.Delete<BaseResponse>(request);
    }

    public async Task<PaginatedResult<SettingListVm>> GetSettingPaginatedList(GetSettingPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/settings/paginated-list");

        return await _client.Get<PaginatedResult<SettingListVm>>(request);
    }

    public async Task<bool> IsSettingNameExist(string settingName, string? id)
    {
        var request = new RestRequest($"api/v6/settings/name-exist?key={settingName}&id={id}");

        return await _client.Get<bool>(request);
    }
    public async Task<List<SettingListVm>> GetSettingsList()
    {
        var request = new RestRequest("api/v6/settings");

        return await _client.Get<List<SettingListVm>>(request);
    }
    public async Task<GetSettingBySKeyVm> GetSettingBySKey(string sKey)
    {
        var request = new RestRequest($"api/v6/settings/skey?key={sKey}");

        return await _client.Get<GetSettingBySKeyVm>(request);
    }
}