﻿using ContinuityPatrol.Application.Features.TableAccess.Event.Create;

namespace ContinuityPatrol.Application.Features.TableAccess.Commands.Create;

public class CreateTableAccessCommandHandler : IRequestHandler<CreateTableAccessCommand, CreateTableAccessResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly ITableAccessRepository _tableAccessRepository;

    public CreateTableAccessCommandHandler(IMapper mapper, ITableAccessRepository tableAccessRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _tableAccessRepository = tableAccessRepository;
        _publisher = publisher;
    }

    public async Task<CreateTableAccessResponse> Handle(CreateTableAccessCommand request,
        CancellationToken cancellationToken)
    {
        var tableAccess = _mapper.Map<Domain.Entities.TableAccess>(request);

        await _tableAccessRepository.AddAsync(tableAccess);

        var response = new CreateTableAccessResponse
        {
            Message = Message.Create("Table Access", tableAccess.TableName),
            TableAccessId = tableAccess.ReferenceId
        };

        await _publisher.Publish(new TableAccessCreatedEvent { TableName = tableAccess.TableName }, cancellationToken);

        return response;
    }
}