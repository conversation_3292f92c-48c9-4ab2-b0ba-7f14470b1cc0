using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ReportScheduleExecutionFixture : IDisposable
{
    public List<ReportScheduleExecution> ReportScheduleExecutionPaginationList { get; set; }
    public List<ReportScheduleExecution> ReportScheduleExecutionList { get; set; }
    public ReportScheduleExecution ReportScheduleExecutionDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ReportScheduleExecutionFixture()
    {
        var fixture = new Fixture();

        ReportScheduleExecutionList = fixture.Create<List<ReportScheduleExecution>>();

        ReportScheduleExecutionPaginationList = fixture.CreateMany<ReportScheduleExecution>(20).ToList();

        ReportScheduleExecutionDto = fixture.Create<ReportScheduleExecution>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
