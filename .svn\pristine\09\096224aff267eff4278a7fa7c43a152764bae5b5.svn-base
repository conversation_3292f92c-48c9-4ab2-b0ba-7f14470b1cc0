﻿using ContinuityPatrol.Application.Features.Node.Events.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Node.Commands.Create;

public class CreateNodeCommandHandler : IRequestHandler<CreateNodeCommand, CreateNodeResponse>
{
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly INodeRepository _nodeRepository;
    private readonly IPublisher _publisher;

    public CreateNodeCommandHandler(IMapper mapper, INodeRepository nodeRepository, IPublisher publisher,
        ILoggedInUserService loggedInUserService)
    {
        _nodeRepository = nodeRepository;
        _mapper = mapper;
        _publisher = publisher;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<CreateNodeResponse> Handle(CreateNodeCommand request, CancellationToken cancellationToken)
    {
        request.CompanyId = _loggedInUserService.CompanyId;

        var node = _mapper.Map<Domain.Entities.Node>(request);

        node = await _nodeRepository.AddAsync(node);

        var response = new CreateNodeResponse
        {
            Message = Message.Create(nameof(Domain.Entities.Node), node.Name),

            NodeId = node.ReferenceId
        };

        await _publisher.Publish(new NodeCreatedEvent { NodeName = node.Name }, cancellationToken);

        return response;
    }
}