﻿using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;

namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetNames;

public class GetInfraObjectNameQueryHandler : IRequestHandler<GetInfraObjectNameQuery, List<GetInfraObjectNameVm>>
{
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly IMapper _mapper;

    public GetInfraObjectNameQueryHandler(IMapper mapper, IInfraObjectRepository infraObjectRepository)
    {
        _mapper = mapper;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<List<GetInfraObjectNameVm>> Handle(GetInfraObjectNameQuery request,
        CancellationToken cancellationToken)
    {
        var infraObject = await _infraObjectRepository.GetInfraObjectNames();

        return _mapper.Map<List<GetInfraObjectNameVm>>(infraObject);
    }
}