using AutoFixture;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserViewFixture : IDisposable
{
    public List<UserView> UserViewPaginationList { get; set; }
    public List<UserView> UserViewList { get; set; }
    public UserView UserViewDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string TestLoginName = "testuser";
    public const string TestEmail = "<EMAIL>";
    public const string TestRole = "754483ba-4a02-4e7e-93cf-315d47c6822e"; // SuperAdmin
    public const string TestRoleName = "SuperAdmin";
    public const string TestUserId = "user-123";

    public ApplicationDbContext DbContext { get; private set; }

    public UserViewFixture()
    {
        var fixture = new Fixture();

        UserViewList = fixture.Create<List<UserView>>();

        UserViewPaginationList = fixture.CreateMany<UserView>(20).ToList();

        UserViewDto = fixture.Create<UserView>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public UserView CreateUserView(
        string loginName = TestLoginName,
        string companyId = CompanyId,
        string role = TestRole,
        string roleName = TestRoleName,
        string email = TestEmail,
        bool isActive = true,
        bool isLock = false,
        bool isLoggedIn = false,
        bool isReset = false,
        bool infraObjectAllFlag = false,
        bool isVerify = false,
        bool isGroup = false,
        bool isDefaultDashboard = false,
        string loginType = "Local",
        int sessionTimeout = 30)
    {
        return new UserView
        {
            ReferenceId = Guid.NewGuid().ToString(),
            LoginName = loginName,
            CompanyId = companyId,
            CompanyName = "Test Company",
            LoginPassword = "TestPassword123",
            Role = role,
            RoleName = roleName,
            RoleLogo = "role-logo.png",
            LoginType = loginType,
            IsLock = isLock,
            IsReset = isReset,
            InfraObjectAllFlag = infraObjectAllFlag,
            SessionTimeout = sessionTimeout,
            IsVerify = isVerify,
            TwoFactorAuthentication = "None",
            IsGroup = isGroup,
            IsDefaultDashboard = isDefaultDashboard,
            Url = "http://localhost",
            UserName = loginName,
            Mobile = "1234567890",
            Email = email,
            AlertMode = "Email",
            IsPreferredMode = true,
            IsEmailSuccess = true,
            LogoName = "user-logo.png",
            Properties = "{}",
            IsApplication = 0,
            IsLoggedIn = isLoggedIn,
            LoginDate = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<UserView> CreateMultipleUserViews(
        int count,
        string companyId = CompanyId,
        bool isActive = true)
    {
        var userViews = new List<UserView>();
        for (int i = 1; i <= count; i++)
        {
            userViews.Add(CreateUserView(
                loginName: $"user{i:D3}",
                companyId: companyId,
                role: i % 2 == 0 ? TestRole : "dc13f0c9-6cb9-43bb-b29e-3d1c6c1da24d", // Alternate between SuperAdmin and Administrator
                roleName: i % 2 == 0 ? TestRoleName : "Administrator",
                email: $"user{i:D3}@example.com",
                isActive: isActive
            ));
        }
        return userViews;
    }

    public UserView CreateSiteAdminUserView(
        string loginName = "siteadmin",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            role: "d635aff0-1f05-4d61-b942-e15c19e180b8", // SiteAdmin
            roleName: "SiteAdmin",
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateSuperAdminUserView(
        string loginName = "superadmin",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            role: TestRole, // SuperAdmin
            roleName: TestRoleName,
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateAdministratorUserView(
        string loginName = "admin",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            role: "dc13f0c9-6cb9-43bb-b29e-3d1c6c1da24d", // Administrator
            roleName: "Administrator",
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateOperatorUserView(
        string loginName = "operator",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            role: "8a9d55f3-8887-4aa1-9525-945ae910073b", // Operator
            roleName: "Operator",
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateManagerUserView(
        string loginName = "manager",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            role: "c9a9b83a-bf69-4054-ab5b-7d52c0e94e14", // Manager
            roleName: "Manager",
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateLoggedInUserView(
        string loginName = "loggedinuser",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            isLoggedIn: true,
            email: $"{loginName}@example.com"
        );
    }

    public UserView CreateLockedUserView(
        string loginName = "lockeduser",
        string companyId = CompanyId)
    {
        return CreateUserView(
            loginName: loginName,
            companyId: companyId,
            isLock: true,
            email: $"{loginName}@example.com"
        );
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
