﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Job.Events.UpdateJobState;

public class JobStateUpdatedEventHandler : INotificationHandler<JobStateUpdatedEvent>
{
    private readonly ILogger<JobStateUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public JobStateUpdatedEventHandler(ILoggedInUserService userService, ILogger<JobStateUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(JobStateUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.MonitoringJob.ToString(),
            Action = $"{ActivityType.Update} {Modules.MonitoringJob}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Job Name :'{updatedEvent.JobName}' , State:{updatedEvent.State} updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"Job Name :'{updatedEvent.JobName}',  State : {updatedEvent.State} updated successfully.");
    }
}