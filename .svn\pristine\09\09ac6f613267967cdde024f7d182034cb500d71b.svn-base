using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class UserInfosControllerTests : IClassFixture<UserInfoFixture>
{
    private readonly UserInfoFixture _userInfoFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly UserInfosController _controller;

    public UserInfosControllerTests(UserInfoFixture userInfoFixture)
    {
        _userInfoFixture = userInfoFixture;
        
        var testBuilder = new ControllerTestBuilder<UserInfosController>();
        _controller = testBuilder.CreateController(
            _ => new UserInfosController(),
            out _mediatorMock);
    }

    #region GetUserId Tests

    [Fact]
    public async Task GetUserId_WithValidUserId_ReturnsOkResult()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var expectedVm = new UserInfoDetailVm
        {
            UserId = userId,
            UserName = "TestUser",
            Email = "<EMAIL>",
            Mobile = "1234567890",
            AlertMode = "Email",
            IsPreferredMode = true,
            LogoName = "logo.png"
        };

        var mediatorMock = new Mock<IMediator>();
        mediatorMock
            .Setup(m => m.Send(
                It.Is<GetUserInfoDetailQuery>(q => q.UserId == userId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await _controller.GetUserId(userId);

    }



    [Fact]
    public async Task GetUserId_WithInvalidUserId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUserId("invalid-guid"));
    }

    [Fact]
    public async Task GetUserId_WithEmptyUserId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUserId(""));
    }

    [Fact]
    public async Task GetUserId_WithNonExistentUserId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentUserId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserInfoDetailQuery>(q => q.UserId == nonExistentUserId), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new NotFoundException("UserInfo", nonExistentUserId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetUserId(nonExistentUserId));
    }

    [Fact]
    public async Task GetUserId_WithValidUserId_ReturnsCompleteUserInfo()
    {
        // Arrange
        var validUserId = Guid.NewGuid().ToString();
        var expectedDetail = new UserInfoDetailVm
        {
            UserId = validUserId,
            UserName = "Jane Manager",
            Mobile = "******-7890",
            Email = "<EMAIL>",
            AlertMode = "SMS",
            IsPreferredMode = false,
            LogoName = "jane_manager_avatar.png"
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserInfoDetailQuery>(q => q.UserId == validUserId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetUserId(validUserId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<UserInfoDetailVm>(okResult.Value);
        Assert.Equal(validUserId, returnedDetail.UserId);
        Assert.Equal("Jane Manager", returnedDetail.UserName);
        Assert.Equal("******-7890", returnedDetail.Mobile);
        Assert.Equal("<EMAIL>", returnedDetail.Email);
        Assert.Equal("SMS", returnedDetail.AlertMode);
        Assert.False(returnedDetail.IsPreferredMode);
        Assert.Equal("jane_manager_avatar.png", returnedDetail.LogoName);
    }

    #endregion

    #region UpdateUserInfo Tests

    [Fact]
    public async Task UpdateUserInfo_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _userInfoFixture.UpdateUserInfoCommand;
        var expectedResponse = _userInfoFixture.UpdateUserInfoResponse;

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUserInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateUserInfoResponse>(okResult.Value);
        Assert.Equal(expectedResponse.UserId, returnedResponse.UserId);
        Assert.Equal(expectedResponse.Message, returnedResponse.Message);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task UpdateUserInfo_WithNonExistentUserId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _userInfoFixture.UpdateUserInfoCommand;
        command.UserId = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new NotFoundException("UserInfo", command.UserId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateUserInfo(command));
    }

    [Fact]
    public async Task UpdateUserInfo_WithUpdatedAlertPreferences_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdateUserInfoCommand
        {
            UserId = Guid.NewGuid().ToString(),
            UserName = "John Smith",
            Email = "<EMAIL>",
            Mobile = "******-1234",
            AlertMode = "SMS", // Updated alert mode
            IsPreferredMode = true, // Updated preference
            LogoName = "john_smith_avatar.png"
        };

        var expectedResponse = new UpdateUserInfoResponse
        {
            UserId = command.UserId,
            Message = $"UserInfo '{command.UserName}' has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUserInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateUserInfoResponse>(okResult.Value);
        Assert.Equal(command.UserId, returnedResponse.UserId);
        Assert.Equal($"UserInfo '{command.UserName}' has been updated successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task UpdateUserInfo_WithUpdatedContactInfo_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdateUserInfoCommand
        {
            UserId = Guid.NewGuid().ToString(),
            UserName = "Sarah Johnson",
            Email = "<EMAIL>", // Updated email
            Mobile = "******-9876", // Updated mobile
            AlertMode = "Email",
            IsPreferredMode = false,
            LogoName = "sarah_johnson_avatar.png"
        };

        var expectedResponse = new UpdateUserInfoResponse
        {
            UserId = command.UserId,
            Message = $"UserInfo '{command.UserName}' has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUserInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateUserInfoResponse>(okResult.Value);
        Assert.Equal(command.UserId, returnedResponse.UserId);
        Assert.Equal($"UserInfo '{command.UserName}' has been updated successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task UpdateUserInfo_WithUpdatedProfileImage_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdateUserInfoCommand
        {
            UserId = Guid.NewGuid().ToString(),
            UserName = "Michael Brown",
            Email = "<EMAIL>",
            Mobile = "******-5678",
            AlertMode = "Email",
            IsPreferredMode = true,
            LogoName = "michael_brown_new_avatar.png" // Updated logo/avatar
        };

        var expectedResponse = new UpdateUserInfoResponse
        {
            UserId = command.UserId,
            Message = $"UserInfo '{command.UserName}' has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUserInfo(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateUserInfoResponse>(okResult.Value);
        Assert.Equal(command.UserId, returnedResponse.UserId);
        Assert.Equal($"UserInfo '{command.UserName}' has been updated successfully", returnedResponse.Message);
    }
    [Fact]
    public void ClearDataCache_WhenCalled_ThrowsNotImplementedException()
    {
        // Arrange
        var controller = new UserInfosController();

        // Act & Assert
        Assert.Throws<NotImplementedException>(() => controller.ClearDataCache());
    }

    #endregion
}
