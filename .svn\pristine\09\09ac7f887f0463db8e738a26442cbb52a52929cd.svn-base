using ContinuityPatrol.Application.Features.RsyncOption.Commands.Create;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Delete;
using ContinuityPatrol.Application.Features.RsyncOption.Commands.Update;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetDetail;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetList;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.RsyncOption.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RsyncOptionModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Configuration;

public class RsyncOptionService : BaseService, IRsyncOptionService
{
    public RsyncOptionService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<RsyncOptionListVm>> GetRsyncOptionList()
    {
        Logger.LogDebug("Get All RsyncOptions");

        return await Mediator.Send(new GetRsyncOptionListQuery());
    }

    public async Task<RsyncOptionDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RsyncOption Id");

        Logger.LogDebug($"Get RsyncOption Detail by Id '{id}'");

        return await Mediator.Send(new GetRsyncOptionDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateRsyncOptionCommand createRsyncOptionCommand)
    {
        Logger.LogDebug($"Create RsyncOption '{createRsyncOptionCommand}'");

        return await Mediator.Send(createRsyncOptionCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateRsyncOptionCommand updateRsyncOptionCommand)
    {
        Logger.LogDebug($"Update RsyncOption '{updateRsyncOptionCommand}'");

        return await Mediator.Send(updateRsyncOptionCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "RsyncOption Id");

        Logger.LogDebug($"Delete RsyncOption Details by Id '{id}'");

        return await Mediator.Send(new DeleteRsyncOptionCommand { Id = id });
    }

    #region NameExist

    public async Task<bool> IsRsyncOptionNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "RsyncOption Name");

        Logger.LogDebug($"Check Name Exists Detail by RsyncOption Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetRsyncOptionNameUniqueQuery { Name = name, Id = id });
    }

    #endregion

    #region Paginated

    public async Task<PaginatedResult<RsyncOptionListVm>> GetPaginatedRsyncOptions(
        GetRsyncOptionPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in RsyncOption Paginated List");

        return await Mediator.Send(query);
    }

    #endregion
}