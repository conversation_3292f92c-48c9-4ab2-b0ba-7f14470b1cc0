﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.CGExecutionReport.Queries;

public class GetCgExecutionPaginatedListQueryHandlerTests : IClassFixture<CGExecutionReportFixture>
{
    private readonly CGExecutionReportFixture _fixture;
    private readonly Mock<IResiliencyReadyWorkflowScheduleLogRepository> _mockWorkflowScheduleLogRepository;
    private readonly Mock<IRpForVmCgEnableDisableStatusRepository> _mockCgEnableDisableStatusRepository;
    private readonly GetCgExecutionPaginatedListQueryHandler _handler;

    public GetCgExecutionPaginatedListQueryHandlerTests(CGExecutionReportFixture fixture)
    {
        _fixture = fixture;

        // Setup test data in fixture (like BusinessService pattern)
        _fixture.CgExecutionPaginatedList[0].WorkflowName = "Test_Workflow_1";
        _fixture.CgExecutionPaginatedList[0].CurrentActionName = "Start_Action";
        _fixture.CgExecutionPaginatedList[0].Status = "Running";

        _fixture.CgExecutionPaginatedList[1].WorkflowName = "Test_Workflow_2";
        _fixture.CgExecutionPaginatedList[1].CurrentActionName = "Process_Action";
        _fixture.CgExecutionPaginatedList[1].Status = "Completed";

        // Setup your existing mocks properly
        _mockWorkflowScheduleLogRepository = new Mock<IResiliencyReadyWorkflowScheduleLogRepository>();
        _mockWorkflowScheduleLogRepository.Setup(x => x.GetResiliencyReadyWorkflowScheduleLogById(It.IsAny<string>()))
            .ReturnsAsync((123, "CurrentActionName-Test"));

        _mockCgEnableDisableStatusRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();

        // Setup the pagination method to return your fixture data
        _mockCgEnableDisableStatusRepository.Setup(x => x.GetCgExecutionPaginatedList(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<int>()))
            .ReturnsAsync((string type, string startDate, string endDate, int pageNumber, int pageSize) =>
            {
                // Use your fixture data
                var pagedData = _fixture.CgExecutionPaginatedList
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedResult<CgExecutionPaginatedListVm>
                {
                    Data = pagedData,
                    TotalCount = _fixture.CgExecutionPaginatedList.Count,
                    PageSize = pageSize,
                    CurrentPage = pageNumber,
                    Succeeded = true,
                    TotalPages = (int)Math.Ceiling((double)_fixture.CgExecutionPaginatedList.Count / pageSize)
                };
            });

        _handler = new GetCgExecutionPaginatedListQueryHandler(
            _mockWorkflowScheduleLogRepository.Object,
            _mockCgEnableDisableStatusRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        // Act
        var result = await _handler.Handle(new GetCgExecutionPaginatedListQuery
        {
            Type = "All",
            StartDate = "2024-01-01",
            EndDate = "2025-12-31",
            PageNumber = 1,
            PageSize = 10
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<CgExecutionPaginatedListVm>>();
        result.TotalCount.ShouldBe(10); // Your fixture creates 10 items
        result.TotalPages.ShouldBe(1);
        result.Succeeded.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_PaginatedCgExecution_When_ValidRequest()
    {
        // Act
        var result = await _handler.Handle(new GetCgExecutionPaginatedListQuery
        {
            Type = "All",
            StartDate = "2024-01-01",
            EndDate = "2025-12-31",
            PageNumber = 1,
            PageSize = 5
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<CgExecutionPaginatedListVm>>();
        result.TotalCount.ShouldBe(10);
        result.Data.Count.ShouldBe(5); // Page size

        result.Data[0].WorkflowName.ShouldBe("Test_Workflow_1");
        result.Data[0].CurrentActionName.ShouldBe("CurrentActionName-Test");
        result.Data[0].Status.ShouldBe("Running");

        result.Data[1].WorkflowName.ShouldBe("Test_Workflow_2");
        result.Data[1].CurrentActionName.ShouldBe("CurrentActionName-Test");
        result.Data[1].Status.ShouldBe("Completed");
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoData()
    {
        // Arrange - Setup empty result for this test
        _mockCgEnableDisableStatusRepository.Setup(x => x.GetCgExecutionPaginatedList(
                "NoData", It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync(new PaginatedResult<CgExecutionPaginatedListVm>
            {
                Data = new List<CgExecutionPaginatedListVm>(),
                TotalCount = 0,
                PageSize = 10,
                CurrentPage = 1,
                Succeeded = true,
                TotalPages = 0
            });

        // Act
        var result = await _handler.Handle(new GetCgExecutionPaginatedListQuery
        {
            Type = "NoData",
            StartDate = "2024-01-01",
            EndDate = "2025-01-01",
            PageNumber = 1,
            PageSize = 10
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<CgExecutionPaginatedListVm>>();
        result.TotalCount.ShouldBe(0);
        result.Data.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetCgExecutionPaginatedListMethod_OnlyOnce()
    {
        // Act
        await _handler.Handle(new GetCgExecutionPaginatedListQuery
        {
            Type = "All",
            StartDate = "2024-01-01",
            EndDate = "2025-12-31",
            PageNumber = 1,
            PageSize = 10
        }, CancellationToken.None);

        // Assert
        _mockCgEnableDisableStatusRepository.Verify(x => x.GetCgExecutionPaginatedList(
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<string>(),
            It.IsAny<int>(),
            It.IsAny<int>()), Times.Once);
    }
}




//using ContinuityPatrol.Application.Features.CGExecutionReport.Queries.GetCgExecutionReportPaginatedList;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Shared.Core.Wrapper;

//namespace ContinuityPatrol.Application.UnitTests.Features.CGExecution.Queries;

//public class GetCgExecutionPaginatedListQueryHandlerTests : IClassFixture<CGExecutionReportFixture>
//{
//    private readonly CGExecutionReportFixture _fixture;
//    private readonly Mock<IResiliencyReadyWorkflowScheduleLogRepository> _mockWorkflowScheduleLogRepository;
//    private readonly Mock<IRpForVmCgEnableDisableStatusRepository> _mockCgEnableDisableStatusRepository;
//    private readonly GetCgExecutionPaginatedListQueryHandler _handler;

//    public GetCgExecutionPaginatedListQueryHandlerTests(CGExecutionReportFixture fixture)
//    {
//        _fixture = fixture;

//        _mockWorkflowScheduleLogRepository = new Mock<IResiliencyReadyWorkflowScheduleLogRepository>();

//        _mockCgEnableDisableStatusRepository = new Mock<IRpForVmCgEnableDisableStatusRepository>();
//        _handler = new GetCgExecutionPaginatedListQueryHandler(
//            _mockWorkflowScheduleLogRepository.Object,
//            _mockCgEnableDisableStatusRepository.Object);
//    }

//    [Fact]
//    public async Task Handle_Returns_PaginatedResult_With_ConditionActionId()
//    {
//        // Arrange
//        var query = new GetCgExecutionPaginatedListQuery
//        {
//            Type = "All",
//            StartDate = "2024-01-01",
//            EndDate = "2025-12-31",
//            PageNumber = 1,
//            PageSize = 5
//        };

//        // Act
//        PaginatedResult<CgExecutionPaginatedListVm> result = await _handler.Handle(query, CancellationToken.None);

//        // Assert
//        Assert.NotNull(result);
//        Assert.True(result.Succeeded);
//        Assert.NotEmpty(result.Data);
//        Assert.All(result.Data, item =>
//        {
//            Assert.False(string.IsNullOrWhiteSpace(item.Id));
//            Assert.False(string.IsNullOrWhiteSpace(item.CurrentActionName));
//        });
//    }

//    [Fact]
//    public async Task Handle_Returns_EmptyResult_When_NoData()
//    {
//        // Arrange - empty the mock list
//        _fixture.CgExecutionPaginatedList.Clear();

//        var query = new GetCgExecutionPaginatedListQuery
//        {
//            Type = "Any",
//            StartDate = "2024-01-01",
//            EndDate = "2025-12-31",
//            PageNumber = 1,
//            PageSize = 10
//        };

//        // Act
//        var result = await _handler.Handle(query, CancellationToken.None);

//        // Assert
//        Assert.NotNull(result);
//        Assert.True(result.Succeeded);
//        Assert.Empty(result.Data);
//        Assert.Equal(0, result.TotalCount);
//    }
//    [Fact]
//    public async Task Handle_Call_GetCgExecutionPaginatedListMethod_OnlyOnce()
//    {
//        // Act
//        await _handler.Handle(new GetCgExecutionPaginatedListQuery
//        {
//            Type = "All",
//            StartDate = "2024-01-01",
//            EndDate = "2025-12-31",
//            PageNumber = 1,
//            PageSize = 10
//        }, CancellationToken.None);

//        // Assert
//        _mockCgEnableDisableStatusRepository.Verify(x => x.GetCgExecutionPaginatedList(
//            It.IsAny<string>(),
//            It.IsAny<string>(),
//            It.IsAny<string>(),
//            It.IsAny<int>(),
//            It.IsAny<int>()), Times.Once);
//    }
//}
