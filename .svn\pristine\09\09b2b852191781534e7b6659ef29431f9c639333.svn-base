﻿using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.IncidentManagement.Commands;

public class DeleteIncidentManagementTests : IClassFixture<IncidentManagementFixture>
{
    private readonly IncidentManagementFixture _incidentManagementFixture;
    private readonly Mock<IIncidentManagementRepository> _mockIncidentManagementRepository;
    private readonly DeleteIncidentManagementCommandHandler _handler;

    public DeleteIncidentManagementTests(IncidentManagementFixture incidentManagementFixture)
    {
        _incidentManagementFixture = incidentManagementFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockIncidentManagementRepository = IncidentManagementRepositoryMocks.DeleteIncidentManagementRepository(_incidentManagementFixture.IncidentManagements);

        _handler = new DeleteIncidentManagementCommandHandler(_mockIncidentManagementRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_IncidentManagementDeleted()
    {
        var validGuid = Guid.NewGuid();

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteIncidentManagementCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteIncidentManagementResponse_When_IncidentManagementDeleted()
    {
        var validGuid = Guid.NewGuid();

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteIncidentManagementCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteIncidentManagementResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_IncidentManagementDeleted_ByReferenceId()
    {
        var validGuid = Guid.NewGuid();

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteIncidentManagementCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var incidentManagement = await _mockIncidentManagementRepository.Object.GetByReferenceIdAsync(validGuid.ToString());

        incidentManagement.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidIncidentManagementId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteIncidentManagementCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = validGuid.ToString();

        _incidentManagementFixture.IncidentManagements[0].ReferenceId = Guid.NewGuid().ToString();

        await _handler.Handle(new DeleteIncidentManagementCommand { Id = _incidentManagementFixture.IncidentManagements[0].ReferenceId }, CancellationToken.None);

        _mockIncidentManagementRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockIncidentManagementRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.IncidentManagement>()), Times.Once);
    }
}