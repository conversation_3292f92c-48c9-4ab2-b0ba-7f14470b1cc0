﻿using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.RiskMitigation.Queries;

public class GetRiskMitigationDetailQueryHandlerTests : IClassFixture<RiskMitigationFixture>
{
    private readonly RiskMitigationFixture _riskMitigationFixture;

    private readonly Mock<IRiskMitigationRepository> _mockRiskMitigationRepository;

    private readonly GetRiskMitigationDetailQueryHandler _handler;

    public GetRiskMitigationDetailQueryHandlerTests(RiskMitigationFixture riskMitigationFixture)
    {
        _riskMitigationFixture = riskMitigationFixture;

        _mockRiskMitigationRepository = RiskMitigationRepositoryMocks.GetRiskMitigationRepository(_riskMitigationFixture.RiskMitigations);

        _handler = new GetRiskMitigationDetailQueryHandler(_riskMitigationFixture.Mapper, _mockRiskMitigationRepository.Object);

    }


    [Fact]
    public async Task Handle_Return_RiskMitigationDetails_When_ValidRiskMitigationId()
    {
        var result = await _handler.Handle(new GetRiskMitigationDetailQuery { Id = _riskMitigationFixture.RiskMitigations[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<RiskMitigationDetailVm>();

        result.Id.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ReferenceId);
        result.BusinessServiceId.ShouldBe(_riskMitigationFixture.RiskMitigations[0].BusinessServiceId);
        result.BusinessServiceName.ShouldBe(_riskMitigationFixture.RiskMitigations[0].BusinessServiceName);
        result.InfraObjectId.ShouldBe(_riskMitigationFixture.RiskMitigations[0].InfraObjectId);
        result.InfraObjectName.ShouldBe(_riskMitigationFixture.RiskMitigations[0].InfraObjectName);
        result.UnderControlRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].UnderControlRTO);
        result.ExeedRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ExceedRTO);
        result.MaintenanceRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].MaintenanceRTO);
        result.RTODescription.ShouldBe(_riskMitigationFixture.RiskMitigations[0].RTODescription);
        result.UnderControlRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].UnderControlRPO);
        //result.ExeedRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ExceedRPO);
        result.MaintenanceRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].MaintenanceRPO);
        result.RPODescription.ShouldBe(_riskMitigationFixture.RiskMitigations[0].RPODescription);
        result.IsAffected.ShouldBe(_riskMitigationFixture.RiskMitigations[0].IsAffected);
        result.ErrorMessage.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidRiskMitigationId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetRiskMitigationDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));

        exceptionDetails.Message.ShouldContain("not found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetRiskMitigationDetailQuery { Id = _riskMitigationFixture.RiskMitigations[0].ReferenceId }, CancellationToken.None);

        _mockRiskMitigationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
    [Fact]
    public void Should_Assign_And_Return_All_Properties_Correctly()
    {
        // Arrange
        var vm = new RiskMitigationDetailVm
        {
            Id = "RM001",
            BusinessServiceId = "BS001",
            BusinessServiceName = "Payment Service",
            InfraObjectId = "INF123",
            InfraObjectName = "Database Server",
            UnderControlRTO = true,
            ExeedRTO = false,
            MaintenanceRTO = true,
            RTODescription = "Planned maintenance",
            UnderControlRPO = false,
            ExeedRPO = true,
            MaintenanceRPO = false,
            RPODescription = "Backup lag",
            IsAffected = true,
            ErrorMessage = "Disk failure"
        };

        // Assert
        vm.Id.ShouldBe("RM001");
        vm.BusinessServiceId.ShouldBe("BS001");
        vm.BusinessServiceName.ShouldBe("Payment Service");
        vm.InfraObjectId.ShouldBe("INF123");
        vm.InfraObjectName.ShouldBe("Database Server");
        vm.UnderControlRTO.ShouldBeTrue();
        vm.ExeedRTO.ShouldBeFalse();
        vm.MaintenanceRTO.ShouldBeTrue();
        vm.RTODescription.ShouldBe("Planned maintenance");
        vm.UnderControlRPO.ShouldBeFalse();
        vm.ExeedRPO.ShouldBeTrue();
        vm.MaintenanceRPO.ShouldBeFalse();
        vm.RPODescription.ShouldBe("Backup lag");
        vm.IsAffected.ShouldBeTrue();
        vm.ErrorMessage.ShouldBe("Disk failure");
    }
    [Fact]
    public void Should_Assign_And_Return_Id_Correctly()
    {
        // Arrange
        var query = new GetRiskMitigationDetailQuery
        {
            Id = "RM-12345"
        };

        // Assert
        query.Id.ShouldBe("RM-12345");
    }
}