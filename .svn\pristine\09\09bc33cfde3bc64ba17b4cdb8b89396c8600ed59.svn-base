using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncOptions.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSyncOptions.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DataSyncOptionsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class DataSyncOptionsService : BaseClient, IDataSyncOptionsService
{
    public DataSyncOptionsService(IConfiguration config, IAppCache cache, ILogger<DataSyncOptionsService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<DataSyncOptionsListVm>> GetDataSyncList()
    {
        var request = new RestRequest("api/v6/datasyncOptions");

        return await GetFromCache<List<DataSyncOptionsListVm>>(request, "GetDataSyncList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDataSyncOptionsCommand createDataSyncCommand)
    {
        var request = new RestRequest("api/v6/datasyncOptions", Method.Post);

        request.AddJsonBody(createDataSyncCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDataSyncOptionsCommand updateDataSyncCommand)
    {
        var request = new RestRequest("api/v6/datasyncOptions", Method.Put);

        request.AddJsonBody(updateDataSyncCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/datasyncOptions/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DataSyncOptionsDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/datasyncOptions/{id}");

        return await Get<DataSyncOptionsDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsDataSyncNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/datasyncOptions/name-exist?datasyncName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<DataSyncOptionsListVm>> GetPaginatedDataSyncs(GetDataSyncOptionsPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/datasyncs/paginated-list");

      return await Get<PaginatedResult<DataSyncOptionsListVm>>(request);
  }
   #endregion
}
