﻿using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Create;
using ContinuityPatrol.Application.Features.DataSyncJob.Commands.Update;
using ContinuityPatrol.Application.Features.DataSyncJob.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.DataSyncJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class DataSyncJobService:IDataSyncJobService
{
    private readonly IBaseClient _client;
    public DataSyncJobService(IBaseClient client)
    {
        _client = client;
    }
    public async Task<BaseResponse> CreateDataSyncJob(CreateDataSyncJobCommand createDataSyncJobCommand)
    {
        var request = new RestRequest("api/v6/datasyncjobs", Method.Post);

        request.AddJsonBody(createDataSyncJobCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteDataSyncJob(string id)
    {
        var request = new RestRequest($"api/v6/datasyncjob/{id}", Method.Delete);

        return await _client.Get<BaseResponse>(request);
    }

    public async Task<PaginatedResult<DataSyncJobListVm>> GetPaginatedDataSyncJobs(GetDataSyncJobPaginatedQuery query)
    {

        var request = new RestRequest($"api/v6/datasyncjob/paginated-list/{query}");

        return await _client.Get<PaginatedResult<DataSyncJobListVm>>(request);
    }

    public async Task<DataSyncJobDetailVm> GetDataSyncJobById(string id)
    {
        var request = new RestRequest($"api/v6/datasyncjob/{id}");

        return await _client.Get<DataSyncJobDetailVm>(request);
    }

    public async Task<List<DataSyncJobListVm>> GetDataSyncJobs()
    {
        var request = new RestRequest("api/v6/datasyncjob");

        return await _client.Get<List<DataSyncJobListVm>>(request);
    }

    public async Task<BaseResponse> UpdateDataSyncJob(UpdateDataSyncJobCommand updateDataSyncJobCommand)
    {
        var request = new RestRequest("api/v6/datasyncjob", Method.Put);

        request.AddJsonBody(updateDataSyncJobCommand);

        return await _client.Put<BaseResponse>(request);
    }
}