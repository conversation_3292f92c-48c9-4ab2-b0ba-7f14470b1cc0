﻿namespace ContinuityPatrol.Domain.Views;

public class WorkflowExecutionTimeDeviationView
{
    public int WorkflowId { get; set; }
    public string ReferenceId { get; set; }
    public string WorkflowName { get; set; }
    public int ExecutionTime { get; set; }
    public string ConfiguredRTO { get; set; }
    public double AvgExecutionTime { get; set; }  // FLOAT in SQL maps best to double
    public int RunCount { get; set; }
    public double DeviationPercent { get; set; }  // Result of percentage calc
    public int WeightagePoints { get; set; }
    public string DeviationFormula { get; set; }
}
