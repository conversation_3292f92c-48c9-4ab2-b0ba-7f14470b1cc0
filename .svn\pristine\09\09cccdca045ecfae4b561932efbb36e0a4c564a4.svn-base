using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository
{
    public class WorkflowActionFieldMasterRepositoryTests :IClassFixture<WorkflowActionFieldMasterFixture>
    {
        private readonly WorkflowActionFieldMasterFixture _workflowActionFieldMasterFixture;
        private readonly ApplicationDbContext _dbContext;
        private readonly WorkflowActionFieldMasterRepository _repository;

        public WorkflowActionFieldMasterRepositoryTests(WorkflowActionFieldMasterFixture workflowActionFieldMasterFixture)
        {
           _workflowActionFieldMasterFixture = workflowActionFieldMasterFixture;
            _dbContext = DbContextFactory.CreateInMemoryDbContext();
            _repository = new WorkflowActionFieldMasterRepository(_dbContext, DbContextFactory.GetMockUserService());
        }

        [Fact]
        public async Task IsNameExist_ShouldReturnTrue_WhenNameExists_AndIdIsInvalidGuid()
        {
            // Arrange
            var entity = _workflowActionFieldMasterFixture.WorkflowActionFieldMasterDto;
            entity.Name = "TestName";
            _dbContext.WorkflowActionFieldMasters.Add(entity);
            _dbContext.SaveChanges();

            // Act
            var result = await _repository.IsNameExist("TestName", "invalid-guid");

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExist_AndIdIsInvalidGuid()
        {
            // Act
            var result = await _repository.IsNameExist("NonExistentName", "invalid-guid");

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task IsNameExist_ShouldReturnExpected_WhenIdIsValidGuid()
        {
            // Arrange
            var id = Guid.NewGuid().ToString();
            var entity = _workflowActionFieldMasterFixture.WorkflowActionFieldMasterDto;
            await _dbContext.WorkflowActionFieldMasters.AddAsync(entity);
            await  _dbContext.SaveChangesAsync();

            // Act
            var result = await _repository.IsNameExist("UniqueName", id);

            // Assert
            // The Unique extension method is not standard; assuming it returns false if only one entity with the id exists
            Assert.False(result);
        }
    }
}