using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetailByDatabaseId;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class InfraReplicationMappingFixture : IDisposable
{
    public CreateInfraReplicationMappingCommand CreateInfraReplicationMappingCommand { get; set; }
    public UpdateInfraReplicationMappingCommand UpdateInfraReplicationMappingCommand { get; set; }
    public DeleteInfraReplicationMappingCommand DeleteInfraReplicationMappingCommand { get; set; }
    public GetInfraReplicationMappingDetailQuery GetInfraReplicationMappingDetailQuery { get; set; }
    public GetInfraReplicationMappingListQuery GetInfraReplicationMappingListQuery { get; set; }
    public GetInfraReplicationMappingPaginatedListQuery GetInfraReplicationMappingPaginatedListQuery { get; set; }
    
    public CreateInfraReplicationMappingResponse CreateInfraReplicationMappingResponse { get; set; }
    public UpdateInfraReplicationMappingResponse UpdateInfraReplicationMappingResponse { get; set; }
    public DeleteInfraReplicationMappingResponse DeleteInfraReplicationMappingResponse { get; set; }
    public InfraReplicationMappingDetailVm InfraReplicationMappingDetailVm { get; set; }
    public List<InfraReplicationMappingListVm> InfraReplicationMappingListVm { get; set; }

    public List<InfraReplicationMappingByDatabaseIdVm> InfraReplicationMappingByDatabaseIdListVm { get; set; }
    public PaginatedResult<InfraReplicationMappingListVm> InfraReplicationMappingPaginatedResult { get; set; }

    public InfraReplicationMappingFixture()
    {
        var fixture = new Fixture();
        
        CreateInfraReplicationMappingCommand = fixture.Create<CreateInfraReplicationMappingCommand>();
        UpdateInfraReplicationMappingCommand = fixture.Create<UpdateInfraReplicationMappingCommand>();
        DeleteInfraReplicationMappingCommand = fixture.Create<DeleteInfraReplicationMappingCommand>();
        GetInfraReplicationMappingDetailQuery = fixture.Create<GetInfraReplicationMappingDetailQuery>();
        GetInfraReplicationMappingListQuery = fixture.Create<GetInfraReplicationMappingListQuery>();
        GetInfraReplicationMappingPaginatedListQuery = fixture.Create<GetInfraReplicationMappingPaginatedListQuery>();
        
        CreateInfraReplicationMappingResponse = fixture.Create<CreateInfraReplicationMappingResponse>();
        UpdateInfraReplicationMappingResponse = fixture.Create<UpdateInfraReplicationMappingResponse>();
        DeleteInfraReplicationMappingResponse = fixture.Create<DeleteInfraReplicationMappingResponse>();
        InfraReplicationMappingDetailVm = fixture.Create<InfraReplicationMappingDetailVm>();
        InfraReplicationMappingListVm = fixture.CreateMany<InfraReplicationMappingListVm>(3).ToList();
        InfraReplicationMappingByDatabaseIdListVm = fixture.CreateMany<InfraReplicationMappingByDatabaseIdVm>(3).ToList();

        InfraReplicationMappingPaginatedResult = new PaginatedResult<InfraReplicationMappingListVm>(
            true, InfraReplicationMappingListVm, new List<string>(), 1, 10, InfraReplicationMappingListVm.Count);
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}













