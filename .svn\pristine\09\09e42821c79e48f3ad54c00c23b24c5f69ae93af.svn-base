﻿using ContinuityPatrol.Application.Features.FiaImpactType.Events.Create;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaImpactType.Events
{
    public class FiaImpactTypeCreatedEventHandlerTests
    {
        private readonly Mock<ILogger<FiaImpactTypeCreatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly FiaImpactTypeCreatedEventHandler _handler;
        private readonly List<Domain.Entities.UserActivity> _userActivityList;

        public FiaImpactTypeCreatedEventHandlerTests()
        {
            _mockLogger = new Mock<ILogger<FiaImpactTypeCreatedEventHandler>>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _userActivityList = new List<Domain.Entities.UserActivity>();
            _mockUserActivityRepository = FiaImpactTypeRepositoryMocks.CreateFiaImpactTypeEventRepository(_userActivityList);

            _mockUserService.Setup(x => x.UserId).Returns("TestUserId");
            _mockUserService.Setup(x => x.LoginName).Returns("TestUser");
            _mockUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");
            _mockUserService.Setup(x => x.RequestedUrl).Returns("/api/fia-impact-type");

            _handler = new FiaImpactTypeCreatedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Add_UserActivity_When_Event_Is_Handled()
        {
            // Arrange
            var @event = new FiaImpactTypeCreatedEvent
            {
                Name = "Test Impact"
            };

            // Act
            await _handler.Handle(@event, CancellationToken.None);

            // Assert
            Assert.Single(_userActivityList);
            var activity = _userActivityList.First();

            Assert.Equal("TestUserId", activity.UserId);
            Assert.Equal("TestUser", activity.LoginName);
            Assert.Equal("127.0.0.1", activity.HostAddress);
            Assert.Equal("/api/fia-impact-type", activity.RequestUrl);
            Assert.Equal("FiaImpactType", activity.Entity);
            Assert.Equal(ActivityType.Create.ToString(), activity.ActivityType);
            Assert.Contains("created successfully", activity.ActivityDetails);

            _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
            _mockLogger.Verify(x => x.Log(
                It.Is<LogLevel>(l => l == LogLevel.Information),
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Test Impact")),
                It.IsAny<Exception>(),
                (Func<It.IsAnyType, Exception?, string>)It.IsAny<object>()),
                Times.Once);
        }
    }
}
