﻿using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Update;
using ContinuityPatrol.Application.Features.IncidentLogs.Queries.GetPaginated;
using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract
{
    public interface IIncidentLogsService
    {
        Task<List<IncidentLogsListVm>> GetList();
        Task<IncidentLogsDetailVm> GetIncidentLogsById(string id);
        Task<BaseResponse> CreateIncidentLogs(CreateIncidentLogsCommand createIncidentLogsCommand);
        Task<BaseResponse> UpdateIncidentLogs(UpdateIncidentLogsCommand updateIncidentLogsCommand);
        Task<BaseResponse> DeleteIncidentLogs(string id);
        Task<PaginatedResult<IncidentLogsListVm>> GetPaginatedIncidentLogs(GetIncidentLogsPaginatedQuery query);
    }
}
