using ContinuityPatrol.Application.Features.CyberMappingHistory.Queries.GetCyberMappingHistoryById;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class CyberMappingHistoryController : CommonBaseController
{
    [HttpGet("{id}", Name = "GetCyberComponentMappingHistory")]
    [Authorize(Policy = Permissions.Cyber.View)]
    public async Task<ActionResult<CyberMappingHistoryIdVm>> CyberComponentMappingHistoryById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "CyberComponentMappingHistory Id");

        Logger.LogInformation($"Get CyberComponentMappingHistory Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetCyberMappingHistoryByIdQuery { CyberComponentMappingId = id }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


