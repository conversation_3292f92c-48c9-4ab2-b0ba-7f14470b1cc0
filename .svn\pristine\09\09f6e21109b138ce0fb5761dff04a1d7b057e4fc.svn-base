﻿namespace ContinuityPatrol.Web.Helper;

public class HashHelper
{
    public static string ComputeSha256Hash(string rawData)
    {
        if (string.IsNullOrEmpty(rawData))
        {
            throw new ArgumentException("Input cannot be null or empty.", nameof(rawData));
        }
        // Create a SHA256
        using var sha256Hash = SHA256.Create();

        // ComputeHash - returns byte array
        var bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));

        // Convert byte array to a string
        var builder = new StringBuilder();

        foreach (var item in bytes)
        {
            builder.Append(item.ToString("x2"));
        }
        return builder.ToString();
    }
}