﻿namespace ContinuityPatrol.Application.Features.DataSet.Commands.Create;

public class CreateDataSetCommandValidator : AbstractValidator<CreateDataSetCommand>
{
    private readonly IDataSetRepository _dataSetRepository;

    public CreateDataSetCommandValidator(IDataSetRepository dataSetRepository)
    {
        _dataSetRepository = dataSetRepository;

        RuleFor(p => p.DataSetName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Description)
            .MaximumLength(250).WithMessage("{PropertyName} Maximum 250 characters.")
               .Matches(@"^[a-zA-Z0-9_\s-]+$")
            .WithMessage("{PropertyName} contains invalid characters.")
            .When(p => p.Description.IsNotNullOrWhiteSpace());

        RuleFor(p => p.PrimaryTableName)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}.");

        RuleFor(p => p.StoredQuery)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull();

        RuleFor(e => e)
            .MustAsync(DataSetNameUnique)
            .WithMessage("A same name already exists");
    }

    private async Task<bool> DataSetNameUnique(CreateDataSetCommand createDataSetCommand, CancellationToken token)
    {
        return !await _dataSetRepository.IsDataSetNameUnique(createDataSetCommand.DataSetName);
    }
}