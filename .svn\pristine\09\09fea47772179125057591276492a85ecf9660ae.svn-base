let nameDr, DrId, editedDRData, selectedTexts = [];

const DrCalURL = {
    DrCalURLExistUrl: "Configuration/DRCalendar/IsActivityNameExist",
    getProfileUrl: "Configuration/DRCalendar/GetProfilesName",
    DrCalURLPagination: '/Configuration/DRCalendar/DrCalendarPaginationList',
    ProfileNames: "Configuration/DRCalendar/GetProfileNamesByBusinessServiceId",
    BusinessService: "Configuration/DRCalendar/GetBusinessService",
    userList: "Configuration/DRCalendar/GetUserList"
}


const drCalPermission = {
    createPermission: $("#configurationCreate").data("create-permission")?.toLowerCase(),
    deletePermission: $("#configurationDelete").data("delete-permission")?.toLowerCase()
}

if (drCalPermission.createPermission == 'false') {
    $("#create_btn").removeClass('#create_btn').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', drCalPermission.createPermission == 'false');
}

//GetUserList()
//CurrentAcitivity();
//GetBusinessService()
let events = [];
let selectedEvent = null;

$('#btnCreateDRCalendar').text("Save");

//FetchEventAndRenderCalendar();
function FetchEventAndRenderCalendar() {
    //$('#CreateModal').modal('hide');
    events = [];
    $.ajax({
        type: "GET",
        url: DrCalURL.DrCalURLPagination,
        success: function (data) {

            $.each(data?.data?.data, function (i, v) {

                const startDate = moment(v["scheduledStartDate"]);
                const endDate = v["scheduledEndDate"] ? moment(v["scheduledEndDate"]) : null;
                const currentDate = moment();
                const isFinished = endDate && currentDate.isAfter(endDate);
                const isToday = currentDate.isSame(startDate, 'day');
                const isUpcoming = currentDate.isBefore(startDate);
                const isPassed = endDate && currentDate.isAfter(endDate);
                let backgroundColor;

                if (isToday) {
                    backgroundColor = isFinished ? '#adacac' : '#39b32e';
                } else if (isUpcoming) {
                    backgroundColor = '#5da2fc';
                } else if (isPassed) {
                    backgroundColor = '#adacac';
                }

                const borderColor = backgroundColor;

                events.push({
                    eventID: v["id"],
                    title: v["activityName"],
                    description: v["description"],
                    start: startDate,
                    end: endDate,
                    Location: v['location'],
                    ActivityName: v["activityName"],
                    ActivityStatus: v["activityStatus"],
                    ActivityType: v["activityType"],
                    BusinessServiceId: v["businessServiceId"],
                    CompanyId: v["companyId"],
                    FileName: v["fileName"],
                    WorkflowProfiles: v["workflowProfiles"],
                    Responsibility: v["responsibility"],
                    RecipientTwo: v["recipientTwo"],
                    SetReminders: v["setReminders"],
                    backgroundColor: backgroundColor,
                    borderColor: borderColor,
                    allDay: false
                });
            });
            GenerateCalender(events);
        },
        error: function (error) {
            alert('failed FetchEventAndRenderCalendar');
        }
    })
}

function GenerateCalender(events) {

    $('#calender').fullCalendar('destroy');
    $('#calender').fullCalendar({
        contentHeight: 400,
        defaultDate: new Date(),
        timeFormat: 'h(:mm)a',
        dayMinWidth: 200,
        header: {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek,agendaDay'
        },
        views: {
            basicDay: {
                type: 'resourceTimeGrid',

            }
        },
        eventLimit: true,
        eventColor: '#378006',
        events: events,
        selectable: true,
        select: function (start, end) {
            Activity(events, start)

            var todaysDate = new Date();

            if (start._d.setHours(0, 0, 0, 0) >= todaysDate.setHours(0, 0, 0, 0))
            // if (start._d <= todaysDate )
            {
                selectedEvent = {
                    eventID: 0,
                    title: '',
                    description: '',
                    start: start._d,
                    end: end._d,
                    allDay: false,
                    color: ''
                };
                $("#drUserNameRecipient").val(null).trigger('change');
            }
        },
        editable: false,
        eventDrop: function (event) {
            var data = {
                EventID: event.eventID,
                Subject: event.title,
                Start: event.start.format('DD/MM/YYYY HH:mm A'),
                End: event.end != null ? event.end.format('DD/MM/YYYY HH:mm A') : null,
                Description: event.description,
                ThemeColor: event.color,
                IsFullDay: event.allDay
            };

            SaveEvent(data);
        },
        minDate: moment(),

    })
}

function Activity(events, startDate) {

    $('#EditModal #pfname,#Activitydata').empty();
    let dataFound = false;
    events.forEach(event => {
        let eStart = event.start.format("DD-MM-YYYY");
        let formattedDate = moment(event.start).format("DD-MM-YYYY HH:mm");
        let start = startDate.format("DD-MM-YYYY");

        // Current date
        let formattedNow = moment().format("DD-MM-YYYY HH:mm");

        const [dayA, monthA, yearTimeA] = formattedNow.split('-');
        const [dayB, monthB, yearTimeB] = formattedDate.split('-');

        const dateA = new Date(`${yearTimeA.split(' ')[0]}-${monthA}-${dayA}T${yearTimeA.split(' ')[1]}`);
        const dateB = new Date(`${yearTimeB.split(' ')[0]}-${monthB}-${dayB}T${yearTimeB.split(' ')[1]}`);

        if (eStart === start && dateA < dateB) {
            dataFound = true;

            let startDateFormatted = startDate.format("DD-MM-YYYY");
            let todayFormatted = moment().format("DD-MM-YYYY");

            let bgColorClass = '';
            if (todayFormatted === startDateFormatted) {
                bgColorClass = 'light-success-bg';
            } else {
                bgColorClass = 'light-blue-bg';
            }

            const isPastEvent = event.start.isBefore(moment());

            const dropdownPermissions = () => {
                const create = drCalPermission.createPermission === 'true';
                const del = drCalPermission.deletePermission === 'true';

                if (!create && !del) {
                    return `
                        <li><span class="icon-disabled dropdown-item"><i class="cp-edit me-1"></i>Edit</span></li>
                        <li><span class="icon-disabled dropdown-item"><i class="cp-Delete me-1"></i>Delete</span></li>`;
                }
                if (create && del) {
                    return `
                        <li>
                            <span role="button" id="DrEdit" class="edit-button dropdown-item" data-datasync='${JSON.stringify(event)}'>
                                <i class="cp-edit me-1"></i>Edit
                            </span>
                        </li>
                        <li>
                            <span role="button" id="dr_delete" class="delete-button dropdown-item" data-siteid="${event.eventID}" data-sitename="${event.title}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                <i class="cp-Delete me-1"></i>Delete
                            </span>
                        </li>`;
                }
                return `
                    <li>
                        <span role="button" id="DrEdit" class="${create ? 'edit-button' : 'icon-disabled'} dropdown-item" ${create ? `data-datasync='${JSON.stringify(event)}'` : ''}>
                            <i class="cp-edit me-1"></i>Edit
                        </span>
                    </li>
                    <li>
                        <span role="button" id="dr_delete" class="${del ? 'delete-button' : 'icon-disabled'} dropdown-item"
                            ${del ? `data-siteid="${event.eventID}" data-sitename="${event.title}" data-bs-toggle="modal" data-bs-target="#DeleteModal"` : ''}>
                            <i class="cp-Delete me-1"></i>Delete
                        </span>
                    </li>`;
            };

            const eventCardHtml = `
                <div class="card w-100 ${bgColorClass}">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center justify-content-between">
                            <div style="width:180px;">
                                <div class="my-1 text-truncate">
                                    <span class="me-1">Activity:</span>
                                    <span class="list-title" title="${event.title}">${event.title}</span>
                                </div>
                                <div class="text-truncate text-secondary">
                                    <small class="text-black-50">Description:
                                        <span id="pfname" title="${event.description || 'NA'}">${event.description || 'NA'}</span>
                                    </small>
                                </div>
                            </div>
                            <div class="d-flex align-items-center flex-column gap-1">
                                <span>Start date: ${event.start.format("DD-MM-YYYY HH:mm")}</span>
                                <div class="small-circle bg-opacity-50 rounded-circle ms-2 d-none"></div>
                                <span class='pe-1'>End date: ${event.end ? event.end.format("DD-MM-YYYY HH:mm") : 'N/A'}</span>
                            </div>
                            <div class="dropdown">
                                <span class="cp-vertical-dots" role="button" data-bs-toggle="dropdown" aria-expanded="false"></span>
                                ${!isPastEvent ? `<ul class="dropdown-menu">${dropdownPermissions()}</ul>` : ''}
                            </div>
                        </div>
                    </div>
                </div>`;

            $('#Activitydata').append(eventCardHtml);
        }
    });

    if (!dataFound) {
        $('#Activitydata').append('<div class="alert alert-warning">No Activity.</div>');
    }

    $('#EditModal').modal('show');
}


function SaveEvent(data) {
    $.ajax({
        type: "POST",
        url: '/home/<USER>',
        success: function (data) {
            if (data.status) {
                FetchEventAndRenderCalendar();
                $('#myModalSave').modal('hide');
            }
        },
        error: function () {
            alert('FailedSaveEvent');
        }
    })
}

function CurrentAcitivity() {

    $.ajax({
        type: "GET",
        url: DrCalURL.DrCalURLPagination,
        success: function (data) {
            var c = document.getElementById("tilesContainer");
            if (data) {
                data?.data?.data?.sort((a, b) => {
                    const startDateA = new Date(a.scheduledStartDate);
                    const startDateB = new Date(b.scheduledStartDate);
                    const endDateA = new Date(a.scheduledEndDate);
                    const endDateB = new Date(b.scheduledEndDate);

                    // Compare start dates
                    if (startDateA - startDateB !== 0) {
                        return startDateA - startDateB;
                    } else {
                        return endDateA - endDateB;
                    }
                });

                var output = "";
                let j = 0;
                let validRecordFound = false;

                // Get today's date
                var today = new Date();
                today.setHours(0, 0, 0, 0);

                var day = String(today.getDate()).padStart(2, '0');
                var month = String(today.getMonth() + 1).padStart(2, '0');
                var year = today.getFullYear();

                var formattedDate = day + '-' + month + '-' + year;

                if (data?.data?.data?.length > 0) {

                    for (let i = 0; i < data?.data?.data?.length; i++) {
                        let date = new Date();
                        let currentdate = moment(date).format("DD-MM-YYYY HH:mm");

                        // Parse the date string using Date object
                        let datetimes = moment(data?.data?.data[i].scheduledStartDate).format("DD-MM-YYYY HH:mm");
                        var originalStartDate = new Date(data?.data?.data[i].scheduledStartDate);

                        let currentDateParts = currentdate.split(' ');
                        let currentMonthYear = currentDateParts[0].split('-').slice(1).join('-');

                        // Similarly extract month and year from datetimes
                        let datetimeParts = datetimes.split(' ');
                        let datetimeMonthYear = datetimeParts[0].split('-').slice(1).join('-');

                        if (originalStartDate >= today && currentdate < datetimes || currentMonthYear < datetimeMonthYear) {
                            validRecordFound = true;

                            // Format the date using jQuery
                            var formattedStartDate = $.datepicker.formatDate('dd-mm-yy', originalStartDate);
                            var starthrs = (originalStartDate.getHours() < 10 ? '0' : '') + originalStartDate.getHours();
                            var startmin = (originalStartDate.getMinutes() < 10 ? '0' : '') + originalStartDate.getMinutes();
                            var formattedStartTime = starthrs + ":" + startmin;
                            var originalEndDate = new Date(data?.data?.data[i].scheduledEndDate);
                            // Format the end date
                            var formattedEndDate = $.datepicker.formatDate('dd-mm-yy', originalEndDate);
                            var Endhrs = (originalEndDate.getHours() < 10 ? '0' : '') + originalEndDate.getHours();
                            var Endmin = (originalEndDate.getMinutes() < 10 ? '0' : '') + originalEndDate.getMinutes();
                            var formattedEndTime = Endhrs + ":" + Endmin;
                            // Combine date and time
                            // var resultEnddt = formattedStartDate;
                            var resultStartdt = formattedStartTime + " - " + formattedEndTime;
                            // Assign background color based on date
                            var background_color;
                            var background_circle;
                            if (formattedDate === formattedStartDate) {
                                background_color = 'light-success-bg';
                                background_circle = 'bg-success';
                            } else {
                                background_color = 'light-blue-bg';
                                background_circle = 'bg-primary';
                            }

                            // Generate the card output
                            output += '<div><div class="card w-100 ' + background_color + '">'
                                + '<div class="card-body p-2">'
                                + '<div class="d-flex align-items-center justify-content-between gap-3 ">'
                                + '  <div class="d-flex align-items-center gap-1"><div class="small-circle ' + background_circle + ' bg-opacity-50 rounded-circle d-inline-block">'
                                + ` </div><span>${formattedStartDate} to ${formattedEndDate}</span></div><div> <span>${resultStartdt}</span></div>`
                                + '  </div>'
                                + '<div class="my-1 text-truncate" style="max-width:70%;">'
                                + `<span class="me-1">Activity:</span> <span class="list-title" title="${data.data.data[i].activityName}"> ${data.data.data[i].activityName} </span>`
                                + '</div>'
                                + '<div class="my-1 text-truncate" style="max-width:280px;">'
                                + ' <small class="text-black-50">Operational Service : ' + data.data.data[i].businessServiceName + '</small>'
                                + '</div>'
                                + ' </div>'
                                + ' </div>'
                                + ' </div>';
                            j++;

                            if (j > 3) {

                                j = 0;
                            }
                            // Append the result to the container
                            c.innerHTML = output;
                        }
                    }
                    if (!validRecordFound) {
                        noRecord();
                    }
                } else {
                    noRecord();
                }
            }
        },
        error: function () {
            noRecord();
        }
    });
}

function noRecord() {
    var c = document.getElementById("tilesContainer");
    var output = "";
    output += '<div><div class="card w-100 light-warning-bg">'
        + '<div class="card-body p-2">'
        + '<div class="d-flex align-items-center justify-content-between gap-3 ">'
        + '  </div>'
        + '<div class="my-1">'
        + ' <span class="me-1" > Activity :</span ><span class="list-title">No Record Found. </span>'
        + '</div>'
        + ' </div>'
        + ' </div>'
        + ' </div>';

   // c.innerHTML = output;
};

// Profile 
async function GetProfileNamesId(id, profileData) {
    $("#drWorkFlowsProfile").empty();
    await $.ajax({
        type: "GET",
        url: RootUrl + DrCalURL.ProfileNames,
        data: { businessServiceId: id },
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {

                if (result?.data && result?.data?.length) {

                    const uniqueProfileNames = new Set();
                    const uniqueProfiles = [];
                    result?.data.forEach((s) => {
                        if (!uniqueProfileNames.has(s.profileName)) {

                            uniqueProfileNames.add(s.profileName);
                            uniqueProfiles.push(s);
                        }
                    });
                    uniqueProfiles.forEach((s) => {
                        $('#drWorkFlowsProfile').append(`<option value="${s.profileId}">${s.profileName}</option>`);
                    })
                }
                $("#drWorkFlowsProfile").val(profileData)
            } else {
                errorNotification(result)
            }
        }
    })
}

// Business Service
async function GetBusinessService() {
    await $.ajax({
        type: "GET",
        url: RootUrl + DrCalURL.BusinessService,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                if (result?.data && result?.data?.length) {
                    $('#infraBusinessServiceId').append('<option value=""></option>');

                    result.data.forEach((s) => {

                        $('#infraBusinessServiceId').append(`<option value=${s.id}>${s.name}</option>`);
                    })

                }
            } else {
                errorNotification(result)
            }
        }
    })
}

//  Responsibility
async function GetUserList(parseData) {
    $("#drUserName").empty();
    await $.ajax({
        type: "GET",
        url: RootUrl + DrCalURL.userList,
        data: {},
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                if (result?.data && result?.data?.length > 0) {

                    $('#drUserName').append('<option value=""></option>');
                    result.data.forEach(item => {
                        $('#drUserName').append('<option value="' + item.id + '">' + item.loginName + '</option>');
                    });
                }
                $("#drUserName").val(parseData)
            } else {

                errorNotification(result);
            }
        }
    })
}

//  Recipient 
async function GetRecipientUserList(selectedTexts, datass) {

    $('#drUserNameRecipient').empty()

    try {
        const result = await $.ajax({
            type: "GET",
            url: RootUrl + DrCalURL.userList,
            data: {},
            dataType: "json",
            traditional: true
        });

        if (result.success) {
            if (result?.data && result?.data?.length > 0) {
                const filteredData = result?.data.filter(item => !selectedTexts.includes(item.id));
                filteredData.forEach(item => {
                    $('#drUserNameRecipient').append('<option value="' + item.id + '">' + item.loginName + '</option>');
                });
            }
            $("#drUserNameRecipient").val(datass)
        } else {
            errorNotification(result);
        }
    } catch (error) {
        console.error("Error fetching user list:", error);
        // Handle the error appropriately
    }
}

function openAddForm() {
    $('#btnCreateDRCalendar').text("Save");
    Removeclass();
    $('#createDRCalendar').trigger("reset");
    $('#textrefId').val('');
    $('#myModalSave').modal('show');
}

function Removeclass() {
    $('#drWorkFlowsProfile').empty()
    const errorElements = ['#drActivityNameError', '#drScheduledEndDateError', '#setRemindersError', '#drScheduledStartDateError', '#drDescriptionError', '#drActivityTypeError', '#BusinessServiceId-error', '#drActivityStatusError', '#profilesError', '#drResponsibilityError', '#Recipient2Error', '#locationError'];
    errorElements.forEach(element => {
        $(element).text('').removeClass('field-validation-error');
    });
}

// Create
$("#create_btn").on('click', function () {
    Removeclass()
    openAddForm()
    $('#myModalSave').modal('show');
});

// Delete
$('#EditModal').on('click', '#dr_delete', function () {
    let drId = $(this).data('siteid');
    let drName = $(this).data('sitename');
    $('#deleteData').text(drName);
    $('#textDeleteId').val(drId);
});

// Edit
$('#EditModal').on('click', '#DrEdit', function () {
    let Drdata = $(this).data('datasync');
    $('#EditModal').modal('hide');
    openAddEditForm(Drdata);
    Removeclass()
})

// User Name 
$('#drUserName').on('change', function () {
    let value = $(this).val();
    $('#drUserName option:selected').each(function () {
        selectedTexts.push($(this).val());
    });
    let stringValue = JSON.stringify(value);
    $("#drResponsibilityData").val(stringValue);
    if (value.length === 0) {
        $('#drUserNameRecipient').empty()
    } else {
        GetRecipientUserList(selectedTexts);
    }
    Validation(value, 'Select recipient', 'drResponsibilityError');
});

// Location 
$('#location').on('input', async function () {
    const value = $(this).val();
    const sanitizedValue = value.replace(/[^a-zA-Z]/g, '');
    $(this).val(sanitizedValue);
    await Validation(sanitizedValue, 'Enter location', 'locationError');
});

// Recipient
$('#drUserNameRecipient').on('change', function () {
    let value = $(this).val();
    let filteredValues = value.filter(value => value !== '');
    if (filteredValues.length === 0) {
        $("#RecipientData").val('');
    } else {
        let stringValue = JSON.stringify(value);
        $("#RecipientData").val(stringValue);
    }
});

// Reminder
$('#drSetReminders').on('change', function () {
    let value = $(this).val();
    validateDates(startDate, endDate, value)
    Validation(value, 'Select reminder', 'setRemindersError');
});

// BusinessService
$('#infraBusinessServiceId').on('change', async function () {
    let value = $(this).val();
    Validation(value, 'Select operational service', 'BusinessServiceId-error');
    GetProfileNamesId(value)
});

// Description
$('#drDescription').on('change', function () {
    let value = $(this).val();
    Validation(value, 'Enter Description', 'drDescriptionError');
});

// Activity Type
$('#drActivityType').on('change', function () {
    let value = $(this).val();
    Validation(value, 'Select activity type', 'drActivityTypeError');
});

// Activity Status
$('#drActivityStatus').on('change', function () {
    let value = $(this).val();
    Validation(value, 'Select activity status', 'drActivityStatusError');
});

// WorkFlows Profile
$('#drWorkFlowsProfile').on('change', function () {
    let value = $(this).val();
    let filteredValue = value.filter(function (val) {
        return val !== '';
    });
    let stringValue = JSON.stringify(filteredValue);
    $("#ProfileData").val(stringValue);
    Validation(filteredValue, 'Select workflow profile', 'profilesError');
});

// Start Data 
$('#drScheduledStartDate').on('change', function () {
    const startDate = $(this).val();
    const endDate = $("#drScheduledEndDate").val();
    validateName(startDate, DrId, IsNameExist);
    validateDates(startDate, endDate);
});

// End Date
$('#drScheduledEndDate').on('change', function () {
    const endDate = $(this).val();
    const startDate = $("#drScheduledStartDate").val();
    validateDates(startDate, endDate);
});

// Remove Image
$("#removeImgss").on("click", function () {
    $("#textCompanyLogo").val('')
    $('#drUploadFileError').text('').removeClass('field-validation-error');
});

let fileInput = document.getElementById('textCompanyLogo');

$('#textCompanyLogo').on('change', async function () {
    let errorElement = $('#drUploadFileError');
    await FileValidation(fileInput, errorElement);
});

// Activity Name
$('#drActivityName').on('keyup', async function () {
    const value = $(this).val();
    nameDr = value
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    let errorElement = $('#drActivityNameError');
    await validateGroupName(value, "Enter activity name", errorElement)
})

function formatDate(date) {
    let year = date.getFullYear();
    let month = String(date.getMonth() + 1).padStart(2, '0');
    let day = String(date.getDate()).padStart(2, '0');
    let hours = String(date.getHours()).padStart(2, '0');
    let minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// Save
$("#btnCreateDRCalendar").on("click", async function () {
    let form = $("#createDRCalendar")
    let ActivityName = $('#drActivityName').val();
    elementDescription = $("#drDescription").val()
    errorElement = $('#drDescriptionError');
    const endDate = $("#drScheduledEndDate").val();
    const startDate = $("#drScheduledStartDate").val();
    var ActivityDetails = await validateDescription(elementDescription, "Should not allow more than 250 characters", errorElement);;
    let BusinessServiceId = $("#infraBusinessServiceId").val();
    let ActivityType = $("#drActivityType").val();
    let SetReminders = $("#drSetReminders").val();
    let ActivityStatus = $("#drActivityStatus").val();
    let WorkFlowsProfile = $("#drWorkFlowsProfile").val();
    let UserName = $("#drUserName").val();
    let location = $("#location").val();

    let errorElement2 = $('#drActivityNameError');
    let validateName = await validateGroupName(ActivityName, "Enter activity name", errorElement2);
    let validateLocation = await Validation(location, "Enter location", "locationError");
    let validateActivityType = await Validation(ActivityType, " Select activity type", "drActivityTypeError");
    let validateBusinessServiceId = await Validation(BusinessServiceId, " Select operational service", "BusinessServiceId-error");
    let isSetReminders = await Validation(SetReminders, "Select reminder", "setRemindersError");
    let validateActivityStatus = await Validation(ActivityStatus, " Select activity status", "drActivityStatusError");
    let validateWorkFlowsProfile = await validationArray(WorkFlowsProfile, "Select workflow profile", "profilesError");
    let validateUserName = await validationArray(UserName, " Select recipient", "drResponsibilityError");
    let fileInput = document.getElementById('textCompanyLogo');
    let fileErrorElement = $('#drUploadFileError');
    let isFileValid = true;
    if (fileInput.files.length > 0) {
        isFileValid = await FileValidation(fileInput, fileErrorElement);
    }
    let validateDatesResult = validateDates(startDate, endDate, SetReminders);

    if (validateName && isFileValid && validateLocation && ActivityDetails && isSetReminders && validateActivityType && validateBusinessServiceId && validateActivityStatus && validateWorkFlowsProfile && validateUserName && validateDatesResult) {
        $("#drStartDate").val($("#drScheduledStartDate").val());
        $("#drEndDate").val($("#drScheduledEndDate").val());
        form.trigger('submit');
    }

});

$('#btnEdit').on('click', function () {
    $('#btnCreateDRCalendar').text('Update').attr('title', 'Update');
});

// Description validate
async function validateDescription(value, errorMessage) {
    const errorElement = $('#drDescriptionError');
    if (!value) {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    } else if (value.length < 0) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    } else if (value.length > 250) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    else {
        errorElement.text('').removeClass('field-validation-error');
        return true;
    }
}

// validate GroupName 
async function validateGroupName(value, errorMsg, errorElement) {
    if (!value) {
        errorElement.text(errorMsg).addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await SpecialCharValidate(value),
        await ShouldNotBeginWithSpace(value),
        await ShouldNotBeginWithUnderScore(value),
        await OnlyNumericsValidate(value),
        await ShouldNotBeginWithNumber(value),
        await ShouldNotEndWithSpace(value),
        await ShouldNotAllowMultipleSpace(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
    ];
    return await CommonValidation(errorElement, validationResults);
}


// File Validation
async function FileValidation(fileInput, errorElement) {
    const file = fileInput.files[0];
    // Validate file type (png and pdf)
    const allowedExtensions = ['image/png', 'application/pdf'];
    if (!allowedExtensions.includes(file.type)) {
        $(errorElement).text("Only PNG and PDF files are allowed.").addClass('field-validation-error');
        return false;
    }
    // Validate file size (2MB max)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
        $(errorElement).text("File size should be less than 2MB.").addClass('field-validation-error');
        return false;
    }
    return true;
}

// Date validation
function validateDates(startDate, endDate, value) {

    const sdate = new Date(startDate);
    const edate = new Date(endDate);
    let todayDate = new Date();
    let adjustedDate = new Date();
    let formattedDate = formatDate(todayDate);
    let RemVal = value;
    let sdatetime = $('#drScheduledStartDate').val()
    // Define the reminder times
    let val15 = "15 minutes before";
    let val30 = "30 minutes before";
    let val45 = "45 minutes before";
    let val1hr = "1 hours before";
    let val2hr = "2 hours before";
    let val3hr = "3 hours before";
    let val1day = "1 day before";
    let valN = "Never";

    const reminderMap = {
        [val15]: 15,
        [val30]: 30,
        [val45]: 45,
        [val1hr]: 60,
        [val2hr]: 120,
        [val3hr]: 180,
        [val1day]: 1440,
    };

    clearDateError();

    if (!startDate && !endDate) {

        showError("drScheduledStartDateError", "Select start date");
        showError("drScheduledEndDateError", "Select end date");
        return false;

    } else if (!startDate && endDate) {
        showError("drScheduledStartDateError", "Select start date");
        return false;
    } else if (sdate < todayDate) {
        showError("drScheduledStartDateError", "Start date cannot be in the past");
        return false;
    }
    else if (sdate.getDate() === edate.getDate() &&
        sdate.getMonth() === edate.getMonth() &&
        sdate.getFullYear() === edate.getFullYear() &&
        sdate.getHours() === edate.getHours() &&
        sdate.getMinutes() === edate.getMinutes()) {

        showError("drScheduledEndDateError", "End date and time must be greater than start date and time");
        return false;
    }
    else if (startDate && !endDate) {
        showError("drScheduledEndDateError", "Select end date");
        return false;
    } else if (edate < todayDate) {

        showError("drScheduledEndDateError", "End date cannot be in the past");
        return false;
    }
    else if (edate < sdate) {
        showError("drScheduledEndDateError", "End date and time must be greater than start date and time");
        return false;
    }

    if (reminderMap.hasOwnProperty(RemVal)) {
        adjustedDate.setMinutes(adjustedDate.getMinutes() + reminderMap[RemVal]);
        adjustedDate = formatDate(adjustedDate);

        if (sdatetime <= adjustedDate) {
            showError("drScheduledStartDateError", `Select start date at least ${RemVal}`);
            return false;
        }
    } else if (RemVal === 'Never') {
        return true;
    }
    return true;
}

function clearDateError() {
    $("#drScheduledStartDateError, #drScheduledEndDateError").attr('hidden', true).text("");
}

function showError(elementId, errorMessage) {
    document.getElementById(elementId).removeAttribute('hidden');
    $("#" + elementId).text(errorMessage).addClass('field-validation-error');
}

// Validate Name
async function validateName(startDate, id = null) {
    const errorElement = $('#drActivityNameError');
    let url = RootUrl + DrCalURL.nameExistUrl;
    let data = {};
    data.activityName = nameDr;
    data.id = id;
    data.scheduleStartTime = startDate;
    const validationResults = [
        await IsNameExist(url, data, OnError)
    ];
    return await CommonValidation(errorElement, validationResults);
}
async function IsNameExist(url, data, errorFunc) {
    return !data?.activityName?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

// Validation 
function Validation(value, errorMessage, errorElement) {
    if (!value) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
};

// Validation
function validationArray(value, errorMessage, errorElement) {
    if (value.length == 0) {
        $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElement).text('').removeClass('field-validation-error');
        return true;
    }
};

// Edit Model
async function openAddEditForm(Drdata) {
    $("#textCompanyLogo, #ProfileData, #drResponsibilityData, #RecipientData").val('');
    $('#myModal').modal('hide');
    $('#myModalSave').modal('show');
    $('#btnCreateDRCalendar').text("Update");
    DrId = Drdata.eventID
    $("#ProfileData").val(Drdata.WorkflowProfiles);
    $("#drResponsibilityData").val(Drdata.Responsibility);
    GetRecipientUserList(JSON.parse(Drdata.Responsibility), JSON.parse(Drdata.RecipientTwo))
    $("#RecipientData").val(Drdata.RecipientTwo);
    $('#drDescription').val(Drdata.description);
    $('#infraBusinessServiceId').val(Drdata.BusinessServiceId);
    $('#drActivityType').val(Drdata.ActivityType);
    $('#drActivityStatus').val(Drdata.ActivityStatus);
    $("#drSetReminders").val(Drdata.SetReminders);
    $('#ddThemeColor').val(Drdata.color);
    $('#hdEventID').val(Drdata.eventID);
    $('#textrefId').val(Drdata.eventID);
    $('#drActivityName').val(Drdata.title);
    $('#location').val(Drdata.Location);
    let startDate = new Date(Drdata.start);
    let startYear = startDate.getFullYear();
    let startMonth = String(startDate.getMonth() + 1).padStart(2, '0');
    let startDay = String(startDate.getDate()).padStart(2, '0');
    let startHours = String(startDate.getHours()).padStart(2, '0');
    let startMinutes = String(startDate.getMinutes()).padStart(2, '0');
    await GetUserList(JSON.parse(Drdata.Responsibility))
    await GetProfileNamesId(Drdata.BusinessServiceId, JSON.parse(Drdata.WorkflowProfiles))
    let formattedStartDate = `${startYear}-${startMonth}-${startDay}T${startHours}:${startMinutes}`;
    $('#drScheduledStartDate').val(formattedStartDate);
    if (Drdata.end) {
        let endDate = new Date(Drdata.end);
        let endYear = endDate.getFullYear();
        let endMonth = String(endDate.getMonth() + 1).padStart(2, '0');
        let endDay = String(endDate.getDate()).padStart(2, '0');
        let endHours = String(endDate.getHours()).padStart(2, '0');
        let endMinutes = String(endDate.getMinutes()).padStart(2, '0');
        let formattedEndDate = `${endYear}-${endMonth}-${endDay}T${endHours}:${endMinutes}`;
        $('#drScheduledEndDate').val(formattedEndDate);
    } else {
        $('#drScheduledEndDate').val('');
    }
}

// start date
var datetimeInput = $('#drScheduledStartDate');
var minYear = new Date().getFullYear()
var maxYear = new Date().getFullYear() + 76;
var minDateString = minYear + "-01-01T00:00";
var maxDateString = maxYear + "-12-31T23:59";
datetimeInput.attr('min', minDateString);
datetimeInput.attr('max', maxDateString);
var today = new Date().toISOString().slice(0, 16);
//document.getElementsByName("sdate")[0].min = today;

// end date
var datetimeInput = $('#drScheduledEndDate');
var minYear = new Date().getFullYear()
var maxYear = new Date().getFullYear() + 76;
var minDateString = minYear + "-01-01T00:00";
var maxDateString = maxYear + "-12-31T23:59";
datetimeInput.attr('min', minDateString);
datetimeInput.attr('max', maxDateString);
var today = new Date().toISOString().slice(0, 16);
//document.getElementsByName("edate")[0].min = today;
