using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactTypeModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.FiaBia;

public class FiaImpactTypeService : BaseClient, IFiaImpactTypeService
{
    public FiaImpactTypeService(IConfiguration config, IAppCache cache, ILogger<FiaImpactTypeService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<FiaImpactTypeListVm>> GetFiaImpactTypeList()
    {
        var request = new RestRequest("api/v6/fiaimpacttypes");

        return await GetFromCache<List<FiaImpactTypeListVm>>(request, "GetFiaImpactTypeList");
    }

    public async Task<BaseResponse> CreateAsync(CreateFiaImpactTypeCommand createFiaImpactTypeCommand)
    {
        var request = new RestRequest("api/v6/fiaimpacttypes", Method.Post);

        request.AddJsonBody(createFiaImpactTypeCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateFiaImpactTypeCommand updateFiaImpactTypeCommand)
    {
        var request = new RestRequest("api/v6/fiaimpacttypes", Method.Put);

        request.AddJsonBody(updateFiaImpactTypeCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/fiaimpacttypes/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<FiaImpactTypeDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/fiaimpacttypes/{id}");

        return await Get<FiaImpactTypeDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsFiaImpactTypeNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/fiaimpacttypes/name-exist?fiaimpacttypeName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<FiaImpactTypeListVm>> GetPaginatedFiaImpactTypes(GetFiaImpactTypePaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/fiaimpacttypes/paginated-list");

      return await Get<PaginatedResult<FiaImpactTypeListVm>>(request);
  }
   #endregion
}
