﻿@using Newtonsoft.Json;

@model ContinuityPatrol.Domain.ViewModels.LicenseManagerModel.BaseLicenseViewModel
<div class="modal-dialog modal-xl modal-dialog-scrollabel modal-dialog-centered Organization_modal">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title">
                <i class="cp-derived-licence"></i><span>
                    Derived
                    License
                </span>
            </h6>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" title="Close"></button>
        </div>
        <div class="modal-body">
                <form id="derived-form" asp-area="Admin" asp-controller="LicenseManager" asp-action="DerivedLicenseCreateOrUpdate" method="post" class="tab-wizard wizard-circle wizard clearfix">
                    <div class="form-group">
                        <div class="form-label">Company</div>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="cp-company"></i>
                            </span>
                            <select asp-for="DerivedLicense.CompanyName" id="ddlCompanyName" class="form-select-modal" aria-label="Default select example" data-live-search="true">
                                <option value="" disabled selected>Select Company</option>
                                @foreach (var company in Model.Companies)
                                {
                                    <option id="@company.Value" value="@company.Text" data-companyid="@company.Value">@company.Text</option>
                                }
                            </select>

                            <input asp-for="DerivedLicense.CompanyId" name="DerivedLicense.CompanyId" type="hidden" autocomplete="off" id="textCompanyId" class="form-control" />

                            <input asp-for="DerivedLicense.IsState" name="DerivedLicense.IsState" type="hidden" autocomplete="off" id="textstate" class="form-control" />
                        </div>
                        <span asp-validation-for="DerivedLicense.CompanyName" id="Company-error"></span>
                    </div>
                    <table class="table" style="table-layout:fixed;">
                        <thead>
                            <tr>
                                <th></th>
                                <th>License Pool</th>
                                <th>Derived License</th>
                            </tr>
                        </thead>
                                            
                        <tbody>

                            <tr>
                                <td><i class="cp-application me-1"></i>Application Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseApplication"></span><span class="fs-6 fw-semibold" id="applicadata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="Application Count" id="derivedApplication" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="application-error"></span>
                                    </div>
                                </td>

                            </tr>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center gap-2"><i class="cp-database me-1"></i><span>Database Count</span><i class="cp-circle-plus text-primary" role="button" data-bs-toggle="collapse" data-bs-target=".multi-collapse" role="button" aria-expanded="false" aria-controls="collapseExample"></i></div>
                                <div class="collapse multi-collapse mt-2" id="collapseExample">
                                    <div class="card mb-0">
                                        <div class="card-body pb-0" style="max-height: 220px; overflow-y: auto;">
                                            <div class="row">
                                              
                                                <div class="col dbdatas">
                                                  
                                                
                                                </div>
                              
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                                <td><span class="fs-6 fw-semibold" id="baseDB"></span><span class="fs-6 fw-semibold" id="dbdata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                        <input placeholder="Database Count" id="derivedDB" autocomplete="off" class="form-control" value=""  />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="db-error"></span>
                                    </div>
                                <div style="max-height:220px;overflow-y:auto">

                                <div id=" collapseExample" class="collapse multi-collapse databasecountdetail" ></div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-replication-on me-1"></i>Replication Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseReplica"></span><span class="fs-6 fw-semibold" id="replicadata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="Replication Count" id="derivedReplica" autocomplete="off" class="form-control"  />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="replica-error"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-network me-1"></i>Network Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseNetwork"></span><span class="fs-6 fw-semibold" id="networkdata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="Network Count" id="derivedNetwork" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="network-error"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-storage me-1"></i>Storage Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseStorage"></span><span class="fs-6 fw-semibold" id="storagedata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="Storage Count" id="derivedStorage" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="storage-error"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-virtualization me-1"></i>Virtualization Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseVirtual"></span><span class="fs-6 fw-semibold" id="virtualdata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="Virtualization Count" id="derivedVirtual" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="virtual-error"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-api me-1"></i>ThirdParty Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseThird"></span><span class="fs-6 fw-semibold" id="thirddata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="ThirdParty Count" id="derivedThirdParty" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="thirdParty-error"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="cp-DNS me-1"></i>DNS Count</td>
                                <td><span class="fs-6 fw-semibold" id="baseDns"></span><span class="fs-6 fw-semibold" id="dnsdata"></span></td>
                                <td>
                                    <div class="form-group mb-0">
                                        <div class="input-group">
                                            <input placeholder="DNS Count" id="derivedDns" autocomplete="off" class="form-control" value="" />
                                        </div>
                                        <span asp-validation-for="DerivedLicense.Properties" id="dns-error"></span>
                                    </div>
                                </td>
                            </tr>                            
                        </tbody>                        
                    </table>                
                    <input asp-for="DerivedLicense.CommonBaseLicenseUpdateCommand.Id" type="hidden" id="parentId" />
                    <input asp-for="DerivedLicense.CommonBaseLicenseUpdateCommand.LicenseKey" type="hidden" id="licenseKey" />
                    <input asp-for="Id" type="hidden" id="Id" />
                    <input asp-for="DerivedLicense.Id" type="hidden" id="derivedId" />
                    <input asp-for="DerivedLicense.LicenseKey" type="hidden" id="derivedLicenseKey" />
                    <input asp-for="DerivedLicense.PONumber" type="hidden" id="derivedPONumber" />
                    <input asp-for="DerivedLicense.CommonBaseLicenseUpdateCommand.Properties" type="hidden" id="baseproperties" />
                    <input asp-for="DerivedLicense.Properties" type="hidden" id="derivedproperties" />
                </form>
        </div>
        <div class="modal-footer d-flex justify-content-between align-items-center">
            <small class="text-secondary">
                <i class="cp-note me-1"></i>Note: All fields are mandatory
                except optional
            </small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button id="derivedSave" class="btn btn-primary btn-sm" href="javascript:void(0)">Save</button>
            </div>
        </div>
    </div>
</div>

<script src="~/js/Admin/License/License Manager/derivedLicense.js"></script>
