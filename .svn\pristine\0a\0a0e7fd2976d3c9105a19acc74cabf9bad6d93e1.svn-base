async function MSSQLAlwaysOnAvailabilityGroup() {
    //MSSQLAlways-On AvailabilityGroup
    let _options = [];
    let serverRoleObject = "";
    let serverTypeObject = "";
    try {

        await $.ajax({
            type: "GET",
            url: RootUrl + 'Configuration/Server/GetServerRole',
            dataType: "json",
            success: function (result) {
                if (result.success) {
                    serverRoleObject = result.data.find(item => item.name.toLowerCase() === "database");
                } else {
                    errorNotification(result)
                }
            },
        });

        //await $.ajax({
        //    type: "GET",
        //    url: RootUrl + 'Configuration/Server/GetServerType',
        //    dataType: "json",
        //    data: { id: serverRoleObject?.id },
        //    success: function (result) {
        //        if (result.success) {
        //            serverTypeObject = result.data.find(item => item.name.toLowerCase() === "prdbserver" || item.name.toLowerCase() === "drdbserver");
        //        } else {
        //            errorNotification(result);
        //        }
        //    },
        //});

        const result = await $.ajax({
            url: RootUrl + "Configuration/Server/GetServerNames",
            method: 'GET',
            dataType: 'json',
            data: { 'roleType': serverRoleObject?.id || "", 'serverType': "" }
        });

        if (result.success) {
            $.each(result?.data, function (index, item) {
                let props = "";
                if (item.properties) {
                    props = JSON.parse(item.properties);
                }

                let normalizedProps = {};
                for (let key in props) {
                    normalizedProps[key.toLowerCase()] = props[key];
                }               

                _options.push({
                    value: item.id,
                    text: item.name,
                    ipaddress: normalizedProps["ipaddress"] || "NA",
                    connectviahost: normalizedProps["connectviahostname"],
                    hostname: normalizedProps["hostname"] || "NA",
                });
            });
        } else {
            errorNotification(result);
        }
    } catch (error) {
        console.error("Error fetching server names:", error);
    }

    let availabilityGroupDiv =
        `<div class='availabilityGroupType f-field-group'>
                <div class="mb-3 form-group">
                <div class="form-label">Server</div>
                <div class="">                                            
                    <select name="ListenerServerId" class='form-select-modal-replication' data-live-search="true"
                            data-placeholder="Select Server" id="availabilityGroupServer">
                        <option value=""></option>
                    </select>
                </div>
                <span asp-validation-for="FormName" id="availabilityGroupServerServerError"></span>
                </div>

                <div class="mb-3 form-group">
                <div class="form-label">IP Address</div>
                <div class="input-group">                              
                    <input name="IPAddress" disabled id="availabilityGroupIP" type="text" class="form-control"
                        placeholder="Enter IP Address">
                </div>
                     <span id="availabilityGroupIPError"></span>
                </div>

                <div class="mb-3 form-group d-none" id="hostName">
                <div class="form-label">Host</div>
                <div class="input-group">
                    <input name="Port" disabled id="availabilityGroupHost" type="text" class="form-control"
                        placeholder="Enter Host">
                </div>
                     <span id="availabilityGroupHostError"></span>
                </div>               
        </div>`;

    if ($('.formeo-column').length) {
        $('.formeo-column:first').prepend(availabilityGroupDiv);
        setTimeout(() => {
            _options.forEach(option => {
                $("#availabilityGroupServer").append(
                    `<option value="${option.value}"
                        data-ipaddress="${option.ipaddress}"
                        data-connectviahost="${option.connectviahost}"
                        data-hostname="${option.hostname}">
                        ${option.text}
                    </option>`
                );
            });

            setTimeout(() => {
                let selectElements = document.querySelectorAll('.form-select-modal-replication');
                selectElements.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.select2({
                        dropdownParent: this1.find('.modal-content'),
                        placeholder: $this.attr('placeholder')
                    });
                });

                $('.form-select-modal-replication').next('.select2-container').css('width', '100%');

                let disableSelectTagTitle = document.querySelectorAll('.select2-selection__rendered');
                disableSelectTagTitle?.forEach(async function (selectElement) {
                    let $this = $(selectElement);
                    $this.attr('title', '');
                });
            }, 500);
        }, 200)
    }
}

//Sybase Type onChange
$(document).on("change", "#availabilityGroupServer", function () {
    let selectedOption = $(this).find(":selected");
    let ipaddress = selectedOption.data("ipaddress");
    let host = selectedOption.data("connectviahost");
    let hostname = selectedOption.data("hostname");

    if (host) {
        $("#hostName").removeClass("d-none");
    } else {
        $("#hostName").addClass("d-none");
    }

    $('#availabilityGroupIP').val(ipaddress);
    $('#availabilityGroupHost').val(hostname);
});