﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceEvaluation.Queries;

public class GetBusinessServiceEvaluationDetailQueryHandlerTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly Mock<IBusinessServiceEvaluationRepository> _mockBusinessServiceEvaluationRepository;
    private readonly GetBusinessServiceEvaluationDetailQueryHandler _handler;

    public GetBusinessServiceEvaluationDetailQueryHandlerTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.GetBusinessServiceEvaluationRepository(_businessServiceEvaluationFixture.BusinessServiceEvaluations);

        _handler = new GetBusinessServiceEvaluationDetailQueryHandler(_businessServiceEvaluationFixture.Mapper, _mockBusinessServiceEvaluationRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_BusinessServiceEvaluationDetails_When_ValidId()
    {
        var result = await _handler.Handle(new GetBusinessServiceEvaluationDetailQuery { Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType<BusinessServiceEvaluationDetailVm>();
        result.Id.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId);
        result.BusinessServiceId.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceId);
        result.BusinessServiceName.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].BusinessServiceName);
        result.Description.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Description);
        result.Grade.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].Grade);
        result.GradeValue.ShouldBe(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].GradeValue);
    }

    [Fact]
    public async Task Handle_Throw_NotFoundException_When_InvalidBusinessServiceEvaluationId()
    {
        var exceptionDetails = await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new GetBusinessServiceEvaluationDetailQuery { Id = int.MaxValue.ToString() }, CancellationToken.None));
        exceptionDetails.Message.ShouldContain("Not Found");
    }

    [Fact]
    public async Task Handle_Call_GetByReferenceIdAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetBusinessServiceEvaluationDetailQuery { Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.GetBusinessServiceEvaluationByReferenceIdAsync(It.IsAny<string>()), Times.Once);
    }
}