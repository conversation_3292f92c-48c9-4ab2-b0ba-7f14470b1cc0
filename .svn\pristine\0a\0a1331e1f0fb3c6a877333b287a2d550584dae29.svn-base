using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class OracleRACMonitorStatusFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "OracleRAC";

    public List<OracleRACMonitorStatus> OracleRACMonitorStatusPaginationList { get; set; }
    public List<OracleRACMonitorStatus> OracleRACMonitorStatusList { get; set; }
    public OracleRACMonitorStatus OracleRACMonitorStatusDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public OracleRACMonitorStatusFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<OracleRACMonitorStatus>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow));

        OracleRACMonitorStatusPaginationList = _fixture.CreateMany<OracleRACMonitorStatus>(20).ToList();
        OracleRACMonitorStatusList = _fixture.CreateMany<OracleRACMonitorStatus>(5).ToList();
        OracleRACMonitorStatusDto = _fixture.Create<OracleRACMonitorStatus>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public OracleRACMonitorStatus CreateOracleRACMonitorStatusWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<OracleRACMonitorStatus>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)
            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public OracleRACMonitorStatus CreateOracleRACMonitorStatusWithWhitespace()
    {
        return CreateOracleRACMonitorStatusWithProperties(type: "  OracleRAC  ");
    }

    public List<OracleRACMonitorStatus> CreateMultipleOracleRACMonitorStatusWithSameType(string type, int count)
    {
        var statuses = new List<OracleRACMonitorStatus>();
        for (int i = 0; i < count; i++)
        {
            statuses.Add(CreateOracleRACMonitorStatusWithProperties(type: type, isActive: true));
        }
        return statuses;
    }

    public OracleRACMonitorStatus CreateOracleRACMonitorStatusWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateOracleRACMonitorStatusWithProperties(infraObjectId: infraObjectId);
    }

    public List<OracleRACMonitorStatus> CreateOracleRACMonitorStatusWithDateRange(DateTime startDate, DateTime endDate, string infraObjectId = null)
    {
        var statuses = new List<OracleRACMonitorStatus>();
        var currentDate = startDate;
        
        while (currentDate <= endDate)
        {
            statuses.Add(CreateOracleRACMonitorStatusWithProperties(
                infraObjectId: infraObjectId ?? InfraObjectId,
                createdDate: currentDate,
                isActive: true));
            currentDate = currentDate.AddDays(1);
        }
        
        return statuses;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
