﻿using ContinuityPatrol.Application.Features.DataSetColumns.Event.Delete;

namespace ContinuityPatrol.Application.Features.DataSetColumns.Commands.Delete;

public class
    DeleteDataSetColumnsCommandHandler : IRequestHandler<DeleteDataSetColumnsCommand, DeleteDataSetColumnsResponse>
{
    private readonly IDataSetColumnsRepository _dataSetColumnsRepository;
    private readonly IPublisher _publisher;

    public DeleteDataSetColumnsCommandHandler(IDataSetColumnsRepository dataSetColumnsRepository, IPublisher publisher)
    {
        _dataSetColumnsRepository = dataSetColumnsRepository;
        _publisher = publisher;
    }

    public async Task<DeleteDataSetColumnsResponse> Handle(DeleteDataSetColumnsCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _dataSetColumnsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.DataSetColumns),
            new NotFoundException(nameof(DataSetColumns), request.Id));

        eventToDelete.IsActive = false;

        await _dataSetColumnsRepository.UpdateAsync(eventToDelete);

        var response = new DeleteDataSetColumnsResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.DataSetColumns), eventToDelete.TableName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new DataSetColumnsDeletedEvent { TableName = eventToDelete.TableName },
            cancellationToken);

        return response;
    }
}