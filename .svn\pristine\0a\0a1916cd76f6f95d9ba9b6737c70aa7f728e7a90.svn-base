﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Application.Features.Workflow.Events.CreateWorkflowExecutionEvent;

public class WorkflowExecutionCreatedEventHandler : INotificationHandler<WorkflowExecutionCreatedEvent>
{
    private readonly ILogger<WorkflowExecutionCreatedEventHandler> _logger;
    private readonly ILoggedInUserService _userService;
    private readonly IWorkflowExecutionEventLogRepository _workflowExecutionEventLogRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public WorkflowExecutionCreatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<WorkflowExecutionCreatedEventHandler> logger,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowExecutionEventLogRepository workflowExecutionEventLogRepository,
        IWorkflowOperationRepository workflowOperationRepository)
    {
        _userService = loggedInUserService;
        _logger = logger;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowExecutionEventLogRepository = workflowExecutionEventLogRepository;
        _workflowOperationRepository = workflowOperationRepository;
    }

    public async Task Handle(WorkflowExecutionCreatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var workflowOperationRunningStatus = await _workflowOperationRepository.GetWorkflowOperationByRunningStatus();

        foreach (var workflowOperation in workflowOperationRunningStatus)
        {
            var workflowOperationGroupList =
                await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(workflowOperation
                    .ReferenceId);

            var workflowOperationGroupDetail =
                workflowOperationGroupList.FirstOrDefault(x => x.WorkflowId.Equals(updatedEvent.WorkflowId));

            if (workflowOperationGroupDetail != null)
            {
                var result = new Domain.Entities.WorkflowExecutionEventLog
                {
                    LoginName = _userService.LoginName,
                    WorkflowActionName = string.Empty,
                    CompanyId = _userService.CompanyId,
                    Status = "",
                    UserEvent = "WorkflowModified",
                    Message = "",
                    WorkflowOperationId = workflowOperationGroupDetail.WorkflowOperationId,
                    WorkflowOperationGroupId = workflowOperationGroupDetail.ReferenceId,
                    InfraObjectId = workflowOperationGroupDetail.InfraObjectId,
                    ActionId = "",
                    ConditionActionId = 0,
                    SkipStep = false,
                    IsReload = 0,
                    Direction = ""
                };
                await _workflowExecutionEventLogRepository.AddAsync(result);
            }
        }

        _logger.LogInformation($"Workflow '{updatedEvent.WorkflowId}' updated successfully.");
    }
}