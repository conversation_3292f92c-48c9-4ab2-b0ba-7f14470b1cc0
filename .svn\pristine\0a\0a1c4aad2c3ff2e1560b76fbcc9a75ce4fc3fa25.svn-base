﻿using ContinuityPatrol.Application.Features.BusinessServiceEvaluation.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BusinessServiceEvaluation.Commands;

public class DeleteBusinessServiceEvaluationTests : IClassFixture<BusinessServiceEvaluationFixture>
{
    private readonly BusinessServiceEvaluationFixture _businessServiceEvaluationFixture;
    private readonly Mock<IBusinessServiceEvaluationRepository> _mockBusinessServiceEvaluationRepository;
    private readonly DeleteBusinessServiceEvaluationCommandHandler _handler;

    public DeleteBusinessServiceEvaluationTests(BusinessServiceEvaluationFixture businessServiceEvaluationFixture)
    {
        _businessServiceEvaluationFixture = businessServiceEvaluationFixture;

        _mockBusinessServiceEvaluationRepository = new Mock<IBusinessServiceEvaluationRepository>();

        _mockBusinessServiceEvaluationRepository = BusinessServiceEvaluationRepositoryMocks.DeleteBusinessServiceEvaluationRepository(_businessServiceEvaluationFixture.BusinessServiceEvaluations);

        _handler = new DeleteBusinessServiceEvaluationCommandHandler(_mockBusinessServiceEvaluationRepository.Object);

        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].IsActive = true;
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_BusinessServiceEvaluationDeleted()
    {
        var validGuid = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId = validGuid;

        _mockBusinessServiceEvaluationRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0]);

        var result = await _handler.Handle(new DeleteBusinessServiceEvaluationCommand { Id = validGuid }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();

        _mockBusinessServiceEvaluationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_DeleteBusinessServiceEvaluationResponse_When_BusinessServiceEvaluationDeleted()
    {
        var validGuid = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId = validGuid;

        _mockBusinessServiceEvaluationRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0]);

        var result = await _handler.Handle(new DeleteBusinessServiceEvaluationCommand { Id = validGuid }, CancellationToken.None);
        result.ShouldBeOfType(typeof(DeleteBusinessServiceEvaluationResponse));
        result.Success.ShouldBeTrue();
        result.IsActive.ShouldBeFalse();
        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
        _mockBusinessServiceEvaluationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_BusinessServiceEvaluationDeleted()
    {
        var validGuid = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId = validGuid;

        _mockBusinessServiceEvaluationRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0]);

        var result = await _handler.Handle(new DeleteBusinessServiceEvaluationCommand { Id = validGuid }, CancellationToken.None);

        var businessServiceEvaluation = await _mockBusinessServiceEvaluationRepository.Object.GetByReferenceIdAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId);

        businessServiceEvaluation.IsActive.ShouldBeFalse();

        _mockBusinessServiceEvaluationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidBusinessServiceEvaluationId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteBusinessServiceEvaluationCommand { Id = invalidGuid }, CancellationToken.None));
    }
    
    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId;

        _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId = validGuid;

        _mockBusinessServiceEvaluationRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_businessServiceEvaluationFixture.BusinessServiceEvaluations[0]);

        var result = await _handler.Handle(new DeleteBusinessServiceEvaluationCommand { Id = _businessServiceEvaluationFixture.BusinessServiceEvaluations[0].ReferenceId }, CancellationToken.None);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockBusinessServiceEvaluationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BusinessServiceEvaluation>()), Times.Once);
    }
}