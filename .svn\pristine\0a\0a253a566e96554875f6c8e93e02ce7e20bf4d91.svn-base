﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class RpForVmCGMonitorLogsRepository:BaseRepository<RpForVmCGMonitorLogs>, IRpForVmCGMonitorLogsRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    public RpForVmCGMonitorLogsRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService):base(dbContext,loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
   
} 