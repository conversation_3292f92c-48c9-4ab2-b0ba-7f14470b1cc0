using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberAirGapStatus.Events.Update;

public class CyberAirGapStatusUpdatedEventHandler : INotificationHandler<CyberAirGapStatusUpdatedEvent>
{
    private readonly ILogger<CyberAirGapStatusUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberAirGapStatusUpdatedEventHandler(ILoggedInUserService loggedInUserService,
        ILogger<CyberAirGapStatusUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = loggedInUserService;
    }

    public async Task Handle(CyberAirGapStatusUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} CyberAirGapStatus",
            Entity = "CyberAirGapStatus",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"CyberAirGapStatus '{updatedEvent.Name}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"CyberAirGapStatus '{updatedEvent.Name}' updated successfully.");
    }
}