﻿using ContinuityPatrol.Application.Features.IncidentManagement.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.IncidentManagement.Validators;

public class UpdateIncidentManagementValidatorTests : IClassFixture<IncidentManagementFixture>
{
    private readonly Mock<IIncidentManagementRepository> _mockIncidentManagementRepository;

    private readonly IncidentManagementFixture _incidentManagementFixture;

    public UpdateIncidentManagementValidatorTests(IncidentManagementFixture incidentManagementFixture)
    {
        _incidentManagementFixture = incidentManagementFixture;

        var incidentManagements = new Fixture().Create<List<Domain.Entities.IncidentManagement>>();

        _mockIncidentManagementRepository = IncidentManagementRepositoryMocks.UpdateIncidentManagementRepository(incidentManagements);
    }

    [Fact]
    public void Constructor_ShouldInitializeWithRepository()
    {
        // Arrange & Act
        var validator = new UpdateIncidentManagementCommandValidator(_mockIncidentManagementRepository.Object);

        // Assert
        validator.ShouldNotBeNull();
    }

    [Theory]
    [AutoIncidentManagementData]
    public async Task Verify_Update_ValidCommand_ShouldPass(UpdateIncidentManagementCommand updateIncidentManagementCommand)
    {
        // Arrange
        var validator = new UpdateIncidentManagementCommandValidator(_mockIncidentManagementRepository.Object);

        updateIncidentManagementCommand.Id = Guid.NewGuid().ToString();
        updateIncidentManagementCommand.IncidentName = "TestIncident";
        updateIncidentManagementCommand.IncidentCode = "INC-001";
        updateIncidentManagementCommand.Status = 1;
        updateIncidentManagementCommand.InfraId = Guid.NewGuid().ToString();
        updateIncidentManagementCommand.InfraComponentId = Guid.NewGuid().ToString();
        updateIncidentManagementCommand.InfraComponentType = "Server";
        updateIncidentManagementCommand.SourceId = Guid.NewGuid().ToString();
        updateIncidentManagementCommand.SourceTypId = Guid.NewGuid().ToString();
        updateIncidentManagementCommand.IncidentComment = "Test Comment";
        updateIncidentManagementCommand.AppProcess = "Test Process";
        updateIncidentManagementCommand.JobName = "Test Job";

        // Act
        var validateResult = await validator.ValidateAsync(updateIncidentManagementCommand, CancellationToken.None);

        // Assert
        validateResult.IsValid.ShouldBeTrue();
    }

    [Theory]
    [AutoIncidentManagementData]
    public async Task Verify_Update_EmptyCommand_ShouldPass(UpdateIncidentManagementCommand updateIncidentManagementCommand)
    {
        // Arrange
        var validator = new UpdateIncidentManagementCommandValidator(_mockIncidentManagementRepository.Object);

        // Act
        var validateResult = await validator.ValidateAsync(updateIncidentManagementCommand, CancellationToken.None);

        // Assert - Since the validator has no rules, it should pass even with empty/default values
        validateResult.IsValid.ShouldBeTrue();
    }

}