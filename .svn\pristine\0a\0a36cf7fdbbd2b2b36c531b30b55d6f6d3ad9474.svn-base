﻿using ContinuityPatrol.Application.Features.Database.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Events;

public class CreateDatabaseEventTests : IClassFixture<DatabaseFixture>, IClassFixture<UserActivityFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly DatabaseCreatedEventHandler _handler;

    public CreateDatabaseEventTests(DatabaseFixture databaseFixture, UserActivityFixture userActivityFixture)
    {
        _databaseFixture = databaseFixture;

        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockDatabaseEventLogger = new Mock<ILogger<DatabaseCreatedEventHandler>>();

        _mockUserActivityRepository = DatabaseRepositoryMocks.CreateDatabaseEventRepository(_userActivityFixture.UserActivities);

        _handler = new DatabaseCreatedEventHandler(mockLoggedInUserService.Object, mockDatabaseEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CreateDatabaseEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_databaseFixture.DatabaseCreatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_databaseFixture.DatabaseCreatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}