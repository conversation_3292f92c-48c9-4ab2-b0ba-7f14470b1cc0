﻿namespace ContinuityPatrol.Application.Features.InfraObjectScheduler.Queries.GetNames;

public class
    GetInfraObjectSchedulerNameQueryHandler : IRequestHandler<GetInfraObjectSchedulerNameQuery,
        List<InfraObjectSchedulerNameVm>>
{
    private readonly IInfraObjectSchedulerRepository _infraObjectSchedulerRepository;
    private readonly IMapper _mapper;

    public GetInfraObjectSchedulerNameQueryHandler(IInfraObjectSchedulerRepository infraObjectSchedulerRepository,
        IMapper mapper)
    {
        _infraObjectSchedulerRepository = infraObjectSchedulerRepository;
        _mapper = mapper;
    }

    public async Task<List<InfraObjectSchedulerNameVm>> Handle(GetInfraObjectSchedulerNameQuery request,
        CancellationToken cancellationToken)
    {
        var infraObjectSchedulers = await _infraObjectSchedulerRepository.GetInfraObjectSchedulerNames();

        var infraObjectSchedulerDto = _mapper.Map<List<InfraObjectSchedulerNameVm>>(infraObjectSchedulers);

        return infraObjectSchedulerDto;
    }
}