﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.TeamMaster.Events.Create;

public class TeamMasterCreatedEventHandler : INotificationHandler<TeamMasterCreatedEvent>
{
    private readonly ILogger<TeamMasterCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public TeamMasterCreatedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<TeamMasterCreatedEventHandler> logger)
    {
        _userService = userService;
        _userActivityRepository = userActivityRepository;
        _logger = logger;
    }

    public async Task Handle(TeamMasterCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.TeamMaster.ToString(),
            Action = $"{ActivityType.Create} {Modules.TeamMaster}",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $" Team master '{createdEvent.GroupName}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Team master '{createdEvent.GroupName}' created successfully.");
    }
}