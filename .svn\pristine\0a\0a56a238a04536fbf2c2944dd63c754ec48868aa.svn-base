//using ContinuityPatrol.Application.Features.BiaImpact.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Delete;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetBiaRulesByEntityId;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetList;
using ContinuityPatrol.Application.Features.BiaRules.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Database.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class BiaRulesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<BiaRulesListVm>>> GetBiaImpacts()
    {
        Logger.LogDebug("Get All BiaImpacts");

        return Ok(await Mediator.Send(new GetBiaRulesListQuery()));
    }

    [HttpGet("{id}", Name = "GetBiaImpact")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<BiaRulesDetailVm>> GetBiaImpactById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BiaImpact Id");

        Logger.LogDebug($"Get BiaImpact Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetBiaRulesDetailQuery { Id = id }));
    }
    #region Paginated
    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<BiaRulesListVm>>> GetPaginatedBiaImpacts([FromQuery] GetBiaRulesPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in BiaImpact Paginated List");

        return Ok(await Mediator.Send(query));
    }
    #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateBiaRulesResponse>> CreateBiaImpact([FromBody] CreateBiaRulesCommand createBiaImpactCommand)
    {
        Logger.LogDebug($"Create BiaImpact '{createBiaImpactCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateBiaImpact), await Mediator.Send(createBiaImpactCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateBiaRulesResponse>> UpdateBiaImpact([FromBody] UpdateBiaRulesCommand updateBiaImpactCommand)
    {
        Logger.LogDebug($"Update BiaImpact '{updateBiaImpactCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateBiaImpactCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteBiaRulesResponse>> DeleteBiaImpact(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "BiaImpact Id");

        Logger.LogDebug($"Delete BiaImpact Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteBiaRulesCommand { Id = id }));
    }
    [Route("entityId"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<BiaRulesListVm>> GetBiaRulesByEntityIdAndType(string entityId,string type)
    {
        Logger.LogDebug($"Get BiaRules Details by EntityId '{entityId}' and Type '{type}'");

        return Ok(await Mediator.Send(new GetBiaRulesByEntityIdQuery { EntityId = entityId, Type = type }));
    }
    #region NameExist

    // [Route("name-exist"), HttpGet]
    // public async Task<ActionResult> IsBiaImpactNameExist(string biaImpactName, string? id)
    // {
    //     Guard.Against.NullOrWhiteSpace(BiaImpactName, "BiaImpact Name");
    //
    //     Logger.LogDebug($"Check Name Exists Detail by BiaImpact Name '{biaImpactName}' and Id '{id}'");
    //
    //     return Ok(await Mediator.Send(new GetBiaImpactNameUniqueQuery { Name = BiaImpactName, Id = id }));
    // }
    #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


