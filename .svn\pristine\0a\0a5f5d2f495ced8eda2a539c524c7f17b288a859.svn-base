using ContinuityPatrol.Application.Features.Rto.Events.Update;

namespace ContinuityPatrol.Application.Features.Rto.Commands.Update;

public class UpdateRtoCommandHandler : IRequestHandler<UpdateRtoCommand, UpdateRtoResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IRtoRepository _rtoRepository;

    public UpdateRtoCommandHandler(IMapper mapper, IRtoRepository rtoRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _rtoRepository = rtoRepository;
        _publisher = publisher;
    }

    public async Task<UpdateRtoResponse> Handle(UpdateRtoCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _rtoRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Rto), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateRtoCommand), typeof(Domain.Entities.Rto));

        await _rtoRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateRtoResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Rto), eventToUpdate.BusinessServiceName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new RtoUpdatedEvent { Name = eventToUpdate.BusinessServiceName }, cancellationToken);

        return response;
    }
}