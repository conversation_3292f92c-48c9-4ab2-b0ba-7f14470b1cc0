﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.HacmpCluster.Events.PaginatedView;

public class HacmpClusterPaginatedViewEventHandler : INotificationHandler<HacmpClusterPaginatedViewEvent>
{
    private readonly ILogger<HacmpClusterPaginatedViewEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public HacmpClusterPaginatedViewEventHandler(ILogger<HacmpClusterPaginatedViewEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(HacmpClusterPaginatedViewEvent paginatedViewEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = "HACMPCluster",
            Action = $"{ActivityType.View} HACMPCluster",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "HACMP Cluster viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("HACMP Cluster viewed");
    }
}