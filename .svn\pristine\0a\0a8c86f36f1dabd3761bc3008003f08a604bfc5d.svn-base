﻿namespace ContinuityPatrol.Application.Features.Server.Commands.SaveAll;

public class SaveAllServerCommand : IRequest<SaveAllServerResponse>
{
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public List<SaveAllServerListCommand> ServerList { get; set; }
}

public class SaveAllServerListCommand
{
    public string Name { get; set; }
    public string SiteId { get; set; }
    public string SiteName { get; set; }
    public string ServerTypeId { get; set; }
    public string ServerType { get; set; }
    public string Logo { get; set; }
    public string Properties { get; set; }
    public string LicenseId { get; set; }
    public string LicenseKey { get; set; }
}