using AutoFixture;
using ContinuityPatrol.Application.Features.TemplateHistory.Queries.GetTemplateHistoryByTemplateId;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class TemplateHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<TemplateHistory> TemplateHistories { get; set; }
    public List<TemplateHistory> InvalidTemplateHistories { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<TemplateHistoryByIdVm> TemplateHistoryByIdVm { get; }

    // Queries
    public GetTemplateHistoryByTemplateIdQuery GetTemplateHistoryByTemplateIdQuery { get; set; }

    public TemplateHistoryFixture()
    {
        try
        {
            // Create test data using AutoFixture
            TemplateHistories = AutoTemplateHistoryFixture.Create<List<TemplateHistory>>();
            InvalidTemplateHistories = AutoTemplateHistoryFixture.Create<List<TemplateHistory>>();
            UserActivities = AutoTemplateHistoryFixture.Create<List<UserActivity>>();

            // Set invalid template histories to inactive
            foreach (var invalidTemplateHistory in InvalidTemplateHistories)
            {
                invalidTemplateHistory.IsActive = false;
            }

            // Queries
            GetTemplateHistoryByTemplateIdQuery = AutoTemplateHistoryFixture.Create<GetTemplateHistoryByTemplateIdQuery>();

            // Set query IDs to match existing entities
            if (TemplateHistories.Any())
            {
                GetTemplateHistoryByTemplateIdQuery.TemplateId = TemplateHistories.First().TemplateId;
            }
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            TemplateHistories = new List<TemplateHistory>();
            InvalidTemplateHistories = new List<TemplateHistory>();
            UserActivities = new List<UserActivity>();
            GetTemplateHistoryByTemplateIdQuery = new GetTemplateHistoryByTemplateIdQuery();
        }

        // Configure View Models
        TemplateHistoryByIdVm = new List<TemplateHistoryByIdVm>
        {
            new TemplateHistoryByIdVm
            {
                Id = "TH_001",
                CompanyId = "COMPANY_001",
                LoginName = "admin",
                TemplateId = "TMPL_001",
                TemplateName = "SQL Server Backup Template",
                Description = "Automated SQL Server backup template with compression and verification",
                Type = "Backup",
                ActionType = "Database",
                Version = "1.0",
                Icon = "database-backup-icon.png",
                Properties = @"{
                    ""backupType"": ""Full"",
                    ""compression"": true,
                    ""encryption"": false,
                    ""retentionDays"": 30,
                    ""verifyBackup"": true,
                    ""checksumEnabled"": true,
                    ""backupLocation"": ""\\\\backup-server\\sql-backups"",
                    ""scheduleType"": ""Daily"",
                    ""scheduleTime"": ""02:00""
                }",
                ReplicationTypeId = "REP_001",
                ReplicationTypeName = "SQL Server Replication",
                UpdaterId = "USER_001",
                Comments = "Initial template creation with standard backup settings"
            },
            new TemplateHistoryByIdVm
            {
                Id = "TH_002",
                CompanyId = "COMPANY_001",
                LoginName = "admin",
                TemplateId = "TMPL_001",
                TemplateName = "SQL Server Backup Template",
                Description = "Updated SQL Server backup template with enhanced security",
                Type = "Backup",
                ActionType = "Database",
                Version = "1.1",
                Icon = "database-backup-icon.png",
                Properties = @"{
                    ""backupType"": ""Full"",
                    ""compression"": true,
                    ""encryption"": true,
                    ""retentionDays"": 30,
                    ""verifyBackup"": true,
                    ""checksumEnabled"": true,
                    ""backupLocation"": ""\\\\backup-server\\sql-backups"",
                    ""scheduleType"": ""Daily"",
                    ""scheduleTime"": ""02:00"",
                    ""encryptionAlgorithm"": ""AES_256""
                }",
                ReplicationTypeId = "REP_001",
                ReplicationTypeName = "SQL Server Replication",
                UpdaterId = "USER_001",
                Comments = "Added encryption support for enhanced security"
            },
            new TemplateHistoryByIdVm
            {
                Id = "TH_003",
                CompanyId = "COMPANY_001",
                LoginName = "manager",
                TemplateId = "TMPL_002",
                TemplateName = "Exchange Mailbox Backup Template",
                Description = "Exchange mailbox backup template with incremental support",
                Type = "Backup",
                ActionType = "Email",
                Version = "1.0",
                Icon = "email-backup-icon.png",
                Properties = @"{
                    ""backupType"": ""Incremental"",
                    ""compression"": true,
                    ""encryption"": true,
                    ""retentionDays"": 90,
                    ""mailboxSelection"": ""All"",
                    ""backupLocation"": ""\\\\backup-server\\exchange-backups"",
                    ""scheduleType"": ""Hourly"",
                    ""scheduleInterval"": 4
                }",
                ReplicationTypeId = "REP_002",
                ReplicationTypeName = "Exchange Replication",
                UpdaterId = "USER_002",
                Comments = "Initial Exchange backup template for all mailboxes"
            },
            new TemplateHistoryByIdVm
            {
                Id = "TH_004",
                CompanyId = "COMPANY_001",
                LoginName = "operator",
                TemplateId = "TMPL_003",
                TemplateName = "File Server Replication Template",
                Description = "Continuous file server replication template",
                Type = "Replication",
                ActionType = "FileSystem",
                Version = "2.0",
                Icon = "file-replication-icon.png",
                Properties = @"{
                    ""replicationType"": ""Continuous"",
                    ""bandwidth"": ""100MB"",
                    ""compression"": true,
                    ""encryption"": true,
                    ""sourceLocation"": ""\\\\file-server\\data"",
                    ""targetLocation"": ""\\\\dr-server\\data"",
                    ""filterExtensions"": ["".tmp"", "".log""],
                    ""monitoringInterval"": 30
                }",
                ReplicationTypeId = "REP_003",
                ReplicationTypeName = "File System Replication",
                UpdaterId = "USER_003",
                Comments = "Updated to version 2.0 with improved monitoring"
            },
            new TemplateHistoryByIdVm
            {
                Id = "TH_005",
                CompanyId = "COMPANY_001",
                LoginName = "admin",
                TemplateId = "TMPL_004",
                TemplateName = "VM Snapshot Template",
                Description = "Virtual machine snapshot template with memory capture",
                Type = "Snapshot",
                ActionType = "VirtualMachine",
                Version = "1.2",
                Icon = "vm-snapshot-icon.png",
                Properties = @"{
                    ""snapshotType"": ""Daily"",
                    ""retention"": 7,
                    ""quiesceVM"": true,
                    ""memory"": true,
                    ""scheduleTime"": ""23:00"",
                    ""vmSelection"": ""Critical"",
                    ""storageLocation"": ""\\\\storage\\vm-snapshots"",
                    ""compressionLevel"": ""Medium""
                }",
                ReplicationTypeId = "REP_004",
                ReplicationTypeName = "VM Replication",
                UpdaterId = "USER_001",
                Comments = "Enhanced snapshot template with memory capture for critical VMs"
            }
        };

        // Configure AutoMapper for TemplateHistory mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<TemplateHistoryProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoTemplateHistoryFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize TemplateHistory entity
            fixture.Customize<TemplateHistory>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.CompanyId, "COMPANY_001")
                .With(b => b.LoginName, "testuser")
                .With(b => b.TemplateId, "TMPL_001")
                .With(b => b.TemplateName, "Test Template")
                .With(b => b.Description, "Test template description")
                .With(b => b.Type, "Backup")
                .With(b => b.ActionType, "Database")
                .With(b => b.Version, "1.0")
                .With(b => b.Icon, "test-icon.png")
                .With(b => b.Properties, "{\"test\": true}")
                .With(b => b.ReplicationTypeId, "REP_001")
                .With(b => b.ReplicationTypeName, "Test Replication")
                .With(b => b.UpdaterId, "USER_001")
                .With(b => b.Comments, "Test comments"));

            // Customize GetTemplateHistoryByTemplateIdQuery
            fixture.Customize<GetTemplateHistoryByTemplateIdQuery>(c => c
                .With(b => b.TemplateId, "TMPL_001"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "TemplateHistory")
                .With(b => b.ActivityType, "View"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
