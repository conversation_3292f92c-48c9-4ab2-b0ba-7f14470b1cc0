﻿//using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;
//using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
//using ContinuityPatrol.Shared.Core.Wrapper;

//namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapStatus.Queries;

//public class GetHeatMapStatusPaginatedListQueryHandlerTests : IClassFixture<HeatMapStatusFixture>
//{
//    private readonly HeatMapStatusFixture _heatMapStatusFixture;

//    private readonly Mock<IHeatMapStatusViewRepository> _mockHeatMapStatusViewRepository;

//    private readonly GetHeatMapStatusPaginatedListQueryHandler _handler;

//    public GetHeatMapStatusPaginatedListQueryHandlerTests(HeatMapStatusFixture heatMapStatusFixture)
//    {
//        _heatMapStatusFixture = heatMapStatusFixture;

//        _mockHeatMapStatusViewRepository = HeatMapStatusRepositoryMocks.GetPaginatedHeatMapStatusRepository(_heatMapStatusFixture.);

//        _handler = new GetHeatMapStatusPaginatedListQueryHandler(_heatMapStatusFixture.Mapper, _mockHeatMapStatusViewRepository.Object);

//        _heatMapStatusFixture.HeatMapStatusList[0].BusinessServiceName = "BS_Test_01";
//        _heatMapStatusFixture.HeatMapStatusList[0].BusinessFunctionName= "BF_Test_01";
//        _heatMapStatusFixture.HeatMapStatusList[0].InfraObjectName = "IO_Test_01";
//        _heatMapStatusFixture.HeatMapStatusList[0].HeatmapType= "Server";
        
//        _heatMapStatusFixture.HeatMapStatusList[1].BusinessServiceName = "BS_Test_02";
//        _heatMapStatusFixture.HeatMapStatusList[1].BusinessFunctionName= "BF_Test_02";
//        _heatMapStatusFixture.HeatMapStatusList[1].InfraObjectName = "IO_Test_02";
//        _heatMapStatusFixture.HeatMapStatusList[1].HeatmapType= "Database";
//    }

//    [Fact]
//    public async Task Handle_Return_TotalPage_ShouldRequested()
//    {
//        var result = await _handler.Handle(new GetHeatMapStatusPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapStatusListVm>>();

//        result.TotalCount.ShouldBe(3);

//        result.TotalPages.ShouldBe(1);
//    }

//    [Fact]
//    public async Task Handle_Return_HeatMapStatusList_With_MultipleQueryStringParameter()
//    {
//        var result = await _handler.Handle(new GetHeatMapStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "BusinessServiceName=BS_Test_01;businessFunctionName=BF_Test_01;infraObjectName=IO_Test_01;HeatMapType=Server" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapStatusListVm>>();

//        result.TotalCount.ShouldBe(1);

//        result.Data[0].BusinessServiceName.ShouldBe("BS_Test_01");

//        result.Data[0].BusinessFunctionName.ShouldBe("BF_Test_01");

//        result.Data[0].InfraObjectName.ShouldBe("IO_Test_01");

//        //result.Data[0].HeatmapType.ShouldBe("Server");
//    }

//    [Fact]
//    public async Task Handle_Return_PaginatedHeatMapStatusList_When_QueryStringMatch()
//    {
//        var result = await _handler.Handle(new GetHeatMapStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Server" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapStatusListVm>>();

//        result.TotalCount.ShouldBe(1);

//        result.Data[0].ShouldBeOfType<HeatMapStatusListVm>();

//        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

//        result.Data[0].BusinessServiceName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessServiceName);

//        result.Data[0].BusinessFunctionName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].BusinessFunctionName);

//        result.Data[0].InfraObjectName.ShouldBe(_heatMapStatusFixture.HeatMapStatusList[0].InfraObjectName);

//        //result.Data[0].HeatmapType.ShouldBe("Server");
//    }

//    [Fact]
//    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
//    {
//        var result = await _handler.Handle(new GetHeatMapStatusPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABC" }, CancellationToken.None);

//        result.ShouldBeOfType<PaginatedResult<HeatMapStatusListVm>>();

//        result.TotalCount.ShouldBe(0);
//    }

//    [Fact]
//    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(new GetHeatMapStatusPaginatedListQuery(), CancellationToken.None);

//        _mockHeatMapStatusViewRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
//    }
//}