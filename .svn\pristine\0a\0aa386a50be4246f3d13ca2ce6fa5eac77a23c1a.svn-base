﻿using ContinuityPatrol.Application.Features.TableAccess.Event.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Events
{
    public class DeleteTableAccessEventTests
    {
        private readonly Mock<ILogger<TableAccessDeletedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TableAccessDeletedEventHandler _handler;

        public DeleteTableAccessEventTests()
        {
            _mockLogger = new Mock<ILogger<TableAccessDeletedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TableAccessDeletedEventHandler(
                _mockUserService.Object,
                _mockLogger.Object,
                _mockUserActivityRepository.Object);
        }

        [Fact]
        public async Task Handle_ValidEvent_LogsInformationAndAddsUserActivity()
        {
            var tableAccessDeletedEvent = new TableAccessDeletedEvent
            {
                TableName = "TestTable"
            };

            _mockUserService.Setup(service => service.UserId).Returns("TestUserId");
            _mockUserService.Setup(service => service.LoginName).Returns("TestUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(service => service.CompanyId).Returns("TestCompanyId");
            _mockUserService.Setup(service => service.IpAddress).Returns("127.0.0.1");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(tableAccessDeletedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "TestUserId" &&
                activity.LoginName == "TestUser" &&
                activity.RequestUrl == "http://example.com" &&
                activity.CompanyId == "TestCompanyId" &&
                activity.HostAddress == "127.0.0.1" &&
                activity.Action == "Delete TableAccess" &&
                activity.Entity == "TableAccess" &&
                activity.ActivityType == "Delete" &&
                activity.ActivityDetails == "TableAccess 'TestTable' deleted successfully."
            )), Times.Once);

    
        }



        [Fact]
        public async Task Handle_ValidEvent_CallsAddAsyncOnce()
        {
            var tableAccessDeletedEvent = new TableAccessDeletedEvent
            {
                TableName = "AnotherTestTable"
            };

            _mockUserService.Setup(service => service.UserId).Returns("AnotherUserId");
            _mockUserService.Setup(service => service.LoginName).Returns("AnotherUser");
            _mockUserService.Setup(service => service.RequestedUrl).Returns("http://example.com");
            _mockUserService.Setup(service => service.CompanyId).Returns("AnotherCompanyId");
            _mockUserService.Setup(service => service.IpAddress).Returns("***********");

            var cancellationToken = CancellationToken.None;

            await _handler.Handle(tableAccessDeletedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }
    }
}
