using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Create;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Commands.Update;
using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetDetail;
//using ContinuityPatrol.Application.Features.DriftResourceSummary.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftResourceSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IDriftResourceSummaryService
{
    Task<List<DriftResourceSummaryListVm>> GetDriftResourceSummaryList();
    Task<BaseResponse> CreateAsync(CreateDriftResourceSummaryCommand createDriftResourceSummaryCommand);
    Task<BaseResponse> UpdateAsync(UpdateDriftResourceSummaryCommand updateDriftResourceSummaryCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<DriftResourceSummaryDetailVm> GetByReferenceId(string id);
    #region NameExist
   // Task<bool> IsDriftResourceSummaryNameExist(string name, string? id);
   #endregion
    #region Paginated
   // Task<PaginatedResult<DriftResourceSummaryListVm>> GetPaginatedDriftResourceSummarys(GetDriftResourceSummaryPaginatedListQuery query);
    #endregion
}
