﻿using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Queries;

public class GetVeritasClusterNameUniqueQueryHandlerTests :IClassFixture<VeritasClusterFixture>
{
    private readonly VeritasClusterFixture _veritasClusterFixture;
    private Mock<IVeritasClusterRepository> _mockVeritasClusterRepository;
    private readonly GetVeritasClusterNameUniqueQueryHandler _handler;

    public GetVeritasClusterNameUniqueQueryHandlerTests(VeritasClusterFixture veritasClusterFixture)
    {
        _veritasClusterFixture = veritasClusterFixture;
        _mockVeritasClusterRepository = VeritasClusterRepositoryMock.GetVeritasClusterNameUniqueRepository(_veritasClusterFixture.VeritasClusters);
        _handler = new GetVeritasClusterNameUniqueQueryHandler(_mockVeritasClusterRepository.Object);
    }


    [Fact]
    public async Task Handle_Return_True_VeritasClusterName_Exist()
    {
        _veritasClusterFixture.VeritasClusters[0].ClusterName = "PRS";
        _veritasClusterFixture.VeritasClusters[0].IsActive = true;

        var result = await _handler.Handle(new GetVeritasClusterNameUniqueQuery { Name = _veritasClusterFixture.VeritasClusters[0].ClusterName, Id = _veritasClusterFixture.VeritasClusters[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_VeritasClusterNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetVeritasClusterNameUniqueQuery { Name = "Virtusa", Id = 1.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_VeritasClusterName_NotMatch()
    {
        var result = await _handler.Handle(new GetVeritasClusterNameUniqueQuery { Name = "SEP", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OneTime()
    {
        await _handler.Handle(new GetVeritasClusterNameUniqueQuery(), CancellationToken.None);

        _mockVeritasClusterRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockVeritasClusterRepository = VeritasClusterRepositoryMock.GetVeritasClusterEmptyRepository();

        var result = await _handler.Handle(new GetVeritasClusterNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}