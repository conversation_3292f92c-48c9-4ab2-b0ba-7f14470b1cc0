using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Create;
using ContinuityPatrol.Application.Features.SingleSignOn.Commands.Update;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.SingleSignOnModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class SingleSignOnFixture
{
    public List<SingleSignOnListVm> SingleSignOnListVm { get; }
    public PaginatedResult<SingleSignOnListVm> PaginatedSingleSignOnListVm { get; }
    public SingleSignOnDetailVm SingleSignOnDetailVm { get; }
    public CreateSingleSignOnCommand CreateSingleSignOnCommand { get; }
    public UpdateSingleSignOnCommand UpdateSingleSignOnCommand { get; }
    public GetSingleSignOnPaginatedListQuery GetSingleSignOnPaginatedListQuery { get; }
    public List<SingleSignOnTypeVm> SingleSignOnTypeVm { get; }
    public List<SingleSignOnNameVm> SingleSignOnNameVm { get; }

    public SingleSignOnFixture()
    {
        var fixture = new Fixture();

        SingleSignOnListVm = fixture.Create<List<SingleSignOnListVm>>();
        PaginatedSingleSignOnListVm = fixture.Create<PaginatedResult<SingleSignOnListVm>>();
        SingleSignOnDetailVm = fixture.Create<SingleSignOnDetailVm>();
        CreateSingleSignOnCommand = fixture.Create<CreateSingleSignOnCommand>();
        UpdateSingleSignOnCommand = fixture.Create<UpdateSingleSignOnCommand>();
        GetSingleSignOnPaginatedListQuery = fixture.Create<GetSingleSignOnPaginatedListQuery>();
        SingleSignOnTypeVm = fixture.Create<List<SingleSignOnTypeVm>>();
        SingleSignOnNameVm = fixture.Create<List<SingleSignOnNameVm>>();
    }

    public void Dispose()
    {

    }
}
