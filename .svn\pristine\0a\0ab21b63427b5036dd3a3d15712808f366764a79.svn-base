﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.Create;
using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateDrOperation;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateStateToUnlock;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationCategoryType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessFunctionId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByBusinessServiceId;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectByDrReady;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetInfraObjectDetailById;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class InfraObjectService : BaseClient, IInfraObjectService
{
    public InfraObjectService(IConfiguration config, IAppCache cacheService, ILogger<InfraObjectService> logger)
        : base(config, cacheService, logger)
    {
    }
  
    public async Task<List<InfraObjectListVm>> GetInfraObjectList()
    {
        var request = new RestRequest("api/v6/infraobjects");

        return await Get<List<InfraObjectListVm>>(request);
    }

    public async Task<bool> IsInfraObjectNameExist(string infraObjectName, string id)
    {
        var request = new RestRequest($"api/v6/infraobjects/name-exist?infraObjectName={infraObjectName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateInfraObjectCommand createInfraObjectCommand)
    {
        var request = new RestRequest("api/v6/infraobjects", Method.Post);

        request.AddJsonBody(createInfraObjectCommand);

        return await Post<BaseResponse>(request);
    }

    public async  Task<BaseResponse> UpdateAsync(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var request = new RestRequest("api/v6/infraobjects", Method.Put);

        request.AddJsonBody(updateInfraObjectCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/infraobjects/{infraObjectId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async  Task<DrReadyCount> GetInfraObjectDrReady()
    {
        var request = new RestRequest("api/v6/infraobjects");

        return await Get<DrReadyCount>(request);
    }

    public async Task<InfraObjectDetailVm> GetInfraObjectById(string infraObjectId)
    {
       var request = new RestRequest($"api/v6/infraobjects/{infraObjectId}");

        return await Get<InfraObjectDetailVm>(request);
    }

    public async Task<List<GetInfraObjectByBusinessServiceIdVm>> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        var request = new RestRequest($"api/v6/infraobjects/{businessServiceId}");

        return await Get<List<GetInfraObjectByBusinessServiceIdVm>>(request);
    }

    public  async Task<BaseResponse> UpdateInfraObjectState(UpdateInfraObjectStateCommand updateInfraObjectStateCommand)
    {
        var request = new RestRequest("api/v6/infraobjects/state", Method.Put);

        request.AddJsonBody(updateInfraObjectStateCommand);

        return await Put<BaseResponse>(request);
    }
    public async Task<BaseResponse> UpdateInfraObjectStateUnlock(UpdateStateToUnlockCommand updateStateToUnlockCommand)
    {
        var request = new RestRequest("api/v6/infraobjects/unlockstate", Method.Put);

        request.AddJsonBody(updateStateToUnlockCommand);

        return await Put<BaseResponse>(request);
    }
  


    public async Task<PaginatedResult<InfraObjectListVm>> GetPaginatedInfraObjects(GetInfraObjectPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/infraobjects/paginated-list{query}");

        return await Get<PaginatedResult<InfraObjectListVm>>(request);
    }
    public async Task<List<GetInfraObjectNameVm>> GetInfraObjectNames()
    {
        var request = new RestRequest("api/v6/infraobjects/names");

        return await Get<List<GetInfraObjectNameVm>>(request);
    }

    public async Task<GetInfraObjectDetailByIdVm> GetInfraObjectDetailsById(string infraObjectId)
    {
        var request = new RestRequest($"api/v6/infraobjects/it-view-solution-diagram?infraObjectId={infraObjectId}");

        return await Get<GetInfraObjectDetailByIdVm>(request);
    }

    public Task<List<GetInfraObjectByBusinessServiceIdVm>> GetInfraObjectByBusinessServiceName(string businessServiceName)
    {
        throw new NotImplementedException();
    }

    public async Task<List<InfraObjectListByReplicationCategoryTypeVm>> GetInfraObjectListByReplicationCategoryType(string replicationCategoryTypeId)
    {
        var request = new RestRequest($"api/v6/infraobjects/replicationcategorytypeid?replicationCategoryTypeId={replicationCategoryTypeId}");

        return await Get<List<InfraObjectListByReplicationCategoryTypeVm>>(request);

    }
    public async Task<List<InfraObjectListByReplicationTypeVm>> GetInfraObjectListByReplicationTypeId(string replicationTypeId)
    {
        var request = new RestRequest("api/v6/infraobjects/by/replicationtype");

        return await Get<List<InfraObjectListByReplicationTypeVm>>(request);
    }

    public async Task<List<GetInfraObjectByBusinessFunctionIdVm>> GetInfraObjectByBusinessFunctionId(string businessFunctionId)
    {
        var request = new RestRequest($"api/v6/infraobjects/by/{businessFunctionId}");

        return await Get<List<GetInfraObjectByBusinessFunctionIdVm>>(request);
    }

    public async Task<UpdateInfraObjectDrOperationResponse> UpdateDrOperationStatus(UpdateInfraObjectDrOperationCommand command)
    {
        var request = new RestRequest("api/v6/infraobjects/dr-operation-status", Method.Put);

        request.AddJsonBody(command);

        return await Put<UpdateInfraObjectDrOperationResponse>(request);
    }
}