﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.SVCMssqlMonitorLog.Commands;
using ContinuityPatrol.Domain.ViewModels.SVCGMMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract
{
    public interface ISVCGMMonitorLogService
    {
        Task<BaseResponse> CreateAsync(CreateSVCMssqlMonitorLogCommand createSVCMssqlMonitorLogCommand);

        Task<List<SVCGMMonitorLogsListVm>> GetAllSVCGMMonitorLog();

        Task<SVCGMMonitorLogDetailVm> GetSVCGMMonitorLogById(string id);

        Task<PaginatedResult<SVCGMMonitorLogsListVm>> GetPaginatedSVCGMMonitorLog(GetSVCGMMonitorLogPaginatedListQuery query);

        Task<List<SVCGMMonitorLogDetailByTypeVm>> GetSVCGMMonitorLogByType(string type);
    }
}
