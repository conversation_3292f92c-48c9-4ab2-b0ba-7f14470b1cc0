﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class AboutCpRepositoryMocks
{
    public static Mock<IAboutCpRepository> GetAboutCpRepository(List<AboutCp> aboutCps)
    {
        var aboutCpRepository = new Mock<IAboutCpRepository>();

        aboutCpRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(aboutCps);

        return aboutCpRepository;
    }

    public static Mock<IConfiguration> GetConfigurationRepository()
    {
        var configurationRepository = new Mock<IConfiguration>();

        var mockSection = new Mock<IConfigurationSection>();

        mockSection.Setup(x => x.Value).Returns("6.0");

        configurationRepository.Setup(config => config.GetSection("CP:Version")).Returns(mockSection.Object);

        return configurationRepository;
    }
    
    public static Mock<IAboutCpRepository> GetAboutCpEmptyRepository()
    {
        var aboutCpEmptyRepository = new Mock<IAboutCpRepository>();

        aboutCpEmptyRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<AboutCp>());

        return aboutCpEmptyRepository;
    }
}