﻿using ContinuityPatrol.Application.Features.BusinessFunction.Events.DashboardViewEvent.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Events.Update;

namespace ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;

public class
    UpdateBusinessFunctionCommandHandler : IRequestHandler<UpdateBusinessFunctionCommand,
        UpdateBusinessFunctionResponse>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;

    public UpdateBusinessFunctionCommandHandler(IMapper mapper, IPublisher publisher,
        IBusinessFunctionRepository businessFunctionRepository, IInfraObjectViewRepository infraObjectViewRepository)
    {
        _mapper = mapper;
        _publisher = publisher;
        _businessFunctionRepository = businessFunctionRepository;
        _infraObjectViewRepository = infraObjectViewRepository;
    }

    public async Task<UpdateBusinessFunctionResponse> Handle(UpdateBusinessFunctionCommand request,
        CancellationToken cancellationToken)
    {
        var infraObjects = await _infraObjectViewRepository.GetInfraObjectByBusinessFunctionId(request.Id);

        var eventToUpdate = await _businessFunctionRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.BusinessFunction), request.Id);

        //if (infraObjects.Count > 0)
        //    throw new InvalidException($"The Operational service '{eventToUpdate.BusinessServiceName}' already mapped with InfraObject.");

        _mapper.Map(request, eventToUpdate, typeof(UpdateBusinessFunctionCommand),
            typeof(Domain.Entities.BusinessFunction));

        await _businessFunctionRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateBusinessFunctionResponse
        {
            Message = Message.Update("Operational Function", eventToUpdate.Name),

            BusinessFunctionId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new BusinessFunctionDashboardViewUpdatedEvent
        {
            BusinessServiceId = eventToUpdate.BusinessServiceId,
            BusinessServiceName = eventToUpdate.BusinessServiceName,
            BusinessFunctionId = eventToUpdate.ReferenceId,
            BusinessFunctionName = eventToUpdate.Name,
            ConfiguredRPO = eventToUpdate.ConfiguredRPO,
            ConfiguredRTO = eventToUpdate.ConfiguredRTO,
            RPOThreshold = eventToUpdate.RPOThreshold
        }, cancellationToken);


        await _publisher.Publish(new BusinessFunctionUpdatedEvent { Name = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}