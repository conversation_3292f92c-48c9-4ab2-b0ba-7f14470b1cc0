﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IRoboCopyJobService
{
    Task<List<RoboCopyJobListVm>> GetRoboCopyJobs();
    Task<RoboCopyJobDetailVm> GetRoboCopyJobById(string id);
    Task<PaginatedResult<RoboCopyJobListVm>> GetPaginatedRoboCopyJobs(GetRoboCopyJobPaginatedQuery query);
    Task<BaseResponse> CreateRoboCopyJob( CreateRoboCopyJobCommand createRoboCopyJobCommand);
    Task<BaseResponse> UpdateRoboCopyJob(UpdateRoboCopyJobCommand updateRoboCopyJobCommand);
    Task<BaseResponse> DeleteRoboCopyJob(string id);
    
}
