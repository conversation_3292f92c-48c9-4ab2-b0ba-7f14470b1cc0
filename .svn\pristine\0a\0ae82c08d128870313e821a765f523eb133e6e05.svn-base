﻿//GET
const infraGetRequest = async (url) => {
    const response = await $.ajax({
        type: "GET",
        url: url,
        dataType: "json"
    });

    if (response?.success) {
        return response?.data;
    } else {
        errorNotification(response);
    }
};

//GET with data 
const infraGetRequestWithData = async (url, data) => {
    const response = await $.ajax({
        type: "GET",
        url: url,
        data: data,
        dataType: "json"
    });

    if (response?.success) {
        return response?.data;
    } else {
        errorNotification(response);
    }
};

//POST 
const infraPostRequest = async (url, data) => {
    const response = await $.ajax({
        type: "POST",
        url: url,
        data: data,
        dataType: "json"
    });

    if (response?.success) {
        return response;
    } else {
        errorNotification(response);
    }
};

//CreateOrUpdate
const infraCreateOrUpdate = async (url, data) => {
    const response = await $.ajax({
        type: "POST",
        url: url,
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: data,
        contentType: false,
        processData: false,
    });

    return response;
};

//Delete
const infraDeleteData = async (url, data) => {
    const response = await $.ajax({
        type: "DELETE",
        url: url,
        headers: {
            'RequestVerificationToken': await gettoken()
        },
        data: data,
        contentType: false,
        processData: false,
    });

    return response;
};

//server, DB, Replication was slow because while delete it's check all dependency.
const showLoaderAndDisableButton = (loaderSelector, buttonElement) => {
    $(loaderSelector).removeClass('d-none').show();
    $(buttonElement).prop('disabled', true);
};

const hideLoaderAndEnableButton = (loaderSelector, buttonElement) => {
    $(loaderSelector).addClass('d-none').hide();
    $(buttonElement).prop('disabled', false);
};

const infraPreventSpecialKeys = (selector) => {
    $(selector).on('keypress', (e) => {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
        }
    });
};

//Name already exists server, Database, Replication, SSO, Rsync....
async function IsNameExist(url, data, errorFunc, dataname) {
    return !data[dataname]?.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}

async function GetAsync(url, data, errorFunc) {
    return await $.get(url, data).fail(errorFunc);
}

const InfraNameValidation = async (value, id = null, nameExistURL, errorelement, nullerror, specialcharactererror, dataname) => {
    if (!value) {
        errorelement.text(nullerror).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorelement.text(specialcharactererror).addClass('field-validation-error');
        return false;
    }
    const url = RootUrl + nameExistURL;
    let data = { 'Id': id, [dataname]: value };
    const validationResults = [
        await SpecialCharValidateCustom(value), //SpecialCharValidate(value),
        await ShouldNotBeginWithUnderScore(value),
        await ShouldNotBeginWithSpace(value),
        await OnlyNumericsValidate(value),
        (dataname?.toLowerCase() == 'servername' || dataname?.toLowerCase() == 'databasename' || dataname?.toLowerCase() == 'replicationname') ? true : await ShouldNotBeginWithNumber(value),
        await SpaceWithUnderScore(value),
        await ShouldNotEndWithUnderScore(value),
        await ShouldNotEndWithSpace(value),
        await MultiUnderScoreRegex(value),
        await SpaceAndUnderScoreRegex(value),
        await minMaxlength(value),
        await secondChar(value),
        await IsNameExist(url, data, OnError, dataname)
    ];
    return await CommonValidation(errorelement, validationResults);
};

//server, DB Test Connection
const testConnectionStatusOff = ($testConnectionButton, $testConnectionAll, $testConnection) => {
    $testConnectionButton.removeClass("d-none");
    $testConnectionAll
        .addClass("btn btn-primary")
        .removeClass("bg-white btn-outline-secondary")
        .attr("test-status", "on");
    $testConnection
        .addClass('text-success')
        .attr("test-status", "off")
        .trigger('click');   
}

const testConnectionStatuson = ($testConnectionButton, $testConnectionAll, $testConnection) => {
    $testConnectionButton.addClass("d-none");
    $testConnectionAll
        .addClass("bg-white btn-outline-secondary")
        .removeClass("btn btn-primary")
        .attr("test-status", "off");
    $testConnection
        .removeClass('text-success')
        .attr("test-status", "on")
        .trigger('click');    
}

const testConnectionBtnOnClick = ($testConnectionButton, $testConnectionAll, $testConnection) => {
    $testConnectionButton.addClass("d-none");
    $testConnectionAll
        .removeClass("btn btn-primary text-success")
        .addClass("bg-white btn-outline-secondary")
        .attr("test-status", "off");
    $testConnection
        .removeClass("text-success")
        .attr("test-status", "off");
}

//server, DB, Repl, SSO remove formbuilder errors.
const removeErrorForSelect = (selector) => {
    document.querySelectorAll(selector)?.forEach(element => element.remove());
};

const removeErrorForInputs = (inputValues, modulename) => {
    if (inputValues?.length) {
        inputValues?.each(function () {
            let $this = $(this);
            let res = $this.val();

            if (!res) {
                $this.siblings('.dynamic-input-field').remove();

                if (modulename === 'replication') {
                    $('.sourceFieldError').removeClass("field-validation-error").text("");
                    $('.destinationFieldError').removeClass("field-validation-error").text("");
                    $('.propertyError').removeClass("field-validation-error").text("");
                }
            }
        })
    }
}