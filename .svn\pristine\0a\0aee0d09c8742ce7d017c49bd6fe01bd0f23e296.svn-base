﻿function databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid) {
    if (field?.conditions && field?.conditions?.length > 0) {
        let isVisible = false;
        field.conditions.forEach(function (condition) {
            let isMatchingCondition = condition.if.some(function (ifClause) {
                sourceField = parsedJsonData.fields[ifClause.source.substring(7)];
                return ifClause.target === selectedValue;
            });
            if (isMatchingCondition) {
                isVisible = true;
            }
        });
        field.conditions.forEach(function (condition) {
            condition.then.forEach(function (thenClause) {
                condition.if.forEach(function (ifClause) {
                    let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                    let targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                    let srcElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                    if (targetElement) {
                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                            if (isVisible) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                            if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                if (targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                        }
                        else if (ifClause.comparison === "notEquals") {

                            if (targetElement && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                            else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                        }
                    }
                    else if (targetElementChk) {
                        if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {
                            if (isVisible) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                            }
                            if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                if (targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.contains("formeo-stage")) return false;
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add("d-none");
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                        }
                        else if (ifClause.comparison === "notEquals") {

                            if (targetElementChk && event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                if (textField?.length > 0) {
                                    removeValidationWhenUncheck(textField);
                                }
                                if (selectField?.length > 0) {
                                    removeValidationWhenUncheck(selectField);
                                }
                            }
                            else if (!event.target.checked && (selectedid.substring(0, selectedid.length - 2) === `f-${ifClause.source.substring(7)}`)) {
                                targetElementChk.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none')
                            }
                        }
                    }
                });
            });
        });
    }
}

function formBuilderDBCondition(event, parsedJsonData) {
    let selectedValue = event.target.value;
    let selectedid = event.target.id;
    let typ = event.target.type;
    let checked = event.target.checked;
    let getId = selectedid.replace('f-', '');

    if (typ === "radio" || typ === "checkbox") {

        let replacedId = getId.replace(/-0$/, '');
        let field = parsedJsonData?.fields && parsedJsonData?.fields[replacedId];

        //if comment this password field is visible so dont comment this code.
        if (selectedValue === 'IsRac' && checked) {
            setTimeout(() => {
                databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid);
            }, 150)
        }

        databaseFormBuilderConditions(event, field, parsedJsonData, selectedValue, selectedid);
    };

    if (typ === "select-one") {
        Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
            let field = parsedJsonData.fields[fieldId];

            if (field.conditions && field.conditions.length > 0) {
                let isMatchingCondition = field.conditions.some(function (condition) {
                    return condition.if.some(function (ifClause) {
                        if (ifClause.source === `fields.${fieldId}` && ifClause.target === selectedValue) {
                            return true;
                        }
                    });
                });

                if (isMatchingCondition) {
                    field.conditions.forEach(function (condition) {
                        condition.then.forEach(function (thenClause) {
                            condition.if.forEach(function (ifClause) {
                                let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                if (targetElement && thenClause.targetProperty === 'isVisible') {
                                    if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && targetElement.parentNode.parentNode.parentNode.parentNode.classList.contains("d-none")) {
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.remove('d-none');
                                        correctElementId = targetElement.id
                                    } else if (ifClause.target !== selectedValue) {
                                        targetElement.value = ""
                                        targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                        let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                        let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                        if (textField?.length > 0) {
                                            removeValidationWhenUncheck(textField);
                                        }
                                        if (selectField?.length > 0) {
                                            removeValidationWhenUncheck(selectField);
                                        }
                                    }
                                }
                            });
                        });
                    });
                } else {
                    Object.keys(parsedJsonData.fields).forEach(function (fieldId) {
                        let field = parsedJsonData.fields[fieldId];

                        if (field.conditions && field.conditions.length > 0) {
                            field.conditions.forEach(function (condition) {
                                condition.then.forEach(function (thenClause) {
                                    condition.if.forEach(function (ifClause) {
                                        let targetElement = document.getElementById(`f-${thenClause.target.substring(7)}`);
                                        let sourceElement = document.getElementById(`f-${ifClause.source.substring(7)}`);
                                        let sourceName = document.getElementsByName(`f-${ifClause.source.substring(7)}`);
                                        if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals' && (selectedid === sourceElement || selectedid == sourceName)) {
                                            targetElement.parentNode.parentNode.parentNode.parentNode.classList.add('d-none');
                                            let textField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-input-field');
                                            let selectField = targetElement?.parentNode?.parentNode?.parentNode?.parentNode?.querySelectorAll('.dynamic-select-tag');
                                            if (textField?.length > 0) {
                                                removeValidationWhenUncheck(textField);
                                            }
                                            if (selectField?.length > 0) {
                                                removeValidationWhenUncheck(selectField);
                                            }
                                        }
                                    });
                                });
                            });
                        }
                    });
                }
            }
        });
    }
}