﻿using System.Linq.Expressions;
using ContinuityPatrol.Application.Features.RpForVmCGMonitorStatus.Queries.GetPagination;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.RpForVmCGMonitorStatus.Queries;
public class GetRpForVmCgMonitorStatusPaginatedQueryHandlerTests
{
    private readonly Mock<IRpForVmCGMonitorStatusRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetRpForVmCGMonitorStatusPaginatedQueryHandler _handler;

    public GetRpForVmCgMonitorStatusPaginatedQueryHandlerTests()
    {
        _repositoryMock = new Mock<IRpForVmCGMonitorStatusRepository>();
        _mapperMock = new Mock<IMapper>();
        Mock<ILogger<GetRpForVmCGMonitorStatusPaginatedQueryHandler>> loggerMock = new();

        _handler = new GetRpForVmCGMonitorStatusPaginatedQueryHandler(
            _repositoryMock.Object,
            _mapperMock.Object,
            loggerMock.Object
        );
    }

    [Fact]
    public async Task Handle_Should_Return_PaginatedResult_With_StatusCounts()
    {
        // Arrange
        var request = new GetRpForVmCGMonitorStatusPaginatedQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "test",
            InfraObjectId = "infra123",
            ConsistencyGroupName = "cg",
            State = "Active",
            AvailabilityStatus = "Available",
            SortColumn = "Date",
            SortOrder = "asc"
        };

        // Correct types
        var domainPaginatedResult = PaginatedResult<Domain.Entities.RpForVmCGMonitorStatus>.Success(
            new List<Domain.Entities.RpForVmCGMonitorStatus>(), 0, 1, 10);

        var viewModelPaginatedResult = PaginatedResult<RpForVmCGMonitorStatusListVm>.Success(
            new List<RpForVmCGMonitorStatusListVm>(), 0, 1, 10);

        var statusCounts = new Dictionary<string, int> { { "Active", 5 } };

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
            request.PageNumber,
            request.PageSize,
            It.IsAny<RpForVmCGMonitorStatusSpecification>(),
            It.IsAny<Expression<Func<Domain.Entities.RpForVmCGMonitorStatus, bool>>>(),
            request.SortColumn,
            request.SortOrder
        )).ReturnsAsync(domainPaginatedResult);

        _repositoryMock.Setup(r => r.GetStatusCountAsync("infra123"))
            .ReturnsAsync(statusCounts);

        _mapperMock.Setup(m => m.Map<PaginatedResult<RpForVmCGMonitorStatusListVm>>(domainPaginatedResult))
            .Returns(viewModelPaginatedResult);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result.Item1);
        Assert.Equal(viewModelPaginatedResult, result.Item1);
        Assert.Equal(statusCounts, result.Item2);
    }


    [Theory]
    [InlineData(null, null, null, null)]
    [InlineData("infra123", null, null, null)]
    [InlineData(null, "CG1", null, null)]
    [InlineData(null, null, "Active", null)]
    [InlineData(null, null, null, "Available")]
    [InlineData("infra123", "CG1", "Active", "Available")]
    public async Task Handle_Should_Work_With_Various_Filters(string infra, string cg, string state, string availability)
    {
        // Arrange
        var request = new GetRpForVmCGMonitorStatusPaginatedQuery
        {
            PageNumber = 1,
            PageSize = 10,
            InfraObjectId = infra,
            ConsistencyGroupName = cg,
            State = state,
            AvailabilityStatus = availability,
            SearchString = "dummy",
            SortColumn = "CreatedDate",
            SortOrder = "desc"
        };

        var domainResult = PaginatedResult<Domain.Entities.RpForVmCGMonitorStatus>.Success(new List<Domain.Entities.RpForVmCGMonitorStatus>(), 0, 1, 10);
        var viewModelResult = PaginatedResult<RpForVmCGMonitorStatusListVm>.Success(new List<RpForVmCGMonitorStatusListVm>(), 0, 1, 10);
        var statusCounts = new Dictionary<string, int> { { "Available", 2 } };

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<Domain.Entities.RpForVmCGMonitorStatus>>(),
                It.IsAny<Expression<Func<Domain.Entities.RpForVmCGMonitorStatus, bool>>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(domainResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<RpForVmCGMonitorStatusListVm>>(
                It.IsAny < PaginatedResult < Domain.Entities.RpForVmCGMonitorStatus >> ()))
            .Returns(viewModelResult);

        _repositoryMock.Setup(r => r.GetStatusCountAsync(It.IsAny<string>()))
            .ReturnsAsync(statusCounts);

        // Act
        var (result, returnedStatusCounts) = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(returnedStatusCounts);
        Assert.Equal(0, result.TotalCount);
    }


    [Fact]
    public async Task Handle_Should_Return_Valid_Paginated_Result()
    {
        // Arrange
        var request = new GetRpForVmCGMonitorStatusPaginatedQuery
        {
            PageNumber = 1,
            PageSize = 10,
            InfraObjectId = "infra1",
            SearchString = "search",
            SortColumn = "Date",
            SortOrder = "asc"
        };

        var domainResult = PaginatedResult<Domain.Entities.RpForVmCGMonitorStatus>.Success(new List<Domain.Entities.RpForVmCGMonitorStatus>(), 0, 1, 10);

        _repositoryMock.Setup(r => r.PaginatedListAllAsync(
            It.IsAny<int>(),
            It.IsAny<int>(),
            It.IsAny<Specification<Domain.Entities.RpForVmCGMonitorStatus>>(),
            It.IsAny<Expression<Func<Domain.Entities.RpForVmCGMonitorStatus, bool>>>(),
            It.IsAny<string>(),
            It.IsAny<string>()
        )).ReturnsAsync(domainResult);

        _mapperMock.Setup(m => m.Map<PaginatedResult<RpForVmCGMonitorStatusListVm>>(domainResult))
            .Returns(PaginatedResult<RpForVmCGMonitorStatusListVm>.Success(new List<RpForVmCGMonitorStatusListVm>(), 0, 1, 10));

        _repositoryMock.Setup(r => r.GetStatusCountAsync("infra1"))
            .ReturnsAsync(new Dictionary<string, int> { { "Available", 5 } });

        // Act
        var (result, statusCounts) = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(statusCounts);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(5, statusCounts["Available"]);
    }
    [Fact]
    public void RpForVmCGMonitorStatusListVm_AllProperties_SetAndGet_Correctly()
    {
        // Arrange
        var vm = new RpForVmCGMonitorStatusListVm
        {
            Id = "CG001",
            InfraObjectId = "Infra123",
            ConsistencyGroupId = "GroupX",
            ConsistencyGroupName = "FinanceGroup",
            CGProperties = "{\"priority\":\"high\"}",
            State = "Enabled",
            TransferStatus = "Active",
            ActivityType = "Snapshot",
            ActivityStatus = "Completed",
            LastSnapShotTime = "2025-07-22T10:30:00Z",
            DataLag = "00:05:00",
            SnapProperties = "{\"snapId\":\"snap123\"}",
            AvailabilityStatus = "Available",
            ProtectedSize = "150GB",
            IsAlertSend = true
        };

        // Act & Assert
        Assert.Equal("CG001", vm.Id);
        Assert.Equal("Infra123", vm.InfraObjectId);
        Assert.Equal("GroupX", vm.ConsistencyGroupId);
        Assert.Equal("FinanceGroup", vm.ConsistencyGroupName);
        Assert.Equal("{\"priority\":\"high\"}", vm.CGProperties);
        Assert.Equal("Enabled", vm.State);
        Assert.Equal("Active", vm.TransferStatus);
        Assert.Equal("Snapshot", vm.ActivityType);
        Assert.Equal("Completed", vm.ActivityStatus);
        Assert.Equal("2025-07-22T10:30:00Z", vm.LastSnapShotTime);
        Assert.Equal("00:05:00", vm.DataLag);
        Assert.Equal("{\"snapId\":\"snap123\"}", vm.SnapProperties);
        Assert.Equal("Available", vm.AvailabilityStatus);
        Assert.Equal("150GB", vm.ProtectedSize);
        Assert.True(vm.IsAlertSend);
    }
}
