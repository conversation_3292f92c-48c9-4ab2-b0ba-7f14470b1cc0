﻿const formURL = {
    createOrUpdate: "Admin/FormBuilder/CreateOrUpdate",
    decryptForm: 'Admin/FormBuilder/FormDataDecrypt',
    delete: "Admin/FormBuilder/Delete",
    encryptForm: 'Admin/FormBuilder/FormDataEncrypt',
    formExport: "Admin/FormBuilder/FormExport",
    formImport: "Admin/FormBuilder/FormImport",
    getFormById: "Admin/FormBuilder/GetFormById",
    getFormHistrotyByID: 'Admin/FormHistory/GetFormHistoryByFormId',
    getServerType: 'Configuration/Server/GetServerType',
    isPublish: "Admin/FormBuilder/IsPublish",
    nameExist: "Admin/FormBuilder/FormNameExist"
};

let formID = "";
let saveAs = "";
let formNameEdit = "";
var formeo = "";
let sourceDirectoryValue = "Source Directory";
let destinationDirectoryValue = "Destination Directory";
let lunHeaderOne = "Header1";
let lunHeaderTwo = "Header2";
let lunHeaderThree = "Header3";
let lunHeaderFour = "Header4";
let lunHeaderFive = "Header5";
let lunHeaderSix = "Header6";
let thIds = ['dynamicHeader1', 'dynamicHeader2'];
let dynamicUpdateCondition = true;
let headerRowData;
let dataRowData;
let formIsValid = [];
let restoreFormname = "";
let btnDisableFormBuilder = false;
let editedFormName = '';
let this1 = "";

////Confirm then commit.
let formIds = [];
let checkedExportValues = "";

$(function () {
    preventSpecialKeys('#search-inp, #formName'); //commonfunctions.js
    $("#isPublish-form").hide();
    disableExportButton();

    const fileInput = document.getElementById('fileInput');
    fileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];

        if (file) {
            const reader = new FileReader();
            reader.onload = async function (e) {
                const decrytedData = await dataEncryptDecrypt(e.target.result, 'decrypt');

                if (decrytedData) {
                    await $.ajax({
                        type: "POST",
                        url: RootUrl + formURL.formImport,
                        dataType: "json",
                        headers: {
                            'RequestVerificationToken': await gettoken()
                        },
                        data: { command: decrytedData },
                        success: function (result) {
                            if (result?.success) {
                                if (result?.data?.success) {
                                    let resultData = result?.data;
                                    resetFileInput()
                                    notificationAlert("success", resultData?.message);
                                } else {
                                    resetFileInput()
                                    errorNotification(result?.data);
                                }
                            } else {
                                resetFileInput()
                                errorNotification(result)
                            }
                        },
                    });
                }
            };
            reader.readAsText(file);
        }
    });

    $('#formName').on('keyup', commonDebounce(async function () {
        const formNameVal = $("#formName").val().replace(/\s{2,}/g, ' ');
        $("#formName").val(formNameVal);
        let data = { id: $('#formID').val(), formName: formNameVal };
        moduleNameValidation('formbuilder', formNameVal, formURL.nameExist, $("#formNameError"), "Enter form name", data); //commonfunctions.js
    }));

    $("#importObjects").on("click", function () {
        $('#fileInput').trigger("click")
    });

    $("#exportObjects").on("click", async function () {
        let formIdToString = formIds.join(',');
        let formId = { id: formIdToString, __RequestVerificationToken: gettoken() };

        let result = await postRequestWithData(RootUrl + formURL.formExport, formId);
        if (result?.success) {
            let anyChecked = $(".exportData:checked").length > 0;
            if (anyChecked) {
                $(".exportData").prop("checked", false);
                $(".exportAllData").prop("checked", false)
                disableExportButton();
            }
            checkedExportValues = {
                "forms": result?.forms,
                "formTypeCategories": result?.formMappings,
                "componentTypes": result?.componts
            };
            const jsonData = JSON.stringify(checkedExportValues);
            const encryptedData = await dataEncryptDecrypt(jsonData, 'encrypt');
            const blob = new Blob([encryptedData], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'Formbuilder_Data.json';
            link.click();
            checkedExportValues = "";
        }
    });

    $(".exportAllData").on("click", function () {
        const isChecked = $(this).prop("checked");
        $(".exportData").prop("checked", isChecked).trigger("change");

        if (!isChecked) formIds = [];
    })

    $("#formBuilderList").on("change", ".exportData", function () {
        const id = $(this).data("id");
        const isChecked = $(this).prop("checked");

        formIds = isChecked
            ? [...formIds, id]
            : formIds.filter(existingId => existingId !== id);

        setTimeout(() => {
            if (formIds.length > 0) {
                $("#exportObjects").removeClass("d-none");
            } else {
                checkedExportValues = "";
            }
        }, 10);

        if ($(".exportData:checked").length === 0) {
            disableExportButton();
        }
    })

    $("#CreateModalForm").on("click", function () {
        $(".dynamicButton").removeClass("me-2").addClass("me-5");
        $("#isRestore").val(false);
        $('#formName').prop({ disabled: false, readonly: false }).val("");
        $('#formNameError').text('').removeClass('field-validation-error');
        $("#formID").val("");
        $("#formBuilderIsPublish").val(false);
        $(".save-button").text("Save");
        createUpdate();
        formID = "";

        setTimeout(() => {
            formeo = new FormeoEditor(formeoOptions);
            document.querySelector('#prevBtn').addEventListener('click', function () {
                $("#prevModal").modal("show")
                let propertyDiv = $('#form-preview-container');
                propertyDiv.empty();
                let renderedForm = new FormeoRenderer({
                    renderContainer: document.querySelector("#form-preview-container")
                });
                renderedForm.render(formeo.formData);
                populateFormbuilderDynamicFields(formeo.formData.fields)
            });
            dynamicIconChange();
        }, 50);
    });

    $(".clear_btn").on("click", function () {
        document.getElementById("formeo-editor").innerHTML = "";
        var formeo = new FormeoEditor(formeoOptions);
    });

    $(".prev_btn").on("click", function () {
        $(".steps").css("display", "block");
    })

    $(".save-button").on("click", async function () {
        if (saveAs === "SaveAsClicked") {
            formID = "";
            $("#formBuilderIsPublish").val(false);
        }

        formIsValid = [];

        const formData = formeo.formData;
        let source = $('.sourceDirectory').val();
        let destination = $('.destinationDirectory').val();

        const tags = Array.from({ length: 6 }, (_, i) => ({
            label: `H${i + 1}`,
            value: `h${i + 1}`,
            selected: false
        }));

        const tagsButton = [
            ['default', ''],
            ['primary', 'primary'],
            ['danger', 'error'],
            ['success', 'success'],
            ['warning', 'warning']
        ].map(([label, value]) => ({
            label,
            value,
            selected: false
        }));

        let elements = document.querySelectorAll('li.formeo-field.first-field.last-field');

        if (elements) {
            elements?.forEach(function (id, index) {
                let fieldID = id?.id;
                Object.keys(formData.fields).forEach(function () {
                    let field = formData?.fields[fieldID];
                    if (field) {
                        field["index"] = index;
                    }
                });
            })
        }

        //Remove if conditions have null value before save it.
        Object.keys(formData.fields).forEach(function (fieldId, index) {
            let field = formData?.fields[fieldId];
            if (field?.conditions && Array.isArray(field.conditions)) {
                field.conditions = field.conditions.filter(function (condition) {
                    // Check if condition is null or if it does not have both 'if' and 'then'
                    return condition
                        && Array.isArray(condition.if)
                        && condition.if.length > 0
                        && Array.isArray(condition.then)
                        && condition.then.length > 0;
                });
            }
        });

        Object.keys(formData.fields).forEach(function (fieldId, index) {
            let field = formData?.fields[fieldId];

            if (field?.attrs?.commonID === "HeaderTag" && field?.attrs?.tag?.length !== 6) {
                let updatedTags = tags.map(item => ({
                    ...item,
                    selected: item.value === field?.attrs?.tag // Set selected to true if value matches 'h2'
                }));
                field.attrs["tag"] = updatedTags;
            }
            if (field?.meta?.id === "dynamicButton" && (!Array.isArray(field?.options[0]?.className))) { //field?.options[0]?.className?.length !== 5
                let updatedClassName = tagsButton.map(item => ({
                    ...item,
                    selected: item.value === field?.options[0]?.className // Set selected to true if value matches
                }));
                field.options[0]["className"] = updatedClassName;
            }
            if (field?.attrs?.attrid === "textField" && !field?.attrs?.inputType) {
                field.attrs["inputType"] = "text";
            }
            if (field?.attrs?.attrid === "textField") {
                field.attrs["minlength"] = field.attrs.minlength > 0 ? field.attrs.minlength : "0";
            }
            if (field?.attrs?.className === "replication-custom-table") {
                field.attrs["sourcePath"] = source;
                field.attrs["destinationPath"] = destination;
            }
            if (field?.attrs?.className === "luns-custom-table") {

                for (let i = 1; i <= 6; i++) {
                    field.attrs[`lunsHeader${i}`] = $(`#lunsTableHeader${i}`).val();
                }
            }
            if (field?.attrs?.className === "dynamic-custom-table") {
                field.attrs["dynamicHeader"] = {};
                //let dynamicTable = $('.dynamic-custom-table');
                //dynamicTable?.each(function (childindex, element) {
                //    if (index === childindex) {
                //        $(element).find('input').each(function (inputIndex, inputElement) {
                //            let inputId = $(inputElement).attr('id');
                //            if (inputId && inputId.startsWith('dynamicHeader')) {
                //                field.attrs.dynamicHeader[inputId] = $(`#${inputId}`).val();
                //            }
                //        });
                //    }                
                //});

                thIds.forEach(function (headerValue) {
                    field.attrs.dynamicHeader[headerValue] = $(`#${headerValue}`).val();
                })
            }

            field?.conditions?.forEach(function (condition, conditionindex) {

                if (condition?.then.length > 0) {
                    condition?.then?.forEach(function (then, thenindex) {
                        let thenFieldID = then.target.replace(/^fields\./, "");

                        if (thenFieldID && !formData?.fields[thenFieldID]) {
                            field?.conditions?.splice(conditionindex, 1);
                        }
                    });
                }
            });
        });
        $('#formID').val(formID);
        $('#formBuilderVersion').val() ? "" : $('#formBuilderVersion').val("1.0.0");
        let data = { id: $('#formID').val(), formName: $("#formName").val() };
        let validateFormName = moduleNameValidation('formbuilder', $("#formName").val(), formURL.nameExist, $("#formNameError"), "Enter form name", data); //commonfunctions.js
        let labelElements = document.querySelectorAll('.formeo-stage .formeo-field .prev-label label');
        let otherElements = document.querySelectorAll('.formeo-stage .formeo-field .f-checkbox label');
        labelElements.forEach(function (labelElement) {
            validateFormLabelInput(labelElement);
        });

        otherElements.forEach(function (labelElement) {
            const parentElement = labelElement.closest('.formeo-field');
            if (parentElement) {
                const hasPrevLabelClass = Array.from(parentElement.children).some(child => child.classList.contains('prev-label'));
                !hasPrevLabelClass && validateFormLabelInput(labelElement);
            } else {
                validateFormLabelInput(labelElement);
            }
        });
        let props = JSON.stringify(formData);

        let encryption = await propertyEncryption(formData);
        $("#formBuilderProperties").val(encryption)

        const jsonObject = JSON.parse(props);

        if (Object.keys(jsonObject.fields).length === 0) {
            let elements = document.getElementsByClassName('formeo-stage');
            elements[0].innerHTML = 'Drag a box here <br/> <img src="/img/isomatric/form_builder_no_form.svg"/>';
            elements[0].style.textAlign = "center";
            setTimeout(() => { formeo = new FormeoEditor(formeoOptions); }, 1500)
            return false;
        }
        if (!btnDisableFormBuilder) {
            validateFormName.then(result => {
                const jsonObject = JSON.parse(props);
                let valid = formIsValid.every(result => result === true);

                if (result && Object.keys(jsonObject.fields).length > 0 && valid) {
                    btnDisableFormBuilder = true;
                    $('#formName').prop({ 'disabled': false, 'readonly': false });
                    //$('#formeo-editor').empty();

                    setTimeout(async () => {
                        $('#formeo-editor :input').prop('disabled', true);
                        const form = $('#formBuilder-form')[0];
                        const formData = new FormData(form);
                        $('#formeo-editor :input').prop('disabled', false);
                        let response = await createOrUpdate(RootUrl + formURL.createOrUpdate, formData); //commonfunctions.js                            
                        $('#CreateModal').modal('hide');
                        $('#RestoreConfirmationModal').modal('hide');
                        $('#restoreModal').modal('hide');                       

                        if (response?.success) {
                            notificationAlert("success", response?.data?.message);
                            setTimeout(() => {
                                if ($("#formBuilderSave").text() === "Restore") {
                                    $("#formBuilderSave").text("Update")
                                }
                                dataTableCreateAndUpdate($("#formBuilderSave"), dataTable);
                            }, 2000)
                        } else {
                            errorNotification(response);
                        }
                        btnDisableFormBuilder = false;
                    })
                }
            });
        }
    });
});

$(document).on('click', '.edit-button', function () {
    $(".dynamicButton").removeClass("me-2").addClass("me-5");
    $("#isRestore").val(false);
    dynamicUpdateCondition = true;
    formID = $(this).data('id');
    saveAs = "";
    createUpdate();
    forEditAndSaveAs("Update", formID);
    //If Disabled update button it's not enable. because json not update when delete condition.
    //$('.save-button').prop('disabled', false);
});

$(document).on('click', '.saveas-button', function () {
    $(".dynamicButton").removeClass("me-5").addClass("me-2");
    $("#isRestore").val(false);
    $('#formNameError, #dynamicWidth').removeClass('w-25').addClass('w-100');
    $('#prevBtn, #formeo-editor').hide();
    $('#dynamicInputName').text('Form Name').addClass("mt-2")
    $('#dynamicHeader').text('Save As Form')
    $('#ModalCreation').removeClass('modal-fullscreen').addClass('modal-m');
    formID = $(this).data('id');
    saveAs = "SaveAsClicked";
    forEditAndSaveAs("Save As", formID);
});

$(document).on("click", "#versionRestore", function () {
    let value = $('#formBuilderVersionControl').val();
    let validateVersion = versionValidation(value, " Select alternative version", "versionRestoreError");

    if (validateVersion) {
        $("#confirmRestoreButton").text("Yes");
        $("#restoreData").text(restoreFormname);
        $('#RestoreConfirmationModal').modal('show');
    }
});

$(document).on('click', '.restoreButton', async function () {
    $("#versionRestoreError").text("").removeClass('field-validation-versionError');
    $("#isRestore").val(true);
    restoreFormname = $(this).data('form-name');
    formID = $(this).data('id');
    let version = $(this).data('version');
    dynamicUpdateCondition = true;
    $('#primaryVersion').text(version);
    saveAs = "";
    formeo = "";
    forEditAndSaveAs("Restore", formID); //Populate form
    $('#formeo-editor, #primaryVersionContainer, #secondaryVersionContainer').empty();
    let data = { formId: formID, __RequestVerificationToken: gettoken() };
    let result = await postRequestWithData(RootUrl + formURL.getFormHistrotyByID, data); //commonfunctions.js
    let formBuilderVersionControl = $('#formBuilderVersionControl');
    formBuilderVersionControl.empty().append($('<option>').val("").text("Select Alternative Version"));

    if (result?.length) {
        let filtered = result?.filter(filter => filter.version !== version);
        filtered?.sort((a, b) => {
            let aParts = a.version.split('.').map(Number);
            let bParts = b.version.split('.').map(Number);

            for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
                if (aParts[i] === undefined) return -1; // a should come before b
                if (bParts[i] === undefined) return 1; // b should come before a
                if (aParts[i] !== bParts[i]) return bParts[i] - aParts[i];
            }
            return 0; // a and b are equal
        });
        filtered?.forEach(function (item) {
            formBuilderVersionControl.append($('<option>').val(item.id).text(item.version));
        });
    }
});

$(document).on('click', '.flexSwitchChecked', async function () {
    let form_id = $(this).data('id');
    let formId = { id: form_id, __RequestVerificationToken: gettoken() };
    let result = await postRequestWithData(RootUrl + formURL.getFormById, formId); //commonfunctions.js

    if (result?.id) {
        let isPublish = result?.isPublish;
        const changedPublish = (isPublish === true || isPublish === "True") ? "False" : "True";       
        let formData = new FormData();
        formData.append('Id', form_id);
        formData.append('IsPublish', changedPublish);

        let isPublishResult = await isPublishWithData(RootUrl + formURL.isPublish, formData);
        if (isPublishResult?.success) {
            notificationAlert("success", isPublishResult?.data?.message);
            setTimeout(() => {
                dataTable.ajax.reload(() => {
                    const currentPage = dataTable.page();
                    dataTable.page(currentPage).draw(false);
                }, false);
            },2000)
        } else {
            errorNotification(isPublishResult)
        }
    }
});

$(document).on('click', '.delete-button', function () {
    const $btn = $(this);
    const name = $btn.data("form-name");
    const id = $btn.data("form-id");
    const publish = $btn.data("form-publish");

    if (!publish) {
        $("#deleteData").attr("title", name).text(name);
        $("#textDeleteId").val(id);
        $('#DeleteModal').modal('show')
    } else {
        $btn.css({ cursor: "none", "pointer-events": "none", opacity: 0.5 });
    }
});

$("#confirmDeleteButton").on("click", async function () {
    const form = $('#deleteFormBuilder')[0];
    const formData = new FormData(form);

    if (!btnDisableFormBuilder) {
        btnDisableFormBuilder = true;
        let response = await deleteData(RootUrl + formURL.delete, formData);           
        $("#DeleteModal").modal("hide");       

        if (response?.success) {           
            notificationAlert("success", response?.data?.message);           
            setTimeout(() => {
                dataTableDelete(dataTable);
            }, 2000)
        } else {
            errorNotification(response);
        }
        btnDisableFormBuilder = false;
    }
});

$("#ForSaveIspublish").hide();
