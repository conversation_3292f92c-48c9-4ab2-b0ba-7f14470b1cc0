﻿namespace ContinuityPatrol.Application.Features.AlertNotification.Commands.Create;

public class
    CreateAlertNotificationCommandHandler : IRequestHandler<CreateAlertNotificationCommand,
        CreateAlertNotificationResponse>
{
    private readonly IAlertNotificationRepository _alertNotificationRepository;
    private readonly IMapper _mapper;

    public CreateAlertNotificationCommandHandler(IMapper mapper,
        IAlertNotificationRepository alertNotificationRepository)
    {
        _mapper = mapper;
        _alertNotificationRepository = alertNotificationRepository;
    }

    public async Task<CreateAlertNotificationResponse> Handle(CreateAlertNotificationCommand request,
        CancellationToken cancellationToken)
    {
        var alertNotification = _mapper.Map<Domain.Entities.AlertNotification>(request);

        //alertNotification.ReferenceId = Guid.NewGuid().ToString();

        alertNotification = await _alertNotificationRepository.AddAsync(alertNotification);

        var response = new CreateAlertNotificationResponse
        {
            Message = Message.Create(nameof(Domain.Entities.AlertNotification), alertNotification.InfraObjectId),
            AlertNotificationId = alertNotification.ReferenceId
        };

        return response;
    }
}