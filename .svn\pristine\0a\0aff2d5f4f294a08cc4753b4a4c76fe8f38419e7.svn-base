﻿using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;
using FluentValidation.TestHelper;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Validators;

public class CreateReportValidatorTests
{
    public List<Domain.Entities.Report> Reports { get; set; }

    private readonly CreateReportCommandValidator _validator;

    public CreateReportValidatorTests()
    {
        Reports = new Fixture().Create<List<Domain.Entities.Report>>();

        Mock<IReportRepository> mockReportRepository = new();

        mockReportRepository.Setup(repo => repo.IsReportNameExist(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);
        _validator = new CreateReportCommandValidator(mockReportRepository.Object);

        ReportRepositoryMocks.CreateReportRepository(Reports);
    }

    //Name

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Name_InReport_WithEmpty(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Name is Required.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Name_InReport_IsNull(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = null;

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("'Name' must not be empty.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_CreateReportCommandValidator_Name_MinimumRange(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "CD";

        var result = await _validator.TestValidateAsync(createReportCommand);

        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Name should contain between 3 to 30 characters.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_CreateReportCommandValidator_Name_MaximumRange(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "ABCDEFGHIJKLMNOPQRSTUWXYZ_ABCDEFGHIJKLMNOPQRSTUV";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Name should contain between 3 to 30 characters.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = " PTS ";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_DoubleSpace_InFront(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "  PTS";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_DoubleSpace_InBack(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "PTS  ";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_TripleSpace_InBetween(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "PTS   India";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_SpecialCharacters_InFront(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "%$#PTS India";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_SpecialCharacters_InBetween(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "PTS%$#*India";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_SpecialCharacters_Only(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "&*&%$#$%";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_UnderScore_InFront(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "_PTS";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_UnderScore_InFront_AndBack(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "_PTS_";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_Numbers_InFront(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "465PTS";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "_465PTS_";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_UnderScore_InFront_AndNumbers_InBack(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "_PTS353";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }

    [Theory]
    [AutoReportData]
    public async Task Verify_Create_Valid_Name_InReport_With_Numbers_Only(CreateReportCommand createReportCommand)
    {
        createReportCommand.Name = "36985715878";

        var result = await _validator.TestValidateAsync(createReportCommand);
        result.ShouldHaveValidationErrorFor(x => x.Name)
            .WithErrorMessage("Please Enter Valid Name.");
    }
}