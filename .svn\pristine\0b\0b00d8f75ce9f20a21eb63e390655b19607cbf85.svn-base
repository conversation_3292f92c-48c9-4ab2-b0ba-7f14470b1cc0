﻿// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance
var chart = am4core.create("ConfiguredRTO", am4charts.PieChart);
if (chart.logo) {
    chart.logo.disabled = true;
}

// Let's cut a hole in our Pie chart the size of 40% the radius
chart.innerRadius = am4core.percent(40);
chart.padding(-10, -10, -10, -10)

// Add and configure Series
var pieSeries = chart.series.push(new am4charts.PieSeries());
pieSeries.dataFields.value = "value";
pieSeries.dataFields.category = "category";
pieSeries.slices.template.stroke = am4core.color("#fff");
pieSeries.innerRadius = 10;
pieSeries.slices.template.fillOpacity = 0.5;
pieSeries.slices.template.propertyFields.fill = "fill";
pieSeries.slices.template.stroke = am4core.color("#fff");

// Add data
pieSeries.data = [
    {
        category: "Actual RTO",
        value: 60,
        fill: "#bdc02b",
    },
    {
        category: "RTO",
        value: 40,
        fill: "#3d5a80",
    }
];

// Disable sliding out of slices
pieSeries.slices.template.states.getKey("hover").properties.shiftRadius = 0;
pieSeries.slices.template.states.getKey("active").properties.scale = 1;

// Add second series
var pieSeries2 = chart.series.push(new am4charts.PieSeries());
pieSeries2.dataFields.value = "value";
pieSeries2.dataFields.category = "category";
pieSeries2.slices.template.states.getKey("hover").properties.shiftRadius = 0;
pieSeries2.slices.template.states.getKey("hover").properties.scale = 1;
pieSeries2.slices.template.propertyFields.fill = "fill";


pieSeries.slices.template.propertyFields.disabled = "labelDisabled";
pieSeries.labels.template.disabled = true;
pieSeries.ticks.template.disabled = true;

pieSeries2.slices.template.propertyFields.disabled = "labelDisabled";
pieSeries2.labels.template.disabled = true;
pieSeries2.ticks.template.disabled = true;

// Add data
pieSeries2.data = [{
    "category": "Actual RTO",
    "value": 60,
    fill: "#bdc02b",
}, {
    "category": "RTO",
    "value": 40,
    fill: "#3d5a80",
}];


pieSeries.adapter.add("innerRadius", function (innerRadius, target) {
    return am4core.percent(40);
})

pieSeries2.adapter.add("innerRadius", function (innerRadius, target) {
    return am4core.percent(60);
})

pieSeries.adapter.add("radius", function (innerRadius, target) {
    return am4core.percent(100);
})

pieSeries2.adapter.add("radius", function (innerRadius, target) {
    return am4core.percent(80);
})

var label = chart.seriesContainer.createChild(am4core.Label);
label.textAlign = "middle";
label.horizontalCenter = "middle";
label.verticalCenter = "middle";
label.adapter.add("text", function (text, target) {
    return (//parseInt(props.configRTO) + parseInt(props.actualRTO) + parseInt(props.savedRTO) !== 0 ?
        "Configured [/]\n RTO [/]\n" +
        "[/]"
        //: null
    );
});
label.fill = am4core.color("#1338BE");



// Themes begin
am4core.useTheme(am4themes_animated);
// Themes end

// Create chart instance
var chart = am4core.create("ActualRTOChart", am4charts.PieChart);
if (chart.logo) {
    chart.logo.disabled = true;
}
// Add data
chart.data = [{
    "country": "Succeeded",
    "litres": 40,
    color: "#50c878",
}, {
    "country": "Skipped",
    "litres": 60,
    color: "#2f4558",
}];

// Add and configure Series
var pieSeries = chart.series.push(new am4charts.PieSeries());
pieSeries.slices.template.propertyFields.fill = "color";
pieSeries.dataFields.value = "litres";
pieSeries.dataFields.category = "country";
pieSeries.slices.template.stroke = am4core.color("#fff");
pieSeries.slices.template.strokeOpacity = 1;

pieSeries.labels.template.disabled = true;
pieSeries.ticks.template.disabled = true;


// This creates initial animation
pieSeries.hiddenState.properties.opacity = 1;
pieSeries.hiddenState.properties.endAngle = -90;
pieSeries.hiddenState.properties.startAngle = -90;

chart.hiddenState.properties.radius = am4core.percent(0);

