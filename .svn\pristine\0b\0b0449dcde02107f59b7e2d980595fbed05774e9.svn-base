﻿using ContinuityPatrol.Application.Features.WorkflowAction.Commands.SaveAs;
using ContinuityPatrol.Application.Features.WorkflowAction.Events.SaveAs;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Commands;

public class SaveAsWorkflowActionTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IWorkflowActionRepository> _workflowActionRepoMock;
    private readonly Mock<IVersionManager> _versionManagerMock;
    private readonly Mock<IPublisher> _publisherMock;
    private readonly SaveAsWorkflowActionCommandHandler _handler;

    public SaveAsWorkflowActionTests()
    {
        _mapperMock = new Mock<IMapper>();
        _workflowActionRepoMock = new Mock<IWorkflowActionRepository>();
        _versionManagerMock = new Mock<IVersionManager>();
        _publisherMock = new Mock<IPublisher>();

        _handler = new SaveAsWorkflowActionCommandHandler(
            _mapperMock.Object,
            _workflowActionRepoMock.Object,
            _versionManagerMock.Object,
            _publisherMock.Object
        );
    }

    [Fact]
    public async Task Handle_ShouldCreateNewWorkflowAction_WhenValidRequest()
    {
        // Arrange
        var request = new SaveAsWorkflowActionCommand
        {
            WorkflowActionId = "ref-1",
            Name = "ClonedAction",
            Version = "v1"
        };

        var originalEntity = new Domain.Entities.WorkflowAction
        {
            Id = 5,
            ReferenceId = "ref-1",
            ActionName = "Original",
            Version = "v1"
        };

        _workflowActionRepoMock
            .Setup(repo => repo.GetByReferenceIdAsync("ref-1"))
            .ReturnsAsync(originalEntity);

        _versionManagerMock
            .Setup(vm => vm.GetVersion("v1"))
            .ReturnsAsync("v2");

        _mapperMock
            .Setup(m => m.Map(
                request,
                It.IsAny<Domain.Entities.WorkflowAction>(),
                typeof(SaveAsWorkflowActionCommand),
                typeof(Domain.Entities.WorkflowAction)))
            .Callback<object, object, Type, Type>((src, dest, _, _) =>
            {
                var srcCmd = src as SaveAsWorkflowActionCommand;
                var destEntity = dest as Domain.Entities.WorkflowAction;

                destEntity!.ActionName = srcCmd!.Name;
                destEntity.Version = srcCmd.Version;
                destEntity.ReferenceId = Guid.NewGuid().ToString(); // simulate mapping
            });

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().NotBeNullOrEmpty();
        result.Message.Should().Contain("ClonedAction");

        _workflowActionRepoMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Once);
        _publisherMock.Verify(x => x.Publish(It.IsAny<WorkflowActionSaveAsEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityDoesNotExist()
    {
        // Arrange
        var request = new SaveAsWorkflowActionCommand
        {
            WorkflowActionId = "missing-id",
            Name = "MissingAction",
            Version = "v1"
        };

        _workflowActionRepoMock
            .Setup(repo => repo.GetByReferenceIdAsync("missing-id"))
            .ReturnsAsync((Domain.Entities.WorkflowAction)null!);

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage("*missing-id*");

        _workflowActionRepoMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.WorkflowAction>()), Times.Never);
        _publisherMock.Verify(x => x.Publish(It.IsAny<WorkflowActionSaveAsEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }
    [Fact]
    public void SaveAsWorkflowActionCommand_Should_SetAndGet_AllProperties()
    {
        // Arrange
        var expectedId = "wf-123";
        var expectedName = "SaveAsAction";
        var expectedIsLock = true;
        var expectedVersion = "v1.2";

        // Act
        var command = new SaveAsWorkflowActionCommand
        {
            WorkflowActionId = expectedId,
            Name = expectedName,
            IsLock = expectedIsLock,
            Version = expectedVersion
        };

        // Assert
        Assert.Equal(expectedId, command.WorkflowActionId);
        Assert.Equal(expectedName, command.Name);
        Assert.True(command.IsLock);
        Assert.Equal(expectedVersion, command.Version);
    }
}