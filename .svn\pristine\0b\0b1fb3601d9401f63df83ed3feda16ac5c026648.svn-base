﻿namespace ContinuityPatrol.Web.TagHelpers
{
    [HtmlTargetElement("multilogin", Attributes = "user-name")]
    public class MultiLoginTagHelper : TagHelper
    {
        public string UserName { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            base.Process(context, output);

            output.TagName = "div";

            var message = string.IsNullOrEmpty(UserName) ? "User already logged in same browser session" : $"User <span class=\"fw-semibold text-primary\">{UserName}</span> is already logged in from another session/computer.";

            var content = $@"
<div class=""modal fade"" id=""DeleteAccountModal"" tabindex=""-1"" aria-labelledby=""exampleModalLabel"" aria-hidden=""true"">
    <div class=""modal-dialog modal-sm modal-dialog-centered"">
        <div class=""modal-content"">
            <form>
                <div class=""modal-header p-0 border-bottom-0"">
                    <input type=""hidden"" id=""userName"" value=""{UserName}"" />
                    <img class=""delete-img"" src=""img/isomatric/already_active.png"" alt=""Delete Account"" style=""margin:-100px 0 0 -26px;"" />
                </div>
                <div class=""modal-body text-center pt-0"">
                    <h5 class=""fw-bold""><i class=""cp-fail-back fs-5 text-danger me-2""></i>Alert</h5>
                    <p class=""fs-6"">{message}</p>
                    <p> Do you want to close your previous sessions?</p>
                </div>
                <div class=""modal-footer justify-content-center border-top-0"">
                    <button type=""button"" class=""btn btn-secondary btn-sm"" data-bs-dismiss=""modal"">Cancel</button>
                    <button type=""submit"" class=""btn btn-primary btn-sm"" id=""confirmClearButton"">Continue <div id=""loginLoader"" class=""spinner-border text-white ms-2 mt-1 p-1 d-none"" style=""width: 0.8rem; height: 0.8rem;"" role=""status"">
                                            <span class=""visually-hidden"">Loading...</span>
                                        </div></button>
                </div>
            </form>
        </div>
    </div>
</div>"
                ;

            output.Content.SetHtmlContent(content);
        }
    }
}
