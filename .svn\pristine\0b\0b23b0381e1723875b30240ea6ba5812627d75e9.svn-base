﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.AdPasswordExpireModel.AdPasswordExpireViewModel

<div class="modal-dialog modal-dialog-centered modal-dialog-scrollabel">
    <div class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-BIA-cost"></i><span> AD Password Expire Configuration</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" style="min-height:25rem">
            <div>
                <div class="form-group w-100">
                    <div class="form-label"> Domain Server</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-site-names"></i></span>
                        <select asp-for='DomainServerId' class="form-select-modal mb-0" id="adDomainID" data-placeholder="Select Domain Server">
                            <option></option>
                        </select>
                        <input asp-for='DomainServer' type="hidden" id="ddlDomainName" />
                    </div>
                    <span asp-validation-for="DomainServer" id="domainServerError"></span>
                </div>

                <div class="form-group w-100">
                    <div class="form-label">Username</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-name"></i></span>
                        <input type="text" class="form-control" id="txtUser" asp-for='UserName' placeholder="Enter Username" />
                    </div>
                    <span asp-validation-for="UserName" id="userNameError"></span>
                </div>

                <div class="form-group w-100">
                    <div class="form-label">Notification Email</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-email"></i></span>
                        <input type="text" class="form-control" id="txtEmail" asp-for="Email" placeholder="Enter Notification Email" />
                    </div>
                    <span asp-validation-for="Email" id="EmailError"></span>
                </div>

                <div class="form-group w-100">
                    <div class="form-label">Server List</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-server"></i></span>
                        <select multiple class="form-select-modal mb-0" data-placeholder="Select Server List" id="adServerList">
                            <option></option>
                        </select>
                        <input type="hidden" asp-for='ServerList' id="serverList" />
                    </div>
                    <span asp-validation-for="ServerList" id="serverListError"></span>
                </div>

                <div class="form-group w-100">
                    <div class="form-label">Notification Days</div>
                    <div class="input-group">
                        <span class="input-group-text"><i class="cp-notification"></i></span>
                        <input asp-for='NotificationDays' type="number" class="form-control" id="txtNotificationDays" placeholder="Enter Notification Days" />
                    </div>
                    <span asp-validation-for="NotificationDays" id="notificationDaysError"></span>
                </div>
            </div>

            <input asp-for='Id' type="hidden" id="adPasswordID" />
        </div>

        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div class="gap-2 d-flex">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary btn-sm" id="SaveFunction">Save</button>
            </div>
        </div>
    </div>
</div>