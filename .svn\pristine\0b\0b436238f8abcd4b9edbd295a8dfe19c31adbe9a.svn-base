﻿using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.Report.Queries
{
    public class GetDriftReportStatusHandlerTests
    {
        private readonly Mock<IDriftEventRepository> _mockDriftEventRepository;
        private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
        private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
        private readonly Mock<ILogger<GetDriftReportStatusHandler>> _mockLogger;
        private readonly Mock<IMapper> _mockMapper;

        private readonly GetDriftReportStatusHandler _handler;

        public GetDriftReportStatusHandlerTests()
        {
            _mockDriftEventRepository = new Mock<IDriftEventRepository>();
            _mockLoggedInUserService = new Mock<ILoggedInUserService>();
            _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
            _mockLogger = new Mock<ILogger<GetDriftReportStatusHandler>>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetDriftReportStatusHandler(
                _mockDriftEventRepository.Object,
                _mockLoggedInUserService.Object,
                _mockInfraObjectRepository.Object,
                _mockLogger.Object,
                _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ReturnsMappedDriftList_WhenDataExists()
        {
            var request = new GetDriftReportStatusQuery
            {
                StartDate = "DateTime.Now.AddDays(-7)",
                EndDate = "DateTime.Now",
                Id = Guid.NewGuid().ToString(), 
            };

            var driftEvents = new List<DriftEvent>
            {
                new DriftEvent { EntityStatus = "Active", InfraObjectId = Guid.NewGuid().ToString() },
                new DriftEvent { EntityStatus = "Resolved", InfraObjectId = Guid.NewGuid().ToString() }
            };

            var expectedMappedDriftList = new List<DriftEventReportVm>
            {
                new DriftEventReportVm { InfraObjectId = driftEvents[0].InfraObjectId },
                new DriftEventReportVm { InfraObjectId = driftEvents[1].InfraObjectId }
            };

            _mockDriftEventRepository
                .Setup(repo => repo.GetInfraObjectIdByStatus(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(driftEvents);

            _mockMapper
                .Setup(mapper => mapper.Map<List<DriftEventReportVm>>(It.IsAny<List<DriftEvent>>()))
                .Returns(expectedMappedDriftList);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(expectedMappedDriftList.Count, result.Count);
            _mockDriftEventRepository.Verify(repo => repo.GetInfraObjectIdByStatus(request.StartDate, request.EndDate, request.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<DriftEventReportVm>>(driftEvents), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEmptyList_WhenNoDataExists()
        {
            var request = new GetDriftReportStatusQuery
            {
                StartDate = "DateTime.Now.AddDays(-7)",
                EndDate = "DateTime.Now",
                Id = Guid.NewGuid().ToString(),
            };

            _mockDriftEventRepository
                .Setup(repo => repo.GetInfraObjectIdByStatus(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new List<DriftEvent>());

            _mockMapper
                .Setup(mapper => mapper.Map<List<DriftEventReportVm>>(It.IsAny<List<DriftEvent>>()))
                .Returns(new List<DriftEventReportVm>());

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Empty(result);
            _mockDriftEventRepository.Verify(repo => repo.GetInfraObjectIdByStatus(request.StartDate, request.EndDate, request.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<DriftEventReportVm>>(It.IsAny<List<DriftEvent>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            var request = new GetDriftReportStatusQuery
            {
                StartDate = "DateTime.Now.AddDays(-7)",
                EndDate = "DateTime.Now",
                Id = Guid.NewGuid().ToString(),
            };

            _mockDriftEventRepository
                .Setup(repo => repo.GetInfraObjectIdByStatus(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Database error"));

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.Null(result);
            _mockDriftEventRepository.Verify(repo => repo.GetInfraObjectIdByStatus(request.StartDate, request.EndDate, request.Id), Times.Once);
            _mockLogger.Verify(logger => logger.LogError(It.IsAny<string>()), Times.Once);
        }
    }
}
