﻿using ContinuityPatrol.Application.Features.CGExecutionReport.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.CGExecution.Events;

public class CGExecutionDeleteEventHandlerTests : IClassFixture<CGExecutionReportFixture>, IClassFixture<UserActivityFixture>
{
    private readonly CGExecutionReportFixture _cgExecutionFixture;
    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly CGExecutionDeletedEventHandler _handler;

    public CGExecutionDeleteEventHandlerTests(CGExecutionReportFixture cgExecutionFixture, UserActivityFixture userActivityFixture)
    {
        _cgExecutionFixture = cgExecutionFixture;
        _userActivityFixture = userActivityFixture;

        var mockLogger = new Mock<ILogger<CGExecutionDeletedEventHandler>>();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        _mockUserActivityRepository = CGExecutionReportRepositoryMocks.CreateCGExecutionReportEventRepository(_userActivityFixture.UserActivities);

        _handler = new CGExecutionDeletedEventHandler(mockLoggedInUserService.Object, mockLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_CGExecutionDeleteEventTriggered()
    {
        // Arrange
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        // Act
        var result = _handler.Handle(_cgExecutionFixture.CGExecutionDeletedEvent, CancellationToken.None);

        // Assert
        Assert.True(result.IsCompleted);
        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        // Act
        await _handler.Handle(_cgExecutionFixture.CGExecutionDeletedEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
