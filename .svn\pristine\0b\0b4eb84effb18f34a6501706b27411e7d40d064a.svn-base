﻿using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Delete;
using ContinuityPatrol.Application.Features.BusinessFunction.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetDetail;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetList;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetListByBusinessServiceId;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNames;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.BusinessFunctionModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class BusinessFunctionsController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<BusinessFunctionListVm>>> GetBusinessFunctions()
    {
        Logger.LogDebug("Get All OperationalFunctions");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + LoggedInUserService.CompanyId,
            () => Mediator.Send(new GetBusinessFunctionListQuery()), CacheExpiry));
    }

    [HttpGet("{id}", Name = "GetBusinessFunction")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<BusinessFunctionDetailVm>> GetBusinessFunctionById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "OperationalFunction Id");

        Logger.LogDebug($"Get OperationalFunction Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetBusinessFunctionDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<BusinessFunctionListVm>>> GetPaginatedBusinessFunctions([FromQuery] GetBusinessFunctionPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in OperationalFunction Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet("businessServiceId")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<BusinessFunctionDetailVm>> GetBusinessFunctionListByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "OperationalService Id");

        Logger.LogDebug($"Get OperationalFunction List by OperationalService Id '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetBusinessFunctionListByBusinessServiceIdQuery { BusinessServiceId = businessServiceId }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateBusinessFunctionResponse>> CreateBusinessFunction([FromBody] CreateBusinessFunctionCommand createBusinessFunctionCommand)
    {
        Logger.LogDebug($"Create OperationalFunction '{createBusinessFunctionCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateBusinessFunction), await Mediator.Send(createBusinessFunctionCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateBusinessFunctionResponse>> UpdateBusinessFunction([FromBody] UpdateBusinessFunctionCommand updateBusinessFunctionCommand)
    {
        Logger.LogDebug($"Update OperationalFunction '{updateBusinessFunctionCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateBusinessFunctionCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteBusinessFunctionResponse>> DeleteBusinessFunction(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "OperationalFunction Id");

        Logger.LogDebug($"Delete OperationalFunction Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteBusinessFunctionCommand { Id = id }));
    }

    [HttpGet, Route("names")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<BusinessFunctionNameVm>>> GetBusinessFunctionNames()
    {
        Logger.LogDebug("Get All OperationalFunction Names");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllBusinessFunctionsNameCacheKey,
            () => Mediator.Send(new BusinessFunctionNameQuery()), CacheExpiry));
    }

    [Route("name-exist"), HttpGet]
    public async Task<ActionResult> IsBusinessFunctionNameExist(string businessFunctionName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(businessFunctionName, "OperationalFunction Name");

        Logger.LogDebug($"Check Name Exists Detail by OperationalFunction Name '{businessFunctionName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetBusinessFunctionNameUniqueQuery { BusinessFunctionName = businessFunctionName, BusinessFunctionId = id }));
    }

    [HttpGet, Route("by/{businessServiceId}")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<GetBusinessFunctionNameByBusinessServiceIdVm>>> GetBusinessFunctionNamesByBusinessServiceId(string businessServiceId)
    {
        Guard.Against.InvalidGuidOrEmpty(businessServiceId, "OperationalService Id");

        Logger.LogDebug($"Get OperationalFunction by OperationalService Id '{businessServiceId}'");

        return Ok(await Mediator.Send(new GetBusinessFunctionNameByBusinessServiceIdQuery { Id = businessServiceId }));
    }
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllBusinessFunctionsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllBusinessFunctionsNameCacheKey };

        ClearCache(cacheKeys);
    }
}