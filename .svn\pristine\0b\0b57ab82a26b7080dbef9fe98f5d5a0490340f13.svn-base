﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IDrCalenderRepository : IRepository<DrCalenderActivity>
{
    Task<bool> IsDrCalenderNameUnique(string name);
    Task<bool> IsActivityNameExist(string name, string id, DateTime scheduleStartDate);
    Task<IReadOnlyList<DrCalenderActivity>> DrCalendarDrillEventList();
    Task<IReadOnlyList<DrCalenderActivity>> UpComingDrillList();

    Task<List<DrCalenderActivity>>GetByWorkflowProfileId(string workflowProfileId);
}