﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class OneViewEntitiesEventViewRepository:IOneViewEntitiesEventViewRepository 
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;
    public OneViewEntitiesEventViewRepository(ApplicationDbContext dbContext,ILoggedInUserService loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async  Task<List<OneViewEntitiesEventView>> ListAllAsync()
    {
        var result = await _dbContext.OneViewEntitiesEventViews.AsNoTracking().ToListAsync();
        return result;
    }

    }
