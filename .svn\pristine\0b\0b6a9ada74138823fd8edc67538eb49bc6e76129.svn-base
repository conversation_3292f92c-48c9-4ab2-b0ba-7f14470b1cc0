﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowHistoryRepositoryMocks
{
    public static Mock<IWorkflowHistoryRepository> CreateWorkflowHistoryRepository(List<WorkflowHistory> workflowVersionHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowVersionHistories);

        workflowHistoryRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowHistory>())).ReturnsAsync(
            (WorkflowHistory workflowHistory) =>
            {
                workflowHistory.Id = new Fixture().Create<int>();
                workflowHistory.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowVersionHistories.Add(workflowHistory);

                return workflowHistory;
            });

        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> UpdateWorkflowHistoryRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowHistories);

        workflowHistoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowHistories.SingleOrDefault(x => x.ReferenceId == i));

        workflowHistoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowHistory>())).ReturnsAsync((WorkflowHistory workflowHistory) =>
        {
            var index = workflowHistories.FindIndex(item => item.Id == workflowHistory.Id);

            workflowHistories[index] = workflowHistory;

            return workflowHistory;
        });

        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> DeleteWorkflowHistoryRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowHistories);

        workflowHistoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowHistories.SingleOrDefault(x => x.ReferenceId == i));

        workflowHistoryRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowHistory>())).ReturnsAsync((WorkflowHistory workflowHistory) =>
        {
            var index = workflowHistories.FindIndex(item => item.ReferenceId == workflowHistory.ReferenceId);
            workflowHistory.IsActive = false;
            workflowHistories[index] = workflowHistory;

            return workflowHistory;
        });

        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> GetWorkflowHistoryRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowHistories);

        workflowHistoryRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowHistories.SingleOrDefault(x => x.ReferenceId == i));

        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> GetWorkflowVersionNameUniqueRepositoryMock(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.IsWorkflowHistoryNameExist(It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync((string i, string j) => workflowHistories.Exists(x => x.WorkflowName == i && x.ReferenceId == j));

        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> GetWorkflowHistoryEmptyRepository()
    {
        var mockCategoryRepository = new Mock<IWorkflowHistoryRepository>();

        mockCategoryRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowHistory>());

        return mockCategoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> GetWorkflowHistoryNamesRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.GetWorkflowHistoryNames()).ReturnsAsync(workflowHistories);

        return workflowHistoryRepository;
    }


    public static Mock<IWorkflowHistoryRepository> GetPaginatedWorkflowHistoryRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<WorkflowHistory>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<WorkflowHistory> spec, string sortColumn, string sortOrder) =>
            {
                var sortedCompanies = workflowHistories.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedCompanies = sortedCompanies.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming Company has a Name property; replace logic as needed
                    sortedCompanies = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedCompanies.OrderByDescending(c => c.WorkflowName)
                        : sortedCompanies.OrderBy(c => c.WorkflowName);
                }

                var totalCount = sortedCompanies.Count();
                var paginated = sortedCompanies
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<WorkflowHistory>.Success(paginated, totalCount, pageNumber, pageSize);
            });
        return workflowHistoryRepository;
    }

    public static Mock<IWorkflowHistoryRepository> GetWorkflowHistoryByWorkflowIdRepository(List<WorkflowHistory> workflowHistories)
    {
        var workflowHistoryRepository = new Mock<IWorkflowHistoryRepository>();

        workflowHistoryRepository.Setup(repo => repo.GetWorkflowHistoryByWorkflowId(It.IsAny<string>())).ReturnsAsync(workflowHistories);

        return workflowHistoryRepository;
    }

    //Events

    public static Mock<IUserActivityRepository> CreateWorkflowHistoryEventRepository(List<UserActivity> userActivities)
    {
        var workflowHistoryEventRepository = new Mock<IUserActivityRepository>();

        workflowHistoryEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowHistoryEventRepository;
    }
}