﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetByMaintenanceInfraObject;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupMaintenanceInfraObjectListQueryHandlerTests
{
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly GetMaintenanceInfraObjectListQueryHandler _handler;

    public GetWorkflowOperationGroupMaintenanceInfraObjectListQueryHandlerTests()
    {
        _handler = new GetMaintenanceInfraObjectListQueryHandler(_mockMapper.Object, _mockInfraObjectRepository.Object);
    }
    [Fact]
    public async Task Handle_ReturnsMappedInfraObjects_WhenActiveOrEmptyState()
    {
        // Arrange
        var request = new GetMaintenanceInfraObjectListQuery
        {
            InfraObjectId = "ref1,ref2,ref3"
        };

        var infraObjectList = new List<Domain.Entities.InfraObject>
        {
            new() { ReferenceId = "ref1", State = "Active" },
            new() { ReferenceId = "ref2", State = "" },
            new() { ReferenceId = "ref3", State = "Inactive" }
        };

        var expectedMappedList = new List<GetMaintenanceInfraObjectListVm>
        {
            new() { InfraObjectId = "ref1",InfraObjectName = "Demo"},
            new() { InfraObjectId = "ref2",InfraObjectName = "Demo1" }
        };


        _mockInfraObjectRepository.Setup(repo => repo.GetByReferenceIdsAsync(It.Is<List<string>>(ids =>
                ids.SequenceEqual(new List<string> { "ref1", "ref2", "ref3" }))))
            .ReturnsAsync(infraObjectList);

        _mockMapper.Setup(m => m.Map<List<GetMaintenanceInfraObjectListVm>>(It.IsAny<List<Domain.Entities.InfraObject>>()))
            .Returns(expectedMappedList);

       // var handler = new GetMaintenanceInfraObjectListQueryHandler(_mockMapper.Object, _mockInfraObjectRepository.Object);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.InfraObjectId == "ref1");
        Assert.Contains(result, x => x.InfraObjectId == "ref2");
        Assert.Contains(result, x => x.InfraObjectName == "Demo");
        Assert.Contains(result, x => x.InfraObjectName == "Demo1");

        _mockInfraObjectRepository.Verify(x => x.GetByReferenceIdsAsync(It.IsAny<List<string>>()), Times.Once);
        _mockMapper.Verify(x => x.Map<List<GetMaintenanceInfraObjectListVm>>(It.IsAny<List<Domain.Entities.InfraObject>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoActiveOrEmptyState()
    {
        // Arrange
        var request = new GetMaintenanceInfraObjectListQuery
        {
            InfraObjectId = "ref1"
        };

        var infraObjectList = new List<Domain.Entities.InfraObject>
        {
            new() { ReferenceId = "ref1", State = "Inactive" }
        };

        //var mockRepo = new Mock<IInfraObjectRepository>();
        //var mockMapper = new Mock<IMapper>();

        _mockInfraObjectRepository.Setup(repo => repo.GetByReferenceIdsAsync(It.IsAny<List<string>>()))
            .ReturnsAsync(infraObjectList);

            //var handler = new GetMaintenanceInfraObjectListQueryHandler(_mockMapper.Object, _mockInfraObjectRepository.Object);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        _mockMapper.Verify(m => m.Map<List<GetMaintenanceInfraObjectListVm>>(It.IsAny<List<Domain.Entities.InfraObject>>()), Times.Never);
    }

}