﻿@model ContinuityPatrol.Domain.ViewModels.InfraObjectModel.InfraObjectViewModel
@using ContinuityPatrol.Shared.Services.Helper
<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable modal-xl" id="CreateModal">
    <form class="modal-content wizard-content" id="CreateForm" asp-area="Configuration" asp-controller="InfraObject" asp-action="SaveOrUpdate" method="post">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-infra-object"></i><span>InfraObject Configuration</span></h6>
            <button type="button" class="btn-close" title="Close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body py-2">
            <div class="tab-wizard wizard-circle wizard clearfix example-form icons-tab-steps checkout-tab-steps" id="infraObjectWizard">
                <h6>
                    <span class="step">
                        <i class="cp-add"></i>
                    </span>
                    <span class="step_title">
                        Create InfraObject
                    </span>
                </h6>
                <section>
                    <div class="row row-cols-2">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-name"></i></span>
                                    <input asp-for="Name" maxlength="100" id="textName" type="text" class="form-control" placeholder="Enter InfraObject Name" />
                                    <input asp-for="Id" id="infraNameId" type="hidden" class="form-control" placeholder="Enter InfraObject Name" />

                                </div>
                                <span asp-validation-for="Name" id="NameError"></span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Description <small class="text-secondary">( Optional )</small></label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-description"></i></span>
                                    <input asp-for="Description" id="Description" type="text" class="form-control" maxlength="250" placeholder="Enter Description" />
                                </div>
                                <span asp-validation-for="Description" id="DescriptionError"></span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Operational Service</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-business-service"></i></span>
                                    <select asp-for="BusinessServiceName" id="infraBusinessServiceId" tabindex="-1" class="form-select-modal" data-placeholder="Select Operational Service" aria-label="Default select example">
                                        @foreach (var BusinessServices in Model.BusinessServices)
                                        {
                                            <option id="@BusinessServices.Id" value="@BusinessServices.Name" 
                                            siteProperties="@BusinessServices.SiteProperties">@BusinessServices.Name</option>
                                        }
                                    </select>
                                </div>
                                <span asp-validation-for="BusinessServiceName" id="BusinessServiceError"></span>
                                <input asp-for="BusinessServiceId" id="BusinessServiceId" type="hidden" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label class="form-label">Operational Function</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-business-function"></i></span>
                                    <select id="ddlbusinessFunctionId" class="form-select-modal" data-placeholder="Select Operational Function">
                                    </select>
                                </div>
                                <span asp-validation-for="BusinessFunctionName" id="BusinessFunctionError"></span>
                                <input asp-for="BusinessFunctionName" id="BusinessFunctionVal" type="hidden" class="form-control" />
                                <input asp-for="BusinessFunctionId" id="BusinessFunctionId" type="hidden" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label class="form-label">Activity Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-activity-type"></i></span>
                                    <select asp-for="Type" class="form-select-modal" data-placeholder="Select Activity Type" id="Activetype">
                                        <option value=""></option>
                                        <option value="1">Application</option>
                                        <option value="2">DB</option>
                                        <option value="3">Virtual</option>
                                    </select>
                                </div>
                                <input asp-for="TypeName" id="ActiveTypeName" type="hidden" class="form-control" />
                                <span asp-validation-for="Name" id="SelectActiveTypeError"></span>
                            </div>
                            <div class="form-group" id="DataTypeCol">
                                <label class="form-label">Database Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-database"></i></span>
                                    <select id="SelectDatabaseType" class="form-select-modal" data-placeholder="Select Database Type">
                                    </select>
                                </div>
                                <span asp-validation-for="Name" id="DatbaseTypeError"></span>
                                <input asp-for="SubTypeId" id="DatabaseId" type="hidden" class="form-control" />
                                <input asp-for="SubType" id="databaseText" type="hidden" class="form-control" />
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group" id="NearDRChecked">
                                <label class="form-label">Site Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-web"></i></span>
                                    <select id="siteType" class="form-select-modal" data-placeholder="Select Site Type" multiple="multiple" aria-label="Default select example">
                                    </select>
                                </div>
                                <span id="siteTypeError"></span>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Replication Category</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                    <select id="SelectReplicationType" class="form-select-modal" data-placeholder="Select Replication Category" aria-label="Default select example">
                                    </select>
                                </div>
                                <span asp-validation-for="Name" id="SelectReplicationTypeError"></span>
                                <input asp-for="ReplicationCategoryTypeId" id="infraReplicationTypeId" type="hidden" class="form-control" />
                                <input asp-for="ReplicationCategoryType" id="ReplicationCategorytext" type="hidden" class="form-control" />
                            </div>
                            <div class="form-group" id="ReplicationTypeInput">
                                <label class="form-label">Replication Type</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-replication-type"></i></span>
                                    <select id="ddlReplicationTypeNameId" class="form-select-modal" data-placeholder="Select Replication Type">
                                        <option value="" disabled selected>Select Replication Type</option>
                                    </select>
                                </div>
                                <span asp-validation-for="Name" id="SelectReplicationError"></span>
                                <input asp-for="ReplicationTypeId" id="ReplicationTypeId" type="hidden" class="form-control" />
                                <input asp-for="ReplicationTypeName" id="ReplicationTypeName" type="hidden" class="form-control" />
                            </div>
                            <div class="d-flex gap-2 mb-2">
                                <div class="form-check form-check-inline">
                                    <input type="hidden" id="pairValue" asp-for="IsPair" />
                                    <input class="form-check-input pair" type="checkbox" id="PairId" value="IsPair">
                                    <label class="form-check-label" for="PairId">Pair</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="hidden" id="AssociateValue" asp-for="IsAssociate" />
                                    <input class="form-check-input pair" type="checkbox" id="InfraId" value="IsAssociate">
                                    <label class="form-check-label" for="InfraId">Associate</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="hidden" id="driftValue" asp-for="IsDrift" value="true" />
                                    <input class="form-check-input" type="checkbox" id="drift" checked>
                                    <label class="form-check-label" for="drift">Drift</label>
                                </div>
                            </div>
                            <div class="form-group" id="pairinfra">
                                <label class="form-label">Pair InfraObject</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                    <select asp-for="PairInfraObjectName" id="SelectPairInfra" class="form-select-modal" data-placeholder="Select Pair InfraObject">
                                        <option value="" disabled selected>Select BusinessFunction Service</option>
                                        @foreach (var InfraObjects in Model.InfraObjects)
                                        {
                                            <option infraid="@InfraObjects.Id" value="@InfraObjects.Name">@InfraObjects.Name</option>
                                        }
                                    </select>
                                </div>
                                <span asp-validation-for="Name" id="SelectPairInfraError"></span>
                                <input asp-for="PairInfraObjectId" id="PairInfraId" type="hidden" class="form-control" />
                            </div>
                            <div class="form-group" id="associate">
                                <label class="form-label">Associate InfraObject</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="cp-infra-object"></i></span>
                                    <select asp-for="IsAssociateInfraObjectName" id="SelectAssociate" class="form-select-modal" data-placeholder="Select Associate InfraObject">
                                        <option value="" disabled selected>Select BusinessFunction Service</option>
                                        @foreach (var InfraObjects in Model.InfraObjects)
                                        {
                                            <option Acid="@InfraObjects.Id" value="@InfraObjects.Name">@InfraObjects.Name</option>
                                        }
                                    </select>
                                </div>
                                <span asp-validation-for="Name" id="SelectAssociateError"></span>
                                <input asp-for="IsAssociateInfraObjectId" id="AssociateId" type="hidden" class="form-control" />
                            </div>
                            <div class="form-group w-50">
                                <div class="form-label mb-2">Priority</div>
                                <div class="btn-group d-flex justify-content-between gap-2" role="group" aria-label="Basic radio toggle button group">

                                    <input asp-for="Priority" type="radio" value="High" class="btn-check" name="Priority" id="btnradio1" autocomplete="off" required>
                                    <label class="btn btn-outline-danger rounded-1 btn-sm" for="btnradio1"><i class="cp-up-doublearrow fw-semibold me-2 fs-8"></i>High</label>
                                    <input asp-for="Priority" type="radio" value="Medium" class="btn-check" name="Priority" id="btnradio2" autocomplete="off" required>
                                    <label class="btn btn-outline-warning rounded-1 btn-sm" for="btnradio2"><i class="cp-equal fw-semibold me-2 fs-8"></i>Medium</label>
                                    <input asp-for="Priority" type="radio" value="Low" class="btn-check" name="Priority" id="btnradio3" autocomplete="off" required>
                                    <label class="btn btn-outline-info rounded-1 btn-sm" for="btnradio3"><i class="cp-down-doublearrow fw-semibold me-2 fs-8"></i>Low</label>
                                </div>
                                <input asp-for="CompanyId" id="CompanyId" type="hidden" value="@WebHelper.UserSession.CompanyId" />
                                <input asp-for="DRReady" id="DREnable" type="hidden" class="form-control" />
                                <input asp-for="NodeProperties" id="NodePropertiesData" type="hidden" class="form-control" />
                            </div>
                        </div>
                    </div>
                </section>
                <h6>
                    <span class="step">
                        <i class="cp-settings"></i>
                    </span>
                    <span class="step_title">
                        Establish Relation
                    </span>
                </h6>
                <section>
                    <table class="table" style="table-layout:fixed;">
                        <thead>
                            <tr>
                                <th style="width:10%;">Components</th>
                                <th id="tableServer">Server</th>
                                <th id="tablesrm">SRM Server</th>
                                <th id="tabledatabase">Database</th>
                                <th id="tablereplication">Replication</th>
                            </tr>
                        </thead>
                        <tbody id="infraDynamicBody">
                            <tr class="align-middle" id="Prtable">
                                <td>Production</td>
                                <td class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group" id="PRServerBody">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select id="SelectServerName" class="form-select-modal PRServerName" data-placeholder="Select Server">
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <div class="input-group d-none" id="PRMultipleServerBody">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select class="form-select-modal PRServerName" id="PRMultipleServer" data-placeholder="Select Server" multiple>
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <span asp-validation-for="Name" id="SelectServerNameError"></span>
                                        @* <input asp-for="PRServerName" id="prServerName" type="hidden" class="form-control" /> *@
                                        @* <input asp-for="PRServerId" id="infrServerNameId" type="hidden" class="form-control" /> *@
                                    </div>
                                </td>
                                <td id="prSrm" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select id="prSrmServer" class="form-select-modal srmServer" name='SRMServer' data-placeholder="Select Server ">
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <span id="prSrmServerError"></span>
                                    </div>
                                </td>
                                <td id="prdatabase" class="text-truncate">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="form-group flex-fill " style="width:50px">
                                            <div class="input-group" id="PRDatabaseBody">
                                                <span class="input-group-text"><i class="cp-database"></i></span>
                                                <select id="PRSelectDatabase" class="form-select-modal PRDatabaseName" data-placeholder="Select Database ">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                            <div class="input-group d-none" id="PRMultipleDatabaseBody">
                                                <span class="input-group-text"><i class="cp-database"></i></span>
                                                <select class="form-select-modal PRDatabaseName" id="PRMultipleDatabase" data-placeholder="Select Database" multiple>
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                            <span asp-validation-for="Name" id="PRSelectDatabaseError"></span>
                                            @* <input asp-for="PRDatabaseName" id="prDatabaseName" type="hidden" class="form-control" /> *@
                                            @* <input asp-for="PRDatabaseId" id="PRSelectDatabaseId" type="hidden" class="form-control" /> *@
                                        </div>
                                    </div>
                                </td>
                                <td id="PRReplication" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                            <select  id="PRSelectReplicationName" class="form-select-modal" data-placeholder="Select Replication ">
                                                <option value=""></option>
                                                @foreach (var ReplicationNames in Model.ReplicationNames)
                                                {
                                                    <option prRId="@ReplicationNames.Id" value="@ReplicationNames.Name">@ReplicationNames.Name</option>
                                                }
                                            </select>
                                        </div>
                                        <span asp-validation-for="Name" id="SelectReplicationNameError"></span>
                                        @* <input asp-for="PRReplicationId" id="PRReplicationName" type="hidden" class="form-control" /> *@
                                    </div>
                                </td>
                            </tr>
                            <tr class="align-middle" id="DRCol">
                                <td>DR</td>
                                <td class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group" id="DRServerBody">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select id="DRSelectServerNames" class="form-select-modal DRServerName" data-placeholder="Select Server ">
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <div class="input-group d-none" id="DRMultipleServerBody">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select class="form-select-modal DRServerName" id="DRMultipleServer" data-placeholder="Select Server" multiple>
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <span asp-validation-for="Name" id="DRSelectServerNamesError"></span>
                                        @* <input asp-for="DRServerName" id="drServerName" type="hidden" class="form-control" /> *@
                                        @* <input asp-for="DRServerId" id="DRSelectServerNamesId" type="hidden" class="form-control" /> *@
                                    </div>
                                </td>
                                <td id="drSrm" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select id="drSrmServer" class="form-select-modal srmServer" name='SRMServer' data-placeholder="Select Server ">
                                                <option value=""></option>
                                            </select>
                                        </div>
                                        <span id="drSrmServerError"></span>
                                    </div>
                                </td>
                                <td id="drdatabase" class="text-truncate">
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="form-group flex-fill" style="width:50px">
                                            <div class="input-group" id="DRDatabaseBody">
                                                <span class="input-group-text"><i class="cp-database"></i></span>
                                                <select id="DRSelectDatabase"
                                                        class="form-select-modal DRDatabaseName" data-placeholder="Select Database ">
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                            <div class="input-group d-none" id="DRMultipleDatabaseBody">
                                                <span class="input-group-text"><i class="cp-database"></i></span>
                                                <select class="form-select-modal DRDatabaseName" id="DRMultipleDatabase" data-placeholder="Select Database" multiple>
                                                    <option value=""></option>
                                                </select>
                                            </div>
                                            <span asp-validation-for="Name" id="DRSelectDatabaseNamesError"></span>
                                            @* <input asp-for="DRDatabaseName" id="drDatabaseName" type="hidden" class="form-control" /> *@
                                            @* <input asp-for="DRDatabaseId" id="infraDatabaseId" type="hidden" class="form-control" /> *@
                                        </div>
                                    </div>
                                </td>
                                <td id="DRReplication" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-replication-on"></i></span>
                                            <select  id="SelectReplicationNames" class="form-select-modal" data-placeholder="Select Replication ">
                                                <option value=""></option>
                                                @foreach (var ReplicationNames in Model.ReplicationNames)
                                                {
                                                    <option drRId="@ReplicationNames.Id" value="@ReplicationNames.Name">@ReplicationNames.Name</option>
                                                }
                                            </select>
                                        </div>
                                        <span asp-validation-for="Name" id="DRSelectReplicationNamesError"></span>
                                        @* <input asp-for="DRReplicationId" id="drReplicationNameId" type="hidden" class="form-control" /> *@
                                    </div>
                                </td>
                            </tr>
                            <tr class="align-middle" id="clusterCol">
                                <td>
                                    <div class="form-group">
                                        <div class="input-group" style="border-bottom: none">
                                            <span class="input-group-text">Cluster</span>
                                            <input name='cluster' class="ms-2 srmServer" type='checkbox' id="cluster" />
                                        </div>
                                        <span id="clusterError"></span>
                                    </div>
                                </td>
                                <td id="clusterProperties" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select class="form-select-modal srmServer" data-placeholder="Select Server " id="clusterType" name="cluster">
                                                <option value=""></option>
                                                <option value="veritas">Veritas</option>
                                                <option value="HACMP">HACMP</option>
                                            </select>
                                        </div>
                                        <span id="clusterTypeError"></span>
                                    </div>
                                </td>
                            </tr>
                            <tr class="align-middle" id="selectedClusterCol">
                                <td></td>
                                <td id="veritas-child" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select name='clusters' id="clusterPR" class="form-select-modal srmServer" data-placeholder="Select Server">
                                            </select>
                                        </div>
                                        <span id="clusterPRError"></span>
                                    </div>
                                </td>
                                <td id="HACMP-child" class="text-truncate">
                                    <div class="form-group">
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="cp-server"></i></span>
                                            <select name='clusters' id="clusterDR" class="form-select-modal srmServer" data-placeholder="Select Server">
                                            </select>
                                        </div>
                                        <span id="clusterDRError"></span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <input asp-for="State" type="hidden" class="form-control" id="StateValue" />
                    <input asp-for="NodeProperties" id="nodeProperties" type="hidden" class="form-control" />
                    <input asp-for="ServerProperties" id="serverProperties" type="hidden" class="form-control" />
                    <input asp-for="DatabaseProperties" id="databaseProperties" type="hidden" class="form-control" />
                    <input asp-for="ReplicationProperties" id="replicationProperties" type="hidden" class="form-control" />
                    <input asp-for="SiteProperties" id="siteProperties" type="hidden" class="form-control" />
                </section>
                <h6>
                    <span class="step">
                        <i class="cp-summary"></i>
                    </span>
                    <span class="step_title">
                        Summary
                    </span>
                </h6>
                <section>
                    <span class="fs-6">1. InfraObject Summary</span>
                    <table class="table" style="table-layout: fixed;">
                        <tbody>
                            <tr>
                                <td class="text-secondary">Name</td>
                                <td class="text-truncate" id="infraObjectTableName"></td>
                                <td class="text-secondary">Description</td>
                                <td class="text-truncate" id="DescriptionSum"></td>
                            </tr>
                            <tr>
                                <td class="text-secondary">Operational Service</td>
                                <td class="text-truncate" id="BusinessServiceSum"></td>
                                <td class="text-secondary">Operational Function</td>
                                <td class="text-truncate" id="BusinessFunSum"></td>
                            </tr>
                            <tr>
                                <td class="text-secondary">Activity Type</td>
                                <td id="TypeSum"></td>
                                <td class="text-secondary">Database Type</td>
                                <td id="database_type"></td>
                            </tr>
                            <tr>
                                <td class="text-secondary">Site Types</td>
                                <td class="text-truncate" id="site_category"></td>
                                <td class="text-secondary">Priority</td>
                                <td class="text-truncate" id="PrioritySum"></td>
                            </tr>
                            <tr>
                                <td class="text-secondary">Replication Name</td>
                                <td class="text-truncate" id="replication_name"></td>
                                <td class="text-secondary">Replication Type</td>
                                <td class="text-truncate" id="ReplicationNameSum"></td>
                            </tr>
                            <tr>
                                <td class="text-secondary">IsPair</td>
                                <td id="IsPairSum"></td>
                                <td class="text-secondary">IsAssociate</td>
                                <td id="IsAssociateSum"></td>
                            </tr>
                        </tbody>
                    </table>
                    <span class="fs-6">2. Establish Relation</span>
                    <table class="table">
                        <thead class="thead-light">
                            <tr id="summary_header">
                                <th>Type</th>
                                <th id="pr_summary_header">Production</th>
                                <th id="dr_summary_header">DR</th>
                            </tr>
                        </thead>
                        <tbody id="infraSummary">
                            <tr id="serverTbody">
                                <td class="text-secondary">Server</td>
                                <td id="PRServerSum">NA</td>
                                <td id="DRServerSum">NA</td>
                            </tr>
                            <tr id='srmServerTbody'>
                                <td class="text-secondary">SRM Server</td>
                                <td id="PRSrmServerSum">NA</td>
                                <td id="DRSrmServerSum">NA</td>
                            </tr>
                            <tr id="databaseTbody">
                                <td class="text-secondary">Database</td>
                                <td id="PRDataBaseSum">NA</td>
                                <td id="DRDataBaseSum">NA</td>
                            </tr>
                            <tr id="replicationTbody">
                                <td class="text-secondary">Replication</td>
                                <td id="PRReplicationSum">NA</td>
                                <td id="DRReplicationSum">NA</td>
                            </tr>
                            <tr id='clusterServerTbody'>
                                <td class="text-secondary">Cluster</td>
                                <td id="PRClusterSum">NA</td>
                                <td id="DRClusterSum">NA</td>
                            </tr>
                        </tbody>
                    </table>
                </section>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-between">
            <small class="text-secondary ModalFooter-Note-Text"><i class="cp-note me-1"></i>Note: All fields are mandatory except optional</small>
            <div>
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                <a class="btn btn-primary prev_btn btn-sm" href="javascript:void(0)" role="menuitem" onclick="form.steps('previous')">Previous</a>
                <a id="Next" class="btn btn-primary next_btn btn-sm" href="javascript:void(0)" role="menuitem">Next</a>
                <a id="SaveFunction" type="button" class="btn btn-primary finish_btn btn-sm" href="javascript:void(0)" role="menuitem" onclick="form.steps('finish')">Save</a>
            </div>
        </div>
    </form>
</div>
