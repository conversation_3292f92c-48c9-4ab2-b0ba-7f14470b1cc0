using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class BulkImportActionResultRepository : BaseRepository<BulkImportActionResult>, IBulkImportActionResultRepository
{
    private readonly ApplicationDbContext _dbContext;

    public BulkImportActionResultRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) 
        : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
    }

    public async Task<BulkImportActionResult> GetByEntityIdAndBulkImportOperationId(string entityId, string bulkImportOperationId)
    {
        return await _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => x.EntityId==entityId && x.BulkImportOperationId == bulkImportOperationId)
            .Select(MapToBulkImportActionResultSelector())
            .FirstOrDefaultAsync();
    }

    public async Task<List<BulkImportActionResult>> GetByOperationIdsAndOperationGroupIds(List<string> operationIds, List<string> operationGroupIds)
    {
        return await _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => operationIds.Contains(x.BulkImportOperationId) && operationGroupIds.Contains(x.BulkImportOperationGroupId))
            .OrderBy(x => x.Id)
            .Select(MapToBulkImportActionResultSelector())
            .ToListAsync();
    }
    public async Task<List<BulkImportActionResult>>GetByOperationIdAndOperationGroupId(string operationId, string operationGroupId)
    {
        return await _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => x.BulkImportOperationId == operationId && x.BulkImportOperationGroupId == operationGroupId)
            .OrderBy(x => x.Id)
            .Select(MapToBulkImportActionResultSelector())
            .ToListAsync();
    }


    public async Task<List<BulkImportActionResult>> GetBulkImportActionResultOperationGroupId(string operationGroupId)
    {
        return await _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => x.BulkImportOperationGroupId == operationGroupId)
            .OrderBy(x => x.Id)
            .Select(MapToBulkImportActionResultSelector())
            .ToListAsync();
    }


    public async Task<BulkImportActionResult> GetActionByOperationGroupIdAndEntityType(string operationGroupId, string entityType)
    {
        return await _dbContext.BulkImportActionResults
            .AsNoTracking()
            .Active()
            .Where(x => x.BulkImportOperationGroupId == operationGroupId && x.EntityType.ToLower() == entityType.ToLower())
            .Select(MapToBulkImportActionResultSelector())
            .FirstOrDefaultAsync();
    }

    private static Expression<Func<BulkImportActionResult, BulkImportActionResult>> MapToBulkImportActionResultSelector()
    {
        return x => new BulkImportActionResult
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            CompanyId = x.CompanyId,
            NodeId = x.NodeId,
            NodeName = x.NodeName,
            BulkImportOperationId = x.BulkImportOperationId,
            BulkImportOperationGroupId = x.BulkImportOperationGroupId,
            EntityId = x.EntityId,
            EntityType = x.EntityType,
            EntityName = x.EntityName,
            ConditionalOperation = x.ConditionalOperation,
            Status = x.Status,
            ErrorMessage = x.ErrorMessage,
            StartTime = x.StartTime,
            EndTime = x.EndTime
        };
    }
}
