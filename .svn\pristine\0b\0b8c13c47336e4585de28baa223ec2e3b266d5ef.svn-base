﻿using ContinuityPatrol.Application.Features.Replication.Events.LicenseInfoEvents.Update;
using ContinuityPatrol.Application.Features.Replication.Events.Update;

namespace ContinuityPatrol.Application.Features.Replication.Commands.Update;

public class UpdateReplicationCommandHandler : IRequestHandler<UpdateReplicationCommand, UpdateReplicationResponse>
{
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;

    public UpdateReplicationCommandHandler(IMapper mapper, IReplicationRepository replicationRepository,
        IPublisher publisher, ISiteRepository siteRepository, ISiteTypeRepository siteTypeRepository)
    {
        _mapper = mapper;
        _replicationRepository = replicationRepository;
        _publisher = publisher;
        _siteRepository = siteRepository;
        _siteTypeRepository = siteTypeRepository;
    }

    public async Task<UpdateReplicationResponse> Handle(UpdateReplicationCommand request,
        CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, $"Invalid Replication Id '{request.Id}'");

        var eventToUpdate = await _replicationRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Replication), request.Id);

        if (request.LicenseId != "NA") request.LicenseKey = SecurityHelper.Encrypt(request.LicenseKey);

        _mapper.Map(request, eventToUpdate, typeof(UpdateReplicationCommand), typeof(Domain.Entities.Replication));

        await _replicationRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateReplicationResponse
        {
            Message = Message.Update(nameof(Domain.Entities.Replication), eventToUpdate.Name),

            ReplicationId = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ReplicationUpdatedEvent { ReplicationName = eventToUpdate.Name },
            cancellationToken);

        if (!request.Type.ToLower().Contains("perpetuuiti")) return response;

        var site = await _siteRepository.GetByReferenceIdAsync(eventToUpdate.SiteId);

        var siteType = await _siteTypeRepository.GetByReferenceIdAsync(site.TypeId);

        if (siteType.Category.ToLower().Contains("primary"))
            await _publisher.Publish(new ReplicationLicenseInfoUpdatedEvent
            {
                Id = eventToUpdate.ReferenceId,
                EntityName = eventToUpdate.Name,
                LicenseId = eventToUpdate.LicenseId,
                PONumber = eventToUpdate.LicenseKey,
                EntityId = eventToUpdate.ReferenceId,
                Type = eventToUpdate.Type,
                EntityField = eventToUpdate.SiteName,
                BusinessServiceId = eventToUpdate.BusinessServiceId,
                BusinessServiceName = eventToUpdate.BusinessServiceName,
                Category = eventToUpdate.Type,
                Logo = request.Logo
            }, cancellationToken);

        return response;
    }
}