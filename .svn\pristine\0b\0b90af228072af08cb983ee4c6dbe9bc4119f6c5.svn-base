﻿using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;

public class
    GetSitePaginatedListQueryHandler : IRequestHandler<GetSitePaginatedListQuery, PaginatedResult<SiteListVm>>
{
    private readonly IMapper _mapper;
    private readonly ISiteRepository _siteRepository;

    public GetSitePaginatedListQueryHandler(IMapper mapper, ISiteRepository siteRepository)
    {
        _mapper = mapper;
        _siteRepository = siteRepository;
    }

    public async Task<PaginatedResult<SiteListVm>> Handle(GetSitePaginatedListQuery request,
        CancellationToken cancellationToken)
    {
        var productFilterSpec = new SiteFilterSpecification(request.SearchString);
        
        var queryable =await _siteRepository.PaginatedListAllAsync(request.PageNumber, request.PageSize, productFilterSpec, request.SortColumn, request.SortOrder);
       
        var sitesList = _mapper.Map<PaginatedResult<SiteListVm>>(queryable);
        
        return sitesList;
    }
}