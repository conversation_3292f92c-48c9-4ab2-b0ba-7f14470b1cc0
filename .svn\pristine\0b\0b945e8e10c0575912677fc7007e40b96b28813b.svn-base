﻿using ContinuityPatrol.Domain.ViewModels.WorkflowOperationGroupModel;

namespace ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetRunningStatus;

public class WorkflowOperationGroupRunningStatusVm
{
    public string Id { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string ActionMode { get; set; }
    public bool IsDrCalendar { get; set; }
    public List<WorkflowOperationGroupListVm> WorkflowOperationGroupListVm { get; set; } = new();
    public WorkflowActionStatusCount WorkflowActionStatusCount { get; set; } = new();
}

public class WorkflowActionStatusCount
{
    public string WorkflowOperationId { get; set; }
    public int RunningCount { get; set; }
    public int ErrorCount { get; set; }
    public int SuccessCount { get; set; }
    public int SkipCount { get; set; }
    public int BypassedCount { get; set; }
}