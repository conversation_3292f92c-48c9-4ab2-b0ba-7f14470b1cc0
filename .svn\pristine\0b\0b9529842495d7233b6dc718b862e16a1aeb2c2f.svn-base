﻿
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}



<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title"><i class="cp-monitoring"></i><span>Storage_MSSQL_FullDB_EMCSRDF_SG</span></h6>
        <span><i class="cp-"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Replication Monitor</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Parameter Name</th>
                                    <th class="text-primary">Primary </th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-list-prsite me-1"></i>SG Name</td>
                                    <td class="text-truncate">MSSQL 2k19 NLS Ser Prod</td>
                                    <td class="text-truncate">MSSQL 2k19_NLS_Ser_DR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-server-ip me-1"></i>SID</td>
                                    <td class="text-truncate">**************</td>
                                    <td class="text-truncate">**************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-database me-1"></i>SRP Name</td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-file-location me-1"></i>RDFG</td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-storage-name me-1"></i>Tracks not Committed to the R2 side </td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-apply-finish-time me-1"></i>Time that is behind R1 </td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
            <div class="col-5 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <img src="~/img/isomatric/solutiondiagram.svg" height="259px;" />
                    </div>
                </div>
                <div class="card Card_Design_None">
                    <div class="card-header card-title">Component Monitor</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Component Monitor</th>
                                    <th class="text-primary">Primary </th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-list-prsite me-1"></i>Server Name</td>
                                    <td class="text-truncate">MSSQL 2k19 NLS Ser Prod</td>
                                    <td class="text-truncate">MSSQL 2k19_NLS_Ser_DR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-server-ip me-1"></i>MSSQL IPAddress</td>
                                    <td class="text-truncate">**************</td>
                                    <td class="text-truncate">**************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-database  me-1"></i>MSSQL Database Name </td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-file-location me-1"></i>MSSQL Database State</td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold" ><i class="text-secondary cp-database-success me-1"></i>Database Restrict Access Status</td>
                                    <td class="text-truncate">N/A</td>
                                    <td class="text-truncate">N/A</td>
                                </tr>                              
                            </tbody>

                        </table>
                    </div>
                </div>
        
            </div>
        </div>


    </div>
</div>




