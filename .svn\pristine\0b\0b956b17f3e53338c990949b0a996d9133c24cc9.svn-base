﻿using ContinuityPatrol.Application.Features.BiaRules.Commands.Create;
using ContinuityPatrol.Application.Features.BiaRules.Commands.Update;
using ContinuityPatrol.Application.Features.BiaRules.Events.PaginatedView;
using ContinuityPatrol.Domain.ViewModels.BiaRulesModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class BiaRulesController : Controller
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;
    private readonly ILogger<BiaRulesController> _logger;
    public BiaRulesController(IPublisher publisher, ILogger<BiaRulesController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _dataProvider = dataProvider;
        _mapper = mapper;
        _logger = logger;
    }

    [EventCode(EventCodes.BiaRules.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in BIARules");
        await _publisher.Publish(new BiaRulesPaginatedEvent());
        return View();
    }

    [EventCode(EventCodes.BiaRules.BiaImpactList)]
    public async Task<IActionResult> GetBiaImpactList()
    {
        _logger.LogDebug("Entering List method in BIARules");
        try
        {
            var biaImpactListVm = await _dataProvider.BiaRule.GetBiaImpactList();
            _logger.LogDebug("Successfully retrieved Bia Impact list in BIARules page");
            return Json(new { Success = true, data = biaImpactListVm });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page while retrieving the list.", ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [AntiXss]
    [ValidateAntiForgeryToken]
    [EventCode(EventCodes.BiaRules.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(BiaRulesViewModel impactViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in BIARules");
        var id = Request.Form["id"].ToString();
        try
        {
            BaseResponse result;
            if (id.IsNullOrWhiteSpace())
            {
                var impact = _mapper.Map<CreateBiaRulesCommand>(impactViewModel);
                result = await _dataProvider.BiaRule.CreateAsync(impact);
                _logger.LogDebug($"Creating Database '{impact.Type}'");
            }
            else
            {
                var impact = _mapper.Map<UpdateBiaRulesCommand>(impactViewModel);
                result = await _dataProvider.BiaRule.UpdateAsync(impact);
                _logger.LogDebug($"Updating Database '{impact.Type}'");
            }
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on bia rule page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page while processing the request for create or update.", ex);
            return ex.GetJsonException();
        }
    }

    [EventCode(EventCodes.BiaRules.BiaRulesByEntityIdAndType)]
    public async Task<IActionResult> GetBiaRulesByEntityIdAndType(string entityId, string type)
    {
        _logger.LogDebug("Entering Delete method in BIARules");
        try
        {
            var biaRules = await _dataProvider.BiaRule.GetBiaRulesByEntityIdAndType(entityId, type);
            _logger.LogDebug("Successfully deleted record in BIARules");
            return Json(new { Success = true, data = biaRules });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page while GetBiaRulesByEntityIdAndType record on BIARules.", ex);
            return ex.GetJsonException();
        }

    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.BiaRules.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in BIARules");
        try
        {
            var delete = await _dataProvider.BiaRule.DeleteAsync(id);
            _logger.LogDebug("Successfully deleted record in BIARules");
            return Json(new { Success = true, data = delete });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page while deleting record on BIARules.", ex);
            return ex.GetJsonException();
        }

    }
    [EventCode(EventCodes.BiaRules.BusinessServiceList)]
    public async Task<IActionResult> GetBusinessServiceList()
    {
        _logger.LogDebug("Entering GetBusinessServiceList method in BIARules");
        try
        {
            var businessServiceList = await _dataProvider.BusinessService.GetBusinessServiceList();
            _logger.LogDebug("Successfully retrieved BusinessService list in BIARules page");
            return Json(new { Success = true, data = businessServiceList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page retrieving the businessService list.", ex);
            return ex.GetJsonException();
        }

    }

    [EventCode(EventCodes.BiaRules.BusinessFunctionList)]
    public async Task<IActionResult> GetBusinessFunctionList()
    {
        _logger.LogDebug("Entering GetBusinessFunctionList method in BIARules");
        try
        {
            var list = await _dataProvider.BusinessFunction.GetBusinessFunctionList();
            _logger.LogDebug("Successfully retrieved BusinessFunction list in BIARules page");
            return Json(new { Success = true, data = list });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on BIA Rule page while retrieving the businessFunction list.", ex);
            return ex.GetJsonException();
        }
    }
    [EventCode(EventCodes.BiaRules.InfraObjectByBusinessServiceId)]
    public async Task<IActionResult> GetInfraObjectByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetInfraObjectByBusinessServiceId method in BIARules");
        if (string.IsNullOrWhiteSpace(businessServiceId))
        {
            return Json("");
        }
        else
        {
            try
            {
                var infraObjects = await _dataProvider.InfraObject.GetInfraObjectByBusinessServiceId(businessServiceId);
                _logger.LogDebug($"Successfully retrieved InfraObject by BusinessServiceId '{businessServiceId}' in BIARules page");
                return Json(new { Success = true, data = infraObjects });
            }
            catch (Exception ex)
            {
                _logger.Exception($"An error occurred on BIA Rule page while retrieving the InfraObject by businessServiceId.", ex);
                return Json(new { success = false, message = ex.GetMessage() });
            }
        }
    }
    [EventCode(EventCodes.BiaRules.GetInfraObjectById)]
    public async Task<IActionResult> GetInfraObjectById(string infraObjectId)
    {
        _logger.LogDebug("Entering GetInfraObjectById method in BIARules");
        if (infraObjectId.IsNullOrWhiteSpace())
        {
            return Json("");
        }
        else
        {
            try
            {
                var infraObjects = await _dataProvider.InfraObject.GetInfraObjectDetailsById(infraObjectId);
                _logger.LogDebug($"Successfully retrieved InfraObject by Id '{infraObjectId}' in BIARules page");
                return Json(new { Success = true, data = infraObjects });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on BIA Rule page while retrieving the InfraObject by id.", ex);
                return Json(new { success = false, message = ex.GetMessage() });
            }
        }
    }
    [EventCode(EventCodes.BiaRules.BiaBusinessServiceTreeViewListByBusinessServiceId)]
    public async Task<IActionResult> GetBIABusinessServiceTreeViewListByBusinessServiceId(string businessServiceId)
    {
        _logger.LogDebug("Entering GetBusinessServiceTreeViewListByBusinessServiceId  method in service availability page.");
        if (businessServiceId.IsNullOrWhiteSpace())
        {
            return Json("");
        }
        else
        {
            try
            {
                var result = await _dataProvider.BusinessService.GetBusinessServiceDiagramByBusinessServiceId(businessServiceId);
                return Json(new { Success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.Exception("An error occurred on BIA Rule page while retrieving the GetBusinessServiceTreeViewListByBusinessServiceId by id.", ex);
                return Json(new { success = false, message = ex.GetMessage() });
            }
        }    
    }
}