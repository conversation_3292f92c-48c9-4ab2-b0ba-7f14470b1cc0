using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Delete;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetCyberAlertCount;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetList;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class CyberAlertControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly CyberAlertsController _controller;
    private readonly CyberAlertFixture _cyberAlertFixture;

    public CyberAlertControllerTests()
    {
        _cyberAlertFixture = new CyberAlertFixture();

        var testBuilder = new ControllerTestBuilder<CyberAlertsController>();
        _controller = testBuilder.CreateController(
            _ => new CyberAlertsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetCyberAlerts_ReturnsExpectedList()
    {
        // Arrange
        var expectedCyberAlerts = new List<CyberAlertListVm>
        {
            _cyberAlertFixture.CyberAlertListVm,
            _cyberAlertFixture.CyberAlertListVm,
            _cyberAlertFixture.CyberAlertListVm
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAlertListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedCyberAlerts);

        // Act
        var result = await _controller.GetCyberAlerts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAlerts = Assert.IsAssignableFrom<List<CyberAlertListVm>>(okResult.Value);
        Assert.Equal(3, cyberAlerts.Count);
    }

    [Fact]
    public async Task GetCyberAlertById_ReturnsExpectedDetail()
    {
        // Arrange
        var cyberAlertId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAlertDetailQuery>(q => q.Id == cyberAlertId), default))
            .ReturnsAsync(_cyberAlertFixture.CyberAlertDetailVm);

        // Act
        var result = await _controller.GetCyberAlertById(cyberAlertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAlert = Assert.IsType<CyberAlertDetailVm>(okResult.Value);
        Assert.Equal(_cyberAlertFixture.CyberAlertDetailVm.JobName, cyberAlert.JobName);
    }

    [Fact]
    public async Task GetPaginatedCyberAlerts_ReturnsExpectedResults()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            severity = "Critical"
        };

        var expectedData = new List<CyberAlertListVm>
        {
            _cyberAlertFixture.CyberAlertListVm,
            _cyberAlertFixture.CyberAlertListVm
        };
        var expectedResults = PaginatedResult<CyberAlertListVm>.Success(expectedData, 2, 1, 10);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAlertPaginatedListQuery>(q => 
                q.PageNumber == query.PageNumber && q.PageSize == query.PageSize && q.severity == query.severity), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAlerts(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAlertListVm>>(okResult.Value);
        Assert.Equal(2, paginatedResult.Data.Count);
        Assert.Equal(1, paginatedResult.CurrentPage);
        Assert.Equal(10, paginatedResult.PageSize);
    }

    [Fact]
    public async Task CreateCyberAlert_ReturnsCreatedAtAction()
    {
        // Arrange
        var command = _cyberAlertFixture.CreateCyberAlertCommand;
        var expectedMessage = "CyberAlert has been created successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAlert(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAlertResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAlert_ReturnsOk()
    {
        // Arrange
        var command = _cyberAlertFixture.UpdateCyberAlertCommand;
        var expectedMessage = "CyberAlert has been updated successfully!.";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAlert(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task DeleteCyberAlert_ReturnsOk()
    {
        // Arrange
        var cyberAlertId = Guid.NewGuid().ToString();
        var expectedMessage = "CyberAlert has been deleted successfully!.";

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteCyberAlertCommand>(c => c.Id == cyberAlertId), default))
            .ReturnsAsync(new DeleteCyberAlertResponse
            {
                Message = expectedMessage,
                IsActive = false
            });

        // Act
        var result = await _controller.DeleteCyberAlert(cyberAlertId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeleteCyberAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.False(response.IsActive);
    }

    [Fact]
    public async Task GetCyberAlertsCount_ReturnsExpectedCount()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAlertCountQuery>(), default))
            .ReturnsAsync(_cyberAlertFixture.CyberAlertCountVm);

        // Act
        var result = await _controller.GetCyberAlertsCount();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var countResult = Assert.IsType<CyberAlertCountVm>(okResult.Value);
        Assert.Equal(_cyberAlertFixture.CyberAlertCountVm.TotalCount, countResult.TotalCount);
        Assert.Equal(_cyberAlertFixture.CyberAlertCountVm.AlertSeverityCount.Count, countResult.AlertSeverityCount.Count);
    }

    [Fact]
    public async Task GetCyberAlerts_HandlesEmptyList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAlertListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<CyberAlertListVm>());

        // Act
        var result = await _controller.GetCyberAlerts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAlerts = Assert.IsAssignableFrom<List<CyberAlertListVm>>(okResult.Value);
        Assert.Empty(cyberAlerts);
    }

    [Fact]
    public async Task GetCyberAlertById_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetCyberAlertById(invalidId));
    }

    [Fact]
    public async Task GetCyberAlertById_HandlesNotFound()
    {
        // Arrange
        var cyberAlertId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAlertDetailQuery>(q => q.Id == cyberAlertId), default))
            .ThrowsAsync(new NotFoundException("CyberAlert", cyberAlertId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() => _controller.GetCyberAlertById(cyberAlertId));
    }

    [Fact]
    public async Task DeleteCyberAlert_ThrowsException_WhenInvalidGuid()
    {
        // Arrange
        var invalidId = "invalid-guid";

        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteCyberAlert(invalidId));
    }

    [Fact]
    public async Task CreateCyberAlert_HandlesCriticalAlert()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Type = "Security Breach",
            Severity = "Critical",
            SystemMessage = "Unauthorized access attempt detected on production database server. Multiple failed login attempts from suspicious IP address.",
            UserMessage = "CRITICAL SECURITY ALERT: Potential security breach detected. Access has been temporarily restricted. Security team has been notified immediately.",
            JobName = "Security Monitor",
            ClientAlertId = Guid.NewGuid().ToString(),
            IsResolve = 0,
            IsAcknowledgement = 0,
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "Security System",
            AlertCategoryId = 1
        };

        var expectedMessage = "CyberAlert has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAlert(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAlertResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
    }

    [Fact]
    public async Task UpdateCyberAlert_HandlesAlertResolution()
    {
        // Arrange
        var command = new UpdateCyberAlertCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "System Recovery",
            Severity = "Info",
            SystemMessage = "System has recovered from previous critical error. All services are now operational and performance is within normal parameters.",
            UserMessage = "System recovery complete. All services have been restored and are functioning normally.",
            JobName = "System Recovery Monitor",
            ClientAlertId = Guid.NewGuid().ToString(),
            IsResolve = 1,
            IsAcknowledgement = 1,
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "System Recovery",
            AlertCategoryId = 3
        };

        var expectedMessage = "CyberAlert has been updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAlert(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(command.Id, response.Id);
    }

    [Fact]
    public async Task GetPaginatedCyberAlerts_HandlesAdvancedFiltering()
    {
        // Arrange
        var query = new GetCyberAlertPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 25,
            SearchString = "Security",
            severity = "Critical"
        };

        var expectedData = new List<CyberAlertListVm>
        {
            new CyberAlertListVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Security Alert",
                Severity = "Critical",
                SystemMessage = "Critical security event detected",
                IsResolve = 0,
                EntityType = "Security System"
            }
        };
        var expectedResults = PaginatedResult<CyberAlertListVm>.Success(expectedData, 1, 2, 25);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetCyberAlertPaginatedListQuery>(q =>
                q.PageNumber == query.PageNumber &&
                q.PageSize == query.PageSize &&
                q.SearchString == query.SearchString &&
                q.severity == query.severity), default))
            .ReturnsAsync(expectedResults);

        // Act
        var result = await _controller.GetPaginatedCyberAlerts(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<CyberAlertListVm>>(okResult.Value);
        Assert.Single(paginatedResult.Data);
        Assert.Equal("Critical", paginatedResult.Data.First().Severity);
        Assert.Equal(2, paginatedResult.CurrentPage);
        Assert.Equal(25, paginatedResult.PageSize);
    }

    [Fact]
    public async Task GetCyberAlertsCount_HandlesDetailedBreakdown()
    {
        // Arrange
        var expectedCount = _cyberAlertFixture.CyberAlertCountVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAlertCountQuery>(), default))
            .ReturnsAsync(expectedCount);

        // Act
        var result = await _controller.GetCyberAlertsCount();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var alertCount = Assert.IsType<CyberAlertCountVm>(okResult.Value);
        Assert.Equal(_cyberAlertFixture.CyberAlertCountVm.TotalCount, alertCount.TotalCount);
        Assert.Equal(_cyberAlertFixture.CyberAlertCountVm.AlertSeverityCount.Count, alertCount.AlertSeverityCount.Count);
    }

    [Fact]
    public async Task CreateCyberAlert_HandlesInfrastructureAlert()
    {
        // Arrange
        var command = new CreateCyberAlertCommand
        {
            Type = "Infrastructure Failure",
            Severity = "High",
            SystemMessage = "Primary database server experiencing high CPU utilization and memory pressure. Performance degradation detected.",
            UserMessage = "Infrastructure alert: Database performance issues detected. Monitoring team investigating.",
            JobName = "Infrastructure Monitor",
            ClientAlertId = Guid.NewGuid().ToString(),
            IsResolve = 0,
            IsAcknowledgement = 0,
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "Database Server",
            AlertCategoryId = 2
        };

        var expectedMessage = "Infrastructure CyberAlert has been created successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new CreateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = Guid.NewGuid().ToString()
            });

        // Act
        var result = await _controller.CreateCyberAlert(command);

        // Assert
        var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var response = Assert.IsType<CreateCyberAlertResponse>(createdAtActionResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.NotNull(response.Id);
    }

    [Fact]
    public async Task UpdateCyberAlert_HandlesAcknowledgement()
    {
        // Arrange
        var command = new UpdateCyberAlertCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "Acknowledged Alert",
            Severity = "Medium",
            SystemMessage = "Alert has been acknowledged by operations team. Investigation in progress.",
            UserMessage = "Alert acknowledged. Operations team is investigating the issue.",
            JobName = "Alert Management",
            ClientAlertId = Guid.NewGuid().ToString(),
            IsResolve = 0,
            IsAcknowledgement = 1,
            EntityId = Guid.NewGuid().ToString(),
            EntityType = "Operations",
            AlertCategoryId = 2
        };

        var expectedMessage = "CyberAlert acknowledgement updated successfully!.";
        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(new UpdateCyberAlertResponse
            {
                Message = expectedMessage,
                Id = command.Id
            });

        // Act
        var result = await _controller.UpdateCyberAlert(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<UpdateCyberAlertResponse>(okResult.Value);
        Assert.Equal(expectedMessage, response.Message);
        Assert.Equal(1, command.IsAcknowledgement);
        Assert.Equal(0, command.IsResolve);
    }

    [Fact]
    public async Task GetCyberAlerts_HandlesPerformanceWithLargeDataset()
    {
        // Arrange
        var largeAlertList = new List<CyberAlertListVm>();
        for (int i = 0; i < 2000; i++)
        {
            largeAlertList.Add(new CyberAlertListVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = $"Alert Type {i % 10}",
                Severity = i % 4 == 0 ? "Critical" : i % 3 == 0 ? "High" : "Medium",
                SystemMessage = $"System alert message {i + 1}",
                IsResolve = i % 3,
                EntityType = i % 2 == 0 ? "Server" : "Database"
            });
        }

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetCyberAlertListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(largeAlertList);

        // Act
        var result = await _controller.GetCyberAlerts();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var cyberAlerts = Assert.IsAssignableFrom<List<CyberAlertListVm>>(okResult.Value);
        Assert.Equal(2000, cyberAlerts.Count);
        Assert.Contains(cyberAlerts, alert => alert.Severity == "Critical");
        Assert.Contains(cyberAlerts, alert => alert.EntityType == "Server");
        Assert.Contains(cyberAlerts, alert => alert.EntityType == "Database");
    }
}
