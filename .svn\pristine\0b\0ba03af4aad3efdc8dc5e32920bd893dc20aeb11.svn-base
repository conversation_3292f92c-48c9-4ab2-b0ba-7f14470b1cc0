﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Dashboard;

public class HeatMapStatusService : BaseClient, IHeatMapStatusService
{
    public HeatMapStatusService(IConfiguration config, IAppCache cache, ILogger<HeatMapStatusService> logger) : base(config, cache, logger)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateHeatMapStatusCommand createHeatMapStatusCommand)
    {
        var request = new RestRequest("api/v6/heatmapstatus", Method.Post);

        request.AddJsonBody(createHeatMapStatusCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateHeatMapStatusCommand updateHeatMapStatusCommand)
    {
        var request = new RestRequest("api/v6/heatmapstatus", Method.Put);

        request.AddJsonBody(updateHeatMapStatusCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<HeatMapStatusDetailVm> GetHeatMapStatusById(string id)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/{id}");

        return await Get<HeatMapStatusDetailVm>(request);
    }

    public async Task<List<HeatMapStatusListVm>> GetHeatMapStatus()
    {
        var request = new RestRequest($"api/v6/heatmapstatus");

        return await GetFromCache<List<HeatMapStatusListVm>>(request, "GetHeatMapStatus");
    }

    public async Task<HeatMapStatusByInfraObjectandEntityIdVm> GetHeatMapStatusByInfraObjectIdAndEntityId(string? infraObjectId, string? entityId)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/getheatmapstatusbyinfraobjectandentityid?infraObjectId={infraObjectId} & entityId={entityId}");

        return await Get<HeatMapStatusByInfraObjectandEntityIdVm>(request);
    }

    public async Task<List<HeatMapStatusListVm>> GetHeatMapStatusByType(string? businessServiceId,string type, bool isAffected)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/by/type?businessServiceId={businessServiceId}&type={type}&isAffected{isAffected}");

        return await Get<List<HeatMapStatusListVm>>(request);
    }

    public async Task<ImpactDetailVm> GetImpactDetail(string? businessServiceId)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/impact-detail?businessServiceId={businessServiceId}");

        return await GetFromCache<ImpactDetailVm>(request, "GetHeatMapStatus");
    }

    public async Task<BusinessServiceAvailabilityVm> GetBusinessServiceAvailability()
    {
        var request = new RestRequest($"api/v6/heatmapstatus/businessservice-availability");

        return await Get<BusinessServiceAvailabilityVm>(request);
    }

    public async Task<BusinessFunctionAvailabilityVm> GetBusinessFunctionAvailability()
    {
        var request = new RestRequest($"api/v6/heatmapstatus/businessfunction-availability");

        return await Get<BusinessFunctionAvailabilityVm>(request);
    }
    public async Task<PaginatedResult<HeatMapStatusListVm>> GetPaginatedHeatMapStatus(GetHeatMapStatusPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/heatmapstatus/paginated-list?Type={query}");

        return await Get<PaginatedResult<HeatMapStatusListVm>>(request);
    }
}