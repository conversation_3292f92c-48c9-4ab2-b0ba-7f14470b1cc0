﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.UserRole.Events.Update;

public class UserRoleUpdatedEventHandler : INotificationHandler<UserRoleUpdatedEvent>
{
    private readonly ILogger<UserRoleUpdatedEventHandler> _logger;

    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public UserRoleUpdatedEventHandler(ILoggedInUserService userService, ILogger<UserRoleUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;

        _logger = logger;

        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(UserRoleUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userRoleActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Entity = Modules.UserRole.ToString(),
            Action = $"{ActivityType.Update} {Modules.UserRole}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"UserRole '{updatedEvent.UserRoleName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userRoleActivity);

        _logger.LogInformation($"UserRole '{updatedEvent.UserRoleName}' updated successfully.");
    }
}