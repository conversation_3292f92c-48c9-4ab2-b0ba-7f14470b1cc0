﻿using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.RoboCopy.Queries;

public class GetRoboCopyPaginatedListQueryHandlerTests
{
    private readonly Mock<IRoboCopyRepository> _mockRoboCopyRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetRoboCopyPaginatedListQueryHandler _handler;

    public GetRoboCopyPaginatedListQueryHandlerTests()
    {
        _mockRoboCopyRepository = new Mock<IRoboCopyRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetRoboCopyPaginatedListQueryHandler(_mockMapper.Object, _mockRoboCopyRepository.Object);
    }

 

    [Fact]
    public async Task Handle_ShouldApplySearchFilter_WhenSearchStringIsProvided()
    {
        var query = new GetRoboCopyPaginatedListQuery { PageNumber = 1, PageSize = 5, SearchString = "Test" };
        var roboCopies = new List<Domain.Entities.RoboCopy>
{
new Domain.Entities.RoboCopy { Id = 1, Name = "Test1", ReferenceId = "Ref1" },
new Domain.Entities.RoboCopy { Id = 2, Name = "Another", ReferenceId = "Ref2" }
};

        var paginatedResult = new PaginatedResult<Domain.Entities.RoboCopy>
        {
            TotalCount = 1,
            Data = roboCopies.Where(r => r.Name.Contains("Test")).ToList()
        };

        _mockRoboCopyRepository
            .Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<Domain.Entities.RoboCopy>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync(paginatedResult);

        // Then mock mapping the entire PaginatedResult<RoboCopy> to PaginatedResult<RoboCopyListVm>
        _mockMapper
            .Setup(mapper => mapper.Map<PaginatedResult<RoboCopyListVm>>(It.IsAny<PaginatedResult<Domain.Entities.RoboCopy>>()))
            .Returns(new PaginatedResult<RoboCopyListVm>
            {
                TotalCount = 1,
                Data = new List<RoboCopyListVm> { new RoboCopyListVm { Id = "Ref1", Name = "Test1" } }
            });


        var result = await _handler.Handle(query, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Single(result.Data);
        Assert.Equal(1, result.TotalCount);
    }


}
