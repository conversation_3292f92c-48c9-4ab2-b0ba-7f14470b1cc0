﻿using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetPaginatedList;
[ExcludeFromCodeCoverage]
public record GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery : PaginatedBase,
    IRequest<PaginatedResult<MsSqlNativeLogShippingMonitorStatusListVm>>;