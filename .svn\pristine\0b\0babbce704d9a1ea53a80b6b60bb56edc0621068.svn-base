﻿
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content">
    <div class="card Card_Design_None" >
        <div class="card-header header">
            <h6 class="page_title" title="Log Viewer"><i class="cp-log-viewer"></i><span>Log Viewer</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="search-inp" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" ><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown">
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="CompanyName">
                                        <label class="form-check-label" for="CompanyName">
                                            Error (100)
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="DisplayName">
                                        <label class="form-check-label" for="DisplayName">
                                            Info (50)
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="" id="WebAddress">
                                        <label class="form-check-label" for="WebAddress">
                                            Warning (75)
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>                
            </form>
        </div>
        <div class="card-body header_filter pt-0">
            <div class="d-flex gap-2">
                <div class="input-group">
                    <span class="input-group-text form-label mb-0" for="basic-url" title="File Name">File Name</span>
                    <div class="input-group">
                        <select class="form-select" data-live-search="true" >
                            <option value="1">File1</option>
                            <option value="2">File2</option>
                            <option value="3">File3</option>
                        </select>
                    </div>
                </div>
                <div class="input-group">
                    <span class="input-group-text form-label mb-0" for="basic-url" title="Node Name">Node Name</span>
                    <div class="input-group">
                        <select class="form-select" data-live-search="true">
                            <option value="1">Node1</option>
                            <option value="2">Node2</option>
                            <option value="3">Node3</option>
                        </select>
                    </div>
                </div>
              @*   <div class="input-group">
                    <span class="input-group-text form-label mb-0" for="basic-url">Total File</span>
                    <div class="input-group">
                        <select class="form-select" data-live-search="true">
                            <option value="1">Server</option>
                            <option value="2">Database</option>
                            <option value="3">Replication</option>
                        </select>
                    </div>
                </div> *@
                <div class="input-group">
                    <span class="input-group-text form-label mb-0" for="startDate" cursorshover="true">Start&nbsp;Date</span>
                    <div class="input-group" id="startDate">
                        <input placeholder="Select start date" type="date"
                               class="form-control custom-cursor-default-hover" value="" />
                    </div>
                </div>
                <div class="input-group">
                    <span class="input-group-text form-label mb-0" for="endDate">End&nbsp;Date</span>
                    <div class="input-group" id="endDate">
                        <input placeholder="Select end date" type="date"
                               class="form-control custom-cursor-default-hover" value="" />
                    </div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" title="View">View</button>
            </div>
            <table id="example" class="datatable table table-hover dataTable no-footer" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>Level</th>
                        <th>Time</th>
                        <th >Env.</th>
                        <th >Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td><i class="cp-error text-danger fs-5"></i> Error</td>
                        <td>202-07-10 23:57:02</td>
                        <td>Production</td>
                        <td>SQLSTATE[HY]:invalid parameter (SQL:ANALYZE TABLE'Action toplist')</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td><i class="cp-note text-info fs-5"></i> Info</td>
                        <td>202-07-10 23:57:02</td>
                        <td>Production</td>
                        <td>Maximum execution time of 100 seconds execeeded</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td><i class="cp-warning text-warning fs-5"></i> Warning</td>
                        <td>202-07-10 23:57:02</td>
                        <td>Production</td>
                        <td>Maximum execution time of 100 seconds execeeded</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

