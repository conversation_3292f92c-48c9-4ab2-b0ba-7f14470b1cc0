﻿namespace ContinuityPatrol.Application.Features.AlertNotification.Commands.Delete;

public class
    DeleteAlertNotificationCommandHandler : IRequestHandler<DeleteAlertNotificationCommand,
        DeleteAlertNotificationResponse>
{
    private readonly IAlertNotificationRepository _alertNotificationRepository;

    public DeleteAlertNotificationCommandHandler(IAlertNotificationRepository alertNotificationRepository)
    {
        _alertNotificationRepository = alertNotificationRepository;
    }

    public async Task<DeleteAlertNotificationResponse> Handle(DeleteAlertNotificationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _alertNotificationRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.AlertNotification),
            new NotFoundException(nameof(Domain.Entities.AlertNotification), request.Id));

        eventToDelete.IsActive = false;

        await _alertNotificationRepository.UpdateAsync(eventToDelete);

        var response = new DeleteAlertNotificationResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.AlertNotification), eventToDelete.InfraObjectId),

            IsActive = eventToDelete.IsActive
        };

        return response;
    }
}