﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ReportSchedule.Event.Update;

public class ReportScheduleUpdateEventHandler : INotificationHandler<ReportScheduleUpdateEvent>
{
    private readonly ILogger<ReportScheduleUpdateEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ReportScheduleUpdateEventHandler(ILogger<ReportScheduleUpdateEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(ReportScheduleUpdateEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.ReportSchedule}",
            Entity = Modules.ReportSchedule.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Report Schedule '{updatedEvent.ReportName}' updated successfully."
        };
        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Report Schedule'{updatedEvent.ReportName}' updated successfully.");
    }
}