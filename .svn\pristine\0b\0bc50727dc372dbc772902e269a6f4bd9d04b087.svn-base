﻿function DRBusinessFunction(businessDetails) {
  
    var DRAvaliable = businessDetails ? businessDetails : null

    if (DRAvaliable != null && DRAvaliable.totalBusinessFunction != 0) {

        am4core.useTheme(am4themes_animated);
        // Themes end

        // Create chart instance
        var chart = am4core.create("DRBusinessFunctionsChart", am4charts.PieChart);
        if (chart.logo) {
            chart.logo.disabled = true;
        }
        if (DRAvaliable != null && DRAvaliable.totalBusinessFunction != 0) {
            chart.data = [{
                "country": "Ready (" + DRAvaliable.bfdrReady + ")",
                "litres": DRAvaliable.bfdrReady
            }, {
                "country": "Not Ready (" + DRAvaliable.bfdrNotReady + ")",
                "litres": DRAvaliable.bfdrNotReady
            }];
        }
        // Set inner radius
        chart.innerRadius = am4core.percent(65);

        // Add and configure Series
        var pieSeries = chart.series.push(new am4charts.PieSeries());
        pieSeries.dataFields.value = "litres";
        pieSeries.dataFields.category = "country";
        pieSeries.ticks.template.disabled = true;
        pieSeries.labels.template.disabled = true;
        pieSeries.slices.template.stroke = am4core.color("#fff");
        pieSeries.slices.template.strokeWidth = 5;
        pieSeries.slices.template.strokeOpacity = 5;
        pieSeries.slices.template.cornerRadius = 20;
        pieSeries.slices.template.innerCornerRadius = 20;
        // This creates initial animation
        pieSeries.hiddenState.properties.opacity = 1;
        pieSeries.hiddenState.properties.endAngle = -90;
        pieSeries.hiddenState.properties.startAngle = -90;

        pieSeries.colors.list = [
            am4core.color("#10b310"),
            am4core.color("#f77586"),
            //am4core.color("#f32f41"),
            //am4core.color("#d9d9d9")
        ];

        let DRlabel = pieSeries.createChild(am4core.Label);
        let totalBusinessFunctionCount = DRAvaliable?.totalBusinessFunction ?? 0
        DRlabel.text = "[bold font-size:16px]" + totalBusinessFunctionCount + "[/] \n [font-size:13px]Total[/]"
        DRlabel.textAlign = "middle";
        DRlabel.horizontalCenter = "middle";
        DRlabel.verticalCenter = "middle";
        DRlabel.fontSize = 14;

        // Change the padding values
        chart.padding(-5, -20, -15, -20)
        chart.legend = new am4charts.Legend();
        chart.legend.itemContainers.template.padding(8, 0, 0, 0);

        // Add a legend
        chart.legend.position = "left";
        chart.legend.valueLabels.template.disabled = true;
        chart.legend.itemContainers.template.clickable = false;
        chart.legend.itemContainers.template.focusable = false;
        chart.legend.labels.template.text = "[font-size:10px ]{name}";
        chart.legend.labels.template.fill = am4core.color("#6c757d");
        chart.legend.markers.template.children.getIndex(0).cornerRadius(30, 30, 30, 30);
        var markerTemplate = chart.legend.markers.template;
        markerTemplate.width = 10;
        markerTemplate.height = 10;

    } else {
        $('#DRBusinessFunctionsChart')
            .css('text-align', 'center')
            .html('<img class="m-auto" src="../../img/isomatric/operational_functions_not_configured.svg" height="112">');
    }
}