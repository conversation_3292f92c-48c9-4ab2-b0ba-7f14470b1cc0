﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;

public class UpdateApprovalMatrixCommand : IRequest<UpdateApprovalMatrixResponse>
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string BusinessFunctionId { get; set; }
    public string BusinessFunctionName { get; set; }
    public string Description { get; set; }
    public string Properties { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}