﻿using ContinuityPatrol.Application.Features.ApprovalMatrix.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrix.Commands;

public class UpdateApprovalMatrixTests : IClassFixture<ApprovalMatrixFixture>
{
    private readonly ApprovalMatrixFixture _approvalMatrixFixture;

    private readonly Mock<IApprovalMatrixRepository> _mockApprovalMatrixRepository;

    private readonly UpdateApprovalMatrixCommandHandler _handler;

    public UpdateApprovalMatrixTests(ApprovalMatrixFixture approvalMatrixFixture)
    {
        _approvalMatrixFixture = approvalMatrixFixture;

        _mockApprovalMatrixRepository = ApprovalMatrixRepositoryMocks.UpdateApprovalMatrixRepository(_approvalMatrixFixture.ApprovalMatrices);

        _handler = new UpdateApprovalMatrixCommandHandler(_approvalMatrixFixture.Mapper, _mockApprovalMatrixRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidApprovalMatrix_UpdateReferenceIdAsync_ToApprovalMatricesRepo()
    {
        _approvalMatrixFixture.UpdateApprovalMatrixCommand.Id = _approvalMatrixFixture.ApprovalMatrices[0].ReferenceId;

        var result = await _handler.Handle(_approvalMatrixFixture.UpdateApprovalMatrixCommand, CancellationToken.None);

        var approvalMatrix = await _mockApprovalMatrixRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_approvalMatrixFixture.UpdateApprovalMatrixCommand.Name, approvalMatrix.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidApprovalMatrixResponse_WhenUpdate_ApprovalMatrix()
    {
        _approvalMatrixFixture.UpdateApprovalMatrixCommand.Id = _approvalMatrixFixture.ApprovalMatrices[0].ReferenceId;

        var result = await _handler.Handle(_approvalMatrixFixture.UpdateApprovalMatrixCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateApprovalMatrixResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_approvalMatrixFixture.UpdateApprovalMatrixCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidApprovalMatrixId()
    {
        _approvalMatrixFixture.UpdateApprovalMatrixCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_approvalMatrixFixture.UpdateApprovalMatrixCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _approvalMatrixFixture.UpdateApprovalMatrixCommand.Id = _approvalMatrixFixture.ApprovalMatrices[0].ReferenceId;

        await _handler.Handle(_approvalMatrixFixture.UpdateApprovalMatrixCommand, CancellationToken.None);

        _mockApprovalMatrixRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockApprovalMatrixRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.ApprovalMatrix>()), Times.Once);
    }
}