﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.AccessManager.Events.Delete;

public class AccessManagerDeletedEventHandler : INotificationHandler<AccessManagerDeletedEvent>
{
    private readonly ILogger<AccessManagerDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public AccessManagerDeletedEventHandler(ILoggedInUserService userService,
        ILogger<AccessManagerDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(AccessManagerDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.AccessManager}",
            Entity = Modules.AccessManager.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Access manager '{deletedEvent.RoleName}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Access manager '{deletedEvent.RoleName}' deleted successfully.");
    }
}