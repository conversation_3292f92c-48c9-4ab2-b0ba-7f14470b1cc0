using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetList;

public class
    GetBulkImportOperationListQueryHandler : IRequestHandler<GetBulkImportOperationListQuery,
        List<BulkImportOperationListVm>>
{
    private readonly IBulkImportOperationRepository _bulkImportOperationRepository;
    private readonly IMapper _mapper;

    public GetBulkImportOperationListQueryHandler(IMapper mapper,
        IBulkImportOperationRepository bulkImportOperationRepository)
    {
        _mapper = mapper;
        _bulkImportOperationRepository = bulkImportOperationRepository;
    }

    public async Task<List<BulkImportOperationListVm>> Handle(GetBulkImportOperationListQuery request,
        CancellationToken cancellationToken)
    {
        var bulkImportOperations = await _bulkImportOperationRepository.ListAllAsync();

        if (bulkImportOperations.Count <= 0) return new List<BulkImportOperationListVm>();

        return _mapper.Map<List<BulkImportOperationListVm>>(bulkImportOperations);
    }
}