﻿namespace ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetDetail;

public class GetBusinessServiceAvailabilityDetailQueryHandler : IRequestHandler<
    GetBusinessServiceAvailabilityDetailQuery, BusinessServiceAvailabilityDetailVm>
{
    private readonly IBusinessServiceAvailabilityRepository _businessServiceAvailabilityRepository;
    private readonly IMapper _mapper;

    public GetBusinessServiceAvailabilityDetailQueryHandler(IMapper mapper,
        IBusinessServiceAvailabilityRepository businessServiceAvailabilityRepository)
    {
        _mapper = mapper;
        _businessServiceAvailabilityRepository = businessServiceAvailabilityRepository;
    }

    public async Task<BusinessServiceAvailabilityDetailVm> Handle(GetBusinessServiceAvailabilityDetailQuery request,
        CancellationToken cancellationToken)
    {
        var serviceAvailability = await _businessServiceAvailabilityRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(serviceAvailability, nameof(Domain.Entities.BusinessServiceAvailability),
            new NotFoundException(nameof(Domain.Entities.BusinessServiceAvailability), request.Id));

        var serviceAvailabilityDtl = _mapper.Map<BusinessServiceAvailabilityDetailVm>(serviceAvailability);

        return serviceAvailabilityDtl ??
               throw new NotFoundException(nameof(Domain.Entities.BusinessServiceAvailability), request.Id);
    }
}