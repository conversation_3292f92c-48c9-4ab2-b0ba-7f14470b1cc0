﻿using ContinuityPatrol.Application.Features.DataSyncOptions.Events.Create;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.DataSyncOptions.Events;
public class DataSyncOptionsCreatedEventHandlerTests
{
    [Fact]
    public async Task Handle_Should_Add_UserActivity_And_Log_When_Event_Handled()
    {
        // Arrange
        var mockUserService = new Mock<ILoggedInUserService>();
        var mockLogger = new Mock<ILogger<DataSyncOptionsCreatedEventHandler>>();
        var mockUserActivityRepo = new Mock<IUserActivityRepository>();

        var userId = Guid.NewGuid().ToString();
        var loginName = "testuser";
        var companyId = "company123";
        var requestUrl = "/api/test";
        var ipAddress = "127.0.0.1";

        mockUserService.SetupGet(s => s.UserId).Returns(userId);
        mockUserService.SetupGet(s => s.LoginName).Returns(loginName);
        mockUserService.SetupGet(s => s.CompanyId).Returns(companyId);
        mockUserService.SetupGet(s => s.RequestedUrl).Returns(requestUrl);
        mockUserService.SetupGet(s => s.IpAddress).Returns(ipAddress);

        var handler = new DataSyncOptionsCreatedEventHandler(
            mockUserService.Object,
            mockLogger.Object,
            mockUserActivityRepo.Object
        );

        var createdEvent = new DataSyncOptionsCreatedEvent { Name = "SyncOption A" };

        // Act
        await handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        mockUserActivityRepo.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.CompanyId == companyId &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress &&
            ua.Action == $"{ActivityType.Create} DataSyncOptionsProperties" &&
            ua.Entity == "DataSyncProperties" &&
            ua.ActivityType == ActivityType.Create.ToString() &&
            ua.ActivityDetails == "DataSync Options 'SyncOption A' created successfully." &&
            ua.CreatedBy == userId &&
            ua.LastModifiedBy == userId
        )), Times.Once);

        mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("DataSync Options 'SyncOption A' created successfully.")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }
}
