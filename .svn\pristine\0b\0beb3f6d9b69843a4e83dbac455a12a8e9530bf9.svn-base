using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class ReplicationFixture
{
    public List<ReplicationListVm> ReplicationListVm { get; }
    public List<ReplicationNameVm> ReplicationNameVm { get; }
    public PaginatedResult<ReplicationListVm> PaginatedReplicationListVm { get; }
    public ReplicationDetailVm ReplicationDetailVm { get; }
    public CreateReplicationCommand CreateReplicationCommand { get; }
    public UpdateReplicationCommand UpdateReplicationCommand { get; }
    public SaveAsReplicationCommand SaveAsReplicationCommand { get; }
    public SaveAllReplicationCommand SaveAllReplicationCommand { get; }
    public GetReplicationPaginatedListQuery GetReplicationPaginatedListQuery { get; }
    public List<GetReplicationByLicenseKeyListVm> GetReplicationByLicenseKeyListVm { get; }
    public List<ReplicationTypeVm> ReplicationTypeVm { get; }

    public ReplicationFixture()
    {
        var fixture = new Fixture();

        ReplicationListVm = fixture.Create<List<ReplicationListVm>>();
        ReplicationNameVm = fixture.Create<List<ReplicationNameVm>>();
        PaginatedReplicationListVm = fixture.Create<PaginatedResult<ReplicationListVm>>();
        ReplicationDetailVm = fixture.Create<ReplicationDetailVm>();
        CreateReplicationCommand = fixture.Create<CreateReplicationCommand>();
        UpdateReplicationCommand = fixture.Create<UpdateReplicationCommand>();
        SaveAsReplicationCommand = fixture.Create<SaveAsReplicationCommand>();
        SaveAllReplicationCommand = fixture.Create<SaveAllReplicationCommand>();
        GetReplicationPaginatedListQuery = fixture.Create<GetReplicationPaginatedListQuery>();
        GetReplicationByLicenseKeyListVm = fixture.Create<List<GetReplicationByLicenseKeyListVm>>();
        ReplicationTypeVm = fixture.Create<List<ReplicationTypeVm>>();
    }

    public void Dispose()
    {

    }
}
