﻿namespace ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterNameUnique;

public class GetAlertMasterNameUniqueQueryHandler : IRequestHandler<GetAlertMasterNameUniqueQuery, bool>
{
    private readonly IAlertMasterRepository _alertMasterRepository;

    public GetAlertMasterNameUniqueQueryHandler(IAlertMasterRepository alertMasterRepository)
    {
        _alertMasterRepository = alertMasterRepository;
    }

    public async Task<bool> Handle(GetAlertMasterNameUniqueQuery request, CancellationToken cancellationToken)
    {
        var isExist = await _alertMasterRepository.IsNameExist(request.AlertName, request.AlertId);
        return isExist;
    }
}