﻿using ContinuityPatrol.Application.Features.PageWidget.Events.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.PageWidget.Events;

public class DeletePageWidgetEventTests
{
    private readonly Mock<ILogger<PageWidgetDeletedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly PageWidgetDeletedEventHandler _handler;

    public DeletePageWidgetEventTests()
    {
        _mockLogger = new Mock<ILogger<PageWidgetDeletedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _handler = new PageWidgetDeletedEventHandler(_mockLoggedInUserService.Object, _mockLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ShouldLogInformation_WhenEventIsHandledSuccessfully()
    {
        var pageWidgetEvent = new PageWidgetDeletedEvent { Name = "TestWidget" };

        _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
        _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
        _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
        _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");
        var userDetails = new Domain.Entities.UserActivity
        {
            ReferenceId = "123"
        };
        _mockUserActivityRepository.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(userDetails);

        await _handler.Handle(pageWidgetEvent, CancellationToken.None);


        _mockUserActivityRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldSaveUserActivity_WhenEventIsHandledSuccessfully()
    {
        var pageWidgetEvent = new PageWidgetDeletedEvent { Name = "TestWidget" };

        _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
        _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
        _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
        _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

        await _handler.Handle(pageWidgetEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(r =>
            r.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "123" &&
                ua.LoginName == "TestUser" &&
                ua.RequestUrl == "http://test.url" &&
                ua.CompanyId == "456" &&
                ua.HostAddress == "127.0.0.1" &&
                ua.Action == "Delete PageWidget" &&
                ua.Entity == "PageWidget" &&
                ua.ActivityType == "Delete" &&
                ua.ActivityDetails == $"PageWidget '{pageWidgetEvent.Name}' deleted successfully.")),
            Times.Once);
    }


}
