﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IMssqlAlwaysOnAvailabilityGroupMonitorLogRepository
{
    Task<List<MssqlAlwaysOnAvailabilityGroupMonitorLog>> GetByInfraObjectId(string infraObjectId, string startDate, string endDate);
    Task<List<MssqlAlwaysOnAvailabilityGroupMonitorLog>> GetDetailByType(string type);
}
