﻿using ContinuityPatrol.Application.Features.Database.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Events;

public class DeleteDatabaseEventTests : IClassFixture<DatabaseFixture>, IClassFixture<UserActivityFixture>, IClassFixture<HeatMapStatusFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly HeatMapStatusFixture _heatMapStatusFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    //private readonly Mock<IHeatMapStatusRepository> _mockHeatMapStatusRepository;

    private readonly DatabaseDeletedEventHandler _handler;

    public DeleteDatabaseEventTests(DatabaseFixture databaseFixture, UserActivityFixture userActivityFixture, HeatMapStatusFixture heatMapStatusFixture)
    {
        _databaseFixture = databaseFixture;

        _userActivityFixture = userActivityFixture;

        _heatMapStatusFixture = heatMapStatusFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockDatabaseEventLogger = new Mock<ILogger<DatabaseDeletedEventHandler>>();

        _mockUserActivityRepository = DatabaseRepositoryMocks.CreateDatabaseEventRepository(_userActivityFixture.UserActivities);

       // _mockHeatMapStatusRepository = HeatMapStatusRepositoryMocks.GetHeatMapStatusRepository(_heatMapStatusFixture.HeatMapStatusList);

        _handler = new DatabaseDeletedEventHandler(mockLoggedInUserService.Object, mockDatabaseEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_DeleteDatabaseEventDeleted()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_databaseFixture.DatabaseDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_databaseFixture.DatabaseDeletedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}