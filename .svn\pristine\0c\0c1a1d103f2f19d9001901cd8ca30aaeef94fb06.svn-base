﻿using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId;
using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetPagination;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Create;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class FastCopyMonitorService:BaseService, IFastCopyMonitorService
{
    public FastCopyMonitorService(IHttpContextAccessor accessor) : base(accessor)
    {
        
    }

    public async Task<List<FatCopyMonitorListVm>> GetByDriftJobIds(List<string> dataSyncJobIds)
    {
        Logger.LogDebug($"Get fast copy monitor by Data Sync Job Id");

        return await Mediator.Send(new GetFastCopyMonitorByDataSyncIdsQuery{ DataSyncJobIds=dataSyncJobIds});
    }

    public async Task<PaginatedResult<FastCopyMonitorPaginatedListVm>> GetPagination(GetFastCopyMonitorPaginatedQuery query)
    {
        Logger.LogDebug($"Get list by pagination.");

        return await Mediator.Send(query);

    }
}
