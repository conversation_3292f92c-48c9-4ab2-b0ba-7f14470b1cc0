using ContinuityPatrol.Application.Features.AdPasswordJob.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.AdPasswordJob.Queries;

public class GetAdPasswordJobNameUniqueQueryTests : IClassFixture<AdPasswordJobFixture>
{
    private readonly AdPasswordJobFixture _adPasswordJobFixture;
    private readonly Mock<IAdPasswordJobRepository> _mockAdPasswordJobRepository;
    private readonly GetAdPasswordJobNameUniqueQueryHandler _handler;

    public GetAdPasswordJobNameUniqueQueryTests(AdPasswordJobFixture adPasswordJobFixture)
    {
        _adPasswordJobFixture = adPasswordJobFixture;

        _mockAdPasswordJobRepository = AdPasswordJobRepositoryMocks.CreateQueryAdPasswordJobRepository(_adPasswordJobFixture.AdPasswordJobs);
        
        _handler = new GetAdPasswordJobNameUniqueQueryHandler(_mockAdPasswordJobRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExists_And_NoIdProvided()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = existingJob.DomainServer,
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_When_NameDoesNotExist_And_NoIdProvided()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "NonExistentDomainServer",
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_False_When_NameExistsForSameId()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = existingJob.DomainServer,
            Id = existingJob.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_True_When_NameExistsForDifferentId()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        var differentId = Guid.NewGuid().ToString();
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = existingJob.DomainServer,
            Id = differentId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Call_IsNameExist_OnlyOnce()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_PassCorrectParameters_When_CallingRepository()
    {
        // Arrange
        var testName = "TestDomainServer";
        var testId = "TestId";
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = testName,
            Id = testId
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist(testName, testId), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_RepositoryResult_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        _mockAdPasswordJobRepository.Setup(x => x.IsNameExist("TestName", "TestId"))
            .ReturnsAsync(true);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_HandleEmptyName_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "",
            Id = "TestId"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("", "TestId"), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleNullId_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "TestName",
            Id = null
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("TestName", null), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyId_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "TestName",
            Id = ""
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("TestName", ""), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnBooleanType_When_QueryExecuted()
    {
        // Arrange
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "TestName",
            Id = "TestId"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<bool>();
    }

    [Fact]
    public async Task Handle_CheckDomainServerUniqueness_When_ValidatingName()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.DomainServer = "ExistingDomain.com";
        
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "ExistingDomain.com",
            Id = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("ExistingDomain.com", null), Times.Once);
    }

    [Fact]
    public async Task Handle_AllowSameDomainServerForUpdate_When_SameIdProvided()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.DomainServer = "ExistingDomain.com";
        
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "ExistingDomain.com",
            Id = existingJob.ReferenceId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeFalse();
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("ExistingDomain.com", existingJob.ReferenceId), Times.Once);
    }

    [Fact]
    public async Task Handle_PreventDuplicateDomainServer_When_DifferentIdProvided()
    {
        // Arrange
        var existingJob = _adPasswordJobFixture.AdPasswordJobs.First();
        existingJob.DomainServer = "ExistingDomain.com";
        var differentId = Guid.NewGuid().ToString();
        
        var query = new GetAdPasswordJobNameUniqueQuery 
        { 
            Name = "ExistingDomain.com",
            Id = differentId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeTrue();
        _mockAdPasswordJobRepository.Verify(x => x.IsNameExist("ExistingDomain.com", differentId), Times.Once);
    }
}
