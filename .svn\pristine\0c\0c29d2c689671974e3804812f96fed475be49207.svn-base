﻿using ContinuityPatrol.Application.Features.Workflow.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Queries;

public class GetWorkflowListQueryHandlerTests : IClassFixture<WorkflowFixture>, IClassFixture<WorkflowPermissionFixture>
{
    private readonly WorkflowFixture _workflowFixture;
    private Mock<IWorkflowRepository> _mockWorkflowRepository;
    private readonly Mock<IWorkflowPermissionRepository> _mockWorkflowPermissionRepository;
    private readonly GetWorkflowListQueryHandler _handler;

    public GetWorkflowListQueryHandlerTests(WorkflowFixture workflowFixture, WorkflowPermissionFixture workflowPermissionFixture)
    {
        _workflowFixture = workflowFixture;

        _mockWorkflowRepository = WorkflowRepositoryMocks.GetWorkflowRepository(_workflowFixture.Workflows);

        _mockWorkflowPermissionRepository = WorkflowPermissionRepositoryMocks.GetWorkflowPermissionRepository(workflowPermissionFixture.WorkflowPermissions);

        _handler = new GetWorkflowListQueryHandler(_workflowFixture.Mapper, _mockWorkflowRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowCount()
    {
        var result = await _handler.Handle(new GetWorkflowListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowListQuery(), CancellationToken.None);

        _mockWorkflowRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowDetail()
    {
        var result = await _handler.Handle(new GetWorkflowListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowListVm>>();

        result[0].Id.ShouldBe(_workflowFixture.Workflows[0].ReferenceId);
        result[0].CompanyId.ShouldBe(_workflowFixture.Workflows[0].CompanyId);
        result[0].Name.ShouldBe(_workflowFixture.Workflows[0].Name);
        result[0].Version.ShouldBe(_workflowFixture.Workflows[0].Version);
        result[0].Properties.ShouldBe(_workflowFixture.Workflows[0].Properties);
        result[0].CreatedBy.ShouldBe(_workflowFixture.Workflows[0].CreatedBy);
        result[0].ReferenceId.ShouldBe(_workflowFixture.Workflows[0].ReferenceId);
        result[0].IsLock.ShouldBe(_workflowFixture.Workflows[0].IsLock);
        //result[0].IsFreeze.ShouldBe(_workflowFixture.Workflows[0].IsFreeze);
        result[0].IsPublish.ShouldBe(_workflowFixture.Workflows[0].IsPublish);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowRepository = WorkflowRepositoryMocks.GetWorkflowEmptyRepository();

        var handler = new GetWorkflowListQueryHandler(_workflowFixture.Mapper, _mockWorkflowRepository.Object);

        var result = await handler.Handle(new GetWorkflowListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}