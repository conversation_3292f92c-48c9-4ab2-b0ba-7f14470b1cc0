﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateStateToUnlock;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]
public class ManageOperationalServiceController : BaseController
{  
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IDataProvider _dataProvider;
    private readonly ILogger<ManageOperationalServiceController> _logger;
    public ManageOperationalServiceController(ILoggedInUserService loggedInUserService, IMapper mapper, ILogger<ManageOperationalServiceController> logger, IDataProvider provider)
    {
        _loggedInUserService = loggedInUserService;
        _mapper = mapper;
        _dataProvider = provider;
        _logger = logger;
    }
    [EventCode(EventCodes.ManageOperationalService.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in manage operational service");
        try
        {
           await CreateUserActivity();

            //var infraView = await _dataProvider.InfraObject.GetPaginatedInfraObjects(new Application.Features.InfraObject.Queries.GetPaginatedList.GetInfraObjectPaginatedListQuery());

            //var manageBusinessServiceModel = new InfraObjectViewModel
            //{
            //    PaginatedInfra = infraView
            //};

            _logger.LogDebug("Successfully retrieved list in manage operational service.");

            return View();
        }
        catch (Exception ex)
        { 
            _logger.Exception("An error occurred on manage operational service page while retrieving list.",ex);
            return ex.GetJsonException();
        }
    }
    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Permissions.Manage.CreateAndEdit)]
    [AntiXss]
    [EventCode(EventCodes.ManageOperationalService.UpdateInfraObjectState)]
    public async Task<IActionResult> UpdateInfraObjectState(UpdateInfraObjectStateCommand updateInfraObjectStateCommand)
    {
        _logger.LogDebug("Entering UpdateInfraObjectState method in manageoperationservice");

        try
        {
            BaseResponse result;
            result = await _dataProvider.InfraObject.UpdateInfraObjectState(updateInfraObjectStateCommand);
            _logger.LogDebug($" '{updateInfraObjectStateCommand}'");

            //_logger.LogDebug("CreateOrUpdate operation completed successfully in DriftParameter, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on manageoperationservice page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [Authorize(Policy = Permissions.Manage.CreateAndEdit)]
    [AntiXss]
    [EventCode(EventCodes.ManageOperationalService.UpdateInfraObjectUnlock)]
    public async Task<IActionResult> UpdateInfraObjectUnlock(UpdateStateToUnlockCommand updateStateToUnlockCommand)
    {
        _logger.LogDebug("Entering UpdateInfraObjectState method in manageoperationservice");

        try
        {
            BaseResponse result;
            result = await _dataProvider.InfraObject.UpdateInfraObjectStateUnlock(updateStateToUnlockCommand);
            _logger.LogDebug($" '{updateStateToUnlockCommand}'");

            //_logger.LogDebug("CreateOrUpdate operation completed successfully in DriftParameter, returning view.");
            return Json(new { Success = true, data = result });
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on manageoperationservice page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on drift parameter page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    [EventCode(EventCodes.ManageOperationalService.GetUserInfraObjectList)]
    public async Task<GetUserInfraObjectByBusinessServiceVm> GetUserInfraObjectList(string data)
    {
        _logger.LogDebug("Entering GetUserInfraObjectList method in manage operational service");
        try
        {
            var userInfraObject = await _dataProvider.UserInfraObject.GetUserInfraObjects("");

            _logger.LogDebug("Successfully retrieved user infra object list in manage operational service");

            return userInfraObject;
        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on manage operational service page while retrieving user infra object list", ex);
            return new GetUserInfraObjectByBusinessServiceVm();
        }
    }
   
    [HttpGet]
    [EventCode(EventCodes.ManageOperationalService.Pagination)]
    public async Task<JsonResult> GetPagination(GetInfraObjectPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in manage operational service");
        try
        {
            var paginationList = await _dataProvider.InfraObject.GetPaginatedInfraObjects(query);

            _logger.LogDebug("Successfully retrieved pagination list in manage operational service.");

            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on manage operational service page while retrieving pagination list.",ex);
           
            return ex.GetJsonException();
        }
      
    }
    private async Task CreateUserActivity()
    {
        try
        {
            var userActivity = new CreateUserActivityCommand
            {
                CompanyId = _loggedInUserService.CompanyId,
                UserId = _loggedInUserService.UserId,
                LoginName = _loggedInUserService.LoginName,
                RequestUrl = _loggedInUserService.RequestedUrl,
                HostAddress = _loggedInUserService.IpAddress,
                Action = $"{ActivityType.View} ManageOperationalService",
                Entity = "ManageOperationalService",
                ActivityType = ActivityType.View.ToString(),
                ActivityDetails = $"Manage Operational Service viewed"
            };

            await _dataProvider.UserActivity.CreateAsync(userActivity);

            _logger.LogInformation($"Manage Operational Service viewed");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred while creating user activity for Manage Operational Service.", ex);
        }
    }

}
