﻿using ContinuityPatrol.Application.Features.RoboCopy.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopy.Commands.Update;
using ContinuityPatrol.Application.Features.RoboCopy.Events.PaginatedView;
using ContinuityPatrol.Application.Features.RoboCopy.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class RoboCopyOptionsController : Controller
{
    private readonly IPublisher _publisher;
    private readonly ILogger<RoboCopyOptionsController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;

    public RoboCopyOptionsController(IPublisher publisher, ILogger<RoboCopyOptionsController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }

    [HttpGet]
    [AntiXss]
    [EventCode(EventCodes.RoboCopyOptions.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in RoboCopyOptions");
        await _publisher.Publish(new RoboCopyPaginatedEvent());
        return View();
    }


    [HttpGet]
    [EventCode(EventCodes.RoboCopyOptions.GetPagination)]
    public async Task<JsonResult> GetPagination(GetRoboCopyPaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in RoboCopyOptions");
        try
        {
            _logger.LogDebug("Successfully retrieved robocopy options paginated list on RoboCopyOptions page");

            return Json(await _dataProvider.RoboCopy.GetPaginatedRoboCopys(query));
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on robocopy option page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }

    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.RoboCopyOptions.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(RoboCopyViewModel roboCopyViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in RoboCopyOptions");

        var databaseId = Request.Form["id"].ToString();
        try
        {

            if (databaseId.IsNullOrWhiteSpace())
            {
                var createRoboCopyCommand = _mapper.Map<CreateRoboCopyCommand>(roboCopyViewModel);
                var response = await _dataProvider.RoboCopy.CreateAsync(createRoboCopyCommand);
                _logger.LogDebug($"Creating RoboCopyOptions '{createRoboCopyCommand.Name}'");
                TempData.NotifySuccess(response.Message);
            }
            else
            {
                var updateRoboCopyCommand = _mapper.Map<UpdateRoboCopyCommand>(roboCopyViewModel);
                var response = await _dataProvider.RoboCopy.UpdateAsync(updateRoboCopyCommand);
                _logger.LogDebug($"Updating RoboCopyOptions '{updateRoboCopyCommand.Name}'");
                TempData.NotifySuccess(response.Message);
            }

            _logger.LogDebug("CreateOrUpdate operation completed successfully in RoboCopyOptions, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on robocopy option page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on robocopy option page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.RoboCopyOptions.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in RoboCopyOptions");

        try
        {
            var response = await _dataProvider.RoboCopy.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in RoboCopyOptions");

            TempData.NotifySuccess(response.Message);

            return RedirectToAction("List");

        }
        catch (Exception ex)
        {
            TempData.NotifyWarning(ex.GetMessage());

            _logger.Exception("An error occurred while deleting record on RoboCopyOptions.", ex);

            return RedirectToAction("List");
        }
    }

    [EventCode(EventCodes.RoboCopyOptions.IsRoboCopyNameExist)]
    public async Task<bool> IsRoboCopyNameExist(string name, string id)
    {
        _logger.LogDebug("Entering IsRoboCopyNameExist method in RoboCopyOptions");
        try
        {
            var nameExist = await _dataProvider.RoboCopy.IsRoboCopyNameExist(name, id);
            _logger.LogDebug("Returning result for IsRoboCopyNameExist on RoboCopyOptions");
            return nameExist;
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on robocopy option page while checking if robocopy option name exists for : {name}.", ex);

            return false;
        }
    }

}

