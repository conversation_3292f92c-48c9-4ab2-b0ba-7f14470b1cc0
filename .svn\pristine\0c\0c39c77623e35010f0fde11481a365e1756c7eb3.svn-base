﻿using ContinuityPatrol.Infrastructure.Impl;
using ContinuityPatrol.Infrastructure.Models;

namespace ContinuityPatrol.Infrastructure.UnitTests.Impl;

public class SmsServiceTests
{
    private readonly Mock<ILogger<SmsService>> _loggerMock;
    private readonly SmsService _smsService;

    public SmsServiceTests()
    {
        _loggerMock = new Mock<ILogger<SmsService>>();
        _smsService = new SmsService(_loggerMock.Object);
    }

    private SmsMessage CreateValidSmsMessage()
    {
        return new SmsMessage
        {
            Url = "https://sms-service.test/api",
            UserName = "user123",
            Password = "pass123",
            RecipientNo = "ABC001",
            ToMobileNo = "9876543210",
            Message = "Test message"
        };
    }
    [Fact]
    public async Task SendSmsAsync_WhenSmsSentSuccessfully_ReturnsTrue()
    {
        // Arrange
        var sms = CreateValidSmsMessage();

        var mockHandler = new Mock<HttpMessageHandler>();
        mockHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("Success")
            });

        var httpClient = new HttpClient(mockHandler.Object);
        var restClient = new RestClient(httpClient, new RestClientOptions(sms.Url));

        // Inject mock client into private `client` field
        typeof(SmsService)
            .GetMethod("SendSmsAsync")!
            .DeclaringType!
            .GetField("client", BindingFlags.NonPublic | BindingFlags.Instance)?
            .SetValue(_smsService, restClient);

        // Act
        var result = await _smsService.SendSmsAsync(sms);

        // Assert
        Assert.True(result);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("SMS sent successfully")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task SendSmsAsync_WhenSmsRequestFails_ReturnsTrueAndLogsWarning()
    {
        // Arrange
        var sms = CreateValidSmsMessage();

        var mockHandler = new Mock<HttpMessageHandler>();
        mockHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.BadRequest,
                Content = new StringContent("Error")
            });

        var httpClient = new HttpClient(mockHandler.Object);
        var restClient = new RestClient(httpClient, new RestClientOptions(sms.Url));

        typeof(SmsService)
            .GetMethod("SendSmsAsync")!
            .DeclaringType!
            .GetField("client", BindingFlags.NonPublic | BindingFlags.Instance)?
            .SetValue(_smsService, restClient);

        // Act
        var result = await _smsService.SendSmsAsync(sms);

        // Assert
        Assert.True(result); // current code returns true even on fail
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("SMS failed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task SendSmsAsync_WhenExceptionThrown_ReturnsFalseAndLogsError()
    {
        // Arrange
        var sms = CreateValidSmsMessage();

        var mockHandler = new Mock<HttpMessageHandler>();
        mockHandler.Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ThrowsAsync(new HttpRequestException("Simulated exception"));

        var httpClient = new HttpClient(mockHandler.Object);
        var restClient = new RestClient(httpClient, new RestClientOptions(sms.Url));

        typeof(SmsService)
            .GetMethod("SendSmsAsync")!
            .DeclaringType!
            .GetField("client", BindingFlags.NonPublic | BindingFlags.Instance)?
            .SetValue(_smsService, restClient);

        // Act
        var result = await _smsService.SendSmsAsync(sms);

        // Assert
        Assert.False(result);
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString()!.Contains("Exception occurred")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

}