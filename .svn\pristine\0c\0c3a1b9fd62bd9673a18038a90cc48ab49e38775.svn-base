﻿//using ContinuityPatrol.Application.Features.HeatMapLog.Commands.Create;
//using ContinuityPatrol.Application.UnitTests.Fixtures;
//using ContinuityPatrol.Application.UnitTests.Mocks;

//namespace ContinuityPatrol.Application.UnitTests.Features.HeatMapLog.Commands;

//public class CreateHeatMapLogTests : IClassFixture<HeatMapLogFixture>
//{
//    private readonly HeatMapLogFixture _heatMapLogFixture;

//    private readonly Mock<IHeatMapLogRepository> _heatMapLogRepositoryMock;

//    private readonly CreateHeatMapLogCommandHandler _handler;

//    public CreateHeatMapLogTests(HeatMapLogFixture heatMapLogFixture)
//    {
//        _heatMapLogFixture = heatMapLogFixture;
    
//        _heatMapLogRepositoryMock = HeatMapLogRepositoryMocks.CreateHeatMapLogRepository(_heatMapLogFixture.HeatMapLogs);
        
//        _handler = new CreateHeatMapLogCommandHandler(_heatMapLogFixture.Mapper, _heatMapLogRepositoryMock.Object);
//    }

//    [Fact]
//    public async Task Handle_IncreaseHeatMapLogCount_When_HeatMapLogCreated()
//    {
//        await _handler.Handle(_heatMapLogFixture.CreateHeatMapLogCommand, CancellationToken.None);

//        var result = await _heatMapLogRepositoryMock.Object.ListAllAsync();

//        result.Count.ShouldBe(_heatMapLogFixture.HeatMapLogs.Count);
//    }

//    [Fact]
//    public async Task Handle_Return_CreateHeatMapLogResponse_When_HeatMapLogCreated()
//    {
//        var result = await _handler.Handle(_heatMapLogFixture.CreateHeatMapLogCommand, CancellationToken.None);

//        result.ShouldBeOfType(typeof(CreateHeatMapLogResponse));

//        result.Id.ShouldBeGreaterThan(0.ToString());

//        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(_heatMapLogFixture.CreateHeatMapLogCommand, CancellationToken.None);

//        _heatMapLogRepositoryMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.HeatMapLog>()), Times.Once);
//    }
//}