﻿using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;

namespace ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetList;

public class GetGroupPolicyListQueryHandler : IRequestHandler<GetGroupPolicyListQuery, List<GroupPolicyListVm>>
{
    private readonly IGroupPolicyRepository _groupPolicyRepository;
    private readonly IMapper _mapper;

    public GetGroupPolicyListQueryHandler(IGroupPolicyRepository groupPolicyRepository, IMapper mapper)
    {
        _groupPolicyRepository = groupPolicyRepository;
        _mapper = mapper;
    }

    public async Task<List<GroupPolicyListVm>> Handle(GetGroupPolicyListQuery request,
        CancellationToken cancellationToken)
    {
        var groupPolicy = (await _groupPolicyRepository.ListAllAsync()).ToList();

        return groupPolicy.Count == 0
            ? new List<GroupPolicyListVm>()
            : _mapper.Map<List<GroupPolicyListVm>>(groupPolicy);
    }
}