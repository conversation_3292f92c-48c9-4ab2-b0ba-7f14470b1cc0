﻿using ContinuityPatrol.Application.Features.RiskMitigation.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.RiskMitigationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.RiskMitigation.Queries;

public class GetRiskMitigationListQueryHandlerTests : IClassFixture<RiskMitigationFixture>
{
    private readonly RiskMitigationFixture _riskMitigationFixture;

    private Mock<IRiskMitigationRepository> _mockRiskMitigationRepository;

    private readonly GetRiskMitigationListQueryHandler _handler;

    public GetRiskMitigationListQueryHandlerTests(RiskMitigationFixture riskMitigationFixture)
    {
        _riskMitigationFixture = riskMitigationFixture;

        _mockRiskMitigationRepository = RiskMitigationRepositoryMocks.GetRiskMitigationRepository(_riskMitigationFixture.RiskMitigations);

        _handler = new GetRiskMitigationListQueryHandler(_riskMitigationFixture.Mapper, _mockRiskMitigationRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_Valid_RiskMitigationsList()
    {
        var result = await _handler.Handle(new GetRiskMitigationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<RiskMitigationListVm>>();

        result[0].Id.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ReferenceId);
        result[0].BusinessServiceId.ShouldBe(_riskMitigationFixture.RiskMitigations[0].BusinessServiceId);
        result[0].BusinessServiceName.ShouldBe(_riskMitigationFixture.RiskMitigations[0].BusinessServiceName);
        result[0].InfraObjectId.ShouldBe(_riskMitigationFixture.RiskMitigations[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_riskMitigationFixture.RiskMitigations[0].InfraObjectName);
        result[0].UnderControlRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].UnderControlRTO);
        result[0].ExeedRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ExceedRTO);
        result[0].MaintenanceRTO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].MaintenanceRTO);
        result[0].RTODescription.ShouldBe(_riskMitigationFixture.RiskMitigations[0].RTODescription);
        result[0].UnderControlRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].UnderControlRPO);
        //result[0].ExeedRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ExceedRPO);
        result[0].MaintenanceRPO.ShouldBe(_riskMitigationFixture.RiskMitigations[0].MaintenanceRPO);
        result[0].RPODescription.ShouldBe(_riskMitigationFixture.RiskMitigations[0].RPODescription);
        result[0].IsAffected.ShouldBe(_riskMitigationFixture.RiskMitigations[0].IsAffected);
        result[0].ErrorMessage.ShouldBe(_riskMitigationFixture.RiskMitigations[0].ErrorMessage);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockRiskMitigationRepository = RiskMitigationRepositoryMocks.GetRiskMitigationEmptyRepository();

        var handler = new GetRiskMitigationListQueryHandler(_riskMitigationFixture.Mapper, _mockRiskMitigationRepository.Object);

        var result = await handler.Handle(new GetRiskMitigationListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetRiskMitigationListQuery(), CancellationToken.None);

        _mockRiskMitigationRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}