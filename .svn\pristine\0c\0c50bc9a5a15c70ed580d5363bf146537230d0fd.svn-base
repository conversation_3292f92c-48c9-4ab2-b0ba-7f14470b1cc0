﻿namespace ContinuityPatrol.Application.Features.Form.Commands.Create;

public class CreateFormCommand : IRequest<CreateFormResponse>
{
    public string Name { get; set; }

    public string CompanyId { get; set; }

    public string Type { get; set; }

    public string Properties { get; set; }

    public bool IsPublish { get; set; } = false;

    public string Version { get; set; }

    public bool IsLock { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}