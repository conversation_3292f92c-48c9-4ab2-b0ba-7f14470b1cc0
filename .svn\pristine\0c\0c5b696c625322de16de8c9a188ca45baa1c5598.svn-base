﻿using ContinuityPatrol.Application.Features.ServerType.Commands.Create;
using ContinuityPatrol.Application.Features.ServerType.Commands.Update;
using ContinuityPatrol.Application.Features.ServerType.Events.Create;
using ContinuityPatrol.Application.Features.ServerType.Events.Delete;
using ContinuityPatrol.Application.Features.ServerType.Events.PaginatedView;
using ContinuityPatrol.Application.Features.ServerType.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ServerTypeFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ServerType> ServerTypes { get; set; }
    public CreateServerTypeCommand CreateServerTypeCommand { get; set; }
    public UpdateServerTypeCommand UpdateServerTypeCommand { get; set; }
    public ServerTypeCreatedEvent ServerTypeCreatedEvent { get; set; }
    public ServerTypeDeletedEvent ServerTypeDeletedEvent { get; set; }
    public ServerTypeUpdatedEvent ServerTypeUpdatedEvent { get; set; }
    public ServerTypePaginatedEvent ServerTypePaginatedEvent { get; set; }

    public ServerTypeFixture()
    {
        ServerTypes = AutoServerTypeFixture.Create<List<ServerType>>();

        CreateServerTypeCommand = AutoServerTypeFixture.Create<CreateServerTypeCommand>();

        UpdateServerTypeCommand = AutoServerTypeFixture.Create<UpdateServerTypeCommand>();

        ServerTypeCreatedEvent = AutoServerTypeFixture.Create<ServerTypeCreatedEvent>();

        ServerTypeDeletedEvent = AutoServerTypeFixture.Create<ServerTypeDeletedEvent>();

        ServerTypeUpdatedEvent = AutoServerTypeFixture.Create<ServerTypeUpdatedEvent>();

        ServerTypePaginatedEvent = AutoServerTypeFixture.Create<ServerTypePaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ServerTypeProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoServerTypeFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateServerTypeCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateServerTypeCommand>(p => p.Name, 10));
            fixture.Customize<UpdateServerTypeCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<ServerType>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerTypeCreatedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerTypeDeletedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerTypeUpdatedEvent>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ServerTypePaginatedEvent>(p => p.Name, 10));

            return fixture;
        }
    }

    public void Dispose()
    {

    }

}
