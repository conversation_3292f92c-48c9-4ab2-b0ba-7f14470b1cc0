﻿using ContinuityPatrol.Application.Features.WorkflowDrCalender.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowDrCalender.Commands
{
   public class DeleteWorkflowDrCalenderTest : IClassFixture<WorkflowDrCalenderFixture>
    {
        private readonly Mock<IWorkflowDrCalenderRepository> _mockWorkflowDrCalenderRepository;

        private readonly Mock<IPublisher> _mockPublisher;

        private readonly DeleteWorkflowDrCalenderCommandHandler _handler;
        private readonly WorkflowDrCalenderFixture _workflowDrCalenderFixture;


        public DeleteWorkflowDrCalenderTest(WorkflowDrCalenderFixture workflowTempFixture)
        {
            _workflowDrCalenderFixture = workflowTempFixture;

            _mockPublisher = new Mock<IPublisher>();

            _mockWorkflowDrCalenderRepository = WorkflowDrCalenderRepositoryMocks.DeleteworkflowDrcalenderRepository(_workflowDrCalenderFixture.WorkflowDrCalenderInfos);

            _handler = new DeleteWorkflowDrCalenderCommandHandler(_mockWorkflowDrCalenderRepository.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task Handle_Return_Success_When_Delete_WorkflowTemp()
        {
            var validGuid = Guid.NewGuid().ToString();

            var workflowTemp = _workflowDrCalenderFixture.WorkflowDrCalenderInfos[0];

            workflowTemp.ReferenceId = validGuid;

            _mockWorkflowDrCalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(It.Is<string>(id => id == validGuid))).ReturnsAsync(workflowTemp);

            var result = await _handler.Handle(new DeleteWorkflowDrCalenderCommand { Id = validGuid }, CancellationToken.None);

            result.ShouldBeOfType(typeof(DeleteWorkflowDrCalenderResponse));

            result.IsActive.ShouldBeFalse();

            result.Message.ShouldContain($" WorkflowDrCalender '{workflowTemp.UserName}' has been deleted successfully");

            _mockWorkflowDrCalenderRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.WorkflowDrCalender>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Return_IsActive_False_When_Delete_WorkflowProfileInfo()
        {
            var validGuid = Guid.NewGuid().ToString();

            _mockWorkflowDrCalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(validGuid)).ReturnsAsync(_workflowDrCalenderFixture.WorkflowDrCalenderInfos[0]);

            var result = await _handler.Handle(new DeleteWorkflowDrCalenderCommand { Id = validGuid }, CancellationToken.None);

            var workflowProfileInfo = await _mockWorkflowDrCalenderRepository.Object.GetByReferenceIdAsync(validGuid);

            workflowProfileInfo.IsActive.ShouldBeFalse();

            result.ShouldBeOfType<DeleteWorkflowDrCalenderResponse>();

            result.IsActive.ShouldBeFalse();

            result.Message.ShouldContain("has been deleted successfully");
        }

        [Fact]
        public async Task Handle_NotFoundException_When_Invalid_WorkflowProfileInfoId()
        {
            var nonExistentGuid = Guid.NewGuid().ToString();

            _mockWorkflowDrCalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(nonExistentGuid)).ReturnsAsync((Domain.Entities.WorkflowDrCalender)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteWorkflowDrCalenderCommand { Id = nonExistentGuid }, CancellationToken.None));
        }



        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenProfileInfoNotFound()
        {
            var command = new DeleteWorkflowDrCalenderCommand { Id = Guid.NewGuid().ToString() };
            _mockWorkflowDrCalenderRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((Domain.Entities.WorkflowDrCalender)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
        }
    }
}
