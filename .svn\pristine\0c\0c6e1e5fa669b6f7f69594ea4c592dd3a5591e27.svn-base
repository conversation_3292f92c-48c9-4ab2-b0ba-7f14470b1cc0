using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class MSSQLDBMirroringLogsFixture : IDisposable
{
    public static string CompanyId => "COMPANY_123";
    public static string UserId => "USER_123";
    public static string InfraObjectId => "INFRA_123";
    public static string WorkflowId => "WORKFLOW_123";
    public static string Type => "MSSQLDBMirroring";

    public List<MSSQLDBMirroringLogs> MSSQLDBMirroringLogsPaginationList { get; set; }
    public List<MSSQLDBMirroringLogs> MSSQLDBMirroringLogsList { get; set; }
    public MSSQLDBMirroringLogs MSSQLDBMirroringLogsDto { get; set; }
    public ApplicationDbContext DbContext { get; private set; }

    private readonly Fixture _fixture;

    public MSSQLDBMirroringLogsFixture()
    {
        _fixture = new Fixture();

        // Configure AutoFixture to generate valid data
        _fixture.Customize<MSSQLDBMirroringLogs>(composer => composer
            .With(x => x.ReferenceId, () => Guid.NewGuid().ToString())
            .With(x => x.Type, () => Type)
            .With(x => x.InfraObjectId, () => InfraObjectId)
            .With(x => x.InfraObjectName, () => _fixture.Create<string>())
            .With(x => x.WorkflowId, () => WorkflowId)
            .With(x => x.WorkflowName, () => _fixture.Create<string>())
            .With(x => x.Properties, () => _fixture.Create<string>())
            .With(x => x.ConfiguredRPO, () => _fixture.Create<string>())
            .With(x => x.DataLagValue, () => _fixture.Create<string>())
            .With(x => x.Threshold, () => _fixture.Create<string>())
            .With(x => x.IsActive, true)
            .With(x => x.CreatedBy, UserId)
            .With(x => x.CreatedDate, DateTime.UtcNow)
            );

        MSSQLDBMirroringLogsPaginationList = _fixture.CreateMany<MSSQLDBMirroringLogs>(20).ToList();
        MSSQLDBMirroringLogsList = _fixture.CreateMany<MSSQLDBMirroringLogs>(5).ToList();
        MSSQLDBMirroringLogsDto = _fixture.Create<MSSQLDBMirroringLogs>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public MSSQLDBMirroringLogs CreateMSSQLDBMirroringLogsWithProperties(
        string type = null,
        string infraObjectId = null,
        string workflowId = null,
        bool isActive = true,
        DateTime? createdDate = null)
    {
        return _fixture.Build<MSSQLDBMirroringLogs>()
            .With(x => x.ReferenceId, Guid.NewGuid().ToString())
            .With(x => x.Type, type ?? Type)
            .With(x => x.InfraObjectId, infraObjectId ?? InfraObjectId)
            .With(x => x.WorkflowId, workflowId ?? WorkflowId)
            .With(x => x.IsActive, isActive)
            .With(x => x.CreatedDate, createdDate ?? DateTime.UtcNow)

            .With(x => x.CreatedBy, UserId)
            .Create();
    }

    public MSSQLDBMirroringLogs CreateMSSQLDBMirroringLogsWithWhitespace()
    {
        return CreateMSSQLDBMirroringLogsWithProperties(type: "  MSSQLDBMirroring  ");
    }

    public MSSQLDBMirroringLogs CreateMSSQLDBMirroringLogsWithLongType(int length)
    {
        var longType = new string('A', length);
        return CreateMSSQLDBMirroringLogsWithProperties(type: longType);
    }

    public MSSQLDBMirroringLogs CreateMSSQLDBMirroringLogsWithSpecificInfraObjectId(string infraObjectId)
    {
        return CreateMSSQLDBMirroringLogsWithProperties(infraObjectId: infraObjectId);
    }

    public List<MSSQLDBMirroringLogs> CreateMultipleMSSQLDBMirroringLogsWithSameType(string type, int count)
    {
        var logs = new List<MSSQLDBMirroringLogs>();
        for (int i = 0; i < count; i++)
        {
            logs.Add(CreateMSSQLDBMirroringLogsWithProperties(type: type, isActive: true));
        }
        return logs;
    }

    public List<MSSQLDBMirroringLogs> CreateMSSQLDBMirroringLogsWithMixedActiveStatus(string type)
    {
        return new List<MSSQLDBMirroringLogs>
        {
            CreateMSSQLDBMirroringLogsWithProperties(type: type, isActive: true),
            CreateMSSQLDBMirroringLogsWithProperties(type: type, isActive: false),
            CreateMSSQLDBMirroringLogsWithProperties(type: type, isActive: true)
        };
    }

    public List<MSSQLDBMirroringLogs> CreateMSSQLDBMirroringLogsWithDateRange(string infraObjectId, DateTime startDate, DateTime endDate, int count)
    {
        var logs = new List<MSSQLDBMirroringLogs>();
        var dateRange = (endDate - startDate).TotalDays;
        
        for (int i = 0; i < count; i++)
        {
            var randomDate = startDate.AddDays(Random.Shared.NextDouble() * dateRange);
            logs.Add(CreateMSSQLDBMirroringLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: randomDate));
        }
        return logs;
    }

    public List<MSSQLDBMirroringLogs> CreateMSSQLDBMirroringLogsOutsideDateRange(string infraObjectId, DateTime startDate, DateTime endDate)
    {
        return new List<MSSQLDBMirroringLogs>
        {
            CreateMSSQLDBMirroringLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: startDate.AddDays(-5)), // Before range
            CreateMSSQLDBMirroringLogsWithProperties(
                infraObjectId: infraObjectId,
                isActive: true,
                createdDate: endDate.AddDays(5)) // After range
        };
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }

    public static class TestData
    {
        public static readonly string[] CommonTypes = { "MSSQLDBMirroring", "DBMirroring", "MSSQL", "SQLMirroring" };
        public static readonly string[] CommonInfraObjectIds = { "INFRA_001", "INFRA_002", "INFRA_003" };
        public static readonly string[] CommonWorkflowIds = { "WF_001", "WF_002", "WF_003" };
        public static readonly string[] SpecialCharacterTypes = { "Type@#$%", "Type with spaces", "Type_with_underscores", "Type-with-dashes" };
    }
}
