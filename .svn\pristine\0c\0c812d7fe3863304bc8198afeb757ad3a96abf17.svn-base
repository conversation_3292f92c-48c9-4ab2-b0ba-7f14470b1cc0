﻿using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoSiteTypeDataAttribute : AutoDataAttribute
{
    public AutoSiteTypeDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateSiteTypeCommand>(p => p.Type, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateSiteTypeCommand>(p => p.Type, 10));

            return fixture;
        })
    {

    }
}