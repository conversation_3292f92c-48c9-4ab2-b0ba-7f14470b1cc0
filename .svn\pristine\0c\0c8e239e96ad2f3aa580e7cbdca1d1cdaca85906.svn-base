﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.FormHistory.Events.Update;

public class FormHistoryUpdatedEventHandler : INotificationHandler<FormHistoryUpdatedEvent>
{
    private readonly ILogger<FormHistoryUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormHistoryUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<FormHistoryUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(FormHistoryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.FormHistory}",
            Entity = Modules.FormHistory.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"FormHistory '{updatedEvent.FormName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"FormHistory '{updatedEvent.FormName}' updated successfully.");
    }
}