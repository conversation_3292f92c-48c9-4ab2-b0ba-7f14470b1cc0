﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IUserInfraObjectRepository : IRepository<UserInfraObject>
{
    Task<UserInfraObject> GetUserInfraObjectByUserIdAsync(string userId);

    Task<List<UserInfraObject>> GetByUserIdAndProperties();
    Task<UserInfraObject> GetUserInfraObjectPropsByUserIdAsync(string userId);
}