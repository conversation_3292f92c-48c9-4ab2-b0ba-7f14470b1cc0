﻿using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Events.Create;
using ContinuityPatrol.Application.Features.User.Events.Delete;
using ContinuityPatrol.Application.Features.User.Events.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Events.PaginatedView;
using ContinuityPatrol.Application.Features.User.Events.PasswordUpdate;
using ContinuityPatrol.Application.Features.User.Events.ResetPassword;
using ContinuityPatrol.Application.Features.User.Events.SendEmail;
using ContinuityPatrol.Application.Features.User.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class UserFixture : UserInfoFixture
{
    public new IMapper Mapper { get; }

    public List<User> Users { get; set; }

    public List<UserLogin> UserLogins { get; set; }

    public List<UserCredential> UserCredentials { get; set; }

    public CreateUserCommand CreateUserCommand { get; set; }

    public CreateDefaultUserCommand CreateDefaultUserCommand { get; set; }

    public UpdateUserCommand UpdateUserCommand { get; set; }

    public UpdatePasswordCommand UpdatePasswordCommand { get; set; }

    public UserCreatedEvent UserCreatedEvent { get; set; }
    public UserDeletedEvent UserDeletedEvent { get; set; }
    public UserUpdatedEvent UserUpdatedEvent { get; set; }
    public PasswordUpdatedEvent PasswordUpdatedEvent { get; set; }
    public ForgotPasswordUpdatedEvent ForgotPasswordUpdatedEvent { get; set; }
    public UserPaginatedEvent UserPaginatedEvent { get; set; }
    public ResetPasswordUpdatedEvent ResetPasswordUpdatedEvent { get; set; }
    public CreateSendEmailEvent CreateSendEmailEvent { get; set; }

    public UserFixture()
    {
        Users = AutoUserFixture.Create<List<User>>();

        UserLogins = AutoUserFixture.Create<List<UserLogin>>();

        UserCredentials = AutoUserFixture.Create<List<UserCredential>>();

        CreateUserCommand = AutoUserFixture.Create<CreateUserCommand>();

        CreateDefaultUserCommand = AutoUserFixture.Create<CreateDefaultUserCommand>();

        UpdateUserCommand = AutoUserFixture.Create<UpdateUserCommand>();

        UpdatePasswordCommand = AutoUserFixture.Create<UpdatePasswordCommand>();

        UserCreatedEvent = AutoUserFixture.Create<UserCreatedEvent>();

        UserDeletedEvent = AutoUserFixture.Create<UserDeletedEvent>();

        UserUpdatedEvent = AutoUserFixture.Create<UserUpdatedEvent>();

        PasswordUpdatedEvent = AutoUserFixture.Create<PasswordUpdatedEvent>();

        ForgotPasswordUpdatedEvent = AutoUserFixture.Create<ForgotPasswordUpdatedEvent>();

        UserPaginatedEvent = AutoUserFixture.Create<UserPaginatedEvent>();

        ResetPasswordUpdatedEvent = AutoUserFixture.Create<ResetPasswordUpdatedEvent>();

        CreateSendEmailEvent = AutoUserFixture.Create<CreateSendEmailEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<UserProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoUserFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };
            fixture.Create<List<UserCredential>>();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateUserCommand>(p => p.LoginName, 10));
            fixture.Customize<CreateUserCommand>(c => c.With(b => b.CompanyId, 0.ToString));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateUserCommand>(p => p.LoginName, 10));
            fixture.Customize<UpdateUserCommand>(c => c.With(b => b.Id, 0.ToString));
            fixture.Customize<User>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdatePasswordCommand>(p => p.LoginName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserCreatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserDeletedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserUpdatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<PasswordUpdatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ForgotPasswordUpdatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserPaginatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ResetPasswordUpdatedEvent>(p => p.UserName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateSendEmailEvent>(p => p.LoginName, 10));

            return fixture;
        }
    }

    public new static void Dispose()
    {

    }
}