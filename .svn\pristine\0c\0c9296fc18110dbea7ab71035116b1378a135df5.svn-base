using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.Update;

namespace ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Commands.Update;

public class UpdateApprovalMatrixUsersCommandHandler : IRequestHandler<UpdateApprovalMatrixUsersCommand, UpdateApprovalMatrixUsersResponse>
{
    private readonly IApprovalMatrixUsersRepository _approvalMatrixUsersRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public UpdateApprovalMatrixUsersCommandHandler(IMapper mapper, IApprovalMatrixUsersRepository approvalMatrixUsersRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _approvalMatrixUsersRepository = approvalMatrixUsersRepository;
        _publisher = publisher;
    }

    public async Task<UpdateApprovalMatrixUsersResponse> Handle(UpdateApprovalMatrixUsersCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _approvalMatrixUsersRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.ApprovalMatrixUsers), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateApprovalMatrixUsersCommand), typeof(Domain.Entities.ApprovalMatrixUsers));

        await _approvalMatrixUsersRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateApprovalMatrixUsersResponse
        {
            Message = Message.Update(nameof(Domain.Entities.ApprovalMatrixUsers), eventToUpdate.UserName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new ApprovalMatrixUsersUpdatedEvent { UserName = eventToUpdate.UserName }, cancellationToken);

        return response;
    }
}
