﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Form.Events.Import;

public class FormImportEventHandler : INotificationHandler<FormImportEvent>
{
    private readonly ILogger<FormImportEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public FormImportEventHandler(ILogger<FormImportEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(FormImportEvent formPublishEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $" {ActivityType.Import}{Modules.Form}",
            Entity = Modules.Form.ToString(),
            ActivityType = ActivityType.Import.ToString(),
            ActivityDetails =
                $"Form Builder imported successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation(
            $"Form Builder imported successfully.");
    }
}
