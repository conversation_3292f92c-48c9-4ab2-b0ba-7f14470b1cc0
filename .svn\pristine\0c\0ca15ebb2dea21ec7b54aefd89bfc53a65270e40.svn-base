using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Delete;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNames;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Services.Db.Impl.Configuration;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Configuration;

public class ReplicationServiceTests : BaseServiceTestSetup<ReplicationService>, IClassFixture<ReplicationFixture>
{
    private readonly ReplicationFixture _fixture;

    public ReplicationServiceTests(ReplicationFixture fixture)
    {
        InitializeService(accessor => new ReplicationService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetReplicationList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetReplicationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ReplicationListVm);

        var result = await ServiceUnderTest.GetReplicationList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetReplicationNames_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetReplicationNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ReplicationNameVm);

        var result = await ServiceUnderTest.GetReplicationNames();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetReplicationById_Should_Return_Detail()
    {
        var replicationId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetReplicationDetailQuery>(q => q.Id == replicationId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ReplicationDetailVm);

        var result = await ServiceUnderTest.GetReplicationById(replicationId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.ReplicationDetailVm.Id, result.Id);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateReplicationResponse
        {
            Message = "Created",
            ReplicationId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateReplicationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateReplicationCommand);

        Assert.NotNull(result);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateReplicationResponse
        {
            Message = "Updated",
            ReplicationId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateReplicationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateReplicationCommand);

        Assert.NotNull(result);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var replicationId = Guid.NewGuid().ToString();
        var response = new DeleteReplicationResponse
        {
            Message = "Deleted",
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteReplicationCommand>(c => c.Id == replicationId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(replicationId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task SaveAsReplication_Should_Return_Success()
    {
        var response = new SaveAsReplicationResponse
        {
            Message = "SaveAs successful",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.SaveAsReplicationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.SaveAsReplication(_fixture.SaveAsReplicationCommand);

        Assert.True(result.Success);
        Assert.Equal("SaveAs successful", result.Message);
    }

    [Fact]
    public async Task SaveAllReplication_Should_Return_Success()
    {
        var response = new SaveAllReplicationResponse
        {
            Message = "SaveAll successful"
        };

        MediatorMock.Setup(m => m.Send(_fixture.SaveAllReplicationCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.SaveAllReplication(_fixture.SaveAllReplicationCommand);

        Assert.True(result.Success);
        Assert.Equal("SaveAll successful", result.Message);
    }

    [Theory]
    [InlineData("Replication1", "some-guid")]
    [InlineData("AnotherName", "another-guid")]
    public async Task IsReplicationNameExist_Should_Return_True(string name, string id)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetReplicationNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsReplicationNameExist(name, id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetReplicationByLicenseKey_Should_Return_Data()
    {
        var licenseId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetReplicationByLicenseKeyQuery>(q => q.LicenseId == licenseId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetReplicationByLicenseKeyListVm);

        var result = await ServiceUnderTest.GetReplicationByLicenseKey(licenseId);

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetReplicationByType_Should_Return_Data()
    {
        var typeId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetReplicationTypeQuery>(q => q.TypeId == typeId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ReplicationTypeVm);

        var result = await ServiceUnderTest.GetReplicationByType(typeId);

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetPaginatedReplications_Should_Return_Result()
    {
        var query = _fixture.GetReplicationPaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedReplicationListVm);

        var result = await ServiceUnderTest.GetPaginatedReplications(query);

        Assert.Equal(_fixture.ReplicationListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetReplicationById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetReplicationById(null!));
    }

    [Fact]
    public async Task GetReplicationById_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetReplicationById(""));
    }

    [Fact]
    public async Task IsReplicationNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.IsReplicationNameExist(null!, "id"));
    }

    [Fact]
    public async Task IsReplicationNameExist_EmptyName_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.IsReplicationNameExist("", "id"));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }

    [Fact]
    public async Task SaveAllReplication_NullOrEmptyReplicationId_Should_Throw_Exception()
    {
        var command = _fixture.SaveAllReplicationCommand;
        command.ReplicationId = "";

        await Assert.ThrowsAsync<Exception>(() => ServiceUnderTest.SaveAllReplication(command));
    }
}
