using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Create;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Commands.Update;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftCategoryMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftCategoryMasterModel;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;


namespace ContinuityPatrol.Services.Db.Impl.Drift;

public class DriftCategoryMasterService : BaseService, IDriftCategoryMasterService
{
    public DriftCategoryMasterService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<List<DriftCategoryMasterListVm>> GetDriftCategoryMasterList()
    {
        Logger.LogDebug("Get All DriftCategoryMasters");

        return await Mediator.Send(new GetDriftCategoryMasterListQuery());
    }

    public async Task<DriftCategoryMasterDetailVm> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftCategoryMaster Id");

        Logger.LogDebug($"Get DriftCategoryMaster Detail by Id '{id}'");

        return await Mediator.Send(new GetDriftCategoryMasterDetailQuery { Id = id });
    }


    public async Task<BaseResponse> CreateAsync(CreateDriftCategoryMasterCommand createDriftCategoryMasterCommand)
    {
        Logger.LogDebug($"Create DriftCategoryMaster '{createDriftCategoryMasterCommand}'");

        return await Mediator.Send(createDriftCategoryMasterCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDriftCategoryMasterCommand updateDriftCategoryMasterCommand)
    {
        Logger.LogDebug($"Update DriftCategoryMaster '{updateDriftCategoryMasterCommand}'");

        return await Mediator.Send(updateDriftCategoryMasterCommand);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftCategoryMaster Id");

        Logger.LogDebug($"Delete DriftCategoryMaster Details by Id '{id}'");

        return await Mediator.Send(new DeleteDriftCategoryMasterCommand { Id = id });
    }
    #region NameExist
    public async Task<bool> IsDriftCategoryMasterNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "DriftCategoryMaster Name");

        Logger.LogDebug($"Check Name Exists Detail by DriftCategoryMaster Name '{name}' and Id '{id}'");

        return await Mediator.Send(new GetDriftCategoryMasterNameUniqueQuery { Name = name, Id = id });
    }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<DriftCategoryMasterListVm>> GetPaginatedDriftCategoryMasters(GetDriftCategoryMasterPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DriftCategoryMaster Paginated List");

        return await Mediator.Send(query);
    }
    #endregion
}
