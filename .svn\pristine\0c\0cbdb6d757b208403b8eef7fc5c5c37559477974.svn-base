﻿using ContinuityPatrol.Application.Features.InfraObjectSchedulerWorkflowDetail.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectSchedulerWorkflowDetail.Commands;

public class DeleteInfraObjectSchedulerWorkflowDetailTests : IClassFixture<InfraObjectSchedulerWorkflowDetailFixture>
{
    private readonly InfraObjectSchedulerWorkflowDetailFixture _infraObjectSchedulerWorkflowDetailFixture;
    private readonly Mock<IInfraObjectSchedulerWorkflowDetailRepository> _mockInfraObjectSchedulerWorkflowDetailRepository;
    private readonly DeleteInfraObjectSchedulerWorkflowDetailCommandHandler _handler;

    public DeleteInfraObjectSchedulerWorkflowDetailTests(InfraObjectSchedulerWorkflowDetailFixture infraObjectSchedulerWorkflowDetailFixture)
    {
        _infraObjectSchedulerWorkflowDetailFixture = infraObjectSchedulerWorkflowDetailFixture;

        _mockInfraObjectSchedulerWorkflowDetailRepository = InfraObjectSchedulerWorkflowDetailRepositoryMocks.DeleteInfraObjectSchedulerWorkflowDetailRepository(_infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails);

        _handler = new DeleteInfraObjectSchedulerWorkflowDetailCommandHandler(_mockInfraObjectSchedulerWorkflowDetailRepository.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_InfraObjectSchedulerWorkflowDetailDeleted()
    {
        var result = await _handler.Handle(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteInfraObjectSchedulerWorkflowDetailResponse_When_InfraObjectSchedulerWorkflowDetailDeleted()
    {
        var result = await _handler.Handle(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteInfraObjectSchedulerWorkflowDetailResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_InfraObjectSchedulerWorkflowDetailDeleted()
    {
        await _handler.Handle(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId }, CancellationToken.None);

        var accessManager = await _mockInfraObjectSchedulerWorkflowDetailRepository.Object.GetByReferenceIdAsync(_infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId);

        accessManager.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidInfraObjectSchedulerWorkflowDetailId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteInfraObjectSchedulerWorkflowDetailCommand { Id = _infraObjectSchedulerWorkflowDetailFixture.InfraObjectSchedulerWorkflowDetails[0].ReferenceId }, CancellationToken.None);

        _mockInfraObjectSchedulerWorkflowDetailRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockInfraObjectSchedulerWorkflowDetailRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraObjectSchedulerWorkflowDetail>()), Times.Once);
    }
}