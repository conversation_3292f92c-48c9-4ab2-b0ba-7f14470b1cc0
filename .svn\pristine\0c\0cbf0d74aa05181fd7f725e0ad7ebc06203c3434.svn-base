using ContinuityPatrol.Application.Features.SiteType.Commands.Create;
using ContinuityPatrol.Application.Features.SiteType.Commands.Update;
using ContinuityPatrol.Application.Features.SiteType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class SiteTypeFixture
{
    public List<SiteTypeListVm> SiteTypeListVm { get; }
    public CreateSiteTypeCommand CreateSiteTypeCommand { get; }
    public UpdateSiteTypeCommand UpdateSiteTypeCommand { get; }
    public GetSiteTypePaginatedListQuery GetSiteTypePaginatedListQuery { get; }
    public PaginatedResult<SiteTypeListVm> PaginatedSiteTypeListVm { get; }

    public SiteTypeFixture()
    {
        var fixture = new Fixture();

        SiteTypeListVm = fixture.Create<List<SiteTypeListVm>>();
        CreateSiteTypeCommand = fixture.Create<CreateSiteTypeCommand>();
        UpdateSiteTypeCommand = fixture.Create<UpdateSiteTypeCommand>();
        GetSiteTypePaginatedListQuery = fixture.Create<GetSiteTypePaginatedListQuery>();
        PaginatedSiteTypeListVm = fixture.Create<PaginatedResult<SiteTypeListVm>>();
    }

    public void Dispose()
    {

    }
}
