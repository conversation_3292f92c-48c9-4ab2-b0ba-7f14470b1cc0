﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.SiteType.Events.Update;

public class SiteTypeUpdatedEventHandler : INotificationHandler<SiteTypeUpdatedEvent>
{
    private readonly ILogger<SiteTypeUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SiteTypeUpdatedEventHandler(ILoggedInUserService userService, ILogger<SiteTypeUpdatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SiteTypeUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var siteTypeActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Entity = Modules.SiteType.ToString(),
            Action = $"{ActivityType.Update} {Modules.SiteType}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $" Site Type '{updatedEvent.Type}' updated successfully."
        };

        await _userActivityRepository.AddAsync(siteTypeActivity);

        _logger.LogInformation($"Site Type '{updatedEvent.Type}' updated successfully.");
    }
}