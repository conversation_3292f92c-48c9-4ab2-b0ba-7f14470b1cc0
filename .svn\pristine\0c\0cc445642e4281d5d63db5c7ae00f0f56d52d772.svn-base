﻿using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Views;
using Moq;
using Shouldly;

namespace ContinuityPatrol.Application.UnitTests.Features.Server.Queries;

public class GetServerRoleTypeQueryHandlerTests : IClassFixture<ServerFixture>
{
    private readonly ServerFixture _serverFixture;
    private readonly Mock<IServerViewRepository> _mockServerViewRepository;
    private readonly GetServerRoleTypeQueryHandler _handler;

    public GetServerRoleTypeQueryHandlerTests(ServerFixture serverFixture)
    {
        _serverFixture = serverFixture;
        _mockServerViewRepository = new Mock<IServerViewRepository>();
        _handler = new GetServerRoleTypeQueryHandler(_serverFixture.Mapper, _mockServerViewRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnsMappedResult_When_RoleTypeId_Provided()
    {
        // Arrange
        var roleTypeId = _serverFixture.Servers[0].RoleType;
        var serverViews = _serverFixture.Servers.Select(s => new ServerView
        {
            ReferenceId = s.ReferenceId,
            Name = s.Name,
            RoleType = s.RoleType,
            ServerType = s.ServerType,
            ServerTypeId = s.ServerTypeId,
            SiteId = s.SiteId,
            SiteName = s.SiteName,
            Status = s.Status,
            OSType = s.OSType,
            Properties = s.Properties,
            LicenseKey = s.LicenseKey
        }).ToList();

        _mockServerViewRepository
            .Setup(repo => repo.GetRoleType(roleTypeId))
            .ReturnsAsync(serverViews);

        // Act
        var result = await _handler.Handle(new GetServerRoleTypeQuery { RoleTypeId = roleTypeId }, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<List<ServerRoleTypeVm>>();
        result.Count.ShouldBe(serverViews.Count);
    }

    [Fact]
    public async Task Handle_FiltersByServerType_WhenBothRoleTypeIdAndServerTypeIdProvided()
    {
        // Arrange
        var roleTypeId = _serverFixture.Servers[0].RoleType;
        var serverTypeId = _serverFixture.Servers[0].ServerTypeId;

        var serverViews = _serverFixture.Servers.Select(s => new ServerView
        {
            ReferenceId = s.ReferenceId,
            Name = s.Name,
            RoleType = s.RoleType,
            ServerType = s.ServerType,
            ServerTypeId = s.ServerTypeId
        }).ToList();

        _mockServerViewRepository
            .Setup(repo => repo.GetRoleType(roleTypeId))
            .ReturnsAsync(serverViews);

        var query = new GetServerRoleTypeQuery
        {
            RoleTypeId = roleTypeId,
            ServerTypeId = serverTypeId
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.All(x => x.ServerType == _serverFixture.Servers[0].ServerType).ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_ReturnsListAll_WhenRoleTypeIdIsNull()
    {
        // Arrange
        var serverViews = _serverFixture.Servers.Select(s => new ServerView
        {
            ReferenceId = s.ReferenceId,
            Name = s.Name
        }).ToList();

        _mockServerViewRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(serverViews);

        var query = new GetServerRoleTypeQuery
        {
            RoleTypeId = null
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(serverViews.Count);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenNoMatchingRecords()
    {
        // Arrange
        _mockServerViewRepository
            .Setup(repo => repo.GetRoleType(It.IsAny<string>()))
            .ReturnsAsync(new List<ServerView>());

        var query = new GetServerRoleTypeQuery
        {
            RoleTypeId = "non-existent"
        };

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task Handle_CallsGetRoleType_Once_WhenRoleTypeIdProvided()
    {
        // Arrange
        var roleTypeId = _serverFixture.Servers[0].RoleType;

        _mockServerViewRepository
            .Setup(repo => repo.GetRoleType(roleTypeId))
            .ReturnsAsync(new List<ServerView>());

        var query = new GetServerRoleTypeQuery
        {
            RoleTypeId = roleTypeId
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockServerViewRepository.Verify(x => x.GetRoleType(roleTypeId), Times.Once);
    }

    [Fact]
    public async Task Handle_CallsListAllAsync_WhenRoleTypeIdIsNull()
    {
        // Arrange
        _mockServerViewRepository
            .Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(new List<ServerView>());

        var query = new GetServerRoleTypeQuery
        {
            RoleTypeId = null
        };

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockServerViewRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
    [Fact]
    public void ServerRoleTypeVm_Should_Assign_And_Assert_All_Properties()
    {
        // Arrange
        var vm = new ServerRoleTypeVm
        {
            Id = "srv-001",
            Name = "RoleType Server",
            SiteId = "site-001",
            SiteName = "Primary Site",
            RoleTypeId = "role-001",
            RoleType = "Web Server",
            ServerTypeId = "stype-001",
            ServerType = "Application",
            OSTypeId = "os-001",
            OSType = "Linux",
            Status = "Active",
            Properties = "{ \"CPU\": \"8 Core\" }",
            LicenseId = "lic-001",
            LicenseKey = "ABC-123-XYZ",
            Version = "v1.0.0",
            BusinessServiceId = "bs-001",
            BusinessServiceName = "Finance Service",
            ExceptionMessage = "None",
            FormVersion = "1.2",
            IsAttached = true
        };

        // Assert
        vm.Id.Should().Be("srv-001");
        vm.Name.Should().Be("RoleType Server");
        vm.SiteId.Should().Be("site-001");
        vm.SiteName.Should().Be("Primary Site");
        vm.RoleTypeId.Should().Be("role-001");
        vm.RoleType.Should().Be("Web Server");
        vm.ServerTypeId.Should().Be("stype-001");
        vm.ServerType.Should().Be("Application");
        vm.OSTypeId.Should().Be("os-001");
        vm.OSType.Should().Be("Linux");
        vm.Status.Should().Be("Active");
        vm.Properties.Should().Be("{ \"CPU\": \"8 Core\" }");
        vm.LicenseId.Should().Be("lic-001");
        vm.LicenseKey.Should().Be("ABC-123-XYZ");
        vm.Version.Should().Be("v1.0.0");
        vm.BusinessServiceId.Should().Be("bs-001");
        vm.BusinessServiceName.Should().Be("Finance Service");
        vm.ExceptionMessage.Should().Be("None");
        vm.FormVersion.Should().Be("1.2");
        vm.IsAttached.Should().BeTrue();
    }
}