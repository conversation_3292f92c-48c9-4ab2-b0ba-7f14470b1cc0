using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class PageBuilderService : IPageBuilderService
{
    private readonly IBaseClient _client;

    public PageBuilderService(IBaseClient client)
    {
        _client = client;
    }
   
    public async Task<List<PageBuilderListVm>> GetPageBuilderList()
    {
        var request = new RestRequest("api/v6/pagebuilders");

        return await  _client. GetFromCache<List<PageBuilderListVm>>(request, "GetPageBuilderList");
    }

    public async Task<BaseResponse> CreateAsync(CreatePageBuilderCommand createPageBuilderCommand)
    {
        var request = new RestRequest("api/v6/pagebuilders", Method.Post);

        request.AddJsonBody(createPageBuilderCommand);

        return await  _client. Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdatePageBuilderCommand updatePageBuilderCommand)
    {
        var request = new RestRequest("api/v6/pagebuilders", Method.Put);

        request.AddJsonBody(updatePageBuilderCommand);

        return await  _client. Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/pagebuilders/{id}", Method.Delete);

        return await  _client. Delete<BaseResponse>(request);
    }

    public async Task<PageBuilderDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/pagebuilders/{id}");

        return await  _client. Get<PageBuilderDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsPageBuilderNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/pagebuilders/name-exist?pagebuilderName={name}&id={id}");

     return await  _client. Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<PageBuilderListVm>> GetPaginatedPageBuilders(GetPageBuilderPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/pagebuilders/paginated-list");

      return await  _client. Get<PaginatedResult<PageBuilderListVm>>(request);
  }
   #endregion
}
