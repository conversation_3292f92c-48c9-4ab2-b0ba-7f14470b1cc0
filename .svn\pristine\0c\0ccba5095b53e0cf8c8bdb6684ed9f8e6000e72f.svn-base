﻿using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class WorkflowOperationRepositoryMocks 
{
    public static Mock<IWorkflowOperationRepository> CreateWorkflowOperationRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();
        workflowOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperations);
        workflowOperationRepository.Setup(repo => repo.AddAsync(It.IsAny<WorkflowOperation>())).ReturnsAsync(
            (WorkflowOperation workflowOperation) =>
            {
                workflowOperation.Id = new Fixture().Create<int>();
                workflowOperation.ReferenceId = new Fixture().Create<Guid>().ToString();
                workflowOperations.Add(workflowOperation);
                return workflowOperation;
            });

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> UpdateWorkflowOperationRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperations);

        workflowOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperations.SingleOrDefault(x => x.ReferenceId == i));

        workflowOperationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowOperation>())).ReturnsAsync((WorkflowOperation workflowOperation) =>
        {
            var index = workflowOperations.FindIndex(item => item.ReferenceId == workflowOperation.ReferenceId);
            workflowOperations[index] = workflowOperation;
            return workflowOperation;
        });

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> DeleteWorkflowOperationRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();
        workflowOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperations);

        workflowOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperations.SingleOrDefault(x => x.ReferenceId == i));

        workflowOperationRepository.Setup(repo => repo.UpdateAsync(It.IsAny<WorkflowOperation>())).ReturnsAsync((WorkflowOperation workflowOperation) =>
        {
            var index = workflowOperations.FindIndex(item => item.ReferenceId == workflowOperation.ReferenceId);
            workflowOperation.IsActive = false;
            workflowOperations[index] = workflowOperation;

            return workflowOperation;
        });

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> GetWorkflowOperationRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(workflowOperations);

        workflowOperationRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => workflowOperations.SingleOrDefault(x => x.ReferenceId == i));

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> GetWorkflowOperationNamesRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository.Setup(repo => repo.GetWorkflowOperationNames()).ReturnsAsync(workflowOperations);

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> GetWorkflowOperationEmptyRepository()
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<WorkflowOperation>());

        return workflowOperationRepository;
    }

    public static Mock<IWorkflowOperationRepository> GetPaginatedWorkflowOperationRepository(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository.Setup(repo => repo.PaginatedListAllAsync(
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<Specification<WorkflowOperation>>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
            .ReturnsAsync((int pageNumber, int pageSize, Specification<WorkflowOperation> spec, string sortColumn, string sortOrder) =>
            {
                var sortedCompanies = workflowOperations.AsQueryable();

                if (spec.Criteria != null)
                {
                    sortedCompanies = sortedCompanies.Where(spec.Criteria);
                }

                if (!string.IsNullOrWhiteSpace(sortColumn))
                {
                    // Assuming Company has a Name property; replace logic as needed
                    sortedCompanies = string.Equals(sortOrder, "desc", StringComparison.OrdinalIgnoreCase)
                        ? sortedCompanies.OrderByDescending(c => c.ProfileName)
                        : sortedCompanies.OrderBy(c => c.ProfileName);
                }

                var totalCount = sortedCompanies.Count();
                var paginated = sortedCompanies
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return PaginatedResult<WorkflowOperation>.Success(paginated, totalCount, pageNumber, pageSize);
            });
        return workflowOperationRepository;
    }


    public static Mock<IWorkflowOperationRepository> GetWorkflowOperationGroupRunningStatus(List<WorkflowOperation> workflowOperations)
    {
        var workflowOperationRepository = new Mock<IWorkflowOperationRepository>();

        workflowOperationRepository
            .Setup(repo => repo.GetWorkflowOperationByRunningUserId(It.IsAny<List<string>>()))
            .ReturnsAsync((List<string> userIds) =>
                workflowOperations
                    .Where(x => x.Status.Trim().ToLower() == "running" && userIds.Contains(x.CreatedBy))
                    .ToList());
        workflowOperationRepository
            .Setup(repo => repo.GetWorkflowOperationByRunningStatus())
            .ReturnsAsync(workflowOperations);

        return workflowOperationRepository;
    }

    //Events
    public static Mock<IUserActivityRepository> CreateWorkflowOperationEventRepository(List<UserActivity> userActivities)
    {
        var workflowOperationEventRepository = new Mock<IUserActivityRepository>();
       
        workflowOperationEventRepository.Setup(repo => repo.AddAsync(It.IsAny<UserActivity>())).ReturnsAsync(
          (UserActivity userActivity) =>
          {
              userActivity.LoginName = new Fixture().Create<string>();

              userActivities.Add(userActivity);

              return userActivity;
          });

        return workflowOperationEventRepository;
    }
}