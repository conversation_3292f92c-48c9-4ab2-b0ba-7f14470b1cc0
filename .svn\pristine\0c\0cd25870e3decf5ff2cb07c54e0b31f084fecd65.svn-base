﻿namespace ContinuityPatrol.Application.Features.LicenseHistory.Commands.Create;

public class
    CreateLicenseHistoryCommandHandler : IRequestHandler<CreateLicenseHistoryCommand, CreateLicenseHistoryResponse>
{
    private readonly ILicenseHistoryRepository _licenseHistoryRepository;
    private readonly IMapper _mapper;

    public CreateLicenseHistoryCommandHandler(ILicenseHistoryRepository licenseHistoryRepository, IMapper mapper)
    {
        _licenseHistoryRepository = licenseHistoryRepository;
        _mapper = mapper;
    }

    public async Task<CreateLicenseHistoryResponse> Handle(CreateLicenseHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var licenseHistoryRequest = _mapper.Map<Domain.Entities.LicenseHistory>(request);

        licenseHistoryRequest = await _licenseHistoryRepository.AddAsync(licenseHistoryRequest);

        var response = new CreateLicenseHistoryResponse
        {
            LicenseHistoryId = licenseHistoryRequest.ReferenceId,

            Message = $"LicenseKey '{licenseHistoryRequest.PONumber}' Added Successfully."
        };

        return response;
    }
}