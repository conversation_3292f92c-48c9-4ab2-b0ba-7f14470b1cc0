﻿namespace ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationCategoryType;

public class GetInfraObjectListByReplicationCategoryTypeQueryHandler : IRequestHandler<
    GetInfraObjectListByReplicationCategoryTypeQuery, List<InfraObjectListByReplicationCategoryTypeVm>>
{
    private readonly IInfraObjectViewRepository _infraObjectViewRepository;
    private readonly IMapper _mapper;

    public GetInfraObjectListByReplicationCategoryTypeQueryHandler(IInfraObjectViewRepository infraObjectViewRepository,
        IMapper mapper)
    {
        _infraObjectViewRepository = infraObjectViewRepository;
        _mapper = mapper;
    }

    public async Task<List<InfraObjectListByReplicationCategoryTypeVm>> Handle(
        GetInfraObjectListByReplicationCategoryTypeQuery request, CancellationToken cancellationToken)
    {
        var infraObjectList =
            await _infraObjectViewRepository.GetInfraObjectListByReplicationCategoryType(request.ReplicationCategoryTypeId);

        return infraObjectList.Count != 0
            ? _mapper.Map<List<InfraObjectListByReplicationCategoryTypeVm>>(infraObjectList)
            : new List<InfraObjectListByReplicationCategoryTypeVm>();
    }
}