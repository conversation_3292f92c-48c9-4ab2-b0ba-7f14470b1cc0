using ContinuityPatrol.Application.Features.SiteLocation.Commands.Create;
using ContinuityPatrol.Application.Features.SiteLocation.Commands.Update;
using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.SiteLocation.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.SiteLocationModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class SiteLocationFixture
{
    public List<SiteLocationListVm> SiteLocationListVm { get; }
    public PaginatedResult<SiteLocationListVm> PaginatedSiteLocationListVm { get; }
    public SiteLocationDetailVm SiteLocationDetailVm { get; }
    public CreateSiteLocationCommand CreateSiteLocationCommand { get; }
    public UpdateSiteLocationCommand UpdateSiteLocationCommand { get; }
    public GetSiteLocationPaginatedListQuery GetSiteLocationPaginatedListQuery { get; }

    public SiteLocationFixture()
    {
        var fixture = new Fixture();

        SiteLocationListVm = fixture.Create<List<SiteLocationListVm>>();
        PaginatedSiteLocationListVm = fixture.Create<PaginatedResult<SiteLocationListVm>>();
        SiteLocationDetailVm = fixture.Create<SiteLocationDetailVm>();
        CreateSiteLocationCommand = fixture.Create<CreateSiteLocationCommand>();
        UpdateSiteLocationCommand = fixture.Create<UpdateSiteLocationCommand>();
        GetSiteLocationPaginatedListQuery = fixture.Create<GetSiteLocationPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
