﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.GroupPolicy.Event.Create;

public class GroupPolicyCreatedEventHandler : INotificationHandler<GroupPolicyCreatedEvent>
{
    private readonly ILogger<GroupPolicyCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public GroupPolicyCreatedEventHandler(ILogger<GroupPolicyCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(GroupPolicyCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var result = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.GroupPolicy}",
            Entity = Modules.GroupPolicy.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Group Node Policy '{createdEvent.GroupPolicyName}' created successfully."
        };

        await _userActivityRepository.AddAsync(result);

        _logger.LogInformation($"Group Node Policy '{createdEvent.GroupPolicyName}' created successfully.");
    }
}