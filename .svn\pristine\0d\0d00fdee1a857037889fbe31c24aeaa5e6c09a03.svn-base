using ContinuityPatrol.Application.Features.TableAccess.Commands.Create;
using ContinuityPatrol.Application.Features.TableAccess.Commands.Update;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetSchemaNameList;
using ContinuityPatrol.Application.Features.TableAccess.Queries.GetTableNameListBySchema;
using ContinuityPatrol.Domain.ViewModels.TableAccessModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class TableAccessFixture
{
    public List<TableAccessListVm> TableAccessListVm { get; }
    public CreateTableAccessCommand CreateTableAccessCommand { get; }
    public UpdateTableAccessCommand UpdateTableAccessCommand { get; }
    public GetTableAccessPaginatedListQuery GetTableAccessPaginatedListQuery { get; }
    public PaginatedResult<TableAccessListVm> PaginatedTableAccessListVm { get; }
    public List<GetTableNameListBySchemaVm> GetTableNameListBySchemaVm { get; }
    public List<SchemaNameListVm> SchemaNameListVm { get; }

    public TableAccessFixture()
    {
        var fixture = new Fixture();

        TableAccessListVm = fixture.Create<List<TableAccessListVm>>();
        CreateTableAccessCommand = fixture.Create<CreateTableAccessCommand>();
        UpdateTableAccessCommand = fixture.Create<UpdateTableAccessCommand>();
        GetTableAccessPaginatedListQuery = fixture.Create<GetTableAccessPaginatedListQuery>();
        PaginatedTableAccessListVm = fixture.Create<PaginatedResult<TableAccessListVm>>();
        GetTableNameListBySchemaVm = fixture.Create<List<GetTableNameListBySchemaVm>>();
        SchemaNameListVm = fixture.Create<List<SchemaNameListVm>>();
    }

    public void Dispose()
    {

    }
}
