﻿using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.Features.Template.Commands.Update;
using ContinuityPatrol.Application.Features.Template.Events.Create;
using ContinuityPatrol.Application.Features.Template.Events.Delete;
using ContinuityPatrol.Application.Features.Template.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class TemplateFixture : IDisposable
{
    public IMapper Mapper { get; }
    public List<Template> Templates { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateTemplateCommand CreateTemplateCommand { get; set; }
    public UpdateTemplateCommand UpdateTemplateCommand { get; set; }
    public TemplateCreatedEvent TemplateCreatedEvent { get; set; }
    public TemplateDeletedEvent TemplateDeletedEvent { get; set; }
    public TemplateUpdatedEvent TemplateUpdatedEvent { get; set; }

    public TemplateFixture()
    {
        Templates = AutoTemplateFixture.Create<List<Template>>();
        UserActivities = AutoTemplateFixture.Create<List<UserActivity>>();
        CreateTemplateCommand = AutoTemplateFixture.Create<CreateTemplateCommand>();
        UpdateTemplateCommand = AutoTemplateFixture.Create<UpdateTemplateCommand>();
        TemplateCreatedEvent = AutoTemplateFixture.Create<TemplateCreatedEvent>();
        TemplateDeletedEvent = AutoTemplateFixture.Create<TemplateDeletedEvent>();
        TemplateUpdatedEvent = AutoTemplateFixture.Create<TemplateUpdatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<TemplateProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoTemplateFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateTemplateCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateTemplateCommand>(p => p.Name, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UserActivity>(p => p.LoginName, 10));
            fixture.Customize<UpdateTemplateCommand>(c => c.With(b => b.Id, 0.ToString()));
            fixture.Customize<Template>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TemplateCreatedEvent>(p => p.TemplateName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TemplateDeletedEvent>(p => p.TemplateName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<TemplateUpdatedEvent>(p => p.TemplateName, 10));

            return fixture;
        }
    }
    public void Dispose()
    {

    }
}
