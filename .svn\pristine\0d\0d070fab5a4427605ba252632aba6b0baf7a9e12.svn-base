﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.CyberSnaps.Events.Paginated;

public class CyberSnapPaginatedEventHandler : INotificationHandler<CyberSnapPaginatedEvent>
{
    private readonly ILogger<CyberSnapPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public CyberSnapPaginatedEventHandler(ILogger<CyberSnapPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository, ILoggedInUserService userService)
    {
        _logger = logger;
        _userActivityRepository = userActivityRepository;
        _userService = userService;
    }

    public async Task Handle(CyberSnapPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} {Modules.CyberSnaps}",
            Entity = Modules.CyberSnaps.ToString(),
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Cyber Resiliency Snap viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Cyber Resiliency Snap viewed");
    }
}