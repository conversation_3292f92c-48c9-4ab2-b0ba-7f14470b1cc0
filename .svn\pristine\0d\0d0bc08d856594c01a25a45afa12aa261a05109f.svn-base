﻿using ContinuityPatrol.Application.Features.InfraMaster.Events.Delete;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraMaster.Events
{
    public class DeleteInfraMasterEventHandlerTests
    {
        private readonly Mock<ILogger<InfraMasterDeletedEventHandler>> _loggerMock;
        private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly InfraMasterDeletedEventHandler _handler;

        public DeleteInfraMasterEventHandlerTests()
        {
            _loggerMock = new Mock<ILogger<InfraMasterDeletedEventHandler>>();
            _userActivityRepoMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();

            _userServiceMock.Setup(x => x.UserId).Returns("TestUser");
            _userServiceMock.Setup(x => x.LoginName).Returns("testuser");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("http://localhost/delete");
            _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");

            _handler = new InfraMasterDeletedEventHandler(
                _userServiceMock.Object,
                _loggerMock.Object,
                _userActivityRepoMock.Object
            );
        }

        [Fact]
        public async Task Handle_ShouldLogAndSaveActivityCorrectly()
        {
            // Arrange
            var deletedEvent = new InfraMasterDeletedEvent { Name = "Infra123" };

            Domain.Entities.UserActivity capturedActivity = null!;
            _userActivityRepoMock
                .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.NotNull(capturedActivity);
            Assert.Equal("TestUser", capturedActivity.UserId);
            Assert.Equal("Delete InfraMaster", capturedActivity.Action);
            Assert.Equal("Delete", capturedActivity.ActivityType);
            Assert.Equal("InfraMaster", capturedActivity.Entity);
            Assert.Contains("Infra123", capturedActivity.ActivityDetails);

            _loggerMock.Verify(l => l.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString().Contains("InfraMaster 'Infra123' deleted successfully")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            ), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldAllowEmptyUserId()
        {
            // Arrange
            _userServiceMock.Setup(x => x.UserId).Returns(string.Empty);
            var deletedEvent = new InfraMasterDeletedEvent { Name = "InfraXYZ" };

            Domain.Entities.UserActivity capturedActivity = null!;
            _userActivityRepoMock
                .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.NotNull(capturedActivity);
            // Empty UserId should still be assigned
            Assert.Equal(string.Empty, capturedActivity.UserId);
        }

        [Fact]
        public async Task Handle_ShouldLogSuccessfully_WhenNameIsNull()
        {
            // Arrange
            var deletedEvent = new InfraMasterDeletedEvent { Name = null };

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            _loggerMock.Verify(l => l.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, _) => v.ToString().Contains("InfraMaster '' deleted successfully")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            ), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Set_Correct_ActivityDetails_Format()
        {
            // Arrange
            var deletedEvent = new InfraMasterDeletedEvent { Name = "Infra-Detail" };

            Domain.Entities.UserActivity capturedActivity = null!;
            _userActivityRepoMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.Contains("InfraMaster 'Infra-Detail' deleted successfully", capturedActivity.ActivityDetails);
        }

        [Fact]
        public async Task Handle_Should_Map_All_UserService_Properties_To_UserActivity()
        {
            // Arrange
            var deletedEvent = new InfraMasterDeletedEvent { Name = "Infra-XYZ" };

            Domain.Entities.UserActivity capturedActivity = null!;
            _userActivityRepoMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.Equal("TestUser", capturedActivity.UserId);
            Assert.Equal("testuser", capturedActivity.LoginName);
            Assert.Equal("http://localhost/delete", capturedActivity.RequestUrl);
            Assert.Equal("127.0.0.1", capturedActivity.HostAddress);
        }

        [Fact]
        public async Task Handle_Should_Call_AddAsync_Once()
        {
            // Arrange
            var deletedEvent = new InfraMasterDeletedEvent { Name = "Infra-Once" };

            _userActivityRepoMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        public async Task Handle_Should_Work_When_IpAddress_Is_Null_Or_Empty(string? ipAddress)
        {
            // Arrange
            _userServiceMock.Setup(x => x.IpAddress).Returns(ipAddress);

            var deletedEvent = new InfraMasterDeletedEvent { Name = "Infra-IP" };

            Domain.Entities.UserActivity capturedActivity = null!;
            _userActivityRepoMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
                .Callback<Domain.Entities.UserActivity>(ua => capturedActivity = ua)
                .ReturnsAsync(new Domain.Entities.UserActivity());

            // Act
            await _handler.Handle(deletedEvent, CancellationToken.None);

            // Assert
            Assert.Equal(ipAddress, capturedActivity.HostAddress);
        }

    }
}
