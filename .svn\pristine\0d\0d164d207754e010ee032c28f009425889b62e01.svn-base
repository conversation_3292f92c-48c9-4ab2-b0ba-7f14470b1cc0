using AutoFixture;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Moq;
using LicenseInfo = ContinuityPatrol.Domain.Entities.LicenseInfo;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class LicenseInfoRepositoryTests : IClassFixture<LicenseInfoFixture>, IDisposable
{
    private readonly LicenseInfoRepository _repository;
    private readonly ApplicationDbContext _context;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly LicenseInfoFixture _fixture;
    private readonly Fixture _autoFixture;

    public LicenseInfoRepositoryTests(LicenseInfoFixture fixture)
    {
        _fixture = fixture;
        _autoFixture = new Fixture();
        _context = DbContextFactory.CreateInMemoryDbContext();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        
        // Setup default logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns("TEST_USER");
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(LicenseInfoFixture.CompanyId);
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        _repository = new LicenseInfoRepository(_context, _mockLoggedInUserService.Object);
    }

    #region Basic CRUD Operations

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;

        // Act
        await _repository.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Assert
        var result = await _context.LicenseInfo.FindAsync(entity.Id);
        Assert.NotNull(result);
        Assert.Equal(entity.LicenseId, result.LicenseId);
        Assert.Equal(entity.PONumber, result.PONumber);
        Assert.Equal(entity.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        entity.PONumber = "UPDATED_PO";
        entity.BusinessServiceName = "UPDATED_SERVICE";
        await _repository.UpdateAsync(entity);
        await _context.SaveChangesAsync();

        // Assert
        var result = await _context.LicenseInfo.FindAsync(entity.Id);
        Assert.NotNull(result);
        Assert.Equal("UPDATED_PO", result.PONumber);
        Assert.Equal("UPDATED_SERVICE", result.BusinessServiceName);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    //[Fact]
    //public async Task DeleteAsync_ShouldDeleteEntity()
    //{
    //    // Arrange
    //    var entity = _fixture.LicenseInfoDto;
    //    await _context.LicenseInfo.AddAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Act
    //    await _repository.DeleteAsync(entity);
    //    await _context.SaveChangesAsync();

    //    // Assert
    //    var result = await _context.LicenseInfo.FindAsync(entity.Id);
    //    Assert.NotNull(result);
    //    Assert.False(result.IsActive);
    //}

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(entity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(entity.Id, result.Id);
        Assert.Equal(entity.LicenseId, result.LicenseId);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Reference ID Operations


    #endregion

    #region LicenseInfo-Specific Methods

    [Fact]
    public async Task GetAvailableCountByLicenseId_ShouldReturnCorrectCount()
    {
        // Arrange


        var entities = _fixture.LicenseInfoList.Take(5).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.IsActive = true;
        });

        // Add one with different entity
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Server";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableCountByLicenseId(LicenseInfoFixture.LicenseId, "Database");

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task GetAvailableCountByLicenseId_ShouldReturnZero_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetAvailableCountByLicenseId("NON_EXISTENT", "Database");


        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task GetAvailableLicenseByLicenseIdAndEntityType_ShouldReturnCorrectCount()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.EntityType = "Primary";
            x.IsActive = true;
        });

        // Add one with different entity type
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Database";
        differentEntity.EntityType = "Secondary";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableLicenseByLicenseIdAndEntityType(LicenseInfoFixture.LicenseId, "Database", "Primary");

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task GetByEntityId_ShouldReturnEntity_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entity = _fixture.LicenseInfoDto;
        entity.EntityId = LicenseInfoFixture.EntityId;
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEntityId(LicenseInfoFixture.EntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseInfoFixture.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetByEntityId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entity = _fixture.LicenseInfoDto;
        entity.EntityId = LicenseInfoFixture.EntityId;
        entity.CompanyId = LicenseInfoFixture.CompanyId;
        entity.IsActive = true;

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.EntityId = LicenseInfoFixture.EntityId;
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entity, otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByEntityId(LicenseInfoFixture.EntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseInfoFixture.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x => x.IsActive = true);

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.CompanyId = LicenseInfoFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceId_ShouldReturnMatchingEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.IsActive = true;
        });

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseByBusinessServiceId(LicenseInfoFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.BusinessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseInfoList.Take(2).ToList();
        entities.ForEach(x =>
        {
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.CompanyId = LicenseInfoFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseByBusinessServiceId(LicenseInfoFixture.BusinessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseInfoByEntityId_ShouldReturnEntity_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entity = _fixture.LicenseInfoDto;
        entity.EntityId = LicenseInfoFixture.EntityId;
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByEntityId(LicenseInfoFixture.EntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseInfoFixture.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetLicenseInfoByEntityId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entity = _fixture.LicenseInfoDto;
        entity.EntityId = LicenseInfoFixture.EntityId;
        entity.CompanyId = LicenseInfoFixture.CompanyId;
        entity.IsActive = true;

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.EntityId = LicenseInfoFixture.EntityId;
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entity, otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByEntityId(LicenseInfoFixture.EntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseInfoFixture.CompanyId, result.CompanyId);
    }

    [Fact]
    public async Task GetLicenseInfoByEntityId_ShouldReturnNull_WhenNotFound()
    {
        // Act
        var result = await _repository.GetLicenseInfoByEntityId("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region Edge Cases and Error Handling

    [Fact]
    public async Task GetAvailableCountByLicenseId_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Entity = "DATABASE";
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableCountByLicenseId(LicenseInfoFixture.LicenseId, "database");

        // Assert
        Assert.Equal(1, result);
    }

    [Fact]
    public async Task GetAvailableLicenseByLicenseIdAndEntityType_ShouldReturnZero_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetAvailableLicenseByLicenseIdAndEntityType("NON_EXISTENT", "Database", "Primary");

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task GetByEntityId_ShouldReturnNull_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetByEntityId("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseByBusinessServiceId("NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region Missing LicenseInfo-Specific Methods

    [Fact]
    public async Task GroupByUsageCountByLicenseId_ShouldReturnGroupedData()
    {
        // Arrange
        var entities = new List<LicenseInfo>
        {
            new() { LicenseId = "LIC1", PONumber = "PO1", Entity = "Database", EntityType = "Primary", IsActive = true },
            new() { LicenseId = "LIC1", PONumber = "PO1", Entity = "Database", EntityType = "Primary", IsActive = true },
            new() { LicenseId = "LIC1", PONumber = "PO1", Entity = "Server", EntityType = "Secondary", IsActive = true },
            new() { LicenseId = "LIC2", PONumber = "PO2", Entity = "Database", EntityType = "Primary", IsActive = true }
        };

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GroupByUsageCountByLicenseId(new List<string> { "LIC1", "LIC2" });

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // 3 unique groups

        var group1 = result.FirstOrDefault(x => x.LicenseId == "LIC1" && x.Entity == "Database");
        Assert.NotNull(group1);
        Assert.Equal(2, group1.Count);

        var group2 = result.FirstOrDefault(x => x.LicenseId == "LIC1" && x.Entity == "Server");
        Assert.NotNull(group2);
        Assert.Equal(1, group2.Count);
    }

    [Fact]
    public async Task GroupByUsageCountByLicenseId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GroupByUsageCountByLicenseId(new List<string> { "NON_EXISTENT" });

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntity_ShouldReturnMatchingEntities()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.IsActive = true;
        });

        // Add entity with different entity type
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Server";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntity(LicenseInfoFixture.LicenseId, "Database");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("Database", x.Entity));
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntity_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Entity = "DATABASE";
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntity(LicenseInfoFixture.LicenseId, "database");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("DATABASE", result.First().Entity);
    }

    [Fact]
    public async Task GetPaginatedLicenseInfoEntityQueryable_WithNullSearchString_ReturnsAllData()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var listValue = _fixture.LicenseInfoPaginationList;
        foreach (var val in listValue)
            val.IsActive = true;
        await _context.LicenseInfo.AddRangeAsync(listValue);
        await _context.SaveChangesAsync();

        string? searchString = null;
        var spec = new LicenseInfoFilterSpecification(searchString);

        // Act
        var result = await _repository.GetLicenseInfoEntityQueryable(
            entity: "Database",
            pageSize: 1,
            pageNumber: 20,
            productFilterSpec: spec,
            sortColumn: "Id",
            sortOrder: "asc"
        );
        Assert.NotNull(result);

    }
    [Fact]
    public async Task GetLicenseInfoEntityQueryable_ShouldReturnQueryable_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.Entity = "Database";
            x.IsActive = true;
        });

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.SaveChangesAsync();

        // Act
        var queryable = _repository.GetLicenseInfoEntityQueryable("Database");
        var result = await queryable.ToListAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("Database", x.Entity));

        // Verify ordering (should be descending by Id)
        var orderedIds = result.Select(x => x.Id).ToList();
        var expectedOrder = entities.OrderByDescending(x => x.Id).Select(x => x.Id).ToList();
        Assert.Equal(expectedOrder, orderedIds);
    }

    [Fact]
    public async Task GetLicenseInfoEntityQueryable_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseInfoList.Take(2).ToList();
        entities.ForEach(x =>
        {
            x.Entity = "Database";
            x.CompanyId = LicenseInfoFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.Entity = "Database";
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var queryable = _repository.GetLicenseInfoEntityQueryable("Database");
        var result = await queryable.ToListAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceIdAndLicenseId_ShouldReturnMatchingEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.IsActive = true;
        });

        // Add entity with different business service
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.BusinessServiceId = "OTHER_SERVICE";
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseByBusinessServiceIdAndLicenseId(LicenseInfoFixture.BusinessServiceId, LicenseInfoFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.BusinessServiceId, x.BusinessServiceId));
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.LicenseId, x.LicenseId));
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceIdAndLicenseId_ShouldFilterByCompanyId_WhenIsParentFalse()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);

        var entities = _fixture.LicenseInfoList.Take(2).ToList();
        entities.ForEach(x =>
        {
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.CompanyId = LicenseInfoFixture.CompanyId;
            x.IsActive = true;
        });

        // Add entity from different company
        var otherCompanyEntity = _autoFixture.Create<LicenseInfo>();
        otherCompanyEntity.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
        otherCompanyEntity.LicenseId = LicenseInfoFixture.LicenseId;
        otherCompanyEntity.CompanyId = "OTHER_COMPANY";
        otherCompanyEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(otherCompanyEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseByBusinessServiceIdAndLicenseId(LicenseInfoFixture.BusinessServiceId, LicenseInfoFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.CompanyId, x.CompanyId));
    }

    [Fact]
    public async Task GetLicenseInfoDetailByLicenseId_ShouldReturnMatchingEntities_WhenIsParentTrue()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.IsActive = true;
        });

        // Add entity with different license ID
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = "OTHER_LICENSE";
        differentEntity.IsActive = true;
        differentEntity.Id=12548;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoDetailByLicenseId(LicenseInfoFixture.LicenseId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal(LicenseInfoFixture.LicenseId, x.LicenseId));
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndType_ShouldReturnMatchingEntities()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Type = "Primary";
            x.IsActive = true;
        });

        // Add entity with different type
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Type = "Secondary";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndType(LicenseInfoFixture.LicenseId, "Primary");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("Primary", x.Type));
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndType_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Type = "PRIMARY";
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndType(LicenseInfoFixture.LicenseId, "primary");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("PRIMARY", result.First().Type);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntityId_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.EntityId = LicenseInfoFixture.EntityId;
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntityId(LicenseInfoFixture.LicenseId, LicenseInfoFixture.EntityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(LicenseInfoFixture.LicenseId, result.LicenseId);
        Assert.Equal(LicenseInfoFixture.EntityId, result.EntityId);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntityId_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntityId("NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetAvailableLicenseByLicenseIdAndEntityTypeAndBusinessServiceId_ShouldReturnCorrectCount()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.EntityType = "Primary";
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.IsActive = true;
        });

        // Add entity with different business service
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Database";
        differentEntity.EntityType = "Primary";
        differentEntity.BusinessServiceId = "OTHER_SERVICE";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableLicenseByLicenseIdAndEntityTypeAndBusinessServiceId(
            LicenseInfoFixture.LicenseId, "Database", "Primary", LicenseInfoFixture.BusinessServiceId);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndTypeAndEntityType_ShouldReturnMatchingEntities()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Type = "Primary";
            x.EntityType = "Database";
            x.IsActive = true;
        });

        // Add entity with different type
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Type = "Secondary";
        differentEntity.EntityType = "Database";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndTypeAndEntityType(LicenseInfoFixture.LicenseId, "Primary", "Database");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("Primary", x.Type));
        Assert.All(result, x => Assert.Equal("Database", x.EntityType));
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntityAndEntityType_ShouldReturnMatchingEntities()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.EntityType = "Primary";
            x.IsActive = true;
        });

        // Add entity with different entity type
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Database";
        differentEntity.EntityType = "Secondary";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntityAndEntityType(LicenseInfoFixture.LicenseId, "Database", "Primary");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, x => Assert.Equal("Database", x.Entity));
        Assert.All(result, x => Assert.Equal("Primary", x.EntityType));
    }

    [Fact]
    public async Task GetAvailableCountByLicenseIdAndBusinessServiceId_ShouldReturnCorrectCount()
    {
        // Arrange
        var entities = _fixture.LicenseInfoList.Take(3).ToList();
        entities.ForEach(x =>
        {
            x.LicenseId = LicenseInfoFixture.LicenseId;
            x.Entity = "Database";
            x.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
            x.IsActive = true;
        });

        // Add entity with different business service
        var differentEntity = _autoFixture.Create<LicenseInfo>();
        differentEntity.LicenseId = LicenseInfoFixture.LicenseId;
        differentEntity.Entity = "Database";
        differentEntity.BusinessServiceId = "OTHER_SERVICE";
        differentEntity.IsActive = true;

        await _context.LicenseInfo.AddRangeAsync(entities);
        await _context.LicenseInfo.AddAsync(differentEntity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableCountByLicenseIdAndBusinessServiceId(
            LicenseInfoFixture.LicenseId, "Database", LicenseInfoFixture.BusinessServiceId);

        // Assert
        Assert.Equal(3, result);
    }

    [Fact]
    public async Task GetAvailableCountByLicenseIdAndBusinessServiceId_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Entity = "DATABASE";
        entity.BusinessServiceId = LicenseInfoFixture.BusinessServiceId;
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetAvailableCountByLicenseIdAndBusinessServiceId(
            LicenseInfoFixture.LicenseId, "database", LicenseInfoFixture.BusinessServiceId);

        // Assert
        Assert.Equal(1, result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndTypeAndEntityType_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Type = "PRIMARY";
        entity.EntityType = "DATABASE";
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndTypeAndEntityType(LicenseInfoFixture.LicenseId, "primary", "database");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("PRIMARY", result.First().Type);
        Assert.Equal("DATABASE", result.First().EntityType);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntityAndEntityType_ShouldBeCaseInsensitive()
    {
        // Arrange
        var entity = _fixture.LicenseInfoDto;
        entity.LicenseId = LicenseInfoFixture.LicenseId;
        entity.Entity = "DATABASE";
        entity.EntityType = "PRIMARY";
        entity.IsActive = true;

        await _context.LicenseInfo.AddAsync(entity);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntityAndEntityType(LicenseInfoFixture.LicenseId, "database", "primary");

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("DATABASE", result.First().Entity);
        Assert.Equal("PRIMARY", result.First().EntityType);
    }

    [Fact]
    public async Task GetLicenseInfoDetailByLicenseId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseInfoDetailByLicenseId("NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseByBusinessServiceIdAndLicenseId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseByBusinessServiceIdAndLicenseId("NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntity_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntity("NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndType_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndType("NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndTypeAndEntityType_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndTypeAndEntityType("NON_EXISTENT", "NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetLicenseInfoByLicenseIdAndEntityAndEntityType_ShouldReturnEmpty_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetLicenseInfoByLicenseIdAndEntityAndEntityType("NON_EXISTENT", "NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetAvailableCountByLicenseIdAndBusinessServiceId_ShouldReturnZero_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetAvailableCountByLicenseIdAndBusinessServiceId("NON_EXISTENT", "NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.Equal(0, result);
    }

    [Fact]
    public async Task GetAvailableLicenseByLicenseIdAndEntityTypeAndBusinessServiceId_ShouldReturnZero_WhenNoMatches()
    {
        // Act
        var result = await _repository.GetAvailableLicenseByLicenseIdAndEntityTypeAndBusinessServiceId("NON_EXISTENT", "NON_EXISTENT", "NON_EXISTENT", "NON_EXISTENT");

        // Assert
        Assert.Equal(0, result);
    }

    #endregion

    #region InActiveLicense Info by EntityId
    [Fact]
    public async Task InActiveLicenseInfoByEntityId_ShouldReturnLicense_WhenUserIsParent()
    {
        // Arrange
        var license = new LicenseInfo
        {
            EntityId = "ENTITY_123",
            CompanyId = "COMPANY_001",
            IsActive = false
        };

        await _context.LicenseInfo.AddAsync(license);
         _context.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.InActiveLicenseInfoByEntityId("ENTITY_123");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("ENTITY_123", result.EntityId);
        Assert.False(result.IsActive);
    }
    [Fact]
    public async Task InActiveLicenseInfoByEntityId_ShouldReturnLicense_WhenUserIsNotParent()
    {
        // Arrange
        var companyId = "ChHILD_COMPANY_123";
        var license = new LicenseInfo
        {
            EntityId = "ENTITY_456",
            CompanyId = companyId,
            IsActive = false
        };

        await _context.LicenseInfo.AddAsync(license);
         _context.SaveChanges();

        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(false);
        _mockLoggedInUserService.Setup(x => x.CompanyId).Returns(companyId);

        var repository= new LicenseInfoRepository(_context, _mockLoggedInUserService.Object);
        // Act
        var result = await repository.InActiveLicenseInfoByEntityId("ENTITY_456");

        // Assert
        Assert.NotNull(result);
        Assert.Equal("ENTITY_456", result.EntityId);
        Assert.False(result.IsActive);
    }
    [Fact]
    public async Task InActiveLicenseInfoByEntityId_ShouldReturnNull_WhenNoMatch()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.IsParent).Returns(true);

        // Act
        var result = await _repository.InActiveLicenseInfoByEntityId("NON_EXISTENT");

        // Assert
        Assert.Null(result);
    }
    


    #endregion

    public void Dispose()
    {
        _context?.Dispose();
    }
}
