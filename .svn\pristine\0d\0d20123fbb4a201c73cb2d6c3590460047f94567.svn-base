namespace ContinuityPatrol.Application.Features.ApprovalMatrixApproval.Commands.Create;

public class CreateApprovalMatrixApprovalCommand : IRequest<CreateApprovalMatrixApprovalResponse>
{
    public string ProcessName { get; set; }
    public string Description { get; set; }
    public string UserName { get; set; }
    public string Status { get; set; }
    public string Approver { get; set; }
    public DateTime StartDateTime { get; set; }
    public DateTime EndDateTime { get; set; }
}