﻿namespace ContinuityPatrol.Application.Features.Replication.Events.InfraSummaryEvents.Delete;

public class ReplicationInfraSummaryDeletedEventHandler : INotificationHandler<ReplicationInfraSummaryDeletedEvent>
{
    private readonly IInfraSummaryRepository _infraSummaryRepository;
    private readonly ILogger<ReplicationInfraSummaryDeletedEventHandler> _logger;

    public ReplicationInfraSummaryDeletedEventHandler(ILogger<ReplicationInfraSummaryDeletedEventHandler> logger,
        IInfraSummaryRepository infraSummaryRepository)
    {
        _logger = logger;
        _infraSummaryRepository = infraSummaryRepository;
    }

    public async Task Handle(ReplicationInfraSummaryDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var infraSummary =
            await _infraSummaryRepository.GetInfraSummaryByTypeAndBusinessServiceIdAndCompanyId(deletedEvent.Type,
                deletedEvent.BusinessServiceId, deletedEvent.CompanyId);

        if (infraSummary is not null)
        {
            if (infraSummary.Count == 1)
            {
                await _infraSummaryRepository.DeleteAsync(infraSummary);
            }
            else
            {
                infraSummary.Count -= 1;
                await _infraSummaryRepository.UpdateAsync(infraSummary);
            }

            _logger.LogInformation($"InfraSummary '{deletedEvent.Type}' deleted successfully.");
        }
    }
}