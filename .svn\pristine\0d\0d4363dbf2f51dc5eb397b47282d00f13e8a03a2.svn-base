﻿using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraPrinting.Shape;
using Newtonsoft.Json;
using System.Drawing;
using System.Runtime.Versioning;
namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]
    public partial class DataLagStatusReport : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public  Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport.DataLagStatusReport dataLagStatusReport =new Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport.DataLagStatusReport();
        public XRShape SetShape(string color)
        {

            XRShape newShape = new XRShape(); 
            newShape.Shape = new ShapeRectangle();
            newShape.BorderColor = System.Drawing.Color.Transparent;
            newShape.BorderWidth = 0F;
            newShape.FillColor = ColorTranslator.FromHtml(color);
            newShape.LineWidth = 0;
            newShape.LocationFloat = new DevExpress.Utils.PointFloat(1F, 4F);
            newShape.SizeF = new System.Drawing.SizeF(16.44186F, 13.12636F);
            return newShape;

        }
        public DataLagStatusReport(string data)
        {
            try
            {
                dataLagStatusReport = JsonConvert.DeserializeObject<Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport.DataLagStatusReport>(data);

                _logger = PreBuildReportController._logger;
                InitializeComponent();
                ClientCompanyLogo();
                this.DisplayName = "DataLagStatusReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");

                this.xrLblDate.Text = DateTime.Now.AddDays(-1).ToString("dd-MM-yyyy");
                //for (int i = 0; i < 8; i++)
                //{
                //    var newDataLagStatusReportVm = new DataLagStatusReportVm
                //    {
                //        BusinessServiceId = $"NewBusinessServiceId{i}",
                //        BusinessServiceName = $"NewBusinessServiceName{i}",
                //        ReportDate = $"NewReportDate{i}",
                //        UserName = $"NewUserName{i}",
                //        InfraObjectDataLagStatusReports = new List<InfraObjectDataLagStatusReport>()
                //    };

                //    if (i == 2 || i == 4)
                //    {
                //        for (int j = 0; j < 40; j++)
                //        {
                //            var newInfraObjectDataLagStatusReport = new InfraObjectDataLagStatusReport
                //            {
                //                InfraObjectId = $"NewInfraObjectId{j}",
                //                InfraObjectName = $"NewInfraObjectName{j}",
                //                BusinessServiceId = $"NewBusinessServiceId{j}",
                //                InfraObjectHoursDataLagDetails = new List<InfraObjectHoursDataLagDetails>()
                //            };

                //            var newInfraObjectHoursDataLagDetails = new InfraObjectHoursDataLagDetails
                //            {
                //                InfraObjectId = $"NewInfraObjectId{j}",
                //                Hour = j,
                //                IsDataLagValue = j == 1 ? true : false,
                //                IsDataLagValueExist = j == 2 ? true : false,
                //                IsThreshold = j == 3 ? true : false,
                //                IsNotAvailable = j == 4 ? true : false
                //            };

                //            newInfraObjectDataLagStatusReport.InfraObjectHoursDataLagDetails.Add(newInfraObjectHoursDataLagDetails);
                //            newDataLagStatusReportVm.InfraObjectDataLagStatusReports.Add(newInfraObjectDataLagStatusReport);
                //        }
                //    }
                //    infra.Add(newDataLagStatusReportVm);

                //}
                //foreach(var item in infra)
                //{
                //    if(item.InfraObjectDataLagStatusReports.Count!=0)
                //    {
                //        datalagReport1.Add(item);
                //    }
                //}


                HashSet<string> businessServiceNames = new HashSet<string>();
                HashSet<string> infraName = new HashSet<string>();

                xrTable2.BeforePrint += (sender, e) =>
                {
                    var table = (XRTable)sender;
                    table.Rows.Clear();
                    var data = GetCurrentRow() as DataLagStatusReportVm;

                    if (data != null)
                    {
                        if (businessServiceNames.Contains(data.BusinessServiceName)) { return; }
                        businessServiceNames.Add(data.BusinessServiceName);
                        int rowIndex = 0;

                        infraName.Clear();

                        foreach (var infraObject in data.InfraObjectDataLagStatusReports)
                        {
                            if ((infraName.Contains(infraObject.InfraObjectName)) || infraObject.InfraObjectName == "") { continue; }
                            infraName.Add(infraObject.InfraObjectName);
                            XRTableRow dataRow = new XRTableRow();
                            bool isOddRow = rowIndex % 2 != 0;
                            XRTableRow currentRowStyle = new XRTableRow();
                            currentRowStyle.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            currentRowStyle.BorderColor = System.Drawing.Color.Transparent;
                            currentRowStyle.Borders = DevExpress.XtraPrinting.BorderSide.None;
                            currentRowStyle.BorderWidth = 0F;
                            currentRowStyle.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
                            currentRowStyle.ForeColor = System.Drawing.Color.Black;
                            currentRowStyle.Name = isOddRow ? "datalag_odd" : "datalag_even";
                            currentRowStyle.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
                            dataRow = currentRowStyle;

                            dataRow.Cells.Add(new XRTableCell { Text = "  ", WidthF = 5F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            dataRow.Cells.Add(new XRTableCell { Text = infraObject.InfraObjectName, WidthF = 460.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            //dataRow.Cells.Add(new XRTableCell { Text = "AgreedRPO", WidthF = 445.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0,13), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 6.8F) });

                            for (int i = 0; i < 24; i++)
                            {
                                bool hourExists = false;

                                foreach (var hourDetails in infraObject.InfraObjectHoursDataLagDetails)
                                {
                                    if (i == hourDetails.Hour)
                                    {
                                        XRShape newShape = null;

                                        newShape = hourDetails.IsDataLagValue ? SetShape("#3AAF00") :
                                        hourDetails.IsDataLagValueExist ? SetShape("#FE2223") :
                                        hourDetails.IsThreshold ? SetShape("#FF9C08") :
                                        hourDetails.IsNotAvailable ? SetShape("#B7B7B7") :
                                        null;

                                        if (newShape != null)
                                        {
                                            XRTableCell newCell = new XRTableCell { TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Controls = { newShape } };
                                            newCell.StyleName = isOddRow ? "datalag_odd" : "datalag_even";
                                            dataRow.Cells.Add(newCell);
                                            hourExists = true;
                                        }
                                    }
                                }

                                if (!hourExists)
                                {
                                    XRShape newShape = SetShape("#B7B7B7");
                                    XRTableCell newCell = new XRTableCell { Controls = { newShape } };
                                    newCell.StyleName = isOddRow ? "datalag_odd" : "datalag_even";
                                    dataRow.Cells.Add(newCell);
                                }
                            }
                            table.Rows.Add(dataRow);
                            rowIndex++;
                        }

                    }
                };

                DataSource = dataLagStatusReport.DataLagStatusReportVms;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the DataLag_Report. The error message : " + ex.Message); throw; }
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + dataLagStatusReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the DataLag_Report's User Name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the DataLag_Report's CP Version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in DataLag_Report" + ex.Message.ToString());
            }
        }

    }
}
