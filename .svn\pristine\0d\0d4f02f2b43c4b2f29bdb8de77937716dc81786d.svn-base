﻿namespace ContinuityPatrol.Domain.Entities;

public class EscalationMatrixLevel : AuditableEntity
{
    public string EscLevName { get; set; }

    public string EscLevDescription { get; set; }

    public string EscalationTime { get; set; }
    public string EscalationTimeUnit { get; set; }
    public string EscMatrixId { get; set; }
    public string EscMatLevelResourceId { get; set; }
    public string EscMatLevelTeamId { get; set; }
}