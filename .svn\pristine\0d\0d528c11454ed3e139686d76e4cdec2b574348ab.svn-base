﻿@model ContinuityPatrol.Domain.ViewModels.TemplateModel.TemplateViewModel
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}


<link href="~/css/workflowconfiguration.css" rel="stylesheet" />

@Html.AntiForgeryToken()

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header pb-0">
            <h6 class="page_title"><i class="cp-template-store"></i><span>Workflow Templates</span></h6>
            <div class="d-flex align-items-center">
                <div class="col-auto card rounded-0 shadow-none mb-0 me-2 flex-fill">
                    <div class="list-group">
                        <button type="button" title="Upload" class="btn btn-sm btn-primary rounded-1" id="btnImportWorkFlow"><i class="cp-upload"></i></button>
                    </div>
                </div>
                <div class="input-group me-2" style="width: 350px !important;">
                    <span class="input-group-text"><i class="cp-template-store me-1"></i></span>
                    <select class="form-select" id="replicationTypeFilter" placeholder="Select replication Type" aria-label="Default select example">
                        <option value="">All</option>
                    </select>
                </div>
                <div class="input-group w-auto">
                    <input type="search" id="templateSearch" class="form-control" placeholder="Search Template " autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown" aria-expanded="false" class="" title="Filter"><i class="cp-filter"></i></span>
                            <ul class="dropdown-menu filter-dropdown" style="">
                                <li>
                                    <h6 class="dropdown-header">Filter Search</h6>
                                </li>
                                <li class="dropdown-item">
                                    <div><input class="form-check-input" type="checkbox" value="name=" id="CompanyName"> <label class="form-check-label" for="CompanyName"> Name </label></div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body pt-0 p-2" id="templateContainer" style="height: calc(100vh - 115px);overflow-y: auto;">
            <div id="WFTemplateLoader" class="spinner-border text-primary position-absolute" style="width: 3rem; height: 3rem; top:48%;left:48%;display:none" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div id="templateList" class="h-100"></div>
        </div>
    </div>
</div>

@* templateConfirmation *@
<div class="modal" role="dialog" id="templateConfModal">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
            </div>
            <div class="modal-body">
                <p id="confirmationTempText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="confimTemplateDiscard">No</button>
                <button type="button" class="btn btn-primary" id="templateConfirmationSave">Yes</button>
            </div>
        </div>
    </div>
</div>
<div id="orchestrationCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.CreateAndEdit" aria-hidden="true"></div>
<div id="orchestrationDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Orchestration.Delete" aria-hidden="true"></div>


<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>

<!-- Delete -->
<div class="modal fade" id="DeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="Delete" />
</div>

<!--Import Workflow-->
<div class="modal fade" tabindex="-1" id="ImportTemplateModal" aria-labelledby="configureModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <partial name="ImportTemplate" />
</div>

<script src="~/js/itautomation/workflowtemplate/templatestore.js"></script>

