using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUp.Commands.Update;
using ContinuityPatrol.Application.Features.BackUp.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Commands;

/// <summary>
/// Unit tests for UpdateBackUpCommand and UpdateBackUpCommandHandler
/// Tests command handling, repository interactions, and business logic validation
/// Purpose: BackUp manages database backup configurations and scheduling
/// </summary>
public class UpdateBackUpTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<IBackUpRepository> _mockBackUpRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly Mock<ILoadBalancerRepository> _mockNodeConfigurationRepository;
    private readonly Mock<IWindowsService> _mockWindowsService;
    private readonly Mock<IBackUpLogRepository> _mockBackUpLogRepository;
    private readonly Mock<IJobScheduler> _mockClient;
    private readonly UpdateBackUpCommandHandler _handler;
   
    public UpdateBackUpTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockMapper = new Mock<IMapper>();
        _mockBackUpRepository = BackUpRepositoryMocks.CreateBackUpRepository(_backUpFixture.BackUps);
        _mockPublisher = new Mock<IPublisher>();
        _mockNodeConfigurationRepository = BackUpRepositoryMocks.CreateLoadBalancerRepository();
        _mockWindowsService = BackUpRepositoryMocks.CreateWindowsService();
        _mockBackUpLogRepository = BackUpRepositoryMocks.CreateBackUpLogRepository(_backUpFixture.BackUpLogs);
        _mockClient = new Mock<IJobScheduler>();

        _handler = new UpdateBackUpCommandHandler(
            _mockMapper.Object,
            _mockBackUpRepository.Object,
            _mockPublisher.Object,
            _mockNodeConfigurationRepository.Object,
            _mockWindowsService.Object,
            _mockBackUpLogRepository.Object,
            _mockClient.Object);
    }
    
    [Fact]
    public async Task Handle_UpdateBackUp_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "UpdatedServer01",
            DatabaseName = "UpdatedDatabase",
            UserName = "UpdatedUser",
            Password = "UpdatedPassword123",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\UpdatedServer\Backups\UpdatedDatabase.bak",
            BackUpType = "Differential",
            CronExpression = "0 0 4 * * ?",
            ScheduleType = "Daily",
            ScheduleTime = "04:00",
            Properties = "{\"compression\":\"true\",\"encryption\":\"true\",\"retention\":\"60\"}",
            KeepBackUpLast = "60",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "UpdatedNode01"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateBackUpResponse>();
        result.Id.ShouldBe(existingBackUp.ReferenceId);
        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateEntityWithCorrectProperties_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "PropertyTestServer",
            DatabaseName = "PropertyTestDatabase",
            UserName = "PropertyTestUser",
            Password = "PropertyTestPassword",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\PropertyTest\PropertyTestDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 1 * * ?",
            ScheduleType = "Weekly",
            ScheduleTime = "01:00",
            Properties = "{\"test\":\"property\"}",
            KeepBackUpLast = "90",
            NodeId = "property-test-node-id",
            NodeName = "PropertyTestNode"
        };

        Domain.Entities.BackUp updatedEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        updatedEntity.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: NotFoundException when backup does not exist
    /// Expected: NotFoundException thrown for non-existent backup ID
    /// </summary>
    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpNotFound()
    {
        // Arrange
        var command = new UpdateBackUpCommand
        {
            Id = "non-existent-id",
            HostName = "NonExistentServer",
            DatabaseName = "NonExistentDatabase"
        };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));
    }

    /// <summary>
    /// Test: Command handler supports cancellation
    /// Expected: OperationCanceledException when cancellation is requested
    /// </summary>
    [Fact]
    public async Task Handle_SupportCancellation_When_CancellationRequested()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "CancellationTestServer",
            DatabaseName = "CancellationTestDatabase"
        };

        using var cts = new CancellationTokenSource();
        cts.Cancel();

    }

    /// <summary>
    /// Test: BackUpLog is created when backup is updated
    /// Expected: BackUpLogRepository AddAsync called once with correct properties
    /// </summary>
    [Fact]
    public async Task Handle_CreateBackUpLog_When_BackUpUpdated()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "LogTestServer",
            DatabaseName = "LogTestDatabase",
            UserName = "LogTestUser",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\LogTest\LogTestDatabase.bak",
            BackUpType = "Incremental",
            Properties = "{\"logTest\":\"true\"}"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
       
    }

    /// <summary>
    /// Test: Event is published when backup is updated
    /// Expected: BackUpUpdatedEvent is published with correct database name
    /// </summary>
    [Fact]
    public async Task Handle_PublishBackUpUpdatedEvent_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "EventTestServer",
            DatabaseName = "EventTestDatabase"
        };

        BackUpUpdatedEvent publishedEvent = null;
        _mockPublisher.Setup(x => x.Publish(It.IsAny<BackUpUpdatedEvent>(), It.IsAny<CancellationToken>()))
            .Callback<BackUpUpdatedEvent, CancellationToken>((evt, ct) => publishedEvent = evt);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        publishedEvent.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: Background job is scheduled when backup is updated
    /// Expected: BackgroundTaskQueue ScheduleJob called once with correct parameters
    /// </summary>
    [Fact]
    public async Task Handle_ScheduleBackgroundJob_When_BackUpUpdated()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "JobTestServer",
            DatabaseName = "JobTestDatabase"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        _mockClient.Verify(x => x.ScheduleJob(
            It.IsAny<string>(),
            It.Is<Dictionary<string, string>>(dict => dict.ContainsKey("url"))), Times.Once);
    }

    [Fact]
    public async Task Handle_CheckWindowsService_When_BackUpUpdated()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "ServiceTestServer",
            DatabaseName = "ServiceTestDatabase"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockWindowsService.Verify(x => x.CheckWindowsService(It.IsAny<string>()), Times.Once);
    }

    /// <summary>
    /// Test: Update command with different backup types
    /// Expected: All backup types are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateDifferentBackUpTypes_When_ValidCommands()
    {
        // Arrange
        var backupTypes = new[] { "Full", "Differential", "Incremental", "Transaction Log" };
        var existingBackUp = _backUpFixture.BackUps.First();

        foreach (var backupType in backupTypes)
        {
            var command = new UpdateBackUpCommand
            {
                Id = existingBackUp.ReferenceId,
                HostName = $"TypeTestServer_{backupType}",
                DatabaseName = $"TypeTestDatabase_{backupType}",
                BackUpType = backupType
            };

            Domain.Entities.BackUp updatedEntity = null;
            _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
                .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
                .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            updatedEntity.ShouldNotBeNull();
        }
    }

    [Fact]
    public async Task Handle_UpdateDifferentScheduleTypes_When_ValidCommands()
    {
        // Arrange
        var scheduleTypes = new[] { "Daily", "Weekly", "Monthly", "Custom" };
        var existingBackUp = _backUpFixture.BackUps.First();

        foreach (var scheduleType in scheduleTypes)
        {
            var command = new UpdateBackUpCommand
            {
                Id = existingBackUp.ReferenceId,
                HostName = $"ScheduleTestServer_{scheduleType}",
                DatabaseName = $"ScheduleTestDatabase_{scheduleType}",
                ScheduleType = scheduleType
            };

            Domain.Entities.BackUp updatedEntity = null;
            _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
                .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
                .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            updatedEntity.ShouldNotBeNull();
        }
    }

    /// <summary>
    /// Test: Update command with local and remote server configurations
    /// Expected: Both local and remote server configurations are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateLocalAndRemoteServerConfigurations_When_ValidCommands()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();

        // Test local server configuration
        var localCommand = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "LocalTestServer",
            DatabaseName = "LocalTestDatabase",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\LocalBackups\LocalTestDatabase.bak"
        };

        Domain.Entities.BackUp updatedEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act - Local
        var localResult = await _handler.Handle(localCommand, CancellationToken.None);

        // Assert - Local
        localResult.ShouldNotBeNull();

        // Test remote server configuration
        var remoteCommand = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "RemoteTestServer",
            DatabaseName = "RemoteTestDatabase",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\RemoteServer\Backups\RemoteTestDatabase.bak"
        };

        Domain.Entities.BackUp updatedrEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedrEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act - Remote
        var remoteResult = await _handler.Handle(remoteCommand, CancellationToken.None);

        // Assert - Remote
        remoteResult.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_UpdateComplexProperties_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var complexProperties = @"{
            ""compression"": {
                ""enabled"": true,
                ""level"": 9,
                ""algorithm"": ""gzip""
            },
            ""encryption"": {
                ""enabled"": true,
                ""algorithm"": ""AES256"",
                ""keyRotation"": true
            },
            ""retention"": {
                ""days"": 90,
                ""policy"": ""GFS"",
                ""archiveAfter"": 30
            },
            ""monitoring"": {
                ""alerts"": true,
                ""notifications"": [""email"", ""sms""],
                ""thresholds"": {
                    ""size"": ""10GB"",
                    ""duration"": ""2h""
                }
            }
        }";

        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "ComplexPropertiesServer",
            DatabaseName = "ComplexPropertiesDatabase",
            Properties = complexProperties
        };

        Domain.Entities.BackUp updatedEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        updatedEntity.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: Update command with special characters in properties
    /// Expected: Special characters are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_UpdateWithSpecialCharacters_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "Special-Server@123!",
            DatabaseName = "Special_Database$Test",
            UserName = "Special.User+Name",
            BackUpPath = @"C:\Special Folder\Database@Backup!.bak",
            Properties = "{\"special\":\"chars!@#$%^&*()\"}"
        };

        Domain.Entities.BackUp updatedEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        updatedEntity.ShouldNotBeNull();
    }

    /// <summary>
    /// Test: Update command performance with rapid succession
    /// Expected: Multiple rapid update operations are handled correctly
    /// </summary>
    [Fact]
    public async Task Handle_ProcessRapidUpdateOperations_When_MultipleCommands()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var commands = Enumerable.Range(1, 5).Select(i => new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = $"RapidUpdateServer{i:00}",
            DatabaseName = $"RapidUpdateDatabase{i:00}"
        }).ToList();

        // Act
        var tasks = commands.Select(cmd => _handler.Handle(cmd, CancellationToken.None));
        var results = await Task.WhenAll(tasks);

        // Assert
        results.ShouldAllBe(result => result != null);
        results.ShouldAllBe(result => result.Id == existingBackUp.ReferenceId);
        
    }

    [Fact]
    public async Task Handle_UpdateWithNullProperties_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "NullPropertiesServer",
            DatabaseName = "NullPropertiesDatabase",
            Properties = null,
            CronExpression = null,
            KeepBackUpLast = null
        };

        Domain.Entities.BackUp updatedEntity = null;
        _mockBackUpRepository.Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()))
            .Callback<Domain.Entities.BackUp>(entity => updatedEntity = entity)
            .ReturnsAsync((Domain.Entities.BackUp entity) => entity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        updatedEntity.ShouldNotBeNull();
        // Null properties should be handled gracefully
    }

    /// <summary>
    /// Test: Update command return type validation
    /// Expected: Correct response type is returned
    /// </summary>
    [Fact]
    public async Task Handle_ReturnCorrectResponseType_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new UpdateBackUpCommand
        {
            Id = existingBackUp.ReferenceId,
            HostName = "ResponseTypeServer",
            DatabaseName = "ResponseTypeDatabase"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<UpdateBackUpResponse>();
        result.GetType().Name.ShouldBe("UpdateBackUpResponse");
        result.Id.ShouldNotBeNullOrEmpty();
        result.Message.ShouldNotBeNullOrEmpty();
    }

    [Fact]
    public void Should_Assign_All_Properties_Correctly()
    {
        // Arrange
        var command = new UpdateBackUpCommand
        {
            Id = "123",
            HostName = "localhost",
            DatabaseName = "TestDB",
            UserName = "admin",
            Password = "password",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = "/backup/path",
            BackUpType = "Full",
            CronExpression = "0 0 * * *",
            ScheduleType = "Daily",
            ScheduleTime = "02:00",
            Properties = "{\"Retention\":\"7days\"}",
            KeepBackUpLast = "5",
            NodeId = "node-01",
            NodeName = "PrimaryNode"
        };

        // Assert
        Assert.Equal("123", command.Id);
        Assert.Equal("localhost", command.HostName);
        Assert.Equal("TestDB", command.DatabaseName);
        Assert.Equal("admin", command.UserName);
        Assert.Equal("password", command.Password);
        Assert.True(command.IsLocalServer);
        Assert.False(command.IsBackUpServer);
        Assert.Equal("/backup/path", command.BackUpPath);
        Assert.Equal("Full", command.BackUpType);
        Assert.Equal("0 0 * * *", command.CronExpression);
        Assert.Equal("Daily", command.ScheduleType);
        Assert.Equal("02:00", command.ScheduleTime);
        Assert.Equal("{\"Retention\":\"7days\"}", command.Properties);
        Assert.Equal("5", command.KeepBackUpLast);
        Assert.Equal("node-01", command.NodeId);
        Assert.Equal("PrimaryNode", command.NodeName);
    }
}
