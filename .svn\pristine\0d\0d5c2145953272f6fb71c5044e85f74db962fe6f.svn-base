﻿@using Microsoft.AspNetCore.Mvc.TagHelpers
@model ContinuityPatrol.Domain.ViewModels.SiteLocationModel.SiteLocationViewModel
@using ContinuityPatrol.Domain.ViewModels.SiteLocationModel
@using ContinuityPatrol.Shared.Services.Helper
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-site-location"></i><span>Site Location</span></h6>
            <form class="d-flex">
                <div class="input-group me-2 w-auto">
                    <input type="search" id="siteLocSearch" class="form-control" placeholder="Search" autocomplete="off" />
                    <div class="input-group-text">
                        <div class="dropdown">
                            <span data-bs-toggle="dropdown"  title="Filter"><i class="cp-filter" ></i></span>
                            <ul class="dropdown-menu filter-dropdown" >
                                <li><h6 class="dropdown-header">Filter Search</h6></li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="city=" id="siteLocCityFilter">
                                        <label class="form-check-label" for="Name">
                                            City Name
                                        </label>
                                    </div>
                                </li>
                                <li class="dropdown-item">
                                    <div>
                                        <input class="form-check-input" type="checkbox" value="country=" id="siteLocCountryFilter">
                                        <label class="form-check-label" for="Name">
                                            Country Name
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <button type="button" id="siteLocCreate" class="btn btn-primary btn-sm"><i class="cp-add me-1"></i>Create</button>
            </form>
        </div>
        <div class="card-body pt-0">
            <table id="siteLocationTable" class="table table-hover dataTable" style="width:100%">
                <thead>
                    <tr>
                        <th class="SrNo_th">Sr. No.</th>
                        <th>City Name</th>
                        <th >Country Name</th>
                        <th >City Latitude</th>
                        <th >City Longitude</th>
                        <th class="Action-th ">Action</th>
                    </tr>
                </thead>
                <tbody>                   
                </tbody>
            </table>
        </div>
    </div>
</div>

<div id="siteLocConfigCreate" data-create-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.CreateAndEdit" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>
<div id="siteLocConfigDelete" data-delete-permission="@ContinuityPatrol.Shared.Services.Helper.WebHelper.CurrentSession.Permissions.Configuration.Delete" aria-hidden="true">
    <!-- Your Configuration content here -->
</div>

<!--Modal Create-->
<div class="modal fade" id="siteLocCreateModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Configuration" />
</div>
<!--Modal Delete-->
<div class="modal fade" data-bs-backdrop="static" id="siteLocDeleteModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <partial name="Delete" />
</div>
<script type="text/javascript">
    var RootUrl = '@Url.Content("~/")';
</script>
<script src="~/js/Configuration/Sites/Site Location/siteLocation.js"></script>