﻿using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Mocks;

public class DrReadyStatusRepositoryMocks
{
    public static Mock<IDrReadyStatusRepository> CreateDrReadyStatusRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyStatuses);

        mockDrReadyStatusRepository.Setup(repo => repo.AddAsync(It.IsAny<DRReadyStatus>())).ReturnsAsync(
            (DRReadyStatus drReadyStatus) =>
            {
                drReadyStatus.Id = new Fixture().Create<int>();

                drReadyStatus.ReferenceId = new Fixture().Create<Guid>().ToString();

                drReadyStatuses.Add(drReadyStatus);

                return drReadyStatus;
            });

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> UpdateDrReadyStatusRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyStatuses);

        mockDrReadyStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyStatuses.SingleOrDefault(x => x.ReferenceId == i));

        mockDrReadyStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DRReadyStatus>())).ReturnsAsync((DRReadyStatus drReadyStatus) =>
        {
            var index = drReadyStatuses.FindIndex(item => item.Id == drReadyStatus.Id);

            drReadyStatuses[index] = drReadyStatus;

            return drReadyStatus;
        });

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> DeleteDrReadyStatusRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyStatuses);

        mockDrReadyStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyStatuses.SingleOrDefault(x => x.ReferenceId == i));

        mockDrReadyStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<DRReadyStatus>())).ReturnsAsync((DRReadyStatus drReadyStatus) =>
        {
            var index = drReadyStatuses.FindIndex(item => item.Id == drReadyStatus.Id);

            drReadyStatus.IsActive = false;

            drReadyStatuses[index] = drReadyStatus;

            return drReadyStatus;
        });

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> GetDrReadyStatusRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyStatuses);

        mockDrReadyStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyStatuses.SingleOrDefault(x => x.ReferenceId == i));

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> GetDrReadyStatusEmptyRepository()
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(new List<DRReadyStatus>());

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> GetPaginatedDrReadyStatusRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        var queryableDrReadyStatus = drReadyStatuses.BuildMock();

        mockDrReadyStatusRepository.Setup(repo => repo.GetPaginatedQuery()).Returns(queryableDrReadyStatus);

        return mockDrReadyStatusRepository;
    }

    public static Mock<IDrReadyStatusRepository> GetDrReadyStatusByBusinessServiceIdRepository(List<DRReadyStatus> drReadyStatuses)
    {
        var mockDrReadyStatusRepository = new Mock<IDrReadyStatusRepository>();

        mockDrReadyStatusRepository.Setup(repo => repo.ListAllAsync()).ReturnsAsync(drReadyStatuses);

        mockDrReadyStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(It.IsAny<string>())).ReturnsAsync((string i) => drReadyStatuses.SingleOrDefault(x => x.ReferenceId == i));

        mockDrReadyStatusRepository.Setup(repo => repo.GetDrReadyStatusByBusinessServiceId(It.IsAny<string>())).ReturnsAsync((string i) => drReadyStatuses.Where(x => x.BusinessServiceId == i).ToList());

        return mockDrReadyStatusRepository;
    }
}