using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class DataSetFixture : IDisposable
{
    public List<DataSet> DataSetPaginationList { get; set; }
    public List<DataSet> DataSetList { get; set; }
    public DataSet DataSetDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string DataSetName = "TestDataSet";
    public const string TableAccessId = "TABLE_ACCESS_123";

    public ApplicationDbContext DbContext { get; private set; }

    public DataSetFixture()
    {
        var fixture = new Fixture();

        DataSetList = fixture.Create<List<DataSet>>();

        DataSetPaginationList = fixture.CreateMany<DataSet>(20).ToList();

        DataSetPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSetPaginationList.ForEach(x => x.IsActive = true);
        DataSetPaginationList.ForEach(x => x.CompanyId = CompanyId);
        DataSetPaginationList.ForEach(x => x.DataSetName = DataSetName);
        DataSetPaginationList.ForEach(x => x.TableAccessId = TableAccessId);

        DataSetList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        DataSetList.ForEach(x => x.IsActive = true);
        DataSetList.ForEach(x => x.CompanyId = CompanyId);
        DataSetList.ForEach(x => x.DataSetName = DataSetName);
        DataSetList.ForEach(x => x.TableAccessId = TableAccessId);

        DataSetDto = fixture.Create<DataSet>();
        DataSetDto.ReferenceId = Guid.NewGuid().ToString();
        DataSetDto.IsActive = true;
        DataSetDto.CompanyId = CompanyId;
        DataSetDto.DataSetName = DataSetName;
        DataSetDto.TableAccessId = TableAccessId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
