using ContinuityPatrol.Application.Features.AccessManager.Commands.Create;
using ContinuityPatrol.Application.Features.AccessManager.Commands.Update;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetByRole;
using ContinuityPatrol.Application.Features.AccessManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AccessManagerModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class AccessManagerFixture
{
    public List<AccessManagerListVm> AccessManagerListVm { get; }
    public PaginatedResult<AccessManagerListVm> PaginatedAccessManagerListVm { get; }
    public CreateAccessManagerCommand CreateAccessManagerCommand { get; }
    public UpdateAccessManagerCommand UpdateAccessManagerCommand { get; }
    public GetByRoleIdVm GetByRoleIdVm { get; }
    public GetAccessManagerPaginatedListQuery GetAccessManagerPaginatedListQuery { get; }

    public AccessManagerFixture()
    {
        var fixture = new Fixture();

        AccessManagerListVm = fixture.Create<List<AccessManagerListVm>>();
        PaginatedAccessManagerListVm = fixture.Create<PaginatedResult<AccessManagerListVm>>();
        CreateAccessManagerCommand = fixture.Create<CreateAccessManagerCommand>();
        UpdateAccessManagerCommand = fixture.Create<UpdateAccessManagerCommand>();
        GetByRoleIdVm = fixture.Create<GetByRoleIdVm>();
        GetAccessManagerPaginatedListQuery = fixture.Create<GetAccessManagerPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
