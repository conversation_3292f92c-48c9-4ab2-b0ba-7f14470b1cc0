using ContinuityPatrol.Application.Features.Archive.Events.Delete;

namespace ContinuityPatrol.Application.Features.Archive.Commands.Delete;

public class DeleteArchiveCommandHandler : IRequestHandler<DeleteArchiveCommand, DeleteArchiveResponse>
{
    private readonly IArchiveRepository _archiveRepository;
    private readonly IPublisher _publisher;

    public DeleteArchiveCommandHandler(IArchiveRepository archiveRepository, IPublisher publisher)
    {
        _archiveRepository = archiveRepository;

        _publisher = publisher;
    }

    public async Task<DeleteArchiveResponse> Handle(DeleteArchiveCommand request, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(request.Id, "Archive Id");
        var eventToDelete = await _archiveRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.Archive),
            new NotFoundException(nameof(Domain.Entities.Archive), request.Id));

        eventToDelete.IsActive = false;

        await _archiveRepository.UpdateAsync(eventToDelete);

        var response = new DeleteArchiveResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.Archive), eventToDelete.ArchiveProfileName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new ArchiveDeletedEvent { Name = eventToDelete.ArchiveProfileName },
            cancellationToken);

        return response;
    }
}