using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;

namespace ContinuityPatrol.Application.Features.CyberAlert.Queries.GetList;

public class GetCyberAlertListQueryHandler : IRequestHandler<GetCyberAlertListQuery, List<CyberAlertListVm>>
{
    private readonly ICyberAlertRepository _cyberAlertRepository;
    private readonly IMapper _mapper;

    public GetCyberAlertListQueryHandler(IMapper mapper, ICyberAlertRepository cyberAlertRepository)
    {
        _mapper = mapper;
        _cyberAlertRepository = cyberAlertRepository;
    }

    public async Task<List<CyberAlertListVm>> Handle(GetCyberAlertListQuery request,
        CancellationToken cancellationToken)
    {
        var cyberAlerts = await _cyberAlertRepository.ListAllAsync();

        if (cyberAlerts.Count <= 0) return new List<CyberAlertListVm>();

        return _mapper.Map<List<CyberAlertListVm>>(cyberAlerts);
    }
}