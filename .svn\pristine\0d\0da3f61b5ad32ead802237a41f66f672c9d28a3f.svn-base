﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.EscalationMatrixLevel.Events.Update;

public class EscalationMatrixLevelUpdatedEventHandler : INotificationHandler<EscalationMatrixLevelUpdatedEvent>
{
    private readonly ILogger<EscalationMatrixLevelUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public EscalationMatrixLevelUpdatedEventHandler(ILoggedInUserService userService,
        IUserActivityRepository userActivityRepository, ILogger<EscalationMatrixLevelUpdatedEventHandler> logger)
    {
        _userService = userService;
        _userActivityRepository = userActivityRepository;
        _logger = logger;
    }

    public async Task Handle(EscalationMatrixLevelUpdatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Entity = Modules.EscalationMatrixLevel.ToString(),
            Action = $"{ActivityType.Update} {Modules.EscalationMatrixLevel}",
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $" Escalation Matrix Level '{notification.EscLevName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($" Escalation Matrix Level '{notification.EscLevName}' updated successfully.");
    }
}