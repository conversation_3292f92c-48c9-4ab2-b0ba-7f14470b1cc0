using AutoFixture;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetProfileExecutorByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetRunningUserList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class WorkflowOperationFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<WorkflowOperation> WorkflowOperations { get; set; }
    public List<WorkflowOperation> InvalidWorkflowOperations { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<WorkflowOperationListVm> WorkflowOperationListVm { get; }
    public WorkflowOperationDetailVm WorkflowOperationDetailVm { get; }
    public List<WorkflowOperationRunningUserListVm> WorkflowOperationRunningUserListVm { get; }
    public ProfileExecutorByBusinessServiceIdVm ProfileExecutorByBusinessServiceIdVm { get; }
    public List<GetDescriptionByStartTimeAndEndTimeListVm> GetDescriptionByStartTimeAndEndTimeListVm { get; }
    public List<WorkflowOperationDrDrillVm> WorkflowOperationDrDrillVm { get; }
    public PaginatedResult<WorkflowOperationListVm> PaginatedWorkflowOperations { get; }

    // Commands
    public CreateWorkflowOperationCommand CreateWorkflowOperationCommand { get; set; }
    public UpdateWorkflowOperationCommand UpdateWorkflowOperationCommand { get; set; }
    public DeleteWorkflowOperationCommand DeleteWorkflowOperationCommand { get; set; }

    // Queries
    public GetWorkflowOperationDetailQuery GetWorkflowOperationDetailQuery { get; set; }
    public GetWorkflowOperationListQuery GetWorkflowOperationListQuery { get; set; }
    public GetWorkflowOperationPaginatedListQuery GetWorkflowOperationPaginatedListQuery { get; set; }
    public GetWorkflowOperationRunningUserListQuery GetWorkflowOperationRunningUserListQuery { get; set; }
    public GetProfileExecutorByBusinessServiceIdQuery GetProfileExecutorByBusinessServiceIdQuery { get; set; }
    public GetDescriptionByStartTimeAndEndTimeListQuery GetDescriptionByStartTimeAndEndTimeListQuery { get; set; }
    public GetDrDrillByBusinessServiceIdQuery GetDrDrillByBusinessServiceIdQuery { get; set; }

    // Responses
    public CreateWorkflowOperationResponse CreateWorkflowOperationResponse { get; set; }
    public UpdateWorkflowOperationResponse UpdateWorkflowOperationResponse { get; set; }
    public DeleteWorkflowOperationResponse DeleteWorkflowOperationResponse { get; set; }

    public WorkflowOperationFixture()
    {
        try
        {
            // Create test data using AutoFixture
            WorkflowOperations = AutoWorkflowOperationFixture.Create<List<WorkflowOperation>>();
            InvalidWorkflowOperations = AutoWorkflowOperationFixture.Create<List<WorkflowOperation>>();
            UserActivities = AutoWorkflowOperationFixture.Create<List<UserActivity>>();

            // Set invalid workflow operations to inactive
            foreach (var invalidOperation in InvalidWorkflowOperations)
            {
                invalidOperation.IsActive = false;
            }

            // Commands
            CreateWorkflowOperationCommand = AutoWorkflowOperationFixture.Create<CreateWorkflowOperationCommand>();
            UpdateWorkflowOperationCommand = AutoWorkflowOperationFixture.Create<UpdateWorkflowOperationCommand>();
            DeleteWorkflowOperationCommand = AutoWorkflowOperationFixture.Create<DeleteWorkflowOperationCommand>();

            // Set command IDs to match existing entities
            if (WorkflowOperations.Any())
            {
                UpdateWorkflowOperationCommand.Id = WorkflowOperations.First().ReferenceId;
                DeleteWorkflowOperationCommand.Id = WorkflowOperations.First().ReferenceId;
            }

            // Queries
            GetWorkflowOperationDetailQuery = AutoWorkflowOperationFixture.Create<GetWorkflowOperationDetailQuery>();
            GetWorkflowOperationListQuery = AutoWorkflowOperationFixture.Create<GetWorkflowOperationListQuery>();
            GetWorkflowOperationPaginatedListQuery = AutoWorkflowOperationFixture.Create<GetWorkflowOperationPaginatedListQuery>();
            GetWorkflowOperationRunningUserListQuery = AutoWorkflowOperationFixture.Create<GetWorkflowOperationRunningUserListQuery>();
            GetProfileExecutorByBusinessServiceIdQuery = AutoWorkflowOperationFixture.Create<GetProfileExecutorByBusinessServiceIdQuery>();
            GetDescriptionByStartTimeAndEndTimeListQuery = AutoWorkflowOperationFixture.Create<GetDescriptionByStartTimeAndEndTimeListQuery>();
            GetDrDrillByBusinessServiceIdQuery = AutoWorkflowOperationFixture.Create<GetDrDrillByBusinessServiceIdQuery>();

            // Set query IDs to match existing entities
            if (WorkflowOperations.Any())
            {
                GetWorkflowOperationDetailQuery.Id = WorkflowOperations.First().ReferenceId;
            }

            // Responses
            CreateWorkflowOperationResponse = AutoWorkflowOperationFixture.Create<CreateWorkflowOperationResponse>();
            UpdateWorkflowOperationResponse = AutoWorkflowOperationFixture.Create<UpdateWorkflowOperationResponse>();
            DeleteWorkflowOperationResponse = AutoWorkflowOperationFixture.Create<DeleteWorkflowOperationResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            WorkflowOperations = new List<WorkflowOperation>();
            InvalidWorkflowOperations = new List<WorkflowOperation>();
            UserActivities = new List<UserActivity>();
            CreateWorkflowOperationCommand = new CreateWorkflowOperationCommand();
            UpdateWorkflowOperationCommand = new UpdateWorkflowOperationCommand();
            DeleteWorkflowOperationCommand = new DeleteWorkflowOperationCommand();
            GetWorkflowOperationDetailQuery = new GetWorkflowOperationDetailQuery();
            GetWorkflowOperationListQuery = new GetWorkflowOperationListQuery();
            GetWorkflowOperationPaginatedListQuery = new GetWorkflowOperationPaginatedListQuery();
            GetWorkflowOperationRunningUserListQuery = new GetWorkflowOperationRunningUserListQuery();
            GetProfileExecutorByBusinessServiceIdQuery = new GetProfileExecutorByBusinessServiceIdQuery();
            GetDescriptionByStartTimeAndEndTimeListQuery = new GetDescriptionByStartTimeAndEndTimeListQuery();
            GetDrDrillByBusinessServiceIdQuery = new GetDrDrillByBusinessServiceIdQuery();
            CreateWorkflowOperationResponse = new CreateWorkflowOperationResponse();
            UpdateWorkflowOperationResponse = new UpdateWorkflowOperationResponse();
            DeleteWorkflowOperationResponse = new DeleteWorkflowOperationResponse();
        }

        // Configure View Models
        WorkflowOperationListVm = new List<WorkflowOperationListVm>
        {
            new WorkflowOperationListVm
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_001",
                ProfileId = "PROFILE_001",
                ProfileName = "Production Backup Profile",
                Status = "Running",
                Description = "Daily production database backup operation",
                StartTime = DateTime.UtcNow.AddHours(-2),
                EndTime = DateTime.UtcNow.AddHours(1),
                UserName = "admin",
                RunMode = "Automatic",
                IsDrCalendar = true
            },
            new WorkflowOperationListVm
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_001",
                ProfileId = "PROFILE_002",
                ProfileName = "Application Deployment Profile",
                Status = "Completed",
                Description = "Web application deployment operation",
                StartTime = DateTime.UtcNow.AddHours(-4),
                EndTime = DateTime.UtcNow.AddHours(-3),
                UserName = "deployer",
                RunMode = "Manual",
                IsDrCalendar = false
            },
            new WorkflowOperationListVm
            {
                Id = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_001",
                ProfileId = "PROFILE_003",
                ProfileName = "System Maintenance Profile",
                Status = "Pending",
                Description = "Scheduled system maintenance operation",
                StartTime = DateTime.UtcNow.AddHours(2),
                EndTime = DateTime.UtcNow.AddHours(4),
                UserName = "maintenance",
                RunMode = "Scheduled",
                IsDrCalendar = true
            }
        };

        WorkflowOperationDetailVm = new WorkflowOperationDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            ProfileId = "PROFILE_001",
            ProfileName = "Production Backup Profile",
            Status = "Running",
            Description = "Detailed production database backup operation with full verification",
            StartTime = DateTime.UtcNow.AddHours(-2),
            EndTime = DateTime.UtcNow.AddHours(1),
            UserName = "admin",
            RunMode = "Automatic",
            IsDrCalendar = true
        };

        WorkflowOperationRunningUserListVm = new List<WorkflowOperationRunningUserListVm>
        {
            new WorkflowOperationRunningUserListVm
            {
                UserId = Guid.NewGuid().ToString(),
                UserName = "admin",
            },
            new WorkflowOperationRunningUserListVm
            {
                UserId = Guid.NewGuid().ToString(),
                UserName = "deployer",
            }
        };

        ProfileExecutorByBusinessServiceIdVm = new ProfileExecutorByBusinessServiceIdVm
        {
            BusinessServiceId = "BS_001",
        };

        GetDescriptionByStartTimeAndEndTimeListVm = new List<GetDescriptionByStartTimeAndEndTimeListVm>
        {
            new GetDescriptionByStartTimeAndEndTimeListVm
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Production backup completed successfully"
            },
            new GetDescriptionByStartTimeAndEndTimeListVm
            {
                Id = Guid.NewGuid().ToString(),
                Description = "Application deployment in progress"
            }
        };

        WorkflowOperationDrDrillVm = new List<WorkflowOperationDrDrillVm>
        {
            new WorkflowOperationDrDrillVm
            {
                ProfileId = Guid.NewGuid().ToString(),
                ProfileName = "Core Banking Service",
                Status = "Completed"
            }
        };

        // Configure PaginatedWorkflowOperations
        PaginatedWorkflowOperations = new PaginatedResult<WorkflowOperationListVm>
        {
            Data = WorkflowOperationListVm,
            PageSize = 10,
            TotalCount = WorkflowOperationListVm.Count,
            TotalPages = 1
        };

        // Configure AutoMapper for WorkflowOperation mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<WorkflowOperationProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoWorkflowOperationFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize WorkflowOperation entity
            fixture.Customize<WorkflowOperation>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.CompanyId, "COMPANY_001")
                .With(b => b.ProfileId, "PROFILE_001")
                .With(b => b.ProfileName, "Test Workflow Profile")
                .With(b => b.Status, "Running")
                .With(b => b.Description, "Test workflow operation")
                .With(b => b.StartTime, DateTime.UtcNow.AddHours(-1))
                .With(b => b.EndTime, DateTime.UtcNow.AddHours(1))
                .With(b => b.UserName, "testuser")
                .With(b => b.RunMode, "Automatic")
                .With(b => b.IsDrCalendar, true));

            // Customize CreateWorkflowOperationCommand
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateWorkflowOperationCommand>(p => p.ProfileName, 100));
            fixture.Customize<CreateWorkflowOperationCommand>(c => c
                .With(b => b.CompanyId, "COMPANY_001")
                .With(b => b.ProfileId, "PROFILE_NEW")
                .With(b => b.ProfileName, "New Test Workflow Profile")
                .With(b => b.Status, "Pending")
                .With(b => b.Description, "New test workflow operation")
                .With(b => b.StartTime, DateTime.UtcNow.AddHours(1))
                .With(b => b.EndTime, DateTime.UtcNow.AddHours(3))
                .With(b => b.UserName, "admin")
                .With(b => b.RunMode, "Manual")
                .With(b => b.IsDrCalendar, false));

            // Customize UpdateWorkflowOperationCommand
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateWorkflowOperationCommand>(p => p.ProfileName, 100));
            fixture.Customize<UpdateWorkflowOperationCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.ProfileId, "PROFILE_UPD")
                .With(b => b.ProfileName, "Updated Test Workflow Profile")
                .With(b => b.Status, "Completed")
                .With(b => b.Description, "Updated test workflow operation")
                .With(b => b.EndTime, DateTime.UtcNow)
                .With(b => b.UserName, "admin")
                .With(b => b.RunMode, "Automatic")
                .With(b => b.IsDrCalendar, true));

            // Customize DeleteWorkflowOperationCommand
            fixture.Customize<DeleteWorkflowOperationCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize GetWorkflowOperationDetailQuery
            fixture.Customize<GetWorkflowOperationDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize GetWorkflowOperationPaginatedListQuery
            fixture.Customize<GetWorkflowOperationPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "ProfileName")
                .With(b => b.SortOrder, "asc"));

            // Customize GetProfileExecutorByBusinessServiceIdQuery
            fixture.Customize<GetProfileExecutorByBusinessServiceIdQuery>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString()));

            // Customize GetDescriptionByStartTimeAndEndTimeListQuery
            fixture.Customize<GetDescriptionByStartTimeAndEndTimeListQuery>(c => c
                .With(b => b.StartTime, DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd"))
                .With(b => b.EndTime, DateTime.UtcNow.ToString("yyyy-MM-dd"))
                .With(b => b.RunMode, "Automatic"));

            // Customize GetDrDrillByBusinessServiceIdQuery
            fixture.Customize<GetDrDrillByBusinessServiceIdQuery>(c => c
                .With(b => b.BusinessServiceId, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreateWorkflowOperationResponse>(c => c
                .With(b => b.WorkflowOperationId, Guid.NewGuid().ToString())
                .With(b => b.Success, true)
                .With(b => b.Message, "WorkflowOperation created successfully"));

            fixture.Customize<UpdateWorkflowOperationResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Success, true)
                .With(b => b.Message, "WorkflowOperation updated successfully"));

            fixture.Customize<DeleteWorkflowOperationResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Success, true)
                .With(b => b.Message, "WorkflowOperation deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "WorkflowOperation")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
