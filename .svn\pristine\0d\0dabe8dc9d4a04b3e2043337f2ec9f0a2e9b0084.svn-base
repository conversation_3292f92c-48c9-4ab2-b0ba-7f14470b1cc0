using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Create;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Commands.Update;
using ContinuityPatrol.Application.Features.BulkImportActionResult.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class BulkImportActionResultFixture
{
    public List<BulkImportActionResultListVm> BulkImportActionResultListVm { get; }
    public BulkImportActionResultDetailVm BulkImportActionResultDetailVm { get; }
    public CreateBulkImportActionResultCommand CreateBulkImportActionResultCommand { get; }
    public UpdateBulkImportActionResultCommand UpdateBulkImportActionResultCommand { get; }

    public BulkImportActionResultFixture()
    {
        var fixture = new Fixture();

        BulkImportActionResultListVm = fixture.Create<List<BulkImportActionResultListVm>>();
        BulkImportActionResultDetailVm = fixture.Create<BulkImportActionResultDetailVm>();
        CreateBulkImportActionResultCommand = fixture.Create<CreateBulkImportActionResultCommand>();
        UpdateBulkImportActionResultCommand = fixture.Create<UpdateBulkImportActionResultCommand>();
    }

    public void Dispose()
    {

    }
}
