﻿using ContinuityPatrol.Application.Features.OracleMonitorStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.OracleMonitorStatusModel;

namespace ContinuityPatrol.Application.UnitTests.Features.OracleMonitorStatus.Queries;
public class GetOracleMonitorStatusListQueryHandlerTests
{
    private readonly Mock<IOracleMonitorStatusRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetOracleMonitorStatusListQueryHandler _handler;

    public GetOracleMonitorStatusListQueryHandlerTests()
    {
        _repositoryMock = new Mock<IOracleMonitorStatusRepository>();
        _mapperMock = new Mock<IMapper>();
        _handler = new GetOracleMonitorStatusListQueryHandler(_repositoryMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenNoEntitiesExist()
    {
        // Arrange
        _repositoryMock.Setup(r => r.ListAllAsync()).ReturnsAsync(new List<Domain.Entities.OracleMonitorStatus>());

        var request = new GetOracleMonitorStatusListQuery();

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();

        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorStatusListVm>>(It.IsAny<List<Domain.Entities.OracleMonitorStatus>>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedList_WhenEntitiesExist()
    {
        // Arrange
        var entities = new List<Domain.Entities.OracleMonitorStatus>
        {
            new Domain.Entities.OracleMonitorStatus
            {
                ReferenceId = "1",
                Type = "TypeA",
                InfraObjectId = "Infra01",
                InfraObjectName = "Primary",
                WorkflowId = "WF01",
                WorkflowName = "Workflow",
                Properties = "Props",
                ConfiguredRPO = "RPO",
                DataLagValue = "Lag"
            }
        };

        var viewModel = new List<OracleMonitorStatusListVm>
        {
            new OracleMonitorStatusListVm
            {
                Id = "1",
                Type = "TypeA",
                InfraObjectId = "Infra01",
                InfraObjectName = "Primary",
                WorkflowId = "WF01",
                WorkflowName = "Workflow",
                Properties = "Props",
                ConfiguredRPO = "RPO",
                DataLagValue = "Lag"
            }
        };

        _repositoryMock.Setup(r => r.ListAllAsync()).ReturnsAsync(entities);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorStatusListVm>>(entities)).Returns(viewModel);

        var request = new GetOracleMonitorStatusListQuery();

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result[0].Id.Should().Be("1");

        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorStatusListVm>>(entities), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldReturnCorrectMappedList_WhenMultipleEntitiesExist()
    {
        // Arrange
        var entities = new List<Domain.Entities.OracleMonitorStatus>
        {
            new Domain.Entities.OracleMonitorStatus { ReferenceId = "1", Type = "Type1" },
            new Domain.Entities.OracleMonitorStatus { ReferenceId = "2", Type = "Type2" }
        };

        var viewModels = new List<OracleMonitorStatusListVm>
        {
            new OracleMonitorStatusListVm { Id = "1", Type = "Type1" },
            new OracleMonitorStatusListVm { Id = "2", Type = "Type2" }
        };

        _repositoryMock.Setup(r => r.ListAllAsync()).ReturnsAsync(entities);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorStatusListVm>>(entities)).Returns(viewModels);

        var request = new GetOracleMonitorStatusListQuery();

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().HaveCount(2);
        result[0].Id.Should().Be("1");
        result[1].Id.Should().Be("2");

        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorStatusListVm>>(entities), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldAcceptCancellationToken()
    {
        // Arrange
        var entities = new List<Domain.Entities.OracleMonitorStatus>
        {
            new() { ReferenceId = "abc123", Type = "TestType" }
        };

        var mappedVmList = new List<OracleMonitorStatusListVm>
        {
            new() { Id = "abc123", Type = "TestType" }
        };

        _repositoryMock.Setup(r => r.ListAllAsync()).ReturnsAsync(entities);
        _mapperMock.Setup(m => m.Map<List<OracleMonitorStatusListVm>>(entities)).Returns(mappedVmList);

        var token = new CancellationToken();
        var request = new GetOracleMonitorStatusListQuery();

        // Act
        var result = await _handler.Handle(request, token);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);
        result.First().Id.Should().Be("abc123");

        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
        _mapperMock.Verify(m => m.Map<List<OracleMonitorStatusListVm>>(entities), Times.Once);
    }

}