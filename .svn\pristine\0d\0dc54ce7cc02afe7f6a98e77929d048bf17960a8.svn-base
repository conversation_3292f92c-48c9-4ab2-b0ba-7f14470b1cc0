﻿using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Delete;
using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.LicenseHistoryModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class LicenseHistoryController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<List<LicenseHistoryListVm>>> GetLicenseHistory()
    {
        Logger.LogDebug("Get All License History");

        return Ok(await Mediator.Send(new GetLicenseHistoryListQuery()));
    }

    [HttpGet("{id}", Name = "GetLicenseHistoryById")]
    [Authorize(Policy = Permissions.Admin.View)]
    public async Task<ActionResult<LicenseHistoryDetailVm>> GetLicenseHistoryById(string id)
    {

        Guard.Against.InvalidGuidOrEmpty(id, "LicenseHistory Id");

        Logger.LogDebug($"Get LicenseHistory Detail by Id'{id}'");

        return Ok(await Mediator.Send(new GetLicenseHistoryDetailQuery { LicenseId = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Admin.Create)]
    public async Task<ActionResult<CreateLicenseHistoryResponse>> CreateLicenseHistory([FromBody] CreateLicenseHistoryCommand licenseHistoryCommand)
    {
        Logger.LogDebug($"Create LicenseHistory '{licenseHistoryCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateLicenseHistory), await Mediator.Send(licenseHistoryCommand));
    }


    [HttpPut]
    [Authorize(Policy = Permissions.Admin.Edit)]
    public async Task<ActionResult<UpdateLicenseHistoryResponse>> UpdateLicenseHistory([FromBody] UpdateLicenseHistoryCommand updateLicenseHistoryCommand)
    {
        Logger.LogDebug($"Update LicenseHistory '{updateLicenseHistoryCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateLicenseHistoryCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Admin.Delete)]
    public async Task<ActionResult<DeleteLicenseHistoryResponse>> DeleteLicenseHistory(string id)
    {
        Logger.LogDebug($"Delete LicenseHistory Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteLicenseHistoryCommand { Id = id }));

    }


    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllLicenseManagersCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllLicenseManagerNamesCacheKey };

        ClearCache(cacheKeys);
    }
}

