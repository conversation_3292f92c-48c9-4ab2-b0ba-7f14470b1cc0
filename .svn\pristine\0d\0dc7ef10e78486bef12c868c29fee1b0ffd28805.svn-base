﻿using ContinuityPatrol.Application.Features.FiaTemplate.Events.PaginatedView;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FiaTemplate.Events;

public class PaginatedFiaTemplateEventTests : IClassFixture<FiaTemplateFixture>, IClassFixture<UserActivityFixture>
{
    private readonly FiaTemplateFixture _fiaTemplateFixture;

    private readonly UserActivityFixture _userActivityFixture;

    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;

    private readonly FiaTemplatePaginatedeventHandler _handler;

    public PaginatedFiaTemplateEventTests(FiaTemplateFixture fiaTemplateFixture, UserActivityFixture userActivityFixture)
    {
        _fiaTemplateFixture = fiaTemplateFixture;
        _userActivityFixture = userActivityFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        mockLoggedInUserService.Setup(x => x.LoginName).Returns("Admin");

        var mockFiaTemplateEventLogger = new Mock<ILogger<FiaTemplatePaginatedeventHandler>>();

        _mockUserActivityRepository = FiaTemplateRepositoryMocks.CreateFiaTemplateEventRepository(_userActivityFixture.UserActivities);

        _handler = new FiaTemplatePaginatedeventHandler(mockLoggedInUserService.Object, mockFiaTemplateEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_ViewFiaTemplateEventCreated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Admin";

        var result = _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_Entity()
    {
        // Arrange
        Domain.Entities.UserActivity capturedUserActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedUserActivity);
        Assert.Equal("FiaTemplate", capturedUserActivity.Entity);
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_Action()
    {
        // Arrange
        Domain.Entities.UserActivity capturedUserActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedUserActivity);
        Assert.Equal("View FiaTemplate", capturedUserActivity.Action);
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_ActivityType()
    {
        // Arrange
        Domain.Entities.UserActivity capturedUserActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedUserActivity);
        Assert.Equal("View", capturedUserActivity.ActivityType);
    }

    [Fact]
    public async Task Handle_Should_Create_UserActivity_With_Correct_ActivityDetails()
    {
        // Arrange
        Domain.Entities.UserActivity capturedUserActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedUserActivity);
        Assert.Equal("FIA Template viewed", capturedUserActivity.ActivityDetails);
    }

    [Fact]
    public async Task Handle_Should_Set_UserActivity_Properties_From_UserService()
    {
        // Arrange
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        mockLoggedInUserService.Setup(x => x.UserId).Returns("test-user-id");
        mockLoggedInUserService.Setup(x => x.LoginName).Returns("test-login");
        mockLoggedInUserService.Setup(x => x.CompanyId).Returns("test-company-id");
        mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("test-url");
        mockLoggedInUserService.Setup(x => x.IpAddress).Returns("***********");

        var mockLogger = new Mock<ILogger<FiaTemplatePaginatedeventHandler>>();
        var handler = new FiaTemplatePaginatedeventHandler(mockLoggedInUserService.Object, mockLogger.Object, _mockUserActivityRepository.Object);

        Domain.Entities.UserActivity capturedUserActivity = null;
        _mockUserActivityRepository.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(ua => capturedUserActivity = ua);

        // Act
        await handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedUserActivity);
        Assert.Equal("test-user-id", capturedUserActivity.UserId);
        Assert.Equal("test-login", capturedUserActivity.LoginName);
        Assert.Equal("test-company-id", capturedUserActivity.CompanyId);
        Assert.Equal("test-url", capturedUserActivity.RequestUrl);
        Assert.Equal("***********", capturedUserActivity.HostAddress);
    }

    [Fact]
    public async Task Handle_Should_Log_Information_Message()
    {
        // Arrange
        var mockLogger = new Mock<ILogger<FiaTemplatePaginatedeventHandler>>();
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();
        var handler = new FiaTemplatePaginatedeventHandler(mockLoggedInUserService.Object, mockLogger.Object, _mockUserActivityRepository.Object);

        // Act
        await handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);

        // Assert
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("FIA Template viewed")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_Should_Complete_Successfully_With_Valid_Event()
    {
        // Act
        var task = _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, CancellationToken.None);
        await task;

        // Assert
        Assert.True(task.IsCompletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Should_Handle_CancellationToken()
    {
        // Arrange
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act & Assert
        await _handler.Handle(_fiaTemplateFixture.FiaTemplatePaginatedEvent, cts.Token);

        // Should complete without throwing OperationCanceledException
        // since the handler doesn't check for cancellation
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
}
