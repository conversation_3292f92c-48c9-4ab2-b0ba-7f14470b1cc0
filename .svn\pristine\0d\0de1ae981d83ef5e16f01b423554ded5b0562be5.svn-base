﻿using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FormType.Commands;

public class UpdateFormTypeTests : IClassFixture<FormTypeFixture>
{
    private readonly FormTypeFixture _formTypeFixture;

    private readonly Mock<IFormTypeRepository> _mockFormTypeRepository;

    private readonly UpdateFormTypeCommandHandler _handler;

    public UpdateFormTypeTests(FormTypeFixture formTypeFixture)
    {
        _formTypeFixture = formTypeFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockPublisher = new Mock<IPublisher>();

        _mockFormTypeRepository = FormTypeRepositoryMocks.UpdateFormTypeRepository(_formTypeFixture.FormTypes);

        _handler = new UpdateFormTypeCommandHandler(_formTypeFixture.Mapper, mockPublisher.Object, _mockFormTypeRepository.Object, mockLoggedInUserService.Object);
    }

    [Fact]
    public async Task Handle_ValidForm_UpdateToFormTypesRepo()
    {
        _formTypeFixture.UpdateFormTypeCommand.Id = _formTypeFixture.FormTypes[0].ReferenceId;

        var result = await _handler.Handle(_formTypeFixture.UpdateFormTypeCommand, CancellationToken.None);

        var formType = await _mockFormTypeRepository.Object.GetFormTypeById(result.Id);

        Assert.Equal(_formTypeFixture.UpdateFormTypeCommand.FormTypeName, formType.FormTypeName);
    }

    [Fact]
    public async Task Handle_Return_ValidFormTypeResponse_WhenUpdate_FormType()
    {
        _formTypeFixture.UpdateFormTypeCommand.Id = _formTypeFixture.FormTypes[0].ReferenceId;

        var result = await _handler.Handle(_formTypeFixture.UpdateFormTypeCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateFormTypeResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_formTypeFixture.UpdateFormTypeCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidFormTypeId()
    {
        _formTypeFixture.UpdateFormTypeCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_formTypeFixture.UpdateFormTypeCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _formTypeFixture.UpdateFormTypeCommand.Id = _formTypeFixture.FormTypes[0].ReferenceId;

        await _handler.Handle(_formTypeFixture.UpdateFormTypeCommand, CancellationToken.None);

        _mockFormTypeRepository.Verify(x => x.GetFormTypeById(It.IsAny<string>()), Times.Once);

        _mockFormTypeRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.FormType>()), Times.Once);
    }
}