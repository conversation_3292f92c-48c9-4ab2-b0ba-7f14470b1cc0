﻿namespace ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;

public class GetMSSQLAlwaysOnMonitorLogsDetailQueryHandler : IRequestHandler<GetMSSQLAlwaysOnMonitorLogsDetailQuery,
    MSSQLAlwaysOnMonitorLogsDetailVm>
{
    private readonly IMapper _mapper;
    private readonly IMssqlAlwaysOnMonitorLogsRepository _mssqlAlwaysOnMonitorLogsRepository;

    public GetMSSQLAlwaysOnMonitorLogsDetailQueryHandler(
        IMssqlAlwaysOnMonitorLogsRepository mssqlAlwaysOnMonitorLogsRepository, IMapper mapper)
    {
        _mssqlAlwaysOnMonitorLogsRepository = mssqlAlwaysOnMonitorLogsRepository;
        _mapper = mapper;
    }

    public async Task<MSSQLAlwaysOnMonitorLogsDetailVm> Handle(GetMSSQLAlwaysOnMonitorLogsDetailQuery request,
        CancellationToken cancellationToken)
    {
        var mssqlAlwaysOnMonitorLogs = await _mssqlAlwaysOnMonitorLogsRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(mssqlAlwaysOnMonitorLogs, nameof(MSSQLAlwaysOnMonitorLogs),
            new NotFoundException(nameof(MSSQLAlwaysOnMonitorLogs), request.Id));

        var mssqlAlwaysOnMonitorLogsDetailDto = _mapper.Map<MSSQLAlwaysOnMonitorLogsDetailVm>(mssqlAlwaysOnMonitorLogs);

        return mssqlAlwaysOnMonitorLogsDetailDto ??
               throw new NotFoundException(nameof(MSSQLAlwaysOnMonitorLogs), request.Id);
    }
}