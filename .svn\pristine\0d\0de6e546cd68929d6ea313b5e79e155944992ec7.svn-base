using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Create;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Delete;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.TestConnection;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.Update;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateDefault;
using ContinuityPatrol.Application.Features.LoadBalancer.Commands.UpdateNodeStatus;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetListById;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetNamesByType;
using ContinuityPatrol.Application.Features.LoadBalancer.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.LoadBalancerModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class LoadBalancerControllerFixture
{
    private readonly Fixture _fixture;

    public LoadBalancerControllerFixture()
    {
        _fixture = new Fixture();
        _fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
            .ForEach(b => _fixture.Behaviors.Remove(b));
        _fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        
        InitializeTestData();
    }

    public string ValidId { get; private set; }
    public string ValidIpAddress { get; private set; }
    public int ValidPort { get; private set; }
    
    public CreateLoadBalancerCommand CreateCommand { get; private set; }
    public CreateLoadBalancerResponse CreateResponse { get; private set; }
    
    public UpdateLoadBalancerCommand UpdateCommand { get; private set; }
    public UpdateLoadBalancerResponse UpdateResponse { get; private set; }
    
    public DeleteLoadBalancerResponse DeleteResponse { get; private set; }
    
    public LoadBalancerTestConnectionCommand TestConnectionCommand { get; private set; }
    
    public UpdateLoadBalancerDefaultCommand UpdateDefaultCommand { get; private set; }
    public UpdateLoadBalancerDefaultResponse UpdateDefaultResponse { get; private set; }
    
    public UpdateNodeStatusCommand UpdateNodeStatusCommand { get; private set; }
    public UpdateNodeStatusResponse UpdateNodeStatusResponse { get; private set; }
    
    public List<LoadBalancerListVm> LoadBalancerList { get; private set; }
    public LoadBalancerDetailVm LoadBalancerDetail { get; private set; }

    public List<GetLoadBalancerListByIdVm> LoadBalancerListById { get; private set; }

    public List<LoadBalancerTypeVm> LoadBalancerTypeList { get; private set; }

    public List<LoadBalancerNameVm> LoadBalancerNameVmsList { get; private set; }


    private void InitializeTestData()
    {
        ValidId = Guid.NewGuid().ToString();
        ValidIpAddress = "*************";
        ValidPort = 8080;

        // Create Command and Response
        CreateCommand = new CreateLoadBalancerCommand
        {
            Name = "Test LoadBalancer",
            IPAddress = ValidIpAddress,
            HostName = "test-host",
            Port = ValidPort,
            ConnectionType = "http",
            Type = "Workflow Service",
            TypeCategory = "LoadBalancer",
            IsDefault = false,
            IsConnection = true,
            IsNodeStatus = true,
            HealthStatus = "Active"
        };

        CreateResponse = new CreateLoadBalancerResponse
        {
            NodeConfigurationId = ValidId,
            Success = true,
            Message = "LoadBalancer created successfully"
        };

        // Update Command and Response
        UpdateCommand = new UpdateLoadBalancerCommand
        {
            Id = ValidId,
            Name = "Updated LoadBalancer",
            IPAddress = ValidIpAddress,
            HostName = "updated-host",
            Port = ValidPort,
            ConnectionType = "https",
            Type = "Monitor Service",
            TypeCategory = "LoadBalancer",
            IsDefault = true,
            IsConnection = true,
            IsNodeStatus = true,
            HealthStatus = "Active"
        };

        UpdateResponse = new UpdateLoadBalancerResponse
        {
            Message = "LoadBalancer updated successfully"
        };

        // Delete Response
        DeleteResponse = new DeleteLoadBalancerResponse
        {
            Message = "LoadBalancer deleted successfully"
        };

        // Test Connection Command and Response
        TestConnectionCommand = new LoadBalancerTestConnectionCommand
        {
            Id = ValidId,
            Name = "Test LoadBalancer",
            IPAddress = ValidIpAddress,
            HostName = "test-host",
            Port = ValidPort,
            ConnectionType = "http",
            Type = "Workflow Service",
            TypeCategory = "LoadBalancer",
            IsNodeStatus = true,
            HealthStatus = "Active",
            IsConnection = true
        };

        // Update Default Command and Response
        UpdateDefaultCommand = new UpdateLoadBalancerDefaultCommand
        {
            Id = ValidId,
            IsDefault = true
        };

        UpdateDefaultResponse = new UpdateLoadBalancerDefaultResponse
        {
            Message = "Default LoadBalancer updated successfully"
        };

        // Update Node Status Command and Response
        UpdateNodeStatusCommand = new UpdateNodeStatusCommand
        {
            Id = ValidId,
            IsNodeStatus = true
        };

        UpdateNodeStatusResponse = new UpdateNodeStatusResponse
        {
            Message = "Node status updated successfully"
        };

        // LoadBalancer List
        LoadBalancerList = new List<LoadBalancerListVm>
        {
            new LoadBalancerListVm
            {
                Id = ValidId,
                Name = "LoadBalancer 1",
                IPAddress = "*************",
                HostName = "host1",
                Port = 8080,
                ConnectionType = "http",
                Type = "Workflow Service",
                TypeCategory = "LoadBalancer",
                IsDefault = false,
                IsConnection = true,
                IsNodeStatus = true,
                HealthStatus = "Active"
            },
            new LoadBalancerListVm
            {
                Id = Guid.NewGuid().ToString(),
                Name = "LoadBalancer 2",
                IPAddress = "*************",
                HostName = "host2",
                Port = 8081,
                ConnectionType = "https",
                Type = "Monitor Service",
                TypeCategory = "LoadBalancer",
                IsDefault = true,
                IsConnection = true,
                IsNodeStatus = true,
                HealthStatus = "Active"
            }
        };

        // LoadBalancer Detail
        LoadBalancerDetail = new LoadBalancerDetailVm
        {
            Id = ValidId,
            Name = "Test LoadBalancer",
            IPAddress = ValidIpAddress,
            HostName = "test-host",
            Port = ValidPort,
            ConnectionType = "http",
            Type = "Workflow Service",
            TypeCategory = "LoadBalancer",
            IsDefault = false,
            IsConnection = true,
            IsNodeStatus = true,
            HealthStatus = "Active"
        };

        LoadBalancerListById = new List<GetLoadBalancerListByIdVm>
        {
            new GetLoadBalancerListByIdVm
            {
                Id = "1",
                Name = "Test Load Balancer 1",
                HostName = "Test hostName1",
                IPAddress = "************",
                Port = 8080,
                IsNodeStatus = true
                // Add other properties as per your model
            },
            new GetLoadBalancerListByIdVm
            {
                Id = "2",
                Name = "Test Load Balancer 2",
                HostName = "Test hostName2",
                IPAddress = "************",
                Port = 8081,
                IsNodeStatus = true
            }
        };
        LoadBalancerTypeList = new List<LoadBalancerTypeVm>
        {
            new LoadBalancerTypeVm()
            {
                Id = "1",
                Name = "Test Load Balancer 1",
                HostName = "Test hostName1",
                IPAddress = "************",
                Port = 8080,
                IsNodeStatus = true
                // Add other properties as per your model
            },
            new LoadBalancerTypeVm()

            {
                Id = "2",
                Name = "Test Load Balancer 2",
                HostName = "Test hostName2",
                IPAddress = "************",
                Port = 8081,
                IsNodeStatus = true
            }
        };
        LoadBalancerNameVmsList = new List<LoadBalancerNameVm>
        {
            new LoadBalancerNameVm()
            {
                Id = "1",
                Name = "Node1",
                Type = "MySQL"
            },
            new LoadBalancerNameVm()

            {
                Id = "2",
                Name = "Node2",
                Type = "MySQL"

            }
        };
    }
}