﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Create;
using ContinuityPatrol.Application.Features.InfraSummary.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.InfraSummaryModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IInfraSummaryService
{
    Task<BaseResponse> CreateAsync(CreateInfraSummaryCommand createInfraSummaryCommand);
    Task<BaseResponse> UpdateAsync(UpdateInfraSummaryCommand updateInfraSummaryCommand);
    Task<BaseResponse> DeleteAsync(string type);
    Task<List<InfraSummaryListVm>> GetInfraSummaries();
}