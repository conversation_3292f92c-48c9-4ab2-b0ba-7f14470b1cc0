using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDb2HaDrMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Db2HaDrMonitorStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.Db2HaDrMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class Db2HaDrMonitorStatusControllerTests
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Db2HaDrMonitorStatusController _controller;
    private readonly Db2HaDrMonitorStatusFixture _db2HaDrMonitorStatusFixture;

    public Db2HaDrMonitorStatusControllerTests()
    {
        _db2HaDrMonitorStatusFixture = new Db2HaDrMonitorStatusFixture();

        var testBuilder = new ControllerTestBuilder<Db2HaDrMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new Db2HaDrMonitorStatusController(),
            out _mediatorMock);
    }

    #region Core CRUD Operations

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDb2HaDrMonitorStatusResponse>(createdResult.Value);
        Assert.Equal("Enterprise DB2 HADR Monitor Status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDb2HaDrMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDb2HaDrMonitorStatusResponse>(okResult.Value);
        Assert.Equal("Enterprise DB2 HADR Monitor Status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var expectedDetail = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<Db2HaDrMonitorStatusDetailQuery>(q => q.Id == statusId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusById(statusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<Db2HaDrMonitorStatusDetailVm>(okResult.Value);
        Assert.Equal("Enterprise DB2 HADR Status Detail", returnedDetail.Type);
        Assert.Equal("Enterprise DB2 Status Detail Database", returnedDetail.InfraObjectName);
        Assert.Equal("Enterprise HADR Status Detail Workflow", returnedDetail.WorkflowName);
    }

    [Fact]
    public async Task GetAllDb2HaDrMonitorStatus_ReturnsOkResult()
    {
        // Arrange
        var statusList = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<Db2HaDrMonitorStatusListQuery>(), default))
            .ReturnsAsync(statusList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(4, returnedList.Count);
        Assert.All(returnedList, status => Assert.Contains("Enterprise", status.Type));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorStatusFixture.GetDb2HaDrMonitorStatusPaginatedListQuery;
        var paginatedResult = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusPaginatedResult;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(6, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise", status.Type));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var type = "Enterprise DB2 HADR Status";
        var expectedDetailList = new List<Db2HaDrMonitorStatusDetailByTypeVm>
        {
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise DB2 HADR Status Type",
                InfraObjectName = "Enterprise DB2 Status Type Database",
                WorkflowName = "Enterprise DB2 Status Workflow",
                ConfiguredRPO = "15",
                DataLagValue = "3"
            },
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise DB2 HADR Status Type",
                InfraObjectName = "Enterprise DB2 Status Type Database 2",
                WorkflowName = "Enterprise DB2 Status Workflow 2",
                ConfiguredRPO = "20",
                DataLagValue = "5"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<Db2HaDrMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("Enterprise DB2 HADR Status Type", detail.Type));
        Assert.All(returnedDetailList, detail => Assert.Contains("Enterprise DB2 Status Type Database", detail.InfraObjectName));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var expectedReferenceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(expectedReferenceId);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedId = Assert.IsType<string>(okResult.Value);
        Assert.Equal(expectedReferenceId, returnedId);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_CallsClearDataCache()
    {
        // Arrange
        var command = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.CreateDb2HaDrMonitorStatus(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_CallsClearDataCache()
    {
        // Arrange
        var command = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        await _controller.UpdateDb2HaDrMonitorStatus(command);

        // Assert
        _mediatorMock.Verify(m => m.Send(command, default), Times.Once);
    }

    #endregion

    

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_HandlesReplicationConfiguration()
    {
        // Arrange
        var replicationCommand = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        replicationCommand.Type = "Enterprise DB2 HADR Replication";
        replicationCommand.Properties = @"{
            ""replicationMode"": ""SYNC"",
            ""primaryDatabase"": ""PROD-DB2-PRIMARY"",
            ""standbyDatabase"": ""PROD-DB2-STANDBY"",
            ""replicationHealth"": ""optimal"",
            ""automaticFailover"": true,
            ""compressionEnabled"": true,
            ""encryptionEnabled"": true,
            ""monitoringFrequency"": ""realtime""
        }";
        replicationCommand.ConfiguredRPO = "0";
        replicationCommand.DataLagValue = "1";
        replicationCommand.Threshold = "5";

        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(replicationCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorStatus(replicationCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDb2HaDrMonitorStatusResponse>(createdResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise DB2 HADR Replication", replicationCommand.Type);
        Assert.Equal("0", replicationCommand.ConfiguredRPO);
        Assert.Contains("SYNC", replicationCommand.Properties);
        Assert.Contains("automaticFailover", replicationCommand.Properties);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_HandlesPerformanceOptimization()
    {
        // Arrange
        var performanceCommand = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        performanceCommand.Type = "Enterprise DB2 HADR Performance";
        performanceCommand.Properties = @"{
            ""performanceMode"": ""optimized"",
            ""bufferPoolSize"": ""2GB"",
            ""logBufferSize"": ""256MB"",
            ""compressionRatio"": ""75%"",
            ""networkBandwidth"": ""10Gbps"",
            ""latencyThreshold"": ""5ms"",
            ""throughputTarget"": ""1000TPS""
        }";
        performanceCommand.ConfiguredRPO = "3";
        performanceCommand.DataLagValue = "1";
        performanceCommand.Threshold = "8";

        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(performanceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDb2HaDrMonitorStatus(performanceCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDb2HaDrMonitorStatusResponse>(okResult.Value);

        Assert.True(returnedResponse.Success);
        Assert.Equal("Enterprise DB2 HADR Performance", performanceCommand.Type);
        Assert.Equal("3", performanceCommand.ConfiguredRPO);
        Assert.Contains("optimized", performanceCommand.Properties);
        Assert.Contains("10Gbps", performanceCommand.Properties);
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_HandlesComplexFiltering()
    {
        // Arrange
        var query = _db2HaDrMonitorStatusFixture.GetDb2HaDrMonitorStatusPaginatedListQuery;
        query.SearchString = "Enterprise Critical";
        query.PageSize = 25;
        query.SortColumn = "ConfiguredRPO";
        query.SortOrder = "DESC";

        var filteredResult = new PaginatedResult<Db2HaDrMonitorStatusListVm>
        {
            Data = Enumerable.Range(1, 25).Select(i => new Db2HaDrMonitorStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = $"Enterprise Critical DB2 HADR {i}",
                InfraObjectName = $"Enterprise Critical Database {i:D2}",
                WorkflowName = $"Enterprise Critical Workflow {i:D2}",
                ConfiguredRPO = (i % 4 == 0 ? "0" : i % 3 == 0 ? "5" : "10"),
                DataLagValue = (i % 2 == 0 ? "1" : "2")
            }).ToList(),
            TotalCount = 25,
            PageSize = 25,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<Db2HaDrMonitorStatusListVm>>(okResult.Value);

        Assert.Equal(25, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise Critical", status.Type));
        Assert.Contains(returnedResult.Data, status => status.ConfiguredRPO == "0");
        Assert.Contains(returnedResult.Data, status => status.ConfiguredRPO == "5");
        Assert.Contains(returnedResult.Data, status => status.ConfiguredRPO == "10");
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByInfraObjectId_HandlesMultipleInfraObjects()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var expectedReferenceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusByInfraObjectIdQuery>(q =>
                q.InfraObjectId == infraObjectId), default))
            .ReturnsAsync(expectedReferenceId);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusByInfraObjectId(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedId = Assert.IsType<string>(okResult.Value);

        Assert.Equal(expectedReferenceId, returnedId);
        Assert.True(Guid.TryParse(returnedId, out _));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusByType_WithMultiSiteConfiguration_ReturnsOkResult()
    {
        // Arrange
        var type = "Enterprise Multi-Site DB2 HADR";
        var expectedDetailList = new List<Db2HaDrMonitorStatusDetailByTypeVm>
        {
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise Multi-Site DB2 HADR",
                InfraObjectName = "Enterprise Primary Site DB2",
                WorkflowName = "Enterprise Multi-Site HADR Workflow",
                ConfiguredRPO = "10",
                DataLagValue = "2"
            },
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise Multi-Site DB2 HADR",
                InfraObjectName = "Enterprise Secondary Site DB2",
                WorkflowName = "Enterprise Multi-Site HADR Workflow",
                ConfiguredRPO = "10",
                DataLagValue = "3"
            },
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "Enterprise Multi-Site DB2 HADR",
                InfraObjectName = "Enterprise Tertiary Site DB2",
                WorkflowName = "Enterprise Multi-Site HADR Workflow",
                ConfiguredRPO = "10",
                DataLagValue = "4"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<Db2HaDrMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.Equal(3, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("Enterprise Multi-Site DB2 HADR", detail.Type));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("Primary Site"));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("Secondary Site"));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("Tertiary Site"));
        Assert.All(returnedDetailList, detail => Assert.Equal("10", detail.ConfiguredRPO));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_WithHealthCheckFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorStatusFixture.GetDb2HaDrMonitorStatusPaginatedListQuery;
        query.SearchString = "Health Check";
        query.PageSize = 20;

        var healthCheckPaginatedResult = new PaginatedResult<Db2HaDrMonitorStatusListVm>
        {
            Data = Enumerable.Range(1, 20).Select(i => new Db2HaDrMonitorStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Health Check DB2 {i:D2}",
                WorkflowName = $"Enterprise Health Check Workflow {i:D2}",
                ConfiguredRPO = (10 + i % 5).ToString(),
                DataLagValue = (i % 3).ToString(),
                Type = i % 3 == 0 ? "HEALTH_CHECK_CRITICAL" : i % 2 == 0 ? "HEALTH_CHECK_WARNING" : "HEALTH_CHECK_OK"
            }).ToList(),
            TotalCount = 20,
            PageSize = 20,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(healthCheckPaginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(20, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise Health Check", status.InfraObjectName));
        Assert.Contains(returnedResult.Data, status => status.Type == "HEALTH_CHECK_CRITICAL");
        Assert.Contains(returnedResult.Data, status => status.Type == "HEALTH_CHECK_WARNING");
        Assert.Contains(returnedResult.Data, status => status.Type == "HEALTH_CHECK_OK");
    }

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_WithComplianceMonitoring_ReturnsCreatedResult()
    {
        // Arrange
        var complianceCommand = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        complianceCommand.InfraObjectName = "Enterprise Compliance DB2 Monitor";
        complianceCommand.WorkflowName = "Enterprise SOX Compliance Workflow";
        complianceCommand.Type = "COMPLIANCE_MONITORING";
        complianceCommand.ConfiguredRPO = "5";
        complianceCommand.DataLagValue = "1";

        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise DB2 HADR Monitor Status created for compliance monitoring!";

        _mediatorMock
            .Setup(m => m.Send(complianceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorStatus(complianceCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDb2HaDrMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("compliance", returnedResponse.Message.ToLower());
        Assert.Equal("Enterprise Compliance DB2 Monitor", complianceCommand.InfraObjectName);
        Assert.Equal("COMPLIANCE_MONITORING", complianceCommand.Type);
        Assert.Contains("SOX", complianceCommand.WorkflowName);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_WithMaintenanceWindow_ReturnsOkResult()
    {
        // Arrange
        var maintenanceCommand = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        maintenanceCommand.InfraObjectName = "Enterprise Maintenance DB2 Server";
        maintenanceCommand.WorkflowName = "Enterprise Scheduled Maintenance Workflow";
        maintenanceCommand.Type = "MAINTENANCE_WINDOW";
        maintenanceCommand.ConfiguredRPO = "30";
        maintenanceCommand.DataLagValue = "25";

        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise DB2 HADR Monitor Status updated for maintenance window!";

        _mediatorMock
            .Setup(m => m.Send(maintenanceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDb2HaDrMonitorStatus(maintenanceCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDb2HaDrMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("maintenance", returnedResponse.Message.ToLower());
        Assert.Equal("Enterprise Maintenance DB2 Server", maintenanceCommand.InfraObjectName);
        Assert.Equal("MAINTENANCE_WINDOW", maintenanceCommand.Type);
        Assert.True(int.Parse(maintenanceCommand.DataLagValue) < int.Parse(maintenanceCommand.ConfiguredRPO));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatus_WithLargeDataset_ReturnsOkResult()
    {
        // Arrange
        var largeStatusList = Enumerable.Range(1, 100).Select(i => new Db2HaDrMonitorStatusListVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise Large Scale DB2 {i:D3}",
            WorkflowName = $"Enterprise Large Scale Workflow {i:D3}",
            ConfiguredRPO = (i % 10 + 5).ToString(),
            DataLagValue = (i % 5).ToString(),
            Type = i % 4 == 0 ? "LARGE_SCALE_CRITICAL" : i % 3 == 0 ? "LARGE_SCALE_WARNING" : "LARGE_SCALE_NORMAL"
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<Db2HaDrMonitorStatusListQuery>(), default))
            .ReturnsAsync(largeStatusList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(100, returnedList.Count);
        Assert.All(returnedList, status => Assert.Contains("Enterprise Large Scale", status.InfraObjectName));
        Assert.Contains(returnedList, status => status.Type == "LARGE_SCALE_CRITICAL");
        Assert.Contains(returnedList, status => status.Type == "LARGE_SCALE_WARNING");
        Assert.Contains(returnedList, status => status.Type == "LARGE_SCALE_NORMAL");
        Assert.All(returnedList, status => Assert.True(int.Parse(status.ConfiguredRPO) >= 5));
    }

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_WithCloudNativeConfiguration_ReturnsCreatedResult()
    {
        // Arrange
        var cloudNativeCommand = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        cloudNativeCommand.Type = "CLOUD_NATIVE_HADR_STATUS";
        cloudNativeCommand.InfraObjectName = "Enterprise Cloud Native DB2 Status";
        cloudNativeCommand.WorkflowName = "Enterprise Cloud Native HADR Status Workflow";
        cloudNativeCommand.Properties = @"{
            ""cloudPlatform"": ""Kubernetes"",
            ""containerOrchestration"": ""OpenShift"",
            ""storageClass"": ""SSD_Premium"",
            ""autoScaling"": true,
            ""loadBalancing"": ""enabled"",
            ""serviceDiscovery"": ""consul"",
            ""healthChecks"": ""comprehensive"",
            ""metricsCollection"": ""prometheus""
        }";
        cloudNativeCommand.ConfiguredRPO = "8";
        cloudNativeCommand.DataLagValue = "5";
        cloudNativeCommand.Threshold = "12";

        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise Cloud Native DB2 HADR Monitor Status created successfully!";

        _mediatorMock
            .Setup(m => m.Send(cloudNativeCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorStatus(cloudNativeCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDb2HaDrMonitorStatusResponse>(createdResult.Value);
        Assert.Equal("Enterprise Cloud Native DB2 HADR Monitor Status created successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_WithAdvancedSecurityConfiguration_ReturnsOkResult()
    {
        // Arrange
        var securityCommand = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        securityCommand.Type = "ADVANCED_SECURITY_HADR_STATUS";
        securityCommand.InfraObjectName = "Enterprise Security Enhanced DB2 Status";
        securityCommand.WorkflowName = "Enterprise Security HADR Status Workflow";
        securityCommand.Properties = @"{
            ""encryptionAtRest"": ""AES-256"",
            ""encryptionInTransit"": ""TLS-1.3"",
            ""keyManagement"": ""HSM"",
            ""accessControl"": ""RBAC"",
            ""auditLogging"": ""comprehensive"",
            ""threatDetection"": ""enabled"",
            ""complianceFramework"": ""SOX_GDPR"",
            ""dataClassification"": ""confidential""
        }";
        securityCommand.ConfiguredRPO = "6";
        securityCommand.DataLagValue = "3";
        securityCommand.Threshold = "10";

        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise Security Enhanced DB2 HADR Monitor Status updated successfully!";

        _mediatorMock
            .Setup(m => m.Send(securityCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDb2HaDrMonitorStatus(securityCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDb2HaDrMonitorStatusResponse>(okResult.Value);
        Assert.Equal("Enterprise Security Enhanced DB2 HADR Monitor Status updated successfully!", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatusById_WithMicroservicesArchitecture_ReturnsOkResult()
    {
        // Arrange
        var statusId = Guid.NewGuid().ToString();
        var microservicesStatus = _db2HaDrMonitorStatusFixture.Db2HaDrMonitorStatusDetailVm;
        microservicesStatus.Id = statusId;
        microservicesStatus.Type = "MICROSERVICES_HADR_STATUS";
        microservicesStatus.InfraObjectName = "Enterprise Microservices DB2 Status";
        microservicesStatus.WorkflowName = "Enterprise Microservices HADR Status Workflow";
        microservicesStatus.ConfiguredRPO = "4";
        microservicesStatus.DataLagValue = "2";
        microservicesStatus.Properties = @"{
            ""serviceArchitecture"": ""microservices"",
            ""apiGateway"": ""Kong"",
            ""serviceRegistry"": ""Eureka"",
            ""circuitBreaker"": ""Hystrix"",
            ""distributedTracing"": ""Jaeger"",
            ""eventSourcing"": ""enabled"",
            ""sagaPattern"": ""orchestration"",
            ""cqrsImplementation"": ""enabled""
        }";

        _mediatorMock
            .Setup(m => m.Send(It.Is<Db2HaDrMonitorStatusDetailQuery>(q => q.Id == statusId), default))
            .ReturnsAsync(microservicesStatus);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusById(statusId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedStatus = Assert.IsType<Db2HaDrMonitorStatusDetailVm>(okResult.Value);
        Assert.Equal(statusId, returnedStatus.Id);
        Assert.Equal("MICROSERVICES_HADR_STATUS", returnedStatus.Type);
        Assert.Equal("Enterprise Microservices DB2 Status", returnedStatus.InfraObjectName);
        Assert.Equal("4", returnedStatus.ConfiguredRPO);
        Assert.Equal("2", returnedStatus.DataLagValue);
        Assert.Contains("microservices", returnedStatus.Properties);
        Assert.Contains("Kong", returnedStatus.Properties);
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_WithDevOpsIntegration_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorStatusFixture.GetDb2HaDrMonitorStatusPaginatedListQuery;
        query.PageSize = 20;
        query.PageNumber = 1;
        query.SearchString = "Enterprise DevOps";

        var devOpsStatusList = Enumerable.Range(1, 20).Select(i => new Db2HaDrMonitorStatusListVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise DevOps DB2 Status {i:D2}",
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowName = $"Enterprise DevOps HADR Status Workflow {i:D2}",
            Type = i % 4 == 0 ? "DEVOPS_CRITICAL_STATUS" :
                   i % 3 == 0 ? "DEVOPS_WARNING_STATUS" :
                   i % 2 == 0 ? "DEVOPS_NORMAL_STATUS" : "DEVOPS_OPTIMIZED_STATUS",
            ConfiguredRPO = (i + 5).ToString(),
            DataLagValue = (i / 3).ToString(),
            Threshold = (i + 10).ToString(),
            Properties = $@"{{
                ""cicdPipeline"": ""Jenkins"",
                ""containerRegistry"": ""Harbor"",
                ""deploymentStrategy"": ""BlueGreen"",
                ""monitoringStack"": ""ELK"",
                ""automationLevel"": ""{80 + i}%""
            }}"
        }).ToList();

        var paginatedResult = new PaginatedResult<Db2HaDrMonitorStatusListVm>
        {
            Data = devOpsStatusList,
            TotalCount = 100,
            PageSize = 20,
            CurrentPage = 1,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(20, returnedResult.Data.Count);
        Assert.Equal(100, returnedResult.TotalCount);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise DevOps", status.InfraObjectName));
        Assert.Contains(returnedResult.Data, status => status.Type == "DEVOPS_CRITICAL_STATUS");
        Assert.Contains(returnedResult.Data, status => status.Type == "DEVOPS_WARNING_STATUS");
        Assert.Contains(returnedResult.Data, status => status.Type == "DEVOPS_NORMAL_STATUS");
    }

   
    [Fact]
    public async Task GetDb2HaDrMonitorStatusByType_WithGlobalReplicationScenario_ReturnsOkResult()
    {
        // Arrange
        var type = "GLOBAL_REPLICATION_MONITORING";
        var expectedDetailList = new List<Db2HaDrMonitorStatusDetailByTypeVm>
        {
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "GLOBAL_REPLICATION_MONITORING",
                InfraObjectName = "Enterprise Global Primary DB2 - US East",
                WorkflowName = "Enterprise Global Replication Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "2"
            },
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "GLOBAL_REPLICATION_MONITORING",
                InfraObjectName = "Enterprise Global Secondary DB2 - EU West",
                WorkflowName = "Enterprise Global Replication Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "3"
            },
            new Db2HaDrMonitorStatusDetailByTypeVm
            {
                Id = Guid.NewGuid().ToString(),
                Type = "GLOBAL_REPLICATION_MONITORING",
                InfraObjectName = "Enterprise Global Tertiary DB2 - APAC",
                WorkflowName = "Enterprise Global Replication Workflow",
                ConfiguredRPO = "5",
                DataLagValue = "4"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDb2HaDrMonitorStatusDetailByTypeQuery>(q => q.Type == type), default))
            .ReturnsAsync(expectedDetailList);

        // Act
        var result = await _controller.GetDb2HaDrMonitorStatusByType(type);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetailList = Assert.IsType<List<Db2HaDrMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.Equal(3, returnedDetailList.Count);
        Assert.All(returnedDetailList, detail => Assert.Equal("GLOBAL_REPLICATION_MONITORING", detail.Type));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("US East"));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("EU West"));
        Assert.Contains(returnedDetailList, detail => detail.InfraObjectName.Contains("APAC"));
        Assert.All(returnedDetailList, detail => Assert.Equal("5", detail.ConfiguredRPO));
        Assert.All(returnedDetailList, detail => Assert.True(int.Parse(detail.DataLagValue) <= 5));
    }

    [Fact]
    public async Task GetPaginatedDb2HaDrMonitorStatus_WithRealTimeMonitoringFilter_ReturnsOkResult()
    {
        // Arrange
        var query = _db2HaDrMonitorStatusFixture.GetDb2HaDrMonitorStatusPaginatedListQuery;
        query.SearchString = "Real Time Monitoring";
        query.PageSize = 30;

        var realTimeMonitoringResult = new PaginatedResult<Db2HaDrMonitorStatusListVm>
        {
            Data = Enumerable.Range(1, 30).Select(i => new Db2HaDrMonitorStatusListVm
            {
                Id = Guid.NewGuid().ToString(),
                InfraObjectName = $"Enterprise Real-Time DB2 Monitor {i:D2}",
                WorkflowName = $"Enterprise Real-Time Monitoring Workflow {i:D2}",
                ConfiguredRPO = (i % 3 + 1).ToString(),
                DataLagValue = (i % 2).ToString(),
                Type = i % 4 == 0 ? "REAL_TIME_SYNC_ACTIVE" :
                       i % 3 == 0 ? "REAL_TIME_MONITORING_HEALTHY" :
                       i % 2 == 0 ? "REAL_TIME_LAG_DETECTED" : "REAL_TIME_SYNC_OPTIMAL"
            }).ToList(),
            TotalCount = 30,
            PageSize = 30,
            Succeeded = true
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(realTimeMonitoringResult);

        // Act
        var result = await _controller.GetPaginatedDb2HaDrMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(30, returnedResult.Data.Count);
        Assert.True(returnedResult.Succeeded);
        Assert.All(returnedResult.Data, status => Assert.Contains("Enterprise Real-Time", status.InfraObjectName));
        Assert.Contains(returnedResult.Data, status => status.Type == "REAL_TIME_SYNC_ACTIVE");
        Assert.Contains(returnedResult.Data, status => status.Type == "REAL_TIME_MONITORING_HEALTHY");
        Assert.Contains(returnedResult.Data, status => status.Type == "REAL_TIME_LAG_DETECTED");
        Assert.Contains(returnedResult.Data, status => status.Type == "REAL_TIME_SYNC_OPTIMAL");
    }

    [Fact]
    public async Task CreateDb2HaDrMonitorStatus_WithDataCenterMigrationScenario_ReturnsCreatedResult()
    {
        // Arrange
        var migrationCommand = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusCommand;
        migrationCommand.InfraObjectName = "Enterprise Migration Source DB2";
        migrationCommand.WorkflowName = "Enterprise Data Center Migration Workflow";
        migrationCommand.Type = "DATA_CENTER_MIGRATION";
        migrationCommand.ConfiguredRPO = "10";
        migrationCommand.DataLagValue = "8";

        var expectedResponse = _db2HaDrMonitorStatusFixture.CreateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise DB2 HADR Monitor Status created for data center migration!";

        _mediatorMock
            .Setup(m => m.Send(migrationCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateDb2HaDrMonitorStatus(migrationCommand);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateDb2HaDrMonitorStatusResponse>(createdResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("migration", returnedResponse.Message.ToLower());
        Assert.Equal("Enterprise Migration Source DB2", migrationCommand.InfraObjectName);
        Assert.Equal("DATA_CENTER_MIGRATION", migrationCommand.Type);
        Assert.Contains("Migration", migrationCommand.WorkflowName);
    }

    [Fact]
    public async Task UpdateDb2HaDrMonitorStatus_WithLoadBalancingOptimization_ReturnsOkResult()
    {
        // Arrange
        var loadBalanceCommand = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusCommand;
        loadBalanceCommand.InfraObjectName = "Enterprise Load Balanced DB2 Cluster";
        loadBalanceCommand.WorkflowName = "Enterprise Load Balancing Optimization Workflow";
        loadBalanceCommand.Type = "LOAD_BALANCING_OPTIMIZATION";
        loadBalanceCommand.ConfiguredRPO = "3";
        loadBalanceCommand.DataLagValue = "1";

        var expectedResponse = _db2HaDrMonitorStatusFixture.UpdateDb2HaDrMonitorStatusResponse;
        expectedResponse.Message = "Enterprise DB2 HADR Monitor Status updated with load balancing optimization!";

        _mediatorMock
            .Setup(m => m.Send(loadBalanceCommand, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateDb2HaDrMonitorStatus(loadBalanceCommand);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateDb2HaDrMonitorStatusResponse>(okResult.Value);
        Assert.True(returnedResponse.Success);
        Assert.Contains("load balancing", returnedResponse.Message.ToLower());
        Assert.Equal("Enterprise Load Balanced DB2 Cluster", loadBalanceCommand.InfraObjectName);
        Assert.Equal("LOAD_BALANCING_OPTIMIZATION", loadBalanceCommand.Type);
        Assert.True(int.Parse(loadBalanceCommand.DataLagValue) < int.Parse(loadBalanceCommand.ConfiguredRPO));
    }

    [Fact]
    public async Task GetDb2HaDrMonitorStatus_WithCloudHybridEnvironment_ReturnsOkResult()
    {
        // Arrange
        var cloudHybridStatusList = Enumerable.Range(1, 75).Select(i => new Db2HaDrMonitorStatusListVm
        {
            Id = Guid.NewGuid().ToString(),
            InfraObjectName = $"Enterprise Cloud-Hybrid DB2 {i:D2}",
            WorkflowName = $"Enterprise Cloud-Hybrid Workflow {i:D2}",
            ConfiguredRPO = (i % 5 + 2).ToString(),
            DataLagValue = (i % 3).ToString(),
            Type = i % 5 == 0 ? "CLOUD_HYBRID_SYNC" :
                   i % 4 == 0 ? "CLOUD_NATIVE_MONITORING" :
                   i % 3 == 0 ? "HYBRID_REPLICATION_ACTIVE" :
                   i % 2 == 0 ? "CLOUD_BACKUP_VERIFIED" : "HYBRID_FAILOVER_READY"
        }).ToList();

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<Db2HaDrMonitorStatusListQuery>(), default))
            .ReturnsAsync(cloudHybridStatusList);

        // Act
        var result = await _controller.GetAllDb2HaDrMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<Db2HaDrMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(75, returnedList.Count);
        Assert.All(returnedList, status => Assert.Contains("Enterprise Cloud-Hybrid", status.InfraObjectName));
        Assert.Contains(returnedList, status => status.Type == "CLOUD_HYBRID_SYNC");
        Assert.Contains(returnedList, status => status.Type == "CLOUD_NATIVE_MONITORING");
        Assert.Contains(returnedList, status => status.Type == "HYBRID_REPLICATION_ACTIVE");
        Assert.Contains(returnedList, status => status.Type == "CLOUD_BACKUP_VERIFIED");
        Assert.Contains(returnedList, status => status.Type == "HYBRID_FAILOVER_READY");
        Assert.All(returnedList, status => Assert.True(int.Parse(status.ConfiguredRPO) >= 2));
    }

}
