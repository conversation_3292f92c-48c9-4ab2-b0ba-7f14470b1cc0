﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BackUpLog.Events.PaginatedView;

public class BackUpLogPaginatedEventHandler : INotificationHandler<BackUpLogPaginatedEvent>
{
    private readonly ILogger<BackUpLogPaginatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BackUpLogPaginatedEventHandler(ILoggedInUserService userService,
        ILogger<BackUpLogPaginatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BackUpLogPaginatedEvent notification, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.View} BackUpData",
            Entity = "BackUpData",
            ActivityType = ActivityType.View.ToString(),
            ActivityDetails = "Backup Data viewed"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation("Backup Data viewed");
    }
}