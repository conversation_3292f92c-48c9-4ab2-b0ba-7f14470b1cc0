﻿using ContinuityPatrol.Application.Features.Form.Events.SaveAs;
using ContinuityPatrol.Shared.Core.Contracts.Version;

namespace ContinuityPatrol.Application.Features.Form.Commands.SaveAs;

public class SaveAsFormCommandHandler : IRequestHandler<SaveAsFormCommand, SaveAsFormResponse>
{
    private readonly IFormRepository _formRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IVersionManager _versionManager;

    public SaveAsFormCommandHandler(IMapper mapper, IFormRepository formRepository, IVersionManager version,
        IPublisher publisher)
    {
        _mapper = mapper;
        _formRepository = formRepository;
        _versionManager = version;
        _publisher = publisher;
    }

    public async Task<SaveAsFormResponse> Handle(SaveAsFormCommand request, CancellationToken cancellationToken)
    {
        var eventToUpdate = await _formRepository.GetByReferenceIdAsync(request.FormId);

        eventToUpdate.Id = 0;

        var version = await _versionManager.GetVersion(request.Version);

        request.Version = version;

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.Form), request.FormId);

        _mapper.Map(request, eventToUpdate, typeof(SaveAsFormCommand), typeof(Domain.Entities.Form));

        await _formRepository.AddAsync(eventToUpdate);

        var response = new SaveAsFormResponse
        {
            Id = eventToUpdate.ReferenceId,
            Message = Message.Create("Form Builder", eventToUpdate.Name)
        };

        await _publisher.Publish(new FormSaveAsEvent { FormName = eventToUpdate.Name }, cancellationToken);

        return response;
    }
}