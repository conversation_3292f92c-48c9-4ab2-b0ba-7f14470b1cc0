﻿using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Company.Commands;

public class CreateCompanyTests : IClassFixture<CompanyFixture>
{
    private readonly CompanyFixture _companyFixture;
    private readonly Mock<ICompanyRepository> _mockCompanyRepository;
    private readonly CreateCompanyCommandHandler _handler;

    public CreateCompanyTests(CompanyFixture companyFixture)
    {
        _companyFixture = companyFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockCompanyRepository = CompanyRepositoryMocks.CreateCompanyRepository(_companyFixture.Companies);

        _handler = new CreateCompanyCommandHandler(_companyFixture.Mapper, _mockCompanyRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_IncreaseCompanyCount_When_CompanyCreated()
    {
        await _handler.Handle(_companyFixture.CreateCompanyCommand, CancellationToken.None);

        var allCategories = await _mockCompanyRepository.Object.ListAllAsync();

        allCategories.Count.ShouldBe(_companyFixture.Companies.Count);
    }

    [Fact]
    public async Task Handle_Return_CreateCompanyResponse_When_CompanyCreated()
    {
        var result = await _handler.Handle(_companyFixture.CreateCompanyCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(CreateCompanyResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Message.ShouldContain(CommonConstants.CreatedSuccessfully);

        result.Success.ShouldBe(true);
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_companyFixture.CreateCompanyCommand, CancellationToken.None);

        _mockCompanyRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.Company>()), Times.Once);
    }
}