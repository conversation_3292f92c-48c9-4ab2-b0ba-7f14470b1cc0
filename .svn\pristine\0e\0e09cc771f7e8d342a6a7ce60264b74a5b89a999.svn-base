﻿using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrixUsers.Events;

public class ApprovalMatrixUsersCreatedEventHandlerTests
{
    private readonly Mock<ILoggedInUserService> _userServiceMock;
    private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
    private readonly Mock<ILogger<ApprovalMatrixUsersCreatedEventHandler>> _loggerMock;
    private readonly ApprovalMatrixUsersCreatedEventHandler _handler;

    public ApprovalMatrixUsersCreatedEventHandlerTests()
    {
        _userServiceMock = new Mock<ILoggedInUserService>();
        _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
        _loggerMock = new Mock<ILogger<ApprovalMatrixUsersCreatedEventHandler>>();

        _handler = new ApprovalMatrixUsersCreatedEventHandler(
            _userServiceMock.Object,
            _loggerMock.Object,
            _userActivityRepositoryMock.Object
        );
    }
    [Fact]
    public async Task Handle_ShouldLogAndSaveActivity()
    {
        // Arrange
        var createdEvent = new ApprovalMatrixUsersCreatedEvent { UserName = "TestUser" };

        _userServiceMock.Setup(x => x.UserId).Returns("user-123");
        _userServiceMock.Setup(x => x.LoginName).Returns("TestLogin");
        _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");
        _userServiceMock.Setup(x => x.RequestedUrl).Returns("/api/approvalmatrix");

        _userActivityRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(new Domain.Entities.UserActivity());

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(
            x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(a =>
                a.UserId == "user-123" &&
                a.LoginName == "TestLogin" &&
                a.RequestUrl == "/api/approvalmatrix" &&
                a.HostAddress == "127.0.0.1" &&
                a.Action == "Create ApprovalMatrixUsers" &&
                a.Entity == "ApprovalMatrixUsers" &&
                a.ActivityType == "Create" &&
                a.ActivityDetails == "ApprovalMatrixUsers 'TestUser' created successfully." &&
                a.CreatedBy == "user-123" &&
                a.LastModifiedBy == "user-123"
            )),
            Times.Once);

        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, t) => o.ToString() == "ApprovalMatrixUsers 'TestUser' created successfully."),
                null,
                (Func<It.IsAnyType, Exception, string>)It.IsAny<object>()),
            Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldGenerateGuid_WhenUserIdIsNull()
    {
        // Arrange
        var createdEvent = new ApprovalMatrixUsersCreatedEvent { UserName = "AutoUser" };

        _userServiceMock.Setup(x => x.UserId).Returns(string.Empty);
        _userServiceMock.Setup(x => x.LoginName).Returns("Unknown");
        _userServiceMock.Setup(x => x.IpAddress).Returns("***********");
        _userServiceMock.Setup(x => x.RequestedUrl).Returns("/api/test");

        Domain.Entities.UserActivity capturedActivity = null!;
        _userActivityRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(a => capturedActivity = a)
            .ReturnsAsync(capturedActivity);

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        capturedActivity.CreatedBy.Should().NotBeNullOrWhiteSpace();
        capturedActivity.LastModifiedBy.Should().NotBeNullOrWhiteSpace();
        Guid.TryParse(capturedActivity.CreatedBy, out _).Should().BeTrue();
    }
    [Fact]
    public async Task Handle_ShouldUseGuid_WhenUserIdIsNull()
    {
        // Arrange
        _userServiceMock.SetupGet(s => s.UserId).Returns((string)null);
        _userServiceMock.SetupGet(s => s.LoginName).Returns("test-login");
        _userServiceMock.SetupGet(s => s.RequestedUrl).Returns("/api/test");
        _userServiceMock.SetupGet(s => s.IpAddress).Returns("127.0.0.1");

        var notification = new ApprovalMatrixUsersCreatedEvent { UserName = "Bob" };

        _userActivityRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(new Domain.Entities.UserActivity
            {
                UserId = "u123",
                LoginName = "ln123",
                RequestUrl = "/url",
                HostAddress = "127.0.0.1",
                Action = "Create ApprovalMatrixUsers",
                Entity = "ApprovalMatrixUsers",
                ActivityType = "Create",
                ActivityDetails = "ApprovalMatrixUsers 'User1' created successfully.",
                CreatedBy = "u123",
                LastModifiedBy = "u123"
            });


        // Act
        await _handler.Handle(notification, CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(r => r.AddAsync(It.Is<Domain.Entities.UserActivity>(
            u => !string.IsNullOrEmpty(u.CreatedBy) &&
                 !string.IsNullOrEmpty(u.LastModifiedBy)
        )), Times.Once);

    }
    [Fact]
    public async Task Handle_ShouldAddUserActivityWithCorrectFields()
    {
        // Arrange
        _userServiceMock.SetupGet(s => s.UserId).Returns("user-123");
        _userServiceMock.SetupGet(s => s.LoginName).Returns("login-123");
        _userServiceMock.SetupGet(s => s.RequestedUrl).Returns("http://localhost");
        _userServiceMock.SetupGet(s => s.IpAddress).Returns("***********");

        var evt = new ApprovalMatrixUsersCreatedEvent { UserName = "Charlie" };

        Domain.Entities.UserActivity captured = null;

        _userActivityRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(a => captured = a)
            .ReturnsAsync(captured);

        // Act
        await _handler.Handle(evt, CancellationToken.None);

        // Assert
        captured.Should().NotBeNull();
        captured.UserId.Should().Be("user-123");
        captured.LoginName.Should().Be("login-123");
        captured.RequestUrl.Should().Be("http://localhost");
        captured.HostAddress.Should().Be("***********");
        captured.Action.Should().Contain("Create");
        captured.Entity.Should().Be("ApprovalMatrixUsers");
        captured.ActivityDetails.Should().Contain("Charlie");
    }
    [Fact]
    public async Task Handle_ShouldSupportMultipleCalls()
    {
        // Arrange
        _userServiceMock.SetupGet(s => s.UserId).Returns("u123");
        _userServiceMock.SetupGet(s => s.LoginName).Returns("ln123");
        _userServiceMock.SetupGet(s => s.RequestedUrl).Returns("/url");
        _userServiceMock.SetupGet(s => s.IpAddress).Returns("127.0.0.1");

        _userActivityRepositoryMock
            .Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(new Domain.Entities.UserActivity
            {
                UserId = "u123",
                LoginName = "ln123",
                RequestUrl = "/url",
                HostAddress = "127.0.0.1",
                Action = "Create ApprovalMatrixUsers",
                Entity = "ApprovalMatrixUsers",
                ActivityType = "Create",
                ActivityDetails = "ApprovalMatrixUsers 'User1' created successfully.",
                CreatedBy = "u123",
                LastModifiedBy = "u123"
            });



        var event1 = new ApprovalMatrixUsersCreatedEvent { UserName = "User1" };
        var event2 = new ApprovalMatrixUsersCreatedEvent { UserName = "User2" };

        // Act
        await _handler.Handle(event1, CancellationToken.None);
        await _handler.Handle(event2, CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(2));
        _loggerMock.Verify(x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Exactly(2));
    }

}
