using ContinuityPatrol.Application.Features.FormHistory.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.FormHistoryModel;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class FormHistoryFixture
{
    public List<FormHistoryDetailVm> FormHistoryDetailVm { get; }
    public List<FormHistoryListVm> FormHistoryListVm { get; }

    public FormHistoryFixture()
    {
        var fixture = new Fixture();

        FormHistoryDetailVm = fixture.Create<List<FormHistoryDetailVm>>();
        FormHistoryListVm = fixture.Create<List<FormHistoryListVm>>();
    }

    public void Dispose()
    {

    }
}
