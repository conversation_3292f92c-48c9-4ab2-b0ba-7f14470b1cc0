﻿namespace ContinuityPatrol.Application.Features.Company.Queries.GetDisplayNameUnique;

public class GetDisplayNameUniqueQueryHandler : IRequestHandler<GetDisplayNameUniqueQuery, bool>
{
    private readonly ICompanyRepository _companyRepository;

    public GetDisplayNameUniqueQueryHandler(ICompanyRepository companyRepository)
    {
        _companyRepository = companyRepository;
    }

    public async Task<bool> Handle(GetDisplayNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _companyRepository.IsDisplayNameExist(request.DisplayName, request.CompanyId);
    }
}