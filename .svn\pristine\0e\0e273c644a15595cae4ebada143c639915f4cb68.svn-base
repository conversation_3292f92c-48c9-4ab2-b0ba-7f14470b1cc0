﻿using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.Create;
using ContinuityPatrol.Application.Features.RoboCopyJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.RoboCopyJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.RoboCopyJob.Commands.update;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class RoboCopyJobService :  IRoboCopyJobService
{
    private readonly IBaseClient _client;
    public RoboCopyJobService(IBaseClient client)
    {
        _client = client;
    }
    public async Task<BaseResponse> CreateRoboCopyJob(CreateRoboCopyJobCommand createRoboCopyJobCommand)
    {
        var request = new RestRequest("api/v6/robocopyjobs", Method.Post);

        request.AddJsonBody(createRoboCopyJobCommand);

        return await _client.Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteRoboCopyJob(string id)
    {
        var request = new RestRequest($"api/v6/robocopyjob/{id}", Method.Delete);

        return await _client.Get<BaseResponse>(request);
    }

    public async  Task<PaginatedResult<RoboCopyJobListVm>> GetPaginatedRoboCopyJobs(GetRoboCopyJobPaginatedQuery query)
    {
        var request = new RestRequest($"api/v6/robocopyjob/paginated-list/{query}");

        return await _client.Get<PaginatedResult<RoboCopyJobListVm>>(request);
    }

    public async Task<RoboCopyJobDetailVm> GetRoboCopyJobById(string id)
    {
        var request = new RestRequest($"api/v6/robocopyjob/{id}");

        return await _client.Get<RoboCopyJobDetailVm>(request);
    }

    public async Task<List<RoboCopyJobListVm>> GetRoboCopyJobs()
    {
        var request = new RestRequest("api/v6/robocopyjob");

        return await _client.Get<List<RoboCopyJobListVm>>(request);
    }

    public async Task<BaseResponse> UpdateRoboCopyJob(UpdateRoboCopyJobCommand updateRoboCopyJobCommand)
    {
        var request = new RestRequest("api/v6/robocopyjob", Method.Put);

        request.AddJsonBody(updateRoboCopyJobCommand);

        return await _client.Put<BaseResponse>(request);
    }
}
