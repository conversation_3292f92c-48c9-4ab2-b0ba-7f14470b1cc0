﻿using ContinuityPatrol.Application.Features.LicenseManager.Events.Decommission;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDecommissionByPoNumber;
using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.LicenseManager.Command.Decommission;

public class
    LicenseDecommissionCommandHandler : IRequestHandler<LicenseDecommissionCommand, LicenseDecommissionResponse>
{
    private readonly IComplianceHistoryRepository _comComplianceHistoryRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILicenseInfoRepository _licenseInfoRepository;
    private readonly ILicenseManagerRepository _licenseManagerRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IReplicationRepository _replicationRepository;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowProfileInfoRepository _workflowProfileInfoRepository;
    private readonly IWorkflowRepository _workflowRepository;

    public LicenseDecommissionCommandHandler(IMapper mapper, IServerRepository serverRepository,
        IDatabaseRepository databaseRepository, IReplicationRepository replicationRepository,
        IInfraObjectRepository infraObjectRepository,
        IWorkflowRepository workflowRepository, ILicenseInfoRepository licenseInfoRepository,
        ILicenseManagerRepository licenseManagerRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IWorkflowProfileInfoRepository workflowProfileInfoRepository,
        IComplianceHistoryRepository comComplianceHistoryRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _replicationRepository = replicationRepository;
        _infraObjectRepository = infraObjectRepository;
        _workflowRepository = workflowRepository;
        _licenseInfoRepository = licenseInfoRepository;
        _licenseManagerRepository = licenseManagerRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _workflowProfileInfoRepository = workflowProfileInfoRepository;
        _comComplianceHistoryRepository = comComplianceHistoryRepository;
        _publisher = publisher;
    }

    public async Task<LicenseDecommissionResponse> Handle(LicenseDecommissionCommand request,
        CancellationToken cancellationToken)
    {
        var decommissionResponse = new DecommissionDetailQueryHandler(_mapper, _serverRepository, _databaseRepository,
            _replicationRepository,
            _infraObjectRepository, _workflowRepository, _licenseInfoRepository, _licenseManagerRepository,
            _workflowInfraObjectRepository, _workflowProfileInfoRepository);

        var handler = await decommissionResponse.Handle(
            new DecommissionDetailQuery
                { EntityId = request.EntityId, LicenseId = request.LicenseId, EntityType = request.EntityType },
            cancellationToken);

        if (handler != null)
        {
            if (request.EntityType.Trim().ToLower().Equals("server"))
            {
                var result = handler as DecommissionDetailVm;

                var server = new List<Domain.Entities.Server>
                {
                    new()
                    {
                        ReferenceId = result.ServerId,
                        Name = result.ServerName
                    }
                };

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowProfileInfo>>(result.DecommissionWorkflowProfiles),
                    _workflowProfileInfoRepository, Modules.WorkflowProfileInfo.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowInfraObject>>(result
                        .DecommissionWorkflowInfraObjectDetailVms),
                    _workflowInfraObjectRepository, Modules.WorkflowInfraObject.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Workflow>>(result.DecommissionWorkflowDetailVms),
                    _workflowRepository, Modules.Workflow.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.InfraObject>>(result.DecommissionInfraObjectDetailVm),
                    _infraObjectRepository, Modules.InfraObject.ToString(), request.LicenseId, request.EntityId);

                var databases = await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Database>>(result.DecommissionDatabaseDetailVm),
                    _databaseRepository, Modules.Database.ToString(), request.LicenseId, request.EntityId);

                if (databases.Count > 0)
                    foreach (var item in databases)
                    {
                        var dbLicenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(item.ReferenceId);

                        await _licenseInfoRepository.DeleteAsync(dbLicenseInfo);
                    }

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Replication>>(result.DecommissionReplicationDetailVm),
                    _replicationRepository, Modules.Replication.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(server, _serverRepository,
                    Modules.Server.ToString(), request.LicenseId, request.EntityId);

                var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(request.EntityId);

                await _licenseInfoRepository.DeleteAsync(licenseInfo);

                await _publisher.Publish(
                    new LicenseDecommissionEvent
                    {
                        PONumber = SecurityHelper.Decrypt(licenseInfo.PONumber), EntityType = request.EntityType,
                        EntityName = result.ServerName
                    }, cancellationToken);
            }

            if (request.EntityType.Trim().ToLower().Equals("database"))
            {
                var result = handler as DecommissionDatabase;

                var databases = new List<Domain.Entities.Database>
                {
                    new()
                    {
                        ReferenceId = result.DatabaseId,
                        Name = result.DatabaseName
                    }
                };

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowProfileInfo>>(result.DecommissionWorkflowProfiles),
                    _workflowProfileInfoRepository, Modules.WorkflowProfileInfo.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowInfraObject>>(result
                        .DecommissionWorkflowInfraObjectDetailVms),
                    _workflowInfraObjectRepository, Modules.WorkflowInfraObject.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Workflow>>(result.DecommissionWorkflowDetailVms),
                    _workflowRepository, Modules.Workflow.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.InfraObject>>(result.DecommissionInfraObjectDetailVm),
                    _infraObjectRepository, Modules.InfraObject.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Replication>>(result.DecommissionReplicationDetailVm),
                    _replicationRepository, Modules.Replication.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    databases, _databaseRepository,
                    Modules.Database.ToString(), request.LicenseId, request.EntityId);

                var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(request.EntityId);

                await _licenseInfoRepository.DeleteAsync(licenseInfo);

                await _publisher.Publish(
                    new LicenseDecommissionEvent
                    {
                        PONumber = SecurityHelper.Decrypt(licenseInfo.PONumber), EntityType = request.EntityType,
                        EntityName = result.DatabaseName
                    }, cancellationToken);
            }

            if (request.EntityType.Trim().ToLower().Equals("replication"))
            {
                var result = handler as DecommissionReplication;

                var replication = new List<Domain.Entities.Replication>
                {
                    new()
                    {
                        ReferenceId = result.ReplicationId,
                        Name = result.ReplicationName
                    }
                };

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowProfileInfo>>(result.DecommissionWorkflowProfiles),
                    _workflowProfileInfoRepository, Modules.WorkflowProfileInfo.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.WorkflowInfraObject>>(result
                        .DecommissionWorkflowInfraObjectDetailVms),
                    _workflowInfraObjectRepository, Modules.WorkflowInfraObject.ToString(), request.LicenseId,
                    request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.Workflow>>(result.DecommissionWorkflowDetailVms),
                    _workflowRepository, Modules.Workflow.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    _mapper.Map<List<Domain.Entities.InfraObject>>(result.DecommissionInfraObjectDetailVm),
                    _infraObjectRepository, Modules.InfraObject.ToString(), request.LicenseId, request.EntityId);

                await RemoveEntitiesAndAddComplianceHistory(
                    replication, _replicationRepository,
                    Modules.Replication.ToString(), request.LicenseId, request.EntityId);

                var licenseInfo = await _licenseInfoRepository.GetLicenseInfoByEntityId(request.EntityId);

                await _licenseInfoRepository.DeleteAsync(licenseInfo);

                await _publisher.Publish(
                    new LicenseDecommissionEvent
                    {
                        PONumber = SecurityHelper.Decrypt(licenseInfo.PONumber), EntityType = request.EntityType,
                        EntityName = result.ReplicationName
                    }, cancellationToken);
            }
        }

        return new LicenseDecommissionResponse
        {
            Id = request.EntityId,
            Message = "Deleted Successfully!."
        };
    }

    private async Task<List<T>> RemoveEntities<T>(IEnumerable<T> entities, IRepository<T> repository)
        where T : BaseEntity
    {
        var result = new List<T>();

        foreach (var entity in entities)
        {
            var entityData = await repository.GetByReferenceIdAsync(entity.ReferenceId);

            if (entityData is not null)
            {
                var removalResult = await repository.DeleteAsync(entityData);

                result.Add(removalResult);
            }
        }

        return result;
    }

    private async Task<List<T>> RemoveEntitiesAndAddComplianceHistory<T>(
        IEnumerable<T> entities,
        IRepository<T> repository,
        string entityType,
        string licenseId,
        string entityId)
        where T : BaseEntity
    {
        var entitiesList = await RemoveEntities(entities, repository);

        if (entitiesList.Count > 0)
        {
            var properties = JsonConvert.SerializeObject(entitiesList);

            await _comComplianceHistoryRepository.AddAsync(new ComplianceHistory
            {
                LicenseId = licenseId,
                EntityId = entityId,
                Entity = entityType,
                Properties = properties
            });
        }

        return entitiesList;
    }
}