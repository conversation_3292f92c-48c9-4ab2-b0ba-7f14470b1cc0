using ContinuityPatrol.Domain.ViewModels.DriftImpactTypeMasterModel;

namespace ContinuityPatrol.Application.Features.DriftImpactTypeMaster.Queries.GetList;

public class
    GetDriftImpactTypeMasterListQueryHandler : IRequestHandler<GetDriftImpactTypeMasterListQuery,
        List<DriftImpactTypeMasterListVm>>
{
    private readonly IDriftImpactTypeMasterRepository _driftImpactTypeMasterRepository;
    private readonly IMapper _mapper;

    public GetDriftImpactTypeMasterListQueryHandler(IMapper mapper,
        IDriftImpactTypeMasterRepository driftImpactTypeMasterRepository)
    {
        _mapper = mapper;
        _driftImpactTypeMasterRepository = driftImpactTypeMasterRepository;
    }

    public async Task<List<DriftImpactTypeMasterListVm>> Handle(GetDriftImpactTypeMasterListQuery request,
        CancellationToken cancellationToken)
    {
        var driftImpactTypeMasters = await _driftImpactTypeMasterRepository.ListAllAsync();

        if (driftImpactTypeMasters.Count <= 0) return new List<DriftImpactTypeMasterListVm>();

        return _mapper.Map<List<DriftImpactTypeMasterListVm>>(driftImpactTypeMasters);
    }
}