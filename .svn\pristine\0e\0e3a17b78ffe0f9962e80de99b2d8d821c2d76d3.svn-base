﻿namespace ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;

public class UpdateWorkflowOperationCommand : IRequest<UpdateWorkflowOperationResponse>
{
    public string Id { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string Status { get; set; }
    public string Description { get; set; }

    // public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string UserName { get; set; }
    public string RunMode { get; set; }
    public bool IsDrCalendar { get; set; }

    public override string ToString()
    {
        return $"Profile Name: {ProfileName}; Id:{Id};";
    }
}