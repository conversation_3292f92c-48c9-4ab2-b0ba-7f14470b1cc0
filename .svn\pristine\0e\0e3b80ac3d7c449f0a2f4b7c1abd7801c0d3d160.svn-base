﻿namespace ContinuityPatrol.Application.Features.MSSQLDBMirroingLogs.Queries.GetByType;
//public class SQLDBMirroringMonitorLogsDetailByTypeQueryHandler : IRequestHandler<SQLDBMirroringMonitorLogsDetailByTypeQuery, List<SQLDBMirroringMonitorLogsDetailByTypeVm>>
//{
//    private readonly IMapper _mapper;
//    private readonly IMSsqlDBMirroringLogRepository _dbmirroringMonitorLogsRepository;

//    public SQLDBMirroringMonitorLogsDetailByTypeQueryHandler(IMapper mapper, IMSsqlDBMirroringLogRepository dbmirroringMonitorLogsRepository)
//    {
//        _mapper = mapper;
//        _dbmirroringMonitorLogsRepository = dbmirroringMonitorLogsRepository;
//    }

//    public async Task<List<SQLDBMirroringMonitorLogsDetailByTypeVm>> Handle(SQLDBMirroringMonitorLogsDetailByTypeQuery request, CancellationToken cancellationToken)
//    {
//        var dbmirroringMonitorLogs = await _dbmirroringMonitorLogsRepository.GetDetailByType(request.Type);

//        return dbmirroringMonitorLogs.Count <= 0 ? new List<SQLDBMirroringMonitorLogsDetailByTypeVm>() : _mapper.Map<List<SQLDBMirroringMonitorLogsDetailByTypeVm>>(dbmirroringMonitorLogs);
//    }
//}

public class SQLDBMirroringMonitorLogsDetailByTypeQueryHandler : IRequestHandler<
    SQLDBMirroringMonitorLogsDetailByTypeQuery, List<SQLDBMirroringMonitorLogsDetailByTypeVm>>
{
    private readonly IMsSqlDbMirroringLogRepository _dbmirroringMonitorLogsRepository;
    private readonly IMapper _mapper;

    public SQLDBMirroringMonitorLogsDetailByTypeQueryHandler(IMapper mapper,
        IMsSqlDbMirroringLogRepository dbmirroringMonitorLogsRepository)
    {
        _mapper = mapper;
        _dbmirroringMonitorLogsRepository = dbmirroringMonitorLogsRepository;
    }

    public async Task<List<SQLDBMirroringMonitorLogsDetailByTypeVm>> Handle(
        SQLDBMirroringMonitorLogsDetailByTypeQuery request, CancellationToken cancellationToken)
    {
        var dbmirroringMonitorLogs = await _dbmirroringMonitorLogsRepository.GetDetailByType(request.Type);

        return dbmirroringMonitorLogs.Count <= 0
            ? new List<SQLDBMirroringMonitorLogsDetailByTypeVm>()
            : _mapper.Map<List<SQLDBMirroringMonitorLogsDetailByTypeVm>>(dbmirroringMonitorLogs);
    }
}