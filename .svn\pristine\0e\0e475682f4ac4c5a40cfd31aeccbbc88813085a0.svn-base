﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class ReplicationMasterFilterSpecification : Specification<ReplicationMaster>
{
    public ReplicationMasterFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("name=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.Name.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }
}