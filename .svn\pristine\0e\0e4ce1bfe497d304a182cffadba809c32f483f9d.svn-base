﻿let mId = sessionStorage.getItem("monitorId");
let monitortype = "MSSQLFullDbEMCSRDF";
let infraObjectId = sessionStorage.getItem("infraobjectId");
setTimeout(() => { MssqlEmcsrdfmonitorStatus(mId, monitortype) }, 250)
setTimeout(() => { monitoringSolution(infraObjectId) }, 250)

$("#backtoITview").on('click', function () {
    window.location.assign('/Dashboard/ITResiliencyView/List');
})

const noDataImage = '<img src="../../img/isomatric/no_data_found.svg" style="margin-top:100px">';

async function MssqlEmcsrdfmonitorStatus(id, type) {
    const url = "/Monitor/Monitoring/GetMonitorServiceStatusByIdAndType";
    let data = {}
    data.monitorId = id;
    data.type = type;
    let monitoringData = await getAysncWithHandler(url, data);

    if (monitoringData && monitoringData !== null) {
        infraData(monitoringData);
        propertiesData(monitoringData);
    } else {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
    }
}
function infraData(value) {
    $("#infraName").text(checkAndReplace(value?.infraObjectName))
        .attr("title", checkAndReplace(value?.infraObjectName));

    $("#modifiedTime").text(checkAndReplace(value?.rpoGeneratedDate))
        .attr("title", checkAndReplace(value?.rpoGeneratedDate));
}
function checkAndReplace(value) {
    return value == null || value === "" ? "NA" : value;
}
function propertiesData(value) {
    if (!value) {
        $("#noDataimg").css('text-align', 'center').html(noDataImage);
        return
    } else {
        const data = JSON.parse(value?.properties);
        //const data = global
        console.log(data, 'data')

        const componentData = data?.MssqlPRComponentMonitoring?.ComponentMonitoring;
        let dbStateIcon = componentData?.DatabaseState?.toLowerCase() === 'online' ? '<i class="text-success cp-online me-1"></i>' : '';
        $('#ServerName').text(checkAndReplace(componentData?.ServerName)).prepend('<i class="cp-server text-primary me-1"></i>')
        $('#DatabaseName').text(checkAndReplace(componentData?.DatabaseName)).prepend('<i class="cp-database text-primary me-1"></i>')
        $('#DatabaseState').text(checkAndReplace(componentData?.DatabaseState)).prepend(dbStateIcon)
        $('#DatabaseRestrictAccess').text(checkAndReplace(componentData?.DatabaseRestrictAccess));

        let customSite = data?.MssqlComponentMonitoring?.length > 1;
        if (customSite) {
            $("#Sitediv").show();
        } else {
            $("#Sitediv").hide();
        }
        $(".siteContainer").empty();
        data?.MssqlComponentMonitoring?.forEach((a, index) => {
            let selectTab = `
            <li class="nav-item siteListChange" id='siteName${index}'>
                <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class='cp-data-replication'></i></span><span class='siteName'>${a?.Type}</span></a>
            </li>
            <li class="nav-item vr"></li>`;
            $(".siteContainer").append(selectTab);
        })
        if (data?.MssqlComponentMonitoring?.length > 0) {
            $("#siteName0 .nav-link").addClass("active");
            displaySiteData(data?.MssqlComponentMonitoring[0]);
        }
        $(document).on('click', '.siteListChange', function () {
            $(".siteListChange .nav-link").removeClass("active");
            $(this).find(".nav-link").addClass("active");
            let siteId = $(this)[0].id
            let getSiteName = $(`#${siteId} .siteName`).text()

            let MonitoringModel = data?.MssqlComponentMonitoring?.find(d => d?.Type === getSiteName);
            if (MonitoringModel) {
                displaySiteData(MonitoringModel, getSiteName);
            }
            bindMonitoringServices(globalMSSQLServerData, getSiteName);
        });
        function displaySiteData(siteData) {
            let obj = {};
            console.log(siteData, 'sitedata')
            for (let key in siteData?.ComponentMonitoring) {
                obj[`DR_` + key] = siteData?.ComponentMonitoring[key];
            }
            let dbStateDr = siteData?.ComponentMonitoring?.DatabaseState?.toLowerCase() === 'online' ? '<i class="text-success cp-online me-1"></i>' : '';
            $('#DR_ServerName').text(checkAndReplace(siteData?.ComponentMonitoring?.ServerName)).prepend('<i class="cp-server text-primary me-1"></i>')
            $('#DR_DatabaseName').text(checkAndReplace(siteData?.ComponentMonitoring?.DatabaseName)).prepend('<i class="cp-database text-primary me-1"></i>')
            $('#DR_DatabaseState').text(checkAndReplace(siteData?.ComponentMonitoring?.DatabaseState)).prepend(dbStateDr)
            $('#DR_DatabaseRestrictAccess').text(checkAndReplace(siteData?.ComponentMonitoring?.DatabaseRestrictAccess));
            
        }
        
        const replicationData = data?.ReplicationMonitoring;
        replicationDetails(replicationData)
    }
}
function replicationDetails(data) {
    $('#replicationData').empty();
    const rows = [
        {
            label: "Replication Type",
            key: "ReplicationType",
            icon: "cp-replication-type"
        },
        {
            label: "Device Group Name",
            key: "DeviceGroupName",
            icon: "cp-group"
        },
        {
            label: "Disk Group Type",
            key: "DiskGroupType",
            icon: "cp-disk-controller"
        },
        {
            label: "Disk Group Symmetrix Id",
            key: "DiskGroupSymmetrixId",
            icon: "cp-degrade-disk"
        },
        {
            label: "Remote Group Symmetrix Id",
            key: "RemoteGroupSymmetrixId",
            icon: "cp-remote-login"
        },
        {
            label: "RDF (RA) Group Number",
            key: "RDFGroupNumber",
            icon: "cp-RDFG"
        },
        {
            label: "Device State",
            key: "DeviceState",
            icon: "cp-state"
        },
        {
            label: "Pending Tracks",
            key: "PendingTask",
            icon: "cp-pending"
        },
        {
            label: "Lag(R2 Behind R1)",
            key: "Lag",
            icon: "cp-time"
        }
    ];

    let repRow = ``;

    rows?.forEach(row => {
        const value = data?.[row.key] ?? 'NA';
        const iconClass = value !== 'NA'
            ? `${row.icon} me-1 fs-6 text-primary`
            : `cp-disable me-1 fs-6 text-danger`;

        repRow += `
            <tr>
                <td class="fw-semibold text-truncate"><i class="text-secondary ${row.icon} me-1"></i>${row.label}</td>
                <td class="text-truncate"><i class="${iconClass}"></i>${value}</td>
            </tr>
        `;
    });
    $('#replicationData').append(repRow)
}