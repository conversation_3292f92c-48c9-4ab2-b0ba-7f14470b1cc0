﻿using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.Company.Commands.Update;
using ContinuityPatrol.Application.Features.Company.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Company.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ICompanyService
{
    Task<List<CompanyNameVm>> GetCompanyNamesOnLogin();
    Task<PaginatedResult<CompanyListVm>> GetPaginatedCompanies(GetCompanyPaginatedListQuery query);
    Task<BaseResponse> CreateAsync(CreateCompanyCommand company);
    Task<BaseResponse> UpdateAsync(UpdateCompanyCommand company);
    Task<BaseResponse> DeleteAsync(string companyId);
    Task<CompanyDetailVm> GetCompanyById(string companyId);
    Task<bool> IsCompanyNameExist(string companyName, string id);
    Task<bool> IsCompanyDisplayNameExist(string displayName, string id);
    Task<BaseResponse> CreateDefaultCompany(CreateDefaultCompanyCommand createDefaultCompanyCommand);
    Task<List<CompanyListVm>> GetCompanies();
}