﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;

public class GetRtoReportQueryHandler : IRequestHandler<GetRtoReportQuery, RTOReports>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;

    public GetRtoReportQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowActionResultRepository workflowActionResultRepository,
        IInfraObjectRepository infraObjectRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IServerRepository serverRepository,
        IDatabaseRepository databaseRepository,
        IBusinessFunctionRepository businessFunctionRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _serverRepository = serverRepository;
        _databaseRepository = databaseRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
    }

    public async Task<RTOReports> Handle(GetRtoReportQuery request, CancellationToken cancellationToken)
    {
        var workflowActionResultList = new List<Domain.Entities.WorkflowActionResult>();
        var workflowActionResultDrDrillList = new List<WorkflowActionResultDrDrillReportVm>();
        WorkflowOperationGroupDrDrillReportVm actionResult;

        var workflowOperation = await _workflowOperationRepository.GetByReferenceIdAsync(request.Id);

        var workflowDrDrillList = _mapper.Map<RtoReportVm>(workflowOperation);

        var workflowOperationGroup =
            await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationId(workflowOperation
                .ReferenceId);

        var uniqueInfraObjectSet = new HashSet<string>();

        foreach (var wfo in workflowOperationGroup)
        {
            var infraObjectDrDrill = await _infraObjectRepository.GetByReferenceIdAsync(wfo.InfraObjectId);
            var workflowInfraObject = await _workflowInfraObjectRepository
                .GetWorkflowInfraObjectByWorkflowIdAsync(wfo.WorkflowId);

            if (uniqueInfraObjectSet.Add(infraObjectDrDrill.ReferenceId))
               // workflowDrDrillList.TotalInfraComponentCount +=
                    //(infraObjectDrDrill.PRServerId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.DRServerId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.NearDRServerId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.PRDatabaseId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.DRDatabaseId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.NearDRDatabaseId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.PRReplicationId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.DRReplicationId.IsNotNullOrWhiteSpace() ? 1 : 0) +
                    //(infraObjectDrDrill.NearDRReplicationId.IsNotNullOrWhiteSpace() ? 1 : 0);

            actionResult = _mapper.Map<WorkflowOperationGroupDrDrillReportVm>(wfo);

            List<string> PrDbSid = new List<string>();
            List<string> DrDbSid = new List<string>();
            List<string> ProductionIpAddress = new List<string>();
            List<string> DrIpAddress = new List<string>();
            List<string> ProductionHostName = new List<string>();
            List<string> DrHostName = new List<string>();
            string prServerName = string.Empty;
            string drServerName = string.Empty;

            if (infraObjectDrDrill.SubType.IsNotNullOrEmpty() && infraObjectDrDrill.SubType.ToLower().Equals("oracle") && infraObjectDrDrill.ReplicationTypeName.IsNotNullOrEmpty() && infraObjectDrDrill.ReplicationTypeName.ToLower().Equals("native replication-oracle-rac"))
            {
                var prServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                var prServerId = prServerProperties.SelectToken("PR.id")?.ToString();
                prServerName = prServerProperties.SelectToken("PR.name")?.ToString();
                var prSplitId = prServerId.Split(",");

                var drServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                var drServerId = drServerProperties.SelectToken("DR.id")?.ToString();
                drServerName = drServerProperties.SelectToken("DR.name")?.ToString();
                var drSplitId = drServerId.Split(",");

                var prDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                var prDatabaseId = prDatabaseProperties.SelectToken("PR.id")?.ToString();
                var prSidId = prDatabaseId.Split(",");

                var drDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                var drDatabaseId = drDatabaseProperties.SelectToken("DR.id")?.ToString();
                var drSidId = drDatabaseId.Split(",");

                foreach (var prSplit in prSplitId)
                {
                    var prServer = await _serverRepository.GetByReferenceIdAsync(prSplit);
                    var prIpAddress = prServer is not null ? GetJsonProperties.GetIpAddressFromProperties(prServer.Properties) : "-";
                    ProductionIpAddress.Add(prIpAddress);
                    var prHostName = prServer is not null ? GetJsonProperties.GetHostNameFromProperties(prServer.Properties) : "-";
                    ProductionHostName.Add(prHostName);
                }
                foreach (var drSplit in drSplitId)
                {
                    var drServer = await _serverRepository.GetByReferenceIdAsync(drSplit);
                    var drIpAddress = drServer is not null ? GetJsonProperties.GetIpAddressFromProperties(drServer.Properties) : "-";
                    DrIpAddress.Add(drIpAddress);
                    var drHostName = drServer is not null ? GetJsonProperties.GetHostNameFromProperties(drServer?.Properties) : "-";
                    DrHostName.Add(drHostName);
                }
                foreach (var prSid in prSidId)
                {
                    var prDatabase = await _databaseRepository.GetByReferenceIdAsync(prSid);
                    var prDbSid = prDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(prDatabase.Properties) : "-";
                    PrDbSid.Add(prDbSid);
                }
                foreach (var drSid in drSidId)
                {
                    var drDatabase = await _databaseRepository.GetByReferenceIdAsync(drSid);
                    var drDbSid = drDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(drDatabase.Properties) : "-";
                    DrDbSid.Add(drDbSid);
                }
            }
            else
            {
                var prDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                var prDatabaseId = prDatabaseProperties.SelectToken("PR.id")?.ToString();
                var prDatabase = await _databaseRepository.GetByReferenceIdAsync(prDatabaseId);
                var prDatabaseSId = prDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(prDatabase.Properties) : "-";
                PrDbSid.Add(prDatabaseSId);

                var drDatabaseProperties = JObject.Parse(infraObjectDrDrill?.DatabaseProperties);
                var drDatabaseId = drDatabaseProperties.SelectToken("DR.id")?.ToString();
                var drDatabase = await _databaseRepository.GetByReferenceIdAsync(drDatabaseId);
                var drDatabaseSId = drDatabase is not null ? GetJsonProperties.GetJsonDatabaseSidValue(drDatabase.Properties) : "-";
                DrDbSid.Add(drDatabaseSId);

                var prServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                var prServerId = prServerProperties.SelectToken("PR.id")?.ToString();
                var prServer = await _serverRepository.GetByReferenceIdAsync(prServerId);
                var prIpAddress = prServer is not null ? GetJsonProperties.GetIpAddressFromProperties(prServer.Properties) : "-";
                ProductionIpAddress.Add(prIpAddress);
                var prHostName = prServer is not null ? GetJsonProperties.GetHostNameFromProperties(prServer.Properties) : "-";
                ProductionHostName.Add(prHostName);

                var drServerProperties = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                var drServerId = drServerProperties.SelectToken("DR.id")?.ToString();
                var drServer = await _serverRepository.GetByReferenceIdAsync(drServerId);
                var drIpAddress = drServer is not null ? GetJsonProperties.GetIpAddressFromProperties(drServer.Properties) : "-";
                DrIpAddress.Add(drIpAddress);
                var drHostName = drServer is not null ? GetJsonProperties.GetHostNameFromProperties(drServer.Properties) : "-";
                DrHostName.Add(drHostName);

                //Server Name, Database Name and Host Name

                var prServerDetails = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                prServerName = prServerDetails.SelectToken("PR.name")?.ToString();
                //infraObjectDrDrill.PRServerName = prServerName;

                var drServerDetails = JObject.Parse(infraObjectDrDrill?.ServerProperties);
                drServerName = drServerDetails.SelectToken("DR.name")?.ToString();
               // infraObjectDrDrill.DRServerName = drServerName;
            }
            if (workflowInfraObject != null && workflowInfraObject.ActionType.Trim().ToLower().Equals("switchback"))
            {
                workflowDrDrillList.WorkflowId = workflowInfraObject.WorkflowId;
                workflowDrDrillList.WorkflowName = workflowInfraObject.WorkflowName;
                workflowDrDrillList.WorkflowActionType = workflowInfraObject.ActionType;
                workflowDrDrillList.InfraObjectId = infraObjectDrDrill.ReferenceId;
                workflowDrDrillList.InfraObjectName = infraObjectDrDrill.Name;
                workflowDrDrillList.PRServerName = drServerName;
                workflowDrDrillList.DRServerName = prServerName;
                //workflowDrDrillList.PRDatabaseName = infraObjectDrDrill.DRDatabaseName;
                //workflowDrDrillList.DRDatabaseName = infraObjectDrDrill.PRDatabaseName;
                workflowDrDrillList.PRDbSid = DrDbSid;
                workflowDrDrillList.DRDbSid = PrDbSid;
                workflowDrDrillList.ProductionIpAddress = DrIpAddress;
                workflowDrDrillList.DrIpAddress = ProductionIpAddress;
                workflowDrDrillList.ProductionHostName = DrHostName;
                workflowDrDrillList.DrHostName = ProductionHostName;
            }

            else
            {
                workflowDrDrillList.WorkflowId = workflowInfraObject?.WorkflowId;
                workflowDrDrillList.WorkflowName = workflowInfraObject?.WorkflowName;
                workflowDrDrillList.WorkflowActionType = workflowInfraObject?.ActionType;
                workflowDrDrillList.InfraObjectId = infraObjectDrDrill?.ReferenceId;
                workflowDrDrillList.InfraObjectName = infraObjectDrDrill?.Name;
                workflowDrDrillList.PRServerName = prServerName;
                workflowDrDrillList.DRServerName = drServerName;
                //workflowDrDrillList.PRDatabaseName = infraObjectDrDrill?.PRDatabaseName;
                //workflowDrDrillList.DRDatabaseName = infraObjectDrDrill?.DRDatabaseName;
                workflowDrDrillList.PRDbSid = PrDbSid;
                workflowDrDrillList.DRDbSid = DrDbSid;
                workflowDrDrillList.ProductionIpAddress = ProductionIpAddress;
                workflowDrDrillList.DrIpAddress = DrIpAddress;
                workflowDrDrillList.ProductionHostName = ProductionHostName;
                workflowDrDrillList.DrHostName = DrHostName;
            }

            //workflowDrDrillList.ActualRTO = GetTotalTime(actionResult.StartTime, actionResult.EndTime);
            var workflowActionResult = await _workflowActionResultRepository
                .GetWorkflowActionResultByWorkflowOperationId(wfo.WorkflowOperationId);
            workflowActionResultList.AddRange(workflowActionResult);
            var businessFunction = await _businessFunctionRepository
                .GetByReferenceIdAsync(infraObjectDrDrill.BusinessFunctionId);
            workflowDrDrillList.ConfiguredRTO = businessFunction.ConfiguredRTO;
        }

        workflowActionResultList = workflowActionResultList.Distinct().ToList();

        workflowActionResultList.ForEach(wfa =>
        {
            var actionResultDrDrills = _mapper.Map<WorkflowActionResultDrDrillReportVm>(wfa);
            actionResultDrDrills.TotalTime = GetTotalTime(actionResultDrDrills.StartTime, actionResultDrDrills.EndTime);
            workflowActionResultDrDrillList.Add(actionResultDrDrills);
        });

        if (workflowActionResultList.Count > 0)
        {
            var drillStartTime = workflowActionResultDrDrillList.FirstOrDefault()?.StartTime;
            var drillEndTime = workflowActionResultDrDrillList.LastOrDefault()?.EndTime;

            workflowDrDrillList.StartTime = drillStartTime;
            workflowDrDrillList.EndTime = drillEndTime;
            workflowDrDrillList.ActualRTO = GetTotalTime(drillStartTime, drillEndTime);
            workflowDrDrillList.LastDrillStatus = workflowActionResultDrDrillList.LastOrDefault()?.Status;

            if (workflowDrDrillList.LastDrillStatus.Equals("aborted"))
                workflowDrDrillList.ErrorMessage = workflowActionResultDrDrillList.LastOrDefault()?.Message;
        }

        workflowDrDrillList.WorkflowActionResultRtoReportVms =
            _mapper.Map<List<WorkflowActionResultDrDrillReportVm>>(workflowActionResultDrDrillList);

        workflowDrDrillList.TotalProfilesExecutedCount = 1;

        workflowDrDrillList.TotalWorkflowExecutedCount = workflowOperationGroup.Count;


        await _publisher.Publish(
            new ReportViewedEvent { ReportName = "RTO Report", ActivityType = ActivityType.View.ToString() },
            CancellationToken.None);

        return new RTOReports
        {
            ReportGeneratedBy = _loggedInUserService.LoginName,
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            RtoReportVm = workflowDrDrillList
        };
    }

    private static TimeSpan GetTotalTime(string startTime, string endTime)
    {
        var start = DateTime.Parse(startTime);
        var end = DateTime.Parse(endTime);
        var ts = end - start;
        return ts;
    }
}