﻿using ContinuityPatrol.Application.Features.BulkImportOperation.Queries.GetRunningList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Domain.ViewModels.BulkImportActionResultModel;
using Moq;

namespace ContinuityPatrol.Application.UnitTests.Features.BulkImportOperation.Queries;

public class GetBulkImportOperationRunningListQueryTests : IClassFixture<BulkImportOperationFixture>
{
    private readonly BulkImportOperationFixture _bulkImportOperationFixture;
    private readonly Mock<IBulkImportOperationRepository> _mockBulkImportOperationRepository;
    private readonly Mock<IBulkImportOperationGroupRepository> _mockBulkImportOperationGroupRepository;
    private readonly Mock<IBulkImportActionResultRepository> _mockBulkImportActionResultRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetBulkImportOperationRunningListQueryHandler _handler;

    public GetBulkImportOperationRunningListQueryTests(BulkImportOperationFixture bulkImportOperationFixture)
    {
        _bulkImportOperationFixture = bulkImportOperationFixture;

        _mockBulkImportOperationRepository = BulkImportOperationRepositoryMocks.CreateQueryBulkImportOperationRepository(_bulkImportOperationFixture.BulkImportOperations);
        _mockBulkImportOperationGroupRepository = BulkImportOperationRepositoryMocks.CreateBulkImportOperationGroupRepository(new List<Domain.Entities.BulkImportOperationGroup>());
        _mockBulkImportActionResultRepository = BulkImportOperationRepositoryMocks.CreateBulkImportActionResultRepository(new List<Domain.Entities.BulkImportActionResult>());
        _mockMapper = new Mock<IMapper>();

        // Setup mapper for running list
        _mockMapper.Setup(m => m.Map<List<BulkImportOperationRunningListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()))
            .Returns((List<Domain.Entities.BulkImportOperation> entities) =>
            {
                if (entities == null) return new List<BulkImportOperationRunningListVm>();

                return entities.Select(entity => new BulkImportOperationRunningListVm
                {
                    Id = entity?.ReferenceId ?? string.Empty,
                    CompanyId = entity?.CompanyId ?? string.Empty,
                    UserName = entity?.UserName ?? string.Empty,
                    Description = entity?.Description ?? string.Empty,
                    Status = entity?.Status ?? string.Empty,
                    StartTime = entity?.StartTime ?? DateTime.MinValue,
                    EndTime = entity?.EndTime ?? DateTime.MinValue,
                    BulkImportOperationGroup = new List<BulkImportOperationGroupList>()
                }).ToList();
            });

        // Setup mapper for operation groups
        _mockMapper.Setup(m => m.Map<List<BulkImportOperationGroupList>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()))
            .Returns((List<Domain.Entities.BulkImportOperationGroup> entities) =>
            {
                if (entities == null) return new List<BulkImportOperationGroupList>();

                return entities.Select(entity => new BulkImportOperationGroupList
                {
                    Id = entity?.ReferenceId ?? string.Empty,
                    BulkImportOperationId = entity?.BulkImportOperationId ?? string.Empty,
                    InfraObjectName = entity?.InfraObjectName ?? string.Empty,
                    CompanyId = entity?.CompanyId ?? string.Empty,
                    ProgressStatus = entity?.ProgressStatus ?? string.Empty,
                    Status = entity?.Status ?? string.Empty,
                    ErrorMessage = entity?.ErrorMessage ?? string.Empty,
                    ConditionalOperation = entity?.ConditionalOperation ?? 0,
                    NodeId = entity?.NodeId ?? string.Empty,
                    BulkImportActionResultListVms = new List<BulkImportActionResultListVm>()
                }).ToList();
            });

        // Setup mapper for action results
        _mockMapper.Setup(m => m.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()))
            .Returns((List<Domain.Entities.BulkImportActionResult> entities) =>
            {
                if (entities == null) return new List<BulkImportActionResultListVm>();

                return entities.Select(entity => new BulkImportActionResultListVm
                {
                    Id = entity?.ReferenceId ?? string.Empty,
                    BulkImportOperationGroupId = entity?.BulkImportOperationGroupId ?? string.Empty
                }).ToList();
            });

        _handler = new GetBulkImportOperationRunningListQueryHandler(
            _mockBulkImportOperationRepository.Object, _mockMapper.Object,
            _mockBulkImportOperationGroupRepository.Object,
            _mockBulkImportActionResultRepository.Object
            );
    }

    [Fact]
    public async Task Handle_Return_BulkImportOperationRunningListVm_When_RunningOperationsExist()
    {
        // Arrange
        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationRunningListVm>));
        result.Count.ShouldBeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public async Task Handle_Call_GetRunningStatus_OnlyOnce()
    {
        var query = new GetBulkImportOperationRunningListQuery();

        //_mockBulkImportOperationRepository
        //    .Setup(x => x.GetRunningStatus())
        //    .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>()); // ✅ prevent ArgumentNullException

        _mockBulkImportOperationRepository
     .Setup(x => x.GetRunningStatus())
     .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>
     {
        new Domain.Entities.BulkImportOperation
        {
            ReferenceId = Guid.NewGuid().ToString(),
            UserName = "test-user",
            CompanyId = Guid.NewGuid().ToString(),
            // add any other required fields here
        }
     });

        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetRunningStatus(), Times.Once);
    }

    [Fact]
    public async Task Handle_Call_MapperMap_ForOperations()
    {
        // Arrange
        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationRunningListVm>>(It.IsAny<List<Domain.Entities.BulkImportOperation>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_GetOperationGroups_When_OperationsExist()
    {
        // Arrange
        var runningOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation { ReferenceId = "op1", Status = "Running" },
            new Domain.Entities.BulkImportOperation { ReferenceId = "op2", Status = "Pending" }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(runningOperations);

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationGroupRepository.Verify(x => x.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_GetActionResults_When_OperationGroupsExist()
    {
        // Arrange
        var runningOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation { ReferenceId = "op1", Status = "Running" }
        };

        var operationGroups = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup { ReferenceId = "group1", BulkImportOperationId = "op1" }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(runningOperations);

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync(operationGroups);

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
       // _mockBulkImportActionResultRepository.Verify(x => x.GetBulkImportActionResultByBulkImportOperationGroupIds(It.IsAny<List<string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_MapRunningOperations_WithCorrectProperties()
    {
        // Arrange
        var runningOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation 
            { 
                ReferenceId = "op1", 
                CompanyId = "TestCompany",
                UserName = "TestUser",
                Description = "Test running operation",
                Status = "Running",
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now
            }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(runningOperations);

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        
        var firstItem = result.First();
        firstItem.Id.ShouldBe("op1");
        firstItem.CompanyId.ShouldBe("TestCompany");
        firstItem.UserName.ShouldBe("TestUser");
        firstItem.Description.ShouldBe("Test running operation");
        firstItem.Status.ShouldBe("Running");
    }

    [Fact]
    public async Task Handle_ReturnCorrectListType_When_MappingSuccessful()
    {
        // Arrange
        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeAssignableTo<List<BulkImportOperationRunningListVm>>();
        result.GetType().ShouldBe(typeof(List<BulkImportOperationRunningListVm>));
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRunningOperations()
    {
        // Arrange
        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(new List<Domain.Entities.BulkImportOperation>());

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldBeOfType(typeof(List<BulkImportOperationRunningListVm>));
        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_AssignOperationGroupsToOperations_When_DataExists()
    {
        // Arrange
        var runningOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation { ReferenceId = "op1", Status = "Running" }
        };

        var operationGroups = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup 
            { 
                ReferenceId = "group1", 
                BulkImportOperationId = "op1",
                InfraObjectName = "TestInfra"
            }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(runningOperations);

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync(operationGroups);

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        result.First().BulkImportOperationGroup.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_FilterRunningAndPendingOperations_When_GetRunningStatusCalled()
    {
        // Arrange
        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockBulkImportOperationRepository.Verify(x => x.GetRunningStatus(), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleComplexOperationStructure_When_MultipleGroupsAndResults()
    {
        // Arrange
        var runningOperations = new List<Domain.Entities.BulkImportOperation>
        {
            new Domain.Entities.BulkImportOperation { ReferenceId = "op1", Status = "Running" }
        };

        var operationGroups = new List<Domain.Entities.BulkImportOperationGroup>
        {
            new Domain.Entities.BulkImportOperationGroup { ReferenceId = "group1", BulkImportOperationId = "op1" },
            new Domain.Entities.BulkImportOperationGroup { ReferenceId = "group2", BulkImportOperationId = "op1" }
        };

        var actionResults = new List<Domain.Entities.BulkImportActionResult>
        {
            new Domain.Entities.BulkImportActionResult { ReferenceId = "result1", BulkImportOperationGroupId = "group1" },
            new Domain.Entities.BulkImportActionResult { ReferenceId = "result2", BulkImportOperationGroupId = "group2" }
        };

        _mockBulkImportOperationRepository.Setup(x => x.GetRunningStatus())
            .ReturnsAsync(runningOperations);

        _mockBulkImportOperationGroupRepository.Setup(x => x.GetBulkImportOperationGroupByBulkImportOperationIds(It.IsAny<List<string>>()))
            .ReturnsAsync(operationGroups);

        //_mockBulkImportActionResultRepository.Setup(x => x.GetBulkImportActionResultByBulkImportOperationGroupIds(It.IsAny<List<string>>()))
        //    .ReturnsAsync(actionResults);

        var query = new GetBulkImportOperationRunningListQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(1);
        _mockMapper.Verify(x => x.Map<List<BulkImportOperationGroupList>>(It.IsAny<List<Domain.Entities.BulkImportOperationGroup>>()), Times.Once);
        _mockMapper.Verify(x => x.Map<List<BulkImportActionResultListVm>>(It.IsAny<List<Domain.Entities.BulkImportActionResult>>()), Times.Once);
    }
}
