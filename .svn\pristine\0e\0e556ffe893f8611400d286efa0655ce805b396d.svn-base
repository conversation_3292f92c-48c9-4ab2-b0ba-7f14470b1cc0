﻿namespace ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndType;

public class GetByReplicationTypeIdAndTypeQuery : IRequest<List<GetByReplicationTypeIdAndTypeVm>>
{
    public string ReplicationTypeId { get; set; }
    public string ActionType { get; set; }
    public string Type { get; set; }
    public string EntityType { get; set; }
    public string TemplateType { get; set; }
}