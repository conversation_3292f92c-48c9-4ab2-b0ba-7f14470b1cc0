using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ApprovalMatrixApprovalFixture : IDisposable
{
    public List<ApprovalMatrixApproval> ApprovalMatrixApprovalPaginationList { get; set; }
    public List<ApprovalMatrixApproval> ApprovalMatrixApprovalList { get; set; }
    public ApprovalMatrixApproval ApprovalMatrixApprovalDto { get; set; }

    public ApplicationDbContext DbContext { get; private set; }

    public ApprovalMatrixApprovalFixture()
    {
        var fixture = new Fixture();

        ApprovalMatrixApprovalList = fixture.Create<List<ApprovalMatrixApproval>>();

        ApprovalMatrixApprovalPaginationList = fixture.CreateMany<ApprovalMatrixApproval>(20).ToList();

        ApprovalMatrixApprovalPaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixApprovalPaginationList.ForEach(x => x.IsActive = true);

        ApprovalMatrixApprovalList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        ApprovalMatrixApprovalList.ForEach(x => x.IsActive = true);

        ApprovalMatrixApprovalDto = fixture.Create<ApprovalMatrixApproval>();
        ApprovalMatrixApprovalDto.ReferenceId = Guid.NewGuid().ToString();
        ApprovalMatrixApprovalDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
