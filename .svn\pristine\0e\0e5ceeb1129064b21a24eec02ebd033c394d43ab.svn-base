﻿using ContinuityPatrol.Application.Features.ComponentType.Commands.Create;
using ContinuityPatrol.Application.Features.ComponentType.Commands.Update;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoComponentTypeDataAttribute : AutoDataAttribute
{
    public AutoComponentTypeDataAttribute()
        : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateComponentTypeCommand>(p => p.ComponentName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateComponentTypeCommand>(p => p.ComponentName, 10));
            fixture.Customize<UpdateComponentTypeCommand>(c => c.With(b => b.Id, 0.ToString));

            return fixture;
        })
    {

    }

}