﻿using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetNameUnique;
using ContinuityPatrol.Domain.Entities;
using Moq;
using Shouldly;
using Xunit;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberComponentMapping.Queries
{
    public class GetCyberComponentMappingNameUniqueQueryHandlerTests
    {
        private readonly Mock<ICyberComponentMappingRepository> _mockRepository;
        private readonly GetCyberComponentMappingNameUniqueQueryHandler _handler;

        public GetCyberComponentMappingNameUniqueQueryHandlerTests()
        {
            _mockRepository = new Mock<ICyberComponentMappingRepository>();
            _handler = new GetCyberComponentMappingNameUniqueQueryHandler(_mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_True_When_Name_Exists")]
        public async Task Handle_Should_Return_True_When_Name_Exists()
        {
            // Arrange
            var query = new GetCyberComponentMappingNameUniqueQuery
            {
                Name = "ExistingName",
                Id = "id-123"
            };

            _mockRepository.Setup(r => r.IsNameExist(query.Name, query.Id)).ReturnsAsync(true);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.ShouldBeTrue();
            _mockRepository.Verify(r => r.IsNameExist("ExistingName", "id-123"), Times.Once);
        }

        [Fact(DisplayName = "Handle_Should_Return_False_When_Name_DoesNotExist")]
        public async Task Handle_Should_Return_False_When_Name_DoesNotExist()
        {
            // Arrange
            var query = new GetCyberComponentMappingNameUniqueQuery
            {
                Name = "UniqueName",
                Id = "id-456"
            };

            _mockRepository.Setup(r => r.IsNameExist(query.Name, query.Id)).ReturnsAsync(false);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.ShouldBeFalse();
            _mockRepository.Verify(r => r.IsNameExist("UniqueName", "id-456"), Times.Once);
        }
    }
}
