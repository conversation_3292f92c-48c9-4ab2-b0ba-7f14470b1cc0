﻿namespace ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Delete;

public class
    DeleteStateMonitorLogCommandHandler : IRequestHandler<DeleteStateMonitorLogCommand, DeleteStateMonitorLogResponse>
{
    private readonly IStateMonitorLogRepository _stateMonitorLogRepository;

    public DeleteStateMonitorLogCommandHandler(IStateMonitorLogRepository stateMonitorLogRepository)
    {
        _stateMonitorLogRepository = stateMonitorLogRepository;
    }

    public async Task<DeleteStateMonitorLogResponse> Handle(DeleteStateMonitorLogCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _stateMonitorLogRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.NullOrDeactive(eventToDelete, nameof(Domain.Entities.StateMonitorLog),
            new NotFoundException(nameof(Domain.Entities.StateMonitorLog), request.Id));

        eventToDelete.IsActive = false;

        await _stateMonitorLogRepository.UpdateAsync(eventToDelete);

        var response = new DeleteStateMonitorLogResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.StateMonitorLog), eventToDelete.ReferenceId),

            IsActive = eventToDelete.IsActive
        };
        return response;
    }
}