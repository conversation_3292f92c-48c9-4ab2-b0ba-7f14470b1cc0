﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Helper;

public  class InfraComponentPropertyHelper
{
    public static List<InfraComponentProperty> GetPropertyDetails(string json)
    {
        var parsedJson = JsonConvert.DeserializeObject<Dictionary<string, ObjectDetails>>(json);

        var serverDetailsList = new List<InfraComponentProperty>();

        foreach (var key in parsedJson.Keys)
        {
            var details = parsedJson[key];
            var serverDetails = new InfraComponentProperty
            {
                Key = key,
                Id = details.Id,
                Name = details.Name,
                Type = details.Type,
                SiteId = details.SiteId??"Na"
            };
            serverDetailsList.Add(serverDetails);
        }

        return serverDetailsList;
    }
}
public class ObjectDetails
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
    public string SiteId { get; set; }
}

public class InfraComponentProperty
{
    public string Key { get; set; }
    public string Id { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
    public string SiteId { get; set; }
}





