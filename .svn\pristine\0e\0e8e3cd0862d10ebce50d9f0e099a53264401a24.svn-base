﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.GlobalSetting.Commands;

public class UpdateGlobalSettingTests : IClassFixture<GlobalSettingFixture>
{
    private readonly GlobalSettingFixture _globalSettingFixture;
    private readonly Mock<IGlobalSettingRepository> _mockGlobalSettingRepository;
    private readonly UpdateGlobalSettingCommandHandler _handler;

    public UpdateGlobalSettingTests(GlobalSettingFixture globalSettingFixture)
    {
        _globalSettingFixture = globalSettingFixture;

        _mockGlobalSettingRepository = GlobalSettingRepositoryMocks.UpdateGlobalSettingRepository(_globalSettingFixture.GlobalSettings);

        _handler = new UpdateGlobalSettingCommandHandler(_globalSettingFixture.Mapper, _mockGlobalSettingRepository.Object);
    }

    [Fact]
    public async Task Handle_ValidGlobalSetting_UpdateToGlobalSettingsRepo()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = _globalSettingFixture.GlobalSettings[0].ReferenceId;

        var result = await _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        var globalSetting = await _mockGlobalSettingRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingKey, globalSetting.GlobalSettingKey);
    }

    [Fact]
    public async Task Handle_Return_UpdateGlobalSettingResponse_When_GlobalSettingUpdated()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = _globalSettingFixture.GlobalSettings[0].ReferenceId;

        var result = await _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateGlobalSettingResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_globalSettingFixture.UpdateGlobalSettingCommand.Id);

        result.Success.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_EnabledMessage_When_GlobalSettingValueIsTrue()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = _globalSettingFixture.GlobalSettings[0].ReferenceId;
        _globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingValue = "true";

        var result = await _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        result.Message.ShouldContain("Enabled Successfully");
        result.Message.ShouldContain(_globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingKey);
    }

    [Fact]
    public async Task Handle_Return_DisabledMessage_When_GlobalSettingValueIsNotTrue()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = _globalSettingFixture.GlobalSettings[0].ReferenceId;
        _globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingValue = "false";

        var result = await _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        result.Message.ShouldContain("Disabled Successfully");
        result.Message.ShouldContain(_globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingKey);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidGlobalSettingId()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var handler = new UpdateGlobalSettingCommandHandler(_globalSettingFixture.Mapper, _mockGlobalSettingRepository.Object);

        _globalSettingFixture.UpdateGlobalSettingCommand.Id = _globalSettingFixture.GlobalSettings[0].ReferenceId;

        await handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        _mockGlobalSettingRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockGlobalSettingRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.GlobalSetting>()), Times.Once);
    }

    [Fact]
    public async Task Handle_UpdateGlobalSettingProperties_When_ValidCommand()
    {
        // Arrange
        var originalGlobalSetting = _globalSettingFixture.GlobalSettings[0];
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = originalGlobalSetting.ReferenceId;
        _globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingKey = "UpdatedKey";
        _globalSettingFixture.UpdateGlobalSettingCommand.GlobalSettingValue = "UpdatedValue";
        _globalSettingFixture.UpdateGlobalSettingCommand.LoginUserId = "UpdatedUser";

        // Act
        await _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None);

        // Assert
        var updatedGlobalSetting = await _mockGlobalSettingRepository.Object.GetByReferenceIdAsync(originalGlobalSetting.ReferenceId);
        updatedGlobalSetting.GlobalSettingKey.ShouldBe("UpdatedKey");
        updatedGlobalSetting.GlobalSettingValue.ShouldBe("UpdatedValue");
        updatedGlobalSetting.LoginUserId.ShouldBe("UpdatedUser");
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_NullId()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = null;

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_EmptyId()
    {
        _globalSettingFixture.UpdateGlobalSettingCommand.Id = string.Empty;

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_globalSettingFixture.UpdateGlobalSettingCommand, CancellationToken.None));
    }
}