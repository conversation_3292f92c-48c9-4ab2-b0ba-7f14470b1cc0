﻿using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Create;
using ContinuityPatrol.Application.Features.DataSetColumns.Commands.Update;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetColumnNames;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DataSetColumns.Queries.GetNames;
using ContinuityPatrol.Domain.ViewModels.DataSetColumnsModel;

namespace ContinuityPatrol.Application.Mappings;

public class DataSetColumnsProfile : Profile
{
    public DataSetColumnsProfile()
    {
        CreateMap<DataSetColumns, DataSetColumnsDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSetColumns, DataSetColumnsListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSetColumns, DataSetColumnsNameVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<DataSetColumns, DataSetColumnsColumnNameVm>().ReverseMap();

        CreateMap<DataSetColumns, CreateDataSetColumnsCommand>().ReverseMap();
        CreateMap<UpdateDataSetColumnsCommand, DataSetColumns>().ForMember(x => x.Id, y => y.Ignore());


        CreateMap<ColumnInfo, DataSetColumnsColumnNameVm>().ReverseMap();
    }
}