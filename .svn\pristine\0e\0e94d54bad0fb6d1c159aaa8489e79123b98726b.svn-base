﻿using ContinuityPatrol.Application.Features.UserRole.Queries.GetRoleUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.UserRole.Queries;

public class GetUserRoleNameUniqueQueryHandlerTests : IClassFixture<UserRoleFixture>
{
    private readonly UserRoleFixture _userRoleFixture;

    private Mock<IUserRoleRepository> _mockUserRoleRepository;

    private readonly GetUserRoleNameUniqueQueryHandler _handler;

    public GetUserRoleNameUniqueQueryHandlerTests(UserRoleFixture userRoleFixture)
    {
        _userRoleFixture = userRoleFixture;

        _mockUserRoleRepository = UserRoleRepositoryMocks.GetUserRoleNameUniqueRepository(_userRoleFixture.UserRoles);

        _handler = new GetUserRoleNameUniqueQueryHandler(_mockUserRoleRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_UserRoleName_Exist()
    {
        _userRoleFixture.UserRoles[0].Role = "PR_Site";
        _userRoleFixture.UserRoles[0].IsActive = true;

        var result = await _handler.Handle(new GetUserRoleNameUniqueQuery { Role = _userRoleFixture.UserRoles[0].Role, Id = _userRoleFixture.UserRoles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_UserRoleNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetUserRoleNameUniqueQuery { Role = "DR_Site", Id = _userRoleFixture.UserRoles[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsUserRoleNameExist_OneTime()
    {
        var handler = new GetUserRoleNameUniqueQueryHandler(_mockUserRoleRepository.Object);

        await handler.Handle(new GetUserRoleNameUniqueQuery { Role = _userRoleFixture.UserRoles[0].ReferenceId, Id = _userRoleFixture.UserRoles[0].Role }, CancellationToken.None);

        _mockUserRoleRepository.Verify(x => x.IsUserRoleNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_UserRoleName_NotMatch()
    {
        var result = await _handler.Handle(new GetUserRoleNameUniqueQuery { Role = "PR_Pro", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockUserRoleRepository = UserRoleRepositoryMocks.GetUserRoleEmptyRepository();

        var result = await _handler.Handle(new GetUserRoleNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}