﻿using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Queries.GetMYSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.MYSQLMonitorStatusModel;

namespace ContinuityPatrol.Application.Mappings;

public class MysqlMonitorStatusProfile : Profile
{
    public MysqlMonitorStatusProfile()
    {
        CreateMap<MYSQLMonitorStatus, CreateMYSQLMonitorStatusCommand>().ReverseMap();
        CreateMap<UpdateMYSQLMonitorStatusCommand, MYSQLMonitorStatus>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<MYSQLMonitorStatus, MYSQLMonitorStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MYSQLMonitorStatus, MYSQLMonitorStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MYSQLMonitorStatus, MYSQLMonitorStatusDetailByTypeVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<MYSQLMonitorStatus, GetMYSQLMonitorStatusByInfraObjectIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}