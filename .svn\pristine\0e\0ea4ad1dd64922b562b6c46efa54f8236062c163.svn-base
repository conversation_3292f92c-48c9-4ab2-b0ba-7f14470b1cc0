﻿using ContinuityPatrol.Application.Features.Template.Commands.Create;
using ContinuityPatrol.Application.Features.Template.Commands.Delete;
using ContinuityPatrol.Application.Features.Template.Commands.Update;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeId;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndActionTypeUnique;
using ContinuityPatrol.Application.Features.Template.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Template.Queries.GetList;
using ContinuityPatrol.Application.Features.Template.Queries.GetNames;
using ContinuityPatrol.Application.Features.Template.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Template.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Template.Queries.GetTemplateByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.TemplateModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;
using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeIdAndType;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Application.Features.Template.Queries.GetByInfraObjectIdandActiontype;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class TemplatesController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetTemplateListVm>>> GetTemplateList()
    {
        Logger.LogDebug("Get All Template");

        return Ok(await Mediator.Send(new GetTemplateListQuery()));
    }

    [HttpGet("{id}", Name = "GetTemplate")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<TemplateDetailVm>> GetTemplateById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Template Id");

        Logger.LogDebug($"Get Template Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetTemplateDetailQuery { Id = id }));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Orchestration.Create)]
    public async Task<ActionResult<CreateTemplateResponse>> CreateTemplate(
        [FromBody] CreateTemplateCommand createTemplateCommand)
    {
        Logger.LogDebug($"Create Template '{createTemplateCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateTemplate), await Mediator.Send(createTemplateCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Orchestration.Edit)]
    public async Task<ActionResult<UpdateTemplateResponse>> UpdateTemplate(
        [FromBody] UpdateTemplateCommand updateTemplateCommand)
    {
        Logger.LogDebug($"Update Template '{updateTemplateCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateTemplateCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Orchestration.Delete)]
    public async Task<ActionResult<DeleteTemplateResponse>> DeleteTemplate(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "Template Id");

        Logger.LogDebug($"Delete Template Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteTemplateCommand { Id = id }));
    }

    [HttpGet]
    [Route("names")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<TemplateNameVm>>> GetTemplateNames()
    {
        Logger.LogDebug("Get All Template Names");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllTemplatesNameCacheKey,
            () => Mediator.Send(new GetTemplateNameQuery()), CacheExpiry));
    }

    [Route("name-exist")]
    [HttpGet]
    public async Task<ActionResult> IsTemplateNameExist(string name, string? id)
    {
        Guard.Against.NullOrWhiteSpace(name, "Template Name");

        Logger.LogDebug($"Check Name Exists Detail by Template Name '{name}' and id '{id}'");

        return Ok(await Mediator.Send(new GetTemplateNameUniqueQuery { TemplateName = name, TemplateId = id }));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<PaginatedResult<TemplateListVm>>> GetPaginatedTemplate([FromQuery] GetTemplatePaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in Template Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [HttpGet, Route("by/replicationtypeid")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetTemplateByReplicationTypeIdVm>>> GetTemplateByReplicationTypeId(string replicationTypeId)
    {
        Logger.LogDebug($"Get Template Detail by ReplicationTypeId '{replicationTypeId}'");

        return Ok(await Mediator.Send(new GetTemplateByReplicationTypeIdQuery { ReplicationTypeId = replicationTypeId }));
    }
    [HttpGet, Route("by/infraobjectidandactiontype")]
    [Authorize(Policy = Permissions.Orchestration.View)]
    public async Task<ActionResult<TemplateByInfraObjectIdandActiontypeVm>> GetTemplateByInfraObjectIdandActionType(string infraObjectId, string actionType)
    {
        Logger.LogDebug($"Get Template Detail by ReplicationTypeId '{infraObjectId}, {actionType}'");

        return Ok(await Mediator.Send(new GetTemplateByInfraObjectIdandActiontypeQuery { InfraObjectId = infraObjectId, ActionType = actionType }));
    }


    [HttpGet, Route("infraObjectId")]
    [Authorize(Policy =Permissions.Orchestration.View)]
    public async Task<ActionResult<List<GetTemplateByInfraObjectIdVm>>> GetTemplateByInfraObjectId(string infraObjectId)
    {
        Logger.LogDebug($"Get Template Detail by InfraObjectId {infraObjectId} ");

        return Ok(await Mediator.Send(new GetTemplateByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [Route("actionType-and-replicationTypeId_exist")]
    [HttpGet]
    public async Task<ActionResult> IsTemplateReplicationTypeIdAndActionTypeNameUnique(string actionType, string replicationTypeId)
    {
        Guard.Against.NullOrWhiteSpace(actionType, "ActionType");
        Guard.Against.NullOrWhiteSpace(replicationTypeId, "Replication Type Id");

        Logger.LogDebug($"Check Name Exists Detail by ActionType '{actionType}' and ReplicationTypeId '{replicationTypeId}'");

        return Ok(await Mediator.Send(new GetByReplicationTypeIdAndActionTypeUniqueQuery { ActionType = actionType, ReplicationTypeId = replicationTypeId}));
    }
    
    [Route("template-types")]
    [HttpGet]
    public async Task<ActionResult<List<GetByReplicationTypeIdAndTypeVm>>> GetTemplateByTypes(string replicationTypeId,string actionType,string entityType,string? type,string? templateType)
    {
        Guard.Against.NullOrWhiteSpace(replicationTypeId, "Replication Type Id");

        Logger.LogDebug($"Get Template Detail by InfraObjectId ");

        return Ok(await Mediator.Send(new GetByReplicationTypeIdAndTypeQuery {ReplicationTypeId = replicationTypeId,ActionType = actionType ,EntityType = entityType ,Type = type, TemplateType = templateType}));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllTemplatesCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}