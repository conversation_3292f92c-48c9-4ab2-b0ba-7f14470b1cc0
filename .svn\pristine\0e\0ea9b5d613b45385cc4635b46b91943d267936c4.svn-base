﻿using ContinuityPatrol.Application.Features.Template.Queries.GetByReplicationTypeId;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Queries
{
    public class GetTemplateByReplicationTypeIdQueryHandlerTests
    {
        private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
        private readonly Mock<ITemplateRepository> _mockTemplateRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly GetTemplateByReplicationTypeIdQueryHandler _handler;

        public GetTemplateByReplicationTypeIdQueryHandlerTests()
        {
            _mockInfraObjectRepository = new Mock<IInfraObjectRepository>();
            _mockTemplateRepository = new Mock<ITemplateRepository>();
            _mockMapper = new Mock<IMapper>();

            _handler = new GetTemplateByReplicationTypeIdQueryHandler(
                _mockTemplateRepository.Object,
                _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ReturnsTemplateDto_WhenValidRequestIsProvided()
        {
            var request = new GetTemplateByReplicationTypeIdQuery
            {
                //InfraObjectId = "valid-infra-object-id",
                //ActionType = "Create"
            };

            var infraObject = new Domain.Entities.InfraObject
            {
                //PRServerId = "pr-server-id",
                //PRServerName = "PR Server",
                //DRServerId = "dr-server-id",
                //DRServerName = "DR Server",
                //NearDRServerId = "near-dr-server-id",
                //NearDRServerName = "Near DR Server",
                //PRReplicationId = "pr-replication-id",
                //PRReplicationName = "PR Replication",
            };

            var template = new Domain.Entities.Template { Id = 1 };

            var expectedDto = new GetTemplateByReplicationTypeIdVm
            {
                //PRServerId = "pr-server-id",
                //PRServerName = "PR Server",
                //DRServerId = "dr-server-id",
                //DRServerName = "DR Server",
            };

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId))
                .ReturnsAsync(infraObject);

            //_mockTemplateRepository
            //    .Setup(repo => repo.GetTemplateByReplicationTypeIdAndActionType(infraObject.ReplicationTypeId, request.))
            //    .ReturnsAsync(template);

            _mockMapper
                .Setup(mapper => mapper.Map<GetTemplateByReplicationTypeIdVm>(template))
                .Returns(expectedDto);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            //Assert.Equal("pr-server-id", result.PRServerId);
            //Assert.Equal("PR Server", result.PRServerName);
            _mockInfraObjectRepository.Verify(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId), Times.Once);
          //  _mockTemplateRepository.Verify(repo => repo.GetTemplateByReplicationTypeIdAndActionType(infraObject.ReplicationTypeId, request.ActionType), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<GetTemplateByReplicationTypeIdVm>(template), Times.Once);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenInfraObjectIsNull()
        {
            var request = new GetTemplateByReplicationTypeIdQuery
            {
                //InfraObjectId = "invalid-infra-object-id",
                //ActionType = "Create"
            };

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId))
                .ReturnsAsync((Domain.Entities.InfraObject)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));
            _mockInfraObjectRepository.Verify(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId), Times.Once);
            _mockTemplateRepository.Verify(repo => repo.GetTemplateByReplicationTypeIdAndActionType(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            _mockMapper.Verify(mapper => mapper.Map<GetTemplateByReplicationTypeIdVm>(It.IsAny<Domain.Entities.Template>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ThrowsNotFoundException_WhenTemplateIsNull()
        {
            var request = new GetTemplateByReplicationTypeIdQuery
            {
                //InfraObjectId = "valid-infra-object-id",
                //ActionType = "Create"
            };

            var infraObject = new Domain.Entities.InfraObject
            {
                ReplicationTypeId = "valid-replication-type-id"
            };

            _mockInfraObjectRepository
                .Setup(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId))
                .ReturnsAsync(infraObject);

            //_mockTemplateRepository
            //    .Setup(repo => repo.GetTemplateByReplicationTypeIdAndActionType(infraObject.ReplicationTypeId, request.ActionType))
            //    .ReturnsAsync((Domain.Entities.Template)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(request, CancellationToken.None));
            _mockInfraObjectRepository.Verify(repo => repo.GetByReferenceIdAsync(request.ReplicationTypeId), Times.Once);
                // _mockTemplateRepository.Verify(repo => repo.GetTemplateByReplicationTypeIdAndActionType(infraObject.ReplicationTypeId, request.ActionType), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<GetTemplateByReplicationTypeIdVm>(It.IsAny<Domain.Entities.Template>()), Times.Never);
        }
    }
}
