﻿using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Create;
using ContinuityPatrol.Application.Features.HeatMapStatus.Commands.Update;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessFunctionAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetBusinessServiceAvailability;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.HeatMapStatus.Queries.GetDetailByInfraObjectandEntityId;
using ContinuityPatrol.Domain.ViewModels.HeatMapStatusModel;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class HeatMapStatusProfile : Profile
{
    public HeatMapStatusProfile()
    {
        CreateMap<HeatMapStatusView, CreateHeatMapStatusCommand>().ReverseMap();
        CreateMap<UpdateHeatMapStatusCommand, HeatMapStatusView>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<HeatMapStatusView, HeatMapStatusDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HeatMapStatusView, HeatMapStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HeatMapStatusView, HeatMapStatusListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
   
        CreateMap<HeatMapStatusView, ImpactDetailVm>().ReverseMap();

        // CreateMap<HeatMapStatus, ImpactViewVm>().ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<HeatMapStatusView, HeatMapStatusByInfraObjectandEntityIdVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));

        CreateMap<BusinessService, ServiceAvailability>()
            .ForMember(dest => dest.BusinessServiceId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.BusinessServiceName, opt => opt.MapFrom(src => src.Name));

        CreateMap<HeatMapStatusView, HeatmapStatusDto>().ReverseMap();


        CreateMap<BusinessFunction, FunctionAvailability>()
            .ForMember(dest => dest.BusinessFunctionId, opt => opt.MapFrom(src => src.ReferenceId))
            .ForMember(dest => dest.BusinessFunctionName, opt => opt.MapFrom(src => src.Name));

        CreateMap<HeatMapStatusView, BFHeatmapStatusDto>().ReverseMap();


        ////ImpactDetails
        //CreateMap<List<HeatMapStatus>, ImpactDetailVm>()
        //    .ForMember(dest => dest.ServerTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("server"))))
        //    .ForMember(dest => dest.ReplicationTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("replication"))))
        //    .ForMember(dest => dest.NetworkTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("network"))))
        //    .ForMember(dest => dest.ApplicationTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("application"))))
        //    .ForMember(dest => dest.StorageTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("storage"))))
        //    .ForMember(dest => dest.DatabaseTotalCount,
        //        opt => opt.MapFrom(src => src.Count(x => x.HeatmapType.Trim().ToLower().Equals("database"))));


        CreateMap<List<HeatMapStatusView>, ImpactDetailVm>()
            .ForMember(dest => dest.ServerDownCount, opt =>
                opt.MapFrom(src =>
                    src.Count(x => x.HeatmapType.Trim().ToLower().Equals("server") && x.IsAffected.Equals(true))))
            .ForMember(dest => dest.ReplicationDownCount, opt =>
                opt.MapFrom(src => src.Count(x =>
                    x.HeatmapType.Trim().ToLower().Equals("replication") && x.IsAffected.Equals(true))))
            //.ForMember(dest => dest.NetworkDownCount, opt =>
            //    opt.MapFrom(src =>
            //        src.Count(x => x.HeatmapType.Trim().ToLower().Equals("network") && x.IsAffected.Equals(true))))
            //.ForMember(dest => dest.ApplicationDownCount, opt =>
            //    opt.MapFrom(src => src.Count(x =>
            //        x.HeatmapType.Trim().ToLower().Equals("application") && x.IsAffected.Equals(true))))
            //.ForMember(dest => dest.StorageDownCount, opt =>
            //    opt.MapFrom(src =>
            //        src.Count(x => x.HeatmapType.Trim().ToLower().Equals("storage") && x.IsAffected.Equals(true))))
            .ForMember(dest => dest.DatabaseDownCount, opt =>
                opt.MapFrom(src =>
                    src.Count(x => x.HeatmapType.Trim().ToLower().Equals("database") && x.IsAffected.Equals(true))));

        CreateMap<PaginatedResult<HeatMapStatusView>,PaginatedResult<HeatMapStatusListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}