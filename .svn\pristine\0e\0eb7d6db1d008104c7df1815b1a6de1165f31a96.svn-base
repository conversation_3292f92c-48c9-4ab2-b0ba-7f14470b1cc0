using ContinuityPatrol.Domain.ViewModels.CyberAirGapModel;

namespace ContinuityPatrol.Application.Features.CyberAirGap.Queries.GetList;

public class GetCyberAirGapListQueryHandler : IRequestHandler<GetCyberAirGapListQuery, List<CyberAirGapListVm>>
{
    private readonly ICyberAirGapRepository _cyberAirGapRepository;
    private readonly IMapper _mapper;

    public GetCyberAirGapListQueryHandler(IMapper mapper, ICyberAirGapRepository cyberAirGapRepository)
    {
        _mapper = mapper;
        _cyberAirGapRepository = cyberAirGapRepository;
    }

    public async Task<List<CyberAirGapListVm>> Handle(GetCyberAirGapListQuery request,
        CancellationToken cancellationToken)
    {
        var cyberAirGaps = await _cyberAirGapRepository.ListAllAsync();

        if (cyberAirGaps.Count <= 0) return new List<CyberAirGapListVm>();

        return _mapper.Map<List<CyberAirGapListVm>>(cyberAirGaps);
    }
}