﻿using ContinuityPatrol.Application.Features.IncidentDaily.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.IncidentDaily.Validators;

public class UpdateIncidentDailyValidatorTests : IClassFixture<IncidentDailyFixture>
{
    private readonly Mock<IIncidentDailyRepository> _mockIncidentDailyRepository;

    private readonly IncidentDailyFixture _incidentDailyFixture;

    public UpdateIncidentDailyValidatorTests(IncidentDailyFixture incidentDailyFixture)
    {
        _incidentDailyFixture = incidentDailyFixture;

        var incidentDailies = new Fixture().Create<List<Domain.Entities.IncidentDaily>>();

        _mockIncidentDailyRepository = IncidentDailyRepositoryMocks.UpdateIncidentDailyRepository(incidentDailies);
    }

    //ParentBusinessServiceName

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_WithEmpty(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.ParentBusinessServiceNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_IsNull(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = null;

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.ParentBusinessServiceNameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_MinimumRange_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "AB";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.ParentBusinessServiceNameLength, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_MaximumRange_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "This is a very long business service name that exceeds the maximum allowed length";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.ParentBusinessServiceNameLength, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_InvalidFormat_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "123InvalidName";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.ParentBusinessServiceNameInvalid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_ParentBusinessServiceName_ValidFormat_Passes(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "Valid Business Service";
        updateIncidentDailyCommand.InfraObjectName = "Valid Infrastructure";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
    }

    //InfraObjectName

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_WithEmpty(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.InfraObjectName = "";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.InfraObjectNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_IsNull(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.InfraObjectName = null;

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.InfraObjectNameNotEmpty, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_MinimumRange_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.InfraObjectName = "AB";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.InfraObjectNameLength, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_MaximumRange_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.InfraObjectName = "This is a very long infrastructure object name that exceeds the maximum allowed length";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.InfraObjectNameLength, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_InvalidFormat_Validator(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.InfraObjectName = "123InvalidName";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.IncidentDaily.InfraObjectNameInvalid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_InfraObjectName_ValidFormat_Passes(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "Valid Business Service";
        updateIncidentDailyCommand.InfraObjectName = "Valid Infrastructure";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
    }

    [Theory]
    [AutoIncidentDailyData]
    public async Task Verify_Update_BothFields_Valid_Passes(UpdateIncidentDailyCommand updateIncidentDailyCommand)
    {
        var validator = new UpdateIncidentDailyCommandValidator(_mockIncidentDailyRepository.Object);

        updateIncidentDailyCommand.ParentBusinessServiceName = "Test Business Service";
        updateIncidentDailyCommand.InfraObjectName = "Test Infrastructure Object";

        var validateResult = await validator.ValidateAsync(updateIncidentDailyCommand, CancellationToken.None);
        Assert.True(validateResult.IsValid);
        Assert.Empty(validateResult.Errors);
    }
}