﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.PluginManagerHistory.Events.Update;

public class PluginManagerHistoryUpdatedEventHandler : INotificationHandler<PluginManagerHistoryUpdatedEvent>
{
    private readonly ILogger<PluginManagerHistoryUpdatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public PluginManagerHistoryUpdatedEventHandler(ILoggedInUserService userService,
        ILogger<PluginManagerHistoryUpdatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(PluginManagerHistoryUpdatedEvent updatedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Update} {Modules.PluginManagerHistory}",
            Entity = Modules.PluginManagerHistory.ToString(),
            ActivityType = ActivityType.Update.ToString(),
            ActivityDetails = $"Plugin Manager History'{updatedEvent.PluginManagerName}' updated successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Plugin Manager '{updatedEvent.PluginManagerName}' updated successfully.");
    }
}