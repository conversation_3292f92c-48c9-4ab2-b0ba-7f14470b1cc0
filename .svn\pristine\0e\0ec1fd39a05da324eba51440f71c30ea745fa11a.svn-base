﻿using ContinuityPatrol.Application.Features.TableAccess.Queries.GetNames;

namespace ContinuityPatrol.Application.UnitTests.Features.TableAccess.Queries
{
    public class GetTableAccessNameQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ITableAccessRepository> _mockTableAccessRepository;
        private readonly GetTableAccessNameQueryHandler _handler;

        public GetTableAccessNameQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockTableAccessRepository = new Mock<ITableAccessRepository>();
            _handler = new GetTableAccessNameQueryHandler(
                _mockMapper.Object,
                _mockTableAccessRepository.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsTableAccessNameVmList()
        {
            var request = new GetTableAccessNameQuery();
            var tableAccesses = new List<Domain.Entities.TableAccess>
            {
                new Domain.Entities.TableAccess { Id = 1, SchemaName = "schema1", TableName = "table1", IsChecked = true },
                new Domain.Entities.TableAccess { Id = 2, SchemaName = "schema2", TableName = "table2", IsChecked = false }
            };

            var tableAccessNameVmList = new List<TableAccessNameVm>
            {
                new TableAccessNameVm { Id = Guid.NewGuid().ToString(), SchemaName = "schema1", TableName = "table1" },
                new TableAccessNameVm { Id = Guid.NewGuid().ToString(), SchemaName = "schema2", TableName = "table2" }
            };

            _mockTableAccessRepository
                .Setup(repo => repo.GetTableAccessNames())
                .ReturnsAsync(tableAccesses);

            _mockMapper
                .Setup(mapper => mapper.Map<List<TableAccessNameVm>>(tableAccesses))
                .Returns(tableAccessNameVmList);

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("schema1", result[0].SchemaName);
            Assert.Equal("table1", result[0].TableName);
            _mockTableAccessRepository.Verify(repo => repo.GetTableAccessNames(), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<List<TableAccessNameVm>>(tableAccesses), Times.Once);
        }

        [Fact]
        public async Task Handle_EmptyTableAccesses_ReturnsEmptyList()
        {
            var request = new GetTableAccessNameQuery();
            var tableAccesses = new List<Domain.Entities.TableAccess>();

            _mockTableAccessRepository
                .Setup(repo => repo.GetTableAccessNames())
                .ReturnsAsync(tableAccesses);

            _mockMapper
                .Setup(mapper => mapper.Map<List<TableAccessNameVm>>(tableAccesses))
                .Returns(new List<TableAccessNameVm>());

            var result = await _handler.Handle(request, CancellationToken.None);

            Assert.Empty(result);
            _mockTableAccessRepository.Verify(repo => repo.GetTableAccessNames(), Times.Once);
        }

        [Fact]
        public async Task Handle_NullRepositoryResponse_ThrowsException()
        {
            var request = new GetTableAccessNameQuery();
            List<Domain.Entities.TableAccess> tableAccesses = null;

            _mockTableAccessRepository
                .Setup(repo => repo.GetTableAccessNames())
                .ReturnsAsync(tableAccesses);

            await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(request, CancellationToken.None));
        }
    }
}
