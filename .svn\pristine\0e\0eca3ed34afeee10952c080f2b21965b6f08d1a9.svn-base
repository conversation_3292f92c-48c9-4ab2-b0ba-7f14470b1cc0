﻿using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.User.Queries;

public class GetUserPaginatedListQueryHandlerTests : IClassFixture<UserFixture>, IClassFixture<UserInfoFixture>, IClassFixture<UserInfraObjectFixture>,
    IClassFixture<UserRoleFixture>
{
    private readonly UserFixture _userFixture;
    private readonly IMapper _mapper;

    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly Mock<IUserViewRepository> _mockUserViewRepository;

    private readonly GetUserPaginatedListQueryHandler _handler;

    public GetUserPaginatedListQueryHandlerTests(UserFixture userFixture,IMapper mapper, UserInfoFixture userInfoFixture, UserInfraObjectFixture userInfraObjectFixture, UserRoleFixture userRoleFixture)
    {
        _userFixture = userFixture;
        _mapper = mapper;
        _mockUserViewRepository=new Mock<IUserViewRepository>();
        _mockUserRepository = UserRepositoryMocks.GetPaginatedUserRepository(_userFixture.Users);
        var mockUserInfoRepository = UserInfoRepositoryMocks.GetPaginatedUserInfoRepository(userInfoFixture.UserInfos);
        var mockUserInfraObjectRepository = UserInfraObjectRepositoryMocks.GetPaginatedUserInfraObjectRepository(userInfraObjectFixture.UserInfraObjects);
        var mockUserRoleRepository = UserRoleRepositoryMocks.GetUserRoleRepository(userRoleFixture.UserRoles);

        _handler = new GetUserPaginatedListQueryHandler(mapper, _mockUserViewRepository.Object);

        _userFixture.Users[0].CompanyName = "PTS";
        _userFixture.Users[0].LoginName = "Test_1";
        _userFixture.Users[0].Role = "SuperAdmin";
        _userFixture.Users[0].LoginType = "InHouse";

        userInfoFixture.UserInfos[0].UserName = "Tester_1";
        userInfoFixture.UserInfos[0].Email = "<EMAIL>";
        userInfoFixture.UserInfos[0].ReferenceId = "35a7a3f2-1c35-49fe-b82f-afe3dd8e4bd9";

        _userFixture.Users[1].CompanyName = "PTech";
        _userFixture.Users[1].LoginName = "Test_2";
        _userFixture.Users[1].Role = "Admin";
        _userFixture.Users[1].LoginType = "InHouse_1";

        userInfoFixture.UserInfos[1].UserName = "Tester_2";
        userInfoFixture.UserInfos[1].Email = "<EMAIL>";
        userInfoFixture.UserInfos[0].ReferenceId = "f7318e25-b5ea-4161-8925-5de15e57b373";

        userInfoFixture.UserInfos[0].Mobile = 0.ToString();
        userInfoFixture.UserInfos[0].AlertMode = 0.ToString();

        userInfoFixture.UserInfos[1].Mobile = 0.ToString();
        userInfoFixture.UserInfos[1].AlertMode = 0.ToString();

    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetUserPaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserListVm>>();

        result.TotalCount.ShouldBe(6);

        result.TotalPages.ShouldBe(1);
    }

    [Fact]
    public async Task Handle_Return_Users_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetUserPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "CompanyName=Pt;LoginName=Test;Role=SuperAdmin;LoginType=InHouse" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].CompanyName.ShouldBe("PTS");

        result.Data[0].LoginName.ShouldBe("Test_1");

        result.Data[0].Role.ShouldBe("SuperAdmin");

        result.Data[0].LoginType.ShouldBe("InHouse");

    }

    [Fact]
    public async Task Handle_Return_PaginatedUsers_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetUserPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserListVm>>();

        result.TotalCount.ShouldBe(2);

        result.Data[0].ShouldBeOfType<UserListVm>();

        result.Data[0].Id.ShouldBeGreaterThan(0.ToString());

        result.Data[0].LoginName.ShouldBe("Test_1");

        result.Data[0].CompanyName.ShouldBe(_userFixture.Users[0].CompanyName);

        result.Data[0].CompanyId.ShouldNotBeEmpty();

        result.Data[0].Role.ShouldBe(_userFixture.Users[0].Role);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetUserPaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "ABCD" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<UserListVm>>();

        result.TotalCount.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetUserPaginatedListQuery(), CancellationToken.None);

        _mockUserRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
}