
function pageLayoutDesign(layoutName) {
    $(".cardCreation").removeClass('card-show')
    $(".cardCreation").addClass("d-none")
    let appendData;

    let siteNav= `<div class="row mb-2 g-2 mt-0 d-none" id = "Sitediv" >
        <div class="col-12">
            <div class="p-2 bg-white rounded">
                <ul class="nav nav-underline siteContainer">
                    <li class="nav-item siteListChange" id="siteName0">
                        <a class="nav-link active" aria-current="page" href="#">PR<span class="mx-2"><i class="cp-data-replication"></i></span><span class="siteName">DR</span></a>
                    </li>
                    <li class="nav-item vr"></li>
                    <li class="nav-item siteListChange" id="siteName1">
                        <a class="nav-link" aria-current="page" href="#">PR<span class="mx-2"><i class="cp-data-replication"></i></span><span class="siteName">NearDR</span></a>
                    </li>
                    <li class="nav-item vr"></li></ul>
            </div>
        </div>
        </div >`

    if (layoutName == "PB_layout15") {
        // $("#layoutDesignModal").modal("show")
        $(".cardCreation").addClass('card-show')
        $(".cardCreation").removeClass("d-none")
        appendData = siteNav
       // appendData += `
       //<div class="row h-100 g-3 layoutcontainer pageBuilderSetRowDesign" id="pageBuilderInfraId">
       //     <div class="col-12 d-grid">
       //       <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)" >
       //       </div>
       //     </div>
       // </div>
       //  <div class="text-end">
       //      <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
       //           <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
       //     </div>
       // `
        $('#PB_pageContent').append(appendData);
    }
    else {
        if (layoutName == "PB_layout1") {
            appendData = siteNav
            appendData += `
    <div class="row g-2  mt-0 pageBuilderSetRowDesign" style="height: calc(100vh - 56px);" id="pageBuilderInfraId" >
            <div class="col-6 d-grid">
                <div class="card Card_Design_None border-dashed  mb-1 pageBuilderSetDesign PB_layout1Design1" designId="PB_layout1Design1" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
              <div class="col-6 d-grid">
                <div class="card Card_Design_None border-dashed  mb-1 pageBuilderSetDesign PB_layout1Design2" designId="PB_layout1Design2" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
             <div class="col-6 d-grid">
                <div class="card Card_Design_None border-dashed  mb-1 pageBuilderSetDesign PB_layout1Design3" designId="PB_layout1Design3" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
             <div class="col-6 d-grid">
                <div class="card Card_Design_None border-dashed  mb-1 pageBuilderSetDesign PB_layout1Design4" designId="PB_layout1Design4" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
             <div class="col-6 d-grid">
                <div class="card Card_Design_None border-dashed  mb-1 pageBuilderSetDesign PB_layout1Design5" designId="PB_layout1Design5" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
       </div>
          `
        }
        else if (layoutName == "PB_layout2") {
            appendData = siteNav
            appendData += `
    <div class="row g-2 mt-0 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);" id="pageBuilderInfraId">
    <div class="col-6 d-grid " >
     <div class="card Card_Design_None border-dashed mb-2 pageBuilderSetDesign PB_layout1Design1" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
  </div>
    <div class="col-6 d-grid">
     <div class="card Card_Design_None border-dashed mb-2 pageBuilderSetDesign PB_layout1Design2" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
   </div>
   </div>
    <div class="row g-2" style="height: calc(60vh - 23px);">
    <div class="col-6 d-grid">
    <div class="card Card_Design_None border-dashed mb-2 pageBuilderSetDesign PB_layout1Design3" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                        </div>
        <div class="card Card_Design_None border-dashed mb-2 pageBuilderSetDesign PB_layout1Design4" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                        </div>
                         </div>
     
       <div class="col-6 d-grid">
    <div class="card Card_Design_None border-dashed mb-2 pageBuilderSetDesign PB_layout1Design5" ondrop="drop(event)" ondragover="allowDrop(event)" designId="PB_layout1Design5" onclick="pageWidgetListModel(this)">
                        </div></div></div>
      </div>  
    `
        }
        else if (layoutName == "PB_layout3") {
            appendData = siteNav
            appendData += `
<div class="row  g-3 pageBuilderSetRowDesign" style="height: calc(100vh - 56px);" id="pageBuilderInfraId">
        <div class="col-6 d-grid">
            <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

            </div>

        </div>
        <div class="col-6 d-grid">
          <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

          </div>
           <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

          </div>
        </div>
    </div>   
    
    `
        }
        else if (layoutName == "PB_layout4") {
            appendData = siteNav
            appendData += `
        <div class="row g-3 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);"  id="pageBuilderInfraId">
            <div class="col-5 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)"  designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            <div class="col-7 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" designId="PB_layout1Design2" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
        </div>


        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                   
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design5" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design5" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                            
                     
                </div>
            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">

            <div class="col-8 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design6" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design6" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                   
                    
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="row">
                 <div class="col-6 d-grid">
                    <div class="card border-dashed pageBuilderSetDesign PB_layout1Design7" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design7" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                        
                    </div>
                     </div>
                     <div class="col-6 d-grid">
                    <div class="card border-dashed pageBuilderSetDesign PB_layout1Design8" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design8" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                        
                    </div>
                     </div>
                </div>
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design9" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design9" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                    
                </div>
            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design10" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design10" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design11" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design11" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                    
                </div>
            </div>
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design12" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design12" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                   
                </div>
            </div>

            <div class="col-6 d-grid">
            <div class="card border-dashed pageBuilderSetDesign PB_layout1Design13" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design13" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
   
             </div>
            </div>

            <div class="col-12 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design14" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design14" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>

            <div class="col-12 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design15" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design15" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                  
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design16" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design16" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
             <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design17" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design17" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>
             <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design18" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design18" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                   
                </div>
            </div>

        </div>
    `
        }
        else if (layoutName == "PB_layout5") {
            appendData = siteNav
            appendData += `
    <div class="row pageBuilderSetRowDesign" style="height: calc(100vh - 56px);"  id="pageBuilderInfraId">
            <div class="col-5 d-grid">
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)"></div>
            </div>
            <div class="col-7 d-grid">
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)"></div>
              <div class="card border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" designId="PB_layout1Design3" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)"> </div>
              <div class="card border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" designId="PB_layout1Design4" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)"> </div>
            </div>
            <div class="col-12 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design5" id="pageBuilderSetDesign" designId="PB_layout1Design5" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
        </div>
    `
        }
        else if (layoutName == "PB_layout6") {
            appendData = siteNav
            appendData += `
     <div class="row pageBuilderSetRowDesign" style="height: calc(100vh - 56px);"  id="pageBuilderInfraId">
            <div class="col-7 d-grid">
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design1"  id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
             </div>
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
             </div>
            </div>
            <div class="col-5 d-grid">
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)"  designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
             </div>
              <div class="card border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
              </div>
            </div>
            <div class="col-xl-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign mssqlserver PB_layout1Design5"   style="display: none;" id="pageBuilderSetDesign" designId="PB_layout1Design5" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
        </div>
    `
        }
        else if (layoutName == "PB_layout7") {
            appendData = siteNav
            appendData += `
            <div class="row  pageBuilderSetRowDesign"  style="height: calc(100vh - 56px);"  id="pageBuilderInfraId">
            <div class="col-6 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" designId="PB_layout1Design1" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>

            </div>
            <div class="col-6 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" designId="PB_layout1Design2" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
        
  
            <div class="col-12 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" designId="PB_layout1Design3" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>

            </div>

         <div class="col-12 d-grid">
                <div class="card border-dashed pageBuilderSetDesign mssqlserver PB_layout1Design4"   style="display: none;" id="pageBuilderSetDesign" designId="PB_layout1Design4" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            </div>
    `

        }
        else if (layoutName == "PB_layout8") {
            appendData = siteNav
            appendData += `
                     <div class="row g-3 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);"  id="pageBuilderInfraId">
               <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)"  designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" designId="PB_layout1Design2" ondrop="drop(event)" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
             <div class="col-4 d-grid">
             <div class="card border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
                <div class="row">
                 <div class="col-6 d-grid">
                    <div class="card border-dashed pageBuilderSetDesign PB_layout1Design5" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design5" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                    </div>
                     </div>
                     <div class="col-6 d-grid">
                    <div class="card border-dashed pageBuilderSetDesign PB_layout1Design6" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design6" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                    </div>
                     </div>
                </div>
            </div>
        </div>

        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design7" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design7" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design8" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design8" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>
           
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">

            <div class="col-8 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design9" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design9" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">


                </div>
            </div>
             <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design10" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design10" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
                 <div class="card border-dashed pageBuilderSetDesign PB_layout1Design11" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design11" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>
          
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-12 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design12" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design12" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
          
        </div>
        <div class="row g-2" style="height: calc(50vh - 23px);">
            <div class="col-4 d-grid">
                <div class="card border-dashed pageBuilderSetDesign PB_layout1Design13" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design13" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

                </div>
            </div>

            <div class="col-4 d-grid">
            <div class="card border-dashed pageBuilderSetDesign PB_layout1Design14" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design14" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

             </div>
            </div>
             <div class="col-4 d-grid">
            <div class="card border-dashed pageBuilderSetDesign PB_layout1Design15" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design15" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

             </div>
            </div>
        </div>
                     
    ` }
        else if (layoutName == "PB_layout9") {
            appendData = siteNav
            appendData += `
            <div class="row g-3 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);"  id="pageBuilderInfraId">
            <div class="col-6 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            <div class="col-6 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
            </div>
            <div class="row g-3" style="height: calc(50vh - 23px);" >
            <div class="col-12 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
            <div class="col-12 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
             </div>
        </div>
        
    `
        }
        else if (layoutName == "PB_layout10") {
            appendData = siteNav
            appendData += `
     <div class="row g-3 pageBuilderSetRowDesign" style="height: calc(50vh - 23px);"  id="pageBuilderInfraId">
            <div class="col-6 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design1" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design1" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">
                </div>
            </div>
            <div class="col-6 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design2" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design2" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
            </div>
            <div class="row g-3" style="height: calc(50vh - 30px);" >
            <div class="col-12 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design3" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design3" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
            <div class="col-12 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign PB_layout1Design4" id="pageBuilderSetDesign" ondrop="drop(event)" designId="PB_layout1Design4" ondragover="allowDrop(event)" onclick="pageWidgetListModel(this)">

              </div>
            </div>
             </div>
        </div>
        
    `
        }
        else if (layoutName == "PB_layout11") {
            appendData = siteNav
            appendData += `<div class="row g-3 pageBuilderSetRowDesign" style="height: calc(100vh - 56px);" id="pageBuilderInfraId">
            <div class="col-4 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
                  
                </div>
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
                    
                </div>
            </div>
          <div class="col-4 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
            </div>
            <div class="col-4 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
               <div class="row h-100">
              <div class="col-6 d-grid">

                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div></div>
                <div class="col-6 d-grid">

                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div></div></div>
            </div>
        </div>
         <div class="text-end p-2">
             <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
                  <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
            </div>`

        }
        else if (layoutName == "PB_layout12") {
            appendData = siteNav
            appendData += `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>

            </div>
            <div class="col-5 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

              </div>
            </div><div class="col-5 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

              </div>
            </div><div class="col-7 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

              </div>
            </div>
        </div>
         <div class="text-end p-2">
             <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
                  <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
            </div>
    `
        }
        else if (layoutName == "PB_layout13") {
            appendData = siteNav
            appendData += `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-7 d-grid">
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>

            </div>
            <div class="col-5 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

              </div>
            </div><div class="col-5 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

              </div>
            </div><div class="col-7 d-grid">
              <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
              </div>
            </div>
        </div>
         <div class="text-end p-2">
             <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
             <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
            </div>
    `
        }
        else if (layoutName == "PB_layout14") {
            appendData = siteNav
            appendData += `
    <div class="row h-100 g-3 pageBuilderSetRowDesign">
            <div class="col-3 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
                  
                </div>
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
                    
                </div>
            </div>
          <div class="col-3 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
            </div>
            <div class="col-6 d-grid" >
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
                <div class="card  border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
            </div>
        </div>
         <div class="text-end p-2">
             <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
                  <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
            </div>
    `
        }
        else if (layoutName == "PB_layout16") {
            appendData = siteNav
            appendData += `
    <div class="row g-2 h-75 mt-0 mb-2 pageBuilderSetRowDesign">
            <div class="col-6 d-grid" >
                <div class="card mb-0 border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">
                  
                </div>
               
            </div>
          <div class="col-6 d-grid" >
                <div class="card mb-0 border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
               
            </div>
          
        </div>
        <div class="row h-75 g-2">
          <div class="col-6 d-grid" >
                <div class="card mb-2 border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
                <div class="card mb-0 border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
            </div>
              <div class="col-6 d-grid" >
              <div class="card mb-0 border-dashed pageBuilderSetDesign" id="pageBuilderSetDesign" ondrop="drop(event)" ondragover="allowDrop(event)">

                </div>
              </div>
        </div>
         <div class="text-end p-2">
             <button class="btn btn-sm btn-secondary btn_cancel" id="cancelBtn" type="button">Cancel</button>
                  <button class="btn btn-sm btn-primary btn_save" type="button">Save</button>
            </div>
    `
        }
        else if (layoutName == "PB_layout17") {
        }
        $('#PB_pageContent').append(appendData);
    }
}