﻿@using Microsoft.AspNetCore.Mvc.TagHelpers


<div class="modal-dialog modal-dialog-centered modal-xl">
    <form class="modal-content">
        <div class="modal-header">
            <h6 class="page_title"><i class="cp-configure-dataset"></i><span>DataSet Query List</span></h6>
            <button type="button" title="Close" class="btn-close" data-bs-toggle="modal" data-bs-target="#CreateModal" aria-label="Close"></button>
        </div>
        <div class="modal-body" style="overflow-y: hidden;">

            <div style="overflow:auto;height: 500px;">
                <table class="table table-hover dataTable no-footer" style="width:100%;" id="datasetTable">
                    <thead id="tablerow" class="position-sticky top-0 z-3">
                        <tr>
                        </tr>
                    </thead>
                    <tbody id="tableData">
                    </tbody>
                </table>
                <div id="datasetWrapper"></div>
            </div>
            
        </div>
    </form>
</div>
