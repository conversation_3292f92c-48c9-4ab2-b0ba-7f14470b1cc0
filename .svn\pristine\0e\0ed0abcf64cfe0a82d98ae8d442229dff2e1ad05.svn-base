﻿using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfraObject.Commands.Update;

namespace ContinuityPatrol.Application.Features.User.Commands.Update;

public class UpdateUserCommand : IRequest<UpdateUserResponse>
{
    public string Id { get; set; }

    public string CompanyId { get; set; }

    public string CompanyName { get; set; }

    public string LoginType { get; set; }

    public string LoginName { get; set; }

    public string Role { get; set; }

    public string RoleName { get; set; }

    public bool InfraObjectAllFlag { get; set; }
    public int SessionTimeout { get; set; }
    public bool IsVerify { get; set; }
    public string TwoFactorAuthentication { get; set; }
    public bool IsGroup { get; set; }
    public bool IsDefaultDashboard { get; set; }
    public string Url { get; set; }
    public UpdateUserInfoCommand UserInfoCommand { get; set; }

    public UpdateUserInfraObjectCommand UserInfraObjectCommand { get; set; }

    public override string ToString()
    {
        return $"LoginName: {LoginName}; Id:{Id};";
    }
}