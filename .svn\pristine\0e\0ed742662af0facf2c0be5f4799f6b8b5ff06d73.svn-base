﻿using ContinuityPatrol.Application.Features.TeamResource.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamResource.Queries;

public class GetTeamResourceNameQueryHandlerTests : IClassFixture<TeamResourceFixture>
{
    private readonly TeamResourceFixture _teamResourceFixture;

    private Mock<ITeamResourceRepository> _mockTeamResourceRepository;

    private readonly GetTeamResourceNameQueryHandler _handler;

    public GetTeamResourceNameQueryHandlerTests(TeamResourceFixture teamResourceFixture)
    {
        _teamResourceFixture = teamResourceFixture;

        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetTeamResourceNamesRepository(_teamResourceFixture.TeamResources);

        _handler = new GetTeamResourceNameQueryHandler(_mockTeamResourceRepository.Object, _teamResourceFixture.Mapper);
    }
    [Fact]
    public void Should_Assign_And_Assert_TeamResourceNameVm()
    {
        // Arrange
        var vm = new TeamResourceNameVm
        {
            Id = "TR002",
            TeamMasterId = "TM002",
            TeamMasterName = "Support Team",
            ResourceName = "Arun",
            ResourceId = "R202",
            Email = "<EMAIL>",
            Phone = "9123456789"
        };

        // Assert
        Assert.Equal("TR002", vm.Id);
        Assert.Equal("TM002", vm.TeamMasterId);
        Assert.Equal("Support Team", vm.TeamMasterName);
        Assert.Equal("Arun", vm.ResourceName);
        Assert.Equal("R202", vm.ResourceId);
        Assert.Equal("<EMAIL>", vm.Email);
        Assert.Equal("9123456789", vm.Phone);
    }

    [Fact]
    public async Task Handle_Return_Active_TeamResourcesName()
    {
        var result = await _handler.Handle(new GetTeamResourceNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<TeamResourceNameVm>>();

        result[0].Id.ShouldBe(_teamResourceFixture.TeamResources[0].ReferenceId);
        result[0].TeamMasterId.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterId);
        result[0].TeamMasterName.ShouldBe(_teamResourceFixture.TeamResources[0].TeamMasterName);
        result[0].ResourceName.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceName);
        result[0].ResourceId.ShouldBe(_teamResourceFixture.TeamResources[0].ResourceId);
    }

    [Fact]
    public async Task Handle_Return_Active_TeamResourceNamesCount()
    {
        var result = await _handler.Handle(new GetTeamResourceNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<TeamResourceNameVm>>();
        result.Count.ShouldBe(_teamResourceFixture.TeamResources.Count);
    }


    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockTeamResourceRepository = TeamResourceRepositoryMocks.GetTeamResourceEmptyRepository();

        var handler = new GetTeamResourceNameQueryHandler(_mockTeamResourceRepository.Object, _teamResourceFixture.Mapper);

        var result = await handler.Handle(new GetTeamResourceNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetTeamResourceNameQuery(), CancellationToken.None);

        _mockTeamResourceRepository.Verify(x => x.GetTeamResourceNames(), Times.Once);
    }
}
