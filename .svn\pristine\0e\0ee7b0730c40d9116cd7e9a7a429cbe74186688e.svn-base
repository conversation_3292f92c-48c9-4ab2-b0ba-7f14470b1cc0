﻿using ContinuityPatrol.Application.Features.DynamicSubDashboard.Events.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.DynamicSubDashboard.Events
{
    public class DynamicSubDashboardUpdatedEventHandlerTests
    {
        private readonly Mock<IUserActivityRepository> _userActivityRepoMock;
        private readonly Mock<ILoggedInUserService> _userServiceMock;
        private readonly Mock<ILogger<DynamicSubDashboardUpdatedEventHandler>> _loggerMock;
        private readonly List<Domain.Entities.UserActivity> _userActivities;
        private readonly DynamicSubDashboardUpdatedEventHandler _handler;

        public DynamicSubDashboardUpdatedEventHandlerTests()
        {
            _userActivities = new List<Domain.Entities.UserActivity>();
            _userActivityRepoMock = new Mock<IUserActivityRepository>();
            _userServiceMock = new Mock<ILoggedInUserService>();
            _userActivityRepoMock = DynamicSubDashboardRepositoryMocks.UpdateDynamicSubDashboardEventRepository(_userActivities);
            _loggerMock = new Mock<ILogger<DynamicSubDashboardUpdatedEventHandler>>();

            // Set up mock user details
            _userServiceMock.Setup(x => x.UserId).Returns("user-101");
            _userServiceMock.Setup(x => x.LoginName).Returns("test-user");
            _userServiceMock.Setup(x => x.RequestedUrl).Returns("http://localhost/api/update");
            _userServiceMock.Setup(x => x.IpAddress).Returns("127.0.0.1");

            _handler = new DynamicSubDashboardUpdatedEventHandler(
                _userServiceMock.Object,
                _loggerMock.Object,
                _userActivityRepoMock.Object
            );
        }

        [Fact]
        public async Task Handle_Should_Log_And_AddUserActivity_When_ValidEvent()
        {
            // Arrange
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "Updated-Dashboard" };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "user-101" &&
                ua.LoginName == "test-user" &&
                ua.RequestUrl == "http://localhost/api/update" &&
                ua.HostAddress == "127.0.0.1" &&
                ua.Action == "Update DynamicSubDashboard" &&
                ua.Entity == "DynamicSubDashboard" &&
                ua.ActivityType == ActivityType.Update.ToString() &&
                ua.ActivityDetails.Contains("Updated-Dashboard")
            )), Times.Once);

            _loggerMock.Verify(logger => logger.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((obj, t) => obj.ToString()!.Contains("Updated-Dashboard")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            ), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Handle_MissingUserId_Gracefully()
        {
            // Arrange
            _userServiceMock.Setup(x => x.UserId).Returns(string.Empty);
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "EmptyUserIdTest" };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == string.Empty &&
                ua.ActivityDetails.Contains("EmptyUserIdTest")
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_NotThrow_When_LoggerFails()
        {
            // Arrange
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "LoggerFailTest" };

            _loggerMock.Setup(logger => logger.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.IsAny<It.IsAnyType>(),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            )).Throws(new Exception("Logger failure"));

            // Act & Assert
            var ex = await Record.ExceptionAsync(() => _handler.Handle(updateEvent, CancellationToken.None));
            Assert.Null(ex); // Should not throw even if logger fails
        }

        [Fact]
        public async Task Handle_Should_Handle_Long_Name_Without_Exception()
        {
            // Arrange
            var longName = new string('X', 1000);
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = longName };

            // Act
            var ex = await Record.ExceptionAsync(() => _handler.Handle(updateEvent, CancellationToken.None));

            // Assert
            Assert.Null(ex);
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.ActivityDetails.Contains(longName)
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Set_Correct_Entity_Name()
        {
            // Arrange
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "EntityNameTest" };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.Entity == "DynamicSubDashboard"
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Include_Valid_HostAddress()
        {
            // Arrange
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "HostCheck" };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.HostAddress == "127.0.0.1"
            )), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Copy_LoggedInUserService_Properties()
        {
            // Arrange
            var updateEvent = new DynamicSubDashboardUpdatedEvent { Name = "ServicePropTest" };

            // Act
            await _handler.Handle(updateEvent, CancellationToken.None);

            // Assert
            _userActivityRepoMock.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "user-101" &&
                ua.LoginName == "test-user" &&
                ua.RequestUrl == "http://localhost/api/update" &&
                ua.HostAddress == "127.0.0.1"
            )), Times.Once);
        }
    }
}
