using AutoFixture;
using ContinuityPatrol.Application.Features.UserRole.Queries.GetNames;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class UserRoleFixture : IDisposable
{
    public List<UserRole> UserRolePaginationList { get; set; }
    public List<UserRole> UserRoleList { get; set; }
    public UserRole UserRoleDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string TestRole = "TestRole";
    public const string TestLogo = "test-logo.png";

    public ApplicationDbContext DbContext { get; private set; }

    public UserRoleFixture()
    {
        var fixture = new Fixture();

        UserRoleList = fixture.Create<List<UserRole>>();

        UserRolePaginationList = fixture.CreateMany<UserRole>(20).ToList();

        UserRoleDto = fixture.Create<UserRole>();

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public UserRole CreateUserRole(
        string role = TestRole,
        string logo = TestLogo,
        bool isDelete = false,
        bool isActive = true)
    {
        return new UserRole
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Role = role,
            Logo = logo,
            IsDelete = isDelete,
            IsActive = isActive,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.UtcNow,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.UtcNow
        };
    }

    public List<UserRole> CreateMultipleUserRoles(
        int count,
        bool isActive = true)
    {
        var userRoles = new List<UserRole>();
        for (int i = 1; i <= count; i++)
        {
            userRoles.Add(CreateUserRole(
                role: $"Role_{i:D3}",
                logo: $"logo_{i:D3}.png",
                isDelete: i % 5 == 0, // Every 5th role is marked for deletion
                isActive: isActive
            ));
        }
        return userRoles;
    }

    public UserRole CreateSuperAdminRole()
    {
        return CreateUserRole(
            role: "SuperAdmin",
            logo: "superadmin-logo.png"
        );
    }

    public UserRole CreateAdministratorRole()
    {
        return CreateUserRole(
            role: "Administrator",
            logo: "admin-logo.png"
        );
    }

    public UserRole CreateOperatorRole()
    {
        return CreateUserRole(
            role: "Operator",
            logo: "operator-logo.png"
        );
    }

    public UserRole CreateManagerRole()
    {
        return CreateUserRole(
            role: "Manager",
            logo: "manager-logo.png"
        );
    }

    public UserRole CreateSiteAdminRole()
    {
        return CreateUserRole(
            role: "SiteAdmin",
            logo: "siteadmin-logo.png"
        );
    }

    public List<UserRoleNamesVm> CreateUserRoleNamesVms(int count)
    {
        var userRoleNames = new List<UserRoleNamesVm>();
        for (int i = 1; i <= count; i++)
        {
            userRoleNames.Add(new UserRoleNamesVm
            {
                Id = Guid.NewGuid().ToString(),
                Role = $"Role_{i:D3}"
            });
        }
        return userRoleNames;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
