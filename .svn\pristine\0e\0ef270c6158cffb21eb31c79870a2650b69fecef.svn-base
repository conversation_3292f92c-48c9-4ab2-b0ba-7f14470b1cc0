﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SmtpConfiguration.Commands;

public class DeleteSmtpConfigurationTests : IClassFixture<SmtpConfigurationFixture>
{
    private readonly SmtpConfigurationFixture _smtpConfigurationFixture;

    private readonly Mock<IPublisher> _mockPublisher;

    private readonly Mock<ISmtpConfigurationRepository> _mockSmtpConfigurationRepository;

    private readonly DeleteSmtpConfigurationCommandHandler _handler;

    public DeleteSmtpConfigurationTests(SmtpConfigurationFixture smtpConfigurationFixture)
    {
        _smtpConfigurationFixture = smtpConfigurationFixture;

        _mockPublisher = new Mock<IPublisher>();

        _mockSmtpConfigurationRepository = SmtpConfigurationRepositoryMocks.DeleteSmtpConfigurationRepository(_smtpConfigurationFixture.SmtpConfigurations);

        _handler = new DeleteSmtpConfigurationCommandHandler(_mockSmtpConfigurationRepository.Object, _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateReferenceIdAsyncIsActiveFalse_When_SmtpConfigurationDeleted()
    {
        var validGuid = Guid.NewGuid();

        _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteSmtpConfigurationCommand { Id = validGuid.ToString() }, CancellationToken.None);

        Assert.True(result.Success);

        var smtpConfiguration = await _mockSmtpConfigurationRepository.Object.GetByReferenceIdAsync(_smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId);
        Assert.False(smtpConfiguration.IsActive);
    }

    [Fact]
    public async Task Handle_Return_SuccessfulSmtpConfigurationResponse_When_SmtpConfigurationDeleted()
    {
        var validGuid = Guid.NewGuid();

        _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId = validGuid.ToString();

        var result = await _handler.Handle(new DeleteSmtpConfigurationCommand { Id = validGuid.ToString() }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteSmtpConfigurationResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Return_IsActive_False_When_DeleteReferenceIdAsync_SmtpConfiguration()
    {
        var validGuid = Guid.NewGuid();

        _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId = validGuid.ToString();

        await _handler.Handle(new DeleteSmtpConfigurationCommand { Id = validGuid.ToString() }, CancellationToken.None);

        var smtpConfiguration = await _mockSmtpConfigurationRepository.Object.GetByReferenceIdAsync(_smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId);

        smtpConfiguration.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidSmtpConfigurationId()
    {
        var invalidGuid = Guid.NewGuid().ToString();
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteSmtpConfigurationCommand { Id = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        var validGuid = Guid.NewGuid();

        _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId = validGuid.ToString();

        var smtpConfiguration = _smtpConfigurationFixture.SmtpConfigurations[0];

        var ValidGuid = _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId.ToString();

        _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId = Guid.NewGuid().ToString();

        var result = await _handler.Handle(new DeleteSmtpConfigurationCommand { Id = _smtpConfigurationFixture.SmtpConfigurations[0].ReferenceId }, CancellationToken.None);

        _mockSmtpConfigurationRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockSmtpConfigurationRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.SmtpConfiguration>()), Times.Once);
    }
}