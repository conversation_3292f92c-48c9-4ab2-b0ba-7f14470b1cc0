﻿using ContinuityPatrol.Application.Features.Database.Events.LicenseInfoEvents.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Events.LicenseInfoEvents;

public class DeleteDatabaseLicenseInfoEventTests : IClassFixture<DatabaseFixture>, IClassFixture<LicenseInfoFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly LicenseInfoFixture _licenseInfoFixture;

    private readonly Mock<ILicenseInfoRepository> _mockLicenseInfoRepository;

    private readonly DatabaseLicenseInfoDeletedEventHandler _handler;

    public DeleteDatabaseLicenseInfoEventTests(DatabaseFixture databaseFixture, LicenseInfoFixture licenseInfoFixture)
    {
        _databaseFixture = databaseFixture;
       
        _licenseInfoFixture = licenseInfoFixture;
        
        var mockDatabaseLicenseInfoCreatedEventLogger = new Mock<ILogger<DatabaseLicenseInfoDeletedEventHandler>>();
        
        _mockLicenseInfoRepository = LicenseInfoRepositoryMocks.DeleteDatabaseLicenseInfoEventRepository(_licenseInfoFixture.LicenseInfos);
        
        _handler = new DatabaseLicenseInfoDeletedEventHandler(_mockLicenseInfoRepository.Object, mockDatabaseLicenseInfoCreatedEventLogger.Object);
    }

    [Fact]
    public async Task Handle_DecreaseLicenseInfoCount_When_DeleteDatabaseLicenseInfoEvent()
    {
        _databaseFixture.DatabaseLicenseInfoDeletedEvent.EntityId = _licenseInfoFixture.LicenseInfos[0].EntityId;

        var result = _handler.Handle(_databaseFixture.DatabaseLicenseInfoDeletedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        _databaseFixture.DatabaseLicenseInfoDeletedEvent.EntityId = _licenseInfoFixture.LicenseInfos[0].EntityId;

        await _handler.Handle(_databaseFixture.DatabaseLicenseInfoDeletedEvent, CancellationToken.None);

        _mockLicenseInfoRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.LicenseInfo>()), Times.Once);
    }
}