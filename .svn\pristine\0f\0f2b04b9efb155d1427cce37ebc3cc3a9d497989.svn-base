namespace ContinuityPatrol.Application.Features.BulkImportOperation.Commands.Create;

public class CreateBulkImportOperationCommandValidator : AbstractValidator<CreateBulkImportOperationCommand>
{
    public static string InfraObjectName = string.Empty;
    private readonly IGlobalSettingRepository _globalSettingRepository;

    public CreateBulkImportOperationCommandValidator(IGlobalSettingRepository globalSettingRepository)
    {
        _globalSettingRepository = globalSettingRepository;

        RuleFor(e => e)
            .MustAsync(IsGlobalSettingEnable)
            .WithMessage("The bulk import feature is not enabled in the global settings.");
    }

    private async Task<bool> IsGlobalSettingEnable(CreateBulkImportOperationCommand e, CancellationToken token)
    {
        var globalSetting = await _globalSettingRepository.GlobalSettingBySettingKey("Bulk Import");

        return globalSetting is not null && globalSetting.GlobalSettingValue.Equals("true");
    }
}