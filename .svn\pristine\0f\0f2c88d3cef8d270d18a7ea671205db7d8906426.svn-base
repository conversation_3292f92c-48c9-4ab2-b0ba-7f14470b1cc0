﻿using ContinuityPatrol.Application.Features.DataLag.Queries.GetByBusinessServiceId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DataLagModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.DataLag.Queries
{
    public class GetDataLagDetailByBusinessServiceIdQueryHandlerTests : IClassFixture<DataLagFixture>
    {
        private readonly DataLagFixture _fixture;
        private readonly IMapper _mapper;

        public GetDataLagDetailByBusinessServiceIdQueryHandlerTests(DataLagFixture fixture)
        {
            _fixture = fixture;
            _mapper = fixture.Mapper;
        }

        [Fact]
        public async Task Handle_Should_Return_DataLagListVm_When_Valid_BusinessServiceId()
        {
            // Arrange
            var query = _fixture.GetDataLagDetailByBusinessServiceIdQuery;
            var mockRepo = DataLagRepositoryMocks.GetDataLagByBusinessServiceRepository(_fixture.DataLags);

            var handler = new GetDataLagDetailByBusinessServiceIdQueryHandler(mockRepo.Object, _mapper);

            // Act
            var result = await handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DataLagListVm>(result);
            Assert.Equal(query.BusinessServiceId, result.BusinessServiceId);
        }

        [Fact]
        public async Task Handle_Should_Throw_NotFoundException_When_DataLag_Is_Null()
        {
            // Arrange
            var invalidQuery = new GetDataLagDetailByBusinessServiceIdQuery { BusinessServiceId = "non-existing-id" };
            var mockRepo = DataLagRepositoryMocks.GetDataLagEmptyRepository(); // returns no result

            var handler = new GetDataLagDetailByBusinessServiceIdQueryHandler(mockRepo.Object, _mapper);

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                handler.Handle(invalidQuery, CancellationToken.None));
        }
    }
}
