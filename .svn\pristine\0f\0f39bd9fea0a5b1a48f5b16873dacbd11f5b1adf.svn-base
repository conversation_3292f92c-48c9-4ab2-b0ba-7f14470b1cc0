﻿@using ContinuityPatrol.Domain.Entities
@model ContinuityPatrol.Domain.ViewModels.WorkflowProfileInfoModel.WorkflowProfileInfoViewModel;

<!--Modal Delete-->
<div class="modal-dialog modal-sm modal-dialog-centered" id="deletewfinfo">
    <form asp-controller="WorkflowProfileManagement" asp-action="DeleteWorkflowProfileInfo" asp-route-id="txtWorkflowDeleteId" method="post" enctype="multipart/form-data">
        <div class="modal-content">
            <div class="modal-header p-0">
                <img class="delete-img" src="/img/isomatric/delete.png" />
            </div>
            <div class="modal-body text-center pt-0">
                <h4>Are you sure?</h4>
                <p class="d-flex align-items-center justify-content-center gap-1">You want to remove <span class="font-weight-bolder text-truncate text-primary d-inline-block" style="max-width:100px" id="txtWorkflow"></span> data?</p>
                <input asp-for="Id" type="hidden" id="txtWorkflowDeleteId" name="id" class="form-control" />
            </div>
            <div class="modal-footer gap-2 justify-content-center">
                <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary btn-sm" id="confirmWFProfileDeleteButton">Yes</button>
            </div>
        </div>
    </form>
</div>