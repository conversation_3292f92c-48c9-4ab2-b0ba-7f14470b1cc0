﻿namespace ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;

public class CreateAlertReceiverCommandValidator : AbstractValidator<CreateAlertReceiverCommand>
{
    private readonly IAlertReceiverRepository _alertReceiverRepository;

    public CreateAlertReceiverCommandValidator(IAlertReceiverRepository alertReceiverRepository)
    {
        _alertReceiverRepository = alertReceiverRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([1-9][a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d]|[a-zA-Z]([a-zA-Z\d]+[_\s]?)*[a-zA-Z\d])$")
            .WithMessage("Please enter valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.EmailAddress)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^[^@\s]+@[^@\s]+\.[^@\s]+$")
            .WithMessage("Enter the valid Email Address.");

        RuleFor(p => p.Properties)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Must(prop => IsValidJsonObject(prop))
            .WithMessage("{PropertyName} must be a valid json string.");

        RuleFor(p => p.MobileNumber)
            .Must(mob => string.IsNullOrEmpty(mob) || IsValidMobileNumber(mob))
            .WithMessage("Enter the valid {PropertyName}.");

        RuleFor(e => e)
            .MustAsync(AlertReceiverNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> AlertReceiverNameUnique(CreateAlertReceiverCommand e, CancellationToken token)
    {
        return !await _alertReceiverRepository.IsAlertReceiverNameUnique(e.Name);
    }

    private bool IsValidMobileNumber(string mobileNumber)
    {
        return Regex.IsMatch(mobileNumber, @"^(?:\+(\d{1,3})|00(\d{1,3}))[- ]?(\d{1,4})[- ]?(\d{3,4})[- ]?(\d{4,10})$");
    }

    private static bool IsValidJsonObject(string properties)
    {
        try
        {
            JObject.Parse(properties);
            return true;
        }
        catch
        {
            return false;
        }
    }
}