﻿using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Delete;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetList;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class AlertReceiversController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AlertReceiverListVm>>> GetAlertReceivers()
    {
        Logger.LogDebug("Get All AlertReceivers");

        return Ok(await Mediator.Send(new GetAlertReceiverListQuery()));
    }

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateAlertReceiverResponse>> CreateAlertReceiver([FromBody] CreateAlertReceiverCommand createAlertReceiverCommand)
    {
        Logger.LogDebug($"Create AlertReceiver '{createAlertReceiverCommand.Name}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateAlertReceiver), await Mediator.Send(createAlertReceiverCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateAlertReceiverResponse>> UpdateAlertReceiver([FromBody] UpdateAlertReceiverCommand updateAlertReceieverCommand)
    {
        Logger.LogDebug($"Update AlertReceiver '{updateAlertReceieverCommand.Name}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateAlertReceieverCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteAlertReceiverResponse>> DeleteAlertReceiver(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertReceiver Id");

        Logger.LogDebug($"Delete AlertReceiver Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteAlertReceiverCommand { Id = id }));
    }

    [HttpGet("{id}", Name = "GetAlertReceiver")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<AlertReceiverDetailVm>> GetAlertReceiverById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "AlertReceiver Id");

        Logger.LogDebug($"Get AlertReceiver Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetAlertReceiverDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<AlertReceiverListVm>>> GetPaginatedAlertReceivers([FromQuery] GetAlertReceiverPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in AlertReceiver Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("name-exist"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult> IsAlertReceiverNameExist(string alertReceiverName, string? id)
    {
        Guard.Against.NullOrWhiteSpace(alertReceiverName, "AlertReceiver Name");

        Logger.LogDebug($"Check Name Exists Detail by AlertReceiver Name '{alertReceiverName}' and Id '{id}'");

        return Ok(await Mediator.Send(new GetAlertReceiverNameUniqueQuery { AlertReceiverName = alertReceiverName, AlertReceiverId = id }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys =  { ApplicationConstants.Cache.AllAlertReceiverNameCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}
