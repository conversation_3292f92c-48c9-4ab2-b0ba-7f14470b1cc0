﻿namespace ContinuityPatrol.Application.Features.TableAccess.Queries.GetNameUnique;

public class GetTableAccessNameUniqueQueryHandler : IRequestHandler<GetTableAccessNameUniqueQuery, bool>
{
    private readonly ITableAccessRepository _tableAccessRepository;

    public GetTableAccessNameUniqueQueryHandler(ITableAccessRepository tableAccessRepository)
    {
        _tableAccessRepository = tableAccessRepository;
    }

    public async Task<bool> Handle(GetTableAccessNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _tableAccessRepository.IsTableAccessNameExist(request.TableAccessName, request.TableAccessId);
    }
}