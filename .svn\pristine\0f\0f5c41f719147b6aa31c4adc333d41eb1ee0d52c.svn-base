﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}
<link href="~/css/dashboard.css" rel="stylesheet" />
@Html.AntiForgeryToken()
<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i>
            <span>
                AWS Aurora PostgreSQL Cross Region Replication Monitoring:
                <span id="infraName"></span>
            </span>
        </h6>
        <div class="d-flex align-items-center">
            <span title="Last Monitored Time"><i class="cp-"></i>Last Monitored Time : <span id="modifiedTime"></span></span>
            <a class="btn btn-sm btn-primary ms-2 px-2 py-1 rounded-1" href="#" title="" id="backtoITview"><i class="cp-left-arrow me-1 fs-8"></i>Back</a>
        </div>

    </div>
    <div class="monitor_pages" id="noDataimg">
        <div class="row mb-2 g-2 mt-0 " id="Sitediv">
            <div class="col-12">
                <div class="p-2 bg-white rounded">
                    <ul class="nav nav-underline siteContainer">
                    </ul>
                </div>

            </div>
        </div>
        <div class="row g-2 mt-0">
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">
                        Aurora PostgreSQL Database Global Identifier Monitoring
                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" style="table-layout:fixed">
                            <thead>
                                <tr>
                                    <th colspan="2">Database Global Identifier Database Global Identifier</th>
                                </tr>
                            </thead>
                            <tbody id="globalTable">
                                <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database me-1"></i>Name</td>
                                    <td class="text-truncate"><span id="DBGlobalIdentifierName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database me-1"></i>Engine Name</td>
                                    <td class="text-truncate"><span id="GlobalIdentifierEngineName"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-location me-1"></i>Engine version
                                    </td>
                                    <td class="text-truncate"><span id="GlobalIdentifierEngineVersion"></span></td>
                                </tr>

                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-Job-status me-1"></i>Status
                                    </td>
                                    <td class="text-truncate"><span id="GlobalIdentifierStatus"></span></td>

                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-Job-status me-1"></i>Synchronization Status
                                    </td>
                                    <td class="text-truncate"><span id="GlobalIdentifierSyncStatus"></span></td>

                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-6 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center pt-0">
                        <div id="Solution_Diagram" style="width:100%; height:100%"></div>
                    </div>
                </div>
            </div>
        
        <div class="col-xl-6">
            <div class="card Card_Design_None mb-2 h-100">
                <div class="card-header card-title header">
                    <span>Aurora PostgreSQL Database Instance Identifier Monitoring</span>
                  @*  <div class="input-group" style="width:40%;">
                        <select class="form-select form-select-sm">
                            <option>123</option>
                        </select>
                   </div> *@
                    </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed">
                        <thead class="align-middle">
                            <tr>
                                <th>Database Instance Identifier Details</th>
                                <th class="text-primary">Primary</th>
                                <th class="text-info">Secondary</th>
                            </tr>
                        </thead>
                        <tbody>
                                <tr>
                                    <td class="fw-semibold text-truncate">
                                        <i class="text-secondary cp-database me-1"></i>Name
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table table-sm mb-0 table-bordered">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierName"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierName"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database-role me-1"></i>Role</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierRole"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierRole"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-location me-1"></i>Region</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierRegion"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierRegion"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database-sizes me-1"></i>Size</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierSize"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierSize"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-database me-1"></i>Engine Name</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierEngineName"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierEngineName"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-version me-1"></i>Engine Version</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierEngineVersion"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierEngineVersion"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-endpoint-port-number me-1"></i>Port</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierPort"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierPort"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-Job-status me-1"></i>Parameter Group Status</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierGroupStatus"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierGroupStatus"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate"><i class="text-secondary cp-Job-status me-1"></i>Status</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="PR_InstanceIdentifierStatus"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr id="DR_InstanceIdentifierStatus"></tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                        </tbody>
                       @*  <tbody>
                            <tr>
                                <td class="fw-semibold text-truncate "><i class="text-secondary cp-ip-address me-1"></i>Database Instance Identifier Name</td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr>
                                                    <td>12</td>
                                                    <td>23</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        </td>
                                    <td class="text-truncate p-0">
                                        <table class="table-bordered table table-sm mb-0" style="margin: -1px 0px 0px 0px;">
                                            <tbody>
                                                <tr>
                                                    <td>12</td>
                                                    <td>23</td>
                                                </tr>
                                                
                                            </tbody>
                                        </table>
                                    </td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate "><i class="text-secondary cp-roate-settings me-1"></i>Database Instance Identifier Role</td>
                                    <td class="text-truncate"><span id="InstanceIdentifierRole"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierRole"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-ip-address me-1"></i>Database Instance Identifier Region
                                </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierRegion"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierRegion"></span></td>
                            </tr>

                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Size
                                </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierSize"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierSize"></span></td>
                            </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Engine Name
                                    </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierEngineName"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierEngineName"></span></td>
                                </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Engine Version
                                </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierEngineVersion"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierEngineVersion"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Port
                                </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierPort"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierPort"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Parameter Group Status
                                </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierGroupStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierGroupStatus"></span></td>
                            </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier Status
                                    </td>
                                    <td class="text-truncate"><span id="InstanceIdentifierStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_InstanceIdentifierStatus"></span></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>Database Instance Identifier DataLag(Milliseconds)
                                    </td>
                                    <td class="text-truncate"><span id="DataLag"></span></td>
                                    <td class="text-truncate"></td>
                                </tr>
                        </tbody> *@
                    </table>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card Card_Design_None mb-2 h-100">
                <div class="card-header card-title header">
                    <span>Aurora PostgreSQL Database Cluster Identifier Monitoring</span>
                </div>
                <div class="card-body pt-0 p-2">
                    <table class="table mb-0 noDataimg" style="table-layout:fixed">
                        <thead class="align-middle">
                            <tr>
                                    <th>Database Cluster Identifier</th>
                                <th class="text-primary">Primary</th>
                                <th class="text-info">Secondary</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="fw-semibold text-truncate "><i class="text-secondary cp-database me-1"></i>Name</td>
                                    <td class="text-truncate"><span id="ClusterIdentifierName"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierName"></span></td>
                            </tr>
                            <tr>
                                    <td class="fw-semibold text-truncate "><i class="text-secondary cp-database-role me-1"></i>Role</td>
                                    <td class="text-truncate"><span id="ClusterIdentifierRole"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierRole"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-location me-1"></i>Region
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierRegion"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierRegion"></span></td>
                            </tr>

                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-database me-1"></i>Engine Name
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierEngineName"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierEngineName"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-version me-1"></i>Engine Version
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierEngineVersion"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierEngineVersion"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-endpoint-port-number me-1"></i>Port
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierPort"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierPort"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                    <i class="text-secondary cp-Job-status me-1"></i>Status
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierStatus"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierStatus"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-Job-status me-1"></i>Multi-AZ Enabled
                                </td>
                                    <td class="text-truncate"><span id="ClusterIdentifierMultiAZ"></span></td>
                                    <td class="text-truncate"><span id="DR_ClusterIdentifierMultiAZ"></span></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold text-truncate ">
                                        <i class="text-secondary cp-estimated-time me-1"></i>DataLag (Seconds)
                                </td>
                                    <td class="text-truncate"><span id="DataLag"></span></td>
                                <td class="text-truncate"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
            <div class="col-xl-12">
                <div class="card Card_Design_None mb-0" id="mssqlserver">
                    <div class="card-header card-title d-flex align-items-center justify-content-between">
                        <span title=" Services ">
                            Services
                        </span>

                    </div>
                    <div class="card-body pt-0 p-2">
                        <table class="table mb-0" id="tableCluster" style="table-layout:fixed">
                            <thead class="align-middle">
                                <tr>
                                    <th rowspan="2">Service / Process / Workflow Name</th>
                                    <th colspan="2" class="text-center">Server IP/HostName</th>
                                </tr>
                                <tr>
                                    <th id="prIp"></th>
                                    <th id="drIp"></th>
                                </tr>
                            </thead>

                            <tbody id="mssqlserverbody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/forcedirected.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>
<script src="~/js/Monitoring/MonitoringSolutionDiagram.js"></script>
<script src="~/js/Monitoring/AWSAuroraPostgreSQL.js"></script>
<script src="~/js/Monitoring/MonitoringServiceDetails.js"></script>
