﻿using ContinuityPatrol.Application.Features.InfraMaster.Commands.Create;
using ContinuityPatrol.Application.Features.InfraMaster.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraMaster.Commands
{
    public class CreateInfraMasterCommandHandlerTests
    {
        private readonly Mock<IInfraMasterRepository> _repositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IPublisher> _publisherMock;
        private readonly CreateInfraMasterCommandHandler _handler;

        public CreateInfraMasterCommandHandlerTests()
        {
            _repositoryMock = new Mock<IInfraMasterRepository>();
            _mapperMock = new Mock<IMapper>();
            _publisherMock = new Mock<IPublisher>();

            _handler = new CreateInfraMasterCommandHandler(
                _mapperMock.Object,
                _repositoryMock.Object,
                _publisherMock.Object);
        }

        [Fact]
        public async Task Handle_Should_Add_InfraMaster_And_Return_Response()
        {
            // Arrange
            var command = new CreateInfraMasterCommand
            {
                Name = "TestInfra"
                // Add other necessary fields here
            };

            var entity = new Domain.Entities.InfraMaster
            {
                Name = "TestInfra",
                ReferenceId = "Ref001"
            };

            _mapperMock.Setup(m => m.Map<Domain.Entities.InfraMaster>(command))
                .Returns(entity);

            _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.InfraMaster>()))
                .ReturnsAsync(entity);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Ref001", result.Id);
            Assert.Contains("TestInfra", result.Message);

            _mapperMock.Verify(m => m.Map<Domain.Entities.InfraMaster>(command), Times.Once);
            _repositoryMock.Verify(r => r.AddAsync(It.Is<Domain.Entities.InfraMaster>(i => i.Name == "TestInfra")), Times.Once);
            _publisherMock.Verify(p => p.Publish(It.IsAny<InfraMasterCreatedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_ThrowException_When_AddAsyncReturnsNull()
        {
            // Arrange
            var command = new CreateInfraMasterCommand { Name = "TestInfra" };
            var entity = new Domain.Entities.InfraMaster { Name = "TestInfra" };

            _mapperMock.Setup(m => m.Map<Domain.Entities.InfraMaster>(command)).Returns(entity);
            _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.InfraMaster>())).ReturnsAsync((Domain.Entities.InfraMaster)null!);

            // Act & Assert
            await Assert.ThrowsAsync<NullReferenceException>(() =>
                _handler.Handle(command, CancellationToken.None));

            _mapperMock.Verify(m => m.Map<Domain.Entities.InfraMaster>(command), Times.Once);
            _repositoryMock.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.InfraMaster>()), Times.Once);
        }

        [Fact]
        public async Task Handle_Should_Publish_CorrectInfraMasterCreatedEvent()
        {
            // Arrange
            var command = new CreateInfraMasterCommand { Name = "InfraX" };
            var entity = new Domain.Entities.InfraMaster { Name = "InfraX", ReferenceId = "Ref456" };

            _mapperMock.Setup(m => m.Map<Domain.Entities.InfraMaster>(command)).Returns(entity);
            _repositoryMock.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.InfraMaster>())).ReturnsAsync(entity);

            InfraMasterCreatedEvent? publishedEvent = null;
            _publisherMock.Setup(p => p.Publish(It.IsAny<InfraMasterCreatedEvent>(), It.IsAny<CancellationToken>()))
                .Callback<InfraMasterCreatedEvent, CancellationToken>((ev, _) => publishedEvent = ev)
                .Returns(Task.CompletedTask);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(publishedEvent);
            Assert.Equal("InfraX", publishedEvent.Name);
        }
    }
}
