﻿namespace ContinuityPatrol.Application.Features.ApprovalMatrixTemplate.Command.Create;

public class CreateApprovalMatrixTemplateCommandValidator : AbstractValidator<CreateApprovalMatrixTemplateCommand>
{
    private readonly IApprovalMatrixTemplateRepository _approvalMatrixTemplateRepository;

    public CreateApprovalMatrixTemplateCommandValidator(
        IApprovalMatrixTemplateRepository approvalMatrixTemplateRepository)
    {
        _approvalMatrixTemplateRepository = approvalMatrixTemplateRepository;

        RuleFor(p => p.Name)
            .NotEmpty().WithMessage("{PropertyName} is required.")
            .NotNull()
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters.");

        RuleFor(p => p.Description)
          .MaximumLength(250).WithMessage("{PropertyName} Maximum 250 characters.")
           .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
           .WithMessage("{PropertyName} contains invalid characters.")
           .When(p => p.Description.IsNotNullOrWhiteSpace());


        //RuleFor(p => p.UserName)
        //    .NotEmpty().WithMessage("Select {PropertyName}.")
        //    .Matches(@"^([a-zA-Z]+[_s]?)([a-zA-Zd]+[_\s])*[a-zA-d]+$")
        //   .WithMessage("Please Enter Valid {PropertyName}")
        //   .NotNull();

        //RuleFor(p => p.BusinessServiceName)
        //    .NotEmpty().WithMessage("Select {PropertyName}..")
        //    .NotNull();

        RuleFor(e => e)
            .MustAsync(ApprovalaMatrixTemplateNameUnique)
            .WithMessage("A same name already exists.");
    }

    private async Task<bool> ApprovalaMatrixTemplateNameUnique(
        CreateApprovalMatrixTemplateCommand createApprovalMatrixTemplateCommand,
        CancellationToken token)
    {
        return !await _approvalMatrixTemplateRepository.IsApprovalMatrixNameUnique(createApprovalMatrixTemplateCommand
            .Name);
    }
}