using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class InfraReplicationMappingFixture : IDisposable
{
    public ApplicationDbContext DbContext { get; private set; }
    public List<InfraReplicationMapping> InfraReplicationMappingPaginationList { get; set; }

    public InfraReplicationMappingFixture()
    {
        var fixture = new Fixture();
        DbContext = DbContextFactory.CreateInMemoryDbContext();
        InfraReplicationMappingPaginationList= fixture.CreateMany<InfraReplicationMapping>(20).ToList();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
