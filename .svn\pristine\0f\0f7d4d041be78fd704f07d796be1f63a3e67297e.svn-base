using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DynamicDashboardMapModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class DynamicDashboardMapService : BaseClient, IDynamicDashboardMapService
{
    public DynamicDashboardMapService(IConfiguration config, IAppCache cache, ILogger<DynamicDashboardMapService> logger) : base(config, cache, logger)
    {
    }
   
    public async Task<List<DynamicDashboardMapListVm>> GetDynamicDashboardMapList()
    {
        var request = new RestRequest("api/v6/dynamicdashboardmaps");

        return await GetFromCache<List<DynamicDashboardMapListVm>>(request, "GetDynamicDashboardMapList");
    }

    public async Task<BaseResponse> CreateAsync(CreateDynamicDashboardMapCommand createDynamicDashboardMapCommand)
    {
        var request = new RestRequest("api/v6/dynamicdashboardmaps", Method.Post);

        request.AddJsonBody(createDynamicDashboardMapCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateDynamicDashboardMapCommand updateDynamicDashboardMapCommand)
    {
        var request = new RestRequest("api/v6/dynamicdashboardmaps", Method.Put);

        request.AddJsonBody(updateDynamicDashboardMapCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/dynamicdashboardmaps/{id}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<DynamicDashboardMapDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/dynamicdashboardmaps/{id}");

        return await Get<DynamicDashboardMapDetailVm>(request);
    }
     #region NameExist
  public async Task<bool> IsDynamicDashboardMapNameExist(string name, string? id)
  {
     var request = new RestRequest($"api/v6/dynamicdashboardmaps/name-exist?dynamicdashboardmapName={name}&id={id}");

     return await Get<bool>(request);
  }
    #endregion

    #region Paginated
  public async Task<PaginatedResult<DynamicDashboardMapListVm>> GetPaginatedDynamicDashboardMaps(GetDynamicDashboardMapPaginatedListQuery query)
  {
      var request = new RestRequest("api/v6/dynamicdashboardmaps/paginated-list");

      return await Get<PaginatedResult<DynamicDashboardMapListVm>>(request);
  }

    public async Task<DynamicDashboardMapListVm> GetDefaultDashboardByRoleId(string roleId)
    {
        var request = new RestRequest($"/api/v6/dynamicdashboardmaps/roleid?roleId={roleId}");

        return await Get<DynamicDashboardMapListVm>(request);
    }

    public  async Task<DynamicDashboardMapListVm> GetDefaultDashboardByUserId(string userId)
    {
        var request = new RestRequest($"api/v6/dynamicdashboardmaps/userid?userId={userId}");

        return await Get<DynamicDashboardMapListVm>(request);

      
    }
    #endregion
}
