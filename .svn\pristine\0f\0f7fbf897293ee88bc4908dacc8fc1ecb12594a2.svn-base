﻿namespace ContinuityPatrol.Application.Features.WorkflowHistory.Commands.Update;

public class UpdateWorkflowHistoryCommand : IRequest<UpdateWorkflowHistoryResponse>
{
    public string Id { get; set; }
    public string CompanyId { get; set; }
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string LoginName { get; set; }
    public string Properties { get; set; }
    public string UpdaterId { get; set; }
    public string Version { get; set; }
    public string Description { get; set; }
    public string Comments { get; set; }

    public override string ToString()
    {
        return $"Workflow Name: {WorkflowName}; Id:{Id};";
    }
}