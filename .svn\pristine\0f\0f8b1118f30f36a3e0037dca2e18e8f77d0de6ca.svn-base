﻿using ContinuityPatrol.Application.Features.ApprovalMatrixUsers.Events.Update;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.ApprovalMatrixUsers.Events;

public class ApprovalMatrixUsersUpdatedEventHandlerTests
{
    private readonly Mock<ILogger<ApprovalMatrixUsersUpdatedEventHandler>> _loggerMock;
    private readonly Mock<IUserActivityRepository> _userActivityRepositoryMock;
    private readonly Mock<ILoggedInUserService> _userServiceMock;
    private readonly ApprovalMatrixUsersUpdatedEventHandler _handler;

    public ApprovalMatrixUsersUpdatedEventHandlerTests()
    {
        _loggerMock = new Mock<ILogger<ApprovalMatrixUsersUpdatedEventHandler>>();
        _userActivityRepositoryMock = new Mock<IUserActivityRepository>();
        _userServiceMock = new Mock<ILoggedInUserService>();

        _handler = new ApprovalMatrixUsersUpdatedEventHandler(
            _userServiceMock.Object,
            _loggerMock.Object,
            _userActivityRepositoryMock.Object
        );
    }
    [Fact]
    public async Task Handle_Should_Add_UserActivity()
    {
        // Arrange
        _userServiceMock.SetupGet(x => x.UserId).Returns("U1");
        _userServiceMock.SetupGet(x => x.LoginName).Returns("login");
        _userServiceMock.SetupGet(x => x.RequestedUrl).Returns("/url");
        _userServiceMock.SetupGet(x => x.IpAddress).Returns("127.0.0.1");

        _userActivityRepositoryMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(new Domain.Entities.UserActivity());

        var updateEvent = new ApprovalMatrixUsersUpdatedEvent { UserName = "Alice" };

        // Act
        await _handler.Handle(updateEvent, CancellationToken.None);

        // Assert
        _userActivityRepositoryMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }
    [Fact]
    public async Task Handle_Should_Log_Information()
    {
        var updateEvent = new ApprovalMatrixUsersUpdatedEvent { UserName = "Bob" };

        _userServiceMock.SetupGet(x => x.UserId).Returns("1");

        _userActivityRepositoryMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ReturnsAsync(new Domain.Entities.UserActivity());

        // Act
        await _handler.Handle(updateEvent, CancellationToken.None);

        // Assert
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((o, _) => o.ToString().Contains("updated successfully")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once
        );
    }
    [Fact]
    public async Task Handle_Should_Use_Null_UserId_Without_Exception()
    {
        var evt = new ApprovalMatrixUsersUpdatedEvent { UserName = "NullIdUser" };

        _userServiceMock.SetupGet(x => x.UserId).Returns((string)null);
        _userServiceMock.SetupGet(x => x.LoginName).Returns("ln");
        _userServiceMock.SetupGet(x => x.RequestedUrl).Returns("/url");
        _userServiceMock.SetupGet(x => x.IpAddress).Returns("*******");

        _userActivityRepositoryMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ReturnsAsync(new Domain.Entities.UserActivity());

        var act = () => _handler.Handle(evt, CancellationToken.None);

        await act.Should().NotThrowAsync();
    }
    [Fact]
    public async Task Handle_Should_Set_ActivityType_To_Update()
    {
        Domain.Entities.UserActivity? capturedActivity = null;

        _userServiceMock.SetupGet(x => x.UserId).Returns("UID");

        _userActivityRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(a => capturedActivity = a)
            .ReturnsAsync(new Domain.Entities.UserActivity());

        var evt = new ApprovalMatrixUsersUpdatedEvent { UserName = "TestUser" };

        // Act
        await _handler.Handle(evt, CancellationToken.None);

        // Assert
        capturedActivity!.ActivityType.Should().Be(ActivityType.Update.ToString());
    }
    [Fact]
    public async Task Handle_Should_Set_ActivityDetails_Message()
    {
        Domain.Entities.UserActivity? act = null;

        _userServiceMock.SetupGet(x => x.UserId).Returns("ID");

        _userActivityRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(a => act = a)
            .ReturnsAsync(new Domain.Entities.UserActivity());

        var evt = new ApprovalMatrixUsersUpdatedEvent { UserName = "XYZ" };

        // Act
        await _handler.Handle(evt, CancellationToken.None);

        // Assert
        act!.ActivityDetails.Should().Be("ApprovalMatrixUsers 'XYZ' updated successfully.");
    }
    [Fact]
    public async Task Handle_Should_Set_Entity_To_ApprovalMatrixUsers()
    {
        Domain.Entities.UserActivity? captured = null;

        _userServiceMock.SetupGet(x => x.UserId).Returns("U");

        _userActivityRepositoryMock
            .Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .Callback<Domain.Entities.UserActivity>(x => captured = x)
            .ReturnsAsync(new Domain.Entities.UserActivity());

        await _handler.Handle(new ApprovalMatrixUsersUpdatedEvent { UserName = "check" }, CancellationToken.None);

        captured!.Entity.Should().Be("ApprovalMatrixUsers");
    }
    [Fact]
    public async Task Handle_Should_Handle_Multiple_Events()
    {
        _userServiceMock.SetupGet(x => x.UserId).Returns("test");

        _userActivityRepositoryMock.Setup(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>())).ReturnsAsync(new Domain.Entities.UserActivity());

        var evt1 = new ApprovalMatrixUsersUpdatedEvent { UserName = "user1" };
        var evt2 = new ApprovalMatrixUsersUpdatedEvent { UserName = "user2" };

        await _handler.Handle(evt1, CancellationToken.None);
        await _handler.Handle(evt2, CancellationToken.None);

        _userActivityRepositoryMock.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Exactly(2));
    }
}
