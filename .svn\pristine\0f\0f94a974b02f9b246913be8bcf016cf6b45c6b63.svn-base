﻿using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Create;
using ContinuityPatrol.Application.Features.IncidentLogs.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.IncidentLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Mappings;

public class IncidentLogsProfile : Profile
{
    public IncidentLogsProfile()
    {
        CreateMap<IncidentLogs, CreateIncidentLogsCommand>().ReverseMap();
        CreateMap<UpdateIncidentLogsCommand, IncidentLogs>().ForMember(dest => dest.Id, src => src.Ignore());

        CreateMap<IncidentLogs, IncidentLogsDetailVm>()
            .ForMember(desc => desc.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<IncidentLogs, IncidentLogsListVm>()
            .ForMember(desc => desc.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<PaginatedResult<IncidentLogs>,PaginatedResult<IncidentLogsListVm>>()
            .ForMember(dest => dest.Data, opt => opt.MapFrom(src => src.Data));
    }
}