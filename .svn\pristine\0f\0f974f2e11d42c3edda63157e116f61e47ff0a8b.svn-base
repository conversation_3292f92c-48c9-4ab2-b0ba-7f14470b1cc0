﻿using ContinuityPatrol.Application.Features.Database.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.DatabaseModel;

namespace ContinuityPatrol.Application.UnitTests.Features.Database.Queries;

public class GetDatabaseNameQueryHandlerTests : IClassFixture<DatabaseFixture>, IClassFixture<InfraObjectFixture>
{
    private readonly DatabaseFixture _databaseFixture;

    private readonly InfraObjectFixture _infraObjectFixture;

    private Mock<IDatabaseRepository> _mockDatabaseRepository;

    private readonly GetDatabaseNameQueryHandler _handler;

    public GetDatabaseNameQueryHandlerTests(DatabaseFixture databaseFixture, InfraObjectFixture infraObjectFixture)
    {
        _databaseFixture = databaseFixture;

        _infraObjectFixture = infraObjectFixture;

        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseNamesRepository(_databaseFixture.Databases);

        _handler = new GetDatabaseNameQueryHandler(_databaseFixture.Mapper, _mockDatabaseRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_Databases_Name()
    {
        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
       // _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseNameVm>>();

        result[0].Id.ShouldBe(_databaseFixture.Databases[0].ReferenceId);
        result[0].Name.ShouldBe(_databaseFixture.Databases[0].Name);
    }

    [Fact]
    public async Task Handle_Return_Active_DatabaseNamesCount()
    {
        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
      //  _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        var result = await _handler.Handle(new GetDatabaseNameQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DatabaseNameVm>>();

        result.Count.ShouldBeGreaterThan(0);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _databaseFixture.Databases[0].ReferenceId = "5c84cef9-c242-498d-8e79-9b13256c6459";
    //    _infraObjectFixture.InfraObjects[0].DRDatabaseId = _databaseFixture.Databases[0].ReferenceId;

        _mockDatabaseRepository = DatabaseRepositoryMocks.GetDatabaseEmptyRepository();

        var handler = new GetDatabaseNameQueryHandler(_databaseFixture.Mapper, _mockDatabaseRepository.Object);

        var result = await handler.Handle(new GetDatabaseNameQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetDatabaseNameQuery(), CancellationToken.None);

        _mockDatabaseRepository.Verify(x => x.GetDatabaseNames(), Times.Once);
    }
}