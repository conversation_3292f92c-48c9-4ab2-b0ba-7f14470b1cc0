﻿using ContinuityPatrol.Application.Features.WorkflowAction.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowAction.Queries;

public class GetWorkflowActionNameUniqueQueryHandlerTests : IClassFixture<WorkflowActionFixture>
{
    private readonly WorkflowActionFixture _workflowActionFixture;

    private Mock<IWorkflowActionRepository> _mockWorkflowActionRepository;

    private readonly GetWorkflowActionNameUniqueQueryHandler _handler;

    public GetWorkflowActionNameUniqueQueryHandlerTests(WorkflowActionFixture workflowActionFixture)
    {
        _workflowActionFixture = workflowActionFixture;

        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.GetWorkflowActionNameUniqueRepository(_workflowActionFixture.WorkflowActions);

        _handler = new GetWorkflowActionNameUniqueQueryHandler(_mockWorkflowActionRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_WorkflowActionName_Exist()
    {
        _workflowActionFixture.WorkflowActions[0].ActionName = "WorkflowActions_Name";

        var result = await _handler.Handle(new GetWorkflowActionNameUniqueQuery() { WorkflowActionName = _workflowActionFixture.WorkflowActions[0].ActionName, WorkflowActionId = _workflowActionFixture.WorkflowActions[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowActionNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionNameUniqueQuery { WorkflowActionName = "Demo_ERA", WorkflowActionId = _workflowActionFixture.WorkflowActions[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsWorkflowActionNameExist_OneTime()
    {
        await _handler.Handle(new GetWorkflowActionNameUniqueQuery(), CancellationToken.None);

        _mockWorkflowActionRepository.Verify(x => x.IsWorkflowActionNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_WorkflowActionName_NotMatch()
    {
        var result = await _handler.Handle(new GetWorkflowActionNameUniqueQuery { WorkflowActionName = "Action_Test", WorkflowActionId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowActionRepository = WorkflowActionRepositoryMocks.GetWorkflowActionEmptyRepository();

        var result = await _handler.Handle(new GetWorkflowActionNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}