﻿@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration
@{
    ViewData["Title"] = "Logout";
    Layout = "~/Views/Shared/_EmptyLayout.cshtml";
}
<body class="vh-100 d-flex align-items-center justify-content-center" style="background: #fafafa!important;">
    <div class="content-center w-100">
        <div class="container">
            <div class="row text-center">
                <div class="col-lg-12 mb-3">
                    <img alt="Logo" src="~/img/logo/cplogo.svg" width="320" title="Logo" class="">
                </div>
                <div class="col-lg-12 my-2">
                    <img src="~/img/isomatric/logout-img.svg" alt="logout" class="img-fluid">
                </div>
                <div class="col-lg-12 ml-auto">
                    <div class="ex-page-content">
                        <h6 class="my-3 text-dark">You have been successfully signed out</h6>
                        <p>To Login back <a asp-action="Login"> Click Here </a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="text-center fixed-bottom p-1" style="font-size:11px">
        @{
            var version = Configuration.GetValue<string>("CP:Version");
            var isCOE = Configuration.GetValue<string>("Release:isCOE");
        }
        @if (@isCOE != null)
        {
            <small>Continuity Patrol Version <span>@version</span> | <span class="fw-bold">CoE Release</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
        }
        else
        {
            <small>Continuity Patrol Version <span>@version</span> | © @($"{DateTime.Now.Year}-{DateTime.Now.Year + 1}") <a href="https://ptechnosoft.com/" target="_blank">Perpetuuiti </a> - All Rights Reserved.</small>
        }
    </footer>
</body>

