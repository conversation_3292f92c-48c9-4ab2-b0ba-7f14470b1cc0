using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Create;
using ContinuityPatrol.Application.Features.VeritasCluster.Commands.Update;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.VeritasCluster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.VeritasClusterModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class VeritasClusterFixture
{
    public List<VeritasClusterListVm> VeritasClusterListVm { get; }
    public VeritasClusterDetailVm VeritasClusterDetailVm { get; }
    public CreateVeritasClusterCommand CreateVeritasClusterCommand { get; }
    public UpdateVeritasClusterCommand UpdateVeritasClusterCommand { get; }
    public GetVeritasClusterPaginatedListQuery GetVeritasClusterPaginatedListQuery { get; }
    public PaginatedResult<VeritasClusterListVm> PaginatedVeritasClusterListVm { get; }

    public VeritasClusterFixture()
    {
        var fixture = new Fixture();

        VeritasClusterListVm = fixture.Create<List<VeritasClusterListVm>>();
        VeritasClusterDetailVm = fixture.Create<VeritasClusterDetailVm>();
        CreateVeritasClusterCommand = fixture.Create<CreateVeritasClusterCommand>();
        UpdateVeritasClusterCommand = fixture.Create<UpdateVeritasClusterCommand>();
        GetVeritasClusterPaginatedListQuery = fixture.Create<GetVeritasClusterPaginatedListQuery>();
        PaginatedVeritasClusterListVm = fixture.Create<PaginatedResult<VeritasClusterListVm>>();
    }

    public void Dispose()
    {

    }
}
