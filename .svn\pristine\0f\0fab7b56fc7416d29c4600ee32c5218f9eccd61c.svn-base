﻿//using ContinuityPatrol.Admin.Core.Contracts;
//using ContinuityPatrol.Admin.Core.Features.LicenseManager.Command.BaseLicense.Delete;
//using ContinuityPatrol.Admin.Core.UnitTests.Fixtures;
//using ContinuityPatrol.Admin.Core.UnitTests.Mocks;

//namespace ContinuityPatrol.Admin.Core.UnitTests.Domains.LicenseManager.Commands;

//public class DeleteBaseLicenseTests : IClassFixture<LicenseManagerFixture>
//{
//    private readonly LicenseManagerFixture _licenseManagerFixture;
//    private readonly Mock<ILicenseManagerRepository> _mockLicenseManagerRepository;
//    private readonly DeleteBaseLicenseCommandHandler _handler;

//    public DeleteBaseLicenseTests(LicenseManagerFixture licenseManagerFixture)
//    {
//        _licenseManagerFixture = licenseManagerFixture;

//        var mockPublisher = new Mock<IPublisher>();

//        _mockLicenseManagerRepository = LicenseManagerRepositoryMocks.DeleteLicenseManagerRepository(_licenseManagerFixture.LicenseManagers);

//        _handler = new DeleteBaseLicenseCommandHandler(_mockLicenseManagerRepository.Object, mockPublisher.Object);

//    }

//    [Fact]
//    public async Task Handle_UpdateIsActiveFalse_When_LicenseManagerDeleted()
//    {
//        var result = await _handler.Handle(new DeleteBaseLicenseCommand { Id = _licenseManagerFixture.LicenseManagers[0].ReferenceId }, CancellationToken.None);

//        result.IsActive.ShouldBeFalse();
//    }

//    [Fact]
//    public async Task Handle_Return_DeleteLicenseManagerResponse_When_LicenseDeleted()
//    {
//        var result = await _handler.Handle(new DeleteBaseLicenseCommand { Id = _licenseManagerFixture.LicenseManagers[0].ReferenceId }, CancellationToken.None);

//        result.ShouldBeOfType(typeof(DeleteBaseLicenseResponse));

//        result.IsActive.ShouldBeFalse();

//        result.Success.ShouldBeTrue();

//        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
//    }

//    [Fact]
//    public async Task Handle_ReturnIsActive_False_WhenDeleteLicenseManager()
//    {
//        await _handler.Handle(new DeleteBaseLicenseCommand { Id = _licenseManagerFixture.LicenseManagers[0].ReferenceId }, CancellationToken.None);

//        var licenseManager = await _mockLicenseManagerRepository.Object.GetBaseLicenseByParentIdAsync(_licenseManagerFixture.LicenseManagers[0].ReferenceId);

//        licenseManager.IsActive.ShouldBeFalse();
//    }

//    [Fact]
//    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
//    {
//        await _handler.Handle(new DeleteBaseLicenseCommand { Id = _licenseManagerFixture.LicenseManagers[0].ReferenceId }, CancellationToken.None);

//        _mockLicenseManagerRepository.Verify(x => x.GetBaseLicenseByParentIdAsync(It.IsAny<string>()), Times.Once);

//        _mockLicenseManagerRepository.Verify(x => x.UpdateAsync(It.IsAny<Entities.LicenseManager>()), Times.Once);
//    }
//}