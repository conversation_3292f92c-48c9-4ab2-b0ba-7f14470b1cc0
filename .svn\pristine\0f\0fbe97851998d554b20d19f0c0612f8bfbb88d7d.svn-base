﻿using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class DatabaseFilterSpecification : Specification<DatabaseView>
{
    public DatabaseFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("server=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.ServerName.Contains(stringItem.Replace("server=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("name=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.Name.Contains(stringItem.Replace("name=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("type=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.Type.Contains(stringItem.Replace("type=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("database=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.DatabaseType.Contains(stringItem.Replace("database=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("mode=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.ModeType.Contains(stringItem.Replace("mode=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }
                    else if (stringItem.StartsWith("instance=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.InstanceName.Contains(stringItem.Replace("instance=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("version=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.Version.Contains(stringItem.Replace("version=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("sid=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.SID.Contains(stringItem.Replace("sid=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }

                    else if (stringItem.StartsWith("port=", StringComparison.InvariantCultureIgnoreCase))
                    {
                        Or(p => p.Port.Contains(stringItem.Replace("port=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    }
            }
            else
            {
                Criteria = p =>
                    p.Name.Contains(searchString) || p.ServerName.Contains(searchString) ||
                    p.DatabaseType.Contains(searchString) || p.ModeType.Contains(searchString) ||
                    p.SID.Contains(searchString) || p.InstanceName.Contains(searchString) ||
                    p.Port.Contains(searchString) || p.Version.Contains(searchString);
            }
        }
        else
        {
            Criteria = p => p.Name != null;
        }
    }

}