﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class RpForVmCGMonitorStatusSpecification:Specification<RpForVmCGMonitorStatus> 
{

    public RpForVmCGMonitorStatusSpecification( string searchString)
    {

        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.ConsistencyGroupName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("cgname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.ConsistencyGroupName.Contains(stringItem.Replace("sgname=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("state=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.State.Contains(stringItem.Replace("state=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("transferstatus=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.TransferStatus.Contains(stringItem.Replace("transferstatus=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("availabilitystatus=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.AvailabilityStatus.Contains(stringItem.Replace("availabilitystatus=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.ConsistencyGroupName.Contains(searchString) || p.TransferStatus.Contains(searchString) || p.AvailabilityStatus.Contains(searchString)
                    || p.State.Contains(searchString);
            }
        }
    }
}
