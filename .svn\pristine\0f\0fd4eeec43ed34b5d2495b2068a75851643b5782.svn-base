﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IPluginManagerHistoryRepository : IRepository<PluginManagerHistory>
{
    Task<List<PluginManagerHistory>> GetPluginManagerHistoryNames();
    Task<bool> IsPluginManagerHistoryNameUnique(string name);
    Task<bool> IsPluginManagerHistoryNameExist(string name, string id);
    Task<List<PluginManagerHistory>> GetPluginManagerHistoryByPluginManagerId(string pluginId);
}