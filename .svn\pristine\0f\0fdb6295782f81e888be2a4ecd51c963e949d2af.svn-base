﻿using ContinuityPatrol.Application.Features.AlertMaster.Commands.Create;
using ContinuityPatrol.Application.Features.AlertMaster.Commands.Update;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertId;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetAlertMasterByAlertName;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.AlertMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.AlertMasterModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IAlertMasterService
{
    Task<BaseResponse> CreateAsync(CreateAlertMasterCommand  createAlertMasterCommand);
    Task<BaseResponse> UpdateAsync(UpdateAlertMasterCommand updateAlertMasterCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<List<AlertMasterListVm>> GetAlertMastersList();
    Task<AlertMasterDetailVm> GetAlertMasterById(string id);
    Task<PaginatedResult<AlertMasterListVm>> GetPaginatedAlertMasters(GetAlertMasterPaginatedListQuery query);
    Task<List<AlertMasterByAlertNameVm>> GetAlertMasterByAlertName(string alertName);
    Task<List<AlertMasterByAlertIdVm>> GetAlertMasterByAlertId(string alertId);
    Task<bool> IsAlertMasterNameExist(string alertName, string alertId);
    Task<bool> IsAlertMasterIdExist(string alertId);
}
