﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.SVCGMMonitoringLogs.Queries.GetDetail;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringLogs.Queries
{
    public class GetSVCGMMonitorLogDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorLogRepository> _mockSVCGMMonitorLogRepository;
        private readonly GetSVCGMMonitorLogDetailQueryHandler _handler;

        public GetSVCGMMonitorLogDetailQueryHandlerTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorLogRepository = new Mock<ISVCGMMonitorLogRepository>();
            _handler = new GetSVCGMMonitorLogDetailQueryHandler(_mockSVCGMMonitorLogRepository.Object, _mockMapper.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedDetail_WhenDataExists()
        {
            var query = new GetSVCGMMonitorLogDetailQuery { Id = "valid-id" };

            var mockEntity = new SVCGMMonitorLog { Id = 1, ReferenceId = "valid-id", IsActive = true };
            var mockVm = new SVCGMMonitorLogDetailVm { Id = Guid.NewGuid().ToString() };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(mockEntity);

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(mockEntity))
                .Returns(mockVm);

            var result = await _handler.Handle(query, CancellationToken.None);

            Assert.NotNull(result);
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(mockEntity), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenRepositoryReturnsNull()
        {
            var query = new GetSVCGMMonitorLogDetailQuery { Id = "invalid-id" };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync((SVCGMMonitorLog)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(It.IsAny<SVCGMMonitorLog>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsDeactivated()
        {
            var query = new GetSVCGMMonitorLogDetailQuery { Id = "deactivated-id" };

            var mockEntity = new SVCGMMonitorLog { Id = 2, ReferenceId = "deactivated-id", IsActive = false };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(mockEntity);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));
            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(It.IsAny<SVCGMMonitorLog>()), Times.Never);
        }
        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenMappingReturnsNull()
        {
            // Arrange
            var query = new GetSVCGMMonitorLogDetailQuery { Id = "valid-id" };
            var entity = new SVCGMMonitorLog { Id = 4, ReferenceId = "valid-id", IsActive = true };

            _mockSVCGMMonitorLogRepository.Setup(repo => repo.GetByReferenceIdAsync(query.Id))
                .ReturnsAsync(entity);

            _mockMapper.Setup(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(entity))
                .Returns((SVCGMMonitorLogDetailVm)null); // simulate failed mapping

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(query, CancellationToken.None));

            _mockSVCGMMonitorLogRepository.Verify(repo => repo.GetByReferenceIdAsync(query.Id), Times.Once);
            _mockMapper.Verify(mapper => mapper.Map<SVCGMMonitorLogDetailVm>(entity), Times.Once);
        }
        [Fact]
        public void GetSVCGMMonitorLogDetailByTypeQuery_ShouldAssignTypeCorrectly()
        {
            // Arrange
            var query = new GetSVCGMMonitorLogDetailQuery
            {
                Id = "Critical"
            };

            // Assert
            Assert.Equal("Critical", query.Id);
        }
        [Fact]
        public void SVCGMMonitorLogDetailVm_ShouldAssignAndReturnAllPropertiesCorrectly()
        {
            // Arrange
            var vm = new SVCGMMonitorLogDetailVm
            {
                Id = "log-001",
                Type = "Warning",
                InfraObjectId = "infra-789",
                InfraObjectName = "VM-Node-2",
                WorkflowId = "wf-321",
                WorkflowName = "ReplicationCheck",
                Properties = "{ \"status\": \"ok\" }",
                ConfiguredRPO = "10 mins",
                DataLagValue = "8 mins"
            };

            // Assert
            Assert.Equal("log-001", vm.Id);
            Assert.Equal("Warning", vm.Type);
            Assert.Equal("infra-789", vm.InfraObjectId);
            Assert.Equal("VM-Node-2", vm.InfraObjectName);
            Assert.Equal("wf-321", vm.WorkflowId);
            Assert.Equal("ReplicationCheck", vm.WorkflowName);
            Assert.Equal("{ \"status\": \"ok\" }", vm.Properties);
            Assert.Equal("10 mins", vm.ConfiguredRPO);
            Assert.Equal("8 mins", vm.DataLagValue);
        }
    }
}
