﻿namespace ContinuityPatrol.Application.Features.RpoSlaDeviationReport.Commands.Update;

public class UpdateRpoSlaDeviationReportCommandHandler : IRequestHandler<UpdateRpoSlaDeviationReportCommand,
    UpdateRpoSlaDeviationReportResponse>
{
    private readonly IMapper _mapper;
    private readonly IRpoSlaDeviationReportRepository _rpoSlaDeviationReportRepository;

    public UpdateRpoSlaDeviationReportCommandHandler(IRpoSlaDeviationReportRepository rpoSlaDeviationReportRepository,
        IMapper mapper)
    {
        _rpoSlaDeviationReportRepository = rpoSlaDeviationReportRepository;
        _mapper = mapper;
    }

    public async Task<UpdateRpoSlaDeviationReportResponse> Handle(UpdateRpoSlaDeviationReportCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _rpoSlaDeviationReportRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.RpoSlaDeviationReport), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateRpoSlaDeviationReportCommand),
            typeof(Domain.Entities.RpoSlaDeviationReport));

        await _rpoSlaDeviationReportRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateRpoSlaDeviationReportResponse
        {
            Message = Message.Update(nameof(Domain.Entities.RpoSlaDeviationReport), eventToUpdate.InfraObjectName),

            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}