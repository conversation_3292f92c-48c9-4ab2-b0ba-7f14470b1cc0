namespace ContinuityPatrol.Application.Features.DriftParameter.Queries.GetNameUnique;

public class GetDriftParameterNameUniqueQueryHandler : IRequestHandler<GetDriftParameterNameUniqueQuery, bool>
{
    private readonly IDriftParameterRepository _driftParameterRepository;

    public GetDriftParameterNameUniqueQueryHandler(IDriftParameterRepository driftParameterRepository)
    {
        _driftParameterRepository = driftParameterRepository;
    }

    public async Task<bool> Handle(GetDriftParameterNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _driftParameterRepository.IsNameExist(request.Name, request.Id);
    }
}