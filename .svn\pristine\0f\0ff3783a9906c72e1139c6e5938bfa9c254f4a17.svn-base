using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Delete;
using ContinuityPatrol.Application.Features.WorkflowOperation.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDescriptionByStartTimeAndEndTime;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetDrDrillByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetProfileExecutorByBusinessServiceId;
using ContinuityPatrol.Application.Features.WorkflowOperation.Queries.GetRunningUserList;
using ContinuityPatrol.Domain.ViewModels.WorkflowOperationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class WorkflowOperationControllerTests : IClassFixture<WorkflowOperationFixture>
{
    private readonly WorkflowOperationFixture _workflowOperationFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly WorkflowOperationController _controller;

    public WorkflowOperationControllerTests(WorkflowOperationFixture workflowOperationFixture)
    {
        _workflowOperationFixture = workflowOperationFixture;
        
        var testBuilder = new ControllerTestBuilder<WorkflowOperationController>();
        _controller = testBuilder.CreateController(
            _ => new WorkflowOperationController(),
            out _mediatorMock);
    }

    #region GetWorkflowOperationList Tests

    [Fact]
    public async Task GetWorkflowOperationList_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowOperationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_workflowOperationFixture.WorkflowOperationListVm);

        // Act
        var result = await _controller.GetWorkflowOperationList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var workflowOperations = Assert.IsAssignableFrom<List<WorkflowOperationListVm>>(okResult.Value);
        Assert.Equal(3, workflowOperations.Count);
        Assert.Contains(workflowOperations, wo => wo.ProfileName == "Production Backup Profile");
        Assert.Contains(workflowOperations, wo => wo.Status == "Running");
    }

    [Fact]
    public async Task GetWorkflowOperationList_ReturnsEmptyList_WhenNoOperationsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowOperationListQuery>(), default))
            .ReturnsAsync(new List<WorkflowOperationListVm>());

        // Act
        var result = await _controller.GetWorkflowOperationList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<WorkflowOperationListVm>)okResult.Value!));
    }

    #endregion

    #region GetWorkflowOperationById Tests

    [Fact]
    public async Task GetWorkflowOperationById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _workflowOperationFixture.WorkflowOperationDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetWorkflowOperationDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetWorkflowOperationById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<WorkflowOperationDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.ProfileName, returnedDetail.ProfileName);
        Assert.Equal(expectedDetail.Status, returnedDetail.Status);
        Assert.Equal(expectedDetail.Description, returnedDetail.Description);
    }

    [Fact]
    public async Task GetWorkflowOperationById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetWorkflowOperationById("invalid-guid"));
    }

    [Fact]
    public async Task GetWorkflowOperationById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetWorkflowOperationDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("WorkflowOperation", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetWorkflowOperationById(nonExistentId));
    }

    #endregion

    #region CreateWorkflowOperation Tests

    [Fact]
    public async Task CreateWorkflowOperation_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _workflowOperationFixture.CreateWorkflowOperationCommand;
        var expectedResponse = _workflowOperationFixture.CreateWorkflowOperationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateWorkflowOperation(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateWorkflowOperationResponse>(createdResult.Value);
        Assert.Equal("WorkflowOperation created successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.WorkflowOperationId);
    }

    //[Fact]
    //public async Task CreateWorkflowOperation_WithInvalidCommand_ThrowsException()
    //{
    //    // Arrange
    //    var invalidCommand = new CreateWorkflowOperationCommand
    //    {
    //        ProfileId = "", // Invalid empty profile ID
    //        ProfileName = "",
    //        Status = "",
    //        Description = ""
    //    };

    //    _mediatorMock
    //        .Setup(m => m.Send(invalidCommand, default))
    //        .ThrowsAsync(new ValidationException("Invalid workflow operation data"));

    //    // Act & Assert
    //    await Assert.ThrowsAsync<ValidationException>(() =>
    //        _controller.CreateWorkflowOperation(invalidCommand));
    //}

    #endregion

    #region DeleteWorkflowOperation Tests

    [Fact]
    public async Task DeleteWorkflowOperation_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _workflowOperationFixture.DeleteWorkflowOperationResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteWorkflowOperationCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteWorkflowOperation(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteWorkflowOperationResponse>(okResult.Value);
        Assert.Equal("WorkflowOperation deleted successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteWorkflowOperation_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteWorkflowOperation("invalid-guid"));
    }

    [Fact]
    public async Task DeleteWorkflowOperation_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteWorkflowOperation(""));
    }

    [Fact]
    public async Task DeleteWorkflowOperation_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteWorkflowOperationCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("WorkflowOperation", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteWorkflowOperation(nonExistentId));
    }

    #endregion

    #region UpdateWorkflowOperation Tests

    [Fact]
    public async Task UpdateWorkflowOperation_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _workflowOperationFixture.UpdateWorkflowOperationCommand;
        var expectedResponse = _workflowOperationFixture.UpdateWorkflowOperationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateWorkflowOperation(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateWorkflowOperationResponse>(okResult.Value);
        Assert.Equal("WorkflowOperation updated successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateWorkflowOperation_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _workflowOperationFixture.UpdateWorkflowOperationCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("WorkflowOperation", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateWorkflowOperation(command));
    }

    #endregion

    #region GetWorkflowOperationByRunningUserList Tests

    [Fact]
    public async Task GetWorkflowOperationByRunningUserList_ReturnsExpectedList()
    {
        // Arrange
        var expectedResult = _workflowOperationFixture.WorkflowOperationRunningUserListVm;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetWorkflowOperationRunningUserListQuery>(), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetWorkflowOperationByRunningUserList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<WorkflowOperationRunningUserListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
        Assert.Contains(returnedResult, u => u.UserName == "admin");
    }

    #endregion

    #region GetProfileExecutorByBusinessServiceId Tests

    [Fact]
    public async Task GetProfileExecutorByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validBusinessServiceId = Guid.NewGuid().ToString();
        var expectedResult = _workflowOperationFixture.ProfileExecutorByBusinessServiceIdVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetProfileExecutorByBusinessServiceIdQuery>(q => q.BusinessServiceId == validBusinessServiceId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetProfileExecutorByBusinessServiceId(validBusinessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<ProfileExecutorByBusinessServiceIdVm>(okResult.Value);
    }

    [Fact]
    public async Task GetProfileExecutorByBusinessServiceId_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetProfileExecutorByBusinessServiceId("invalid-guid"));
    }

    #endregion

    #region GetDescriptionByStartTimeAndEndTime Tests

    [Fact]
    public async Task GetDescriptionByStartTimeAndEndTime_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var startTime = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endTime = DateTime.UtcNow.ToString("yyyy-MM-dd");
        var runMode = "Automatic";
        var expectedResult = _workflowOperationFixture.GetDescriptionByStartTimeAndEndTimeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDescriptionByStartTimeAndEndTimeListQuery>(
                q => q.StartTime == startTime && q.EndTime == endTime && q.RunMode == runMode), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDescriptionByStartTimeAndEndTime(startTime, endTime, runMode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<GetDescriptionByStartTimeAndEndTimeListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
        Assert.Contains(returnedResult, d => d.Description.Contains("Production backup"));
    }

    [Fact]
    public async Task GetDescriptionByStartTimeAndEndTime_WithNullRunMode_ReturnsOkResult()
    {
        // Arrange
        var startTime = DateTime.UtcNow.AddDays(-7).ToString("yyyy-MM-dd");
        var endTime = DateTime.UtcNow.ToString("yyyy-MM-dd");
        string? runMode = null;
        var expectedResult = _workflowOperationFixture.GetDescriptionByStartTimeAndEndTimeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDescriptionByStartTimeAndEndTimeListQuery>(
                q => q.StartTime == startTime && q.EndTime == endTime && q.RunMode == runMode), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDescriptionByStartTimeAndEndTime(startTime, endTime, runMode);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<GetDescriptionByStartTimeAndEndTimeListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
    }

    #endregion

    #region GetPaginatedWorkflowOperation Tests

    [Fact]
    public async Task GetPaginatedWorkflowOperation_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _workflowOperationFixture.GetWorkflowOperationPaginatedListQuery;
        var expectedResult = _workflowOperationFixture.PaginatedWorkflowOperations;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedWorkflowOperation(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<WorkflowOperationListVm>>(okResult.Value);
        Assert.Equal(3, paginatedResult.Data.Count);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(3, paginatedResult.TotalCount);
    }

    [Fact]
    public async Task GetPaginatedWorkflowOperation_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetWorkflowOperationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Production",
            SortColumn = "ProfileName",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<WorkflowOperationListVm>
        {
            Data = _workflowOperationFixture.WorkflowOperationListVm.Where(wo => wo.ProfileName.Contains("Production")).ToList(),
            PageSize = 10,
            TotalCount = 1,
            TotalPages = 1
        };

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedWorkflowOperation(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<WorkflowOperationListVm>>(okResult.Value);
        Assert.Equal(1, paginatedResult.Data.Count);
        Assert.All(paginatedResult.Data, wo => Assert.Contains("Production", wo.ProfileName));
    }

    #endregion

    #region GetDrDrillByBusinessServiceId Tests

    [Fact]
    public async Task GetDrDrillByBusinessServiceId_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validBusinessServiceId = Guid.NewGuid().ToString();
        var expectedResult = _workflowOperationFixture.WorkflowOperationDrDrillVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrDrillByBusinessServiceIdQuery>(q => q.BusinessServiceId == validBusinessServiceId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetDrDrillByBusinessServiceId(validBusinessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<WorkflowOperationDrDrillVm>>(okResult.Value);
        Assert.Single(returnedResult);
    }

    [Fact]
    public async Task GetDrDrillByBusinessServiceId_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetDrDrillByBusinessServiceId("invalid-guid"));
    }

    [Fact]
    public async Task GetDrDrillByBusinessServiceId_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetDrDrillByBusinessServiceId(""));
    }

    [Fact]
    public async Task GetDrDrillByBusinessServiceId_WithNonExistentId_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDrDrillByBusinessServiceIdQuery>(q => q.BusinessServiceId == nonExistentId), default))
            .ReturnsAsync(new List<WorkflowOperationDrDrillVm>());

        // Act
        var result = await _controller.GetDrDrillByBusinessServiceId(nonExistentId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsAssignableFrom<List<WorkflowOperationDrDrillVm>>(okResult.Value);
        Assert.Empty(returnedResult);
    }

    #endregion
}
