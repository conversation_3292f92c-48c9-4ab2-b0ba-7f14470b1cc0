using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class PostgresMonitorStatusRepositoryTests : IClassFixture<PostgresMonitorStatusFixture>, IDisposable
{
    private readonly PostgresMonitorStatusFixture _postgresMonitorStatusFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly PostgresMonitorStatusRepository _repository;

    public PostgresMonitorStatusRepositoryTests(PostgresMonitorStatusFixture postgresMonitorStatusFixture)
    {
        _postgresMonitorStatusFixture = postgresMonitorStatusFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new PostgresMonitorStatusRepository(_dbContext);
    }

    public void Dispose()
    {
        _dbContext?.Dispose();
    }

    private async Task ClearDatabase()
    {
        _dbContext.PostgresMonitorStatuses.RemoveRange(_dbContext.PostgresMonitorStatuses);
        await _dbContext.SaveChangesAsync();
    }

    #region Constructor Tests

    [Fact]
    public void Constructor_ShouldCreateInstance_WhenValidParametersProvided()
    {
        // Arrange & Act
        var repository = new PostgresMonitorStatusRepository(_dbContext);

        // Assert
        Assert.NotNull(repository);
    }

    #endregion

    #region GetDetailByType Tests

    [Fact]
    public async Task GetDetailByType_ShouldReturnStatusesWithSpecificType()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "TestType";
        var targetStatuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType(targetType, 3);
        var otherStatuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("OtherType", 2);
        
        await _repository.AddRangeAsync(targetStatuses);
        await _repository.AddRangeAsync(otherStatuses);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, status => Assert.Equal(targetType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeDoesNotExist()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("ExistingType", 2);
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDetailByType("NonExistentType");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeIsNull()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("ExistingType", 2);
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDetailByType(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeIsEmpty()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("ExistingType", 2);
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDetailByType(string.Empty);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnEmptyList_WhenTypeIsWhitespace()
    {
        // Arrange
        await ClearDatabase();
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("ExistingType", 2);
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDetailByType("   ");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnResultsInDatabaseOrder()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "TestType";
        var baseDate = DateTime.UtcNow.AddDays(-10);

        var status1 = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(
            type: targetType,
            createdDate: baseDate.AddDays(1));
        var status2 = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(
            type: targetType,
            createdDate: baseDate.AddDays(3));
        var status3 = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(
            type: targetType,
            createdDate: baseDate.AddDays(2));

        await _repository.AddAsync(status1);
        await _repository.AddAsync(status2);
        await _repository.AddAsync(status3);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count);
        Assert.All(result, status => Assert.Equal(targetType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldHandleLargeDataset()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "LargeDatasetType";
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType(targetType, 100);
        
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.Count);
        Assert.All(result, status => Assert.Equal(targetType, status.Type));
    }

    [Fact]
    public async Task GetDetailByType_ShouldReturnCompleteEntityData()
    {
        // Arrange
        await ClearDatabase();
        var targetType = "CompleteDataType";
        var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(
            type: targetType,
            infraObjectId: "test-infra-id",
            infraObjectName: "test-infra-name",
            workflowId: "test-workflow-id",
            workflowName: "test-workflow-name");
        
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetDetailByType(targetType);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        
        var returnedStatus = result.First();
        Assert.Equal(targetType, returnedStatus.Type);
        Assert.Equal("test-infra-id", returnedStatus.InfraObjectId);
        Assert.Equal("test-infra-name", returnedStatus.InfraObjectName);
        Assert.Equal("test-workflow-id", returnedStatus.WorkflowId);
        Assert.Equal("test-workflow-name", returnedStatus.WorkflowName);
        Assert.NotNull(returnedStatus.Properties);
        Assert.NotNull(returnedStatus.ConfiguredRPO);
        Assert.NotNull(returnedStatus.DataLagValue);
        Assert.NotNull(returnedStatus.Threshold);
    }

    #endregion

    //#region AddAsync Tests

    //[Fact]
    //public async Task AddAsync_ShouldAddPostgresMonitorStatus_WhenValidEntityProvided()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties();

    //    // Act
    //    var result = await _repository.AddAsync(status);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(status.Type, result.Type);
    //    Assert.Equal(status.InfraObjectId, result.InfraObjectId);
        
    //    var savedEntity = await _repository.GetByReferenceIdAsync(result.ReferenceId);
    //    Assert.NotNull(savedEntity);
    //}

    //[Fact]
    //public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    //}

    //#endregion

    //#region UpdateAsync Tests

    //[Fact]
    //public async Task UpdateAsync_ShouldUpdatePostgresMonitorStatus_WhenValidEntityProvided()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties();
    //    await _repository.AddAsync(status);

    //    // Modify the entity
    //    status.Type = "UpdatedType";
    //    status.InfraObjectName = "UpdatedInfraObjectName";

    //    // Act
    //    var result = await _repository.UpdateAsync(status);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal("UpdatedType", result.Type);
    //    Assert.Equal("UpdatedInfraObjectName", result.InfraObjectName);
    //}

    //[Fact]
    //public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.UpdateAsync(null));
    //}

    //#endregion

    //#region DeleteAsync Tests

    //[Fact]
    //public async Task DeleteAsync_ShouldDeletePostgresMonitorStatus_WhenValidEntityProvided()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties();
    //    await _repository.AddAsync(status);

    //    // Act
    //    var result = await _repository.DeleteAsync(status);

    //    // Assert
    //    Assert.NotNull(result);
        
    //    // Verify entity is deleted
    //    var deletedEntity = await _dbContext.PostgresMonitorStatuses.FindAsync(status.Id);
    //    Assert.Null(deletedEntity);
    //}

    //[Fact]
    //public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    //}

    //#endregion

    //#region AddRangeAsync Tests

    //[Fact]
    //public async Task AddRangeAsync_ShouldAddMultiplePostgresMonitorStatuses()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("BatchType", 3);

    //    // Act
    //    var result = await _repository.AddRangeAsync(statuses);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(3, result.Count());
        
    //    var allStatuses = await _repository.ListAllAsync();
    //    Assert.Equal(3, allStatuses.Count);
    //}

    //[Fact]
    //public async Task AddRangeAsync_ShouldThrow_WhenEntitiesIsNull()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddRangeAsync(null));
    //}

    //#endregion

    //#region ListAllAsync Tests

    //[Fact]
    //public async Task ListAllAsync_ShouldReturnAllPostgresMonitorStatuses()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType("ListAllType", 4);
    //    await _repository.AddRangeAsync(statuses);

    //    // Act
    //    var result = await _repository.ListAllAsync();

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(4, result.Count);
    //}

    //#endregion

    //#region GetByReferenceIdAsync Tests

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldReturnPostgresMonitorStatus_WhenIdExists()
    //{
    //    // Arrange
    //    await ClearDatabase();
    //    var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties();
    //    await _repository.AddAsync(status);

    //    // Act
    //    var result = await _repository.GetByReferenceIdAsync(status.ReferenceId);

    //    // Assert
    //    Assert.NotNull(result);
    //    Assert.Equal(status.ReferenceId, result.ReferenceId);
    //    Assert.Equal(status.Type, result.Type);
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenIdDoesNotExist()
    //{
    //    // Arrange
    //    await ClearDatabase();

    //    // Act
    //    var result = await _repository.GetByReferenceIdAsync(Guid.NewGuid().ToString());

    //    // Assert
    //    Assert.Null(result);
    //}

    //[Fact]
    //public async Task GetByReferenceIdAsync_ShouldThrow_WhenIdIsInvalid()
    //{
    //    // Act & Assert
    //    await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    //}

    //#endregion

    #region GetPostgresMonitorStatusByInfraObjectIdAsync Tests

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldReturnStatus_WhenInfraObjectIdExists()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(infraObjectId: infraObjectId);
        await _repository.AddAsync(status);

        // Act
        var result = await _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
        Assert.Equal(status.Type, result.Type);
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldReturnNull_WhenInfraObjectIdDoesNotExist()
    {
        // Arrange
        await ClearDatabase();

        // Act
        var result = await _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(Guid.NewGuid().ToString());

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(
            () => _repository.GetPostgresMonitorStatusByInfraObjectIdAsync("invalid-guid"));
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(
            () => _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(null));
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldThrow_WhenInfraObjectIdIsEmpty()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ContinuityPatrol.Shared.Core.Exceptions.InvalidArgumentException>(
            () => _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(string.Empty));
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectIdAsync_ShouldReturnFirstMatch_WhenMultipleStatusesExist()
    {
        // Arrange
        await ClearDatabase();
        var infraObjectId = Guid.NewGuid().ToString();
        var statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameInfraObjectId(infraObjectId, 3);
        await _repository.AddRangeAsync(statuses);

        // Act
        var result = await _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(infraObjectId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(infraObjectId, result.InfraObjectId);
    }

    #endregion

    #region Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        await ClearDatabase();
        var tasks = new List<Task>();

        // Act
        for (int i = 0; i < 10; i++)
        {
            var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(type: $"ConcurrentType_{i}");
            tasks.Add(_repository.AddAsync(status));
        }

        await Task.WhenAll(tasks);

        // Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Equal(10, allStatuses.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleComplexScenario()
    {
        // Arrange
        await ClearDatabase();
        var type1 = "ComplexType1";
        var type2 = "ComplexType2";
        var infraObjectId = Guid.NewGuid().ToString();

        // Add statuses of different types
        var type1Statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType(type1, 3);
        var type2Statuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameType(type2, 2);
        var infraStatuses = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusesWithSameInfraObjectId(infraObjectId, 2);

        await _repository.AddRangeAsync(type1Statuses);
        await _repository.AddRangeAsync(type2Statuses);
        await _repository.AddRangeAsync(infraStatuses);

        // Act & Assert
        var type1Results = await _repository.GetDetailByType(type1);
        Assert.Equal(3, type1Results.Count);

        var type2Results = await _repository.GetDetailByType(type2);
        Assert.Equal(2, type2Results.Count);

        var infraResult = await _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(infraObjectId);
        Assert.NotNull(infraResult);
        Assert.Equal(infraObjectId, infraResult.InfraObjectId);

        var allResults = await _repository.ListAllAsync();
        Assert.Equal(7, allResults.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyDatabase()
    {
        // Arrange
        await ClearDatabase();

        // Act & Assert
        var allStatuses = await _repository.ListAllAsync();
        Assert.Empty(allStatuses);

        var typeResults = await _repository.GetDetailByType("NonExistentType");
        Assert.Empty(typeResults);

        var infraResult = await _repository.GetPostgresMonitorStatusByInfraObjectIdAsync(Guid.NewGuid().ToString());
        Assert.Null(infraResult);
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity()
    {
        // Arrange
        await ClearDatabase();
        var status = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusWithProperties(
            type: "IntegrityType",
            infraObjectId: Guid.NewGuid().ToString(),
            infraObjectName: "IntegrityInfraObject",
            workflowId: Guid.NewGuid().ToString(),
            workflowName: "IntegrityWorkflow");

        // Act
        var addedStatus = await _repository.AddAsync(status);
        var retrievedStatus = await _repository.GetByReferenceIdAsync(addedStatus.ReferenceId);

        // Assert
        Assert.NotNull(retrievedStatus);
        Assert.Equal(status.Type, retrievedStatus.Type);
        Assert.Equal(status.InfraObjectId, retrievedStatus.InfraObjectId);
        Assert.Equal(status.InfraObjectName, retrievedStatus.InfraObjectName);
        Assert.Equal(status.WorkflowId, retrievedStatus.WorkflowId);
        Assert.Equal(status.WorkflowName, retrievedStatus.WorkflowName);
        Assert.Equal(status.Properties, retrievedStatus.Properties);
        Assert.Equal(status.ConfiguredRPO, retrievedStatus.ConfiguredRPO);
        Assert.Equal(status.DataLagValue, retrievedStatus.DataLagValue);
        Assert.Equal(status.Threshold, retrievedStatus.Threshold);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullAndEmptyProperties()
    {
        // Arrange
        await ClearDatabase();
        var status = new PostgresMonitorStatus
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Type = "NullPropsType",
            InfraObjectId = Guid.NewGuid().ToString(),
            InfraObjectName = null,
            WorkflowId = Guid.NewGuid().ToString(),
            WorkflowName = "",
            Properties = null,
            ConfiguredRPO = "",
            DataLagValue = null,
            Threshold = "",
            IsActive = true,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            CreatedBy = "TestUser",
            LastModifiedBy = "TestUser"
        };

        // Act
        var addedStatus = await _repository.AddAsync(status);

        // Assert
        Assert.NotNull(addedStatus);
        var retrievedStatus = await _repository.GetByReferenceIdAsync(addedStatus.ReferenceId);
        Assert.NotNull(retrievedStatus);
        Assert.Equal("NullPropsType", retrievedStatus.Type);
    }

    #endregion
}
