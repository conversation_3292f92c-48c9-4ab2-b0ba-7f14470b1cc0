<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Single Sign-On QUnit Tests</title>
    <link rel="stylesheet" href="https://code.jquery.com/qunit/qunit-2.20.1.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-mockjax/2.6.0/jquery.mockjax.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://code.jquery.com/qunit/qunit-2.20.1.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sinon.js/17.0.1/sinon.min.js"></script>
    <script>
        var RootUrl = "/";
        var SSOURL = {
            getSingleSignOnList: "Configuration/SingleSignOn/GetSingleSignOnList",
            createOrUpdate: "Configuration/SingleSignOn/CreateOrUpdate",
            delete: "Configuration/SingleSignOn/Delete",
            isSingleSignOnNameExist: "Configuration/SingleSignOn/IsSingleSignOnNameExist",
            serverDataDecrypt: "Configuration/Server/ServerDataDecrypt",
            serverDataEncrypt: "Configuration/Server/ServerDataEncrypt"
        };
    </script>

    <!-- Your actual siteLocation.js goes here -->
    <script src="/js/Common/common.js"></script>
    <script src="/js/Configuration/Infra Components/CommonFunctions.js"></script>
    <script src="/js/Configuration/Infra Components/Single Sign-On/SingleSignOnFormBuilder.js"></script>
    <script src="/js/Configuration/Infra Components/Single Sign-On/SingleSignOnFunctions.js"></script>
    <script src="/js/Configuration/Infra Components/Single Sign-On/SingleSignOn.js"></script>
    <script src="/js/Configuration/Infra Components/Single Sign-On/SingleSignOnTest.js"></script>
    <style>
        #qunit-fixture {
            display: none;
        }
    </style>
</head>
<body>
    <div id="qunit"></div>
    <div id="qunit-fixture">
        
    </div>
</body>
</html>
