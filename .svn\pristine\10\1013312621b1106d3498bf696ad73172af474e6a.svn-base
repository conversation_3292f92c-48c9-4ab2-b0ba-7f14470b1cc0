﻿using ContinuityPatrol.Domain.ViewModels.AlertNotificationModel;

namespace ContinuityPatrol.Application.Features.AlertNotification.Queries.GetList;

public class
    GetAlertNotificationListQueryHandler : IRequestHandler<GetAlertNotificationListQuery, List<AlertNotificationListVm>>
{
    private readonly IAlertNotificationRepository _alertNotificationRepository;
    private readonly IMapper _mapper;

    public GetAlertNotificationListQueryHandler(IMapper mapper,
        IAlertNotificationRepository alertNotificationRepository)
    {
        _mapper = mapper;
        _alertNotificationRepository = alertNotificationRepository;
    }

    public async Task<List<AlertNotificationListVm>> Handle(GetAlertNotificationListQuery request,
        CancellationToken cancellationToken)
    {
        var alertNotifications = (await _alertNotificationRepository.ListAllAsync()).ToList();

        return alertNotifications.Count <= 0
            ? new List<AlertNotificationListVm>()
            : _mapper.Map<List<AlertNotificationListVm>>(alertNotifications);
    }
}