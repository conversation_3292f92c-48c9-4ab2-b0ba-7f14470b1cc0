﻿using ContinuityPatrol.Shared.Services.Extension;
using ContinuityPatrol.Shared.Services.Helper;


namespace ContinuityPatrol.Shared.Services.Base;

public class BaseClient
{
    private readonly IAppCache _cache;
    private readonly ILogger<BaseClient> _logger;
    private readonly RestClient _client;
    private readonly string _token;
   
    public BaseClient(IConfiguration config, IAppCache cache, ILogger<BaseClient> logger)
    {
        _cache = cache;
        _logger = logger;
        _token = WebHelper.UserSession.Token;

        var options = new RestClientOptions(config["ApiSettings:ApiUrl"] ?? "https://localhost:2000/")
        {
            RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
        };

        _client = new RestClient(options);
    }
   
    public async Task<T> Post<T>(RestRequest request) where T : class, new()
    {
        request.AddHeader("Accept", "text/plain");
        request.AddHeader("authorization", "Bearer " + _token);

        var response = await _client.ExecutePostAsync(request);
        if (!response.IsSuccessStatusCode || response.Content == null)
        {
            _logger.LogError($"Service Client : Get Failed. {response.DetailErrorMessage()}");
            response.Throw();
        }

        var deserializeJson = JsonConvert.DeserializeObject<T>(response.Content!);
        return Task.FromResult(deserializeJson).Result;
    }

    public async Task<T> Put<T>(RestRequest request) where T : class, new()
    {
        request.AddHeader("Accept", "text/plain");
        request.AddHeader("authorization", "Bearer " + _token);

        var response = await _client.ExecutePutAsync(request);
        if (!response.IsSuccessStatusCode || response.Content == null)
        {
            _logger.LogError($"Service Client : Get Failed. {response.DetailErrorMessage()}");
            response.Throw();
        }

        var deserializeJson = JsonConvert.DeserializeObject<T>(response.Content!);
        return Task.FromResult(deserializeJson).Result;
    }

    public async Task<T> Delete<T>(RestRequest request) where T : class, new()
    {
        request.AddHeader("Accept", "text/plain");
        request.AddHeader("authorization", "Bearer " + _token);

        var response = await _client.DeleteAsync(request);
        if (!response.IsSuccessStatusCode || response.Content == null)
        {
            _logger.LogError($"Service Client : Get Failed. {response.DetailErrorMessage()}");
            response.Throw();
        }

        var deserializeJson = JsonConvert.DeserializeObject<T>(response.Content!);
        return Task.FromResult(deserializeJson).Result;
    }

    public async Task<T> Get<T>(RestRequest request) where T : new()
    {
        request.AddHeader("Accept", "text/plain");
        request.AddHeader("authorization", "Bearer " + _token);

        var response = await _client.ExecuteGetAsync(request);
        if (!response.IsSuccessStatusCode || response.Content == null)
        {
            _logger.LogError($"Service Client : Get Failed. {response.DetailErrorMessage()}");
            response.Throw();
        }

        var deserializeJson = JsonConvert.DeserializeObject<T>(response.Content!);
        return Task.FromResult(deserializeJson).Result;
    }

    public async Task<T> GetFromCache<T>(RestRequest request, string cacheKey) where T : class, new()
    {
        var item = await _cache.GetAsync<T>(cacheKey);
        if (item != null) return item;

        request.AddHeader("Accept", "text/plain");
        request.AddHeader("authorization", "Bearer " + _token);

        var response = await _client.ExecuteGetAsync(request);
        if (!response.IsSuccessStatusCode || response.Content == null)
        {
            _logger.LogError($"Service Client : Get From Cache Failed. {response.DetailErrorMessage()}");
            response.Throw();
        }

        var deserializeJson = JsonConvert.DeserializeObject<T>(response.Content!);
        _cache.Add(cacheKey, deserializeJson);
        return Task.FromResult(deserializeJson).Result;
    }

    public void ClearCache(params string[] cacheKey)
    {
        foreach (var item in cacheKey) _cache.Remove(item);
    }
}