﻿namespace ContinuityPatrol.Domain.Entities;

public class WorkflowOperation : AuditableEntity
{
    public string CompanyId { get; set; }
    public string ProfileId { get; set; }
    public string ProfileName { get; set; }
    public string Status { get; set; }
    public string Description { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string UserName { get; set; }
    public string RunMode { get; set; }
    public bool IsDrCalendar { get; set; }
}