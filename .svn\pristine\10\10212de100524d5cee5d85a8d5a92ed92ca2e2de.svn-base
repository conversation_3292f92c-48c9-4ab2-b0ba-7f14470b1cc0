﻿namespace ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPostgresMonitorStatusByInfraObjectId;

public class
    GetPostgresMonitorStatusByInfraObjectIdQueryHandler : IRequestHandler<GetPostgresMonitorStatusByInfraObjectIdQuery,
        string>
{
    private readonly IMapper _mapper;
    private readonly IPostgresMonitorStatusRepository _postgresMonitorStatusRepository;

    public GetPostgresMonitorStatusByInfraObjectIdQueryHandler(IMapper mapper,
        IPostgresMonitorStatusRepository postgresMonitorStatusRepository)
    {
        _mapper = mapper;
        _postgresMonitorStatusRepository = postgresMonitorStatusRepository;
    }

    public async Task<string> Handle(GetPostgresMonitorStatusByInfraObjectIdQuery request,
        CancellationToken cancellationToken)
    {
        var postgresMonitorStatus =
            await _postgresMonitorStatusRepository.GetPostgresMonitorStatusByInfraObjectIdAsync(request.InfraObjectId);

        Guard.Against.NullOrDeactive(postgresMonitorStatus, nameof(Domain.Entities.PostgresMonitorStatus),
            new NotFoundException(nameof(Domain.Entities.PostgresMonitorStatus), request.InfraObjectId));

        var postgresMonitorStatusDetailDto = postgresMonitorStatus.ReferenceId;

        return postgresMonitorStatusDetailDto;
    }
}