using ContinuityPatrol.Application.Features.DriftParameter.Commands.Create;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Delete;
using ContinuityPatrol.Application.Features.DriftParameter.Commands.Update;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetList;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.DriftParameter.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DriftParameterModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class DriftParametersController : CommonBaseController
{
    [HttpGet]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<List<DriftParameterListVm>>> GetDriftParameters()
    {
        Logger.LogDebug("Get All DriftParameters");

        return Ok(await Mediator.Send(new GetDriftParameterListQuery()));
    }

    [HttpGet("{id}", Name = "GetDriftParameter")]
    [Authorize(Policy = Permissions.Drift.View)]
    public async Task<ActionResult<DriftParameterDetailVm>> GetDriftParameterById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftParameter Id");

        Logger.LogDebug($"Get DriftParameter Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDriftParameterDetailQuery { Id = id }));
    }
    #region Paginated
 [Route("paginated-list"), HttpGet]
 [Authorize(Policy = Permissions.Drift.View)]
 public async Task<ActionResult<PaginatedResult<DriftParameterListVm>>> GetPaginatedDriftParameters([FromQuery] GetDriftParameterPaginatedListQuery query)
 {
     Logger.LogDebug("Get Searching Details in DriftParameter Paginated List");

     return Ok(await Mediator.Send(query));
 }
   #endregion

    [HttpPost]
    [Authorize(Policy = Permissions.Drift.Create)]
    public async Task<ActionResult<CreateDriftParameterResponse>> CreateDriftParameter([FromBody] CreateDriftParameterCommand createDriftParameterCommand)
    {
        Logger.LogDebug($"Create DriftParameter '{createDriftParameterCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDriftParameter), await Mediator.Send(createDriftParameterCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Drift.Edit)]
    public async Task<ActionResult<UpdateDriftParameterResponse>> UpdateDriftParameter([FromBody] UpdateDriftParameterCommand updateDriftParameterCommand)
    {
        Logger.LogDebug($"Update DriftParameter '{updateDriftParameterCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDriftParameterCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Drift.Delete)]
    public async Task<ActionResult<DeleteDriftParameterResponse>> DeleteDriftParameter(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DriftParameter Id");

        Logger.LogDebug($"Delete DriftParameter Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDriftParameterCommand { Id = id }));
    }

     #region NameExist
 [Route("name-exist"), HttpGet]
 public async Task<ActionResult> IsDriftParameterNameExist(string driftParameterName, string? id)
 {
     Guard.Against.NullOrWhiteSpace(driftParameterName, "DriftParameter Name");

     Logger.LogDebug($"Check Name Exists Detail by DriftParameter Name '{driftParameterName}' and Id '{id}'");

     return Ok(await Mediator.Send(new GetDriftParameterNameUniqueQuery { Name = driftParameterName, Id = id }));
 }
   #endregion
    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllFormsCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllFormNamesCacheKey };

        ClearCache(cacheKeys);
    }
}


