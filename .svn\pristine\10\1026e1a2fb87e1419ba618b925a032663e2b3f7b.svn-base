﻿using ContinuityPatrol.Application.Features.Form.Commands.Create;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Enums;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.Form.ImportFormPlugins;

public class ImportPluginListCommandHandler : IRequestHandler<ImportPluginListCommand, ImportFormPluginResponse>
{
    private readonly IFormHistoryRepository _formHistoryRepository;
    private readonly IFormRepository _formRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IVersionManager _versionManager;

    public ImportPluginListCommandHandler(IMapper mapper, IFormRepository formRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher, IFormHistoryRepository formHistoryRepository,
        IVersionManager versionManager)
    {
        _mapper = mapper;
        _formRepository = formRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
        _versionManager = versionManager;
        _formHistoryRepository = formHistoryRepository;
    }

    public async Task<ImportFormPluginResponse> Handle(ImportPluginListCommand request,
        CancellationToken cancellationToken)
    {
        dynamic deserializeObject = JsonConvert.DeserializeObject(request.ImportPlugins);


        var formCreate = new CreateFormCommandHandler(_mapper, _publisher, _formRepository, _loggedInUserService,
            _formHistoryRepository, _versionManager);
        var properties = deserializeObject!.SelectToken("properties");


        if (properties.ToString().ToLower().Contains("server"))
        {
            var result = await formCreate.Handle(new CreateFormCommand
            {
                Name = properties.SelectToken("serverType").ToString(),
                Type = Modules.Server.ToString(),
                Properties = properties.SelectToken("serverdata").ToString()
            }, cancellationToken);
        }

        if (properties.ToString().ToLower().Contains("database"))
        {
            var result = await formCreate.Handle(new CreateFormCommand
            {
                Name = properties.SelectToken("databaseType").ToString(),
                Type = Modules.Database.ToString(),
                Properties = properties.SelectToken("databasedata").ToString()
            }, cancellationToken);
        }

        if (properties.ToString().ToLower().Contains("replication"))
        {
            var result = await formCreate.Handle(new CreateFormCommand
            {
                Name = properties.SelectToken("replicationType").ToString(),
                Type = Modules.Replication.ToString(),
                Properties = properties.SelectToken("replicationdata").ToString()
            }, cancellationToken);
        }

        return new ImportFormPluginResponse
        {
            Message = "Form imported successfully"
        };
    }
}