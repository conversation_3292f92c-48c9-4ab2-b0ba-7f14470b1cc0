﻿using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertReceiver.Queries;

public class GetAlertReceiverListQueryHandlerTests : IClassFixture<AlertReceiverFixture>
{
    private readonly AlertReceiverFixture _alertReceiverFixture;

    private Mock<IAlertReceiverRepository> _mockAlertReceiverRepository;

    private readonly GetAlertReceiverListQueryHandler _handler;

    public GetAlertReceiverListQueryHandlerTests(AlertReceiverFixture alertReceiverFixture)
    {
        _alertReceiverFixture = alertReceiverFixture;

        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.GetAlertReceiverRepository(_alertReceiverFixture.AlertReceivers);

        _handler = new GetAlertReceiverListQueryHandler(_alertReceiverFixture.Mapper,_mockAlertReceiverRepository.Object);

    }

    [Fact]
    public async Task Handle_Return_Active_AlertReceiversCount()
    {
        var result = await _handler.Handle(new GetAlertReceiverListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertReceiverListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_Valid_AlertReceiversList()
    {
        var result = await _handler.Handle(new GetAlertReceiverListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertReceiverListVm>>();

        result[0].Id.ShouldBe(_alertReceiverFixture.AlertReceivers[0].ReferenceId);

        result[0].EmailAddress.ShouldBe(_alertReceiverFixture.AlertReceivers[0].EmailAddress);

        result[0].IsMail.ShouldBe(_alertReceiverFixture.AlertReceivers[0].IsMail);

        result[0].Name.ShouldBe(_alertReceiverFixture.AlertReceivers[0].Name);

        result[0].Properties.ShouldBe(_alertReceiverFixture.AlertReceivers[0].Properties);

    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockAlertReceiverRepository = AlertReceiverRepositoryMocks.GetAlertReceiverEmptyRepository();

        var handler = new GetAlertReceiverListQueryHandler(_alertReceiverFixture.Mapper, _mockAlertReceiverRepository.Object);

        var result = await handler.Handle(new GetAlertReceiverListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetAlertReceiverListQuery(), CancellationToken.None);

        _mockAlertReceiverRepository.Verify(x => x.ListAllAsync(), Times.Once);
    }
}