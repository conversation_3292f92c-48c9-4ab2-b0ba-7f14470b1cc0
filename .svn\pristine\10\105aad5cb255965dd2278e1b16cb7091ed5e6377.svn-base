using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Create;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaTemplate.Commands.Update;
using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetList;
using ContinuityPatrol.Application.Features.FiaTemplate.Queries.GetNameUnique;
using ContinuityPatrol.Services.Db.Impl.FiaBia;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.FiaBia;

public class FiaTemplateServiceTests : BaseServiceTestSetup<FiaTemplateService>, IClassFixture<FiaTemplateFixture>
{
    private readonly FiaTemplateFixture _fixture;

    public FiaTemplateServiceTests(FiaTemplateFixture fixture)
    {
        InitializeService(accessor => new FiaTemplateService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetFiaTemplateList_Should_Return_Data()
    {

        MediatorMock.Setup(m => m.Send(It.IsAny<GetFiaTemplateListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FiaTemplateListVm);

        var result = await ServiceUnderTest.GetFiaTemplateList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var fiaTemplateId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetFiaTemplateDetailQuery>(q => q.Id == fiaTemplateId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FiaTemplateDetailVm);

        var result = await ServiceUnderTest.GetByReferenceId(fiaTemplateId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.FiaTemplateDetailVm.Id, result.Id);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateFiaTemplateResponse
        {
            Message = "Created",
            Success = true
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateFiaTemplateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateFiaTemplateCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateFiaTemplateResponse
        {
            Message = "Updated",
            Success = true
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateFiaTemplateCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateFiaTemplateCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var fiaTemplateId = Guid.NewGuid().ToString();
        var response = new DeleteFiaTemplateResponse
        {
            Message = "Deleted",
            Success = true
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteFiaTemplateCommand>(c => c.Id == fiaTemplateId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(fiaTemplateId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task IsFiaTemplateNameExist_Should_Return_Boolean()
    {
        var templateName = "Test Template";
        var id = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetFiaTemplateNameUniqueQuery>(q => q.Name == templateName && q.Id == id), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsFiaTemplateNameExist(templateName, id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedFiaTemplates_Should_Return_Result()
    {
        var query = _fixture.GetFiaTemplatePaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FiaTemplatePaginatedResult);

        var result = await ServiceUnderTest.GetPaginatedFiaTemplates(query);

        Assert.Equal(_fixture.FiaTemplateListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetByReferenceId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetByReferenceId(null!));
    }

    [Fact]
    public async Task GetByReferenceId_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetByReferenceId(""));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }

    [Fact]
    public async Task IsFiaTemplateNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.IsFiaTemplateNameExist(null!, Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task IsFiaTemplateNameExist_EmptyName_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.IsFiaTemplateNameExist("", Guid.NewGuid().ToString()));
    }
}
