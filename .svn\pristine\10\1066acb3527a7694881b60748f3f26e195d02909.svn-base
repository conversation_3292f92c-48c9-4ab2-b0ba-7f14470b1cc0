using ContinuityPatrol.Application.Features.BackUpLog.Commands.Create;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUpLog.Commands.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class BackUpLogFixture : IDisposable
{
    public List<BackUpLog> BackUpLogs { get; set; }
    public List<UserActivity> UserActivities { get; set; }
    public CreateBackUpLogCommand CreateBackUpLogCommand { get; set; }
    public UpdateBackUpLogCommand UpdateBackUpLogCommand { get; set; }
    public DeleteBackUpLogCommand DeleteBackUpLogCommand { get; set; }
    public IMapper Mapper { get; set; }

    public BackUpLogFixture()
    {
        BackUpLogs = new List<BackUpLog>
        {
            new BackUpLog
            {
                ReferenceId = Guid.NewGuid().ToString(),
                HostName = "TestServer01",
                DatabaseName = "TestDatabase",
                UserName = "TestUser",
                IsLocalServer = true,
                IsBackUpServer = false,
                BackUpPath = @"C:\Backups\TestDatabase.bak",
                Type = "Full",
                Status = "Completed",
                Properties = "{\"compression\":\"true\",\"encryption\":\"false\",\"size\":\"1024MB\"}",
                IsActive = true
            }
        };

        // Create additional entities using AutoFixture and add to existing lists
        try
        {
            var additionalLogs = AutoBackUpLogFixture.CreateMany<BackUpLog>(2).ToList();
            BackUpLogs.AddRange(additionalLogs);

            UserActivities = AutoBackUpLogFixture.CreateMany<UserActivity>(3).ToList();
            CreateBackUpLogCommand = AutoBackUpLogFixture.Create<CreateBackUpLogCommand>();
            UpdateBackUpLogCommand = AutoBackUpLogFixture.Create<UpdateBackUpLogCommand>();
            DeleteBackUpLogCommand = AutoBackUpLogFixture.Create<DeleteBackUpLogCommand>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            UserActivities = new List<UserActivity>();
            CreateBackUpLogCommand = new CreateBackUpLogCommand();
            UpdateBackUpLogCommand = new UpdateBackUpLogCommand();
            DeleteBackUpLogCommand = new DeleteBackUpLogCommand();
        }

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<BackUpLogProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoBackUpLogFixture
    {
        get
        {
            var fixture = new Fixture();

            // Configure fixture to handle circular references
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            // String customizations
            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateBackUpLogCommand>(p => p.HostName, 100));
            fixture.Customize<CreateBackUpLogCommand>(c => c
                .With(b => b.HostName, () => $"TestServer{fixture.Create<int>():00}")
                .With(b => b.DatabaseName, () => $"TestDatabase{fixture.Create<int>()}")
                .With(b => b.UserName, () => $"TestUser{fixture.Create<int>()}")
                .With(b => b.IsLocalServer, true)
                .With(b => b.IsBackUpServer, false)
                .With(b => b.BackUpPath, () => $@"C:\Backups\TestDatabase{fixture.Create<int>()}.bak")
                .With(b => b.Type, "Full")
                .With(b => b.Status, "Completed"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateBackUpLogCommand>(p => p.HostName, 100));
            fixture.Customize<UpdateBackUpLogCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString())
                .With(b => b.HostName, () => $"UpdatedServer{fixture.Create<int>():00}")
                .With(b => b.DatabaseName, () => $"UpdatedDatabase{fixture.Create<int>()}")
                .With(b => b.UserName, () => $"UpdatedUser{fixture.Create<int>()}")
                .With(b => b.IsLocalServer, false)
                .With(b => b.IsBackUpServer, true)
                .With(b => b.BackUpPath, () => $@"D:\Backups\UpdatedDatabase{fixture.Create<int>()}.bak")
                .With(b => b.Type, "Differential")
                .With(b => b.Status, "In Progress"));

            fixture.Customize<DeleteBackUpLogCommand>(c => c
                .With(b => b.Id, () => Guid.NewGuid().ToString()));

            fixture.Customize<BackUpLog>(c => c
                .With(b => b.ReferenceId, () => Guid.NewGuid().ToString())
                .With(b => b.IsActive, true)
                .With(b => b.HostName, () => $"TestServer{fixture.Create<int>():00}")
                .With(b => b.DatabaseName, () => $"TestDatabase{fixture.Create<int>()}")
                .With(b => b.UserName, () => $"TestUser{fixture.Create<int>()}")
                .With(b => b.IsLocalServer, true)
                .With(b => b.IsBackUpServer, false)
                .With(b => b.BackUpPath, () => $@"C:\Backups\TestDatabase{fixture.Create<int>()}.bak")
                .With(b => b.Type, "Full")
                .With(b => b.Status, "Completed")
                .With(b => b.Properties, "{\"compression\":\"true\",\"encryption\":\"false\",\"size\":\"1024MB\"}"));

            // Add UserActivity customization
            fixture.Customize<UserActivity>(c => c
                .With(a => a.ReferenceId, () => Guid.NewGuid().ToString())
                .With(a => a.UserId, () => Guid.NewGuid().ToString())
                .With(a => a.LoginName, () => $"TestUser{fixture.Create<int>()}")
                .With(a => a.Entity, "BackUpLog")
                .With(a => a.Action, "Create")
                .With(a => a.ActivityType, "Create")
                .With(a => a.ActivityDetails, () => $"Test backup activity {fixture.Create<int>()}")
                .With(a => a.RequestUrl, "/api/test")
                .With(a => a.HostAddress, "127.0.0.1")
                .With(a => a.IsActive, true));

            return fixture;
        }
    }

    public void Dispose()
    {
    }
}
