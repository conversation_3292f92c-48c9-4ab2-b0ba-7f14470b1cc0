﻿using ContinuityPatrol.Application.Features.MYSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Helper;

namespace ContinuityPatrol.Application.UnitTests.Features.MYSQLMonitorStatus.Commands;

public class UpdateMYSQLMonitorStatusCommandHandlerTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IMysqlMonitorStatusRepository> _repositoryMock;
    private readonly UpdateMYSQLMonitorStatusCommandHandler _handler;

    public UpdateMYSQLMonitorStatusCommandHandlerTests()
    {
        var entity = new Domain.Entities.MYSQLMonitorStatus
        {
            Id = 123,
            ReferenceId = "some-reference-id",
            WorkflowName = "Active",
            CreatedDate = DateTime.UtcNow
            // Add other required properties
        };
        _mapperMock = new Mock<IMapper>();
        _repositoryMock = new Mock<IMysqlMonitorStatusRepository>();
        _repositoryMock = MYSQLMonitorStatusRepositoryMocks.UpdateMYSQLMonitorStatusRepository(entity);
        _handler = new UpdateMYSQLMonitorStatusCommandHandler(_mapperMock.Object, _repositoryMock.Object);
    }
    [Fact]
    public async Task Handle_ShouldUpdateAndReturnResponse_WhenEntityExists()
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand
        {
            Id = "REF123",
            Type = "MySQL",
            InfraObjectId = "INFRA001",
            InfraObjectName = "Node1",
            WorkflowId = "WF001",
            WorkflowName = "WorkflowName",
            Properties = "{}",
            ConfiguredRPO = "30s",
            DataLagValue = "20s",
            Threshold = "10s"
        };

        var existingEntity = new Domain.Entities.MYSQLMonitorStatus { ReferenceId = command.Id };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(existingEntity);
        _mapperMock.Setup(m => m.Map(command, existingEntity, typeof(UpdateMYSQLMonitorStatusCommand), typeof(Domain.Entities.MYSQLMonitorStatus)));
        _repositoryMock.Setup(r => r.UpdateAsync(existingEntity)).ReturnsAsync(existingEntity);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(command.Id);
        result.Message.Should().Be(Message.Update(nameof(Domain.Entities.MYSQLMonitorStatus), command.Id));

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(existingEntity), Times.Once);
        _mapperMock.Verify(m => m.Map(command, existingEntity, typeof(UpdateMYSQLMonitorStatusCommand), typeof(Domain.Entities.MYSQLMonitorStatus)), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityNotFound()
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand { Id = "MISSING123" };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync((Domain.Entities.MYSQLMonitorStatus)null!);

        // Act
        Func<Task> act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"*MYSQLMonitorStatus*{command.Id}*");

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.MYSQLMonitorStatus>()), Times.Never);
        _mapperMock.Verify(m => m.Map(It.IsAny<UpdateMYSQLMonitorStatusCommand>(), It.IsAny<Domain.Entities.MYSQLMonitorStatus>(), It.IsAny<Type>(), It.IsAny<Type>()), Times.Never);
    }
    [Fact]
    public async Task Handle_ShouldUpdate_WhenOnlyIdIsProvided()
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand { Id = "REF_MIN" };
        var entity = new Domain.Entities.MYSQLMonitorStatus { ReferenceId = "REF_MIN" };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity, typeof(UpdateMYSQLMonitorStatusCommand), typeof(Domain.Entities.MYSQLMonitorStatus)));
        _repositoryMock.Setup(r => r.UpdateAsync(entity)).ReturnsAsync(entity);

        // Act
        var response = await _handler.Handle(command, CancellationToken.None);

        // Assert
        response.Should().NotBeNull();
        response.Id.Should().Be("REF_MIN");
        response.Message.Should().Be(Message.Update(nameof(Domain.Entities.MYSQLMonitorStatus), "REF_MIN"));

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(entity), Times.Once);
        _mapperMock.Verify(m => m.Map(command, entity, typeof(UpdateMYSQLMonitorStatusCommand), typeof(Domain.Entities.MYSQLMonitorStatus)), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldRespectCancellationToken()
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand { Id = "REF_TOKEN" };
        var entity = new Domain.Entities.MYSQLMonitorStatus { ReferenceId = "REF_TOKEN" };
        var token = new CancellationTokenSource().Token;

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(command.Id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map(command, entity, typeof(UpdateMYSQLMonitorStatusCommand), typeof(Domain.Entities.MYSQLMonitorStatus)));
        _repositoryMock.Setup(r => r.UpdateAsync(entity)).ReturnsAsync(entity);

        // Act
        var response = await _handler.Handle(command, token);

        // Assert
        response.Should().NotBeNull();
        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(command.Id), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(entity), Times.Once);
    }
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    public async Task Handle_ShouldThrowNotFoundException_WhenIdIsNullOrEmpty(string id)
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand { Id = id };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(It.IsAny<string>()))
            .ReturnsAsync((Domain.Entities.MYSQLMonitorStatus)null!);

        // Act
        Func<Task> act = async () => await _handler.Handle(command, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>();
    }
    [Fact]
    public void UpdateMYSQLMonitorStatusCommand_AllProperties_AssignedCorrectly()
    {
        // Arrange
        var command = new UpdateMYSQLMonitorStatusCommand
        {
            Id = "MS001",
            Type = "MYSQL",
            InfraObjectId = "INFRA-001",
            InfraObjectName = "MYSQL Infra",
            WorkflowId = "WF001",
            WorkflowName = "MySQL Backup",
            Properties = "{\"lag\":\"30s\"}",
            ConfiguredRPO = "15m",
            DataLagValue = "20s",
            Threshold = "10"
        };

        // Assert
        Assert.Equal("MS001", command.Id);
        Assert.Equal("MYSQL", command.Type);
        Assert.Equal("INFRA-001", command.InfraObjectId);
        Assert.Equal("MYSQL Infra", command.InfraObjectName);
        Assert.Equal("WF001", command.WorkflowId);
        Assert.Equal("MySQL Backup", command.WorkflowName);
        Assert.Equal("{\"lag\":\"30s\"}", command.Properties);
        Assert.Equal("15m", command.ConfiguredRPO);
        Assert.Equal("20s", command.DataLagValue);
        Assert.Equal("10", command.Threshold);
    }
}
