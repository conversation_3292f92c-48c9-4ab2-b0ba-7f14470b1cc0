﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class TableAccessFilterSpecification : Specification<TableAccess>
{
    public TableAccessFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.TableName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("tablename=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.TableName.Contains(stringItem.Replace("tablename=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
                    else if (stringItem.Contains("schemaname=", StringComparison.InvariantCultureIgnoreCase))
                        Or(p => p.SchemaName.Contains(stringItem.Replace("schemaname=", "",
                            StringComparison.InvariantCultureIgnoreCase)));
            }
            else
            {
                Criteria = p => p.TableName.Contains(searchString) || p.SchemaName.Contains(searchString);
            }
        }
    }
}