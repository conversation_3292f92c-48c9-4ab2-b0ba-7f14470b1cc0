﻿using ContinuityPatrol.Application.Features.Template.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Contracts.Version;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.Template.Commands;

public class UpdateTemplateTests : IClassFixture<TemplateFixture>, IClassFixture<TemplateHistoryFixture>
{
    private readonly TemplateFixture _templateFixture;
    private readonly TemplateHistoryFixture _templateHistoryFixture;
    private readonly Mock<ITemplateRepository> _mockTemplateRepository;
    private readonly Mock<ILoggedInUserService> _mockLogInUserService;
    private readonly Mock<IVersionManager> _mockVersionManager;
    private readonly Mock<ITemplateHistoryRepository> _mockTemplateHistoryRepository;
    private readonly UpdateTemplateCommandHandler _handler;

    public UpdateTemplateTests(TemplateFixture templateFixture, TemplateHistoryFixture templateHistoryFixture)
    {
        _templateFixture = templateFixture;

        _templateHistoryFixture = templateHistoryFixture;

        _mockLogInUserService = new Mock<ILoggedInUserService>();
        _mockLogInUserService.Setup(x => x.CompanyId).Returns("b7c5d56b-afdb-47f8-a15d-bd1a033d3cce");
        _mockLogInUserService.Setup(x => x.LoginName).Returns("test_user");
        _mockLogInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());

        _mockVersionManager = new Mock<IVersionManager>();
        _mockVersionManager.Setup(vm => vm.GetUpdateVersion(It.IsAny<string>())).ReturnsAsync("2.0.0");

        _mockTemplateRepository = TemplateRepositoryMocks.UpdateTemplateRepository(_templateFixture.Templates);
        _mockTemplateHistoryRepository = TemplateHistoryRepositoryMocks.UpdateTemplateHistoryRepository(_templateHistoryFixture.TemplateHistories);

        _handler = new UpdateTemplateCommandHandler(_templateFixture.Mapper, _mockTemplateRepository.Object, _mockVersionManager.Object,
            _mockTemplateHistoryRepository.Object, _mockLogInUserService.Object);
    }

    [Fact]
    public async Task Handle_ValidTemplate_UpdateReferenceIdAsync_ToTemplatesRepo()
    {
        _templateFixture.UpdateTemplateCommand.Id = _templateFixture.Templates[0].ReferenceId;

        var result = await _handler.Handle(_templateFixture.UpdateTemplateCommand, CancellationToken.None);

        var updatedTemplate = await _mockTemplateRepository.Object.GetByReferenceIdAsync(result.TemplateId);

        Assert.Equal(_templateFixture.UpdateTemplateCommand.Name, updatedTemplate.Name);
    }

    [Fact]
    public async Task Handle_Return_ValidTemplateResponse_WhenUpdate_Template()
    {
        _templateFixture.UpdateTemplateCommand.Id = _templateFixture.Templates[0].ReferenceId;

        var result = await _handler.Handle(_templateFixture.UpdateTemplateCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateTemplateResponse));

        result.TemplateId.ShouldBeGreaterThan(0.ToString());

        result.TemplateId.ShouldBe(_templateFixture.UpdateTemplateCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidTemplateId()
    {
        _templateFixture.UpdateTemplateCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_templateFixture.UpdateTemplateCommand, CancellationToken.None));
    }
    [Fact]
    public void Should_Assign_And_Assert_UpdateTemplateCommand()
    {
        // Arrange
        var command = new UpdateTemplateCommand
        {
            Id = "T200",
            Name = "Archive Template",
            Properties = "{\"schedule\":\"weekly\"}",
            Icon = "archive-icon.png",
            Version = "3.1",
            Type = "Archive",
            ReplicationTypeId = "RPT003",
            ReplicationTypeName = "Full",
            ReplicationCategoryTypeId = "RCT002",
            ReplicationCategoryTypeName = "Database",
            SubTypeId = "ST002",
            SubTypeName = "Linux",
            Description = "Template for archiving data",
            ActionType = "Modify",
            Comments = "Version updated with Linux support"
        };

        // Assert
        Assert.Equal("T200", command.Id);
        Assert.Equal("Archive Template", command.Name);
        Assert.Equal("{\"schedule\":\"weekly\"}", command.Properties);
        Assert.Equal("archive-icon.png", command.Icon);
        Assert.Equal("3.1", command.Version);
        Assert.Equal("Archive", command.Type);
        Assert.Equal("RPT003", command.ReplicationTypeId);
        Assert.Equal("Full", command.ReplicationTypeName);
        Assert.Equal("RCT002", command.ReplicationCategoryTypeId);
        Assert.Equal("Database", command.ReplicationCategoryTypeName);
        Assert.Equal("ST002", command.SubTypeId);
        Assert.Equal("Linux", command.SubTypeName);
        Assert.Equal("Template for archiving data", command.Description);
        Assert.Equal("Modify", command.ActionType);
        Assert.Equal("Version updated with Linux support", command.Comments);

        var expectedToString = "Name: Archive Template; Id:T200;";
        Assert.Equal(expectedToString, command.ToString());
    }

    [Fact]
    public async Task Handle_Call_UpdateReferenceIdAsyncMethod_OnlyOnce()
    {
        _templateFixture.UpdateTemplateCommand.Id = _templateFixture.Templates[0].ReferenceId;

        await _handler.Handle(_templateFixture.UpdateTemplateCommand, CancellationToken.None);

        _mockTemplateRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockTemplateRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.Template>()), Times.Once);
    }

}