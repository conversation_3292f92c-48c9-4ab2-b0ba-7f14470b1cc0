using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class CyberAirGapLogFilterSpecification : Specification<CyberAirGapLog>
{
    public CyberAirGapLogFilterSpecification(string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            Criteria = p => p.AirGapName != null;
        }
        else
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("airgap=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.AirGapName.Contains(stringItem.Replace("airgap=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("source=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Source.Contains(stringItem.Replace("source=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("target=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Target.Contains(stringItem.Replace("target=", "",
                            StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("rpo=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.RPO.Contains(stringItem.Replace("rpo=", "", StringComparison.OrdinalIgnoreCase)));
                    else if (stringItem.Contains("status=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.Status.Contains(stringItem.Replace("status=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p =>
                    p.AirGapName.Contains(searchString) || p.Source.Contains(searchString) ||
                    p.Target.Contains(searchString) || p.RPO.Contains(searchString) || p.Status.Contains(searchString);
            }
        }
    }
}