﻿using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.ReplicationMaster.Commands;

public class DeleteReplicationMasterTests : IClassFixture<ReplicationMasterFixture>
{
    private readonly ReplicationMasterFixture _replicationMasterFixture;

    private readonly Mock<IReplicationMasterRepository> _mockReplicationMasterRepository;

    private readonly DeleteReplicationMasterCommandHandler _handler;

    public DeleteReplicationMasterTests(ReplicationMasterFixture replicationMasterFixture)
    {
        _replicationMasterFixture = replicationMasterFixture;

        _mockReplicationMasterRepository =
            ReplicationMasterRepositoryMocks.DeleteReplicationMasterRepository(_replicationMasterFixture
                .ReplicationMasters);

        _handler = new DeleteReplicationMasterCommandHandler(_mockReplicationMasterRepository.Object);
    }

    [Fact]

    public async Task Handle_UpdateIsActiveFalse_When_ReplicationMasterDeleted()
    {
        var result = await _handler.Handle(new DeleteReplicationMasterCommand { Id = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteReplicationMasterResponse_When_ReplicationMasterDeleted()
    {
        var result = await _handler.Handle(new DeleteReplicationMasterCommand {Id = _replicationMasterFixture.ReplicationMasters[0].ReferenceId}, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteReplicationMasterResponse));

        result.Success.ShouldBeTrue();

        result.IsActive.ShouldBeFalse();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_ReplicationMasterDeleted()
    {
        await _handler.Handle(new DeleteReplicationMasterCommand { Id = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

        var accessManager = await _mockReplicationMasterRepository.Object.GetByReferenceIdAsync(_replicationMasterFixture.ReplicationMasters[0].ReferenceId);

        accessManager.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidReplicationMasterId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteReplicationMasterCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteReplicationMasterCommand { Id = _replicationMasterFixture.ReplicationMasters[0].ReferenceId }, CancellationToken.None);

        _mockReplicationMasterRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockReplicationMasterRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.ReplicationMaster>()), Times.Once);
    }
}