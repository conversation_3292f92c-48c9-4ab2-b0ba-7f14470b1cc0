using ContinuityPatrol.Application.Features.DynamicDashboardMap.Events.Update;

namespace ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;

public class
    UpdateDynamicDashboardMapCommandHandler : IRequestHandler<UpdateDynamicDashboardMapCommand,
        UpdateDynamicDashboardMapResponse>
{
    private readonly IDynamicDashboardMapRepository _dynamicDashboardMapRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;
    private readonly IUserRepository _userRepository;

    public UpdateDynamicDashboardMapCommandHandler(IMapper mapper,
        IDynamicDashboardMapRepository dynamicDashboardMapRepository, IPublisher publisher,
        IUserRepository userRepository)
    {
        _mapper = mapper;
        _dynamicDashboardMapRepository = dynamicDashboardMapRepository;
        _publisher = publisher;
        _userRepository = userRepository;
    }

    public async Task<UpdateDynamicDashboardMapResponse> Handle(UpdateDynamicDashboardMapCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _dynamicDashboardMapRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.DynamicDashboardMap), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateDynamicDashboardMapCommand),
            typeof(Domain.Entities.DynamicDashboardMap));

        if (request.IsDefault)
        {
            if (request.Type.Trim().ToLower().Equals("user"))
            {
                var dynamicUserMap = await _dynamicDashboardMapRepository.IsDefaultDashboardByUserId(request.UserId);

                if (dynamicUserMap is not null)
                {
                    dynamicUserMap.IsDefault = false;

                    await _dynamicDashboardMapRepository.UpdateAsync(dynamicUserMap);
                }

                var user = await _userRepository.GetByReferenceIdAsync(eventToUpdate.UserId);

                user.IsDefaultDashboard = request.IsDefault;

                await _userRepository.UpdateAsync(user);
            }
            else if (request.Type.Trim().ToLower().Equals("role"))
            {
                var dynamicRoleMap = await _dynamicDashboardMapRepository.IsDefaultDashboardByRoleId(request.RoleId);

                if (dynamicRoleMap is not null)
                {
                    dynamicRoleMap.IsDefault = false;

                    await _dynamicDashboardMapRepository.UpdateAsync(dynamicRoleMap);
                }

                var userRoles = await _userRepository.GetUsersByRoleId(eventToUpdate.RoleId);

                userRoles.ForEach(x => x.IsDefaultDashboard = request.IsDefault);

                await _userRepository.UpdateRangeAsync(userRoles);
            }
        }

        await _dynamicDashboardMapRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateDynamicDashboardMapResponse
        {
            Message = Message.Update(nameof(Domain.Entities.DynamicDashboardMap), eventToUpdate.DashBoardSubName),

            Id = eventToUpdate.ReferenceId
        };

        await _publisher.Publish(new DynamicDashboardMapUpdatedEvent { Name = eventToUpdate.UserName },
            cancellationToken);

        return response;
    }
}