using ContinuityPatrol.Domain.ViewModels.BackUpLogModel;

namespace ContinuityPatrol.Application.Features.BackUpLog.Queries.GetList;

public class GetBackUpLogListQueryHandler : IRequestHandler<GetBackUpLogListQuery, List<BackUpLogListVm>>
{
    private readonly IBackUpLogRepository _backUpLogRepository;
    private readonly IMapper _mapper;

    public GetBackUpLogListQueryHandler(IMapper mapper, IBackUpLogRepository backUpLogRepository)
    {
        _mapper = mapper;
        _backUpLogRepository = backUpLogRepository;
    }

    public async Task<List<BackUpLogListVm>> Handle(GetBackUpLogListQuery request, CancellationToken cancellationToken)
    {
        var backUpLogs = await _backUpLogRepository.ListAllAsync();

        if (backUpLogs.Count <= 0) return new List<BackUpLogListVm>();

        return _mapper.Map<List<BackUpLogListVm>>(backUpLogs);
    }
}