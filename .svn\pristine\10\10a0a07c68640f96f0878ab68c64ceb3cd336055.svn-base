﻿using ContinuityPatrol.Application.Features.SingleSignOn.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SingleSignOn.Queries;

public class GetSingleSignOnNameUniqueQueryHandlerTests : IClassFixture<SingleSignOnFixture>
{
    private readonly SingleSignOnFixture _singleSignOnFixture;
    private Mock<ISingleSignOnRepository> _mockSingleSignOnRepository;
    private readonly GetSingleSignOnNameUniqueQueryHandler _handler;

    public GetSingleSignOnNameUniqueQueryHandlerTests(SingleSignOnFixture singleSignOnFixture)
    {
        _singleSignOnFixture = singleSignOnFixture;

        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnNameUniqueRepository(_singleSignOnFixture.SingleSignOns);

        _handler = new GetSingleSignOnNameUniqueQueryHandler(_mockSingleSignOnRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_SingleSignOn_ProfileName_Exist()
    {
        _singleSignOnFixture.SingleSignOns[0].ProfileName = "PTS";
        _singleSignOnFixture.SingleSignOns[0].Id = 1;

        var result = await _handler.Handle(new GetSingleSignOnNameUniqueQuery { ProfileName = _singleSignOnFixture.SingleSignOns[0].ProfileName, SingleSignOnId = _singleSignOnFixture.SingleSignOns[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_SingleSignOn_ProfileNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetSingleSignOnNameUniqueQuery { ProfileName = "PTC", SingleSignOnId = _singleSignOnFixture.SingleSignOns[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsProfileNameExistMethod_OneTime()
    {
        await _handler.Handle(new GetSingleSignOnNameUniqueQuery(), CancellationToken.None);

        _mockSingleSignOnRepository.Verify(x => x.IsProfileNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_SingleSignOn_ProfileName_NotMatch()
    {
        var result = await _handler.Handle(new GetSingleSignOnNameUniqueQuery { ProfileName = "PTC", SingleSignOnId = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockSingleSignOnRepository = SingleSignOnRepositoryMocks.GetSingleSignOnEmptyRepository();

        var result = await _handler.Handle(new GetSingleSignOnNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}