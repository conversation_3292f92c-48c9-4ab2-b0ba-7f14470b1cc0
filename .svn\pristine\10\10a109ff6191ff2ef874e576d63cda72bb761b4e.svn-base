﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowHistory.Events.Delete;

public class WorkflowHistoryDeletedEventHandler : INotificationHandler<WorkflowHistoryDeletedEvent>
{
    private readonly ILogger<WorkflowHistoryDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowHistoryDeletedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowHistoryDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowHistoryDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"WorkflowHistory '{deletedEvent.WorkflowHistoryName}' Deleted successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.WorkflowHistory}",
            Entity = Modules.WorkflowHistory.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"WorkflowHistory '{deletedEvent.WorkflowHistoryName}' Deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}