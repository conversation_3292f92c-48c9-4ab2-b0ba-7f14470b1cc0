﻿using System.Text.Json.Serialization;

namespace ContinuityPatrol.Application.Features.Workflow.Commands.Create;

public class CreateWorkflowCommand : IRequest<CreateWorkflowResponse>
{
    public string Name { get; set; }

    public string Properties { get; set; }

    public string Comments { get; set; }

    [JsonIgnore] public string CompanyId { get; set; }

    [JsonIgnore] public string Version { get; set; }

    [JsonIgnore] public bool IsPublish { get; set; }

    public override string ToString()
    {
        return $"Name: {Name};";
    }
}