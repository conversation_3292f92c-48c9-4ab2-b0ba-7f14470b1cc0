﻿using ContinuityPatrol.Application.Features.DRReadyLog.Queries.GetByLast7Days;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.DRReadyLog.Queries;

public class GetDrReadyLogByLast7DaysQueryHandlerTests : IClassFixture<DrReadyLogFixture>
{
    private readonly DrReadyLogFixture _drReadyLogFixture;

    private readonly Mock<IDrReadyLogRepository> _drReadyLogRepositoryMock;

    private readonly GetDRReadyLogByLast7DaysQueryHandler _handler;

    public GetDrReadyLogByLast7DaysQueryHandlerTests(DrReadyLogFixture drReadyLogFixture)
    {
        _drReadyLogFixture = drReadyLogFixture;
    
        _drReadyLogRepositoryMock = DrReadyLogRepositoryMocks.GetDrReadyLogByLast7DaysRepository(_drReadyLogFixture.DrReadyLogs);
        
        _handler = new GetDRReadyLogByLast7DaysQueryHandler(_drReadyLogFixture.Mapper, _drReadyLogRepositoryMock.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_DrReadyLogCount()
    {
        var result = await _handler.Handle(new GetDRReadyLogByLast7DaysQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DRReadyLogByLast7DaysVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_ValidDrReadyLogList()
    {
        var result = await _handler.Handle(new GetDRReadyLogByLast7DaysQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<DRReadyLogByLast7DaysVm>>();

        result.Count.ShouldBe(_drReadyLogFixture.DrReadyLogs.Count);

        result[0].Id.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].ReferenceId);
        result[0].WorkflowId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].WorkflowName);
        result[0].InfraObjectId.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectId);
        result[0].InfraObjectName.ShouldBe(_drReadyLogFixture.DrReadyLogs[0].InfraObjectName);
    }

    [Fact]
    public async Task Handle_Call_GetDRReadyLogByLast7DaysMethod_OneTime()
    {
        await _handler.Handle(new GetDRReadyLogByLast7DaysQuery(), CancellationToken.None);

        _drReadyLogRepositoryMock.Verify(x => x.GetDrReadyLogByLast7Days(), Times.Once);
    }
}