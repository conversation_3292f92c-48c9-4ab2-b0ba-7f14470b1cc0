﻿
@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<div class="page-content" style="overflow-y: auto; overflow-x: hidden; height: calc(100vh - 66px);">
    <div class="header pb-2" style="position:sticky; top: -8px; z-index: 999; background-color: #f5f5f7;">
        <h6 class="page_title">
            <i class="cp-monitoring"></i><span> Storage_Mysql_FullDB_SVC_Repli</span>
        </h6>
        <span><i class="cp-"></i>Last Monitored Time : 12/9/2022 1:39:31 PM</span>
    </div>
    <div class="monitor_pages">
        <div class="row g-2 mt-0">
            <div class="col-7 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Mysql FullDB SVC Infra</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>MySql Full DB Summary</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-ip-address me-1"></i>IP Address/HostName</td>
                                    <td class="text-truncate">*************</td>
                                    <td class="text-truncate">*************</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-server me-1"></i>Server Name</td>
                                    <td class="text-truncate">MySQL NLS Ser_Prod</td>
                                    <td class="text-truncate">MySQL NLS Ser_DR</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-database me-1"></i>Database Name</td>
                                    <td class="text-truncate">mysql</td>
                                    <td class="text-truncate">mysql</td>
                                </tr>                               
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">MySql Full DB SVC</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Relationship Monitoring</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-hand1 me-1"></i>Relationship Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-teams me-1"></i>Group Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-hand1 me-1"></i>Relationship Primary Value</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-master-change me-1"></i>Master Change Volume Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-volume-adjustment me-1"></i>Auxiliary Change Volume Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-relationship-state  me-1"></i>Relationship State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-relationship-progress me-1"></i>Relationship Progress</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
               
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC Storage Pool Monitoring</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Clustered System Node Monitoring</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-network me-1"></i>Clustered System Node ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC Clusered System Node Detailed Monitoring</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Node Detailed Monitoring</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-network me-1"></i>Node ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-network me-1"></i>Node Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-network me-1"></i>Node Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-5 d-grid">
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">Solution Diagram</div>
                    <div class="card-body text-center">
                        <img src="~/img/isomatric/solutiondiagram.svg" height="259px;" />
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC-Metro or Global Mirror Consistency Group Monitoring</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-server me-1"></i>Group ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-user-n me-1"></i>Group Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-cluster-database me-1"></i>Master Cluster Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-auxiliary-cluster-name me-1"></i>Auxiliary Cluster Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-icon-park me-1"></i>Group Primary Value</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-box-dobble-arrow me-1"></i>Group State</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-weather-snow-flake me-1"></i>Group Freeze Time</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-user-status me-1"></i>Group Status </td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-user-refresh me-1"></i>Group Sync Status</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-mirror me-1"></i>Mirror Relationship Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-user-one me-1"></i>Group Relationship Count & Names</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card Card_Design_None mb-2">
                    <div class="card-header card-title">SVC Storage Controller Monitoring</div>
                    <div class="card-body pt-0 px-2">
                        <table style="table-layout:fixed" class="table mb-0">
                            <thead>
                                <tr>
                                    <th>Component</th>
                                    <th class="text-primary">Primary</th>
                                    <th >DR</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-disk-controller me-1"></i>Disk Controller ID</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-disk-controller me-1"></i>Disk Controller Name</td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-degrade-disk me-1"></i>Mdisks Degrade Status </td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                                <tr>
                                    <td class="text-truncate fw-semibold"><i class="text-secondary cp-controller-product-id me-1"></i>Controller Product ID </td>
                                    <td class="text-truncate">NA</td>
                                    <td class="text-truncate">NA</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
