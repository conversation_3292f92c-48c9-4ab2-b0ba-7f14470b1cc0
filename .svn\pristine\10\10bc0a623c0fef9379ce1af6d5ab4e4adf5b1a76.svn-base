﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObject.Validators;

public class UpdateInfraObjectValidatorTests
{
    private readonly Mock<IInfraObjectRepository> _mockInfraObjectRepository;
    public List<Domain.Entities.InfraObject> InfraObjects { get; set; }

    public UpdateInfraObjectValidatorTests()
    {
        InfraObjects = new Fixture().Create<List<Domain.Entities.InfraObject>>();

        _mockInfraObjectRepository = InfraObjectRepositoryMocks.UpdateInfraObjectRepository(InfraObjects);
    }

    //Name

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Name_InInfraObject_WithEmpty(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Name_InInfraObject_IsNull(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = null;
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameNotNullRequired, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Name_InInfraObject_MinimumRange(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "AB";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameRangeRequired, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Name_InInfraObject_MaximumRange(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionContainsInvalid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "  PTS  ";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_DoubleSpace_InFront(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "  PTS Technosoft";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_DoubleSpace_InBack(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "PTS Technosoft  ";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_TripleSpace_InBetween(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "Pts   India";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_SpecialCharacters_InFront(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "@#PtsIndia";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_SpecialCharacters_Only(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "@#$%%^&&*><{";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_Underscore_InFront(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "_PTS";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_Underscore_InFront_AndBack(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "_PTS_";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_Numbers_InFront(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "145PTS";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_UnderscoreAndNumbers_InFront_AndUnderScore_InBack(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "_145PTS_";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_Underscore_InFront_AndNumbers_InBack(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "_PTS345";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_Valid_Name_InInfraObject_With_Numbers_Only(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.Name = "123456788900";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectNameValid, validateResult.Errors[0].ErrorMessage);
    }


    //Description

    //[Theory]
    //[AutoInfraObjectData]
    //public async Task Verify_Update_Description_InInfraObject_IsNull(UpdateInfraObjectCommand updateInfraObjectCommand)
    //{
    //    var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

    //    updateInfraObjectCommand.Description = null;
    //    updateInfraObjectCommand.BusinessServiceId = "1c9187b5-b045-4d6b-ab70-0132a2ee2d31";
    //    updateInfraObjectCommand.BusinessFunctionId = "82fda2ff-73c3-4efc-b836-d27ccdff47de";

    //    var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionNotNullRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //[Theory]
    //[AutoInfraObjectData]
    //public async Task Verify_Update_Description_InInfraObject_MaximumRange(UpdateInfraObjectCommand updateInfraObjectCommand)
    //{
    //    var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

    //    updateInfraObjectCommand.Description = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    //    updateInfraObjectCommand.BusinessServiceId = "1c9187b5-b045-4d6b-ab70-0132a2ee2d31";
    //    updateInfraObjectCommand.BusinessFunctionId = "82fda2ff-73c3-4efc-b836-d27ccdff47de";

    //    var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
    //    Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionRangeRequired, validateResult.Errors[1].ErrorMessage);
    //}

    //BusinessFunctionName

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_BusinessFunctionName_InInfraObject_WithEmpty(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.BusinessFunctionName = "";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectDescriptionContainsInvalid, validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_BusinessFunctionName_InInfraObject_IsNull(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.BusinessFunctionName = null;
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalFunctionNameValid, validateResult.Errors[2].ErrorMessage);
    }

    //BusinessServiceName

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_BusinessServiceName_InInfraObject_WithEmpty(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.BusinessServiceName = "";
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalFunctionRequired, validateResult.Errors[2].ErrorMessage);
    }

    [Theory]
    [AutoInfraObjectData]
    public async Task Verify_Update_BusinessServiceName_InInfraObject_IsNull(UpdateInfraObjectCommand updateInfraObjectCommand)
    {
        var validator = new UpdateInfraObjectCommandValidator(_mockInfraObjectRepository.Object);

        updateInfraObjectCommand.BusinessServiceName = null;
        updateInfraObjectCommand.Id = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessServiceId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.BusinessFunctionId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.ReplicationCategoryTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.IsAssociateInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PairInfraObjectId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.SubTypeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRNodeId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRServerId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRDatabaseId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.PRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.DRReplicationId = Guid.NewGuid().ToString();
        updateInfraObjectCommand.NearDRReplicationId = Guid.NewGuid().ToString();

        var validateResult = await validator.ValidateAsync(updateInfraObjectCommand, CancellationToken.None);
        Assert.Equal(ValidatorConstants.InfraObject.InfraObjectOperationalServiceRequired, validateResult.Errors[3].ErrorMessage);
    }
}