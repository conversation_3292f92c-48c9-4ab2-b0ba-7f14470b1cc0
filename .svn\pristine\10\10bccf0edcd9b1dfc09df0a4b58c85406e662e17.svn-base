using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.BackUp.Commands.Delete;
using ContinuityPatrol.Application.Features.BackUp.Events.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.BackUp.Commands;

public class DeleteBackUpTests : IClassFixture<BackUpFixture>
{
    private readonly BackUpFixture _backUpFixture;
    private readonly Mock<IBackUpRepository> _mockBackUpRepository;
    private readonly Mock<IPublisher> _mockPublisher;
    private readonly DeleteBackUpCommandHandler _handler;

    public DeleteBackUpTests(BackUpFixture backUpFixture)
    {
        _backUpFixture = backUpFixture;
        _mockBackUpRepository = BackUpRepositoryMocks.CreateBackUpRepository(_backUpFixture.BackUps);
        _mockPublisher = new Mock<IPublisher>();

        _handler = new DeleteBackUpCommandHandler(
            _mockBackUpRepository.Object,
            _mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_DeleteBackUp_When_ValidCommand()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new DeleteBackUpCommand { Id = existingBackUp.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.GetByReferenceIdAsync(existingBackUp.ReferenceId), Times.Once);
        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => b.IsActive == false)), Times.Once);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteActiveBackUp_When_ValidCommand()
    {
        // Arrange
        var activeBackUp = _backUpFixture.BackUps.First(x => x.IsActive);
        var command = new DeleteBackUpCommand { Id = activeBackUp.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.ReferenceId == activeBackUp.ReferenceId && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteFullBackup_When_ValidCommand()
    {
        // Arrange
        var fullBackup = _backUpFixture.BackUps.First(x => x.BackUpType == "Full");
        var command = new DeleteBackUpCommand { Id = fullBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.BackUpType == "Full" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteDifferentialBackup_When_ValidCommand()
    {
        // Arrange
        // Add a differential backup to the fixture
        var differentialBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "DiffServer",
            DatabaseName = "DiffDatabase",
            UserName = "DiffUser",
            Password = "DiffPassword123",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"D:\Backups\DiffDatabase.bak",
            BackUpType = "Differential",
            CronExpression = "0 0 */6 * * ?",
            ScheduleType = "Hourly",
            ScheduleTime = "06:00",
            KeepBackUpLast = "14",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "DiffNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(differentialBackup);

        var command = new DeleteBackUpCommand { Id = differentialBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.BackUpType == "Differential" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteTransactionLogBackup_When_ValidCommand()
    {
        // Arrange
        // Add a transaction log backup to the fixture
        var transactionLogBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "LogServer",
            DatabaseName = "LogDatabase",
            UserName = "LogUser",
            Password = "LogPassword123",
            IsLocalServer = true,
            IsBackUpServer = true,
            BackUpPath = @"E:\Logs\LogDatabase.trn",
            BackUpType = "Transaction Log",
            CronExpression = "0 */15 * * * ?",
            ScheduleType = "Minutely",
            ScheduleTime = "15",
            KeepBackUpLast = "7",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "LogNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(transactionLogBackup);

        var command = new DeleteBackUpCommand { Id = transactionLogBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.BackUpType == "Transaction Log" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteDailyScheduledBackup_When_ValidCommand()
    {
        // Arrange
        // Add a daily scheduled backup to the fixture
        var dailyBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "DailyServer",
            DatabaseName = "DailyDatabase",
            UserName = "DailyUser",
            Password = "DailyPassword123",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\DailyDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 2 * * ?",
            ScheduleType = "Daily",
            ScheduleTime = "02:00",
            KeepBackUpLast = "30",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "DailyNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(dailyBackup);

        var command = new DeleteBackUpCommand { Id = dailyBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.ScheduleType == "Daily" && 
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteWeeklyScheduledBackup_When_ValidCommand()
    {
        // Arrange
        // Add a weekly scheduled backup to the fixture
        var weeklyBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "WeeklyServer",
            DatabaseName = "WeeklyDatabase",
            UserName = "WeeklyUser",
            Password = "WeeklyPassword123",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\WeeklyDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 2 ? * SUN",
            ScheduleType = "Weekly",
            ScheduleTime = "02:00",
            KeepBackUpLast = "12",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "WeeklyNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(weeklyBackup);

        var command = new DeleteBackUpCommand { Id = weeklyBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.ScheduleType == "Weekly" && 
            b.CronExpression.Contains("SUN") &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpNotFound()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();
        var command = new DeleteBackUpCommand { Id = nonExistentId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBackUpRepository.Verify(x => x.GetByReferenceIdAsync(nonExistentId), Times.Once);
        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()), Times.Never);
        _mockPublisher.Verify(x => x.Publish(It.IsAny<BackUpDeletedEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_BackUpIsNull()
    {
        // Arrange
        var nullId = Guid.NewGuid().ToString();
        _mockBackUpRepository.Setup(x => x.GetByReferenceIdAsync(nullId))
            .ReturnsAsync((Domain.Entities.BackUp)null);

        var command = new DeleteBackUpCommand { Id = nullId };

        // Act & Assert
        await Should.ThrowAsync<NotFoundException>(async () =>
            await _handler.Handle(command, CancellationToken.None));

        _mockBackUpRepository.Verify(x => x.GetByReferenceIdAsync(nullId), Times.Once);
        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.BackUp>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PublishEvent_When_BackUpDeleted()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new DeleteBackUpCommand { Id = existingBackUp.ReferenceId };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        command.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_SoftDelete_When_BackUpDeleted()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var originalIsActive = existingBackUp.IsActive;
        var command = new DeleteBackUpCommand { Id = existingBackUp.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();

        _mockBackUpRepository.Verify(x => x.DeleteAsync(It.IsAny<Domain.Entities.BackUp>()), Times.Never);
    }

    [Fact]
    public async Task Handle_PreserveOtherProperties_When_BackUpDeleted()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var originalHostName = existingBackUp.HostName;
        var originalDatabaseName = existingBackUp.DatabaseName;
        var originalUserName = existingBackUp.UserName;
        var originalBackUpPath = existingBackUp.BackUpPath;
        var originalBackUpType = existingBackUp.BackUpType;
        var originalScheduleType = existingBackUp.ScheduleType;
        var originalCronExpression = existingBackUp.CronExpression;

        var command = new DeleteBackUpCommand { Id = existingBackUp.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.ReferenceId == existingBackUp.ReferenceId &&
            b.HostName == originalHostName &&
            b.DatabaseName == originalDatabaseName &&
            b.UserName == originalUserName &&
            b.BackUpPath == originalBackUpPath &&
            b.BackUpType == originalBackUpType &&
            b.ScheduleType == originalScheduleType &&
            b.CronExpression == originalCronExpression &&
            b.IsActive == false)), Times.Once);
    }


    [Fact]
    public async Task Handle_ReturnCorrectResponse_When_BackUpDeleted()
    {
        // Arrange
        var existingBackUp = _backUpFixture.BackUps.First();
        var command = new DeleteBackUpCommand { Id = existingBackUp.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<DeleteBackUpResponse>();
        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_DeleteRemoteBackup_When_ValidCommand()
    {
        // Arrange
        // Add a remote backup to the fixture
        var remoteBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "RemoteServer",
            DatabaseName = "RemoteDatabase",
            UserName = "RemoteUser",
            Password = "RemotePassword123",
            IsLocalServer = false,
            IsBackUpServer = true,
            BackUpPath = @"\\RemoteServer\Backups\RemoteDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 3 * * ?",
            ScheduleType = "Daily",
            ScheduleTime = "03:00",
            KeepBackUpLast = "90",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "RemoteNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(remoteBackup);

        var command = new DeleteBackUpCommand { Id = remoteBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.IsLocalServer == false && 
            b.IsBackUpServer == true &&
            b.BackUpPath.StartsWith(@"\\") &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteMonthlyScheduledBackup_When_ValidCommand()
    {
        // Arrange
        // Add a monthly scheduled backup to the fixture
        var monthlyBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "MonthlyServer",
            DatabaseName = "MonthlyDatabase",
            UserName = "MonthlyUser",
            Password = "MonthlyPassword123",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\MonthlyDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 2 1 * ?",
            ScheduleType = "Monthly",
            ScheduleTime = "02:00",
            KeepBackUpLast = "12",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "MonthlyNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(monthlyBackup);

        var command = new DeleteBackUpCommand { Id = monthlyBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.ScheduleType == "Monthly" && 
            b.KeepBackUpLast == "12" &&
            b.IsActive == false)), Times.Once);
    }

    [Fact]
    public async Task Handle_DeleteBackupWithLongRetention_When_ValidCommand()
    {
        // Arrange
        // Add a backup with long retention to the fixture
        var longRetentionBackup = new Domain.Entities.BackUp
        {
            ReferenceId = Guid.NewGuid().ToString(),
            HostName = "LongRetentionServer",
            DatabaseName = "LongRetentionDatabase",
            UserName = "LongRetentionUser",
            Password = "LongRetentionPassword123",
            IsLocalServer = true,
            IsBackUpServer = false,
            BackUpPath = @"C:\Backups\LongRetentionDatabase.bak",
            BackUpType = "Full",
            CronExpression = "0 0 2 * * ?",
            ScheduleType = "Daily",
            ScheduleTime = "02:00",
            KeepBackUpLast = "365",
            NodeId = Guid.NewGuid().ToString(),
            NodeName = "LongRetentionNode",
            IsActive = true
        };
        _backUpFixture.BackUps.Add(longRetentionBackup);

        var command = new DeleteBackUpCommand { Id = longRetentionBackup.ReferenceId };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.ShouldNotBeNull();
        result.IsActive.ShouldBeFalse();

        _mockBackUpRepository.Verify(x => x.UpdateAsync(It.Is<Domain.Entities.BackUp>(b => 
            b.KeepBackUpLast == "365" &&
            b.IsActive == false)), Times.Once);
    }
}
