using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.ServerSubType.Events.Create;

public class ServerSubTypeCreatedEventHandler : INotificationHandler<ServerSubTypeCreatedEvent>
{
    private readonly ILogger<ServerSubTypeCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public ServerSubTypeCreatedEventHandler(ILoggedInUserService userService,
        ILogger<ServerSubTypeCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(ServerSubTypeCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            CompanyId = _userService.CompanyId,
            Action = $"{ActivityType.Create} {Modules.ServerSubType}",
            Entity = Modules.ServerSubType.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"ServerSubType '{createdEvent.Name}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"ServerSubType '{createdEvent.Name}' created successfully.");
    }
}