﻿using ContinuityPatrol.Application.Features.OracleMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.OracleMonitorLogs.Queries;
public class GetOracleMonitorLogsDetailQueryHandlerTests
{
    private readonly Mock<IOracleMonitorLogsRepository> _repositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly GetOracleMonitorLogsDetailQueryHandler _handler;

    public GetOracleMonitorLogsDetailQueryHandlerTests()
    {
        var log = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = "ref-001",
            WorkflowName = "Test message",
            IsActive = true
        };
        _repositoryMock = new Mock<IOracleMonitorLogsRepository>();
        _mapperMock = new Mock<IMapper>();
        _repositoryMock = OracleMonitorLogsRepositoryMocks.GetOracleMonitorLogsRepository(log);
        _handler = new GetOracleMonitorLogsDetailQueryHandler(_repositoryMock.Object, _mapperMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedVm_WhenEntityExists()
    {
        // Arrange
        var id = "Log123";
        var entity = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = id,
            Type = "Oracle",
            InfraObjectId = "INFRA01",
            InfraObjectName = "NodeA",
            WorkflowId = "WF01",
            WorkflowName = "WorkflowX",
            Properties = "props",
            ConfiguredRPO = "30s",
            DataLagValue = "15s",
            IsActive = true
        };

        var viewModel = new OracleMonitorLogsDetailVm
        {
            Id = id,
            Type = entity.Type,
            InfraObjectId = entity.InfraObjectId,
            InfraObjectName = entity.InfraObjectName,
            WorkflowId = entity.WorkflowId,
            WorkflowName = entity.WorkflowName,
            Properties = entity.Properties,
            ConfiguredRPO = entity.ConfiguredRPO,
            DataLagValue = entity.DataLagValue
        };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map<OracleMonitorLogsDetailVm>(entity)).Returns(viewModel);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(id);
        result.Type.Should().Be("Oracle");

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(id), Times.Once);
        _mapperMock.Verify(m => m.Map<OracleMonitorLogsDetailVm>(entity), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsNull()
    {
        // Arrange
        var id = "InvalidID";
        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync((Domain.Entities.OracleMonitorLogs)null!);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"OracleMonitorLogs (InvalidID) is not found or not authorized");

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(id), Times.Once);
        _mapperMock.Verify(m => m.Map<OracleMonitorLogsDetailVm>(It.IsAny<Domain.Entities.OracleMonitorLogs>()), Times.Never);
    }

    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenMappedResultIsNull()
    {
        // Arrange
        var id = "MapNull";
        var entity = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = id,
            Type = "Oracle",
            IsActive = true
        };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map<OracleMonitorLogsDetailVm>(entity)).Returns((OracleMonitorLogsDetailVm)null!);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"OracleMonitorLogs (MapNull) is not found or not authorized");

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(id), Times.Once);
        _mapperMock.Verify(m => m.Map<OracleMonitorLogsDetailVm>(entity), Times.Once);
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsInactive()
    {
        // Arrange
        var id = "Inactive123";
        var entity = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = id,
            Type = "Oracle",
            IsActive = false // key for deactivation scenario
        };

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"OracleMonitorLogs (Inactive123) is not found or not authorized");

        _repositoryMock.Verify(r => r.GetByReferenceIdAsync(id), Times.Once);
        _mapperMock.Verify(m => m.Map<OracleMonitorLogsDetailVm>(It.IsAny<Domain.Entities.OracleMonitorLogs>()), Times.Never);
    }
    [Fact]
    public async Task Handle_ShouldRespectCancellationToken()
    {
        // Arrange
        var id = "Cancellable123";
        var entity = new Domain.Entities.OracleMonitorLogs
        {
            ReferenceId = id,
            Type = "Oracle",
            IsActive = true
        };

        var vm = new OracleMonitorLogsDetailVm
        {
            Id = id,
            Type = entity.Type
        };

        using var cts = new CancellationTokenSource();
        var token = cts.Token;

        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);
        _mapperMock.Setup(m => m.Map<OracleMonitorLogsDetailVm>(entity)).Returns(vm);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        var result = await _handler.Handle(request, token);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(id);
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenReferenceIdIsWhitespace()
    {
        // Arrange
        var id = " ";
        _repositoryMock.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync((Domain.Entities.OracleMonitorLogs)null!);

        var request = new GetOracleMonitorLogsDetailQuery { Id = id };

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage($"OracleMonitorLogs ( ) is not found or not authorized");
    }
    [Fact]
    public async Task Handle_ShouldThrowNotFoundException_WhenIdIsNull()
    {
        // Arrange
        var request = new GetOracleMonitorLogsDetailQuery { Id = null! };

        // Act
        Func<Task> act = async () => await _handler.Handle(request, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<NotFoundException>()
            .WithMessage("OracleMonitorLogs () is not found or not authorized");
    }
    [Fact]
    public void OracleMonitorLogsDetailVm_Should_Have_CorrectValues()
    {
        // Arrange & Act
        var vm = new OracleMonitorLogsDetailVm
        {
            Id = "log123",
            Type = "RAC",
            InfraObjectId = "infra456",
            InfraObjectName = "Oracle RAC Cluster",
            WorkflowId = "workflow789",
            WorkflowName = "Backup Workflow",
            Properties = "{\"Key\":\"Value\"}",
            ConfiguredRPO = "15min",
            DataLagValue = "10min"
        };

        // Assert
        Assert.Equal("log123", vm.Id);
        Assert.Equal("RAC", vm.Type);
        Assert.Equal("infra456", vm.InfraObjectId);
        Assert.Equal("Oracle RAC Cluster", vm.InfraObjectName);
        Assert.Equal("workflow789", vm.WorkflowId);
        Assert.Equal("Backup Workflow", vm.WorkflowName);
        Assert.Equal("{\"Key\":\"Value\"}", vm.Properties);
        Assert.Equal("15min", vm.ConfiguredRPO);
        Assert.Equal("10min", vm.DataLagValue);
    }


}