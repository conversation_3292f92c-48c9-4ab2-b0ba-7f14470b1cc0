﻿using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByCompanyId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByPONumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPoNumber;
using ContinuityPatrol.Domain.ViewModels.LicenseInfoModel;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;
using ContinuityPatrol.Shared.Core.Responses;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ILicenseManagerService
{
    Task<List<LicenseManagerDetailViewVm>> LicenseManagerDetailView();
    Task<List<LicenseManagerListVm>> GetLicenseManagerList();
    Task<BaseResponse> CreateBaseLicense(CreateBaseLicenseCommand createBaseLicenseCommand);
    Task<BaseResponse> UpdateBaseLicense(UpdateBaseLicenseCommand updateBaseLicenseCommand);
    Task<BaseResponse> CreateDerivedLicense(CreateDerivedLicenseCommand createDerivedLicenseCommand);
    Task<BaseResponse> UpdateDerivedLicense(UpdateDerivedLicenseCommand updateDerivedLicenseCommand);
    Task<BaseResponse> DeleteAsync(string id);
    Task<LicenseManagerDetailVm> GetLicenseManagerById(string id);
    Task<BaseResponse> DeleteDerivedLicense(string id);
    Task<List<LicenseManagerNameVm>> GetAllPoNumbers();
    Task<List<ChildLicenseDetailByParentIdVm>> GetLicenseByParentIdAndParentPoNumber(string parentId, string parentPoNumber);
    Task<LicenseManagerByPoNumberVm> GetLicenseManagerByPoNumber(string poNumber);
    Task<GetLicenseByPONumberVm> GetLicenseDetailsByPoNumber(string poNumber);
    Task<List<LicenseInfoByEntityListVm>> GetLicenseInfoByEntity(string licenseId, string entity);
    Task<object> GetDecommissionByIdAndEntityId(string licenseId, string entityId, string entityType);
    Task<BaseResponse> DeleteDecommissionByEntityId(string entityId, string licenseId, string entityType, string entityName);
    Task<LicenseCountVm> GetLicenseManagerCount();
    Task<BaseResponse> ReplaceLicenseDetails(LicenseReplaceCommand command);
    Task<List<GetLicenseByCompanyIdVm>> GetLicenseByCompanyId(string companyId);
    Task<List<LicenseExpireListVm>> GetLicenseExpiresByCompanyId(string companyId);
    Task<BaseResponse> UpdateLicenseState(UpdateLicenseStateCommand updateLicenseStateCommand);

    Task<List<GetPoNumberListVm>> GetPoNumber(string type, string roleType, string siteId, string serverId,
        string replicationType,string databaseTypeId);
    Task<List<LicenseExpireListVm>> GetLicenseAMCExpiresByCompanyId(string companyId);
}