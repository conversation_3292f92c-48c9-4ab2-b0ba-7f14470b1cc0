﻿using ContinuityPatrol.Application.Features.PageWidget.Events.Create;

namespace ContinuityPatrol.Application.UnitTests.Features.PageWidget.Events;

public class PageWidgetCreatedEventHandlerTests
{
    private readonly Mock<ILogger<PageWidgetCreatedEventHandler>> _mockLogger;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly PageWidgetCreatedEventHandler _handler;

    public PageWidgetCreatedEventHandlerTests()
    {
        _mockLogger = new Mock<ILogger<PageWidgetCreatedEventHandler>>();
        _mockUserActivityRepository = new Mock<IUserActivityRepository>();
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _handler = new PageWidgetCreatedEventHandler(_mockLoggedInUserService.Object, _mockLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_ShouldLogInformation_WhenEventIsHandledSuccessfully()
    {
        var pageWidgetEvent = new PageWidgetCreatedEvent { Name = "TestWidget" };
        var userDetails = new Domain.Entities.UserActivity
        {
            ReferenceId = "123"
        };
        _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
        _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
        _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
        _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

        _mockUserActivityRepository.Setup(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()))
            .ReturnsAsync(userDetails);

        await _handler.Handle(pageWidgetEvent, CancellationToken.None);

      

        _mockUserActivityRepository.Verify(r => r.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldSaveUserActivity_WhenEventIsHandledSuccessfully()
    {
        var pageWidgetEvent = new PageWidgetCreatedEvent { Name = "TestWidget" };

        _mockLoggedInUserService.Setup(u => u.UserId).Returns("123");
        _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
        _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
        _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

        await _handler.Handle(pageWidgetEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(r =>
            r.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                ua.UserId == "123" &&
                ua.LoginName == "TestUser" &&
                ua.RequestUrl == "http://test.url" &&
                ua.CompanyId == "456" &&
                ua.HostAddress == "127.0.0.1" &&
                ua.Action == "Create PageWidget" &&
                ua.Entity == "PageWidget" &&
                ua.ActivityType == "Create" &&
                ua.ActivityDetails == $"PageWidget '{pageWidgetEvent.Name}' created successfully.")),
            Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldUseDefaultGuid_WhenUserIdIsNullOrEmpty()
    {
        var pageWidgetEvent = new PageWidgetCreatedEvent { Name = "TestWidget" };

        _mockLoggedInUserService.Setup(u => u.UserId).Returns(string.Empty);
        _mockLoggedInUserService.Setup(u => u.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(u => u.RequestedUrl).Returns("http://test.url");
        _mockLoggedInUserService.Setup(u => u.CompanyId).Returns("456");
        _mockLoggedInUserService.Setup(u => u.IpAddress).Returns("127.0.0.1");

        await _handler.Handle(pageWidgetEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(r =>
            r.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
                !string.IsNullOrEmpty(ua.CreatedBy) &&
                !string.IsNullOrEmpty(ua.LastModifiedBy))),
            Times.Once);
    }
}
