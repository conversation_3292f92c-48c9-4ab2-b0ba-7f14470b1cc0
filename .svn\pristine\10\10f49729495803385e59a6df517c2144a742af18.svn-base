﻿namespace ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Delete;

public class
    DeleteReplicationMasterCommandHandler : IRequestHandler<DeleteReplicationMasterCommand,
        DeleteReplicationMasterResponse>
{
    private readonly IReplicationMasterRepository _replicationMasterRepository;

    public DeleteReplicationMasterCommandHandler(IReplicationMasterRepository replicationMasterRepository)
    {
        _replicationMasterRepository = replicationMasterRepository;
    }

    public async Task<DeleteReplicationMasterResponse> Handle(DeleteReplicationMasterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _replicationMasterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToUpdate, nameof(Domain.Entities.ReplicationMaster),
            new NotFoundException(nameof(Domain.Entities.ReplicationMaster), request.Id));

        eventToUpdate.IsActive = false;

        await _replicationMasterRepository.UpdateAsync(eventToUpdate);

        var response = new DeleteReplicationMasterResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.ReplicationMaster), eventToUpdate.Name),

            IsActive = eventToUpdate.IsActive
        };

        return response;
    }
}