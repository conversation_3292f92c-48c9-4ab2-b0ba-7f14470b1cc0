﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class FormTypeRepository : BaseRepository<FormType>, IFormTypeRepository
{
    private readonly ApplicationDbContext _dbContext;

    public FormTypeRepository(ApplicationDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<FormType> GetFormTypeById(string id)
    {
        return await _dbContext.FormTypes
            .Active()
            .Where(x => x.ReferenceId.Equals(id) && x.IsDelete)
            .FirstOrDefaultAsync();
    }

    public async Task<List<FormType>> GetFormTypeNames()
    {
        var matches = await _dbContext.FormTypes.Where(e => e.IsActive)
            .Select(x => new FormType { ReferenceId = x.ReferenceId, FormTypeName = x.FormTypeName })
            .ToListAsync();

        return matches;
    }

    public Task<bool> IsFormTypeNameExist(string name, string id)
    {
        return Task.FromResult(!id.IsValidGuid()
            ? Entities.Any(e => e.FormTypeName.Equals(name))
            : Entities.Where(e => e.FormTypeName.Equals(name)).ToList().Unique(id));
    }

    public Task<bool> IsFormTypeNameUnique(string name)
    {
        var matches = _dbContext.FormTypes.Any(e => e.FormTypeName.Equals(name));

        return Task.FromResult(matches);
    }

    public override async Task<PaginatedResult<FormType>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<FormType> specification, string sortColumn, string sortOrder)
    { 
        return await Entities.Specify(specification).DescOrderById().Select(x=> new FormType
        {
            Id = x.Id,
            ReferenceId = x.ReferenceId,
            FormTypeName = x.FormTypeName,
            FormTypeLogo = x.FormTypeLogo,
            IsDelete = x.IsDelete

        }).ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }
}