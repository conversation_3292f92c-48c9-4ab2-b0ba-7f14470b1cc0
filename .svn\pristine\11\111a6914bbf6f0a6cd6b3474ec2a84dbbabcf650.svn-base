﻿using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Create;
using ContinuityPatrol.Application.Features.WorkflowPermission.Commands.Update;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetDetail;
using ContinuityPatrol.Application.Features.WorkflowPermission.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.WorkflowPermissionModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IWorkflowPermissionService
{
    Task<PaginatedResult<WorkflowPermissionListVm>> GetPaginatedWorkflowPermissions(GetWorkflowPermissionPaginatedListQuery query);
    Task<BaseResponse> UpdateAsync(UpdateWorkflowPermissionCommand updateCommand);
    Task<BaseResponse> CreateAsync(CreateWorkflowPermissionCommand createCommand);
    Task<WorkflowPermissionDetailVm> GetByReferenceId(string id);
    Task<BaseResponse> DeleteAsync(string id);
    Task<List<WorkflowPermissionListVm>> GetWorkflowPermissions();
}