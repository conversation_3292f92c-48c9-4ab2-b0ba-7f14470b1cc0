﻿using ContinuityPatrol.Application.Features.TeamMaster.Events.Update;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.TeamMaster.Events
{
    public class UpdateTeamMasterEventTests
    {
        private readonly Mock<ILogger<TeamMasterUpdatedEventHandler>> _mockLogger;
        private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
        private readonly Mock<ILoggedInUserService> _mockUserService;
        private readonly TeamMasterUpdatedEventHandler _handler;

        public UpdateTeamMasterEventTests()
        {
            _mockLogger = new Mock<ILogger<TeamMasterUpdatedEventHandler>>();
            _mockUserActivityRepository = new Mock<IUserActivityRepository>();
            _mockUserService = new Mock<ILoggedInUserService>();

            _handler = new TeamMasterUpdatedEventHandler(
                _mockUserService.Object,
                _mockUserActivityRepository.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_AddsUserActivityAndLogsInformation_WhenCalled()
        {
            var updatedEvent = new TeamMasterUpdatedEvent { GroupName = "Test Group" };
            var cancellationToken = CancellationToken.None;

            _mockUserService.Setup(s => s.UserId).Returns("user-id");
            _mockUserService.Setup(s => s.LoginName).Returns("user-login");
            _mockUserService.Setup(s => s.RequestedUrl).Returns("/api/team-master");
            _mockUserService.Setup(s => s.CompanyId).Returns("company-id");
            _mockUserService.Setup(s => s.IpAddress).Returns("***********");

            await _handler.Handle(updatedEvent, cancellationToken);

            _mockUserActivityRepository.Verify(repo => repo.AddAsync(It.Is<Domain.Entities.UserActivity>(activity =>
                activity.UserId == "user-id" &&
                activity.LoginName == "user-login" &&
                activity.RequestUrl == "/api/team-master" &&
                activity.CompanyId == "company-id" &&
                activity.HostAddress == "***********" &&
                activity.Entity == Modules.TeamMaster.ToString() &&
                activity.Action == $"{ActivityType.Update} {Modules.TeamMaster}" &&
                activity.ActivityType == ActivityType.Update.ToString() &&
                activity.ActivityDetails == " Team master 'Test Group' updated successfully."
            )), Times.Once);

        }

  

       
    }
}
