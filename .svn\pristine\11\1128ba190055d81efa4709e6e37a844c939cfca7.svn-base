﻿using ContinuityPatrol.Application.Features.CyberJobManagement.Queries.GetJobStatus;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberJobManagement.Queries;
public class GetCyberJobManagementStatusQueryHandlerTests
{
    private readonly Mock<ICyberJobManagementRepository> _mockRepository;
    private readonly GetCyberJobManagementStatusQueryHanlder _handler;

    public GetCyberJobManagementStatusQueryHandlerTests()
    {
        _mockRepository = new Mock<ICyberJobManagementRepository>();
        _handler = new GetCyberJobManagementStatusQueryHanlder(_mockRepository.Object);
    }

    [Fact(DisplayName = "Handle_ShouldReturnGroupedStatusList_WhenJobsExist")]
    public async Task Handle_ShouldReturnGroupedStatusList_WhenJobsExist()
    {
        // Arrange
        var jobList = new List<Domain.Entities.CyberJobManagement>
        {
            new Domain.Entities.CyberJobManagement { ReferenceId = "1", Name = "Job1", Status = "Running" },
            new Domain.Entities.CyberJobManagement { ReferenceId = "2", Name = "Job2", Status = "running" },
            new Domain.Entities.CyberJobManagement { ReferenceId = "3", Name = "Job3", Status = "Failed" }
        };

        _mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(jobList);

        var request = new GetCyberJobManagementStatusQuery();

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

        var running = result.FirstOrDefault(x => x.Status == "running");
        var failed = result.FirstOrDefault(x => x.Status == "failed");

        Assert.NotNull(running);
        Assert.Equal(2, running.Count);

        Assert.NotNull(failed);
        Assert.Equal(1, failed.Count);
    }

    [Fact(DisplayName = "Handle_ShouldReturnEmptyList_WhenNoJobsExist")]
    public async Task Handle_ShouldReturnEmptyList_WhenNoJobsExist()
    {
        // Arrange
        _mockRepository.Setup(repo => repo.ListAllAsync())
            .ReturnsAsync(new List<Domain.Entities.CyberJobManagement>());

        var request = new GetCyberJobManagementStatusQuery();

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
}
