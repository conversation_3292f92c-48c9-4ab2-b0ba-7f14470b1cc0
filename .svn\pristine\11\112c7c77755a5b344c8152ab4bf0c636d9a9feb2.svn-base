using ContinuityPatrol.Application.Features.CyberAlert.Commands.Create;
using ContinuityPatrol.Application.Features.CyberAlert.Commands.Update;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetCyberAlertCount;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetDetail;
using ContinuityPatrol.Application.Features.CyberAlert.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.CyberAlertModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Cyber;

public class CyberAlertService :  ICyberAlertService
{
    private readonly IBaseClient _client;

    public CyberAlertService(IBaseClient client)
    {
        _client = client;

    }

    public async Task<List<CyberAlertListVm>> GetCyberAlertList()
    {
        var request = new RestRequest("api/v6/cyberalerts");

        return await  _client. GetFromCache<List<CyberAlertListVm>>(request, "GetCyberAlertList");
    }

    public async Task<BaseResponse> CreateAsync(CreateCyberAlertCommand createCyberAlertCommand)
    {
        var request = new RestRequest("api/v6/cyberalerts", Method.Post);

        request.AddJsonBody(createCyberAlertCommand);

        return await  _client. Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateCyberAlertCommand updateCyberAlertCommand)
    {
        var request = new RestRequest("api/v6/cyberalerts", Method.Put);

        request.AddJsonBody(updateCyberAlertCommand);

        return await  _client. Put<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string id)
    {
        var request = new RestRequest($"api/v6/cyberalerts/{id}", Method.Delete);

        return await  _client. Delete<BaseResponse>(request);
    }

    public async Task<CyberAlertDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/cyberalerts/{id}");

        return await  _client. Get<CyberAlertDetailVm>(request);
    }
     #region NameExist
  //  public async Task<bool> IsCyberAlertNameExist(string name, string? id)
  //  {
  //     var request = new RestRequest($"api/v6/cyberalerts/name-exist?cyberalertName={name}&id={id}");
  //
  //     return await  _client. Get<bool>(request);
  //  }
    #endregion

    #region Paginated
    public async Task<PaginatedResult<CyberAlertListVm>> GetPaginatedCyberAlerts(GetCyberAlertPaginatedListQuery query)
    {
        var request = new RestRequest("api/v6/cyberalerts/paginated-list");
  
        return await  _client. Get<PaginatedResult<CyberAlertListVm>>(request);
    }

    public async Task<CyberAlertCountVm> GetCyberAlertsCount()
    {
        var request = new RestRequest("api/v6/cyberalerts/alerts-count");

        return await  _client. Get<CyberAlertCountVm>(request);
    }

    #endregion
}
