﻿namespace ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;

public class GetSolutionHistoryByActionIdQueryHandler : IRequestHandler<GetSolutionHistoryByActionIdQuery,
    List<SolutionHistoryByActionIdQueryVm>>
{
    private readonly IMapper _mapper;
    private readonly ISolutionHistoryRepository _solutionRepository;

    public GetSolutionHistoryByActionIdQueryHandler(IMapper mapper, ISolutionHistoryRepository solutionRepository)
    {
        _mapper = mapper;
        _solutionRepository = solutionRepository;
    }

    public async Task<List<SolutionHistoryByActionIdQueryVm>> Handle(GetSolutionHistoryByActionIdQuery request,
        CancellationToken cancellationToken)
    {
        var solutionHistory = await _solutionRepository.GetSolutionHistoryByActionId(request.ActionId);

        var solutionHistoryDetailDto = _mapper.Map<List<SolutionHistoryByActionIdQueryVm>>(solutionHistory);

        return solutionHistoryDetailDto ??
               throw new NotFoundException(nameof(Domain.Entities.SolutionHistory), request.ActionId);
    }
}