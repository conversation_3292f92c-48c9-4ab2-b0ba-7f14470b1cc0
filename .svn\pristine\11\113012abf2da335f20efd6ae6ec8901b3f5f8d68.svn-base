using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class ServerFixture : IDisposable
{
    public List<Server> ServerPaginationList { get; set; }
    public List<Server> ServerList { get; set; }
    public Server ServerDto { get; set; }

    public const string CompanyId = "COMPANY_123";

    public ApplicationDbContext DbContext { get; private set; }

    public ServerFixture()
    {
        var fixture = new Fixture();

        ServerList = fixture.Create<List<Server>>();

        ServerPaginationList = fixture.CreateMany<Server>(20).ToList();

        ServerPaginationList.ForEach(x => x.CompanyId = CompanyId);

        ServerList.ForEach(x => x.CompanyId = CompanyId);

        ServerDto = fixture.Create<Server>();

        ServerDto.CompanyId = CompanyId;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public Server CreateServer(
        string name = "TestServer",
        string companyId = CompanyId,
        string serverTypeId = "SERVER_TYPE_001",
        string serverType = "Web Server",
        string roleType = "Primary",
        string licenseId = "LICENSE_001",
        string siteId = "SITE_001",
        bool isActive = true,
        string osTypeId = "OS_TYPE_001",
        string osType = "Windows",
        string status = "Online",
        string properties = null,
        string businessServiceId = "BS_001",
        string businessServiceName = "Test Business Service",
        string roleTypeId = "ROLE_TYPE_001",
        string formVersion = "1.0",
        string version = "1.0.0")
    {
        return new Server
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = name,
            CompanyId = companyId,
            ServerTypeId = serverTypeId,
            ServerType = serverType,
            RoleType = roleType,
            RoleTypeId = roleTypeId,
            LicenseId = licenseId,
            SiteId = siteId,
            IsActive = isActive,
            OSTypeId = osTypeId,
            OSType = osType,
            Status = status,
            Properties = properties ?? "{\"user\":\"admin\",\"password\":\"password123\"}",
            BusinessServiceId = businessServiceId,
            BusinessServiceName = businessServiceName,
            FormVersion = formVersion,
            Version = version,
            CreatedBy = "TEST_USER",
            CreatedDate = DateTime.Now,
            LastModifiedBy = "TEST_USER",
            LastModifiedDate = DateTime.Now
        };
    }

    public Server CreateServerWithProperties(
        string siteId = null,
        string referenceId = null,
        string name = null,
        string companyId = null)
    {
        var fixture = new Fixture();
        var server = fixture.Create<Server>();

        server.ReferenceId = referenceId ?? Guid.NewGuid().ToString();
        server.SiteId = siteId ?? server.SiteId;
        server.Name = name ?? server.Name;
        server.CompanyId = companyId ?? CompanyId;
        server.IsActive = true;

        return server;
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
