﻿//using ContinuityPatrol.Application.Features.UserGroup.Commands.Delete;
//using ContinuityPatrol.Application.Features.UserGroup.Events.Delete;
//using ContinuityPatrol.Domain.Entities;
//using ContinuityPatrol.Shared.Core.Exceptions;

//namespace ContinuityPatrol.Application.UnitTests.Features.UserGroup.Commands
//{
//    public class DeleteUserGroupTests
//    {
//        private readonly Mock<IUserGroupRepository> _mockUserGroupRepository;
//        private readonly Mock<IReportScheduleRepository> _mockReportScheduleRepository;
//        private readonly Mock<IWorkflowPermissionRepository> _mockWorkflowPermissionRepository;
//        private readonly Mock<IPublisher> _mockPublisher;
//        private readonly Mock<ILogger<DeleteUserGroupCommandResponse>> _mockLogger;
//        private readonly DeleteUserGroupCommandHandler _handler;

//        public DeleteUserGroupTests()
//        {
//            _mockUserGroupRepository = new Mock<IUserGroupRepository>();
//            _mockReportScheduleRepository = new Mock<IReportScheduleRepository>();
//            _mockWorkflowPermissionRepository = new Mock<IWorkflowPermissionRepository>();
//            _mockPublisher = new Mock<IPublisher>();
//            _mockLogger = new Mock<ILogger<DeleteUserGroupCommandResponse>>();

//            _handler = new DeleteUserGroupCommandHandler(
//                _mockUserGroupRepository.Object,
//                _mockPublisher.Object,
//                _mockReportScheduleRepository.Object,
//                _mockWorkflowPermissionRepository.Object,
//                _mockLogger.Object
//            );
//        }

//        [Fact]
//        public async Task Handle_ReturnsResponse_WhenUserGroupIsDeletedSuccessfully()
//        {
//            var command = new DeleteUserGroupCommand { Id = Guid.NewGuid().ToString() };
//            var userGroup = new Domain.Entities.UserGroup
//            {
//                ReferenceId = command.Id,
//                GroupName = "Admins",
//                IsActive = true
//            };

//            _mockUserGroupRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
//                .ReturnsAsync(userGroup);

//            _mockReportScheduleRepository
//                .Setup(repo => repo.GetReportSchedulerByUserGroupId(command.Id))
//                .ReturnsAsync(new List<ReportSchedule>());

//            _mockWorkflowPermissionRepository
//                .Setup(repo => repo.GetWorkflowPermissionByUserIdAsync(command.Id))
//                .ReturnsAsync(new List<Domain.Entities.WorkflowPermission>());

//            _mockUserGroupRepository
//                .Setup(repo => repo.UpdateAsync(userGroup))
//                .ReturnsAsync(userGroup);

//            _mockPublisher
//                .Setup(pub => pub.Publish(It.IsAny<UserGroupDeleteEvent>(), It.IsAny<CancellationToken>()))
//                .Returns(Task.CompletedTask);

//            var result = await _handler.Handle(command, CancellationToken.None);

//            Assert.NotNull(result);
//            Assert.False(userGroup.IsActive);
//            Assert.Equal(" User Group 'Admins' has been deleted successfully", result.Message);

//            _mockUserGroupRepository.Verify(repo => repo.UpdateAsync(userGroup), Times.Once);
//            _mockPublisher.Verify(pub => pub.Publish(It.IsAny<UserGroupDeleteEvent>(), It.IsAny<CancellationToken>()), Times.Once);
//        }

//        [Fact]
//        public async Task Handle_ThrowsInvalidException_WhenUserGroupIsInUse()
//        {
//            var command = new DeleteUserGroupCommand { Id = Guid.NewGuid().ToString() };
//            var userGroup = new Domain.Entities.UserGroup
//            {
//                ReferenceId = command.Id,
//                GroupName = "Editors",
//                IsActive = true
//            };

//            _mockUserGroupRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
//                .ReturnsAsync(userGroup);

//            _mockReportScheduleRepository
//                .Setup(repo => repo.GetReportSchedulerByUserGroupId(command.Id))
//                .ReturnsAsync(new List<ReportSchedule> { new ReportSchedule() });

//            _mockWorkflowPermissionRepository
//                .Setup(repo => repo.GetWorkflowPermissionByUserIdAsync(command.Id))
//                .ReturnsAsync(new List<Domain.Entities.WorkflowPermission>());

//            var exception = await Assert.ThrowsAsync<InvalidException>(() =>
//                _handler.Handle(command, CancellationToken.None));

//            Assert.Equal("The User 'Editors' is currently in use", exception.Message);

//            _mockLogger.Verify(logger =>
//                logger.LogInformation(It.Is<string>(msg =>
//                    msg.Contains("The user 'Editors' is currently being used in Scheduler report.")),
//                Times.Once));
//        }

//        [Fact]
//        public async Task Handle_ThrowsNotFoundException_WhenUserGroupDoesNotExist()
//        {
//            var command = new DeleteUserGroupCommand { Id = Guid.NewGuid().ToString() };

//            _mockUserGroupRepository
//                .Setup(repo => repo.GetByReferenceIdAsync(command.Id))
//                .ReturnsAsync((Domain.Entities.UserGroup)null);

//            var exception = await Assert.ThrowsAsync<NotFoundException>(() =>
//                _handler.Handle(command, CancellationToken.None));

//            Assert.Equal($"User Group with ID {command.Id} not found.", exception.Message);
//        }
//    }
//}
