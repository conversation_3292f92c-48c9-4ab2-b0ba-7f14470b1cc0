using ContinuityPatrol.Application.Features.IncidentManagement.Events.Create;

namespace ContinuityPatrol.Application.Features.IncidentManagement.Commands.Create;

public class
    CreateIncidentManagementCommandHandler : IRequestHandler<CreateIncidentManagementCommand,
        CreateIncidentManagementResponse>
{
    private readonly IIncidentManagementRepository _incidentManagementRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateIncidentManagementCommandHandler(IMapper mapper,
        IIncidentManagementRepository incidentManagementRepository, IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _incidentManagementRepository = incidentManagementRepository;
    }

    public async Task<CreateIncidentManagementResponse> Handle(CreateIncidentManagementCommand request,
        CancellationToken cancellationToken)
    {
        var incidentManagement = _mapper.Map<Domain.Entities.IncidentManagement>(request);

        incidentManagement = await _incidentManagementRepository.AddAsync(incidentManagement);

        var response = new CreateIncidentManagementResponse
        {
            Message = Message.Create(nameof(Domain.Entities.IncidentManagement), incidentManagement.IncidentName),

            Id = incidentManagement.ReferenceId
        };

        await _publisher.Publish(new IncidentManagementCreatedEvent { Name = incidentManagement.IncidentName },
            cancellationToken);

        return response;
    }
}