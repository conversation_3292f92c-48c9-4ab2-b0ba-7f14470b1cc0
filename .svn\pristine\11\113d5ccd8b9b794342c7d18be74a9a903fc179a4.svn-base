﻿using ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetNames;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using FluentAssertions;

namespace ContinuityPatrol.Application.UnitTests.Features.EscalationMatrix.Queries.GetName;

public class GetEscalationMatrixNameQueryHandlerLTests
{
    private readonly Mock<IEscalationMatrixRepository> _repositoryMock;
    private readonly GetEscalationMatrixNameQueryHandlerL _handler;

    public GetEscalationMatrixNameQueryHandlerLTests()
    {
        _repositoryMock = new Mock<IEscalationMatrixRepository>();
        _repositoryMock = EscalationMatrixRepositoryMocks.GetEscalationMatrixNameRepository();
        _handler = new GetEscalationMatrixNameQueryHandlerL(_repositoryMock.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnListOfNames_WhenCalled()
    {
        // Arrange
        var expectedNames = new List<EscalationMatrixNameVm>
        {
            new EscalationMatrixNameVm { Id = "1", Name = "ESC-001" },
            new EscalationMatrixNameVm { Id = "2", Name = "ESC-002" }
        };

        _repositoryMock.Setup(r => r.GetNames()).ReturnsAsync(expectedNames);

        var query = new GetEscalationMatrixNameQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result[0].Name.Should().Be("ESC-001");
        _repositoryMock.Verify(r => r.GetNames(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenNoNamesExist()
    {
        // Arrange
        _repositoryMock.Setup(r => r.GetNames()).ReturnsAsync(new List<EscalationMatrixNameVm>());

        var query = new GetEscalationMatrixNameQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
        _repositoryMock.Verify(r => r.GetNames(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenRepositoryThrowsException()
    {
        // Arrange
        _repositoryMock.Setup(r => r.GetNames()).ThrowsAsync(new Exception("Database error"));

        var query = new GetEscalationMatrixNameQuery();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));

        _repositoryMock.Verify(r => r.GetNames(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnNull_WhenRepositoryReturnsNull()
    {
        // Arrange
        _repositoryMock.Setup(r => r.GetNames()).ReturnsAsync((List<EscalationMatrixNameVm>)null!);

        var query = new GetEscalationMatrixNameQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().BeNull();
        _repositoryMock.Verify(r => r.GetNames(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnLargeList_WhenManyNamesExist()
    {
        // Arrange
        var largeList = new List<EscalationMatrixNameVm>();
        for (int i = 1; i <= 1000; i++)
        {
            largeList.Add(new EscalationMatrixNameVm { Id = i.ToString(), Name = $"ESC-{i:D3}" });
        }

        _repositoryMock.Setup(r => r.GetNames()).ReturnsAsync(largeList);

        var query = new GetEscalationMatrixNameQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        result.Should().HaveCount(1000);
        result[999].Name.Should().Be("ESC-1000");
        _repositoryMock.Verify(r => r.GetNames(), Times.Once);
    }
}
