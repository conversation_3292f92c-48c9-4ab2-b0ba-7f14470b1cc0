﻿using ContinuityPatrol.Application.Features.ServerType.Queries.GetByName;
using ContinuityPatrol.Domain.ViewModels.ServerTypeModel;

namespace ContinuityPatrol.Application.UnitTests.Features.ServerType.Queries;

public class GetServerTypeListByNameQueryHandlerTests
{
    private readonly Mock<IServerTypeRepository> _mockServerTypeRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly GetServerTypeListByNameQueryHandler _handler;

    public GetServerTypeListByNameQueryHandlerTests()
    {
        _mockServerTypeRepository = new Mock<IServerTypeRepository>();
        _mockMapper = new Mock<IMapper>();
        _handler = new GetServerTypeListByNameQueryHandler(_mockServerTypeRepository.Object, _mockMapper.Object);
    }

    [Fact]
    public async Task Handle_ShouldReturnMappedServerTypeList_WhenRepositoryReturnsData()
    {
        var request = new GetServerTypeListByNameQuery { Name = "TestServerType" };

        var serverTypes = new List<Domain.Entities.ServerType>
        {
            new Domain.Entities.ServerType { Id = 1, Name = "TestServerType1" },
            new Domain.Entities.ServerType { Id = 2, Name = "TestServerType2" }
        };

        var serverTypeModels = new List<ServerTypeModel>
        {
            new ServerTypeModel { Id = Guid.NewGuid().ToString(), Name = "TestServerType1" },
            new ServerTypeModel { Id = Guid.NewGuid().ToString(), Name = "TestServerType2" }
        };

        _mockServerTypeRepository
            .Setup(x => x.GetServerTypeListByName(request.Name))
            .ReturnsAsync(serverTypes);

        _mockMapper
            .Setup(x => x.Map<List<ServerTypeModel>>(serverTypes))
            .Returns(serverTypeModels);

        var result = await _handler.Handle(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(serverTypeModels.Count, result.Count);
        Assert.Equal(serverTypeModels[0].Name, result[0].Name);

        _mockServerTypeRepository.Verify(x => x.GetServerTypeListByName(request.Name), Times.Once);
        _mockMapper.Verify(x => x.Map<List<ServerTypeModel>>(serverTypes), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldReturnEmptyList_WhenRepositoryReturnsEmpty()
    {
        var request = new GetServerTypeListByNameQuery { Name = "NonExistentServerType" };

        _mockServerTypeRepository
            .Setup(x => x.GetServerTypeListByName(request.Name))
            .ReturnsAsync(new List<Domain.Entities.ServerType>());

        _mockMapper
            .Setup(x => x.Map<List<ServerTypeModel>>(It.IsAny<List<Domain.Entities.ServerType>>()))
            .Returns(new List<ServerTypeModel>());

        var result = await _handler.Handle(request, CancellationToken.None);

        Assert.NotNull(result);
        Assert.Empty(result);

        _mockServerTypeRepository.Verify(x => x.GetServerTypeListByName(request.Name), Times.Once);
        _mockMapper.Verify(x => x.Map<List<ServerTypeModel>>(It.IsAny<List<Domain.Entities.ServerType>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
    {
        var request = new GetServerTypeListByNameQuery { Name = "TestServerType" };

        _mockServerTypeRepository
            .Setup(x => x.GetServerTypeListByName(request.Name))
            .ThrowsAsync(new Exception("Database error"));

        var exception = await Assert.ThrowsAsync<Exception>(() =>
            _handler.Handle(request, CancellationToken.None));

        Assert.Equal("Database error", exception.Message);

        _mockServerTypeRepository.Verify(x => x.GetServerTypeListByName(request.Name), Times.Once);
        _mockMapper.Verify(x => x.Map<List<ServerTypeModel>>(It.IsAny<List<Domain.Entities.ServerType>>()), Times.Never);
    }
    [Fact]
    public void Properties_ShouldSetAndGetValues()
    {
        // Arrange
        var vm = new GetServerTypeListByNameVm
        {
            Id = "server-001",
            Name = "MyServer",
            Logo = "logo.png"
        };

        // Act & Assert
        Assert.Equal("server-001", vm.Id);
        Assert.Equal("MyServer", vm.Name);
        Assert.Equal("logo.png", vm.Logo);
    }
}