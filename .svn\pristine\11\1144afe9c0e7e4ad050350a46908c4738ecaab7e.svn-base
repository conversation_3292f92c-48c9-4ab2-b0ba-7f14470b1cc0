using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.SolutionMapping.Events.Delete;

public class SolutionMappingDeletedEventHandler : INotificationHandler<SolutionMappingDeletedEvent>
{
    private readonly ILogger<SolutionMappingDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public SolutionMappingDeletedEventHandler(ILoggedInUserService userService, ILogger<SolutionMappingDeletedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(SolutionMappingDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} SolutionMapping",
            Entity = "SolutionMapping",
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"SolutionMapping '{deletedEvent.Name}' deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"SolutionMapping '{deletedEvent.Name}' deleted successfully.");
    }
}
