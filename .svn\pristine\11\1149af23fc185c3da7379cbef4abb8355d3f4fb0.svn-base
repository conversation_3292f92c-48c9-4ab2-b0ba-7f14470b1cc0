﻿async function handleFormUpdate(formData) {
    //compareJSONSecondary = formData?.detail;
    setTimeout(() => {
        let buttonGrp = $('.field-preview .f-btn-group button');
        if (buttonGrp?.length > 0) {
            $('.field-preview button').on('click', function (event) {
                event.preventDefault();
            });
        }

        const labelElement = document.querySelectorAll('.row-edit');
        if (labelElement) {
            labelElement.forEach(function (label) {
                const inputGroupAddon = label.querySelector('.input-group-addon');
                const fieldGroup = label.querySelectorAll('.f-field-group ');
                if (fieldGroup) {
                    fieldGroup.forEach(function (fileds) {
                        const labelsInside = fileds.querySelector('label');
                        if (labelsInside.innerText.trim() === 'Wrap row in a <fieldset> tag') {
                            fileds.insertBefore(inputGroupAddon, fileds.firstChild);
                        }
                    })
                }
                const divElement = label.querySelector('.input-group');
                if (divElement) {
                    divElement.remove();
                }
            })
        };
    }, 200);

    //if (compareJSONPrimary && compareJSONSecondary) {
    //    let res = await compareJSON(compareJSONPrimary, compareJSONSecondary);
    //If Disabled update button it's not enable when delete the condition. because json not update when delete condition.
    //if (!res) {
    //    $('.save-button').prop('disabled', false);
    //} else {
    //    $('.save-button').prop('disabled', false);
    //}
    //}

    let rows = formData.detail.rows
    let columns = formData.detail.columns
    let listofrows = Object.keys(rows)
    let columnWidths = [25, 50, 75, 100];
    let columnElement = $('.formeo-stage .formeo-row');
    columnElement?.each((index, element) => {
        let columnPreset = $(element).find('.column-preset');
        Object.keys(columns).forEach((s) => {
            let specificColumnPreset = $(element).find('#' + s);
            let percentValue = columns[s]?.config?.width.split('%')

            if (specificColumnPreset.length > 0) {
                if (columnPreset.children().length === 0) {
                    let tr; // = `<option selected>Select Column Width</option>`;
                    columnWidths.forEach((column) => {
                        tr += `<option value=${column}>${column}</option>`;
                    });
                    columnPreset.append(tr);
                    percentValue.length && columnPreset.val(percentValue[0]);
                }
            }
        });
    });

    let inputElements = document.querySelectorAll('.DatabaseType');
    inputElements?.forEach(async function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            let labelElement = document.createElement('label');
            labelElement.textContent = 'Database Type';
            let selectElement = document.createElement('select');
            selectElement.setAttribute('name', inputElement.getAttribute('name'));
            selectElement.setAttribute('id', inputElement.getAttribute('id'));
            selectElement.setAttribute('class', 'DatabaseType');
            let databaseList = await getRequest(RootUrl + "Configuration/Database/GetPagination");

            if (databaseList?.data?.length) {
                const optionElement = document.createElement('option');
                optionElement.value = '';
                optionElement.textContent = 'Select Database Type';
                selectElement.appendChild(optionElement);
                const uniqueValues = new Set();
                databaseList?.data?.forEach(function (dbtype) {
                    if (!uniqueValues.has(dbtype?.databaseType?.toLowerCase())) {
                        let optionElement = document.createElement('option');
                        optionElement.setAttribute('value', dbtype?.databaseTypeId);
                        optionElement.textContent = dbtype?.databaseType;
                        selectElement.appendChild(optionElement);
                        uniqueValues.add(dbtype?.databaseType?.toLowerCase());
                    }
                });
            }

            selectElement.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let databaseType = $('.DatabaseType option:selected').text();
                let fieldId = selectElement.id;
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.DatabaseType = databaseType;
                formData.detail.fields[subString].attrs.DatabaseTypeID = selectedValue;
            });
            let parentElement = inputElement.parentNode;
            parentElement?.insertBefore(labelElement, inputElement);
            parentElement?.insertBefore(selectElement, inputElement);
            parentElement?.removeChild(inputElement);
        }
    });

    //ENCRYPTION
    let inputEnc = document.querySelectorAll('.password-input');
    inputEnc?.forEach(function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            let labelElement = document.createElement('label');
            labelElement.textContent = 'Encrypt';
            let parentElement = inputElement.parentNode;
            parentElement.insertBefore(labelElement, inputElement);
        }
    });

    //let restrictspecialcharacters = document.querySelectorAll('.restrict');
    //restrictspecialcharacters.forEach(function (inputElement) {
    //    if (inputElement.tagName === 'INPUT') {
    //        inputElement.classList.add('ms-1');
    //        let label = inputElement.parentNode.querySelector('label');
    //        if (!label) {
    //            let labelElement = document.createElement('label');
    //            labelElement.textContent = 'Restrict special characters';
    //            let parentElement = inputElement.parentNode;
    //            parentElement.insertBefore(labelElement, inputElement);
    //            inputElement.addEventListener('change', function (event) {
    //                event.preventDefault()
    //                if (event.target.checked) {
    //                    inputElement.setAttribute("restrict", true);
    //                    //let encryptionElements = document.querySelectorAll('.encryption'); //Affected more then one input.
    //                    //encryptionElements.forEach(function (restrictElement) {
    //                    //    let parentDiv = restrictElement.parentNode;
    //                    //    if (parentDiv.tagName === 'DIV') {
    //                    //        parentDiv.style.pointerEvents = 'none';
    //                    //        parentDiv.style.opacity = '0.5';
    //                    //    }
    //                    //});
    //                }
    //                else {
    //                    inputElement.setAttribute("restrict", false);
    //                    //let encryptionElements = document.querySelectorAll('.encryption');
    //                    //encryptionElements.forEach(function (restrictElement) {
    //                    //    let parentDiv = restrictElement.parentNode;
    //                    //    if (parentDiv.tagName === 'DIV') {
    //                    //        parentDiv.style.pointerEvents = '';
    //                    //        parentDiv.style.opacity = '';
    //                    //    }
    //                    //});
    //                }
    //            });
    //        }
    //    }
    //});

    //disabled
    let disabled = document.querySelectorAll('.disabled');
    disabled?.forEach(function (inputElement) {
        let label = inputElement.parentNode.querySelector('label');
        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                let labelElement = document.createElement('label');
                labelElement.textContent = 'Disabled';
                let parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    //Hide       
    let hideTable = document.querySelectorAll('.hideLunsTable');
    hideTable?.forEach(function (inputElement) {
        let label = inputElement.parentNode.querySelector('label');
        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                let labelElement = document.createElement('label');
                labelElement.textContent = 'Hide';
                let parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    //MINLENGTH
    let inputMin = document.querySelectorAll('.minlength');
    inputMin?.forEach(function (inputElement) {
        inputElement.type = 'number';
        inputElement.addEventListener('keypress', function (event) {
            if (event.key === 'e' || event.key === 'E' || event.key === '-' || $(this).val().length > 0) {
                event.preventDefault();
            }
        });
        let label = inputElement.parentNode.querySelector('label');
        if (!label) {
            if (inputElement.tagName === 'INPUT') {
                inputElement.placeholder = "Min Length"
                let labelElement = document.createElement('label');
                labelElement.textContent = 'Min Length';
                let parentElement = inputElement.parentNode;
                parentElement.insertBefore(labelElement, inputElement);
            }
        }
    });

    //MAXLENGTH
    let inputMax = document.querySelectorAll('.maxlength');
    inputMax?.forEach(function (inputElement) {
        inputElement.type = 'number';
        inputElement.addEventListener('keypress', function (event) {
            let val = $(this).val();
            if (event.key === 'e' || event.key === 'E' || event.key === '-' || val.length > 3) {
                event.preventDefault();
            }
        });
    });

    //Name
    const inputElement = document.querySelectorAll('.name');
    inputElement?.forEach(function (inputEle) {
        const closestLabel = inputEle.closest('.f-field-group').querySelector('label');
        if (closestLabel) {
            closestLabel.innerHTML = 'Name <span style="color: red;">*</span>';
        } else {
            console.log('No label found for the input.');
        }
    });

    //Input For
    let inputType = document.querySelectorAll('.inputType');
    inputType?.forEach(function (roleElement) {
        if (roleElement.tagName === 'INPUT') {
            let roleLabelElement = document.createElement('label');
            roleLabelElement.innerHTML = 'Input Type <span style="color: red;">*</span>';
            let roleSelectElement = document.createElement('select');
            roleSelectElement.setAttribute('name', roleElement.getAttribute('name'));
            roleSelectElement.setAttribute('id', roleElement.getAttribute('id'));
            roleSelectElement.setAttribute('class', 'inputType');
            let roleOptionElement = document.createElement('option');
            roleOptionElement.setAttribute('value', 'text');
            roleOptionElement.textContent = 'Text';
            roleSelectElement.appendChild(roleOptionElement);
            let roleOptionElement2 = document.createElement('option');
            roleOptionElement2.setAttribute('value', 'path');
            roleOptionElement2.textContent = 'Path';
            roleSelectElement.appendChild(roleOptionElement2);
            roleSelectElement.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let fieldId = roleSelectElement.id;
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.inputType = selectedValue;
            });

            let roleParentElement = roleElement.parentNode;
            roleParentElement.innerHTML = '';
            roleParentElement.appendChild(roleLabelElement);
            roleParentElement.appendChild(roleSelectElement);
        }
    });

    //let formDynamicIcon = document.querySelectorAll('.formDynamicIcon');
    //formDynamicIcon?.forEach(function (dynamicIcon) {
    //    if (dynamicIcon.tagName === 'INPUT') {
    //        let labelElement = document.createElement('label');
    //        labelElement.textContent = 'Select Icon';
    //        let span = document.createElement('span');
    //        //when change class="cp-solaris" line 347 in formbuilder
    //        span.innerHTML = `<i role="button" icon="${dynamicIcon.getAttribute('id')}" title="Select Icon" id="dynamicIcons" class="cp-solaris"></i>`;
    //        let parentElement = dynamicIcon.parentNode;
    //        parentElement.insertBefore(labelElement, dynamicIcon);
    //        parentElement.insertBefore(span, dynamicIcon);
    //        parentElement.removeChild(dynamicIcon);
    //    }
    //});

    //$(document).on('click', '#formDynamicicon td', function () {
    //    let className = this.firstElementChild.getAttribute('class');
    //    let words = className?.split(' ');
    //    let firstWord = words[0];
    //    $('#dynamicIcons').removeClass().addClass(firstWord);
    //    let iconGroups = document.querySelector('#dynamicIcons');
    //    let fieldId = iconGroups.getAttribute('icon');
    //    let subString = fieldId.substring(0, 36);
    //    if (formData?.detail?.fields[subString]?.attrs) {
    //        console.log(formData?.detail?.fields[subString]?.attrs);
    //        formData.detail.fields[subString].attrs.formDynamicIcon = firstWord;
    //    }
    //});

    //forserver role and type
    let roleElements = document.querySelectorAll('.ServerRole');
    roleElements?.forEach(async function (roleElement) {
        if (roleElement.tagName === 'INPUT') {
            let roleLabelElement = document.createElement('label');
            roleLabelElement.textContent = 'Server Role';
            let roleSelectElement = document.createElement('select');
            roleSelectElement.setAttribute('name', roleElement.getAttribute('name'));
            roleSelectElement.setAttribute('id', roleElement.getAttribute('id'));
            roleSelectElement.setAttribute('class', 'ServerRole');

            // Populate Server Role options
            let serverRoleData = await getRequest(RootUrl + 'Configuration/Server/GetServerRole');
            if (serverRoleData?.length) {
                let roleOptionElement = document.createElement('option');
                roleOptionElement.setAttribute('value', '');
                roleOptionElement.textContent = 'Select Server Role';
                roleSelectElement.appendChild(roleOptionElement);
                serverRoleData.forEach(function (optionValue) {
                    let roleOptionElement = document.createElement('option');
                    roleOptionElement.setAttribute('value', optionValue.name);
                    roleOptionElement.setAttribute('data-ServerRoleID', optionValue.id);
                    roleOptionElement.textContent = optionValue.name;
                    roleSelectElement.appendChild(roleOptionElement);
                });
            }

            // Append elements to the parent container for Server Role dropdown
            let roleParentElement = roleElement.parentNode;
            if (roleParentElement) {
                roleParentElement.innerHTML = '';
                roleParentElement.appendChild(roleLabelElement);
                roleParentElement.appendChild(roleSelectElement);
            }
        }

        let ServerRoleElements = document.querySelectorAll('select.ServerRole');
        ServerRoleElements.forEach(function (elementID) {
            document.getElementById(elementID.getAttribute('id'))?.addEventListener("change", function (event) {
                let selectedRole = event.target.value;
                let selectedRoleId = $(`#${elementID.getAttribute('id')} option:selected`).attr('data-ServerRoleID');
                let fieldId = elementID.getAttribute('id');//roleSelectElement.id;
                let subString = fieldId.substring(0, 36)
                if (selectedRole)
                    role = "server"
                formData.detail.fields[subString].attrs.ServerRole = selectedRole;
                formData.detail.fields[subString].attrs.ServerRoleID = selectedRoleId;
                populateSecondDropdown(selectedRoleId, event.target);
            });
        });
    });

    let serverType = document.querySelectorAll('.ServerType');
    serverType?.forEach(function (inputElement) {
        if (inputElement.tagName === 'INPUT') {
            let labelElement = document.createElement('label');
            labelElement.textContent = 'Server Type';
            let selectElement = document.createElement('select');
            selectElement.setAttribute('name', inputElement.getAttribute('name'));
            selectElement.setAttribute('id', inputElement.getAttribute('id'));
            selectElement.setAttribute('class', 'ServerType');
            let options = ['Select Server Type'];
            options.forEach(function (optionValue) {
                let optionElement = document.createElement('option');
                optionElement.setAttribute('value', optionValue);
                optionElement.textContent = optionValue;
                selectElement.appendChild(optionElement);
            });
            selectElement.addEventListener('change', function (event) {
                let selectedValue = event.target.value;
                let fieldId = selectElement.id;
                let SelectedTypeID = $(`#${inputElement.getAttribute('id')} option:selected`).attr('idServerType');
                let subString = fieldId.substring(0, 36)
                formData.detail.fields[subString].attrs.ServerType = selectedValue;
                formData.detail.fields[subString].attrs.ServerTypeID = SelectedTypeID;
            });
            let parentElement = inputElement.parentNode;
            parentElement.insertBefore(labelElement, inputElement);
            parentElement.insertBefore(selectElement, inputElement);
            parentElement.removeChild(inputElement);
        }
    });

    //workflow
    let workflowEl = document.querySelectorAll(".dependentAction");
    workflowEl?.forEach(function (input) {
        if (input.tagName === 'INPUT') {
            let label = input.parentNode.querySelector('label');
            if (!label) {
                let labelElement = document.createElement('label');
                labelElement.textContent = 'Include dependent workflow actions';
                let parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {
                    event.preventDefault()
                    if (event.target.checked) input.setAttribute("data-actions", true)
                    else input.setAttribute("data-actions", false)
                });
            }
        }
    });

    // For each table with the class .custom-table
    setTimeout(() => {
        let customTable = $('.custom-table');
        customTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formCustomTable(element);
            }
        });

        let deploymentsTable = $('.deployments-table');
        deploymentsTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formDeploymentsTable(element);
            }
        });

        // Find the target table
        let lunsCustomTable = $('.luns-custom-table');
        lunsCustomTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formLunsCustomTable(element);
            }
        });

        // Find the target table
        let vmPathCustomTable = $('.vmPath-custom-table');
        vmPathCustomTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formVMPathCustomTable(element);
            }
        });

        // Find the target table
        let replicationCustomTable = $('.replication-custom-table');
        replicationCustomTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formReplicationCustomTable(element);
            }
        });

        if (dynamicUpdateCondition) {
            let dynamicCustomTable = $('.dynamic-custom-table');
            dynamicCustomTable?.each(function (index, element) {
                if ($(element) && $(element).children().length === 0) {
                    formDynamicCustomTable(element);
                }
            });
        } else {
            let tableDetails = document.querySelector('.dynamic-custom-table');
            tableDetails?.append(headerRowData);
            tableDetails?.append(dataRowData);
        }

        let replicationZFSTable = $('.replicationZFSTable',);
        replicationZFSTable?.each(function (index, element) {
            if ($(element) && $(element).children().length === 0) {
                formReplicationZFSTable(element);
            }
        });

    }, 500);

    setTimeout(() => {
        addPlaceholderListener("lunsTableHeader1", "lunsTableDescription1");
        addPlaceholderListener("lunsTableHeader2", "lunsTableDescription2");
        addPlaceholderListener("lunsTableHeader3", "lunsTableDescription3");
        addPlaceholderListener("lunsTableHeader4", "lunsTableDescription4");
        addPlaceholderListener("lunsTableHeader5", "lunsTableDescription5");
        addPlaceholderListener("lunsTableHeader6", "lunsTableDescription6");
        addPlaceholderListener("sDirectory", "sDirectoryPlaceholder");
        addPlaceholderListener("dDirectory", "dDirectoryPlaceholder");
        addPlaceholderListener("dynamicHeader1", "dynamicData1");
        addPlaceholderListener("dynamicHeader2", "dynamicData2");
    }, 700);

    //select 
    let selectFields = document.querySelectorAll(".multiple")
    selectFields?.forEach(function (input) {
        if (input.tagName === 'INPUT') {
            let label = input.parentNode.querySelector('label');
            if (!label) {
                let labelElement = document.createElement('label');
                //labelElement.textContent = 'isMultiple';
                labelElement.textContent = 'Multi-select';
                let parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {
                    event.preventDefault()
                    if (event.target.checked) input.setAttribute("multiple", true)
                    else input.setAttribute("multiple", false)
                });
            }
        }
    })

    ////for encryption
    let encryption = document.querySelectorAll(".encryption");
    encryption?.forEach(function (input) {
        if (input.tagName === 'INPUT') {
            let label = input.parentNode.querySelector('label');
            if (!label) {
                let labelElement = document.createElement('label');
                labelElement.textContent = 'Encrypted';
                let parentElement = input.parentNode;
                parentElement.insertBefore(labelElement, input);
                input.addEventListener('change', function (event) {
                    event.preventDefault()
                    if (event.target.checked) {
                        input.setAttribute("data-encryption", true);
                        //let restrictElements = document.querySelectorAll('.restrict'); //Affected more then one input.
                        //restrictElements.forEach(function (restrictElement) {
                        //    let parentDiv = restrictElement.parentNode;
                        //    if (parentDiv.tagName === 'DIV') {
                        //        parentDiv.style.pointerEvents = 'none';
                        //        parentDiv.style.opacity = '0.5';
                        //    }
                        //});
                    }
                    else {
                        input.setAttribute("data-encryption", false)
                        //let restrictElements = document.querySelectorAll('.restrict');
                        //restrictElements.forEach(function (restrictElement) {
                        //    let parentDiv = restrictElement.parentNode;
                        //    if (parentDiv.tagName === 'DIV') {
                        //        parentDiv.style.pointerEvents = '';
                        //        parentDiv.style.opacity = '';
                        //    }
                        //});
                    }
                });
            }
        }
    });

    const hideAttributeButton = document.querySelectorAll('.add-attrs')
    hideAttributeButton?.forEach(function (target) {
        target.style.display = 'none'
    });

    ////forConditionas
    let conditionTargets = document.querySelectorAll('.condition-target');

    // Loop through each condition-target element
    conditionTargets?.forEach(function (target) {
        let inputs = target.querySelectorAll('input.f-autocomplete-display-field');

        // Loop through input elements and disable autocomplete for the specific placeholder
        inputs.forEach(function (input) {
            if (input.placeholder === 'target / value') {
                const nextElement = input.nextElementSibling;
                if (nextElement) {
                    nextElement?.nextElementSibling?.remove();
                } else {
                    console.log('No element found after the input.');
                }
            }
        });
    });

    let rowColButtons = document.querySelectorAll('.action-btn-wrap button');
    let EditColRemoveBtn = document.querySelectorAll('.prop-remove')
    rowColButtons?.forEach(function (button, index) {
        if (button.classList.contains('item-handle')) {
            button.setAttribute('title', 'Move');
        } else if (button.classList.contains('item-edit-toggle')) {
            button.setAttribute('title', 'Edit');
        } else if (button.classList.contains('item-clone')) {
            button.setAttribute('title', 'Clone');
        } else if (button.classList.contains('item-remove')) {
            button.setAttribute('title', 'Remove');
        }
    });

    EditColRemoveBtn?.forEach(function (button, index) {
        button.setAttribute('title', 'Remove');
    });

    ///// for removing conditions
    const inputFields = document.querySelectorAll('.children .field-edit');
    inputFields?.forEach(field => {
        const parentId = field.closest('.formeo-field').getAttribute('id');
        const formFields = formData.detail?.fields
        if (formFields[parentId] && (formFields[parentId]?.meta?.group.toLowerCase() == 'custom'
            || formFields[parentId]?.meta?.group.toLowerCase() == 'html'
            || formFields[parentId]?.meta?.id.toLowerCase() == 'ip-address'
            || formFields[parentId]?.meta?.id.toLowerCase() == 'dynamicbutton')) {
            if (formFields[parentId]?.tag && formFields[parentId].tag == 'hr') {
                $('#' + parentId).find('.panel-labels h5:contains("Conditions")').remove();
                $('#' + parentId).find('.conditions-panel').remove();
            } else {
                $('#' + parentId).find('.panel-labels h5:contains("Conditions")').remove();
            }
        }
    });
    let labelElements = document.querySelectorAll('.formeo-stage .formeo-field .prev-label label');
    let otherElements = document.querySelectorAll('.formeo-stage .formeo-field .f-checkbox label');

    labelElements?.forEach(function (labelElement) {
        formIsValid = [];
        validateFormLabelInput(labelElement);
    });

    otherElements?.forEach(function (labelElement) {
        const parentElement = labelElement.closest('.formeo-field');
        formIsValid = [];
        if (parentElement) {
            const hasPrevLabelClass = Array.from(parentElement.children).some(child => child.classList.contains('prev-label'));
            !hasPrevLabelClass && validateFormLabelInput(labelElement);
        } else {
            validateFormLabelInput(labelElement);
        }
    });
}
