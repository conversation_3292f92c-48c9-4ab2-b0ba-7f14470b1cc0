﻿using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Events.Create;
using ContinuityPatrol.Application.Features.Replication.Events.Delete;
using ContinuityPatrol.Application.Features.Replication.Events.PaginatedView;
using ContinuityPatrol.Application.Features.Replication.Events.Update;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Fixtures;

public class ReplicationFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ReplicationView> ReplicationViews { get; set; }
    public List<Replication> Replications { get; set; }
    public List<LicenseManager> LicenseManagers { get; set; }

    public CreateReplicationCommand CreateReplicationCommand { get; set; }
    public UpdateReplicationCommand UpdateReplicationCommand { get; set; }
    public ReplicationCreatedEvent ReplicationCreatedEvent { get; set; }
    public ReplicationDeletedEvent ReplicationDeletedEvent { get; set; }
    public ReplicationUpdatedEvent ReplicationUpdatedEvent { get; set; }
    public ReplicationPaginatedEvent ReplicationPaginatedEvent { get; set; }

    public ReplicationFixture()
    {
        ReplicationViews = AutoReplicationFixture.Create<List<ReplicationView>>();
        Replications = AutoReplicationFixture.Create<List<Replication>>();

        LicenseManagers = AutoReplicationFixture.Create<List<LicenseManager>>();

        CreateReplicationCommand = AutoReplicationFixture.Create<CreateReplicationCommand>();

        UpdateReplicationCommand = AutoReplicationFixture.Create<UpdateReplicationCommand>();

        ReplicationCreatedEvent = AutoReplicationFixture.Create<ReplicationCreatedEvent>();

        ReplicationDeletedEvent = AutoReplicationFixture.Create<ReplicationDeletedEvent>();

        ReplicationUpdatedEvent = AutoReplicationFixture.Create<ReplicationUpdatedEvent>();

        ReplicationPaginatedEvent = AutoReplicationFixture.Create<ReplicationPaginatedEvent>();

        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ReplicationProfile>();
        });

        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoReplicationFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateReplicationCommand>(p => p.Name, 10));
            fixture.Customize<CreateReplicationCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<UpdateReplicationCommand>(p => p.Name, 10));
            fixture.Customize<UpdateReplicationCommand>(c => c.With(b => b.Properties, "{\"Name\": \"admin\", \"password\": \"Admin@123\"}"));

            fixture.Customize<Replication>(c => c.With(b => b.IsActive, true));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ReplicationCreatedEvent>(p => p.ReplicationName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ReplicationDeletedEvent>(p => p.ReplicationName, 10));

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ReplicationUpdatedEvent>(p => p.ReplicationName, 10));

            //fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<ReplicationPaginatedEvent>(p => p.ReplicationName, 10));

            return fixture;

        }
    }

    public void Dispose()
    {

    }
}