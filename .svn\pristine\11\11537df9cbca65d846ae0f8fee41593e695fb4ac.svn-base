﻿using ContinuityPatrol.Application.Features.Report.Event.View;
using ContinuityPatrol.Application.Helper;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;

public class
    GetWorkflowOperationDrDrillReportQueryHandler : IRequestHandler<GetWorkflowOperationDrDrillReportQuery,
        DrDrillReport>
{
    private readonly IBusinessFunctionRepository _businessFunctionRepository;
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IDatabaseRepository _databaseRepository;
    private readonly IInfraObjectRepository _infraObjectRepository;
    private readonly ILoggedInUserService _loggedInUserService;
    private readonly IMapper _mapper;
    private readonly ILoadBalancerRepository _nodeConfigurationRepository;
    private readonly IPublisher _publisher;
    private readonly IServerRepository _serverRepository;
    private readonly IWorkflowActionResultRepository _workflowActionResultRepository;
    private readonly IWorkflowInfraObjectRepository _workflowInfraObjectRepository;
    private readonly IWorkflowOperationGroupRepository _workflowOperationGroupRepository;
    private readonly IWorkflowOperationRepository _workflowOperationRepository;
    private readonly IWorkflowActionRepository _workflowActionRepository;
    private readonly IDashboardViewRepository _dashboardViewRepository;

    public GetWorkflowOperationDrDrillReportQueryHandler(IMapper mapper,
        IWorkflowOperationRepository workflowOperationRepository,
        IWorkflowOperationGroupRepository workflowOperationGroupRepository,
        IWorkflowActionResultRepository workflowActionResultRepository,
        IInfraObjectRepository infraObjectRepository,
        IWorkflowInfraObjectRepository workflowInfraObjectRepository,
        IServerRepository serverRepository,
        IBusinessServiceRepository businessServiceRepository,
        IBusinessFunctionRepository businessFunctionRepository,
        ILoggedInUserService loggedInUserService, IPublisher publisher, IDatabaseRepository databaseRepository,
        ILoadBalancerRepository nodeConfigurationRepository, IWorkflowActionRepository workflowActionRepository,
        IDashboardViewRepository dashboardViewRepository)
    {
        _mapper = mapper;
        _workflowOperationRepository = workflowOperationRepository;
        _workflowOperationGroupRepository = workflowOperationGroupRepository;
        _workflowActionResultRepository = workflowActionResultRepository;
        _infraObjectRepository = infraObjectRepository;
        _workflowInfraObjectRepository = workflowInfraObjectRepository;
        _serverRepository = serverRepository;
        _businessServiceRepository = businessServiceRepository;
        _businessFunctionRepository = businessFunctionRepository;
        _loggedInUserService = loggedInUserService;
        _publisher = publisher;
        _databaseRepository = databaseRepository;
        _nodeConfigurationRepository = nodeConfigurationRepository;
        _workflowActionRepository = workflowActionRepository;
        _dashboardViewRepository = dashboardViewRepository;
    }

    public async Task<DrDrillReport> Handle(GetWorkflowOperationDrDrillReportQuery request, CancellationToken cancellationToken)
    {
        if (request.IsCustom)
        {
            if (!_loggedInUserService.IsSuperAdmin)
            {
                throw new UnauthorizedAccessException("You are not authorized to view this report.");
            }

            var workflowDrDrillList = new WorkflowOperationDrDrillReportVm();
            var workflowOperationGroupList = new WorkflowOperationGroupDrDrillReportVm();
            var workflowActionResultList = await _workflowActionResultRepository.GetWorkflowActionResultByWorkflowOperationId(request.Id);

            var result = _mapper.Map<List<WorkflowActionResultDrDrillReportVm>>(workflowActionResultList);

            foreach (var actionResult in result)
            {
                if (actionResult.ExecutionNode.IsNullOrWhiteSpace()) continue;

                var nodeDtl = await _nodeConfigurationRepository.GetByReferenceIdAsync(actionResult.ExecutionNode);

                nodeDtl ??= new Domain.Entities.LoadBalancer();

                actionResult.ExecutionNode = nodeDtl.Name.IsNullOrWhiteSpace() ? "NA" : nodeDtl.Name;
            }

            workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.AddRange(result);
            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Add(workflowOperationGroupList);
            if (workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.Count > 0)
            {
                workflowOperationGroupList.WorkflowActionResultDrDrillReportVms.ForEach(actionlist =>
                {
                    actionlist.TotalTime = GetTotalTime(actionlist.StartTime, actionlist.EndTime);

                });
            }
            return new DrDrillReport
            {
                ReportGeneratedBy = _loggedInUserService.LoginName,
                Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
                WorkflowOperationDrDrillReportVm = workflowDrDrillList
            };
        }
        else
        {
            var workflowActionResultList = new List<Domain.Entities.WorkflowActionResult>();

            // var workflowOperationGroupDrDrill = new WorkflowOperationGroupDrDrillReportVm();

            var workflowActionResultDrDrillList = new List<WorkflowActionResultDrDrillReportVm>();

            var workflowOperation = request.RunMode.IsNotNullOrWhiteSpace()
                ? await _workflowOperationRepository.GetByReferenceIdAndRunMode(request.Id, request.RunMode)
                : await _workflowOperationRepository.GetByReferenceIdAsync(request.Id);

            // var workflowOperation = await _workflowOperationRepository.GetByReferenceIdAndRunMode(request.Id, request.RunMode);

            Guard.Against.NullOrDeactive(workflowOperation, nameof(Domain.Entities.WorkflowOperation),
                new NotFoundException(nameof(Domain.Entities.WorkflowOperation), request.Id));

            var workflowDrDrillList = _mapper.Map<WorkflowOperationDrDrillReportVm>(workflowOperation);

            var workflowOperationGroup = !string.IsNullOrEmpty(workflowOperation.ReferenceId) ?
                await _workflowOperationGroupRepository.GetWorkflowOperationGroupByWorkflowOperationIdByReport(workflowOperation
                    .ReferenceId) : new List<Domain.Entities.WorkflowOperationGroup>();

            if (workflowOperationGroup is not null) workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms =
                 _mapper.Map<List<WorkflowOperationGroupDrDrillReportVm>>(workflowOperationGroup);


            var nodeIdList = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms
                .SelectMany(x => x.NodeId?
                    .Split(',', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>())
                .ToList();


            var loadBalancers = nodeIdList!.Count > 0
                ? await _nodeConfigurationRepository.GetNodeNameByIdAsync(nodeIdList)
                : new List<Domain.Entities.LoadBalancer>();

            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Select(groupVm =>
            {
                var nodeNames = groupVm.NodeId?
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => loadBalancers.FirstOrDefault(lb => lb.ReferenceId == id)?.Name)
                    .Where(name => name.IsNotNullOrWhiteSpace())
                    .ToList();

                groupVm.NodeName = nodeNames != null && nodeNames.Any()
                    ? string.Join(",", nodeNames)
                    : string.Empty;

                return groupVm;
            }).ToList();

            workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.ForEach(wfo =>
            {
                wfo.TotalTime = GetTotalTime(wfo.StartTime, wfo.EndTime);

                var infraObjectDrDrill = !string.IsNullOrEmpty(wfo.InfraObjectId) ? _infraObjectRepository.GetByReferenceIdAsync(wfo.InfraObjectId).Result : new Domain.Entities.InfraObject();
                var dashboardViewDrill = !string.IsNullOrEmpty(wfo.InfraObjectId) ? _dashboardViewRepository.GetBusinessViewByInfraObjectId(wfo.InfraObjectId).Result : new Domain.Entities.DashboardView();
                var workflowInfraObject = !string.IsNullOrEmpty(wfo.WorkflowId) ? _workflowInfraObjectRepository.GetWorkflowInfraObjectByWorkflowIdAsync(wfo.WorkflowId).Result : new Domain.Entities.WorkflowInfraObject();

                List<string> PrDbSid = new List<string>();
                List<string> DrDbSid = new List<string>();
                List<string> ProductionIpAddress = new List<string>();
                List<string> DrIpAddress = new List<string>();
                string PRServerName = string.Empty;
                string DRServerName = string.Empty;

                if (infraObjectDrDrill is not null)
                {
                    var prServerProperties = !string.IsNullOrEmpty(infraObjectDrDrill?.ServerProperties) ? JObject.Parse(infraObjectDrDrill?.ServerProperties) : null;
                    var prServerId = prServerProperties != null ? prServerProperties.SelectToken("PR.id")?.ToString() : null;
                    PRServerName = prServerProperties != null ? prServerProperties.SelectToken("PR.name")?.ToString() : null;
                    var prSplitId = prServerId != null ? prServerId.Split(",") : null;

                    var drServerProperties = !string.IsNullOrEmpty(infraObjectDrDrill?.ServerProperties) ? JObject.Parse(infraObjectDrDrill?.ServerProperties) : null;
                    var drServerId = drServerProperties != null ? drServerProperties.SelectToken("DR.id")?.ToString() : null;
                    DRServerName = drServerProperties != null ? drServerProperties.SelectToken("DR.name")?.ToString() : null;
                    var drSplitId = drServerId != null ? drServerId.Split(",") : null;

                    var prDatabaseProperties = !string.IsNullOrEmpty(infraObjectDrDrill?.DatabaseProperties) ? JObject.Parse(infraObjectDrDrill?.DatabaseProperties) : null;
                    var prDatabaseId = prDatabaseProperties != null ? prDatabaseProperties.SelectToken("PR.id")?.ToString() : null;
                    var prSidId = prDatabaseId != null ? prDatabaseId.Split(",") : null;

                    var drDatabaseProperties = !string.IsNullOrEmpty(infraObjectDrDrill?.DatabaseProperties) ? JObject.Parse(infraObjectDrDrill?.DatabaseProperties) : null;
                    var drDatabaseId = drDatabaseProperties != null ? drDatabaseProperties.SelectToken("DR.id")?.ToString() : null;
                    var drSidId = drDatabaseId != null ? drDatabaseId.Split(",") : null;

                    if (prSplitId != null)
                    {
                        foreach (var prSplit in prSplitId)
                        {
                            var prServer = prSplit != null ? _serverRepository.GetByReferenceIdAsync(prSplit).Result : null;
                            var prIpAddress = prServer != null ? GetJsonProperties.GetIpAddressFromProperties(prServer.Properties) : "-";
                            ProductionIpAddress.Add(prIpAddress);
                        }
                    }
                    else
                    {
                        ProductionIpAddress = null;
                    }
                    if (drSplitId != null)
                    {
                        foreach (var drSplit in drSplitId)
                        {
                            var drServer = drSplit != null ? _serverRepository.GetByReferenceIdAsync(drSplit).Result : null;
                            var drIpAddress = drServer != null ? GetJsonProperties.GetIpAddressFromProperties(drServer.Properties) : "-";
                            DrIpAddress.Add(drIpAddress);
                        }
                    }
                    else
                    {
                        DrIpAddress = null;
                    }
                    if (prSidId != null)
                    {
                        foreach (var prSid in prSidId)
                        {
                            var prDatabase = prSid != null ? _databaseRepository.GetByReferenceIdAsync(prSid).Result : null;
                            var prDbSid = prDatabase != null ? GetJsonProperties.GetJsonDatabaseSidValue(prDatabase.Properties) : "-";
                            PrDbSid.Add(prDbSid);
                        }
                    }
                    else
                    {
                        PrDbSid = null;
                    }
                    if (drSidId != null)
                    {
                        foreach (var drSid in drSidId)
                        {
                            var drDatabase = drSid != null ? _databaseRepository.GetByReferenceIdAsync(drSid).Result : null;
                            var drDbSid = drDatabase != null ? GetJsonProperties.GetJsonDatabaseSidValue(drDatabase.Properties) : "-";
                            DrDbSid.Add(drDbSid);
                        }
                    }
                    else
                    {
                        DrDbSid = null;
                    }
                }
                if (workflowInfraObject != null && workflowInfraObject.ActionType.Trim().ToLower().Equals("switchback"))
                {
                    wfo.WorkflowActionType = workflowInfraObject.ActionType;
                    wfo.PRServerName = DRServerName;
                    wfo.DRServerName = PRServerName;
                    wfo.PRDatabaseName = DrDbSid;
                    wfo.DRDatabaseName = PrDbSid;
                    wfo.ProductionIpAddress = DrIpAddress;
                    wfo.DrIpAddress = ProductionIpAddress;
                }
                else
                {
                    wfo.WorkflowActionType = workflowInfraObject?.ActionType;
                    wfo.PRServerName = PRServerName;
                    wfo.DRServerName = DRServerName;
                    wfo.PRDatabaseName = PrDbSid;
                    wfo.DRDatabaseName = DrDbSid;
                    wfo.ProductionIpAddress = ProductionIpAddress;
                    wfo.DrIpAddress = DrIpAddress;
                }

                var workflowActionResult = !string.IsNullOrEmpty(wfo.WorkflowOperationId) && !string.IsNullOrEmpty(wfo.ReferenceId) ? _workflowActionResultRepository
                    .GetWorkflowActionResultByWorkflowOperationIdAndGroupId(wfo.WorkflowOperationId, wfo.ReferenceId)
                    .Result : new List<Domain.Entities.WorkflowActionResult>();

                wfo.WorkflowActionResultDrDrillReportVms =
                    _mapper.Map<List<WorkflowActionResultDrDrillReportVm>>(workflowActionResult);

                wfo.WorkflowActionResultDrDrillReportVms.ForEach(wfa =>
                {
                    var nodeDetails = !string.IsNullOrWhiteSpace(wfa.ExecutionNode)
                        ? _nodeConfigurationRepository.GetByReferenceIdAsync(wfa.ExecutionNode).Result
                        : new Domain.Entities.LoadBalancer();

                    wfa.ExecutionNode = !string.IsNullOrWhiteSpace(nodeDetails?.Name) ? nodeDetails.Name : "-";
                    wfa.TotalTime = GetTotalTime(wfa.StartTime, wfa.EndTime);
                });

                workflowActionResultList.AddRange(workflowActionResult);

                workflowActionResultDrDrillList.AddRange(wfo.WorkflowActionResultDrDrillReportVms);

                var businessService = !string.IsNullOrEmpty(infraObjectDrDrill?.BusinessServiceId) ? _businessServiceRepository
                    .GetByReferenceIdAsync(infraObjectDrDrill?.BusinessServiceId).Result : new Domain.Entities.BusinessService();

                var businessFunction = !string.IsNullOrEmpty(infraObjectDrDrill?.BusinessFunctionId) ? _businessFunctionRepository
                    .GetByReferenceIdAsync(infraObjectDrDrill?.BusinessFunctionId).Result : new Domain.Entities.BusinessFunction();

                workflowDrDrillList.BusinessServiceDrDillDetails =
                    _mapper.Map<BusinessServiceDrDillDetails>(businessService);

                workflowDrDrillList.BusinessServiceDrDillDetails.ConfiguredRTO = !string.IsNullOrEmpty(businessFunction?.ConfiguredRTO) ? businessFunction?.ConfiguredRTO : "-";

                workflowDrDrillList.ActualRTO = !string.IsNullOrEmpty(dashboardViewDrill?.CurrentRTO) ? TimeSpan.Parse(dashboardViewDrill?.CurrentRTO) : TimeSpan.Zero;

                wfo.ConfiguredRTO = !string.IsNullOrEmpty(businessFunction?.ConfiguredRTO) ? TimeSpan.FromMinutes(int.Parse(businessFunction!.ConfiguredRTO)).ToString() : "-";

                if (workflowActionResultDrDrillList.Count > 0)
                {
                    var startRTO = "start_workflowrto"; var stopRTO = "stop_workflowrto";

                    var startAction = _workflowActionRepository.GetWorkflowActionDetailsByName(startRTO).Result;
                    var stopAction = _workflowActionRepository.GetWorkflowActionDetailsByName(stopRTO).Result;

                    var startList = workflowActionResult.Where(x => x.ActionId.Equals(startAction?.ReferenceId)).ToList();
                    var stopList = workflowActionResult.Where(x => x.ActionId.Equals(stopAction?.ReferenceId)).ToList();

                    if (startList != null && stopList != null && startList.Count > 0 && stopList.Count > 0)
                    {
                        var startTime = startList.FirstOrDefault()?.StartTime.ToString();
                        var endTime = stopList.FirstOrDefault()?.EndTime.ToString();
                        wfo.ActualRTOStartTime = startTime;
                        wfo.ActualRTOEndTime = endTime;
                        wfo.ActualRTO = GetTotalTime(wfo.ActualRTOStartTime, wfo.ActualRTOEndTime);
                        wfo.ConfiguredRTOLessActualRTO = GetTotalTime(wfo.ActualRTO.ToString(), wfo.ConfiguredRTO);
                    }
                    else
                    {
                        var startTime = workflowActionResult.Min(x => x.StartTime).ToString();
                        var endTime = workflowActionResult.Max(x => x.EndTime).ToString();
                        wfo.ActualRTOStartTime = startTime;
                        wfo.ActualRTOEndTime = endTime;
                        wfo.ActualRTO = GetTotalTime(wfo.ActualRTOStartTime, wfo.ActualRTOEndTime);
                        wfo.ConfiguredRTOLessActualRTO = GetTotalTime(wfo.ActualRTO.ToString(), wfo.ConfiguredRTO);
                    }
                }
            });

            if (workflowActionResultList.Count > 0)
            {
                var drillStartTime = workflowDrDrillList.StartTime;
                var drillEndTime = workflowDrDrillList.EndTime;

                workflowDrDrillList.StartTime = drillStartTime;
                workflowDrDrillList.EndTime = drillEndTime;
                workflowDrDrillList.TotalTime = GetTotalTime(drillStartTime, drillEndTime);

                var actualRtoStartTime = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.
                    SelectMany(x => x.WorkflowActionResultDrDrillReportVms).Min(x => x.StartTime);

                var actualRtoEndTime = workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.
                    SelectMany(x => x.WorkflowActionResultDrDrillReportVms).Max(x => x.EndTime);

                workflowDrDrillList.ActualRTOStartTime = actualRtoStartTime;
                workflowDrDrillList.ActualRTOEndTime = actualRtoEndTime;

                workflowDrDrillList.TotalActionExecutedCount = workflowActionResultDrDrillList.Count;
            }

            workflowDrDrillList.ConfiguredRTO = !string.IsNullOrEmpty(workflowDrDrillList.BusinessServiceDrDillDetails.ConfiguredRTO) ? TimeSpan
                .FromMinutes(int.Parse(workflowDrDrillList.BusinessServiceDrDillDetails.ConfiguredRTO)).ToString() : "-";

            workflowDrDrillList.ConfiguredRTOLessActualRTO = GetTotalTime(workflowDrDrillList.ActualRTO.ToString(),
                workflowDrDrillList.ConfiguredRTO);

            workflowDrDrillList.TotalProfilesExecutedCount = 1;

            workflowDrDrillList.TotalWorkflowExecutedCount =
                workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms.Count;

            workflowDrDrillList.TotalWorkflowActionCount += workflowDrDrillList.WorkflowOperationGroupDrDrillDetailVms
                .Sum(count => int.TryParse(count.ProgressStatus?.Split('/').LastOrDefault(), out var actionCount)
                    ? actionCount
                    : 0);
            if (request.RunMode.IsNotNullOrWhiteSpace())
            {
                await _publisher.Publish(
                    new ReportViewedEvent { ReportName = "DR Drill Report", ActivityType = ActivityType.View.ToString() },
                    CancellationToken.None);
            }
            return new DrDrillReport
            {
                ReportGeneratedBy = _loggedInUserService.LoginName,
                Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
                WorkflowOperationDrDrillReportVm = workflowDrDrillList
            };
        }
    }
    private static TimeSpan GetTotalTime(string startTime, string endTime)
    {
        try
        {
            var start = DateTime.Parse(startTime);
            var end = DateTime.Parse(endTime);
            var ts = end - start;
            return ts;
        }
        catch (Exception)
        {
            return TimeSpan.Zero;
        }
    }
}