﻿using ContinuityPatrol.Application.Features.ComponentType.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.UnitTests.Features.ComponentType.Queries;

public class GetComponentTypePaginatedListQueryHandlerTests : IClassFixture<ComponentTypeFixture>, IClassFixture<FormTypeCategoryFixture>
{
    private readonly ComponentTypeFixture _componentTypeFixture;
    private readonly Mock<IComponentTypeRepository> _mockComponentTypeRepository;
    private readonly GetComponentTypePaginatedListQueryHandler _handler;

    public GetComponentTypePaginatedListQueryHandlerTests(ComponentTypeFixture componentTypeFixture, FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _componentTypeFixture = componentTypeFixture;

       
        SetupTestData();

        _mockComponentTypeRepository = ComponentTypeRepositoryMocks.GetPaginatedComponentTypeRepository(_componentTypeFixture.ComponentTypes);

        Mock<IFormTypeCategoryRepository> mockFormTypeCategoryRepository = FormTypeCategoryRepositoryMocks.GetFormTypeCategoriesByFormTypeIds(formTypeCategoryFixture
            .FormTypeCategories);
        _handler = new GetComponentTypePaginatedListQueryHandler(_componentTypeFixture.Mapper, _mockComponentTypeRepository.Object, mockFormTypeCategoryRepository.Object);
    }

    private void SetupTestData()
    {
        
        _componentTypeFixture.ComponentTypes[0].ReferenceId = "component-1";
        _componentTypeFixture.ComponentTypes[0].ComponentName = "Server_Type";
        _componentTypeFixture.ComponentTypes[0].FormTypeId = "form-type-1";

      
        _componentTypeFixture.ComponentTypes[1].ReferenceId = "component-2";
        _componentTypeFixture.ComponentTypes[1].ComponentName = "Testing_Component";
        _componentTypeFixture.ComponentTypes[1].FormTypeId = "form-type-2";

       
        _componentTypeFixture.ComponentTypes[2].ReferenceId = "component-3";
        _componentTypeFixture.ComponentTypes[2].ComponentName = "Database_Type";
        _componentTypeFixture.ComponentTypes[2].FormTypeId = "form-type-3";
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_QueryStringNotMatch()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Return_TotalPage_ShouldRequested()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10 }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.TotalPages.ShouldBe(1);
    }
    
    [Fact]
    public async Task Handle_Return_ComponentTypes_With_MultipleQueryStringParameter()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(3);

        result.Data[0].Id.ShouldBe(_componentTypeFixture.ComponentTypes[0].ReferenceId);

        result.Data[0].ComponentName.ShouldBe("Server_Type");
    }

    [Fact]
    public async Task Handle_Return_PaginatedComponentTypes_When_QueryStringMatch()
    {
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery { PageNumber = 1, PageSize = 10, SearchString = "Test" }, CancellationToken.None);

        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        result.TotalCount.ShouldBe(1);

        result.Data[0].ShouldBeOfType<ComponentTypeListVm>();

        result.Data[0].ComponentName.ShouldBe("Testing_Component");
    }
    [Fact]
    public async Task Handle_Return_PaginatedComponentTypes_When_QueryStringMatch_With_FormTypeId()
    {
        var formTypeId = Guid.NewGuid().ToString(); 

        
        _componentTypeFixture.ComponentTypes[0].FormTypeId = formTypeId;
        _componentTypeFixture.ComponentTypes[0].ComponentName = "Testing_Component";

        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = formTypeId, 
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Test"
           
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
        result.TotalCount.ShouldBeGreaterThanOrEqualTo(0);
            result.Data[0].ShouldBeOfType<ComponentTypeListVm>();
            result.Data[0].ComponentName.ShouldContain("Test");
        
      
    }
    [Fact]
    public async Task Handle_Return_SortedComponentTypes_When_FormTypeIdWithSortingProvided()
    {
        // Arrange
        var formTypeId = "form-type-1";

        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = formTypeId,
            PageNumber = 1,
            PageSize = 10,
            SortColumn = "ComponentName",
            SortOrder = "asc"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.Data.ShouldNotBeNull();

        // Verify sorting if there are multiple items
        if (result.Data.Count > 1)
        {
            var sortedNames = result.Data.Select(x => x.ComponentName).ToList();
            sortedNames.ShouldBe(sortedNames.OrderBy(x => x).ToList());
        }
    }
   
    [Fact]
    public async Task Handle_Return_FilteredComponentTypes_When_FormTypeIdAndSearchStringProvided()
    {
        // Arrange
        var formTypeId = "form-type-1";
        var searchString = "Server";

        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = formTypeId,
            SearchString = searchString,
            PageNumber = 1,
            PageSize = 10
           
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.Data.ShouldNotBeNull();
        result.Data[0].ComponentName.ShouldContain("Server");
        
    }
    [Fact]
    public async Task Handle_Return_EmptyList_When_InvalidFormTypeIdProvided()
    {
        // Arrange
        var invalidFormTypeId = "invalid-form-type-id";

        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = invalidFormTypeId,
            PageNumber = 1,
            PageSize = 10
           
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.TotalCount.ShouldBe(0);
        result.Data.Count.ShouldBe(0);
    }
    [Fact]
    public async Task Handle_Return_PaginatedResults_When_FormTypeIdWithPaginationProvided()
    {
        // Arrange
        var formTypeId = "form-type-1";

        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = formTypeId,
            PageNumber = 1,
            PageSize = 2 // Small page size to test pagination

        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.CurrentPage.ShouldBe(1);
        result.PageSize.ShouldBe(2);
        result.TotalPages.ShouldBeGreaterThanOrEqualTo(1);
    }

    [Fact]
    public async Task Handle_Call_PaginatedListAllAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new GetComponentTypePaginatedListQuery(), CancellationToken.None);
        _mockComponentTypeRepository.Verify(x => x.PaginatedListAllAsync(It.IsAny<int>(),
            It.IsAny<int>(), It.IsAny<ComponentTypeFilterSpecification>(),
            It.IsAny<string>(), It.IsAny<string>()), Times.Once);
        //await _handler.Handle(new GetComponentTypePaginatedListQuery(), CancellationToken.None);

        //_mockComponentTypeRepository.Verify(x => x.GetPaginatedQuery(), Times.Once);
    }
    [Fact]
    public void GetComponentTypePaginatedListQuery_Should_SetAllProperties_Correctly()
    {
        // Act
        var query = new GetComponentTypePaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 15,
            SearchString = "database",
            SortColumn = "Name",
            SortOrder = "desc",
            FormTypeId = "form-123"
        };

        // Assert - This ensures all property getters are covered
        query.PageNumber.ShouldBe(2);
        query.PageSize.ShouldBe(15);
        query.SearchString.ShouldBe("database");
        query.SortColumn.ShouldBe("Name");
        query.SortOrder.ShouldBe("desc");
        query.FormTypeId.ShouldBe("form-123");
    }
    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlyPageNumberProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            PageNumber = 2 // Only PageNumber set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.CurrentPage.ShouldBe(2);
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlyPageSizeProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            PageSize = 5 // Only PageSize set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.PageSize.ShouldBe(5);
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlySearchStringProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SearchString = "Server" // Only SearchString set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        if (result.Data.Any())
        {
            result.Data.ShouldAllBe(x => x.ComponentName.Contains("Server", StringComparison.OrdinalIgnoreCase));
        }
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlySortColumnProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SortColumn = "ComponentName" // Only SortColumn set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlySortOrderProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SortOrder = "desc" // Only SortOrder set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_OnlyFormTypeIdProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            FormTypeId = "form-type-1" // Only FormTypeId set
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_AllPropertiesProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Server",
            SortColumn = "ComponentName",
            SortOrder = "asc",
            FormTypeId = "form-type-1"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.CurrentPage.ShouldBe(1);
        result.PageSize.ShouldBe(10);
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_NullPropertiesProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            PageNumber = 0,
            PageSize = 0,
            SearchString = null,
            SortColumn = null,
            SortOrder = null,
            FormTypeId = null
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_PaginatedResult_When_EmptyStringPropertiesProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SearchString = "",
            SortColumn = "",
            SortOrder = "",
            FormTypeId = ""
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        result.ShouldNotBeNull();
    }

    [Fact]
    public async Task Handle_Return_SortedResults_When_SortColumnAndOrderProvided()
    {
        // Act
        var resultAsc = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SortColumn = "ComponentName",
            SortOrder = "asc"
        }, CancellationToken.None);

        var resultDesc = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SortColumn = "ComponentName",
            SortOrder = "desc"
        }, CancellationToken.None);

        // Assert
        resultAsc.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        resultDesc.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();

        if (resultAsc.Data.Count > 1 && resultDesc.Data.Count > 1)
        {
            resultAsc.Data[0].ComponentName.ShouldNotBe(resultDesc.Data[0].ComponentName);
        }
    }

    [Fact]
    public async Task Handle_Return_FilteredResults_When_SearchStringAndFormTypeIdProvided()
    {
        // Act
        var result = await _handler.Handle(new GetComponentTypePaginatedListQuery
        {
            SearchString = "Test",
            FormTypeId = "form-type-2"
        }, CancellationToken.None);

        // Assert
        result.ShouldBeOfType<PaginatedResult<ComponentTypeListVm>>();
        if (result.Data.Any())
        {
            result.Data.ShouldAllBe(x => x.ComponentName.Contains("Test", StringComparison.OrdinalIgnoreCase));
        }
    }


}
