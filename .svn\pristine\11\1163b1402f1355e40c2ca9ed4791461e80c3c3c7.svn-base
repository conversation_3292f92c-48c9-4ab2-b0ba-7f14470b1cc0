﻿using ContinuityPatrol.Application.Features.BusinessService.Events.Create;
using ContinuityPatrol.Application.Features.BusinessService.Events.DashboardViewEvent.Create;

namespace ContinuityPatrol.Application.Features.BusinessService.Commands.Create;

public class
    CreateBusinessServiceCommandHandler : IRequestHandler<CreateBusinessServiceCommand, CreateBusinessServiceResponse>
{
    private readonly IBusinessServiceRepository _businessServiceRepository;
    private readonly IMapper _mapper;
    private readonly IPublisher _publisher;

    public CreateBusinessServiceCommandHandler(IMapper mapper, IBusinessServiceRepository businessServiceRepository,
        IPublisher publisher)
    {
        _mapper = mapper;
        _publisher = publisher;
        _businessServiceRepository = businessServiceRepository;
    }

    public async Task<CreateBusinessServiceResponse> Handle(CreateBusinessServiceCommand request,
        CancellationToken cancellationToken)
    {
        var businessService = _mapper.Map<Domain.Entities.BusinessService>(request);

        businessService = await _businessServiceRepository.AddAsync(businessService);

        var response = new CreateBusinessServiceResponse
        {
            Message = Message.Create("Operational Service", businessService.Name),

            BusinessServiceId = businessService.ReferenceId
        };

        await _publisher.Publish(new BusinessServiceDashboardViewCreatedEvent
        {
            BusinessServiceId = businessService.ReferenceId,
            BusinessServiceName = businessService.Name,
            CompanyId = businessService.CompanyId,
            Description = businessService.Description,
            SiteProperties = businessService.SiteProperties,
            Priority = businessService.Priority
        }, cancellationToken);

        await _publisher.Publish(new BusinessServiceCreatedEvent { BusinessServiceName = businessService.Name },
            cancellationToken);

        return response;
    }
}