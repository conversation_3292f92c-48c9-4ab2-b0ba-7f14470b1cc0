﻿namespace ContinuityPatrol.Application.Features.OracleRACMonitorStatus.Commands.Update;

public class
    UpdateOracleRACStatusCommandHandler : IRequestHandler<UpdateOracleRACStatusCommand, UpdateOracleRACStatusResponse>
{
    private readonly IMapper _mapper;
    private readonly IOracleRacMonitorStatusRepository _oracleRacMonitorStatusRepository;

    public UpdateOracleRACStatusCommandHandler(IMapper mapper,
        IOracleRacMonitorStatusRepository oracleRacMonitorStatusRepository)
    {
        _mapper = mapper;
        _oracleRacMonitorStatusRepository = oracleRacMonitorStatusRepository;
    }

    public async Task<UpdateOracleRACStatusResponse> Handle(UpdateOracleRACStatusCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _oracleRacMonitorStatusRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null)
            throw new NotFoundException(nameof(Domain.Entities.OracleRACMonitorStatus), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateOracleRACStatusCommand),
            typeof(Domain.Entities.OracleRACMonitorStatus));

        await _oracleRacMonitorStatusRepository.UpdateAsync(eventToUpdate);

        var response = new UpdateOracleRACStatusResponse
        {
            Message = Message.Update(nameof(Domain.Entities.OracleRACMonitorStatus), eventToUpdate.ReferenceId),
            Id = eventToUpdate.ReferenceId
        };

        return response;
    }
}