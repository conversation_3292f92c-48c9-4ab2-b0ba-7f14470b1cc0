﻿using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Application.UnitTests.Attributes;

public class AutoUserActivityDataAttribute : AutoDataAttribute
{
    public AutoUserActivityDataAttribute() : base(() =>
        {
            var fixture = new Fixture();

            fixture.Customizations.Add(new StringPropertyTruncateSpecimenBuilder<CreateUserActivityCommand>(p => p.<PERSON>, 10));

            return fixture;
        })
    {

    }
}