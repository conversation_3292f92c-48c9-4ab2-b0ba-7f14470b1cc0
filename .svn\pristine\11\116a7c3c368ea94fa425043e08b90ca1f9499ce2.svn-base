﻿using ContinuityPatrol.Application.Features.InfraObjectScheduler.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraObjectScheduler.Commands;


public class DeleteInfraObjectSchedulerTests : IClassFixture<InfraObjectSchedulerFixture>
{
    private readonly InfraObjectSchedulerFixture _infraObjectSchedulerFixture;

    private readonly Mock<IInfraObjectSchedulerRepository> _mockInfraObjectSchedulerRepository;

    private readonly DeleteInfraObjectSchedulerCommandHandler _handler;

    public DeleteInfraObjectSchedulerTests(InfraObjectSchedulerFixture infraObjectSchedulerFixture)
    {
        _infraObjectSchedulerFixture = infraObjectSchedulerFixture;

        var mockPublisher = new Mock<IPublisher>();

        _mockInfraObjectSchedulerRepository = InfraObjectSchedulerRepositoryMocks.DeleteInfraObjectSchedulerRepository(_infraObjectSchedulerFixture.InfraObjectSchedulers);

        _handler = new DeleteInfraObjectSchedulerCommandHandler(_mockInfraObjectSchedulerRepository.Object, mockPublisher.Object);
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_InfraObjectSchedulerDeleted()
    {
        var result = await _handler.Handle(new DeleteInfraObjectSchedulerCommand { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        result.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_DeleteInfraObjectSchedulerResponse_When_InfraObjectSchedulerDeleted()
    {
        var result = await _handler.Handle(new DeleteInfraObjectSchedulerCommand { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeOfType(typeof(DeleteInfraObjectSchedulerResponse));

        result.IsActive.ShouldBeFalse();

        result.Success.ShouldBeTrue();

        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidInfraObjectSchedulerId()
    {
        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteInfraObjectSchedulerCommand { Id = int.MaxValue.ToString() }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ReturnIsActive_False_WhenDeleteInfraObjectScheduler()
    {
        await _handler.Handle(new DeleteInfraObjectSchedulerCommand { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        var infraObject = await _mockInfraObjectSchedulerRepository.Object.GetByReferenceIdAsync(_infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId);

        infraObject.IsActive.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(new DeleteInfraObjectSchedulerCommand { Id = _infraObjectSchedulerFixture.InfraObjectSchedulers[0].ReferenceId }, CancellationToken.None);

        _mockInfraObjectSchedulerRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockInfraObjectSchedulerRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraObjectScheduler>()), Times.Once);
    }
}