using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Create;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Delete;
using ContinuityPatrol.Application.Features.FiaImpactType.Commands.Update;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetList;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.FiaImpactType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FiaImpactTypeModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class FiaImpactTypeFixture : IDisposable
{
    public CreateFiaImpactTypeCommand CreateFiaImpactTypeCommand { get; set; }
    public UpdateFiaImpactTypeCommand UpdateFiaImpactTypeCommand { get; set; }
    public DeleteFiaImpactTypeCommand DeleteFiaImpactTypeCommand { get; set; }
    public GetFiaImpactTypeDetailQuery GetFiaImpactTypeDetailQuery { get; set; }
    public GetFiaImpactTypeListQuery GetFiaImpactTypeListQuery { get; set; }
    public GetFiaImpactTypePaginatedListQuery GetFiaImpactTypePaginatedListQuery { get; set; }
    public GetFiaImpactTypeNameUniqueQuery GetFiaImpactTypeNameUniqueQuery { get; set; }
    public List<FiaImpactTypeListVm> FiaImpactTypeListVm { get; set; }
    public FiaImpactTypeDetailVm FiaImpactTypeDetailVm { get; set; }
    public CreateFiaImpactTypeResponse CreateFiaImpactTypeResponse { get; set; }
    public UpdateFiaImpactTypeResponse UpdateFiaImpactTypeResponse { get; set; }
    public DeleteFiaImpactTypeResponse DeleteFiaImpactTypeResponse { get; set; }

    public FiaImpactTypeFixture()
    {
        // Create Command
        CreateFiaImpactTypeCommand = new CreateFiaImpactTypeCommand
        {
            Name = "Revenue Loss",
            Description = "Direct financial impact from loss of revenue streams",
            FiaImpactCategoryId = Guid.NewGuid().ToString(),
            FiaImpactCategoryName = "Financial Impact"
        };

        // Update Command
        UpdateFiaImpactTypeCommand = new UpdateFiaImpactTypeCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Revenue Loss",
            Description = "Updated direct financial impact from loss of revenue streams including indirect costs",
            FiaImpactCategoryId = Guid.NewGuid().ToString(),
            FiaImpactCategoryName = "Financial Impact"
        };

        // Delete Command
        DeleteFiaImpactTypeCommand = new DeleteFiaImpactTypeCommand
        {
            Id = Guid.NewGuid().ToString()
        };

        // Get Detail Query
        GetFiaImpactTypeDetailQuery = new GetFiaImpactTypeDetailQuery
        {
            Id = Guid.NewGuid().ToString()
        };

        // Get List Query
        GetFiaImpactTypeListQuery = new GetFiaImpactTypeListQuery();

        // Get Paginated List Query
        GetFiaImpactTypePaginatedListQuery = new GetFiaImpactTypePaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        // Get Name Unique Query
        GetFiaImpactTypeNameUniqueQuery = new GetFiaImpactTypeNameUniqueQuery
        {
            Name = "Revenue Loss",
            Id = null
        };

        // FiaImpactType List ViewModels
        FiaImpactTypeListVm = new List<FiaImpactTypeListVm>
        {
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Revenue Loss",
                Description = "Direct financial impact from loss of revenue streams",
                FiaImpactCategoryId = Guid.NewGuid().ToString(),
                FiaImpactCategoryName = "Financial Impact"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Productivity Loss",
                Description = "Impact on employee productivity and operational efficiency",
                FiaImpactCategoryId = Guid.NewGuid().ToString(),
                FiaImpactCategoryName = "Operational Impact"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Customer Satisfaction",
                Description = "Impact on customer experience and satisfaction levels",
                FiaImpactCategoryId = Guid.NewGuid().ToString(),
                FiaImpactCategoryName = "Strategic Impact"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Regulatory Compliance",
                Description = "Impact related to regulatory compliance and legal requirements",
                FiaImpactCategoryId = Guid.NewGuid().ToString(),
                FiaImpactCategoryName = "Compliance Impact"
            },
            new()
            {
                Id = Guid.NewGuid().ToString(),
                Name = "Brand Reputation",
                Description = "Impact on brand image and market reputation",
                FiaImpactCategoryId = Guid.NewGuid().ToString(),
                FiaImpactCategoryName = "Strategic Impact"
            }
        };

        // FiaImpactType Detail ViewModel
        FiaImpactTypeDetailVm = new FiaImpactTypeDetailVm
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Revenue Loss",
            Description = "Direct financial impact from loss of revenue streams during business disruptions",
            FiaImpactCategoryId = Guid.NewGuid().ToString(),
            FiaImpactCategoryName = "Financial Impact"
        };

        // Create Response
        CreateFiaImpactTypeResponse = new CreateFiaImpactTypeResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "FIA Impact Type Revenue Loss created successfully"
        };

        // Update Response
        UpdateFiaImpactTypeResponse = new UpdateFiaImpactTypeResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "FIA Impact Type Updated Revenue Loss updated successfully"
        };

        // Delete Response
        DeleteFiaImpactTypeResponse = new DeleteFiaImpactTypeResponse
        {
            Message = "FIA Impact Type Revenue Loss deleted successfully"
        };
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
