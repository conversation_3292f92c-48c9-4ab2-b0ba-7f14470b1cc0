﻿using ContinuityPatrol.Domain.ViewModels.SiteModel;

namespace ContinuityPatrol.Application.Features.Site.Queries.GetNames;

public class GetSiteNameQueryHandler : IRequestHandler<GetSiteNameQuery, List<SiteNameVm>>
{
    private readonly IMapper _mapper;
    private readonly ISiteRepository _siteRepository;
    private readonly ISiteTypeRepository _siteTypeRepository;

    public GetSiteNameQueryHandler(ISiteRepository siteRepository, IMapper mapper,
        ISiteTypeRepository siteTypeRepository)
    {
        _siteRepository = siteRepository;
        _mapper = mapper;
        _siteTypeRepository = siteTypeRepository;
    }

    public async Task<List<SiteNameVm>> Handle(GetSiteNameQuery request, CancellationToken cancellationToken)
    {
        var sites = await _siteRepository.GetSiteNames();

        var siteDto = _mapper.Map<List<SiteNameVm>>(sites);

        var siteTypeIds = siteDto
         .Select(site => site.TypeId)
         .Distinct()
         .ToList();

        var siteList = await _siteTypeRepository.GetSitesBySiteTypeId(siteTypeIds);
            siteDto = siteDto
            .Join(siteList,
                site => site.TypeId,
                s => s.ReferenceId,  
                (site, s) => new SiteNameVm
                {
                    Id = site.Id,
                    TypeId = site.TypeId,
                    Name = site.Name,
                    Category = s.Category 
                })
            .ToList();

        return siteDto;
    }
   
}