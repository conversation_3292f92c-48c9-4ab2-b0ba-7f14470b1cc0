﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowInfraObject.Events.Delete;

public class WorkflowInfraObjectDeletedEventHandler : INotificationHandler<WorkflowInfraObjectDeletedEvent>
{
    private readonly ILogger<WorkflowInfraObjectDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowInfraObjectDeletedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowInfraObjectDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowInfraObjectDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.DeAttach} {Modules.WorkflowInfraObject}",
            Entity = Modules.WorkflowInfraObject.ToString(),
            ActivityType = ActivityType.DeAttach.ToString(),
            ActivityDetails =
                $"Workflow {deletedEvent.WorkflowName} de-attached to infraobject {deletedEvent.InfraObjectName} for '{deletedEvent.ActionType}' successfully !"
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation(
            $"Workflow {deletedEvent.WorkflowName} de-attached to infraobject {deletedEvent.InfraObjectName} for '{deletedEvent.ActionType}' successfully !");
    }
}