using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.BulkImportOperation.Events.Create;

public class BulkImportOperationCreatedEventHandler : INotificationHandler<BulkImportOperationCreatedEvent>
{
    private readonly ILogger<BulkImportOperationCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public BulkImportOperationCreatedEventHandler(ILoggedInUserService userService,
        ILogger<BulkImportOperationCreatedEventHandler> logger,
        IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(BulkImportOperationCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} BulkImportOperation",
            Entity = "BulkImportOperation",
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"BulkImportOperation '{createdEvent.Name}' created successfully.",
            CreatedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId,
            LastModifiedBy = _userService.UserId.IsNullOrEmpty() ? Guid.NewGuid().ToString() : _userService.UserId
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"BulkImportOperation '{createdEvent.Name}' created successfully.");
    }
}