﻿using Microsoft.AspNetCore.Mvc;
using Moq;
using ContinuityPatrol.Web.Areas.Monitor.Controllers;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using Microsoft.Extensions.Logging;

namespace ContinuityPatrol.Web.UnitTests.Areas.Monitor.Controller;

public class DB2HADRLinuxControllerShould
{
    private readonly DB2HADRLinuxController _controller;
    private readonly Mock<IDashboardViewService> _mockDashboardViewService;
    private readonly Mock<ILogger<DB2HADRLinuxController>> _mockLogger;

    public DB2HADRLinuxControllerShould()
    {
        _mockDashboardViewService = new Mock<IDashboardViewService>();
        _mockLogger = new Mock<ILogger<DB2HADRLinuxController>>();
        _controller = new DB2HADRLinuxController(_mockDashboardViewService.Object, _mockLogger.Object);
    }

    [Fact]
    public void List_ReturnsViewResult()
    {
        // Act
        var result = _controller.List();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
            
    }

    [Fact]
    public async Task GetMonitorServiceStatusByIdAndType_ReturnsViewModel_OnSuccess()
    {
            
        var monitorId = "testMonitorId";
        var type = "testType";
        var expectedResult = new GetByEntityIdVm();
        _mockDashboardViewService
            .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
            .ReturnsAsync(expectedResult);

            
        var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

            
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task GetMonitorServiceStatusByIdAndType_ReturnsNull_OnException()
    {
            
        var monitorId = "testMonitorId";
        var type = "testType";
        _mockDashboardViewService
            .Setup(service => service.GetMonitorServiceStatusByIdAndType(monitorId, type))
            .ThrowsAsync(new Exception("Test exception"));

            
        var result = await _controller.GetMonitorServiceStatusByIdAndType(monitorId, type);

           
        Assert.Null(result);
            
    }
}