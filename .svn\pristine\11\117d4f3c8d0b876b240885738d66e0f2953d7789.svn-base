﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetWorkflowInfraObjectByInfraObjectId;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowInfraObject.Queries;

public class GetWorkflowInfraObjectByInfraObjectIdQueryHandlerTests : IClassFixture<WorkflowInfraObjectFixture>
{
    private readonly WorkflowInfraObjectFixture _workflowInfraObjectFixture;

    private Mock<IWorkflowInfraObjectRepository> _mockWorkflowInfraObjectRepository;

    private readonly GetWorkflowInfraObjectByInfraObjectIdQueryHandler _handler;

    public GetWorkflowInfraObjectByInfraObjectIdQueryHandlerTests(WorkflowInfraObjectFixture workflowInfraObjectFixture)
    {
        _workflowInfraObjectFixture = workflowInfraObjectFixture;

        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.GetWorkflowInfraObjectFromInfraObjectIdRepository(_workflowInfraObjectFixture.WorkflowInfraObjects);

        _handler = new GetWorkflowInfraObjectByInfraObjectIdQueryHandler(_workflowInfraObjectFixture.Mapper, _mockWorkflowInfraObjectRepository.Object);
    }

    [Fact]
    public async Task Handle_ReturnWorkflowInfraObject_When_ValidInfraObjectId()
    {
        var result = await _handler.Handle(new GetWorkflowInfraObjectByInfraObjectIdQuery { InfraObjectId = _workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId }, CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowInfraObjectByInfraObjectIdVm>>();
        result.Count.ShouldBe(3);
        result[0].WorkflowId.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId);
        result[0].WorkflowName.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowName);
        result[0].ActionType.ShouldBe(_workflowInfraObjectFixture.WorkflowInfraObjects[0].ActionType);
    }

    [Fact]
    public async Task Handle_ReturnEmptyList_When_NoRecords()
    {
        _mockWorkflowInfraObjectRepository = WorkflowInfraObjectRepositoryMocks.GetWorkflowInfraObjectEmptyRepository();

        var handler = new GetWorkflowInfraObjectByInfraObjectIdQueryHandler(_workflowInfraObjectFixture.Mapper, _mockWorkflowInfraObjectRepository.Object);

        var result = await handler.Handle(new GetWorkflowInfraObjectByInfraObjectIdQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_GetWorkflowInfraObjectNameByInfraObjectIdMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowInfraObjectByInfraObjectIdQuery { InfraObjectId = _workflowInfraObjectFixture.WorkflowInfraObjects[0].WorkflowId }, CancellationToken.None);

        _mockWorkflowInfraObjectRepository.Verify(x => x.GetWorkflowInfraObjectFromInfraObjectId(It.IsAny<string>()), Times.Once);
    }
}