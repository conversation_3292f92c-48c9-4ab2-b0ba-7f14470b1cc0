﻿namespace ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;

public class CreateGlobalSettingCommandValidator : AbstractValidator<CreateGlobalSettingCommand>
{
    public CreateGlobalSettingCommandValidator()
    {
        RuleFor(s => s).Must(IsValidGUID)
            .WithMessage("Invalid {PropertyName}");

        RuleFor(s => s.GlobalSettingKey)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Matches(@"^[a-zA-Z]+(?: [a-zA-Z]+)*$")
            .WithMessage("Enter valid {propertyName}");

        RuleFor(s => s.GlobalSettingValue)
            .NotEmpty().WithMessage("{PropertyName} is Required.")
            .NotNull()
            .Must(p => (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("true")) ||
                       (!string.IsNullOrWhiteSpace(p) && p.ToLower().Equals("false")))
            .WithMessage("Invalid {PropertyName}");
    }

    private bool IsValidGUID(CreateGlobalSettingCommand p)
    {
        Guard.Against.InvalidGuidOrEmpty(p.LoginUserId, "LoginUser Id");
        return true;
    }
}