using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Features.User.Commands.UserUnLock;
using ContinuityPatrol.Application.Features.User.Queries.GetDetail;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;
using ContinuityPatrol.Application.Features.UserInfo.Commands.Update;
using ContinuityPatrol.Application.Features.UserInfo.Queries.GetDetail;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserId;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class UserFixture
{
    public List<UserListVm> UserListVm { get; }
    public UserDetailVm UserDetailVm { get; }
    public CreateUserCommand CreateUserCommand { get; }
    public CreateDefaultUserCommand CreateDefaultUserCommand { get; }
    public UpdateUserCommand UpdateUserCommand { get; }
    public ForgotPasswordCommand ForgotPasswordCommand { get; }
    public ResetPasswordCommand ResetPasswordCommand { get; }
    public UpdatePasswordCommand UpdatePasswordCommand { get; }
    public UserLockCommand UserLockCommand { get; }
    public UserUnLockCommand UserUnLockCommand { get; }
    public GetUserPaginatedListQuery GetUserPaginatedListQuery { get; }
    public PaginatedResult<UserViewListVm> PaginatedUserViewListVm { get; }
    public GetUserInfraObjectByUserIdVm GetUserInfraObjectByUserIdVm { get; }
    public UserLoginNameVm UserLoginNameVm { get; }
    public List<UsersByUserRoleVm> UsersByUserRoleVm { get; }
    public List<UserNameVm> UserNameVm { get; }
    public List<string> DomainList { get; }
    public List<string> DomainUsers { get; }
    public List<string> DomainGroups { get; }
    public UserProfileDetailVm UserProfileDetailVm { get; }
    public UserInfoDetailVm UserInfoDetailVm { get; }
    public UpdateUserInfoCommand UpdateUserInfoCommand { get; }

    public UserFixture()
    {
        var fixture = new Fixture();

        UserListVm = fixture.Create<List<UserListVm>>();
        UserDetailVm = fixture.Create<UserDetailVm>();
        CreateUserCommand = fixture.Create<CreateUserCommand>();
        CreateDefaultUserCommand = fixture.Create<CreateDefaultUserCommand>();
        UpdateUserCommand = fixture.Create<UpdateUserCommand>();
        ForgotPasswordCommand = fixture.Create<ForgotPasswordCommand>();
        ResetPasswordCommand = fixture.Create<ResetPasswordCommand>();
        UpdatePasswordCommand = fixture.Create<UpdatePasswordCommand>();
        UserLockCommand = fixture.Create<UserLockCommand>();
        UserUnLockCommand = fixture.Create<UserUnLockCommand>();
        GetUserPaginatedListQuery = fixture.Create<GetUserPaginatedListQuery>();
        PaginatedUserViewListVm = fixture.Create<PaginatedResult<UserViewListVm>>();
        GetUserInfraObjectByUserIdVm = fixture.Create<GetUserInfraObjectByUserIdVm>();
        UserLoginNameVm = fixture.Create<UserLoginNameVm>();
        UsersByUserRoleVm = fixture.Create<List<UsersByUserRoleVm>>();
        UserNameVm = fixture.Create<List<UserNameVm>>();
        DomainList = fixture.Create<List<string>>();
        DomainUsers = fixture.Create<List<string>>();
        DomainGroups = fixture.Create<List<string>>();
        UserProfileDetailVm = fixture.Create<UserProfileDetailVm>();
        UserInfoDetailVm = fixture.Create<UserInfoDetailVm>();
        UpdateUserInfoCommand = fixture.Create<UpdateUserInfoCommand>();
    }

    public void Dispose()
    {

    }
}
