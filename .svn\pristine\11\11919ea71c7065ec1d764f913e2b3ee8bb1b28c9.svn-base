﻿using ContinuityPatrol.Application.Helper;

namespace ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;

public class GetSlaBreachListQueryHandler : IRequestHandler<GetSlaBreachListQuery, GetSlaBreachListVm>
{
    private readonly IAlertRepository _alertRepository;
    private readonly IDashboardViewLogRepository _dashboardViewLogRepository;

    public GetSlaBreachListQueryHandler(IDashboardViewLogRepository dashboardViewLogRepository,
        IAlertRepository alertRepository)
    {
        _dashboardViewLogRepository = dashboardViewLogRepository;
        _alertRepository = alertRepository;
    }

    public async Task<GetSlaBreachListVm> Handle(GetSlaBreachListQuery request, CancellationToken cancellationToken)
    {
        var businessServiceSlaBreach = new GetSlaBreachListVm();

        var dashboardViewLogs = await _dashboardViewLogRepository.GetDashboardViewLogByLast30daysList();

        var alertList = _alertRepository.GetPaginatedQuery();

        businessServiceSlaBreach.ActiveAlertCount = alertList.Count();

        // Calculate SLA breach counts
        foreach (var dashboardViewLog in dashboardViewLogs)
        {
            if (dashboardViewLog.CurrentRPO.IsNotNullOrWhiteSpace() && dashboardViewLog.CurrentRPO is not "NA")
            {
                var currentRpoSec = GetJsonProperties.ConvertToSeconds(dashboardViewLog.CurrentRPO);
                var configuredRpoSec = TimeSpan.Parse(dashboardViewLog.ConfiguredRPO).TotalSeconds;

                if (currentRpoSec > configuredRpoSec)
                    businessServiceSlaBreach.SlaBreachCount += 1;
                else
                    businessServiceSlaBreach.SlaNonBreachCount += 1;
            }

            if (dashboardViewLog.CurrentRTO.IsNotNullOrWhiteSpace() && dashboardViewLog.CurrentRTO != "NA")
            {
                var currentRtoSec = GetJsonProperties.ConvertToSeconds(dashboardViewLog.CurrentRTO);
                var configuredRtoSec = TimeSpan.Parse(dashboardViewLog.ConfiguredRTO).TotalSeconds;

                if (currentRtoSec <= configuredRtoSec)
                    businessServiceSlaBreach.SlaMeetingRtoCount += 1;
            }
        }

        // Group SLA impact data
        for (var i = 0; i < 30; i++)
        {
            var currentDate = DateTime.Now.Date.AddDays(-i);

            var recordsForCurrentDate = dashboardViewLogs
                .Where(item => item.LastModifiedDate.Date == currentDate)
                .ToList();

            var breachedCount = recordsForCurrentDate
                .Where(r => r.CurrentRPO.IsNotNullOrWhiteSpace() && r.CurrentRPO.Trim().ToLower() != "na")
                .Count(r => GetJsonProperties.ConvertToSeconds(r.CurrentRPO) >
                            TimeSpan.Parse(r.ConfiguredRPO).TotalSeconds);

            var nonBreachedCount = recordsForCurrentDate.Count - breachedCount;

            businessServiceSlaBreach.SlaImpactList.Add(new GetSlaImpactListVm
            {
                Date = currentDate,
                ImpactCount = breachedCount,
                NonImpactCount = nonBreachedCount
            });
        }

        return businessServiceSlaBreach;
    }
}