﻿using ContinuityPatrol.Web.Areas.Admin.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Admin.Controller
{
    public class ServerRoleControllerShould
    {
        private readonly ServerRoleController _controller;

        public ServerRoleControllerShould()
        {
            _controller = new ServerRoleController();
        }

        // ===== BASIC FUNCTIONALITY TESTS =====

        [Fact]
        public void List_ReturnsViewResult()
        {
            // Act
            var result = _controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void List_ReturnsViewResultWithExpectedProperties()
        {
            // Act
            var result = _controller.List() as ViewResult;

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.ViewName); // Should use default view name
            Assert.Null(result.Model); // Should have no model
            Assert.Null(result.ViewData.Model); // ViewData model should also be null
        }

        // ===== CONTROLLER SETUP TESTS =====

        [Fact]
        public void List_WorksWithMinimalControllerSetup()
        {
            // Arrange
            var controller = new ServerRoleController();

            // Act
            var result = controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        [Fact]
        public void List_WorksWithFullControllerSetup()
        {
            // Arrange
            var controller = new ServerRoleController();
            controller.ControllerContext.HttpContext = new DefaultHttpContext();

            // Act
            var result = controller.List();

            // Assert
            Assert.NotNull(result);
            Assert.IsType<ViewResult>(result);
        }

        // ===== CONTROLLER ATTRIBUTES AND METADATA TESTS =====

        [Fact]
        public void Controller_ShouldHaveAreaAttribute()
        {
            // Arrange
            var controllerType = typeof(ServerRoleController);

            // Act
            var areaAttribute = controllerType.GetCustomAttributes(typeof(AreaAttribute), false)
                .Cast<AreaAttribute>()
                .FirstOrDefault();

            // Assert
            Assert.NotNull(areaAttribute);
            Assert.Equal("Admin", areaAttribute.RouteValue);
        }

        [Fact]
        public void Controller_ShouldImplementControllerBase()
        {
            // Act
            var controller = new ServerRoleController();

            // Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Mvc.Controller>(controller);
        }

        [Fact]
        public void List_Method_ShouldBePublic()
        {
            // Arrange
            var controllerType = typeof(ServerRoleController);

            // Act
            var listMethod = controllerType.GetMethod("List");

            // Assert
            Assert.NotNull(listMethod);
            Assert.True(listMethod.IsPublic);
            Assert.Equal(typeof(IActionResult), listMethod.ReturnType);
        }

        // ===== EDGE CASE TESTS =====

        [Fact]
        public void List_ShouldReturnSameResultOnMultipleCalls()
        {
            // Act
            var result1 = _controller.List();
            var result2 = _controller.List();

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.IsType<ViewResult>(result1);
            Assert.IsType<ViewResult>(result2);
            // Both should be ViewResult instances (though different objects)
            Assert.NotSame(result1, result2);
        }

        [Fact]
        public void List_ShouldNotThrowException()
        {
            // Act & Assert
            var exception = Record.Exception(() => _controller.List());
            Assert.Null(exception);
        }

        // ===== CONSTRUCTOR TESTS =====

        [Fact]
        public void Constructor_ShouldCreateInstanceSuccessfully()
        {
            // Act
            var controller = new ServerRoleController();

            // Assert
            Assert.NotNull(controller);
            Assert.IsType<ServerRoleController>(controller);
        }

        [Fact]
        public void Constructor_ShouldNotRequireParameters()
        {
            // Act & Assert - Should not throw
            var exception = Record.Exception(() => new ServerRoleController());
            Assert.Null(exception);
        }
    }
}
