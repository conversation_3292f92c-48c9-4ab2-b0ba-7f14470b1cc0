﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Event.Delete;
using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Delete;

public class DeleteInfraReplicationMappingCommandHandler : IRequestHandler<DeleteInfraReplicationMappingCommand,
    DeleteInfraReplicationMappingResponse>
{
    private readonly IInfraReplicationMappingRepository _infraReplicationMappingRepository;
    private readonly IPublisher _publisher;
    private readonly IInfraObjectRepository _infraObjectRepository;


    public DeleteInfraReplicationMappingCommandHandler(
        IInfraReplicationMappingRepository infraReplicationMappingRepository, IPublisher publisher, IInfraObjectRepository infraObjectRepository)
    {
        _infraReplicationMappingRepository = infraReplicationMappingRepository;
        _publisher = publisher;
        _infraObjectRepository = infraObjectRepository;
    }

    public async Task<DeleteInfraReplicationMappingResponse> Handle(DeleteInfraReplicationMappingCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _infraReplicationMappingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.InfraReplicationMapping),
            new NotFoundException(nameof(Domain.Entities.InfraReplicationMapping), request.Id));

        var objects = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(eventToDelete.Properties);
        var ids = objects.Select(o => o["id"]).ToList();

        var infraObjects = await _infraObjectRepository.GetByReplicationTypeId(ids);

        var infraObjectVm = await _infraObjectRepository.GetByReplicationCategoryTypeId(eventToDelete.ReplicationMasterId);

        if (infraObjects.Any() || infraObjectVm.Any())
        {
            throw new InvalidException("The Infra-Replication Mapping is currently in use.");
        }

        eventToDelete.IsActive = false;

        await _infraReplicationMappingRepository.UpdateAsync(eventToDelete);

        var response = new DeleteInfraReplicationMappingResponse
        {
            Message = Message.Delete("Infra-Replication Mapping", 
                eventToDelete.Type == "Database" ? eventToDelete.DatabaseName : eventToDelete.ReplicationMasterName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new InfraReplicationMappingDeletedEvent { Name = eventToDelete.Type == "Database" ? eventToDelete.DatabaseName : eventToDelete.ReplicationMasterName }, cancellationToken);

        return response;
    }
}