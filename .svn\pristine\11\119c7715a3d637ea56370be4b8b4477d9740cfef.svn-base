﻿using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Create;
using ContinuityPatrol.Application.Features.LicenseHistory.Commands.Update;
using ContinuityPatrol.Application.Features.LicenseHistory.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.LicenseHistoryModel;

namespace ContinuityPatrol.Application.Mappings;

public class LicenseHistoryProfile : Profile
{
    public LicenseHistoryProfile()
    {
        CreateMap<LicenseHistory, CreateLicenseHistoryCommand>().ReverseMap();
        CreateMap<UpdateLicenseHistoryCommand, LicenseHistory>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<LicenseHistory, LicenseHistoryListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<LicenseHistory, LicenseHistoryDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}