﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowExecutionEventLogRepository : BaseRepository<WorkflowExecutionEventLog>,
    IWorkflowExecutionEventLogRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowExecutionEventLogRepository(ApplicationDbContext dbContext,
        ILoggedInUserService loggedInUserService) : base(dbContext)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }
}