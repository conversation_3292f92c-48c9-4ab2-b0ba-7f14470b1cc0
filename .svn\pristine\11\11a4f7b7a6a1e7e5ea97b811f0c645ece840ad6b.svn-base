﻿using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class SolutionHistoryService : BaseClient, ISolutionHistoryService
{
    public SolutionHistoryService(IConfiguration config, IAppCache cache, ILogger<SolutionHistoryService> logger) : base(config, cache, logger)
    {
    }

    public Task<List<SolutionHistoryByActionIdQueryVm>> GetSolutionHistoryByActionId(string actionId)
    {
        throw new NotImplementedException();
    }
}
