﻿using ContinuityPatrol.Application.Features.FormTypeCategory.Commands.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.FormTypeCategory.Commands;

public class UpdateFormTypeCategoryTests : IClassFixture<FormTypeCategoryFixture>
{
    private readonly FormTypeCategoryFixture _formTypeCategoryFixture;
    private readonly Mock<IFormTypeCategoryRepository> _mockFormTypeCategoryRepository;
    private readonly UpdateFormTypeCategoryCommandHandler _handler;

    public UpdateFormTypeCategoryTests(FormTypeCategoryFixture formTypeCategoryFixture)
    {
        _formTypeCategoryFixture = formTypeCategoryFixture;

        var publisher = new Mock<IPublisher>();

        _mockFormTypeCategoryRepository = FormTypeCategoryRepositoryMocks.UpdateFormTypeCategoryRepository(_formTypeCategoryFixture.FormTypeCategories);

        _handler = new UpdateFormTypeCategoryCommandHandler(_mockFormTypeCategoryRepository.Object,_formTypeCategoryFixture.Mapper, publisher.Object);
    }

    [Fact]
    public async Task Handle_ValidFormTypeCategory_UpdateToFormTypeCategoriesRepo()
    {
        _formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Id = _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId;

        var result = await _handler.Handle(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand, CancellationToken.None);

        var formTypeCategory = await _mockFormTypeCategoryRepository.Object.GetByReferenceIdAsync(result.Id);

        Assert.Equal(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Name, formTypeCategory.Name);
    }

    [Fact]
    public async Task Handle_Return_UpdateFormTypeCategoryResponse_When_FormTypeCategoryUpdated()
    {
        _formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Id = _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId;

        var result = await _handler.Handle(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand, CancellationToken.None);

        result.ShouldBeOfType(typeof(UpdateFormTypeCategoryResponse));

        result.Id.ShouldBeGreaterThan(0.ToString());

        result.Id.ShouldBe(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Id);

        result.Message.ShouldContain(CommonConstants.UpdatedSuccessfully);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidFormTypeCategoryId()
    {
        _formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Id = int.MaxValue.ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {

        _formTypeCategoryFixture.UpdateFormTypeCategoryCommand.Id = _formTypeCategoryFixture.FormTypeCategories[0].ReferenceId;

        await _handler.Handle(_formTypeCategoryFixture.UpdateFormTypeCategoryCommand, CancellationToken.None);

        _mockFormTypeCategoryRepository.Verify(x => x.GetByReferenceIdAsync(It.IsAny<string>()), Times.Once);

        _mockFormTypeCategoryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.FormTypeCategory>()), Times.Once);
    }
}