﻿using ContinuityPatrol.Application.Features.WorkflowActionResult.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.WorkflowActionResultModel;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowActionResult.Queries;

public class GetWorkflowActionResultListQueryHandlerTests : IClassFixture<WorkflowActionResultFixture>
{
    private readonly WorkflowActionResultFixture _workflowActionResultFixture;

    private Mock<IWorkflowActionResultRepository> _mockWorkflowActionResultRepository;

    private readonly GetWorkflowActionResultListQueryHandler _handler;

    public GetWorkflowActionResultListQueryHandlerTests(WorkflowActionResultFixture workflowActionResultFixture)
    {
        _workflowActionResultFixture = workflowActionResultFixture;

        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultRepository(_workflowActionResultFixture.WorkflowActionResults);

        _handler = new GetWorkflowActionResultListQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_Active_WorkflowActionResultsCount()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionResultListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Call_GetAllMethod_OneTime()
    {
        await _handler.Handle(new GetWorkflowActionResultListQuery(), CancellationToken.None);

        _mockWorkflowActionResultRepository.Verify(x => x.ListAllAsync(), Times.Once());
    }

    [Fact]
    public async Task Handle_Return_Valid_WorkflowActionResultDetail()
    {
        var result = await _handler.Handle(new GetWorkflowActionResultListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<WorkflowActionResultListVm>>();

        result[0].Id.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ReferenceId);
        result[0].WorkflowActionName.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowActionName);
        result[0].Status.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Status);
        result[0].Message.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Message);
        result[0].WorkflowOperationId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationId);
        result[0].WorkflowOperationGroupId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].WorkflowOperationGroupId);
        result[0].InfraObjectId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].InfraObjectId);
        result[0].ActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ActionId);
        result[0].ConditionActionId.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].ConditionActionId);
        result[0].SkipStep.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].SkipStep);
        result[0].IsReload.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].IsReload);
        result[0].Direction.ShouldBe(_workflowActionResultFixture.WorkflowActionResults[0].Direction);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockWorkflowActionResultRepository = WorkflowActionResultRepositoryMocks.GetWorkflowActionResultEmptyRepository();

        var handler = new GetWorkflowActionResultListQueryHandler(_workflowActionResultFixture.Mapper, _mockWorkflowActionResultRepository.Object);

        var result = await handler.Handle(new GetWorkflowActionResultListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }
}