﻿@{
    ViewData["Title"] = "List";
    Layout = "~/Views/Shared/_MasterLayout.cshtml";
}

<style>
    .date-picker-container {
        position: relative;
        display: inline-block;
    }

    .input-group-text {
        margin-top: 4px;
    }

   .disables {
        pointer-events: none;
        opacity: 0.5;
    }

    .refresh-icon {
        display: inline-block;
        transform: rotate(66deg);
        position: relative;
        border-color: transparent rgb(96, 96, 96);
        border-radius: 50%;
        border-style: solid;
        border-width: .125em;
        height: 1.1em;
        margin: .25em;
        width: 1.1em;
    }

        .refresh-icon::before,
        .refresh-icon::after {
            border-style: solid;
            content: '';
            display: block;
            position: absolute;
            width: 0;
            transform: rotate(-45deg);
        }

        .refresh-icon::after {
            border-color: transparent transparent transparent rgb(96, 96, 96);
            border-width: .3125em 0 .3125em .5em;
            top: -.3125em;
            left: .0625em;
        }

        .refresh-icon::before {
            border-color: transparent rgb(96, 96, 96) transparent transparent;
            border-width: .3125em .5em .3125em 0;
            bottom: -.3125em;
            right: .0625em;
        }

    #customButton:hover .refresh-icon {
        border-color: transparent rgb(33,111,177);
    }

    .refresh-icon:hover::before {
        border-color: transparent rgb(33,111,177) transparent transparent;
    }

    .refresh-icon:hover::after {
        border-color: transparent transparent transparent, rgb(33,111,177);
    }
    .refresh-icon:hover::before {
        border-color: transparent rgb(33,111,177) transparent transparent;
    }

    .refresh-icon:hover::after {
        border-color: transparent transparent transparent rgb(33,111,177);
    }  
</style>

<link href="~/css/thirdparty.bundle.css" rel="stylesheet" />
<link href="~/css/viewer.part.bundle.css" rel="stylesheet">
<link href="~/css/report.css" rel="stylesheet" />

<div class="page-content">
    <div class="card Card_Design_None">
        <div class="card-header header">
            <h6 class="page_title"><i class="cp-prebuild-reports"></i><span></span><span id="ReportsName"></span></h6>
            <form class="d-flex gap-3">
                <div id="navigationID" style="display : none">
                    <div class="navbar navbar-expand-lg bg-white">
                        <ul class="navbar-nav d-flex nav-underline">
                            <li class="nav-item">
                                <a id="todayId" class="nav-link" href="#">Today</a>
                            </li>
                            <li class="nav-item">
                                <a Id="yesterdayId" class="nav-link" href="#">Yesterday</a>
                            </li>
                            <li class="nav-item">
                                <a Id="customId" class="nav-link" href="#">Custom</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div id="headExecutionMode" class="form-group-lg" style="height: 30px">
                    <div id="executionMode" class="input-group" style="width:150px ; display : none">
                        <select id="executionModeId" class="form-select" data-placeholder="Select Execution Mode" title="Select Execution Mode" maxlength="30">
                            <option value="" disabled selected>Select Execution Mode</option>
                            <option value="Dry Run">Dry Run</option>
                            <option value="Execution">Execution</option>
                        </select>
                    </div>
                    <span id="errorExecutionMode" style="color:red; background-color:transparent; border: 0; display:none" class="input-group w-auto"></span>
                </div>
                <div id="ReportType" class="input-group" style="display:none">
                    <span class="input-group-text form-label mb-0 pe-0">Report Type</span>
                    <span class="input-group-text"><i class="cp-note-pad"></i></span>
                    <select class="form-select" data-placeholder="Select report type">
                        <option></option>
                        <option value="1">Critical</option>
                        <option value="2">High</option>
                        <option value="3">Medium</option>
                        <option value="4">Low</option>
                    </select>
                </div>
                <div id="headDateFormat" class="form-group-lg" style="height: 30px">
                    <div id="dateFormat" class="input-group" style="width:220px; display : none">
                        <select id="dateOption" class="form-select" data-placeholder="Select Date Format">
                            <option value="" disabled selected>Select Date Format</option>
                            <option value="Daily">Daily</option>
                            <option value="Weekly">Weekly</option>
                            <option value="Monthly">Monthly</option>
                        </select>
                    </div>
                    <span id="errordateOption" style="color:red; background-color:transparent; border: 0; margin-left:8px; display:none" class="input-group">
                    </span>
                </div>
                <div id="headRpostartDate" class="form-group-lg date-picker-container" style="height: 30px">
                    <div id="rpostartDate" class="input-group w-auto" style="display : none">
                        <span class="input-group-text form-label mb-0 pe-1" id="spanStartDate">Select Date</span>
                        <input id="rpoclndrStart" type="date" placeholder="Select Date" style="border:none;margin-left:5px;" min="2011-01-01" onkeydown="return false" />
                    </div>

                    <span id="rpoerrorStart" style="color:red; background-color:transparent; border: 0; display:none" class="input-group w-auto">

                    </span>
                </div>
                <div id="headStartDate" class="form-group-lg date-picker-container" style="height: 30px">
                    <div id="startDate" class="input-group w-auto " style="display : none">
                        <span class="input-group-text form-label mb-0 pe-1">Start Date</span>
                        <input id="clndrStart" type="date" placeholder="Select Date" style="border:none;margin-left:5px;" min="2011-01-01" onkeydown="return false" />
                    </div>

                    <span id="errorStart" style="color:red; background-color:transparent; border: 0; display:none" class="input-group w-auto">

                    </span>
                </div>
                <div id="headEndDate" class="form-group-lg date-picker-container" style="height: 30px">
                    <div id="endDate" class="input-group w-auto" style="display : none">
                        <span class="input-group-text form-label mb-0 pe-1">End Date</span>
                        <input id="clndrEnd" type="date" placeholder="Select Date" style="border:none;margin-left:5px;" min="2011-01-01" onkeydown="return false" />
                    </div>

                    <span id="errorEnd" style="color:red; background-color:transparent; border: 0; display:none" class="input-group w-auto">

                    </span>
                </div>
                <div id="headWorkflowendDate" class="form-group-lg date-picker-container" style="height: 30px">
                    <div id="workflowendDate" class="input-group w-auto" style="display : none">
                        <span class="input-group-text form-label mb-0 pe-1">End Date</span>
                        <input id="workflowEnd" type="date" placeholder="Select Date" style="border:none;margin-left:5px;" min="2011-01-01" onkeydown="return false" />
                    </div>
                    <div id="errorEndWF" style="color:red; background-color:transparent; border: 0; display:none" class="input-group w-auto">
                        <span id="spnWorkflow"></span>
                    </div>
                </div>
                <div id="UserDetails" class="input-group" style="width:220px ; display : none">
                    <select id="AllUsers" class="form-select" data-placeholder="Select User Name" maxlength="30">
                        <option value="" disabled selected>Select User Name</option>
                        <option value="All">All</option>
                    </select>
                </div>
                <div id="BusinessID" class="input-group" style="width:220px ; display : none">
                    <select id="BusinessServiceID" class="form-select" data-placeholder="Select Operational Service Name" maxlength="30">
                        <option value="" selected>Select Operational Service Name</option>
                        <option value="All">All</option>
                    </select>
                    <span id="errorBusinessID" style="color: red;background-color: transparent;border: 0px;top: 36px;margin-left: -212px; display:none" class="input-group w-auto"></span>
                </div>
                <div id="drReadyBusinessId" class="input-group" style="width:220px ; display : none">
                    <select id="drReadyBusinessServiceId" class="form-select" data-placeholder="Select Operational Service Name" maxlength="30">
                        <option value="" selected>Select Operational Service Name</option>
                        <option value="All">All</option>
                    </select>
                </div>
                <div id="licenseFormat" class="input-group" style="width:220px; display : none">
                    <select id="licenseOption" class="form-select" data-placeholder="Select Date Format">
                        <option value="Operational Service" selected>Operational Service</option>
                        <option value="PO Number">PO Number</option>
                    </select>
                </div>
                <div id="headBusinessServiceLicense" class="form-group-lg" style="height: 30px">
                    <div id="BusinessServiceLicense" class="input-group" style="width:220px ; display : none">
                        <select id="BusinessServiceIDLicense" class="form-select text-truncate" style="overflow-y: auto;" data-placeholder="Select Operational Service Name" multiple>
                            <option value="All">All</option>
                        </select>
                    </div>
                    <span id="errorBLicense" style="color:red; background-color:transparent; border: 0; margin-left:8px; display:none" class="input-group">
                    </span>
                </div>
                <div id="headLicenceID" class="form-group-lg" style="height: 30px">
                    <div id="LicenceID" class="input-group" style="width:220px ; display : none">
                        <select id="ddlLicenceID" class="form-select" data-placeholder="Select License PO Number" maxlength="30" multiple>
                            <option value="All">All</option>
                        </select>
                    </div>
                    <span id="errorLicensepo" style="color:red; background-color:transparent; border: 0; margin-left:8px; display:none" class="input-group">
                    </span>
                </div>
                <div id="headLicenseDerived" class="form-group-lg" style="height: 30px">
                    <div id="LicenseDerived" class="input-group" style="width:220px ; display : none">
                        <select id="ddlDerivedID" class="form-select" data-placeholder="Select Derived PO Number (Optional)" multiple>
                            <option value="All">All</option>
                        </select>
                    </div>
                    <span id="errorLicensechildpo" style="color:red; background-color:transparent; border: 0;display:none" class="input-group">
                    </span>
                </div>
                <div id="InfraDgn" class="input-group" style="width:220px ; display : none">
                    <select id="Infraobject" class="form-select" data-placeholder="Select InfraObject Name" maxlength="30">
                        <option value="" disabled selected>Select InfraObject Name</option>
                    </select>
                </div>
                <div id="InfraDrift" class="input-group" style="width:220px ; display : none">
                    <select id="InfraobjectDriftId" class="form-select" data-placeholder="Select InfraObject Name" maxlength="30">
                        <option value="" disabled selected>Select InfraObject Name</option>
                        <option value="all">All</option>
                    </select>
                </div>
                <div id="DriftStatus" class="input-group" style="width:220px ; display : none">
                    <select id="StatusId" class="form-select" data-placeholder="Select Status " maxlength="30">
                        <option value="" disabled selected>Select Status </option>
                        <option value="all">All</option>
                    </select>
                </div>
                <div id="AirGap" class="input-group" style="width:220px ; display : none">
                    <select id="airGapList" class="form-select" data-placeholder="Select AirGap Name" maxlength="30">
                        <option value="" disabled selected>Select AirGap Name</option>
                        <option value="all">All</option>
                    </select>
                </div>

                <div id="SnapsDropDown" class="input-group" style="width:220px ; display : none">
                    <select id="SnapsDropDownId" class="form-select" data-placeholder="Select SnapTag Name" maxlength="30">
                        <option value="" disabled selected>Select SnapTag Name</option>
                        <option value="all">All</option>
                    </select>
                </div>
                <div id="customizedContainer" class="input-group" style="width:100px ; display : none">
                    <select class="form-select">
                        <option>Default</option>
                        <option>Customizable</option>
                    </select>
                </div>
                <div id="Workflow" class="input-group" style="width:200px ; display : none">
                    @* <span class="input-group-text form-label mb-0 pe-0">Workflow</span> *@
                    <span class="input-group-text"><i class="cp-workflow-profile"></i></span>
                    <select id="workflowid" class="form-select" data-placeholder="Select Workflow Profile Name">
                        <option value="" disabled selected>Select Workflow Name</option>
                    </select>
                </div>
                <div id="InfraDgnRPOSLADeviation" class="input-group" style="width:160px ; display : none">
                    <select id="InfraobjectDeviation" class="form-select" data-placeholder="Select InfraObject Name" maxlength="30">
                        <option value="" disabled selected>Select InfraObject Name</option>
                    </select>
                </div>
                <div id="licensesubmit" style="display:none;">
                    <button type="button" class="btn btn-primary btn-sm" title="" id="LicenseButtonID">Submit</button>
                    <button type="button" class="btn btn-secondary btn-sm" title="" id="LicenseCancelId">Cancel</button>
                </div>
                <div id="bulkImportDropdown" class="input-group" style="width:160px ; display : none">
                    <select id="bulkImportId" class="form-select" data-placeholder="Select Bulk Import Name" maxlength="30">
                        <option value="" disabled selected>Select Bulk Import Name</option>
                        <option value="all">All</option>
                    </select>
                </div>
            </form>
        </div>


        <div class="card-body pt-0">
            <div class="row">
                <div class="col-2">
                    <div id="rptdropdown" class="dropdown">
                        <select id="reportDropdown" class="form-select" style="width: 100%; margin-top: 10px;">
                            <option value="">All</option>
                            @*  <option value="">Select a report</option> *@
                            @* <option value="CMDBImportSummary">CMDB Import Summary</option> *@
                            <option value="AirGapReport">AirGap Report</option>
                            <option value="BulkImportReport">Bulk Import Report</option>
                            <option value="CyberResiliencyScheduleLogReport">Cyber Resiliency Schedule Log Report</option>
                            <option value="DataLagStatusReport">DataLag Status Report</option>
                            <option value="DrillSummary">DR Drill Summary Report</option>
                            <option value="DriftReport">Drift Report</option>
                            <option value="InfraObject Configuration Report">InfraObject Configuration Report</option>
                            <option value="InfraObjectSummaryReport">InfraObject Summary Report</option>
                            <option value="License Utilization Report">License Utilization Report</option>
                            <option value="Operational Service Summary">Operational Service Summary Report</option>
                            <option value="Resiliency Readiness Report">Resiliency Readiness Report</option>
                            <option value="Resiliency Readiness Execution Report">Resiliency Readiness Execution Report</option>
                            <option value="ResiliencyReadinessSchedulerLogReport">Resiliency Readiness Scheduler Log Report</option>
                            <option value="RPOSLADeviationReport">RPO SLA Deviation Report</option>
                            <option value="RPOSLAReport">RPO SLA Report</option>
                            <option value="RTOReport">RTO Report</option>
                            <option value="SnapReport">Snap Report</option>
                            <option value="UserAuditReport">User Audit Report</option>
                        </select>
                    </div>
                    <div class="form-group d-none">
                        <div class="input-group">
                            <input type="text" placeholder="Search" class="form-control" />
                            <span class="input-group-text"><i class="cp-search"></i></span>
                        </div>
                    </div>
                    <div class="list-group1 rounded-0" style="height: calc(100vh - 185px); overflow-y: auto;">
                        <a href="#" title="AirGap Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="AirGapReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/AirGapReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    AirGap Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="Bulk Import Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="BulkImportReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/BulkImport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Bulk Import Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="CyberResiliencyScheduleLogReport" class="list-group1-item-action load-partial" aria-current="true" data-report-name="CyberResiliencyScheduleLogReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/CyberResiliencySchedulerLogReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Cyber Resiliency Schedule Log Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="DataLag Status Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="DataLagStatusReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/DataLagStatusReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    DataLag Status Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="DR Drill Summary Report" class="list-group1-item-action load-partial" data-report-name="DRDrillReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/dr_drill_summary.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    DR Drill Summary Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="Drift Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="DriftReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/DriftReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Drift Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="InfraObject Configuration Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="InfraObjectConfigurationReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/InfraObjectCongfigurationReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    InfraObject Configuration Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="InfraObject Summary Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="InfraObjectSummaryReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/InfraObjectSummaryReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    InfraObject Summary Report
                                </div>
                            </div>
                        </a>

                        <a href="#" title="License Utilization Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="LicenseUtilizationReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/DataBaseLicenseReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    License Utilization Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="Operational Service Summary Report" class="list-group1-item-action load-partial" data-report-name="BusinessServiceSummaryReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/BusinessServiceSummaryReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Operational Service Summary Report
                                </div>
                            </div>
                        </a>

                        <a href="#" title="Resiliency Readiness Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="DRReadyReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/DRReadyReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Resiliency Readiness Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="Resiliency Readiness Execution Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="DRReadinessLog">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/DRReadinessLog_dev_file.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Resiliency Readiness Execution Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="ResiliencyReadinessSchedulerLogReport" class="list-group1-item-action load-partial" aria-current="true" data-report-name="ResiliencyReadinessSchedulerLogReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/InfraObjectSchedulerLogReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Resiliency Readiness Scheduler Log Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="RPO SLA Deviation Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="RPOSLADeviationReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/RPO_SLA_Deviation_report.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    RPO SLA Deviation Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="RPO SLA Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="RPOSLAReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/RPOSLAReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    RPO SLA Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="RTO Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="RTOReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/RTOReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    RTO Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="Snap Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="SnapReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/CyberSnapReport.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    Snap Report
                                </div>
                            </div>
                        </a>
                        <a href="#" title="User Audit Report" class="list-group1-item-action load-partial" aria-current="true" data-report-name="UserActivityReport">
                            <div class="card text-center mb-0">
                                <div class="card-body">
                                    <img src="../../img/reports/user_activity_report.png" class="w-100" />
                                </div>
                                <div class="card-footer">
                                    User Audit Report
                                </div>
                            </div>
                        </a>

                    </div>
                </div>
                <div class="col-10 Report-Scroll">
                    <div id="Loader" class="justify-content-center align-items-center  h-100 gap-2" hidden>
                        <div class="justify-content-center align-items-center d-flex h-100 gap-2">
                            <div class="spinner-grow text-primary" role="status" style="width:10px; height:10px;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="spinner-grow text-primary" role="status" style="width:10px; height:10px;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="spinner-grow text-primary" role="status" style="width:10px; height:10px;">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                    @* Tools *@
                    <div class="row position-sticky top-0 z-1">

                    </div>
                    @* Logo *@
                    @* report partial view section start *@
                    <div id="btndrdrill"></div>
                    @* report partial view section end *@
                    <div id="EmptyAlert" class="content-center w-100" hidden>
                        <div class="container">
                            <div class="row text-center">
                                <div class="col-lg-12 my-2">

                                    <svg class="mt-5" width="400" height="380" viewBox="0 0 200 133" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2_9098)"><mask id="mask0_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M0 0.984863H200V132.235H0V0.984863Z" fill="white" /></mask><g mask="url(#mask0_2_9098)"><mask id="mask1_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white" /></mask><g mask="url(#mask1_2_9098)"><mask id="mask2_2_9098" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="133"><path d="M200 0.984863H0V132.235H200V0.984863Z" fill="white" /></mask><g mask="url(#mask2_2_9098)"><path d="M186.1 28.5871L183.309 42.1195L181.513 40.3649L178.459 42.5479L176.124 39.6428L170.888 43.4553L166.414 35.961L161.422 38.8888L159.443 36.157L153.105 39.7337L151.2 35.6428L155.642 14.0928L179.458 19.0189L186.1 28.5871Z" fill="#EBEBEB" /><path d="M179.457 19.02L186.099 28.5882L177.837 26.8797L179.457 19.02Z" fill="#DBDBDB" /><path d="M182.715 44.8241L178.625 64.6775L146.544 58.0434L150.605 38.3502L152.511 42.4383L158.852 38.8616L160.828 41.5951L165.822 38.669L170.294 46.1638L175.532 42.3513L177.865 45.2525L180.923 43.0729L182.715 44.8241ZM136.797 42.4974L104.105 44.5235L101.507 2.49111L125.783 0.984863L134.695 8.4735L136.797 42.4974Z" fill="#EBEBEB" /><path d="M125.783 0.984863L134.696 8.4735L126.278 8.99623L125.783 0.984863ZM116.733 29.2672L116.604 28.3184C116.275 26.353 116.812 24.1559 118.517 21.8582C120.057 19.8138 120.89 18.3542 120.797 16.6962C120.694 14.8269 119.445 13.6479 117.147 13.7411C115.829 13.8104 114.563 14.2786 113.516 15.0843L112.512 12.828C113.677 11.8792 115.723 11.1786 117.657 11.0752C121.863 10.8428 123.901 13.3354 124.071 16.119C124.209 18.6076 122.922 20.4803 121.284 22.6672C119.778 24.6616 119.286 26.3048 119.5 28.165L119.591 29.1155L116.733 29.2672ZM116.213 34.4377C116.137 33.0837 116.997 32.0797 118.278 32.0099C119.559 31.94 120.49 32.8394 120.567 34.1974C120.637 35.478 119.853 36.5195 118.499 36.5877C117.208 36.661 116.275 35.7212 116.201 34.4434L116.213 34.4377Z" fill="#DBDBDB" /><path d="M34.9575 22.1181L31.6752 20.626L31.2166 21.6317C31.1496 21.776 31.0443 21.8991 30.9122 21.9877C30.78 22.0762 30.6261 22.1268 30.4672 22.134C30.2877 22.1416 30.1093 22.1596 29.932 22.188C29.7752 22.2105 29.6153 22.1904 29.469 22.1297C29.3227 22.069 29.1955 21.97 29.1007 21.8431L28.4564 20.9499L25.5223 23.0578L26.1661 23.951C26.2552 24.0811 26.3075 24.2328 26.3175 24.3901C26.3275 24.5474 26.2948 24.7045 26.2229 24.8448C26.14 25.0055 26.0656 25.1705 26.0002 25.3391C25.9424 25.4871 25.8455 25.6166 25.7198 25.7137C25.5941 25.8108 25.4443 25.8718 25.2865 25.8902L24.182 25.9988L24.5388 29.5993L25.6433 29.4885C25.8018 29.4758 25.9607 29.5064 26.1031 29.5772C26.2454 29.648 26.3658 29.7562 26.4513 29.8902C26.4979 29.9669 26.5479 30.0385 26.599 30.1118C26.6513 30.1842 26.7049 30.2556 26.7598 30.326C26.8613 30.4489 26.9267 30.5975 26.9484 30.7554C26.9702 30.9132 26.9476 31.0741 26.8831 31.2198L26.4229 32.2363L29.7127 33.7283L30.1712 32.7152C30.2394 32.5715 30.3456 32.4491 30.4783 32.3612C30.6109 32.2733 30.765 32.2232 30.924 32.2164C31.1032 32.2092 31.2816 32.1914 31.4593 32.163C31.6158 32.1401 31.7756 32.1601 31.9217 32.2208C32.0677 32.2815 32.1946 32.3807 32.2888 32.5078L32.9343 33.401L35.8729 31.2931L35.2286 30.3908C35.1361 30.2623 35.0816 30.1104 35.0711 29.9524C35.0607 29.7944 35.0948 29.6366 35.1695 29.4971C35.2502 29.3368 35.3227 29.1732 35.3871 29.0061C35.4436 28.8572 35.5401 28.7269 35.666 28.6294C35.792 28.5319 35.9424 28.4712 36.1007 28.4539L37.2053 28.3431L36.8485 24.7482L35.7422 24.8556C35.5847 24.8691 35.4266 24.8388 35.2852 24.7681C35.1438 24.6975 35.0248 24.5891 34.9411 24.455C34.8917 24.3805 34.8413 24.3066 34.7899 24.2334C34.7391 24.1605 34.6855 24.0896 34.6291 24.0209C34.5303 23.8968 34.4668 23.7483 34.4454 23.5911C34.424 23.4338 34.4455 23.2738 34.5075 23.1277L34.9575 22.1181ZM33.0916 25.4488C33.3461 25.8049 33.5185 26.213 33.5966 26.6437C33.6747 27.0744 33.6565 27.517 33.5434 27.9398C33.4302 28.3626 33.2248 28.7552 32.942 29.0892C32.6592 29.4233 32.3059 29.6906 31.9075 29.8721C31.3747 30.1138 30.7821 30.1919 30.2048 30.0964C29.6275 30.0009 29.0916 29.7361 28.6649 29.3357C28.2738 28.9689 27.9895 28.503 27.8423 27.9874C27.6951 27.4718 27.6905 26.926 27.8291 26.408C27.9676 25.89 28.2441 25.4193 28.629 25.0461C29.014 24.6729 29.4929 24.411 30.0149 24.2885C30.585 24.1553 31.1818 24.1945 31.7296 24.4011C32.2774 24.6077 32.7515 24.9723 33.0916 25.4488ZM183.602 69.8817L181.409 71.1499L181.796 71.8203C181.854 71.9166 181.883 72.0276 181.88 72.1399C181.876 72.2523 181.841 72.3613 181.777 72.4539C181.709 72.5596 181.645 72.6669 181.586 72.7817C181.538 72.8823 181.462 72.967 181.367 73.0257C181.272 73.0845 181.162 73.115 181.051 73.1135H180.278V75.6505H181.055C181.166 75.6496 181.276 75.6806 181.371 75.7399C181.466 75.7991 181.541 75.8842 181.589 75.9851C181.647 76.0972 181.71 76.2061 181.779 76.3118C181.843 76.404 181.879 76.5129 181.882 76.6251C181.886 76.7374 181.856 76.8483 181.798 76.9442L181.411 77.6158L183.607 78.8845L183.998 78.2107C184.054 78.1149 184.136 78.0367 184.235 77.985C184.333 77.9333 184.444 77.9101 184.555 77.9181H184.927C185.038 77.9109 185.149 77.9344 185.247 77.9861C185.345 78.0377 185.427 78.1154 185.484 78.2107L185.875 78.8885L188.064 77.6215L187.676 76.9459C187.621 76.8491 187.594 76.7387 187.598 76.6272C187.602 76.5158 187.637 76.4077 187.7 76.3152C187.767 76.2101 187.831 76.0993 187.889 75.9885C187.937 75.8879 188.013 75.8031 188.108 75.7443C188.203 75.6854 188.312 75.6548 188.424 75.6561H189.198V73.1192H188.417C188.305 73.1206 188.195 73.0901 188.1 73.0312C188.006 72.9723 187.93 72.8875 187.881 72.7868C187.823 72.6754 187.76 72.5663 187.693 72.4595C187.63 72.3672 187.595 72.2592 187.591 72.1478C187.587 72.0365 187.614 71.9262 187.669 71.8294L188.056 71.1539L185.868 69.8868L185.477 70.5533C185.42 70.6491 185.337 70.727 185.238 70.7785C185.139 70.83 185.027 70.8529 184.916 70.8448H184.544C184.433 70.8516 184.322 70.8281 184.223 70.7768C184.124 70.7254 184.041 70.6482 183.983 70.5533L183.602 69.8817ZM184.738 72.3061C185.149 72.3054 185.551 72.4267 185.893 72.6547C186.235 72.8827 186.501 73.2071 186.659 73.5868C186.776 73.8706 186.83 74.177 186.815 74.4839C186.801 74.7908 186.718 75.0907 186.574 75.362C186.43 75.6333 186.227 75.8693 185.981 76.0531C185.735 76.2368 185.451 76.3638 185.15 76.4249C184.848 76.4847 184.537 76.4771 184.239 76.4024C183.941 76.3278 183.663 76.188 183.426 75.9932C183.188 75.7984 182.997 75.5533 182.865 75.2757C182.734 74.998 182.665 74.6946 182.665 74.3874C182.664 74.1143 182.717 73.8438 182.82 73.5911C182.924 73.3385 183.076 73.1088 183.269 72.9152C183.461 72.7216 183.69 72.5678 183.942 72.4627C184.194 72.3576 184.465 72.3033 184.738 72.3027V72.3061ZM41.6104 11.3294L39.6019 12.8692L40.0598 13.4914C40.1291 13.5794 40.1716 13.6855 40.1824 13.7969C40.1932 13.9084 40.1717 14.0207 40.1206 14.1203C40.0645 14.2361 40.0163 14.3555 39.9763 14.4777C39.9403 14.5829 39.8756 14.6759 39.7896 14.7463C39.7036 14.8167 39.5996 14.8616 39.4894 14.876L38.7183 14.976L39.045 17.4902L39.8155 17.3902C39.9278 17.3749 40.0421 17.3916 40.1453 17.4383C40.2485 17.4851 40.3365 17.56 40.399 17.6544C40.4687 17.756 40.5443 17.8529 40.6257 17.9453C40.7009 18.0282 40.7507 18.1308 40.7693 18.241C40.7879 18.3513 40.7746 18.4646 40.7308 18.5675L40.4382 19.2823L42.7718 20.2539L43.0627 19.5397C43.1068 19.438 43.1784 19.3507 43.2695 19.2875C43.3605 19.2242 43.4673 19.1876 43.578 19.1817L43.7655 19.1607L43.9439 19.1323C44.0519 19.1121 44.1634 19.1219 44.2663 19.1606C44.3691 19.1992 44.4594 19.2653 44.5274 19.3516L45.0019 19.9738L47.0178 18.4118L46.5485 17.7959C46.4801 17.7071 46.4382 17.6009 46.4274 17.4893C46.4167 17.3778 46.4375 17.2655 46.4877 17.1652C46.543 17.0524 46.5911 16.9368 46.632 16.8186C46.6677 16.713 46.7323 16.6194 46.8184 16.5486C46.9046 16.4778 47.0089 16.4326 47.1195 16.4181L47.8899 16.3181L47.5632 13.8039L46.7928 13.9044C46.6808 13.92 46.5667 13.9038 46.4635 13.8576C46.3603 13.8114 46.2722 13.737 46.2093 13.6431C46.139 13.5412 46.0621 13.444 45.9791 13.3522C45.9114 13.2674 45.8675 13.1661 45.8521 13.0587C45.8368 12.9513 45.8504 12.8417 45.8916 12.7414L46.1842 12.0266L43.8507 11.0527L43.5604 11.7675C43.515 11.8684 43.443 11.9549 43.3521 12.0179C43.2612 12.0809 43.1548 12.1179 43.0445 12.1249L42.857 12.1408H42.6791C42.5711 12.1615 42.4593 12.152 42.3564 12.1132C42.2534 12.0744 42.1631 12.0078 42.0956 11.9209L41.6104 11.3294ZM43.0479 13.5863C43.6035 13.5881 44.1356 13.8105 44.5274 14.2044C44.8191 14.4986 45.0174 14.8724 45.0973 15.2789C45.1771 15.6853 45.1351 16.1064 44.9764 16.489C44.8176 16.8717 44.5494 17.1989 44.2052 17.4295C43.8611 17.6601 43.4565 17.7838 43.0422 17.7851C42.4867 17.7849 41.9539 17.5642 41.561 17.1715C41.2683 16.8782 41.0689 16.5049 40.9878 16.0986C40.9067 15.6923 40.9475 15.2711 41.1052 14.8879C41.2628 14.5048 41.5302 14.1768 41.8737 13.9452C42.2173 13.7136 42.6216 13.5887 43.036 13.5863H43.0479ZM26.6024 77.2016C26.7115 74.3794 26.6723 71.5527 26.4848 68.7346C26.0686 62.9152 25.1826 57.139 23.8354 51.4624C23.095 48.2681 22.1854 45.1272 21.2348 41.9931C21.2064 41.8965 21.0564 41.9232 21.0814 42.0232C24.0155 53.0885 26.424 64.5039 26.2547 76.0067C26.2081 79.2226 25.8354 82.3868 25.5411 85.5868C25.5411 85.6709 25.6718 85.7067 25.6928 85.6192C26.2905 82.8658 26.4882 79.9982 26.6024 77.2016Z" fill="#EBEBEB" /><path d="M21.0808 42.0093C21.0808 42.0093 16.6376 53.3081 17.6325 55.9456C18.6274 58.5831 20.678 59.1615 20.678 59.1615C20.678 59.1615 17.9592 63.0496 19.8592 66.6962C21.7587 70.3428 25.9814 70.1712 26.2723 74.3059C26.2638 74.3292 26.5007 57.0428 21.0808 42.0093Z" fill="#EBEBEB" /><path d="M25.788 67.1922C25.7949 67.1733 25.7949 67.1526 25.788 67.1337C25.2025 59.4195 23.755 51.7951 21.4726 44.403C21.4726 44.3814 21.4283 44.3865 21.4351 44.403C22.3127 47.7007 23.0903 51.0242 23.767 54.3689C23.1697 53.8024 22.4395 53.3953 21.6437 53.1848C21.6437 53.1848 21.6209 53.2019 21.6437 53.2076C22.48 53.5138 23.2317 54.0143 23.8368 54.6678C23.9647 55.3246 24.0931 55.9786 24.213 56.6331C23.2187 55.5218 21.842 54.9689 20.4664 54.4439C20.4637 54.4471 20.4623 54.4511 20.4624 54.4553C20.4624 54.4598 20.4637 54.4638 20.4664 54.4672C21.9039 55.0519 23.3334 55.8036 24.3056 57.0456C24.6446 58.9199 24.942 60.8005 25.1976 62.6877C24.4789 61.936 23.5582 61.408 22.5465 61.1672C22.5147 61.1672 22.4965 61.2047 22.5289 61.2138C23.5708 61.5195 24.5008 62.1226 25.205 62.949H25.2221C25.3092 63.61 25.3925 64.2746 25.4721 64.9428C24.9169 64.473 24.2908 64.0942 23.617 63.8206C23.5993 63.8206 23.5868 63.8422 23.617 63.8473C24.3058 64.1458 24.9363 64.5638 25.4795 65.082H25.5022C25.576 65.7166 25.6488 66.3507 25.7113 66.9831C24.7113 65.7716 23.4127 64.8418 21.9437 64.2854C21.9406 64.2853 21.9376 64.2858 21.9348 64.2869C21.932 64.2879 21.9294 64.2896 21.9272 64.2916C21.9249 64.2939 21.9231 64.2965 21.922 64.2994C21.9208 64.3024 21.9203 64.3055 21.9204 64.3087C21.9204 64.3113 21.9207 64.3144 21.9215 64.3178C21.9225 64.3207 21.9243 64.3232 21.9266 64.3252C21.9287 64.3276 21.9315 64.3294 21.9346 64.3303C21.9372 64.3314 21.9402 64.332 21.9437 64.332C23.3935 64.9716 24.6672 65.9523 25.6562 67.1905C25.6677 67.2036 25.6826 67.2132 25.6993 67.2182C25.7159 67.2232 25.7337 67.2235 25.7505 67.2189C25.8471 68.1911 25.9289 69.1627 26.0039 70.1382C26.0046 70.1408 26.0062 70.143 26.0084 70.1445L26.0158 70.1462L26.0226 70.1445C26.0248 70.143 26.0264 70.1408 26.0272 70.1382C25.9461 69.1723 25.866 68.1903 25.788 67.1922Z" fill="white" /><path d="M22.2092 52.0725C21.4029 51.5706 20.4817 51.2832 19.533 51.2378C19.5308 51.2378 19.5287 51.2384 19.5268 51.2395C19.5237 51.241 19.5212 51.2433 19.5194 51.2463C19.5177 51.2492 19.5169 51.2526 19.5171 51.256C19.517 51.2604 19.5185 51.2647 19.5215 51.268C19.5245 51.2713 19.5286 51.2733 19.533 51.2736C20.4484 51.4475 21.337 51.7418 22.1751 52.1486C22.2194 52.1582 22.241 52.0952 22.2092 52.0725ZM22.7546 64.0338C22.1346 63.6755 21.4513 63.4401 20.7421 63.3406C20.7194 63.3406 20.7171 63.373 20.7421 63.3781C21.4319 63.5173 22.0989 63.7514 22.7245 64.0736C22.7546 64.0963 22.7745 64.0554 22.7546 64.0338Z" fill="white" /><path d="M38.2867 56.1496C36.4074 57.4508 34.6815 58.9606 33.1419 60.6501C31.5533 62.4409 30.3281 64.5235 29.5345 66.7819C27.8459 71.4865 26.9379 76.4355 26.8464 81.4331C26.7896 84.2018 27.0544 86.9677 27.6362 89.6751C27.6387 89.6852 27.6431 89.6946 27.6492 89.703C27.655 89.7117 27.6625 89.7191 27.6713 89.7246C27.6802 89.7302 27.69 89.7339 27.7004 89.7354C27.7106 89.7364 27.7209 89.736 27.7311 89.7342C27.75 89.7294 27.7665 89.7177 27.7774 89.7015C27.7883 89.6852 27.7929 89.6655 27.7901 89.6462C27.0674 84.8685 27.095 80.0073 27.872 75.2382C28.6356 70.566 29.8345 65.4791 32.8026 61.6808C34.4083 59.6121 36.4316 57.9865 38.422 56.303C38.5294 56.2155 38.3953 56.0723 38.2867 56.1496Z" fill="#EBEBEB" /><path d="M27.5 78.0678C28.5689 76.3353 29.8253 74.7258 31.2466 73.2683C33.4693 71.0405 35.3409 69.4581 35.633 67.9871C35.9261 66.5166 33.4051 65.4286 33.4051 65.4286C33.4051 65.4286 36.4807 65.9842 37.3301 64.6911C38.179 63.3967 38.5784 55.9053 38.5784 55.9053C38.5784 55.9053 32.9818 59.9075 30.3722 65.5195C27.7625 71.132 27.5 78.0678 27.5 78.0678Z" fill="#EBEBEB" /><path d="M36.6395 57.8272C34.2849 60.1499 31.9139 62.6118 30.5167 65.6584C30.0432 66.7059 29.63 67.7796 29.2792 68.8743C29.2094 69.0976 29.1361 69.3192 29.0684 69.5408C29.0646 69.5524 29.0646 69.565 29.0684 69.5766C28.4097 71.8524 27.8786 74.1632 27.4775 76.4982C27.4775 76.513 27.4969 76.5198 27.5003 76.4982C27.7429 75.33 28.0071 74.163 28.3048 72.9749H28.3406C28.8045 72.7766 29.296 72.6506 29.798 72.601C29.8304 72.601 29.8264 72.5391 29.798 72.5425C29.3022 72.5645 28.8118 72.6547 28.3406 72.8107C28.6116 71.7493 28.9059 70.6897 29.2327 69.6482C30.3252 69.3848 31.4431 69.241 32.5667 69.2192M32.5667 69.2033C31.4711 69.1519 30.3734 69.2449 29.302 69.48L29.4804 68.9442C30.2029 68.7715 30.9359 68.6463 31.6747 68.5692V68.5459C30.9474 68.5596 30.2224 68.6476 29.5128 68.8084C29.8069 67.9089 30.1395 67.0225 30.5099 66.1516C30.8128 65.4457 31.1664 64.7627 31.5679 64.1079C32.1544 63.9593 32.7505 63.8517 33.352 63.7857V63.7573C32.7875 63.715 32.22 63.7712 31.6747 63.9232C31.9683 63.4457 32.2808 62.9802 32.6116 62.5277C33.8599 62.251 35.1395 61.9919 36.4241 62.2363C36.4267 62.2356 36.4291 62.2343 36.431 62.2325C36.433 62.2307 36.4345 62.2285 36.4355 62.226C36.4376 62.221 36.4378 62.2153 36.4361 62.2101C36.4326 62.2053 36.4284 62.2011 36.4236 62.1976C35.2037 61.8743 33.9906 62.0618 32.7736 62.301C32.9059 62.1226 33.0429 61.9437 33.1787 61.7772C33.9349 61.5434 34.7207 61.419 35.5122 61.4079M35.5122 61.3811C34.7775 61.3152 34.0366 61.3811 33.3247 61.5755C34.3633 60.2857 35.5088 59.0738 36.6429 57.8721C36.7219 57.8039 36.6804 57.7715 36.6253 57.8272L35.5122 61.3811Z" fill="white" /><path d="M36.5669 62.9336C36.1984 62.8782 35.8247 62.8656 35.4533 62.8961C35.409 62.8961 35.4124 62.9711 35.4533 62.9745C35.8209 62.9745 36.1902 62.9569 36.5578 62.9745C36.5639 62.973 36.5686 62.9694 36.572 62.9637C36.5738 62.9612 36.575 62.9583 36.5755 62.9553C36.5761 62.9522 36.5761 62.9491 36.5754 62.9461C36.573 62.9415 36.5705 62.9373 36.5669 62.9336ZM32.547 70.1984C31.9556 70.2105 31.3709 70.3274 30.8203 70.5438V70.5705C31.4038 70.4325 31.9675 70.3773 32.5436 70.279C32.5535 70.2734 32.5614 70.2649 32.5664 70.2546C32.5708 70.2437 32.5708 70.2315 32.5664 70.2205C32.562 70.2116 32.5553 70.2039 32.547 70.1984ZM31.3385 67.6887C31.0933 67.6875 30.8485 67.709 30.6073 67.7529C30.6043 67.754 30.6017 67.7557 30.5994 67.7578C30.5972 67.76 30.5954 67.7625 30.5942 67.7654C30.5928 67.7681 30.592 67.7711 30.5918 67.7741C30.5916 67.7771 30.5921 67.7802 30.5931 67.783C30.5961 67.7895 30.6008 67.7942 30.6073 67.7972C30.8565 67.8109 31.1064 67.7993 31.3533 67.7626C31.3578 67.7603 31.362 67.7574 31.3658 67.754C31.369 67.7501 31.3717 67.7457 31.3737 67.741C31.3751 67.7362 31.3759 67.7312 31.376 67.7262C31.3757 67.7162 31.3717 67.7067 31.3646 67.6995C31.3575 67.6928 31.3482 67.689 31.3385 67.6887Z" fill="white" /><path d="M11.0911 64.0969C12.9133 64.7169 14.6592 65.5421 16.2951 66.5565C17.991 67.6349 19.4629 69.0303 20.6303 70.6662C23.0815 74.085 24.9579 77.8812 26.1854 81.9048C26.8705 84.1333 27.2931 86.4341 27.4445 88.7605C27.4421 88.776 27.434 88.79 27.4218 88.7997C27.4129 88.8074 27.4022 88.8125 27.3908 88.8146C27.3793 88.8167 27.3675 88.8158 27.3565 88.8118C27.3456 88.8078 27.3359 88.8009 27.3285 88.7919C27.321 88.7829 27.3162 88.772 27.3144 88.7605C26.7987 84.7274 25.6551 80.7998 23.9252 77.1202C22.2195 73.5247 20.0786 69.6838 16.807 67.2969C15.0229 65.9957 13.0172 65.1435 11.0178 64.2446C10.9127 64.2094 10.9803 64.0554 11.0911 64.0969Z" fill="#EBEBEB" /><path d="M24.8795 79.3381C23.6133 78.1842 22.2243 77.1729 20.7375 76.3222C18.4176 75.0358 16.5432 74.1886 15.9687 73.0631C15.3943 71.9375 17.1795 70.4864 17.1795 70.4864C17.1795 70.4864 14.8193 71.6483 13.8346 70.7864C12.85 69.9256 10.8022 63.9688 10.8022 63.9688C10.8022 63.9688 16.2557 65.9108 19.6574 69.8483C23.0596 73.7869 24.8795 79.3381 24.8795 79.3381Z" fill="#EBEBEB" /><path d="M24.5945 78.11C21.8633 72.9901 18.2275 68.3441 13.2451 65.2964C14.5477 66.1341 15.7696 67.0911 16.8951 68.1549C15.8801 68.0994 14.8684 68.3126 13.9622 68.7731C13.9601 68.774 13.9582 68.7753 13.9566 68.777C13.9551 68.7787 13.9539 68.7806 13.9531 68.7827C13.9521 68.7849 13.9516 68.7872 13.9516 68.7896C13.9516 68.7919 13.9521 68.7942 13.9531 68.7964L13.9622 68.8055C14.9264 68.4424 15.9508 68.2691 16.9803 68.2941C16.9917 68.2979 17.0031 68.2979 17.0144 68.2941C17.1928 68.4606 17.3713 68.6339 17.5389 68.8055C17.0127 68.8299 16.49 68.9004 15.9764 69.0163V69.0396C16.5212 68.936 17.0753 68.8898 17.6298 68.9015C18.5417 69.8258 19.4011 70.8006 20.2036 71.8214C19.8367 71.7695 19.4629 71.7962 19.107 71.8998C19.1017 71.9003 19.0966 71.9027 19.0928 71.9066C19.0894 71.9105 19.0874 71.9156 19.0872 71.9208C19.0872 71.9265 19.0891 71.9312 19.0928 71.935C19.0966 71.9388 19.1014 71.9407 19.107 71.9407C19.4945 71.8975 19.8852 71.8927 20.2735 71.9265C20.4213 72.1163 20.5644 72.3072 20.7195 72.5112C19.604 72.4402 18.4843 72.5486 17.4031 72.8322C17.3855 72.8322 17.4031 72.8612 17.4031 72.8577C18.5273 72.624 19.6768 72.5356 20.8235 72.5947C20.9622 72.7737 21.0945 72.9702 21.2315 73.1555C20.5701 73.1408 19.9098 73.2178 19.2695 73.3845L19.2627 73.3884L19.2582 73.3941L19.2565 73.4021C19.2565 73.4047 19.2572 73.4072 19.2588 73.4095L19.2627 73.4174L19.2695 73.422C19.9394 73.2772 20.6258 73.2243 21.3099 73.2646C21.894 74.074 22.4567 74.8988 22.9974 75.7379C22.7062 75.6837 22.4063 75.7014 22.1235 75.7896M22.1235 75.8129C22.4358 75.7914 22.7495 75.8068 23.0582 75.8589C23.5445 76.6157 24.0131 77.3799 24.4639 78.1515C24.5338 78.2282 24.6428 78.1782 24.6014 78.11L22.1235 75.8129Z" fill="white" /><path d="M20.3521 73.8566C19.8156 73.8199 19.2771 73.8864 18.7657 74.0526V74.0759C19.2872 73.9862 19.8142 73.9314 20.343 73.9117C20.3822 73.9117 20.3913 73.86 20.3521 73.8566ZM15.5941 67.6071C15.3494 67.5878 15.1033 67.6015 14.8623 67.648C14.8373 67.648 14.8447 67.6946 14.8623 67.6929C15.1049 67.6662 15.3407 67.6753 15.5759 67.6702C15.585 67.6698 15.5928 67.6666 15.5992 67.6605C15.6046 67.6555 15.6081 67.6488 15.6094 67.6415C15.6106 67.6342 15.6094 67.6268 15.606 67.6202C15.6036 67.6147 15.5994 67.6101 15.5941 67.6071Z" fill="white" /><path d="M31.9134 96.2969H19.6021L21.3577 83.4185L21.8066 80.1162H29.71L30.1577 83.4185L31.9134 96.2969Z" fill="#DBDBDB" /><path d="M30.1579 83.4185H21.3579L21.8073 80.1162H29.7108L30.1579 83.4185Z" fill="#C7C7C7" /><path d="M30.8382 78.9712H20.6763V81.7672H30.8376L30.8382 78.9712Z" fill="#DBDBDB" /><path d="M11.7769 96.2975L33.9331 96.2276L56.0922 96.2009L100.409 96.1509L144.724 96.2009L166.883 96.2276L189.041 96.2975L166.883 96.3708L144.724 96.3935L100.409 96.4441L56.0922 96.3935L33.9331 96.3668L11.7769 96.2975Z" fill="#263238" /><path d="M100.992 118.058C147.157 118.058 184.581 113.951 184.581 108.885C184.581 103.819 147.157 99.7119 100.992 99.7119C54.8273 99.7119 17.4028 103.818 17.4028 108.885C17.4028 113.951 54.8267 118.058 100.992 118.058Z" fill="#EBEBEB" /><path d="M81.5324 107.366C80.8528 107.596 61.3739 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0483 100.74C60.0222 99.9785 60.0008 99.2167 59.9841 98.4549L58.4341 88.0492L71.4568 87.9634L72.6841 98.3725L72.6346 100.662C72.6346 100.662 80.104 103.976 80.867 104.576C81.6307 105.176 82.2119 107.135 81.5324 107.366Z" fill="#EB9481" /><path d="M81.5331 107.365C80.8536 107.595 61.3746 107.656 60.6325 107.195C60.2882 106.979 60.0973 102.83 59.9956 98.4545L72.6615 98.3721L72.6115 100.661C72.6115 100.661 80.0808 103.975 80.8445 104.575C81.6081 105.175 82.2132 107.134 81.5331 107.365Z" fill="white" /><path d="M81.5329 107.366C80.8528 107.596 61.3738 107.657 60.6318 107.196C60.3392 107.017 60.1733 104.18 60.0488 100.741L72.6045 100.662C72.6045 100.662 80.0716 103.976 80.8352 104.576C81.5988 105.177 82.2125 107.135 81.5329 107.366Z" fill="#A6A6A6" /><path d="M81.2502 106.579C77.9837 106.508 64.807 106.539 61.5746 106.722C61.5479 106.722 61.5479 106.738 61.5746 106.742C64.811 106.879 77.9871 106.742 81.2502 106.63C81.32 106.634 81.32 106.579 81.2502 106.579ZM79.5911 103.88C79.2276 103.871 78.8663 103.938 78.5303 104.077C78.1942 104.216 77.8908 104.423 77.6394 104.686C77.1811 105.154 76.9221 105.781 76.9166 106.437C76.9166 106.458 76.9524 106.458 76.9541 106.437C77.0632 105.792 77.3775 105.199 77.8503 104.747C78.3231 104.294 78.9292 104.007 79.5786 103.926C79.5846 103.928 79.5907 103.926 79.5967 103.923C79.6023 103.92 79.6065 103.914 79.6087 103.908C79.6093 103.905 79.6094 103.902 79.6088 103.899C79.6082 103.896 79.607 103.893 79.6053 103.891C79.6038 103.888 79.6017 103.886 79.5993 103.884C79.5968 103.882 79.594 103.881 79.5911 103.88ZM73.8808 100.983C72.5729 100.969 71.0604 101.435 70.2502 102.509C70.2217 102.55 70.2791 102.595 70.3183 102.574C71.4591 101.97 72.6612 101.491 73.9041 101.143C73.9179 101.138 73.93 101.129 73.9393 101.118C73.9486 101.107 73.9547 101.093 73.957 101.078C73.9593 101.064 73.9577 101.049 73.9525 101.035C73.9472 101.022 73.9383 101.009 73.9269 101C73.9137 100.99 73.8976 100.984 73.8808 100.983ZM74.8939 101.371C73.5848 101.371 72.0718 101.827 71.2655 102.899C71.2331 102.942 71.2905 102.986 71.3314 102.965C72.4709 102.361 73.6727 101.881 74.9155 101.536C74.9299 101.531 74.9429 101.523 74.953 101.512C74.963 101.5 74.9699 101.486 74.9729 101.472C74.9758 101.457 74.9747 101.441 74.9698 101.427C74.9648 101.413 74.956 101.4 74.9445 101.39C74.9303 101.378 74.9124 101.371 74.8939 101.371ZM75.9041 101.755C74.5979 101.755 73.0837 102.207 72.2735 103.283C72.2445 103.324 72.3019 103.367 72.3411 103.347C73.4824 102.745 74.6847 102.266 75.9274 101.918C75.9477 101.911 75.9647 101.897 75.9751 101.879C75.9855 101.86 75.9886 101.838 75.9837 101.817C75.9794 101.799 75.9692 101.784 75.9547 101.772C75.9403 101.761 75.9225 101.755 75.9041 101.755ZM76.9149 102.153C75.6058 102.138 74.0928 102.605 73.2842 103.681C73.2524 103.72 73.3149 103.764 73.3524 103.745C74.4929 103.141 75.6951 102.662 76.9382 102.316C76.9585 102.309 76.9755 102.295 76.986 102.277C76.9966 102.258 76.9998 102.236 76.995 102.216C76.9912 102.197 76.9811 102.181 76.9664 102.17C76.9518 102.158 76.9336 102.152 76.9149 102.153ZM62.3968 103.62C60.9394 103.62 60.9394 105.912 62.3968 105.903C63.8547 105.894 63.8649 103.611 62.3968 103.62Z" fill="#263238" /><path d="M75.283 97.4843C74.6211 96.9355 73.7864 97.4843 73.3813 98.0468C72.7085 99.0434 72.3065 100.198 72.2148 101.397C72.2148 101.403 72.2158 101.408 72.2177 101.413C72.2201 101.417 72.2232 101.422 72.2268 101.425C72.2307 101.429 72.2351 101.432 72.2398 101.434C72.245 101.435 72.2503 101.436 72.2558 101.437C72.2647 101.476 72.2881 101.51 72.3212 101.532C72.3542 101.555 72.3946 101.564 72.4342 101.558C72.4516 101.554 72.4683 101.547 72.4836 101.538C73.4188 100.895 74.4893 100.325 75.2029 99.4031C75.5932 98.8707 75.9165 98.0025 75.283 97.4843ZM73.6898 100.27C73.2421 100.6 72.7785 100.906 72.3415 101.254C72.5522 100.594 72.7962 99.944 73.0728 99.3082C73.2099 99.0052 73.3675 98.7135 73.5455 98.4332C73.8359 97.9985 75.1063 96.9002 75.2416 98.3218C75.3166 99.1008 74.2234 99.8786 73.6898 100.27Z" fill="#263238" /><path d="M68.9583 101.896C70.0946 102.075 71.2776 101.739 72.3856 101.561C72.4264 101.549 72.4616 101.524 72.4844 101.488C72.495 101.47 72.5022 101.45 72.5054 101.429C72.5097 101.391 72.5016 101.354 72.4822 101.321L72.4901 101.311C72.4916 101.306 72.4925 101.302 72.4929 101.298C72.4931 101.293 72.4922 101.288 72.4903 101.284C72.4884 101.28 72.4856 101.276 72.4822 101.273C71.5861 100.469 70.4936 99.9154 69.3151 99.6686C68.6265 99.5385 67.6532 99.7277 67.5651 100.583C67.4668 101.398 68.2969 101.791 68.9583 101.896ZM68.1651 101.155C67.2907 100.035 68.9606 99.9385 69.4634 100.054C69.7888 100.137 70.1068 100.24 70.4174 100.364C71.0564 100.629 71.6797 100.927 72.2873 101.257C71.7327 101.312 71.1884 101.411 70.6373 101.487C69.9793 101.575 68.6554 101.773 68.1651 101.157V101.155Z" fill="#263238" /><path d="M39.2837 39.9521C39.2837 39.9521 54.4041 65.7146 54.8041 67.5266C55.4303 70.3084 58.7218 97.7521 58.7218 97.7521L74.4632 97.4664C74.4632 97.4664 71.7536 73.2652 69.1843 65.2914C67.336 59.5493 53.9905 40.501 53.9905 40.501L39.2837 39.9521Z" fill="#1A2E35" /><path d="M73.0314 95.0548C70.7729 95.0383 62.7649 94.9469 59.7552 95.2326C59.7319 95.2326 59.7354 95.2741 59.7552 95.2758C60.7899 95.4258 70.7695 95.1951 73.028 95.1224C73.0319 95.1242 73.0362 95.1247 73.0405 95.1241C73.0447 95.1241 73.0488 95.1229 73.0524 95.1207C73.0564 95.1195 73.0599 95.1169 73.0621 95.1133C73.0647 95.11 73.067 95.1064 73.0689 95.1025C73.0701 95.0986 73.0708 95.0946 73.0712 95.0906C73.0709 95.0864 73.0701 95.0822 73.0689 95.0781C73.0673 95.0743 73.0648 95.0709 73.0615 95.0684C73.0588 95.0653 73.0552 95.0629 73.0513 95.0616L73.0314 95.0548ZM41.8626 42.319C42.8547 43.8758 43.9035 45.3997 44.9138 46.9429C45.9229 48.4872 46.9365 50.0326 47.9354 51.5741C49.9297 54.6878 51.8729 57.8222 53.7649 60.9775C54.6893 62.4406 55.48 63.9839 56.1274 65.5889C56.7184 67.1939 57.1621 68.8494 57.453 70.5349C57.7632 72.2253 57.9666 73.9298 58.1558 75.6417C58.3498 77.4565 58.553 79.2736 58.7655 81.0929C59.1795 84.8111 59.5958 88.5304 60.0143 92.2508C60.0626 92.7059 60.1126 93.1616 60.1592 93.6207C60.1593 93.6328 60.1558 93.6446 60.1491 93.6547C60.1424 93.6648 60.1329 93.6726 60.1217 93.6772C60.1106 93.6819 60.0983 93.6831 60.0865 93.6807C60.0746 93.6783 60.0637 93.6725 60.0552 93.6639C60.0494 93.6584 60.0448 93.6516 60.0418 93.6442C60.0388 93.6367 60.0373 93.6287 60.0376 93.6207C59.574 89.9258 59.1888 86.2184 58.7694 82.5253C58.559 80.6833 58.3497 78.8413 58.1416 76.9991C57.9467 75.2838 57.7598 73.5684 57.4956 71.8656C57.2525 70.1856 56.8821 68.5264 56.3876 66.9025C55.8439 65.2187 55.1146 63.6007 54.2132 62.0781C52.3893 58.9264 50.4666 55.8247 48.5314 52.7361C46.5956 49.6463 44.6189 46.5128 42.5837 43.4446C42.3346 43.0655 42.0789 42.6908 41.8166 42.3207C41.7933 42.3156 41.8433 42.2906 41.8626 42.319Z" fill="#263238" /><path d="M44.2385 108.883C43.5254 108.839 24.9993 101.324 24.4851 100.611C24.4632 100.569 24.4534 100.521 24.4567 100.474C24.4567 99.8357 25.743 97.6039 26.8146 94.6721C26.8828 94.4937 27.3612 93.6164 28.0231 92.4459C29.8623 89.23 33.068 83.709 33.068 83.709L45.176 88.5329L39.3794 97.2181L38.4788 99.3209C38.4788 99.3209 44.1606 105.203 44.6385 106.036C45.1169 106.868 44.9521 108.923 44.2385 108.883Z" fill="#EB9481" /><path d="M44.2385 108.884C43.5255 108.84 24.9993 101.325 24.4851 100.612C24.4631 100.569 24.4533 100.522 24.4567 100.475C24.4567 99.8365 25.7431 97.6047 26.8147 94.6723C26.8829 94.4939 27.3613 93.6167 28.0232 92.4468L39.3925 97.2138L38.5005 99.3161C38.5005 99.3161 44.18 105.198 44.6584 106.031C45.1368 106.863 44.9522 108.923 44.2385 108.884Z" fill="white" /><path d="M44.2388 108.884C43.5252 108.839 24.9991 101.325 24.4854 100.612C24.2871 100.34 25.115 97.8081 26.8184 94.6729L38.4786 99.3183C38.4786 99.3183 44.1621 105.197 44.6422 106.043C45.1224 106.89 44.9525 108.923 44.2388 108.884Z" fill="#A6A6A6" /><path d="M44.2683 108.054C41.2689 106.764 29.0439 101.841 25.9785 100.796C25.9518 100.796 25.9467 100.796 25.9785 100.814C28.9263 102.158 41.1905 106.98 44.2569 108.113C44.316 108.127 44.341 108.081 44.2683 108.054ZM43.7427 104.922C43.4103 104.775 43.051 104.7 42.6876 104.7C42.3243 104.7 41.9649 104.775 41.6325 104.922C41.0341 105.185 40.56 105.668 40.3086 106.271C40.3086 106.288 40.3353 106.303 40.3444 106.285C40.6881 105.725 41.2044 105.291 41.8157 105.049C42.4271 104.808 43.1003 104.771 43.7342 104.945C43.7362 104.944 43.738 104.943 43.7395 104.941C43.741 104.939 43.7421 104.937 43.7427 104.935C43.7436 104.933 43.744 104.931 43.744 104.929C43.744 104.926 43.7436 104.924 43.7427 104.922ZM39.5359 100.091C38.3314 99.5879 36.753 99.4521 35.6007 100.145C35.5575 100.17 35.6007 100.232 35.6376 100.229C36.9217 100.099 38.2159 100.106 39.4984 100.25C39.5199 100.252 39.5412 100.246 39.5581 100.232C39.5746 100.218 39.5851 100.199 39.5876 100.177C39.59 100.159 39.586 100.14 39.5763 100.125C39.5667 100.11 39.5526 100.098 39.5359 100.091ZM40.3263 100.834C39.1217 100.33 37.5433 100.194 36.3905 100.888C36.3478 100.912 36.3905 100.975 36.428 100.972C37.7117 100.839 39.006 100.846 40.2882 100.993C40.3097 100.995 40.3311 100.988 40.3478 100.975C40.3645 100.961 40.3751 100.941 40.3774 100.92C40.3796 100.902 40.3757 100.883 40.3663 100.868C40.357 100.852 40.3432 100.84 40.3263 100.834ZM41.1297 101.575C39.9217 101.072 38.341 100.938 37.1944 101.629C37.1461 101.656 37.1944 101.717 37.2285 101.713C38.5121 101.581 39.8064 101.589 41.0888 101.735C41.0992 101.737 41.11 101.736 41.1204 101.734C41.1308 101.732 41.1406 101.727 41.149 101.72C41.1577 101.714 41.1651 101.706 41.1706 101.697C41.1762 101.688 41.1799 101.678 41.1814 101.667C41.1844 101.648 41.181 101.629 41.1717 101.612C41.1618 101.596 41.1471 101.583 41.1297 101.575ZM41.9092 102.315C40.703 101.812 39.1257 101.677 37.9734 102.366C37.9268 102.396 37.9734 102.456 38.0092 102.453C39.293 102.321 40.5872 102.328 41.8694 102.474C41.8899 102.478 41.9108 102.473 41.928 102.462C41.9452 102.45 41.9574 102.433 41.9621 102.412C41.9672 102.393 41.9645 102.372 41.9547 102.354C41.9447 102.336 41.9285 102.322 41.9092 102.315ZM27.903 98.2215C26.5399 97.6857 25.6961 99.801 27.0626 100.344C28.4291 100.887 29.2785 98.7646 27.903 98.2215Z" fill="#263238" /><path d="M42.1467 97.3773C41.7347 96.62 40.7643 96.8075 40.1717 97.1842C39.1751 97.854 38.3713 98.7731 37.8404 99.8501C37.8386 99.8543 37.8372 99.8586 37.8364 99.8631C37.8364 99.8677 37.837 99.8724 37.8381 99.8773C37.84 99.8814 37.8422 99.8852 37.8449 99.8887C37.8484 99.8918 37.8522 99.8945 37.8563 99.8967C37.8563 99.9933 37.9205 100.107 38.0347 100.075C39.1387 99.8217 40.3535 99.6893 41.3404 99.1069C41.9165 98.7785 42.5336 98.0921 42.1467 97.3773ZM39.6239 99.3592C39.0887 99.4967 38.5427 99.6075 38.0097 99.7643C38.451 99.2321 38.9184 98.7236 39.412 98.2387C39.6544 98.0069 39.912 97.7925 40.1847 97.5955C40.6069 97.3041 42.2035 96.7626 41.7898 98.1319C41.5722 98.8961 40.2682 99.1967 39.6239 99.3592Z" fill="#263238" /><path d="M34.6345 99.0913C35.6248 99.6754 36.8339 99.8061 37.9368 100.069C38.0504 100.095 38.126 99.9811 38.1152 99.8902L38.1271 99.8862C38.1306 99.8839 38.1339 99.8812 38.1368 99.8783C38.142 99.8714 38.1444 99.8629 38.1436 99.8544C38.1442 99.8497 38.1442 99.8449 38.1436 99.8402C37.6141 98.7577 36.8093 97.8335 35.8101 97.1601C35.2271 96.7794 34.251 96.5896 33.8373 97.3385C33.4231 98.0868 34.0549 98.7498 34.6345 99.0913ZM34.1726 98.1032C33.7674 96.7351 35.3623 97.2777 35.7782 97.5669C36.0506 97.7639 36.3074 97.979 36.5487 98.2123C37.0405 98.6982 37.5074 99.2087 37.9476 99.7419C37.4123 99.5845 36.8771 99.472 36.3419 99.3345C35.6924 99.1646 34.401 98.8714 34.1726 98.1032Z" fill="#263238" /><path d="M40.3206 96.7292L26.7627 91.2496C26.7627 91.2496 43.5655 66.5053 43.6866 64.6633C43.7991 63.0337 36.4172 48.8383 36.128 44.7002C35.8144 40.2337 37.9587 36.7797 40.3206 34.6343L54.2996 40.5871C54.2996 40.5871 52.8241 42.9491 50.5087 44.2655C50.5087 44.2655 57.6763 60.6729 57.2701 65.7189C56.8633 70.7644 40.3206 96.7292 40.3206 96.7292Z" fill="#1A2E35" /><path d="M40.9205 94.1472C38.9585 93.3035 39.5364 93.516 37.5671 92.687C36.604 92.2853 30.2773 89.6495 29.3057 89.4302C29.2841 89.4302 29.2699 89.4586 29.2898 89.4694C30.1335 89.9876 36.5114 92.5063 37.4813 92.8978C39.4671 93.6807 38.9085 93.4342 40.8977 94.1932C40.9002 94.196 40.9033 94.1982 40.9068 94.1995C40.9105 94.2008 40.9143 94.2018 40.9182 94.2023C40.9221 94.2018 40.9259 94.2008 40.9296 94.1995C40.9331 94.1982 40.9362 94.196 40.9387 94.1932C40.9413 94.1905 40.9433 94.1873 40.9447 94.1838C40.946 94.1803 40.9467 94.1766 40.9466 94.1728C40.9467 94.169 40.946 94.1653 40.9447 94.1618C40.9433 94.1583 40.9413 94.1551 40.9387 94.1523C40.9331 94.1497 40.9271 94.148 40.921 94.1472M52.8068 43.9643C51.3516 44.3059 49.9277 44.7691 48.55 45.3489C48.539 45.353 48.5299 45.3612 48.5246 45.3717C48.5194 45.3823 48.5183 45.3944 48.5216 45.4057C48.5257 45.4168 48.5339 45.4257 48.5445 45.4308C48.5551 45.4359 48.5673 45.4367 48.5784 45.433C50.0057 45.0165 51.4597 44.6256 52.8602 44.1319C52.879 44.1222 52.8937 44.1062 52.9018 44.0868C52.9099 44.0673 52.9108 44.0456 52.9044 44.0255C52.898 44.0054 52.8847 43.9882 52.8668 43.977C52.849 43.9658 52.8277 43.9613 52.8068 43.9643Z" fill="#263238" /><path d="M51.1422 43.3969C51.0058 43.639 50.8869 43.889 50.7853 44.1469C50.6858 44.3433 50.6252 44.5571 50.6069 44.7765C50.6069 44.8191 50.6638 44.8276 50.691 44.8015C50.8394 44.6268 50.9598 44.4301 51.0478 44.2185C51.1672 43.9884 51.2905 43.7594 51.4047 43.5236C51.4169 43.4976 51.4212 43.4686 51.4172 43.4401C51.4132 43.4117 51.4009 43.385 51.3819 43.3634C51.3631 43.3415 51.3381 43.3259 51.3102 43.3186C51.2823 43.3113 51.2528 43.3127 51.2257 43.3225C51.1892 43.3354 51.1592 43.3621 51.1422 43.3969Z" fill="#263238" /><path d="M51.3172 42.0797C51.4047 41.8638 51.4956 41.649 51.5797 41.4325C51.6632 41.2166 51.7831 40.995 51.8701 40.7677C51.8701 40.7325 51.9399 40.7677 51.9257 40.795C51.8382 41.0109 51.7831 41.2382 51.7166 41.4598C51.6502 41.6814 51.5723 41.9047 51.5007 42.1263C51.3644 42.5426 51.204 42.9492 51.0195 43.3462C51.249 43.3576 51.4781 43.3175 51.6899 43.2285C51.9067 43.0906 52.073 42.8863 52.1644 42.6462C52.2877 42.4064 52.4019 42.1598 52.5218 41.9206C52.6755 41.6607 52.8043 41.3891 52.9081 41.1058C52.9081 41.0717 52.978 41.1058 52.9638 41.1325C52.7742 41.7314 52.5248 42.3097 52.2195 42.8587C52.0899 43.0712 51.9882 43.2962 51.749 43.3785C51.4899 43.4501 51.2195 43.4694 50.9536 43.4337C50.9178 43.4337 50.8769 43.4337 50.8894 43.3802C51.0144 42.9354 51.1547 42.4996 51.3172 42.0791M50.7218 44.8001C51.2218 45.8547 51.6587 46.9052 52.1223 47.9558C52.5865 49.0064 52.9968 50.0229 53.4144 51.0683C54.2469 53.1778 54.9926 55.3205 55.6496 57.491C55.9751 58.5666 56.2725 59.6505 56.5416 60.7416C56.8361 61.8293 57.0513 62.937 57.1854 64.0558C57.3026 65.174 57.2033 66.3042 56.8928 67.3848C56.5734 68.4526 56.1622 69.4907 55.6638 70.4876C55.357 71.1189 55.2547 71.0348 55.4013 70.716C55.8189 69.8104 56.9104 67.07 56.8286 64.7649C56.7658 63.6624 56.5961 62.5686 56.3218 61.499C56.0667 60.3963 55.78 59.3011 55.4621 58.2149C54.8411 56.0712 54.103 53.9717 53.3462 51.8757C52.9201 50.7069 52.4706 49.5405 52.0337 48.3717C51.5968 47.203 51.1587 46.1297 50.6683 44.8399C50.6581 44.7876 50.7081 44.7683 50.7223 44.8001M40.4348 41.316C40.6757 41.2268 41.0166 41.3746 41.2627 41.416C41.8945 41.5229 42.5377 41.5444 43.1752 41.4802C43.4937 41.4713 43.8058 41.388 44.0865 41.2371C44.5479 40.9007 44.9435 40.4824 45.2536 40.003C45.6227 39.5128 45.9273 38.9771 46.1598 38.4092C46.1598 38.3734 46.2206 38.4092 46.2098 38.4319C46.1076 38.7558 46.0314 39.0825 45.9172 39.4007C45.7922 39.6996 45.6428 39.9882 45.4695 40.2621C45.1882 40.8134 44.7728 41.2853 44.2615 41.6342C43.6996 41.9558 42.9752 41.9268 42.349 41.9001C41.9854 41.8797 41.625 41.8321 41.2678 41.7575C41.011 41.7018 40.5797 41.6609 40.4172 41.4354C40.403 41.4205 40.395 41.4008 40.395 41.3802C40.395 41.3597 40.403 41.3399 40.4172 41.3251L40.4348 41.316Z" fill="#263238" /><path d="M30.6354 88.6535C32.865 85.2735 35.0545 81.8672 37.2036 78.4354C39.3543 75.0028 41.4708 71.5428 43.553 68.0553C44.053 67.224 44.6235 66.3956 44.9462 65.4717C45.303 64.4712 45.0979 63.5058 44.7842 62.5178C44.1857 60.7298 43.4877 58.9766 42.6933 57.2666C41.9121 55.5411 41.07 53.845 40.2422 52.1422C39.4144 50.4393 38.5473 48.7587 37.9087 46.9842C37.3274 45.2871 37.0899 43.5308 37.5519 41.7723C38.0138 40.0138 38.9791 38.4615 40.0104 36.9984C40.2774 36.6143 40.5541 36.232 40.8252 35.832C40.8467 35.8013 40.8899 35.832 40.87 35.8661C39.7411 37.3899 38.6416 39.0047 37.9797 40.795C37.6421 41.6774 37.4535 42.6099 37.4217 43.5541C37.4164 44.5124 37.5644 45.4654 37.8604 46.3768C38.4098 48.153 39.2752 49.8308 40.0831 51.5013C40.8916 53.1717 41.7536 54.8962 42.549 56.6178C43.3698 58.3492 44.0998 60.1222 44.7359 61.9297C45.007 62.732 45.3172 63.574 45.2945 64.4314C45.2746 65.3751 44.8252 66.2178 44.3592 67.0189C42.3188 70.4723 40.2321 73.8982 38.0996 77.2956C35.9586 80.7056 33.7808 84.0923 31.5666 87.4553L30.728 88.7251C30.6769 88.7774 30.603 88.7132 30.6354 88.6535Z" fill="#263238" /><path d="M55.0796 24.8495C56.3426 27.6222 62.7182 31.4438 65.7029 31.8569C67.8438 32.1478 74.3727 32.2535 76.6085 31.912C79.233 31.5086 77.2256 24.429 75.679 24.6137C71.4222 25.1103 67.8085 25.6643 66.6699 25.6643C65.2841 25.6643 57.7773 23.2523 56.8727 22.9109C54.6568 22.0853 54.3659 23.2751 55.0796 24.8495Z" fill="#FFC3BD" /><path d="M55.121 25.5883C56.7266 27.9559 62.0261 31.8275 62.0261 31.8275L62.7653 30.1911L65.2124 24.7843C65.2124 24.7843 60.3374 23.2513 57.1261 22.5513C56.749 22.4645 56.3652 22.41 55.9789 22.3883C53.9204 22.2758 53.742 23.5445 55.121 25.5883Z" fill="#007CFF" /><path d="M55.1204 25.5906C56.7261 27.9576 62.0261 31.8292 62.0261 31.8292L62.7648 30.1929C60.7846 27.89 57.7267 24.4008 55.9693 22.3957C53.9199 22.2781 53.742 23.5463 55.1204 25.5906Z" fill="#263238" /><path d="M61.0704 30.7294C61.475 29.734 63.2772 25.7266 63.7267 24.8902C63.7551 24.8311 63.8017 24.8385 63.7761 24.8902C62.9948 26.8839 62.1046 28.8333 61.1096 30.7294C61.1096 30.7652 61.054 30.7522 61.0704 30.7294Z" fill="#263238" /><path d="M74.897 24.6866C77.4856 24.3469 80.6896 23.7662 83.4027 24.919C83.7072 25.0389 83.9635 25.2577 84.1294 25.5406C85.3527 26.0372 85.4862 26.7645 85.4862 26.7645C86.4873 27.5758 86.5231 28.3406 86.5231 28.3406C86.5231 28.3406 87.9504 28.9247 87.7379 29.8645C87.4987 30.9043 85.5345 29.8645 84.1061 29.9497C80.8265 30.1543 76.235 31.3599 75.2879 31.5122L74.897 24.6866Z" fill="#FFC3BD" /><path d="M80.5218 27.7462C81.4803 27.6804 82.4422 27.6804 83.4007 27.7462C84.4456 27.8786 85.4786 28.0882 86.4922 28.374C86.4954 28.3752 86.4987 28.3757 86.5021 28.3754C86.5054 28.3751 86.5086 28.374 86.5115 28.3723C86.5176 28.3694 86.5223 28.3642 86.5245 28.3578C86.5268 28.3514 86.5264 28.3444 86.5235 28.3382C86.5202 28.3325 86.5149 28.3281 86.5087 28.3257C84.6067 27.5775 82.5354 27.3672 80.5218 27.7178C80.4752 27.711 80.4752 27.7502 80.5218 27.7462Z" fill="#FFC3BD" /><path d="M80.1134 25.7852C81.0549 25.7852 82.7106 25.8318 85.481 26.7983L80.1134 25.7852ZM85.481 26.7806C83.8217 25.9403 81.9609 25.5795 80.1077 25.7386C80.056 25.7386 80.0492 25.7818 80.1077 25.7852L85.481 26.7806ZM79.9112 24.7125C81.5163 24.7943 82.5117 25.0517 84.1208 25.5358L79.9112 24.7125ZM84.1208 25.5181C82.3498 24.7767 81.6237 24.7068 79.9038 24.6426C79.8487 24.6653 79.8509 24.7119 79.9038 24.7119L84.1208 25.5181Z" fill="#263238" /><path d="M80.171 30.4482C79.7539 30.6426 79.8551 30.8624 80.6528 31.0465C81.4505 31.2306 84.7562 31.2857 84.6403 32.442C84.5238 33.5977 79.8551 33.4511 78.5533 33.042C77.2511 32.6329 75.2886 31.513 75.2886 31.513C76.213 31.3596 78.3818 30.7715 80.171 30.4482Z" fill="#FFC3BD" /><path d="M58.5472 29.2702C58.5268 29.777 58.4809 30.2815 58.4097 30.7838C58.4078 30.7734 58.4088 30.7627 58.4126 30.7529C58.4164 30.7431 58.4229 30.7345 58.4313 30.7281C58.5273 30.4639 58.6239 30.1923 58.7399 29.9423H58.7666C58.7097 30.2207 58.6258 30.491 58.5148 30.7531C58.4631 30.8781 58.4131 31.0014 58.358 31.1247C58.322 31.3481 58.282 31.5709 58.2381 31.7929C58.1512 32.2111 57.7569 33.9855 57.6552 34.2855C57.637 34.348 57.562 34.3338 57.5677 34.2628C57.5893 33.9355 57.9245 32.0878 57.9853 31.6679C58.1148 30.8565 58.2887 30.052 58.5058 29.2594C58.5205 29.2435 58.5506 29.2554 58.5472 29.2702Z" fill="#263238" /><path d="M55.5156 42.5456C54.7361 42.9456 53.7315 43.5671 49.2145 42.6774C48.9279 42.6187 48.6439 42.5479 48.3633 42.4649C44.1281 41.191 39.0349 36.2007 38.7588 35.8541C38.7588 35.8541 45.4895 24.9251 54.1008 21.4893C55.1713 21.0654 56.9247 21.8836 57.89 22.9024C58.2361 23.2598 58.9923 25.9507 58.7031 28.1228C58.4139 30.295 56.2935 42.1456 55.5156 42.5456Z" fill="#007CFF" /><path d="M54.9094 28.0176C54.9412 28.4795 54.8769 28.943 54.7207 29.3789C54.6829 28.9168 54.7473 28.452 54.9094 28.0176ZM51.9719 35.4681C51.9912 35.6982 51.9832 35.9301 51.9491 36.1579C51.9207 36.388 51.865 36.6136 51.7832 36.8301C51.7628 36.5995 51.7706 36.3673 51.8065 36.1386C51.8368 35.909 51.8916 35.6855 51.9719 35.4681ZM46.9844 29.8329C47.0182 30.2963 46.9538 30.7615 46.7952 31.1982C46.7595 30.7348 46.824 30.2692 46.9844 29.8329ZM56.8281 35.0812C56.8463 35.3122 56.8387 35.5427 56.8054 35.7727C56.777 36.0028 56.7207 36.2283 56.6372 36.4443C56.6058 35.9814 56.6708 35.5176 56.8281 35.0812ZM50.4264 28.8698C50.6554 28.8501 50.886 28.8571 51.1133 28.8908C51.3436 28.92 51.5671 28.9761 51.7838 29.059C51.323 29.0935 50.8602 29.029 50.4264 28.8698ZM44.7633 34.246C45.2248 34.2131 45.6881 34.2777 46.123 34.4357C45.6614 34.4722 45.1973 34.4074 44.7633 34.246ZM51.4338 40.063C51.8949 40.0309 52.3575 40.0961 52.7917 40.2545C52.3306 40.2901 51.8671 40.2248 51.4338 40.063ZM43.0167 37.9499C43.4787 37.9198 43.9418 37.9874 44.3758 38.1482C43.9137 38.1836 43.4494 38.1162 43.0167 37.9499ZM54.8667 22.7823C55.328 22.7493 55.7911 22.8138 56.2258 22.9715C55.7645 23.0045 55.3015 22.9401 54.8667 22.7823ZM53.2167 24.6477C53.0695 24.8252 52.9012 24.9841 52.7156 25.121C52.535 25.2615 52.3393 25.3813 52.1321 25.4783C52.2791 25.3015 52.4444 25.1443 52.6281 25.0068C52.8099 24.8647 53.0077 24.7448 53.2167 24.6477ZM44.2423 29.942C44.0927 30.1174 43.9234 30.2751 43.7378 30.4119C43.5594 30.5555 43.3633 30.6756 43.1542 30.7693C43.3042 30.5943 43.4741 30.4363 43.6594 30.2994C43.8383 30.1573 44.0344 30.0374 44.2423 29.942ZM55.452 32.3301C55.302 32.5069 55.1336 32.6641 54.9469 32.8016C54.7684 32.9448 54.5719 33.0647 54.3639 33.1596C54.5133 32.9828 54.6826 32.8238 54.8684 32.6857C55.0479 32.5441 55.2439 32.4247 55.452 32.3301ZM48.1923 36.9079C48.0419 37.0848 47.8736 37.2426 47.6872 37.3812C47.5073 37.524 47.3128 37.6431 47.1037 37.7386C47.2541 37.5617 47.4224 37.4037 47.6088 37.2647C47.7892 37.1244 47.9853 37.0047 48.1923 36.9079ZM58.2321 26.0744C58.0817 26.2513 57.9133 26.4092 57.727 26.5482C57.5464 26.6885 57.3506 26.8082 57.1434 26.9051C57.2938 26.7289 57.4622 26.5717 57.6486 26.4335C57.8289 26.291 58.0234 26.1713 58.2321 26.0744ZM58.0775 31.3823C57.901 31.2325 57.7424 31.0628 57.6048 30.8766C57.4628 30.697 57.343 30.5009 57.248 30.2926C57.4253 30.441 57.5823 30.6085 57.719 30.7948C57.8592 30.9778 57.9787 31.1736 58.0775 31.3823ZM41.9008 35.0357C41.7241 34.887 41.5655 34.718 41.4281 34.5323C41.2874 34.3518 41.1676 34.1559 41.0713 33.9482C41.4185 34.2511 41.7007 34.6207 41.9008 35.0357ZM56.1139 39.3846C55.9367 39.237 55.7782 39.0683 55.6417 38.8823C55.5018 38.7013 55.3821 38.5054 55.2849 38.2982C55.4614 38.4479 55.6184 38.6158 55.7559 38.8022C55.8972 38.9825 56.0166 39.1766 56.1139 39.3846ZM48.9912 26.9903C48.8145 26.8419 48.6559 26.6733 48.5184 26.488C48.3788 26.3065 48.2592 26.1105 48.1616 25.9033C48.3385 26.0518 48.4955 26.2193 48.6327 26.4056C48.7728 26.5878 48.8921 26.7825 48.9906 26.9897M49.1162 41.8596C48.9395 41.7109 48.7808 41.5419 48.6434 41.3562C48.5034 41.175 48.3838 40.9789 48.2866 40.7715C48.4639 40.9193 48.623 41.088 48.7594 41.2738C48.9003 41.456 49.0192 41.6513 49.1162 41.8596ZM50.3258 33.0886C50.151 32.9387 49.9935 32.7697 49.8565 32.5846C49.713 32.406 49.5931 32.2097 49.4997 32.0005C49.6758 32.1509 49.8323 32.3194 49.969 32.5062C50.1111 32.6852 50.2304 32.8812 50.3258 33.0886Z" fill="white" /><path d="M56.3103 25.8074C55.2114 25.6034 54.1785 23.329 54.0483 21.7977C54.0483 21.6943 54.5603 20.8614 55.1472 19.8205C55.504 19.1858 55.8785 18.471 56.1785 17.7921C56.241 17.6563 56.5989 18.1188 56.5989 18.1188L57.3807 18.7034L59.9603 20.6864C59.3456 21.4246 58.8238 22.2354 58.4063 23.1006C58.3655 23.1948 58.3339 23.2928 58.312 23.3932V23.4415C58.1535 24.021 57.3631 25.9983 56.3103 25.8074Z" fill="#FFC3BD" /><path d="M58.3117 23.4088V23.4588C58.1587 23.4107 58.0096 23.351 57.8657 23.2804C55.0435 21.96 56.2924 18.1702 56.2924 18.1702L57.4003 18.7191L59.9799 20.702C59.3657 21.4404 58.8439 22.251 58.4259 23.1157C58.3789 23.2096 58.3407 23.3078 58.3117 23.4088ZM62.9265 12.6196C62.9265 12.6196 64.2162 13.4395 64.2236 14.8992C64.2305 16.3594 63.6401 17.7867 63.51 17.8401C63.3799 17.8935 62.9265 12.6196 62.9265 12.6196Z" fill="#263238" /><path d="M56.5172 13.3799C55.7417 14.9219 55.7826 19.7458 56.7513 20.9964C58.1553 22.8009 60.8541 23.4225 62.4956 21.6401C64.0797 19.9106 63.9229 13.6157 62.7882 12.4418C61.1468 10.6998 57.6661 11.1106 56.5172 13.3799Z" fill="#FFC3BD" /><path d="M60.6336 17.2446C60.6336 17.2446 60.6069 17.2628 60.6103 17.273C60.6285 17.6037 60.5927 17.9878 60.2983 18.1003V18.1168C60.6642 18.0628 60.7035 17.5571 60.6336 17.2446Z" fill="#263238" /><path d="M60.3349 16.9107C59.7997 16.8823 59.7872 17.9437 60.2792 17.9704C60.7718 17.9971 60.7769 16.934 60.3349 16.9107ZM62.4326 17.2442C62.4364 17.2442 62.44 17.245 62.4434 17.2465C62.4467 17.2485 62.4497 17.2508 62.4525 17.2533C62.4552 17.2562 62.4572 17.2597 62.4582 17.2636C62.4593 17.2673 62.4597 17.2711 62.4593 17.2749C62.4417 17.6033 62.4803 17.9897 62.7764 18.0988V18.1147C62.4093 18.0499 62.3593 17.5465 62.4326 17.2442Z" fill="#263238" /><path d="M62.7333 16.8981C63.2685 16.8663 63.2861 17.9294 62.7975 17.9578C62.3088 17.9862 62.2895 16.9248 62.7333 16.8981ZM60.0026 16.1032C60.142 16.0638 60.2787 16.0172 60.4128 15.9634C60.5658 15.9357 60.7052 15.8577 60.8088 15.7419C60.8343 15.6994 60.845 15.6498 60.8392 15.6006C60.8335 15.5515 60.8116 15.5057 60.777 15.4703C60.6986 15.4098 60.6054 15.3715 60.5071 15.3595C60.4088 15.3475 60.3091 15.3622 60.2185 15.4021C60.0201 15.4575 59.8496 15.5852 59.7407 15.76C59.7166 15.8006 59.705 15.8474 59.7073 15.8946C59.7096 15.9417 59.7257 15.9871 59.7536 16.0252C59.7815 16.0633 59.82 16.0923 59.8643 16.1087C59.9085 16.1251 59.9566 16.1281 60.0026 16.1174V16.1032ZM63.0929 16.5714C62.9511 16.5404 62.8112 16.5018 62.6736 16.4555C62.5197 16.4353 62.3771 16.3641 62.2685 16.2532C62.2405 16.2125 62.2271 16.1636 62.2304 16.1143C62.2337 16.065 62.2535 16.0182 62.2867 15.9816C62.3617 15.9175 62.4526 15.8747 62.5499 15.8577C62.6471 15.8407 62.7472 15.8501 62.8395 15.885C63.0384 15.9277 63.2157 16.0424 63.3355 16.2072C63.3526 16.2363 63.3634 16.2688 63.367 16.3023C63.3707 16.3359 63.3671 16.3699 63.3566 16.402C63.3462 16.4342 63.329 16.4637 63.3063 16.4887C63.2835 16.5137 63.2557 16.5335 63.2247 16.547C63.1835 16.5657 63.1376 16.5716 63.0929 16.564V16.5714ZM59.8878 20.3691C59.9662 20.4475 60.0412 20.5475 60.1605 20.5634C60.2838 20.5634 60.4054 20.5384 60.5174 20.4884C60.5174 20.4884 60.5372 20.4884 60.5174 20.5066C60.4706 20.572 60.4067 20.6233 60.3328 20.6547C60.2588 20.6862 60.1776 20.6967 60.098 20.685C60.0318 20.6664 59.9723 20.629 59.9268 20.5774C59.8812 20.5258 59.8515 20.4622 59.8412 20.3941L59.8389 20.3845L59.8412 20.3748L59.8469 20.3669L59.8554 20.3617C59.8609 20.3591 59.8671 20.3583 59.873 20.3595C59.8791 20.3606 59.884 20.3638 59.8878 20.3691ZM61.7941 18.9623C61.7941 18.9623 61.8083 19.4532 61.7941 19.6765C61.3458 19.6285 60.893 19.6441 60.4492 19.7231C60.3435 19.7458 60.356 19.6623 60.4759 19.6066C60.8331 19.4418 61.2318 19.3889 61.6196 19.4549C61.6424 19.3924 61.5799 18.6958 61.6196 18.6975C61.7952 18.7345 61.9657 18.7941 62.1259 18.8759C62.1259 17.8487 61.9617 16.8322 61.9776 15.8049C61.9776 15.7968 61.9808 15.789 61.9866 15.7832C61.9923 15.7775 62.0001 15.7742 62.0083 15.7742C62.0162 15.7742 62.0232 15.7773 62.0293 15.7833C62.0322 15.7861 62.0346 15.7894 62.0362 15.7931C62.0377 15.7969 62.0385 15.8009 62.0384 15.8049C62.2458 16.8924 62.3429 17.9981 62.3293 19.1049C62.3333 19.2333 61.8816 19.0089 61.7941 18.9623Z" fill="#263238" /><path d="M61.1384 19.5078C61.0219 19.8136 60.8065 20.0716 60.5264 20.2408C60.0861 20.4192 59.9435 20.0192 60.3855 19.6561C60.6181 19.5386 60.8786 19.4873 61.1384 19.5078Z" fill="#263238" /><path d="M60.6789 20.1496C60.6329 20.1878 60.5811 20.2185 60.5255 20.2405C60.1204 20.403 59.9602 20.0797 60.2863 19.7524C60.5079 19.7706 60.7607 19.853 60.6789 20.1496Z" fill="#FFC3BD" /><path d="M56.294 17.5101C56.8287 17.6726 57.4389 16.0681 57.6139 15.4663C57.7656 14.93 57.94 13.197 57.9707 13.0771C58.0008 12.9578 60.3025 14.605 61.6457 14.2067C62.9889 13.8084 63.8878 12.5987 63.9076 12.0408C63.9275 11.484 62.2576 10.0453 60.8162 9.97373C59.3747 9.90214 57.4156 11.7249 57.4156 11.7249C57.6527 11.4559 57.8669 11.1699 58.0582 10.8669C58.0349 10.8027 57.102 11.176 56.9287 11.8408C56.9287 11.8408 57.1162 11.0027 57.0463 10.9885C56.977 10.9743 56.2508 11.6891 56.4133 12.2391C56.4133 12.2391 55.5747 12.9663 55.4986 13.7635C55.4213 14.5601 55.669 17.3169 56.294 17.5101Z" fill="#263238" /><path d="M62.5909 13.9098C62.1942 14.1261 61.7664 14.2796 61.3227 14.3649C60.8231 14.4101 60.3202 14.3299 59.8596 14.1314C59.3972 13.9534 58.968 13.6991 58.5897 13.3791C58.2545 13.1036 57.9545 12.7678 57.596 12.5229C57.5051 12.4587 57.4034 12.5911 57.4585 12.6712C58.0562 13.5174 58.9108 14.1484 59.8954 14.4706C60.3527 14.6198 60.8408 14.6476 61.3121 14.5515C61.7834 14.4554 62.2217 14.2386 62.584 13.9223C62.584 13.9223 62.5982 13.9098 62.5909 13.9098Z" fill="#263238" /><path d="M56.8536 17.3666C56.8536 17.3666 56.0257 15.6854 55.278 15.9575C54.5308 16.2286 54.9877 18.5547 55.7547 18.982C55.9398 19.1014 56.1647 19.1425 56.3801 19.0962C56.5955 19.05 56.7837 18.9202 56.9036 18.7354L56.9195 18.707L56.8536 17.3666Z" fill="#FFC3BD" /><path d="M55.4268 16.6211C55.4268 16.6211 55.4092 16.6211 55.4268 16.6438C55.962 16.9586 56.1728 17.5194 56.3012 18.104C56.277 18.0424 56.2398 17.9867 56.192 17.9408C56.1443 17.8949 56.0872 17.8599 56.0246 17.8381C55.962 17.8163 55.8955 17.8084 55.8296 17.8148C55.7637 17.8212 55.6999 17.8417 55.6427 17.8751C55.6251 17.8751 55.6427 17.9075 55.6427 17.9075C55.7467 17.8918 55.8531 17.9085 55.9473 17.9555C56.0414 18.0025 56.1188 18.0774 56.1688 18.17C56.2522 18.3268 56.3188 18.4906 56.3688 18.6614C56.3847 18.7165 56.4813 18.7023 56.4722 18.6398C56.5989 17.9126 56.1904 16.812 55.4268 16.6211Z" fill="#263238" /><path d="M51.3876 24.8778C50.804 28.1846 54.3842 39.3306 56.6785 41.4119C60.091 44.5068 66.4029 46.5414 68.6916 47.2045C69.6893 47.496 72.4472 40.4687 71.437 40.0039C69.2768 39.0198 62.5677 36.9187 61.929 36.3931C61.2898 35.8681 57.4421 30.2988 55.7279 27.1022C53.4387 22.784 51.6773 23.2039 51.3876 24.8778Z" fill="#FFC3BD" /><path d="M69.7361 39.3857C69.7361 39.3857 72.7173 40.1471 74.6372 40.7346C75.7071 41.0636 77.4997 41.7909 78.1793 42.3926C79.1929 43.2948 82.8611 47.7085 81.8117 48.6232C80.8611 49.4522 78.1901 45.2568 78.1901 45.2568C78.1901 45.2568 81.2571 49.2499 79.9406 50.0363C78.6242 50.8221 76.2122 45.6801 76.2122 45.6801C76.2122 45.6801 79.2696 49.8539 77.7747 50.4471C76.644 50.8937 74.2173 46.359 74.2173 46.359C74.2173 46.359 76.8236 49.967 75.6747 50.4778C74.4105 51.0437 71.9111 46.9971 71.9111 46.9971C68.7798 47.7977 66.9565 46.9039 65.7469 46.1198C65.469 45.9284 69.7361 39.3857 69.7361 39.3857Z" fill="#FFC3BD" /><path d="M77.2186 43.8058C77.5971 44.2615 77.9823 44.7223 78.3175 45.2098C78.6546 45.6967 78.9684 46.1994 79.2579 46.716C79.652 47.3839 79.9675 48.095 80.1982 48.8354C79.6971 47.5507 78.5232 45.7854 78.1874 45.2973C77.8522 44.8098 77.5397 44.3041 77.2079 43.8166C77.1919 43.8053 77.2101 43.7939 77.2186 43.8058ZM75.4374 44.9865C76.0851 45.5007 77.4408 47.4109 77.9618 48.6922C77.9846 48.7456 77.9618 48.7422 77.9243 48.6922C77.1057 47.4504 76.2731 46.2179 75.4266 44.995C75.4141 44.9865 75.4283 44.9791 75.4374 44.9865ZM73.726 45.7348C74.5301 46.5797 75.1652 47.5706 75.5971 48.6541C75.6169 48.7058 75.5772 48.6973 75.5454 48.6541C74.9408 47.6695 74.4749 46.9013 73.6936 45.7598C73.6811 45.7348 73.7078 45.7081 73.726 45.7348Z" fill="#263238" /><path d="M54.6388 25.1178C51.9791 21.3496 50.386 22.745 50.9195 26.8382C51.4206 30.6655 52.7604 35.236 52.7604 35.236L59.4644 31.9769C59.4644 31.9769 56.3337 27.5337 54.6388 25.1178Z" fill="#007CFF" /><path d="M56.2573 26.6019C56.3465 26.8138 56.4107 27.036 56.4482 27.2627C56.4892 27.4922 56.5034 27.7233 56.4909 27.9558C56.4056 27.7416 56.3457 27.5181 56.3124 27.2899C56.2662 27.0637 56.2477 26.8326 56.2573 26.6019ZM52.2852 28.8928C52.7132 28.7189 53.1737 28.6394 53.6352 28.6598C53.4219 28.7454 53.2013 28.8051 52.9732 28.8388C52.7471 28.8843 52.5164 28.9024 52.2852 28.8928ZM52.4573 23.0303C52.2758 23.4565 52.0065 23.8396 51.667 24.1547C51.8469 23.7279 52.1162 23.3447 52.4568 23.0308M58.138 30.6547C57.9571 31.0805 57.6886 31.4634 57.3499 31.7786C57.5293 31.3519 57.798 30.9687 58.138 30.6547ZM53.4749 32.9394C53.0487 32.759 52.666 32.4893 52.3528 32.1485C52.7789 32.3297 53.1607 32.599 53.4749 32.9394Z" fill="white" /><path d="M55.5146 26.2549C55.8714 26.7475 56.2419 27.2299 56.6095 27.7145C56.5149 27.4706 56.4334 27.2217 56.3652 26.9691M56.3834 26.9691C56.4567 27.1623 56.5402 27.3515 56.6095 27.5537L56.7152 27.8464C56.7239 27.8664 56.7315 27.8867 56.7379 27.9072C57.0288 28.2844 57.3214 28.6611 57.589 29.0452C57.9192 29.5066 58.2368 29.9748 58.5578 30.4407C58.8788 30.9066 59.2021 31.4895 59.5481 31.9594C59.5799 32.0009 58.9646 32.2526 58.8987 32.2833C58.8328 32.314 58.797 32.26 58.8612 32.2401C58.9254 32.2202 59.414 31.9185 59.4197 31.9134C59.1004 31.4395 58.7288 31.0055 58.3936 30.5464C58.0584 30.0873 57.7265 29.6299 57.4038 29.1657C56.747 28.2202 56.1459 27.2395 55.493 26.2907L56.3834 26.9691ZM51.0782 29.5464C51.1858 29.7407 51.2828 29.9401 51.3692 30.1447L51.4799 30.3986C51.3265 29.6731 51.189 28.9384 51.0288 28.2134C51.3437 29.3922 51.6456 30.5745 51.9345 31.76C52.2061 32.893 52.5362 34.0384 52.693 35.1941C52.8965 35.1052 53.1071 35.0336 53.3226 34.9799C53.1066 35.1209 52.8735 35.2335 52.6288 35.3151C52.2773 34.1397 51.9854 32.9473 51.7544 31.7424C51.6816 31.4151 51.6135 31.0884 51.5442 30.7611C51.4692 30.5628 51.3782 30.3736 51.2873 30.177C51.1964 29.9805 51.1089 29.7623 51.0339 29.5549C51.0515 29.5447 51.0714 29.5322 51.0782 29.5464Z" fill="#263238" /><path d="M52.8592 34.0995C53.7922 33.5745 57.6905 31.554 58.5677 31.1802C58.6302 31.1535 58.6626 31.1802 58.6001 31.2211C56.7515 32.297 54.8459 33.2717 52.8916 34.141C52.8558 34.1461 52.8325 34.1137 52.8592 34.0995Z" fill="#263238" /><path d="M159.323 110.856H70.5049L83.3458 37.9077H172.163L159.323 110.856Z" fill="#115CF2" /><path d="M113.744 30.353H84.6776L83.3452 37.9087H114.451L113.744 30.353Z" fill="#007CFF" /><g opacity="0.1"><path d="M159.498 110.858H70.6797L83.5212 37.9106H172.339L159.498 110.858Z" fill="black" /><path d="M145.318 30.3545H116.252L114.919 37.9107H146.026L145.318 30.3545Z" fill="#007CFF" /></g><path d="M81.5393 33.4373C81.3444 32.6021 81.3444 31.7328 81.5393 30.897C81.733 31.7322 81.733 32.6015 81.5393 33.4373ZM78.9489 34.1845C78.6528 33.8759 78.3975 33.5307 78.1893 33.1572C77.9711 32.7904 77.7997 32.3978 77.6791 31.9885C77.9762 32.2958 78.2325 32.6407 78.4404 33.014C78.658 33.3816 78.8291 33.7748 78.9489 34.1845ZM77.0916 36.1271C76.2715 35.8781 75.52 35.4431 74.8955 34.8561C75.3042 34.9759 75.6962 35.1464 76.0626 35.3634C76.4359 35.5742 76.7813 35.8311 77.0916 36.1271ZM76.4393 38.7419C75.6049 38.9371 74.7367 38.9371 73.9023 38.7419C74.7367 38.5467 75.6049 38.5467 76.4393 38.7419ZM77.1961 41.3294C76.8874 41.625 76.5429 41.8808 76.1705 42.0907C75.8055 42.3093 75.4141 42.4804 75.0058 42.5998C75.3131 42.3021 75.6579 42.0456 76.0313 41.8367C76.3958 41.6176 76.7874 41.4471 77.1961 41.3294ZM79.1262 43.1953C78.8766 44.0159 78.4426 44.7686 77.8575 45.3958C77.9753 44.9862 78.1469 44.594 78.3677 44.2294C78.5751 43.8535 78.83 43.506 79.1262 43.1953ZM81.7444 43.8396C81.8461 44.2549 81.8944 44.6822 81.8893 45.11C81.8956 45.538 81.8469 45.965 81.7444 46.3805C81.6396 45.9654 81.5905 45.5381 81.5984 45.11C81.5937 44.6821 81.6427 44.2553 81.7444 43.8396ZM84.3262 43.1072C84.6234 43.4152 84.8791 43.7606 85.0881 44.1345C85.3069 44.4998 85.4773 44.893 85.595 45.3027C85.0097 44.6753 84.5757 43.9224 84.3262 43.1015V43.1072ZM86.1893 41.1504C87.0067 41.4018 87.756 41.8363 88.3802 42.4208C87.9715 42.3031 87.5799 42.1325 87.2154 41.9134C86.8418 41.7046 86.4968 41.4481 86.1893 41.1504ZM86.8342 38.5345C87.6691 38.339 88.5379 38.339 89.3728 38.5345C88.5379 38.7299 87.6691 38.7299 86.8342 38.5345ZM86.0853 35.947C86.3946 35.6518 86.7397 35.3966 87.1126 35.1873C87.4766 34.9662 87.8682 34.7943 88.2773 34.676C87.6541 35.2625 86.904 35.6974 86.0853 35.947ZM84.1501 34.0822C84.2705 33.6731 84.441 33.2811 84.658 32.914C84.867 32.5404 85.1231 32.1951 85.42 31.8867C85.1697 32.7056 84.7357 33.4565 84.1512 34.0822H84.1501Z" fill="#263238" /><path d="M159.191 110.856H70.182L57.5405 40.5576H146.554L159.191 110.856Z" fill="#007CFF" /><path d="M88.388 83.5335C90.4317 83.5335 92.088 81.8738 92.088 79.8273C92.088 77.7807 90.4317 76.1216 88.388 76.1216C86.3442 76.1216 84.688 77.7812 84.688 79.8273C84.688 81.8733 86.3448 83.5335 88.388 83.5335ZM117.633 83.5335C119.676 83.5335 121.332 81.8738 121.332 79.8273C121.332 77.7807 119.676 76.1216 117.633 76.1216C116.651 76.1226 115.709 76.5136 115.016 77.2084C114.322 77.9033 113.932 78.8452 113.932 79.8273C113.932 81.8738 115.589 83.5335 117.633 83.5335ZM111.199 93.6511L110.887 93.054C110.86 93.0023 107.924 87.5528 102.021 87.5528C96.6494 87.5528 95.3067 92.6801 95.2709 92.9034L95.1096 93.5551L93.8056 93.2386L93.9613 92.5841C93.9613 92.5204 95.5641 86.2045 102.021 86.2045C108.735 86.2045 111.944 92.1738 112.079 92.4301L112.389 93.0131L111.199 93.6511Z" fill="white" /><path d="M153.671 79.5464L153.61 79.2526C153.799 79.2117 153.986 79.1691 154.172 79.1191L154.245 79.4106C154.055 79.4595 153.863 79.5048 153.671 79.5464ZM155.374 79.0731L155.279 78.8004C155.644 78.6739 156.006 78.5361 156.363 78.3873L156.475 78.6572C156.116 78.8089 155.747 78.9481 155.374 79.0731ZM157.545 78.1424L157.413 77.8856C157.758 77.7086 158.094 77.514 158.419 77.3026L158.571 77.5492C158.245 77.7746 157.903 77.9723 157.545 78.1424ZM159.55 76.9078L159.372 76.672C159.681 76.4441 159.982 76.2059 160.275 75.9578L160.476 76.1697C160.171 76.4345 159.879 76.6828 159.55 76.9203V76.9078ZM161.334 75.3555L161.122 75.1532C161.389 74.86 161.643 74.5691 161.876 74.2782L162.106 74.4566C161.865 74.7722 161.61 75.0762 161.34 75.3674L161.334 75.3555ZM162.777 73.4862L162.53 73.3305C162.73 73.0044 162.909 72.6663 163.065 72.3163L163.333 72.4356C163.168 72.8015 162.981 73.1553 162.772 73.497L162.777 73.4862ZM163.743 71.3191L163.45 71.2384C163.556 70.8719 163.635 70.4982 163.687 70.1203L163.978 70.1578C163.93 70.5549 163.85 70.9475 163.739 71.3316L163.743 71.3191ZM164.065 68.9839H163.775V68.8998C163.775 68.5424 163.753 68.185 163.719 67.8276L164.01 67.7992C164.049 68.1633 164.069 68.5284 164.069 68.8947L164.065 68.9839ZM163.541 66.6987C163.463 66.3218 163.366 65.9492 163.25 65.5822L163.541 65.4947C163.661 65.8693 163.758 66.2498 163.832 66.6362L163.541 66.6987ZM162.851 64.4992C162.708 64.1407 162.548 63.789 162.373 63.4453L162.635 63.3146C162.814 63.6722 162.975 64.0295 163.118 64.3867L162.851 64.4992ZM161.83 62.4174C161.652 62.0992 161.45 61.764 161.247 61.4186L161.495 61.2634C161.7 61.5944 161.894 61.9317 162.078 62.2748L161.83 62.4174ZM160.612 60.4345C160.4 60.1146 160.184 59.7896 159.961 59.4663L160.203 59.3021C160.425 59.6265 160.64 59.9498 160.853 60.2776L160.612 60.4345ZM159.299 58.5032C159.081 58.1811 158.86 57.8578 158.644 57.5367L158.885 57.372C159.101 57.6936 159.32 58.0168 159.538 58.335L159.299 58.5032ZM157.991 56.5521C157.779 56.2237 157.571 55.893 157.367 55.56L157.615 55.4083C157.82 55.7407 158.027 56.0674 158.235 56.3947L157.991 56.5521ZM156.776 54.5475C156.577 54.1896 156.394 53.843 156.227 53.5078L156.489 53.3754C156.654 53.7095 156.846 54.0538 157.024 54.4038L156.776 54.5475ZM155.739 52.443C155.589 52.0814 155.449 51.7153 155.321 51.3453L155.613 51.2509C155.735 51.6081 155.872 51.9653 156.023 52.3225L155.739 52.443ZM154.983 50.2146C154.885 49.829 154.803 49.4397 154.737 49.0475L155.03 49.0015C155.094 49.3748 155.172 49.7538 155.269 50.1265L154.983 50.2146ZM154.589 47.8862C154.559 47.5262 154.544 47.1651 154.544 46.8038V46.7072H154.837V46.8004C154.829 47.1542 154.844 47.5072 154.88 47.8595L154.589 47.8862ZM154.88 45.5509L154.589 45.5208C154.593 45.439 154.6 45.3575 154.613 45.2765C154.646 44.9634 154.691 44.6526 154.743 44.3538L155.035 44.4043C154.983 44.7042 154.941 45.0054 154.907 45.3078C154.919 45.3919 154.915 45.4782 154.894 45.56L154.88 45.5509ZM155.285 43.2782L154.994 43.1998C155.101 42.8157 155.227 42.4384 155.367 42.0793L155.638 42.185C155.506 42.5461 155.392 42.9141 155.299 43.2873L155.285 43.2782ZM169.811 41.5776C169.644 41.5735 169.477 41.5579 169.312 41.5311C169.085 41.4982 168.859 41.4602 168.634 41.4169L168.692 41.1254C168.908 41.1651 169.132 41.2044 169.354 41.2362C169.513 41.2559 169.67 41.2703 169.826 41.2793L169.811 41.5776ZM171.01 41.4867L170.948 41.1953C171.314 41.1186 171.664 40.9823 171.986 40.7918L172.138 41.0396C171.794 41.2513 171.418 41.4052 171.025 41.4953L171.01 41.4867ZM156.125 41.1401L155.865 41.0021C156.051 40.654 156.256 40.3163 156.48 39.9907L156.719 40.1589C156.506 40.4775 156.313 40.8074 156.139 41.1487L156.125 41.1401ZM167.494 41.122C167.118 41.0019 166.748 40.8623 166.386 40.7038L166.506 40.4356C166.855 40.5913 167.211 40.7265 167.576 40.8413L167.494 41.122ZM173.025 40.2106L172.782 40.0481C172.99 39.7461 173.113 39.3936 173.139 39.0276L173.431 39.0492C173.406 39.4681 173.269 39.8725 173.033 40.2197L173.025 40.2106ZM165.329 40.168C164.987 39.9688 164.657 39.7511 164.34 39.5157L164.518 39.2816C164.827 39.5104 165.147 39.7206 165.48 39.9123L165.329 40.168ZM157.443 39.2566L157.227 39.06C157.493 38.7657 157.774 38.4882 158.071 38.2276L158.263 38.447C157.976 38.7034 157.706 38.9761 157.452 39.2651L157.443 39.2566ZM163.447 38.7634C163.156 38.4953 162.88 38.2113 162.621 37.9129L162.84 37.7197C163.092 38.0102 163.359 38.2862 163.64 38.5475L163.447 38.7634ZM172.932 37.9703C172.764 37.6364 172.533 37.3389 172.25 37.0947L172.443 36.8765C172.752 37.1457 173.007 37.4721 173.192 37.8379L172.932 37.9703ZM159.167 37.7396L158.999 37.5004C159.325 37.2774 159.663 37.0717 160.01 36.8839L160.148 37.1413C159.812 37.3265 159.487 37.529 159.173 37.7487L159.167 37.7396ZM161.889 36.9805C161.777 36.8218 161.67 36.6596 161.568 36.4941L161.19 36.6447L161.079 36.3748L161.413 36.2407C161.361 36.1532 161.309 36.0623 161.259 35.9782L161.513 35.835C161.571 35.9373 161.629 36.0407 161.691 36.1282C161.853 36.0695 162.019 36.0134 162.189 35.96L162.274 36.2532C162.13 36.2958 161.982 36.3441 161.848 36.3924C161.935 36.5386 162.032 36.678 162.139 36.8106L161.889 36.9805ZM171.314 36.4782C171.032 36.3372 170.739 36.2177 170.439 36.1208L170.247 36.06L170.33 35.7691L170.529 35.8333C170.842 35.9326 171.145 36.0557 171.438 36.2026L171.314 36.4782ZM163.389 35.9549L163.33 35.6651C163.716 35.5782 164.105 35.508 164.497 35.4549L164.54 35.7475C164.155 35.8083 163.773 35.8816 163.4 35.9657L163.389 35.9549ZM169.134 35.7975C168.755 35.7307 168.373 35.6814 167.99 35.6498L168.013 35.3561C168.418 35.3919 168.813 35.4418 169.18 35.5083L169.134 35.7975ZM165.678 35.6276L165.659 35.3367C166.004 35.3154 166.349 35.3046 166.695 35.3043H166.839V35.5958H166.698C166.369 35.6043 166.026 35.6134 165.687 35.6367L165.678 35.6276ZM160.753 34.9328C160.594 34.5754 160.46 34.2055 160.33 33.8288L160.621 33.7379C160.744 34.1055 160.88 34.468 161.031 34.8095L160.753 34.9328ZM160.014 32.6907C159.925 32.3045 159.853 31.9145 159.798 31.522L160.091 31.4811C160.146 31.8669 160.215 32.2492 160.3 32.6191L160.014 32.6907ZM159.688 30.3532C159.673 30.0874 159.665 29.8212 159.664 29.5549V29.1742H159.955V29.5475C159.955 29.81 159.955 30.0839 159.976 30.3305L159.688 30.3532ZM160.036 28.018L159.743 27.9896C159.784 27.6032 159.839 27.2089 159.907 26.8214L160.198 26.8714C160.118 27.2525 160.058 27.6365 160.019 28.0237L160.036 28.018ZM160.434 25.7384L160.143 25.6686C160.235 25.2919 160.344 24.9112 160.463 24.5322L160.754 24.6231C160.628 24.9917 160.518 25.3655 160.425 25.7436L160.434 25.7384ZM161.134 23.5282L160.863 23.4225C161.006 23.0657 161.154 22.6918 161.333 22.3401L161.594 22.4686C161.42 22.8176 161.263 23.1755 161.125 23.5407L161.134 23.5282ZM162.137 21.447L161.884 21.2964C162.082 20.9612 162.294 20.6265 162.515 20.3049L162.754 20.4714C162.531 20.7888 162.322 21.1161 162.129 21.4521L162.137 21.447ZM163.452 19.5458L163.223 19.3669C163.468 19.0759 163.732 18.7646 164.003 18.4919L164.212 18.6936C163.94 18.9678 163.684 19.2555 163.443 19.5566L163.452 19.5458ZM165.038 17.864L164.841 17.6481C165.134 17.3873 165.425 17.1316 165.733 16.8833L165.911 17.114C165.6 17.3623 165.304 17.6163 165.019 17.8697L165.038 17.864ZM166.847 16.4208L166.681 16.1725C167.004 15.9493 167.333 15.7359 167.669 15.5328L167.819 15.7811C167.48 15.9884 167.166 16.2009 166.838 16.4208H166.847ZM168.827 15.2163L168.683 14.9589C169.031 14.7771 169.383 14.6028 169.739 14.4362L169.86 14.7021C169.49 14.868 169.146 15.043 168.81 15.222L168.827 15.2163ZM170.934 14.2464L170.827 13.9714C171.194 13.8282 171.564 13.6799 171.931 13.5725L172.025 13.864C171.653 13.9774 171.286 14.1067 170.925 14.2515L170.934 14.2464ZM173.182 13.5078L173.109 13.2151C173.302 13.1641 173.497 13.1193 173.693 13.0811L173.746 13.3725C173.746 13.3725 173.535 13.4163 173.182 13.5078Z" fill="#263238" /><path d="M178.334 11.6213C177.601 9.31446 172.606 10.1434 173.584 13.1594C174.562 16.1753 179.078 13.9315 178.334 11.6213Z" fill="#263238" /><path d="M170.877 10.6992C170.915 9.68617 172.32 8.91287 174.307 9.27026C176.295 9.62764 178.165 10.9725 178.165 10.9725C178.165 10.9725 176.203 12.1646 174.194 12.3828C172.185 12.6004 170.838 11.7055 170.877 10.6992Z" fill="#EBEBEB" /><path d="M178.163 10.9623C177.13 10.9623 176.103 10.9322 175.074 10.8998C174.045 10.868 173.012 10.8163 171.984 10.7339C173.016 10.7339 174.048 10.7606 175.075 10.793C176.103 10.8254 177.138 10.8771 178.163 10.9629" fill="#263238" /><path d="M172.873 9.79736C173.459 10.0779 174.011 10.4237 174.52 10.828C174.23 10.688 173.949 10.5317 173.678 10.3599C173.399 10.1879 173.131 10.0004 172.873 9.79736ZM175.576 10.8655C175.049 11.2929 174.477 11.6624 173.871 11.9678C174.401 11.5396 174.975 11.17 175.585 10.8655H175.576ZM175.876 10.2957C176.17 10.4377 176.428 10.6451 176.629 10.903C176.333 10.7635 176.075 10.5556 175.876 10.2957Z" fill="#263238" /><path d="M172.772 16.7063C173.392 17.5051 174.987 17.3142 176.39 15.8597C177.792 14.4051 178.53 12.2188 178.53 12.2188C178.53 12.2188 176.235 12.3972 174.482 13.3869C172.73 14.3767 172.147 15.8932 172.772 16.7063Z" fill="#EBEBEB" /><path d="M178.533 12.2109C177.695 12.795 176.878 13.4439 176.059 14.0706C175.241 14.698 174.439 15.3463 173.654 16.0149C174.495 15.4303 175.31 14.7854 176.127 14.1581C176.944 13.528 177.747 12.8787 178.533 12.2109Z" fill="#263238" /><path d="M174.915 16.2458C175.07 15.9617 175.21 15.6698 175.333 15.3702C175.454 15.0691 175.557 14.7617 175.641 14.4481C175.484 14.7311 175.345 15.0235 175.224 15.3236C175.104 15.6251 175.001 15.9325 174.915 16.2458ZM176.494 13.8032C175.812 13.7674 175.128 13.8043 174.453 13.9128C174.793 13.9321 175.134 13.9321 175.474 13.9128C175.816 13.8946 176.156 13.8581 176.494 13.8037M177.057 14.0941C177.135 13.9494 177.194 13.7975 177.235 13.6384C177.278 13.4812 177.304 13.321 177.312 13.1577C177.157 13.4467 177.07 13.7668 177.057 14.0941Z" fill="#263238" /><path d="M178.335 11.6214C178.171 11.6751 177.994 11.6787 177.828 11.6315C177.661 11.5844 177.513 11.4886 177.401 11.3566C177.262 11.1911 177.19 10.9804 177.197 10.7647C177.205 10.5491 177.291 10.3437 177.441 10.1881C177.59 10.0325 177.792 9.93748 178.007 9.92139C178.222 9.90529 178.436 9.96922 178.607 10.1009C178.766 10.2263 178.876 10.4029 178.92 10.6007C178.963 10.7985 178.936 11.0052 178.844 11.1856C178.739 11.3924 178.556 11.5492 178.335 11.6214Z" fill="#455A64" /><path d="M178.872 13.2889C178.749 13.3288 178.619 13.3409 178.491 13.3243C178.363 13.3077 178.24 13.2629 178.131 13.1931C178.022 13.1232 177.93 13.0301 177.862 12.9204C177.793 12.8108 177.75 12.6872 177.735 12.5588C177.715 12.387 177.746 12.2131 177.824 12.0588C177.883 11.9433 177.966 11.8423 178.069 11.763C178.171 11.6837 178.29 11.6282 178.417 11.6004C178.543 11.5726 178.674 11.5733 178.801 11.6024C178.927 11.6315 179.045 11.6882 179.147 11.7686C179.248 11.8489 179.331 11.9508 179.388 12.0669C179.446 12.183 179.477 12.3104 179.479 12.44C179.481 12.5695 179.455 12.6979 179.401 12.8159C179.348 12.934 179.269 13.0387 179.17 13.1225C179.083 13.1974 178.982 13.2541 178.872 13.2889Z" fill="#455A64" /><path d="M83.2977 12.5428C83.1585 13.8818 82.6499 15.1559 81.8289 16.2229C81.0078 17.2899 79.9065 18.1078 78.6477 18.5854C77.3901 19.0627 76.0245 19.181 74.7035 18.927C73.3825 18.6731 72.1581 18.0569 71.167 17.1473L67.5312 17.6831L69.3511 14.5371C68.9353 13.5129 68.7579 12.4076 68.8322 11.3047C68.9065 10.2018 69.2306 9.1303 69.7801 8.17117C70.3297 7.21278 71.0904 6.39221 72.0045 5.77174C73.3824 4.83634 75.04 4.40338 76.6993 4.54553C78.3585 4.68769 79.9184 5.39629 81.117 6.55242C81.9125 7.31982 82.5229 8.25819 82.902 9.29644C83.2811 10.3347 83.419 11.4456 83.3051 12.545L83.2977 12.5428Z" fill="white" /><path d="M83.299 12.5437C83.1639 13.8842 82.6592 15.1608 81.841 16.2312C81.4422 16.7738 80.9606 17.2503 80.4137 17.6432C79.8807 18.0505 79.2921 18.3796 78.666 18.6204C77.7216 18.9916 76.7091 19.1578 75.6955 19.1079C75.3581 19.0989 75.0219 19.0638 74.6899 19.0028C73.3486 18.7534 72.1036 18.1345 71.095 17.2159L71.1802 17.2426L67.5501 17.8L67.2842 17.8415L67.42 17.6074L69.2274 14.454V14.579C68.8004 13.5361 68.6187 12.4091 68.6962 11.2849C68.7737 10.1607 69.1084 9.06926 69.6745 8.09488C70.2307 7.12303 71.0021 6.29153 71.9296 5.66419C73.3282 4.71943 75.011 4.28879 76.6915 4.44565C78.3719 4.6025 79.946 5.33715 81.1455 6.52442C81.937 7.29906 82.5421 8.24343 82.9152 9.28618C83.2882 10.3289 83.4195 11.4428 83.299 12.5437ZM83.299 12.5437C83.4436 11.1738 83.1927 9.79103 82.576 8.55916C81.9594 7.32729 81.0028 6.29783 79.8194 5.5926C78.6371 4.88834 77.2777 4.53798 75.9023 4.5831C74.5269 4.62821 73.1933 5.06691 72.0597 5.84715C71.1714 6.47237 70.4295 7.28311 69.8853 8.22328C69.3477 9.16548 69.0312 10.2175 68.9598 11.3C68.8883 12.3899 69.0633 13.4819 69.4717 14.4949L69.495 14.5642L69.4609 14.625L67.6359 17.7625L67.5069 17.5693L71.1467 17.0659H71.1967L71.2308 17.0977C72.2024 18.0049 73.4074 18.6237 74.7109 18.8846C75.0363 18.9513 75.3652 18.9911 75.6978 19.004C76.0285 19.0278 76.3596 19.0278 76.691 19.004C77.3544 18.95 78.0074 18.8058 78.6319 18.5756C79.2584 18.3471 79.848 18.028 80.3819 17.6284C80.9249 17.2388 81.4061 16.7695 81.8092 16.2364C82.6389 15.1674 83.1548 13.8892 83.299 12.5437Z" fill="#263238" /><path d="M72.5273 15.1799C72.5178 14.9932 72.582 14.8102 72.7062 14.6705C72.8303 14.5307 73.0044 14.4453 73.1909 14.4327H73.2409C73.3389 14.4328 73.436 14.4522 73.5265 14.4898C73.617 14.5274 73.6992 14.5824 73.7685 14.6518C73.8377 14.7212 73.8926 14.8036 73.93 14.8942C73.9674 14.9848 73.9865 15.0819 73.9864 15.1799C73.9862 15.3776 73.9077 15.5673 73.7679 15.7072C73.6282 15.8472 73.4387 15.926 73.2409 15.9265C73.0544 15.9259 72.8754 15.8526 72.7421 15.7221C72.6088 15.5915 72.5317 15.4141 72.5273 15.2276V15.1799ZM72.8176 13.4537L72.6506 7.66113H73.8154L73.6472 13.4537H72.8176ZM75.2943 15.1799C75.2856 14.9935 75.35 14.8111 75.4739 14.6716C75.5978 14.5321 75.7713 14.4466 75.9574 14.4333H76.0023C76.1004 14.4331 76.1975 14.4522 76.2882 14.4897C76.3788 14.5271 76.4612 14.5821 76.5305 14.6515C76.5998 14.7209 76.6548 14.8033 76.6921 14.894C76.7295 14.9846 76.7486 15.0818 76.7483 15.1799C76.748 15.3777 76.6693 15.5673 76.5295 15.7073C76.3897 15.8472 76.2001 15.926 76.0023 15.9265C75.8152 15.9259 75.6357 15.8521 75.5023 15.7208C75.3689 15.5896 75.2922 15.4113 75.2887 15.2242C75.292 15.2097 75.2939 15.1948 75.2943 15.1799ZM75.5852 13.4537L75.4262 7.66113H76.5909L76.421 13.4537H75.5852ZM78.0568 15.1799C78.0481 14.9934 78.1126 14.8109 78.2366 14.6714C78.3606 14.5319 78.5343 14.4464 78.7205 14.4333H78.7705C78.8685 14.4331 78.9656 14.4524 79.0562 14.4898C79.1468 14.5273 79.2291 14.5823 79.2983 14.6517C79.3676 14.7211 79.4225 14.8034 79.4598 14.8941C79.4972 14.9847 79.5162 15.0819 79.5159 15.1799C79.5158 15.3776 79.4372 15.5673 79.2975 15.7072C79.1578 15.8472 78.9682 15.926 78.7705 15.9265C78.5836 15.927 78.404 15.8539 78.2705 15.7232C78.137 15.5924 78.0603 15.4144 78.0568 15.2276V15.1799ZM78.3495 13.4537L78.1801 7.66113H79.3466L79.1807 13.4537H78.3495Z" fill="black" /></g></g></g></g><defs><clipPath id="clip0_2_9098"><rect width="200" height="131.25" fill="white" transform="translate(0 0.984863)" /></clipPath></defs></svg>
                                    @* <img src="~/img/isomatric/no_data_found.svg" alt="404Error" class="img-fluid" style="margin-top:10%"> *@
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="ReportDefaultImage" class="content-center w-100" hidden>
                        <div class="container">
                            <div class="row text-center">
                                <div class="col-lg-12 my-2">
                                    @* <img src="~/img/isomatric/reportdefaultimage.svg" alt="defaultImage" class="img-fluid" style="margin-top:10%"> *@
                                    <svg class="mt-5" width="348" height="348" fill="none"><path fill="#D5F0FF" d="M30.313 135.356c-8.682 33.147 26.918 103.426 93.282 132.066 66.398 28.64 132.257 15.991 147.117-28.275 6.055-18.113-11.972-38.645-7.97-58.655 2.906-18.949 55.541-54.567 37.254-96.257-18.288-41.708-112.526-53.366-147.187-27.788-34.695 25.613-46.58 32.225-55.07 35.67-12.32 5.55-58.76 10.109-67.426 43.239" opacity=".5" /><path fill="#DFE6FF" d="M173.394 333.419c78.435 0 142.019-3.225 142.019-7.204s-63.584-7.203-142.019-7.203-142.019 3.225-142.019 7.203 63.584 7.204 142.019 7.204" /><path fill="#8DAFDB" fill-rule="evenodd" d="M254.11 323.884H74.194c11.71-71.915-10.875-149.954 0-229.263H254.11c-2.767 13.433-4.211 27.823-4.768 42.769a407 407 0 0 0-.191 6.63c-1.305 60.273 10.162 128.603 4.959 179.864" clip-rule="evenodd" /><path fill="#DBCDE4" fill-rule="evenodd" d="M78.147 88.43h172.748c-13.659 70.835 6.42 167.666 0 235.456H78.147c11.258-73.863-10.457-153.99 0-235.456" clip-rule="evenodd" /><path fill="#fff" fill-rule="evenodd" d="M72.266 78.18h175.375c-13.868 73.915 6.525 174.957 0 245.688H72.266c11.432-77.065-10.597-160.672 0-245.688" clip-rule="evenodd" /><g fill-rule="evenodd" clip-rule="evenodd" opacity=".65"><path fill="#4B06A3" d="M212.874 199.626c0-26.622-21.354-48.26-47.89-48.716v49.684h47.862c.028-.313.028-.626.028-.968" /><path fill="#1A31FB" d="M212.777 206.517h-45.129l41.057 17.083a48 48 0 0 0 4.072-17.083" /><path fill="#1EB007" d="m209.395 229.409-42.309-17.624 21.326 39.349c9.111-4.841 16.485-12.471 20.983-21.725" /><path fill="#FA6900" d="m179.981 250.191-22.237-41 .143-.086-.114-.028v-51.421c-26.536.456-47.89 22.095-47.89 48.716 0 26.906 21.809 48.716 48.716 48.716 7.687.028 14.947-1.737 21.382-4.897" /></g><path fill="#2C7EFE" d="M109.883 118.18v-15.608h6.751c1.392 0 2.61.226 3.619.679 1.009.452 1.792 1.096 2.349 1.949.557.852.818 1.861.818 3.027s-.278 2.158-.818 2.993q-.835 1.253-2.349 1.931c-1.009.453-2.21.662-3.619.662h-4.75l1.601-1.584v5.934h-3.602zm3.602-5.533-1.601-1.688h4.559q1.67 0 2.505-.731c.557-.487.818-1.148.818-2.001 0-.87-.278-1.531-.818-2.001-.557-.47-1.374-.713-2.505-.713h-4.559l1.601-1.723zm6.316 5.533-3.898-5.672h3.863l3.95 5.672zM129.856 115.274h8.491v2.906h-12.093v-15.608h11.797v2.906h-8.213v9.796zm-.261-6.42h7.517v2.818h-7.517zM141.238 118.18v-15.608h6.751c1.392 0 2.61.226 3.62.679 1.009.452 1.792 1.096 2.349 1.949.556.852.817 1.861.817 3.027s-.278 2.158-.817 3.01c-.557.853-1.34 1.497-2.349 1.949-1.01.453-2.21.679-3.62.679h-4.75l1.601-1.636v5.934h-3.602zm3.619-5.533-1.6-1.723h4.558c1.114 0 1.949-.243 2.506-.713s.818-1.131.818-1.984c0-.87-.279-1.531-.818-2.001-.557-.47-1.375-.713-2.506-.713h-4.558l1.6-1.723zM165.149 118.442c-1.236 0-2.384-.209-3.428-.609a8.3 8.3 0 0 1-2.715-1.688 8 8 0 0 1-1.792-2.557 8.1 8.1 0 0 1-.644-3.219c0-1.166.209-2.228.644-3.219a7.6 7.6 0 0 1 1.792-2.558c.766-.731 1.688-1.288 2.715-1.688a9.4 9.4 0 0 1 3.393-.609c1.235 0 2.366.209 3.41.609 1.027.4 1.932.974 2.697 1.688a8 8 0 0 1 1.792 2.558c.435.974.644 2.053.644 3.219s-.209 2.227-.644 3.219a7.6 7.6 0 0 1-1.792 2.557 8.4 8.4 0 0 1-2.697 1.688c-1.026.418-2.157.609-3.375.609m-.018-3.08a5 5 0 0 0 1.932-.365 4.6 4.6 0 0 0 1.548-1.027 4.6 4.6 0 0 0 1.044-1.583q.366-.914.366-2.036t-.366-2.036a4.9 4.9 0 0 0-1.026-1.583 4.5 4.5 0 0 0-1.549-1.027 5.1 5.1 0 0 0-1.949-.365 5 5 0 0 0-1.931.365 4.65 4.65 0 0 0-1.549 1.027c-.452.452-.8.974-1.044 1.583q-.365.914-.365 2.036c0 .731.122 1.41.365 2.019a5 5 0 0 0 1.027 1.6c.435.453.957.783 1.548 1.027.609.261 1.253.365 1.949.365M176.383 118.18v-15.608h6.751c1.392 0 2.61.226 3.619.679 1.009.452 1.792 1.096 2.349 1.949.557.852.818 1.861.818 3.027s-.278 2.158-.818 2.993q-.835 1.253-2.349 1.931c-1.009.453-2.21.662-3.619.662h-4.75l1.601-1.584v5.934h-3.602zm3.619-5.533-1.601-1.688h4.559q1.67 0 2.506-.731c.556-.487.817-1.148.817-2.001 0-.87-.278-1.531-.817-2.001-.557-.47-1.375-.713-2.506-.713h-4.559l1.601-1.723zm6.316 5.533-3.897-5.672h3.862l3.95 5.672zM195.802 118.18v-12.667h-4.993v-2.941h13.606v2.941h-5.011v12.667z" /><path fill="#153663" fill-rule="evenodd" d="M248.805 298.516c-4.124 1.079-8.421 3.219-12.319 3.254-3.376-7.987 6.612-17.47 5.272-31.807l8.37 12.162z" clip-rule="evenodd" /><path fill="#F8A780" fill-rule="evenodd" d="M247.656 270.902c1.497 6.073.192 12.354-3.254 16.06-2.105-.574-4.21-1.183-6.281-1.757-2.714.539-5.429 1.079-8.456.887-2.906-.957-5.812-1.879-8.683-2.836-1.949-.313-4.124-.644-5.551-2.488-.73-.766-.539-2.262.992-2.714-1.061-.696-2.801-1.184-2.801-2.593.035-.731.313-1.636.765-2.175-.87-.609-2.61-.887-2.61-2.523 0-.644 0-1.496.766-2.036-3.167-.992-6.803-1.253-10.214-1.722-1.27.104-3.793-2.715 0-3.933 5.203-.644 12.163.679 18.009.226 3.48 0 6.508-.87 9.553-.87-2.367-4.123-7.152-9.326-1.497-12.58 2.402-.87 2.802 8.457 8.683 10.405 3.132 2.593 4.628 6.23 6.925 9.327z" clip-rule="evenodd" /><path fill="#0479FF" fill-rule="evenodd" d="M284.458 174.125c5.516 2.731 16.635 10.735 22.568 15.103 12.528 9.204 19.105 16.008 25.004 30.519 7.534 18.514-12.963 73.237-17.017 80.267-8.648 6.229-20.845 3.549-31.477 3.636.574 6.699 1.079 13.92 3.289 19.923h-37.863l-.452-22.481c-3.863.192-7.726.348-11.623.54 5.69-6.229 11.623-20.271 9.987-31.094l2.175.539.087-106.905zm12.72 83.45c1.374 1.879-2.332 14.407-3.515 21.611-3.219.678-4.472-1.079-8.108-.418-1.462-1.061-1.218-.8-1.88-2.018 3.637-6.212 7.013-12.685 13.503-19.175" clip-rule="evenodd" /><path fill="#0479FF" fill-rule="evenodd" d="M265.96 181.172c-3.376 4.732-10.423 16.686-12.859 22.08l-3.828 26.379.122-25.665 7.691-27.597z" clip-rule="evenodd" /><path fill="#FFCDAD" fill-rule="evenodd" d="m250.963 114.495-.035.8c-1.636.748-3.463 2.001-4.489 3.585-.905 1.426-.139 2.679.957 2.731 2.053-.034 3.549-.748 5.22-1.618.818-.504 1.566-1.931 1.496-3.184-.504-1.096-1.775-2.384-3.149-2.314M244.613 123.924c3.601-1.723 7.081-3.028 10.701-4.75 1.6 0 1.6.156 2.557 1.218.679 1.635-.156 2.558-1.078 3.254l-11.328 3.793c-1.775.383-3.271-2.053-.852-3.515" clip-rule="evenodd" /><path fill="#FFCDAD" fill-rule="evenodd" d="M242.454 129.302c.696-.678 10.144-4.437 12.162-4.976 2.036-.539 2.297.8 2.454 2.018.122 1.218-.157 2.593-1.375 2.976l-10.788 3.515c-2.592.939-3.932-2.315-2.453-3.533M243.808 133.373c3.967-1.27 7.917-2.523 11.884-3.793 1.079-.261 1.914.539 2.175 1.374.54 2.297-.8 3.637-2.418 4.315a873 873 0 0 1-9.744 1.88c-2.436.817-3.515-2.976-1.897-3.776M252.582 143.571c-.922.469-2.123.522-3.445.469.052-2.227.104-4.437.191-6.629 2.784-.957 6.456-2.384 7.761-1.322 1.078.974-3.533 4.889-4.507 7.482" clip-rule="evenodd" /><path fill="#E5997A" fill-rule="evenodd" d="m305.949 166.26-28.24 2.279c-.957 7.552-1.949 15.208-2.767 22.777-.4 13.398 24.412 4.646 26.553 3.567z" clip-rule="evenodd" /><path fill="#EEAC8C" fill-rule="evenodd" d="m317.811 131.896-51.765-9.814c.348 18.844-5.951 50.93 1.148 56.446 4.037 5.289 25.822 7.516 33.635 3.741 7.186-2.471 12.945-31.529 16.982-50.373" clip-rule="evenodd" /><path fill="#263238" fill-rule="evenodd" d="M307.131 133.321c-4.194-4.246-36.401-.835-41.186-4.629-13.468-8.7-4.872-6.159 1.27-18.931 2.906-8.23 8.717-2.679 34.017-.365 5.568.609 8.787 7.795 10.423 10.492 8.839 3.863 15.085 4.976 14.424 12.354-.765 8.857-10.353 20.375-15.486 30.624-7.169-10.753 3.724-23.159-3.462-29.545" clip-rule="evenodd" /><path fill="#EEAC8C" fill-rule="evenodd" d="m303.93 151.539 1.009-9.031c.974-3.062 7.308-3.95 8.943.139 1.236 5.499-7.36 10.98-9.952 8.892" clip-rule="evenodd" /><path fill="#263238" fill-rule="evenodd" d="M278.664 185.539c-15.764.47-21.68-13.102-16.408-29.528 3.184-1.357 15.225 20.811 39.115 9.344.105 1.514.192 3.028.296 4.524-.348 12.685-12.215 16.391-23.003 15.66" clip-rule="evenodd" /><path fill="#263238" fill-rule="evenodd" d="m306.485 129.701 2.801 7.134-9.274 36.453c-3.584.435-7.064.783-10.649 1.218 8.787-10.457 12.546-26.674 17.122-44.805" clip-rule="evenodd" /><path fill="#fff" fill-rule="evenodd" d="M281.238 176.848c-4.367-2.332-7.569-3.55-13.189-3.811 6.734-3.532 10.127-.974 13.189 3.811" clip-rule="evenodd" /></svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='Notification'>
                        <div id='mytoastrdata' class='toast fade' role='alert' aria-live='assertive' aria-atomic='true'>
                            <div class='d-flex'>
                                <div class='toast-body'>
                                    <span id="alertClass" class='success-toast'>
                                        <i id="icon" class='cp-check toast_icon'></i>
                                    </span>
                                    <span id="message">

                                    </span>
                                </div>
                                <button type='button' class='btn-close ms-auto m-2' data-bs-dismiss='toast' aria-label='Close'></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script src="~/js/common/thirdparty.bundle.js"></script>
<script src="~/js/common/viewer.part.bundle.js"></script>

<script src="~/lib/amcharts4/core.js"></script>
<script src="~/lib/amcharts4/charts.js"></script>
<script src="~/lib/amcharts4/animated.js"></script>

<script src="~/js/report-charts/report.js"></script>
<script src="~/js/report-charts/ReportXls.js"></script>
<script src="~/js/report-charts/ReportRefresh.js"></script>
<script>

    $('select').on('
:closing', () => {
        $('.select2-selection').width('auto');
        var $choice = $('.select2-selection__choice');
        $choice.first().show();
        $choice.slice(2).show();
        $choice.eq(2).after(`<li class='select2-selection__choice select2-selection__choice_more'>...</li>`);
    });
</script>