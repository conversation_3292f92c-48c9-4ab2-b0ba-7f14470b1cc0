﻿namespace ContinuityPatrol.Application.Features.WorkflowExecutionTemp.Commands.Create;

public class CreateWorkflowExecutionTempCommand : IRequest<CreateWorkflowExecutionTempResponse>
{
    public string WorkflowId { get; set; }
    public string WorkflowName { get; set; }
    public string Properties { get; set; }

    public override string ToString()
    {
        return $"Workflow Name: {WorkflowName};";
    }
}