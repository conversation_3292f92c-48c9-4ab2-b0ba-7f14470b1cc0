﻿using ContinuityPatrol.Application.Features.VeritasCluster.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;


namespace ContinuityPatrol.Application.UnitTests.Features.VeritasCluster.Events;

public class UpdateVeritasClusterEventTests : IClassFixture<VeritasClusterFixture>, IClassFixture<UserActivityFixture>
{
    private readonly VeritasClusterFixture _veritasClusterFixture;
    private readonly UserActivityFixture _userActivityFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly VeritasClusterUpdatedEventHandler _handler;

    public UpdateVeritasClusterEventTests(VeritasClusterFixture veritasClusterFixture, UserActivityFixture userActivityFixture)
    {
        _userActivityFixture = userActivityFixture;
        _veritasClusterFixture = veritasClusterFixture;
        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockVeritasClusterEventLogger = new Mock<ILogger<VeritasClusterUpdatedEventHandler>>();

        _mockUserActivityRepository = VeritasClusterRepositoryMock.CreateVeritasClusterEventRepository(_userActivityFixture.UserActivities);

        _handler = new VeritasClusterUpdatedEventHandler(mockLoggedInUserService.Object, mockVeritasClusterEventLogger.Object, _mockUserActivityRepository.Object);
    }
    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateVeritasClusterUpdated()
    {
        _userActivityFixture.UserActivities[0].LoginName = "Tester";

        var result = _handler.Handle(_veritasClusterFixture.VeritasClusterUpdatedEvent, CancellationToken.None);

        Assert.True(result.IsCompleted);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_veritasClusterFixture.VeritasClusterUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

}