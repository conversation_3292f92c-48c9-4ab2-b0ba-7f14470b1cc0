using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.BaseLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Create;
using ContinuityPatrol.Application.Features.LicenseManager.Command.DerivedLicense.Update;
using ContinuityPatrol.Application.Features.LicenseManager.Command.Replace;
using ContinuityPatrol.Application.Features.LicenseManager.Command.UpdateState;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetByPoNumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetChildLicenseByParentId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByCompanyId;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseByPONumber;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseCount;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetLicenseExpireList;
using ContinuityPatrol.Application.Features.LicenseManager.Queries.GetPoNumber;
using ContinuityPatrol.Domain.ViewModels.LicenseManagerModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class LicenseManagerControllerFixture : IDisposable
{
    public List<LicenseManagerListVm> LicenseManagerListVm { get; }
    public LicenseManagerDetailVm LicenseManagerDetailVm { get; }
    public CreateBaseLicenseCommand CreateBaseLicenseCommand { get; }
    public UpdateBaseLicenseCommand UpdateBaseLicenseCommand { get; }
    public CreateDerivedLicenseCommand CreateDerivedLicenseCommand { get; }
    public UpdateDerivedLicenseCommand UpdateDerivedLicenseCommand { get; }
    public List<LicenseExpireListVm> LicenseExpireListVm { get; }
    public List<LicenseManagerDetailViewVm> LicenseManagerDetailViewVm { get; }
    public LicenseReplaceCommand LicenseReplaceCommand { get; }
    public LicenseManagerByPoNumberVm LicenseManagerByPoNumberVm { get; }
    public GetLicenseByPONumberVm GetLicenseByPoNumberVm { get; }
    public List<GetLicenseByCompanyIdVm> GetLicenseByCompanyIdVm { get; }
    public LicenseCountVm LicenseCountVm { get; }
    public List<GetPoNumberListVm> GetPoNumberListVm { get; }
    public List<LicenseManagerNameVm> LicenseManagerNameVm { get; }
    public List<ChildLicenseDetailByParentIdVm> ChildLicenseDetailByParentIdVm { get; }
    public UpdateLicenseStateCommand UpdateLicenseStateCommand { get; }

    public LicenseManagerControllerFixture()
    {
        var fixture = new Fixture();

        LicenseManagerListVm = fixture.Create<List<LicenseManagerListVm>>();
        LicenseManagerDetailVm = fixture.Create<LicenseManagerDetailVm>();
        CreateBaseLicenseCommand = fixture.Create<CreateBaseLicenseCommand>();
        UpdateBaseLicenseCommand = fixture.Create<UpdateBaseLicenseCommand>();
        CreateDerivedLicenseCommand = fixture.Create<CreateDerivedLicenseCommand>();
        UpdateDerivedLicenseCommand = fixture.Create<UpdateDerivedLicenseCommand>();
        LicenseExpireListVm = fixture.Create<List<LicenseExpireListVm>>();
        LicenseManagerDetailViewVm = fixture.Create<List<LicenseManagerDetailViewVm>>();
        LicenseReplaceCommand = fixture.Create<LicenseReplaceCommand>();
        LicenseManagerByPoNumberVm = fixture.Create<LicenseManagerByPoNumberVm>();
        GetLicenseByPoNumberVm = fixture.Create<GetLicenseByPONumberVm>();
        GetLicenseByCompanyIdVm = fixture.Create<List<GetLicenseByCompanyIdVm>>();
        LicenseCountVm = fixture.Create<LicenseCountVm>();
        GetPoNumberListVm = fixture.Create<List<GetPoNumberListVm>>();
        LicenseManagerNameVm = fixture.Create<List<LicenseManagerNameVm>>();
        ChildLicenseDetailByParentIdVm = fixture.Create<List<ChildLicenseDetailByParentIdVm>>();
        UpdateLicenseStateCommand = fixture.Create<UpdateLicenseStateCommand>();
    }

    public void Dispose()
    {

    }
}