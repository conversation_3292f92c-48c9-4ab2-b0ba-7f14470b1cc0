﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.AccessManager.Events.Create;

public class AccessManagerCreatedEventHandler : INotificationHandler<AccessManagerCreatedEvent>
{
    private readonly ILogger<AccessManagerCreatedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public AccessManagerCreatedEventHandler(ILoggedInUserService userService,
        ILogger<AccessManagerCreatedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(AccessManagerCreatedEvent createdEvent, CancellationToken cancellationToken)
    {
        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            CompanyId = _userService.CompanyId,
            RequestUrl = _userService.RequestedUrl,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Create} {Modules.AccessManager}",
            Entity = Modules.AccessManager.ToString(),
            ActivityType = ActivityType.Create.ToString(),
            ActivityDetails = $"Access manager '{createdEvent.RoleName}' created successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);

        _logger.LogInformation($"Access manager '{createdEvent.RoleName}' created successfully.");
    }
}