using AutoFixture;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Create;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Commands.Update;
using ContinuityPatrol.Application.Features.BusinessServiceAvailability.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceAvailabilityModel;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class BusinessServiceAvailabilityFixture : IDisposable
{
    public CreateBusinessServiceAvailabilityCommand CreateBusinessServiceAvailabilityCommand { get; set; }
    public UpdateBusinessServiceAvailabilityCommand UpdateBusinessServiceAvailabilityCommand { get; set; }
    public List<BusinessServiceAvailabilityListVm> BusinessServiceAvailabilityListVm { get; set; }
    public BusinessServiceAvailabilityDetailVm BusinessServiceAvailabilityDetailVm { get; set; }

    public BusinessServiceAvailabilityFixture()
    {
        CreateBusinessServiceAvailabilityCommand = AutoBusinessServiceAvailabilityFixture.Create<CreateBusinessServiceAvailabilityCommand>();
        UpdateBusinessServiceAvailabilityCommand = AutoBusinessServiceAvailabilityFixture.Create<UpdateBusinessServiceAvailabilityCommand>();
        BusinessServiceAvailabilityListVm = AutoBusinessServiceAvailabilityFixture.CreateMany<BusinessServiceAvailabilityListVm>(3).ToList();
        BusinessServiceAvailabilityDetailVm = AutoBusinessServiceAvailabilityFixture.Create<BusinessServiceAvailabilityDetailVm>();
    }

    public Fixture AutoBusinessServiceAvailabilityFixture
    {
        get
        {
            var fixture = new Fixture();

            fixture.Customize<CreateBusinessServiceAvailabilityCommand>(c => c
                .With(b => b.TotalBusinessService, 100)
                .With(b => b.AvailabilityUp, 85)
                .With(b => b.AvailabilityDown, 15)
                .With(b => b.TotalBusinessFunction, 50)
                .With(b => b.BusinessFunctionUp, 45)
                .With(b => b.BusinessFunctionDown, 5)
                .With(b => b.HealthUp, 90)
                .With(b => b.HealthDown, 10)
                .With(b => b.DRReadynessUp, 80)
                .With(b => b.DRReadynessDown, 20)
                .With(b => b.TotalAlert, 25)
                .With(b => b.AlertUp, 20)
                .With(b => b.AlertDown, 5)
                .With(b => b.TotalIncident, 10)
                .With(b => b.IncidentUp, 8)
                .With(b => b.IncidentDown, 2));

            fixture.Customize<UpdateBusinessServiceAvailabilityCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.TotalBusinessService, 120)
                .With(b => b.AvailabilityUp, 100)
                .With(b => b.AvailabilityDown, 20)
                .With(b => b.TotalBusinessFunction, 60)
                .With(b => b.BusinessFunctionUp, 55)
                .With(b => b.BusinessFunctionDown, 5)
                .With(b => b.HealthUp, 95)
                .With(b => b.HealthDown, 5)
                .With(b => b.DRReadynessUp, 85)
                .With(b => b.DRReadynessDown, 15)
                .With(b => b.TotalAlert, 30)
                .With(b => b.AlertUp, 25)
                .With(b => b.AlertDown, 5)
                .With(b => b.TotalIncident, 12)
                .With(b => b.IncidentUp, 10)
                .With(b => b.IncidentDown, 2));

            fixture.Customize<BusinessServiceAvailabilityListVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.TotalBusinessService, () => fixture.Create<int>() % 200 + 50)
                .With(b => b.AvailabilityUp, () => fixture.Create<int>() % 150 + 30)
                .With(b => b.AvailabilityDown, () => fixture.Create<int>() % 50 + 5)
                .With(b => b.TotalBusinessFunction, () => fixture.Create<int>() % 100 + 20)
                .With(b => b.BusinessFunctionUp, () => fixture.Create<int>() % 80 + 15)
                .With(b => b.BusinessFunctionDown, () => fixture.Create<int>() % 20 + 2)
                .With(b => b.HealthUp, () => fixture.Create<int>() % 100 + 50)
                .With(b => b.HealthDown, () => fixture.Create<int>() % 30 + 5)
                .With(b => b.DRReadynessUp, () => fixture.Create<int>() % 90 + 40)
                .With(b => b.DRReadynessDown, () => fixture.Create<int>() % 40 + 10)
                .With(b => b.TotalAlert, () => fixture.Create<int>() % 50 + 10)
                .With(b => b.AlertUp, () => fixture.Create<int>() % 40 + 8)
                .With(b => b.AlertDown, () => fixture.Create<int>() % 15 + 2)
                .With(b => b.TotalIncident, () => fixture.Create<int>() % 20 + 5)
                .With(b => b.IncidentUp, () => fixture.Create<int>() % 15 + 3)
                .With(b => b.IncidentDown, () => fixture.Create<int>() % 8 + 1));

            fixture.Customize<BusinessServiceAvailabilityDetailVm>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString)
                .With(b => b.TotalBusinessService, 150)
                .With(b => b.AvailabilityUp, 130)
                .With(b => b.AvailabilityDown, 20)
                .With(b => b.TotalBusinessFunction, 75)
                .With(b => b.BusinessFunctionUp, 70)
                .With(b => b.BusinessFunctionDown, 5)
                .With(b => b.HealthUp, 140)
                .With(b => b.HealthDown, 10)
                .With(b => b.DRReadynessUp, 120)
                .With(b => b.DRReadynessDown, 30)
                .With(b => b.TotalAlert, 35)
                .With(b => b.AlertUp, 30)
                .With(b => b.AlertDown, 5)
                .With(b => b.TotalIncident, 15)
                .With(b => b.IncidentUp, 12)
                .With(b => b.IncidentDown, 3));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
