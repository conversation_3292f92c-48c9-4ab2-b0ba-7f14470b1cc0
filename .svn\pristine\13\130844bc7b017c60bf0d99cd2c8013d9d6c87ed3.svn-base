using AutoFixture;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Fixtures;

public class BusinessServiceFixture : IDisposable
{
    public List<BusinessService> BusinessServicePaginationList { get; set; }
    public List<BusinessService> BusinessServiceList { get; set; }
    public BusinessService BusinessServiceDto { get; set; }

    public const string CompanyId = "COMPANY_123";
    public const string BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

    public ApplicationDbContext DbContext { get; private set; }

    public BusinessServiceFixture()
    {
        var fixture = new Fixture();

        BusinessServiceList = fixture.Create<List<BusinessService>>();

        BusinessServicePaginationList = fixture.CreateMany<BusinessService>(20).ToList();

        BusinessServicePaginationList.ForEach(x => x.CompanyId = CompanyId);
        BusinessServicePaginationList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessServicePaginationList.ForEach(x => x.IsActive = true);

        BusinessServiceList.ForEach(x => x.CompanyId = CompanyId);
        BusinessServiceList.ForEach(x => x.ReferenceId = Guid.NewGuid().ToString());
        BusinessServiceList.ForEach(x => x.IsActive = true);

        BusinessServiceDto = fixture.Create<BusinessService>();
        BusinessServiceDto.CompanyId = CompanyId;
        BusinessServiceDto.ReferenceId = BusinessServiceId;
        BusinessServiceDto.IsActive = true;

        DbContext = DbContextFactory.CreateInMemoryDbContext();
    }

    public void Dispose()
    {
        DbContext?.Dispose();
    }
}
