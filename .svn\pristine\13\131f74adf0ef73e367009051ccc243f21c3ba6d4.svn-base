﻿using ContinuityPatrol.Application.Features.SmtpConfiguration.Events.Create;
using ContinuityPatrol.Application.Features.SmtpConfiguration.Events.Update;
using ContinuityPatrol.Domain.Entities;

namespace ContinuityPatrol.Application.Features.SmtpConfiguration.Commands.Update;

public class
    UpdateSmtpConfigurationCommandHandler : IRequestHandler<UpdateSmtpConfigurationCommand,
        UpdateSmtpConfigurationResponse>
{
    private readonly IMapper _mapper;
    private readonly ISmtpConfigurationRepository _smtpConfigurationRepository;
    private readonly IPublisher _publisher;

    public UpdateSmtpConfigurationCommandHandler(IMapper mapper,
        ISmtpConfigurationRepository smtpConfigurationRepository,IPublisher publisher)
    {
        _mapper = mapper;
        _smtpConfigurationRepository = smtpConfigurationRepository;
        _publisher=publisher;
    }

    public async Task<UpdateSmtpConfigurationResponse> Handle(UpdateSmtpConfigurationCommand request,
        CancellationToken cancellationToken)
    {
        var eventToUpdate = await _smtpConfigurationRepository.GetByReferenceIdAsync(request.Id);

        if (eventToUpdate == null) throw new NotFoundException(nameof(Domain.Entities.SmtpConfiguration), request.Id);

        _mapper.Map(request, eventToUpdate, typeof(UpdateSmtpConfigurationCommand),
        typeof(Domain.Entities.SmtpConfiguration));

        await _smtpConfigurationRepository.UpdateAsync(eventToUpdate);

        await _publisher.Publish(new SmtpConfigurationUpdatedEvent { UserName= SecurityHelper.Decrypt(eventToUpdate.UserName) },cancellationToken);

        var response = new UpdateSmtpConfigurationResponse
        {
            Message = "SMTP Configuration has been updated successfully",

            Id = eventToUpdate.ReferenceId
        };
        return response;
    }
}