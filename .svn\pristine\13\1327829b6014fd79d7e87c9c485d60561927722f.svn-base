﻿using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Infrastructure.Models;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Infrastructure.Impl;

public class WindowsService : IWindowsService
{
    private readonly ILogger<WindowsService> _logger;
    private readonly IHubContext<NotificationHub> _hubContext;

    public WindowsService(ILogger<WindowsService> logger, IHubContext<NotificationHub> hubContext)
    {
        _logger = logger;
        _hubContext = hubContext;
    }

    public async Task<bool> GetAsync(string url,[Optional] string type)
    {
        try
        {
            _logger.LogInformation($"Send request to load balancer URL : '{url}'.");

            var options = new RestClientOptions(url)
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
            };
            var client = new RestClient(options);

            var requestUrl = new RestRequest(url);

            var response = await client.ExecuteGetAsync(requestUrl);

            if (type.IsNotNullOrWhiteSpace())
            {
                var message = response.Content.IsNullOrWhiteSpace()
                    ? response.ErrorException?.Message ?? response.ErrorMessage ?? "An unknown error occurred."
                    : response.Content;

                _logger.LogInformation("Notification message: {Message}", message);

                await _hubContext.Clients.All.SendAsync("notification", new
                {
                    Message = message,
                    Group = type
                });
            }

            if (response.IsSuccessful)
            {
                _logger.LogInformation("Load balancer request successful. Fetched data from URL: '{Url}' with status code: {StatusCode}", url, response.StatusCode);
                return true;
            }

            _logger.LogError("Load balancer request failed for URL: '{Url}'. Status code: {StatusCode}. Response content: {Content}",
                url, response.StatusCode, response.Content);
            return false;
        }
        catch (Exception ex)
        {
            _logger.Exception($"Error in GetAsync method. Url : '{url}'.", ex);

            return false;
        }
    }

    public async Task<ServiceResponse> CheckWindowsService(string url)
    {
        try
        {
            _logger.LogInformation($"Check WindowsService Url : '{url}'.");

            var options = new RestClientOptions(url)
            {
                RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true,
            };
            var client = new RestClient(options);

            var requestUrl = new RestRequest(url);

            var response = await client.ExecuteGetAsync(requestUrl);
            
            if (response.IsSuccessful)
            {
                _logger.LogInformation("Service check succeeded for URL: '{Url}'", url);
            }
            else
            {
                if (response.ErrorException != null)
                {
                    _logger.LogError(response.Content.IsNullOrWhiteSpace()
                        ? response.ErrorException.Message
                        : response.Content, "Exception occurred while connecting to WindowsService.");

                    return new ServiceResponse
                    {
                        Success = false,
                        Message = response.Content.IsNullOrWhiteSpace()
                            ? response.ErrorException.Message
                            : response.Content
                    };
                }

                _logger.LogError("Service check failed for URL: '{Url}' with status code: {StatusCode}. Response content: {Content}",
                    url, response.StatusCode, response.Content);
            }

            try
            {
                var deserializeJson = JsonConvert.DeserializeObject<ServiceResponse>(response.Content!);

                return deserializeJson;
            }
            catch(JsonException)
            {
                _logger.LogInformation("Service check succeeded for URL: '{Url}' with status code: {StatusCode}. Response content: {Content}",
                    url, response.StatusCode, response.Content);

                return new ServiceResponse { Success = response.IsSuccessful, Message = response.Content };
            }
        }
        catch (Exception e)
        {
            _logger.Exception($"Error in CheckWindowsService method. Url : '{url}'.", e);

            return new ServiceResponse { Message = e.GetMessage(),Success = false};
        }
    }

    public async Task<bool> IsTcpClientConnectionSuccessfulAsync(string ipAddress, int port)
    {
        try
        {
            using (var tcpClient = new TcpClient())
            {
                // Try to connect with a timeout
                var connectTask = tcpClient.ConnectAsync(ipAddress, port);

                // Wait for connection to complete or timeout
                if (await Task.WhenAny(connectTask, Task.Delay(TimeSpan.FromSeconds(2))) == connectTask)
                {
                    // Connection succeeded
                    if (tcpClient.Connected)
                    {
                        _logger.LogDebug($"Successfully connected to {ipAddress}:{port}");

                        tcpClient.Close();

                        return true;
                    }
                }

                _logger.LogDebug($"Cannot connect to {ipAddress}:{port} (Timeout).");

                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.Exception($"Connection to {ipAddress}:{port} failed:", ex);
            return false;
        }
    }

}