﻿using ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.StateMonitorLog.Commands.Update;
using ContinuityPatrol.Application.Features.StateMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.StateMonitorLogModel;

namespace ContinuityPatrol.Application.Mappings;

public class StateMonitorLogProfile : Profile
{
    public StateMonitorLogProfile()
    {
        CreateMap<StateMonitorLog, CreateStateMonitorLogCommand>().ReverseMap();
        CreateMap<UpdateStateMonitorLogCommand, StateMonitorLog>().ForMember(x => x.Id, y => y.Ignore());

        CreateMap<StateMonitorLog, StateMonitorLogDetailVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
        CreateMap<StateMonitorLog, StateMonitorLogListVm>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ReferenceId));
    }
}