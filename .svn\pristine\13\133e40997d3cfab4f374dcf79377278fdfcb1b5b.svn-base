﻿using ContinuityPatrol.Application.Features.Site.Commands.Create;
using ContinuityPatrol.Application.Features.Site.Commands.Update;
using ContinuityPatrol.Application.Features.Site.Queries.GetByType;
using ContinuityPatrol.Application.Features.Site.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Site.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Site.Queries.GetSiteByCompanyId;
using ContinuityPatrol.Domain.ViewModels.SiteModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface ISiteService
{
    Task<List<SiteBySiteTypeVm>> GetSiteByTypeAndCompanyId(string companyId, string siteTypeId);
    Task<bool> IsSiteNameExist(string siteName, string id);
    Task<PaginatedResult<SiteListVm>> GetSitePaginatedList(GetSitePaginatedListQuery query);
    Task<BaseResponse> DeleteAsync(string siteId);
    Task<BaseResponse> UpdateAsync(UpdateSiteCommand updateSite);
    Task<BaseResponse> CreateAsync(CreateSiteCommand createSite);
    Task<List<SiteNameVm>> GetSiteNames();
    Task<List<GetSiteByCompanyIdVm>> GetSiteByCompanyId(string companyId);
    Task<SiteDetailVm> GetSiteById(string siteId);
    Task<List<SiteListVm>> GetSites();
}