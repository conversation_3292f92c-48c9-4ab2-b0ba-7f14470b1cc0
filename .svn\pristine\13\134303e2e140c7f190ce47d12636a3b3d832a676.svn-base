﻿using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Create;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Commands.Update;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetDetailByDatabaseId;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetType;
using ContinuityPatrol.Application.Features.InfraReplicationMapping.Queries.GetTypeByDatabaseIdAndReplicationId;
using ContinuityPatrol.Domain.ViewModels.InfraReplicationMappingModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
public class InfraReplicationMappingController : CommonBaseController
{

    [HttpPost]
    [Authorize(Policy = Permissions.Configuration.Create)]
    public async Task<ActionResult<CreateInfraReplicationMappingResponse>> CreateInfraReplicationMapping([FromBody] CreateInfraReplicationMappingCommand createInfraReplicationMappingCommand)
    {
        Logger.LogDebug($"Create InfraReplicationMapping '{createInfraReplicationMappingCommand}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateInfraReplicationMapping), await Mediator.Send(createInfraReplicationMappingCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Configuration.Edit)]
    public async Task<ActionResult<UpdateInfraReplicationMappingResponse>> UpdateInfraReplicationMapping([FromBody] UpdateInfraReplicationMappingCommand updateInfraReplicationMappingCommand)
    {
        Logger.LogDebug($"Update InfraReplicationMapping '{updateInfraReplicationMappingCommand}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateInfraReplicationMappingCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Configuration.Delete)]
    public async Task<ActionResult<DeleteInfraReplicationMappingResponse>> DeleteInfraReplicationMapping(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraReplicationMapping Id");

        Logger.LogDebug($"Delete InfraReplicationMapping Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteInfraReplicationMappingCommand { Id = id }));
    }


    [HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<InfraReplicationMappingListVm>>> GetInfraReplicationMappingList()
    {
        Logger.LogDebug("Get All InfraReplicationMapping");

        return Ok(await Mediator.Send(new GetInfraReplicationMappingListQuery()));
    }

    [HttpGet("{id}", Name = "InfraReplicationMapping")]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<InfraReplicationMappingDetailVm>> GetInfraReplicationMappingById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "InfraReplicationMapping Id");

        Logger.LogDebug($"Get InfraReplicationMapping Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetInfraReplicationMappingDetailQuery { Id = id }));
    }

    [Route("paginated-list"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<PaginatedResult<InfraReplicationMappingListVm>>> GetPaginatedInfraReplicationMapping([FromQuery] GetInfraReplicationMappingPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in InfraReplicationMapping Paginated List");

        return Ok(await Mediator.Send(query));
    }

    [Route("databaseid/replicationmasterid"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<InfraReplicationMappingByDatabaseIdVm>> GetInfraReplicationMappingByDatabaseId(string databaseid, string replicationmasterid)
    {
        Guard.Against.InvalidGuidOrEmpty(databaseid, "Database Id");

        Logger.LogDebug($"Get InfraReplicationMapping Details by Id '{databaseid}', '{replicationmasterid}'");

        return Ok(await Mediator.Send(new InfraReplicationMappingByDatabaseIdQuery { DatabaseId = databaseid, ReplicationMasterId = replicationmasterid }));
    }

    [Route("type/dbid/rmid"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<InfraReplicationMappingListVm>> GetTypeByDatabaseIdAndReplicationMasterId(string? databaseid, string replicationmasterid, string type)
    {
        //Guard.Against.InvalidGuidOrEmpty(databaseid, "Database Id");

        Logger.LogDebug($"Get InfraReplicationMapping Details by Id '{databaseid}', '{replicationmasterid}', '{type}'");

        return Ok(await Mediator.Send(new GetTypeByDatabaseIdAndReplicationIdQuery { DatabaseId = databaseid, ReplicationMasterId = replicationmasterid, Type = type}));
    }

    [Route("by/type"), HttpGet]
    [Authorize(Policy = Permissions.Configuration.View)]
    public async Task<ActionResult<List<InfraReplicationMappingListVm>>> GetInfraReplicationMappingByType(string? type)
    {
        Logger.LogDebug($"Get InfraReplicationMapping Details by Type '{type}'");

        return base.Ok(await Mediator.Send(new InfraReplicationMappingTypeQuery { Type = type }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllInfraReplicationMappingCacheKey + LoggedInUserService.CompanyId, ApplicationConstants.Cache.AllInfraReplicationMappingNameCacheKey };

        ClearCache(cacheKeys);
    }
}