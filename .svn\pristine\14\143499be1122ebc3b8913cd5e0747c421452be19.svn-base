using ContinuityPatrol.Application.Features.VeritasCluster.Events.Delete;

namespace ContinuityPatrol.Application.Features.VeritasCluster.Commands.Delete;

public class
    DeleteVeritasClusterCommandHandler : IRequestHandler<DeleteVeritasClusterCommand, DeleteVeritasClusterResponse>
{
    private readonly IPublisher _publisher;
    private readonly IVeritasClusterRepository _veritasClusterRepository;

    public DeleteVeritasClusterCommandHandler(IVeritasClusterRepository veritasClusterRepository, IPublisher publisher)
    {
        _veritasClusterRepository = veritasClusterRepository;

        _publisher = publisher;
    }

    public async Task<DeleteVeritasClusterResponse> Handle(DeleteVeritasClusterCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _veritasClusterRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.VeritasCluster),
            new NotFoundException(nameof(Domain.Entities.VeritasCluster), request.Id));

        eventToDelete.IsActive = false;

        await _veritasClusterRepository.UpdateAsync(eventToDelete);

        var response = new DeleteVeritasClusterResponse
        {
            Message = Message.Delete("Veritas Cluster", eventToDelete.ClusterName),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new VeritasClusterDeletedEvent { Name = eventToDelete.ClusterName },
            cancellationToken);

        return response;
    }
}