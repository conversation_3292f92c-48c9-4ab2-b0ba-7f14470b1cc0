﻿using ContinuityPatrol.Application.Features.FastCopyMonitor.Queries.GetByDataSyncId;
using ContinuityPatrol.Services.Db.Impl.Manage;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Manage;

public class FastCopyMonitorServiceTests : BaseServiceTestSetup<FastCopyMonitorService>, IClassFixture<FastCopyMonitorServiceFixture>
{
    private readonly FastCopyMonitorServiceFixture _fixture;

    public FastCopyMonitorServiceTests(FastCopyMonitorServiceFixture fixture)
    {
        InitializeService(accessor => new FastCopyMonitorService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetByDriftJobIds_Should_Return_ValidList()
    {
        // Arrange
        MediatorMock
            .Setup(m => m.Send(It.Is<GetFastCopyMonitorByDataSyncIdsQuery>(q => q.DataSyncJobIds == _fixture.DataSyncJobIds), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.FastCopyListResponse);

        // Act
        var result = await ServiceUnderTest.GetByDriftJobIds(_fixture.DataSyncJobIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fixture.FastCopyListResponse.Count, result.Count);
    }

    [Fact]
    public async Task GetPagination_Should_Return_PaginatedResult()
    {
        // Arrange
        MediatorMock
            .Setup(m => m.Send(_fixture.PaginatedQuery, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        // Act
        var result = await ServiceUnderTest.GetPagination(_fixture.PaginatedQuery);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_fixture.PaginatedResult.TotalCount, result.TotalCount);
    }
}
