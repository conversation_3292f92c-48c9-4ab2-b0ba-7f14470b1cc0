﻿namespace ContinuityPatrol.Application.Features.Database.Queries.GetDatabaseByType;

public class GetDatabaseByTypeVm
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string DatabaseTypeId { get; set; }
    public string DatabaseType { get; set; }
    public string Type { get; set; }
    public string ServerId { get; set; }
    public string ServerName { get; set; }
    public string ModeType { get; set; }
    public string Properties { get; set; }
    public string LicenseId { get; set; }
    public string LicenseKey { get; set; }
    public string Version { get; set; }
    public string BusinessServiceId { get; set; }
    public string BusinessServiceName { get; set; }
    public string ExceptionMessage { get; set; }
    public string FormVersion { get; set; }
}