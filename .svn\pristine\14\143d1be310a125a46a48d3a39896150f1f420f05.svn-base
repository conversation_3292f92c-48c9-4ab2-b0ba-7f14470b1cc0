﻿using ContinuityPatrol.Application.Features.WorkflowOperationGroup.Queries.GetLogDataByGroupId;
using ContinuityPatrol.Infrastructure.Contract;
using ContinuityPatrol.Shared.Infrastructure.Hubs;

namespace ContinuityPatrol.Application.UnitTests.Features.WorkflowOperationGroup.Queries;

public class GetWorkflowOperationGroupLogByGroupIdQueryHandlerTests
{
    private readonly Mock<ISeqService> _mockSeqService = new();
    private readonly Mock<IMapper> _mockMapper = new();
    private readonly Mock<IJobScheduler> _mockJobScheduler = new();
    private readonly Mock<ILogger<GetLogByGroupIdQueryHandler>> _mockLogger = new();
    private readonly GetLogByGroupIdQueryHandler _handler;

    public GetWorkflowOperationGroupLogByGroupIdQueryHandlerTests()
    {
        _handler = new GetLogByGroupIdQueryHandler(_mockMapper.Object, _mockSeqService.Object, _mockLogger.Object, _mockJobScheduler.Object);
    }

    [Fact]
    public async Task Handle_ReturnsMappedLogs_WhenLogsExist()
    {
        // Arrange
        var groupId = "group1";
        var request = new GetLogByGroupIdQuery { GroupId = groupId };

        var seqLogs = new List<LogHubVm>
        {
            new() { Message = "Log message 1",Arrived = "001",Id = 12,Level = "Information",Timestamp = "2025-07-20",WorkflowOperationGroupId = "1234"},
            new() { Message = "Log message 2" ,Arrived = "001",Id = 12,Level = "Information",Timestamp = "2025-07-20",WorkflowOperationGroupId = "1234"}
        };

        var mappedLogs = new List<GetLogByGroupIdVm>
        {
            new() { Message = "Log message 1",Arrived = "001",Id = 12,Level = "Information",Timestamp = "2025-07-20",WorkflowOperationGroupId = "1234"},
            new() { Message = "Log message 2" ,Arrived = "001",Id = 12,Level = "Information",Timestamp = "2025-07-20", WorkflowOperationGroupId = "1234"}
        };

        _mockSeqService
            .Setup(x => x.GetSeqLogsByGroupIdAsync(groupId, It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync(seqLogs);


        _mockMapper.Setup(x => x.Map<List<GetLogByGroupIdVm>>(seqLogs))
            .Returns(mappedLogs);

        _mockJobScheduler.Setup(x => x.ScheduleSeqServiceJob(groupId, It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Equal(12, result[0].Id);
        Assert.Equal("Log message 1", result[0].Message);
        Assert.Equal("001", result[0].Arrived);
        Assert.Equal("Information", result[0].Level);
        Assert.Equal("2025-07-20", result[0].Timestamp);
        Assert.Equal("1234", result[0].WorkflowOperationGroupId);

        _mockSeqService.Verify(x => x.GetSeqLogsByGroupIdAsync(groupId, It.IsAny<bool>(), It.IsAny<bool>()), Times.Once);
        _mockMapper.Verify(x => x.Map<List<GetLogByGroupIdVm>>(seqLogs), Times.Once);
        _mockJobScheduler.Verify(x => x.ScheduleSeqServiceJob(groupId, It.IsAny<Dictionary<string, string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsEmptyList_WhenSeqLogsAreNull()
    {
        // Arrange
        var request = new GetLogByGroupIdQuery { GroupId = "group2" };

        _mockSeqService.Setup(x => x.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>()))
            .ReturnsAsync((List<LogHubVm>)null!);

        _mockJobScheduler.Setup(x => x.ScheduleSeqServiceJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        _mockSeqService.Verify(x => x.GetSeqLogsByGroupIdAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<bool>()), Times.Once);
        _mockMapper.Verify(x => x.Map<List<GetLogByGroupIdVm>>(It.IsAny<List<LogHubVm>>()), Times.Never);
        _mockJobScheduler.Verify(x => x.ScheduleSeqServiceJob(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ReturnsMappedLogs_WhenExceptionIsThrown()
    {
        // Arrange
        var groupId = "group3";
        var request = new GetLogByGroupIdQuery { GroupId = groupId };

        _mockSeqService.SetupSequence(x => x.GetSeqLogsByGroupIdAsync(groupId, It.IsAny<bool>(), It.IsAny<bool>()))
            .Throws(new Exception("Test Exception"))
            .ReturnsAsync(new List<LogHubVm>
            {
                new() { Message = "Recovered log" }
            });

        _mockMapper.Setup(x => x.Map<List<GetLogByGroupIdVm>>(It.IsAny<List<LogHubVm>>()))
            .Returns<List<LogHubVm>>(logs =>
                new List<GetLogByGroupIdVm> { new() { Message = logs[0].Message } });

        _mockJobScheduler.Setup(x => x.ScheduleSeqServiceJob(groupId, It.IsAny<Dictionary<string, string>>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        Assert.Single(result);
        Assert.Equal("Recovered log", result[0].Message);

        _mockLogger.Verify(
            x => x.Log(
                It.Is<LogLevel>(l => l == LogLevel.Error),
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Get Seq Log")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()!),
            Times.Once);

        _mockJobScheduler.Verify(x => x.ScheduleSeqServiceJob(groupId, It.IsAny<Dictionary<string, string>>()), Times.Once);
    }
}