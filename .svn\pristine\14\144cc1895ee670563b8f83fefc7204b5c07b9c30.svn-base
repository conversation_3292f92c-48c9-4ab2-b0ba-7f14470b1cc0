﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.Features.WorkflowActionResult.Events.Delete;

public class WorkflowActionResultDeletedEventHandler : INotificationHandler<WorkflowActionResultDeletedEvent>
{
    private readonly ILogger<WorkflowActionResultDeletedEventHandler> _logger;
    private readonly IUserActivityRepository _userActivityRepository;
    private readonly ILoggedInUserService _userService;

    public WorkflowActionResultDeletedEventHandler(ILoggedInUserService userService,
        ILogger<WorkflowActionResultDeletedEventHandler> logger, IUserActivityRepository userActivityRepository)
    {
        _userService = userService;
        _logger = logger;
        _userActivityRepository = userActivityRepository;
    }

    public async Task Handle(WorkflowActionResultDeletedEvent deletedEvent, CancellationToken cancellationToken)
    {
        _logger.LogInformation($"Workflow Action Result'{deletedEvent.WorkflowActionName}' Deleted successfully.");

        var userActivity = new Domain.Entities.UserActivity
        {
            UserId = _userService.UserId,
            LoginName = _userService.LoginName,
            RequestUrl = _userService.RequestedUrl,
            CompanyId = _userService.CompanyId,
            HostAddress = _userService.IpAddress,
            Action = $"{ActivityType.Delete} {Modules.WorkflowActionResult}",
            Entity = Modules.WorkflowActionResult.ToString(),
            ActivityType = ActivityType.Delete.ToString(),
            ActivityDetails = $"Workflow Action Result '{deletedEvent.WorkflowActionName}' Deleted successfully."
        };

        await _userActivityRepository.AddAsync(userActivity);
    }
}