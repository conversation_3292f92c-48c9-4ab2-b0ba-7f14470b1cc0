﻿using ContinuityPatrol.Application.Features.Server.Commands.Create;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Server.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Server.Commands.TestConnection;
using ContinuityPatrol.Application.Features.Server.Commands.Update;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateBulkPassword;
using ContinuityPatrol.Application.Features.Server.Commands.UpdateVersion;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Server.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetRoleType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByLicenseKey;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByOsType;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByServerName;
using ContinuityPatrol.Application.Features.Server.Queries.GetServerByUserName;
using ContinuityPatrol.Application.Features.Server.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ServerModel;
using ContinuityPatrol.Shared.Core.Enums;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Configuration;

public class ServerService : BaseClient, IServerService
{
   

    public ServerService(IConfiguration config, IAppCache cacheService, ILogger<ServerService> logger) : base(config, cacheService, logger)
    {
    }

    public async Task<List<ServerNameVm>> GetServerNames()
    {
        var request = new RestRequest("api/v6/servers/names");

        return await Get<List<ServerNameVm>>(request);
    }

    public async Task<List<ServerListVm>> GetServerList()
    {
        var request = new RestRequest("api/v6/servers");

        return await Get<List<ServerListVm>>(request);
    }

    public async Task<BaseResponse> CreateAsync(CreateServerCommand createServerCommand)
    {
        var request = new RestRequest("api/v6/servers", Method.Post);

        request.AddJsonBody(createServerCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateServerCommand updateServerCommand)
    {
        var request = new RestRequest("api/v6/servers", Method.Put);

        request.AddJsonBody(updateServerCommand);

        return await Put<BaseResponse>(request);
    }
    public async Task<BaseResponse> ServerTestConnection(ServerTestConnectionCommand command)
    {
        var request = new RestRequest($"api/v6/servers/servertestconnection", Method.Post);

        request.AddJsonBody(command);

        return await Get<BaseResponse>(request);
    }

    public async Task<BaseResponse> DeleteAsync(string serverId)
    {
        var request = new RestRequest($"api/v6/servers/{serverId}", Method.Delete);

        return await Delete<BaseResponse>(request);
    }

    public async Task<ServerDetailVm> GetByReferenceId(string id)
    {
        var request = new RestRequest($"api/v6/servers/{id}");

        return await Get<ServerDetailVm>(request);
    }

    public async Task<bool> IsServerNameExist(string serverName, string? id)
    {
        var request = new RestRequest($"api/v6/servers/name-exist?serverName={serverName}&id={id}");

        return await Get<bool>(request);
    }

    public async Task<List<ServerTypeVm>> GetByType(string serverTypeId)
    {
        var request = new RestRequest($"api/v6/servers/by/type?type={serverTypeId}");

        return await Get<List<ServerTypeVm>>(request);
    }

    public async Task<List<GetServerByOsTypeVm>> GetByServerOsType(string? osTypeId)
    {
        var request = new RestRequest($"api/v6/servers/by/serverOstype?serverOstype={osTypeId}");

        return await Get<List<GetServerByOsTypeVm>>(request);
    }

    public async Task<ServerByServerNameVm> GetByServerName(string serverName)
    {
        var request = new RestRequest($"api/v6/servers/servername?serverName={serverName}");

        return await Get<ServerByServerNameVm>(request);
    }

    public async Task<List<ServerRoleTypeVm>> GetByRoleTypeAndServerType(string? roleType, string? serverType)
    {
        var request = new RestRequest($"api/v6/servers/roletype?roleType={roleType}&serverType={serverType}");

        return await Get<List<ServerRoleTypeVm>>(request);
    }

    public async Task<List<ServerByLicenseKeyVm>> GetByLicenseKey(string? licenseId)
    {
        var request = new RestRequest($"api/v6/servers/by/{licenseId}");

        return await Get<List<ServerByLicenseKeyVm>>(request);
    }

   

    public async Task<PaginatedResult<ServerViewListVm>> GetPaginatedServers(GetServerPaginatedListQuery query)
    {
        var request = new RestRequest($"api/v6/servers/paginated-list{query}", Method.Post);

        return await Get<PaginatedResult<ServerViewListVm>>(request);
    }

    public async Task<BaseResponse> SaveAsServer(SaveAsServerCommand saveAsServerCommand)
    {
        var request = new RestRequest("api/v6/servers/save-as", Method.Post);

        request.AddJsonBody(saveAsServerCommand);

        return await Post<BaseResponse>(request);
    }

    public async Task<BaseResponse> UpdateServerPassword(UpdateBulkPasswordCommand command)
    {
        var request = new RestRequest("api/v6/servers/bulk-password", Method.Put);

        request.AddJsonBody(command);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<ServerByUserNameVm>> GetServerByUserName(string userName, string? osTypeId, bool substituteAuthentication)
    {
        var request = new RestRequest($"api/v6/servers/username?userName={userName}&osType={osTypeId}&substituteAuthentication={substituteAuthentication}");

        return await Get<List<ServerByUserNameVm>>(request);
    }

    public async Task<BaseResponse> UpdateServerFormVersion(UpdateServerVersionCommand updateServerVersionCommand)
    {
        var request = new RestRequest("api/v6/servers/update-formVersion", Method.Put);

        request.AddJsonBody(updateServerVersionCommand);

        return await Put<BaseResponse>(request);
    }

    public async Task<List<ServerListVm>> GetServerByIpAddress(string ipAddress)
    {
        var request = new RestRequest($"/api/v6/servers/ipaddress?ipAddress={ipAddress}");

        return await Get<List<ServerListVm>>(request);
    }

    public async Task<BaseResponse> SaveAllServer(SaveAllServerCommand command)
    {
        if (command.ServerId.IsNullOrWhiteSpace()) throw new Exception("Server Id is required");

        var request = new RestRequest("api/v6/servers/save-all", Method.Post);

        request.AddJsonBody(command);

        return await Post<BaseResponse>(request);
    }

    public async Task<List<ServerListVm>> GetServerBySiteId(string siteId)
    {
        var request = new RestRequest($"/api/v6/servers/siteId?siteid={siteId}");

        return await Get<List<ServerListVm>>(request);
    }
}