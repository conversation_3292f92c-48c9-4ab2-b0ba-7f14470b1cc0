﻿using ContinuityPatrol.Application.Features.SiteType.Queries.GetNameUnique;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.SiteType.Queries;

public class GetSiteTypeNameUniqueQueryHandlerTests : IClassFixture<SiteTypeFixture>
{
    private readonly SiteTypeFixture _siteTypeFixture;

    private Mock<ISiteTypeRepository> _mockSiteTypeRepository;

    private readonly GetSiteTypeNameUniqueQueryHandler _handler;

    public GetSiteTypeNameUniqueQueryHandlerTests(SiteTypeFixture siteTypeFixture)
    {
        _siteTypeFixture = siteTypeFixture;

        _mockSiteTypeRepository = SiteTypeRepositoryMocks.GetSiteTypeNameUniqueRepository(_siteTypeFixture.SiteTypes);

        _handler = new GetSiteTypeNameUniqueQueryHandler(_mockSiteTypeRepository.Object);
    }

    [Fact]
    public async Task Handle_Return_True_SiteTypeName_Exist()
    {
        _siteTypeFixture.SiteTypes[0].Type = "DR_Site";

        var result = await _handler.Handle(new GetSiteTypeNameUniqueQuery { Type = _siteTypeFixture.SiteTypes[0].Type, Id = _siteTypeFixture.SiteTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeTrue();
    }

    [Fact]
    public async Task Handle_Return_False_SiteTypeNameAndId_NotMatch()
    {
        var result = await _handler.Handle(new GetSiteTypeNameUniqueQuery { Type = "DR_Site", Id = _siteTypeFixture.SiteTypes[0].ReferenceId }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Call_IsSiteTypeNameExist_OneTime()
    {
        var handler = new GetSiteTypeNameUniqueQueryHandler(_mockSiteTypeRepository.Object);

        await handler.Handle(new GetSiteTypeNameUniqueQuery { Id = _siteTypeFixture.SiteTypes[0].ReferenceId, Type = _siteTypeFixture.SiteTypes[0].Type }, CancellationToken.None);

        _mockSiteTypeRepository.Verify(x => x.IsSiteTypeNameExist(It.IsAny<string>(), It.IsAny<string>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_False_SiteTypeName_NotMatch()
    {
        var result = await _handler.Handle(new GetSiteTypeNameUniqueQuery { Type = "DR_Pro", Id = 0.ToString() }, CancellationToken.None);

        result.ShouldBeFalse();
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockSiteTypeRepository = SiteTypeRepositoryMocks.GetSiteTypeEmptyRepository();

        var result = await _handler.Handle(new GetSiteTypeNameUniqueQuery(), CancellationToken.None);

        result.ShouldBe(false);
    }
}