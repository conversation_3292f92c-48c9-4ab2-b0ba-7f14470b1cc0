﻿namespace ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByUserIdAndProperties;

public class
    GetByUserIdAndPropertiesQueryHandler : IRequestHandler<GetByUserIdAndPropertiesQuery,
        List<GetByUserIdAndPropertiesVm>>
{
    private readonly IMapper _mapper;
    private readonly IUserInfraObjectRepository _userInfraObjectRepository;

    public GetByUserIdAndPropertiesQueryHandler(IUserInfraObjectRepository userInfraObjectRepository, IMapper mapper)
    {
        _userInfraObjectRepository = userInfraObjectRepository;
        _mapper = mapper;
    }

    public async Task<List<GetByUserIdAndPropertiesVm>> Handle(GetByUserIdAndPropertiesQuery request,
        CancellationToken cancellationToken)
    {
        var sites = await _userInfraObjectRepository.GetByUserIdAndProperties();

        var siteDto = _mapper.Map<List<GetByUserIdAndPropertiesVm>>(sites);

        return siteDto;
    }
}