﻿using ContinuityPatrol.Application.Features.CyberComponentMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.CyberComponentMapping.Queries
{
    public class GetCyberComponentMappingDetailsQueryHandlerTests
    {
        private readonly Mock<ICyberComponentMappingRepository> _mockRepository;
        private readonly IMapper _mapper;
        private readonly GetCyberComponentMappingDetailsQueryHandler _handler;

        public GetCyberComponentMappingDetailsQueryHandlerTests()
        {
            _mockRepository = new Mock<ICyberComponentMappingRepository>();

            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<CyberComponentMappingProfile>(); // Ensure you have this profile created
            });
            _mapper = config.CreateMapper();

            _handler = new GetCyberComponentMappingDetailsQueryHandler(_mapper, _mockRepository.Object);
        }

        [Fact(DisplayName = "Handle_Should_Return_DetailVm_When_Entity_Exists_And_Active")]
        public async Task Handle_Should_Return_DetailVm_When_Entity_Exists_And_Active()
        {
            // Arrange
            var id = Guid.NewGuid().ToString();
            var entity = new Domain.Entities.CyberComponentMapping
            {
                ReferenceId = id,
                Name = "Firewall Rule",
                IsActive = true
            };

            _mockRepository.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);

            var query = new GetCyberComponentMappingDetailQuery { Id = id };

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.ShouldNotBeNull();
            result.Name.ShouldBe("Firewall Rule");
            _mockRepository.Verify(r => r.GetByReferenceIdAsync(id), Times.Once);
        }

        [Fact(DisplayName = "Handle_Should_Throw_NotFoundException_When_Entity_Is_Null")]
        public async Task Handle_Should_Throw_NotFoundException_When_Entity_Is_Null()
        {
            // Arrange
            var id = Guid.NewGuid().ToString();
            _mockRepository.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync((Domain.Entities.CyberComponentMapping)null);

            var query = new GetCyberComponentMappingDetailQuery { Id = id };

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact(DisplayName = "Handle_Should_Throw_NotFoundException_When_Entity_Is_Inactive")]
        public async Task Handle_Should_Throw_NotFoundException_When_Entity_Is_Inactive()
        {
            // Arrange
            var id = Guid.NewGuid().ToString();
            var entity = new Domain.Entities.CyberComponentMapping
            {
                ReferenceId = id,
                Name = "Old Mapping",
                IsActive = false // Marked inactive
            };

            _mockRepository.Setup(r => r.GetByReferenceIdAsync(id)).ReturnsAsync(entity);

            var query = new GetCyberComponentMappingDetailQuery { Id = id };

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact]
        public void Should_Assign_Properties_Correctly()
        {
            // Arrange
            var expectedId = "abc123";
            var expectedName = "Test Component";
            var expectedProperties = "{ \"key\": \"value\" }";

            // Act
            var viewModel = new CyberComponentMappingDetailVm
            {
                Id = expectedId,
                Name = expectedName,
                Properties = expectedProperties
            };

            // Assert
            Assert.Equal(expectedId, viewModel.Id);
            Assert.Equal(expectedName, viewModel.Name);
            Assert.Equal(expectedProperties, viewModel.Properties);
        }
    }
}
