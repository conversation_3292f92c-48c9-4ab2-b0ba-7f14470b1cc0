﻿using Newtonsoft.Json;

namespace ContinuityPatrol.Application.Features.RsyncJob.Commands.Create;

public class CreateRsyncJobCommandValidator : AbstractValidator<CreateRsyncJobCommand>
{
    private readonly IRsyncJobRepository _rsyncJobRepository;

    public CreateRsyncJobCommandValidator(IRsyncJobRepository rsyncJobRepository)
    {
        _rsyncJobRepository = rsyncJobRepository;

        RuleFor(p => p.ReplicationName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.ReplicationType)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull();

        RuleFor(p => p.SiteName)
            .NotEmpty().WithMessage("{PropertyName} is required")
            .Matches(@"^([a-zA-Z]+[_\s]?)([a-zA-Z\d]+[_\s])*[a-zA-Z\d]+$")
            .WithMessage("Please Enter Valid {PropertyName}")
            .NotNull()
            .Length(3, 100).WithMessage("{PropertyName} should contain between 3 to 100 characters");

        RuleFor(p => p.Properties)
            .Must(IsValidJson)
            .WithMessage("{PropertyName} must be a valid JSON string.")
            .When(p => p.Properties.IsNotNullOrWhiteSpace());

        RuleFor(p => p)
            .MustAsync(IsValidGuidId)
            .WithMessage("ID is invalid");
    }

    private Task<bool> IsValidGuidId(CreateRsyncJobCommand p, CancellationToken cancellationToken)
    {
        Guard.Against.InvalidGuidOrEmpty(p.ReplicationId, "Replication Id");
        Guard.Against.InvalidGuidOrEmpty(p.ReplicationTypeId, "Replication Type Id");
        Guard.Against.InvalidGuidOrEmpty(p.SiteId, "Site Id");

        if (!string.IsNullOrEmpty(p.RsyncOptionId))
            Guard.Against.InvalidGuidOrEmpty(p.RsyncOptionId, "RsyncOption Id");

        return Task.FromResult(true);
    }

    private static bool IsValidJson(string properties)
    {
        if (string.IsNullOrWhiteSpace(properties))
            return false;

        properties = properties.Trim();
        if ((properties.StartsWith("{") && properties.EndsWith("}")) || // For object
            (properties.StartsWith("[") && properties.EndsWith("]"))) // For array
            try
            {
                return JsonConvert.DeserializeObject(properties) != null;
            }
            catch (JsonException)
            {
                return false;
            }

        return false;
    }
}