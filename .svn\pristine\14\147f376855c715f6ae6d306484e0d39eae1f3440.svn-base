﻿using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Commands.Create;
using ContinuityPatrol.Application.Features.SVCGMMonitoringStatus.Commands.Update;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.SVCGMMonitoringStatus.Commands
{
    public class UpdateSVCGMMonitorStatusTests
    {
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ISVCGMMonitorStatusRepository> _mockSVCGMMonitorStatusRepository;
        private readonly UpdateSVCGMMonitorStatusCommandHandler _handler;

        public UpdateSVCGMMonitorStatusTests()
        {
            _mockMapper = new Mock<IMapper>();
            _mockSVCGMMonitorStatusRepository = new Mock<ISVCGMMonitorStatusRepository>();
            _handler = new UpdateSVCGMMonitorStatusCommandHandler(_mockMapper.Object, _mockSVCGMMonitorStatusRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldSuccessfullyUpdateSVCGMMonitorStatus_AndReturnResponse()
        {
            var command = new UpdateSVCGMMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowName = "Updated Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = command.Id,
                WorkflowName = "Old Monitor",
                Type = "Inactive"
            };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()))
                .Returns(Task.FromResult(entity));

            var result = await _handler.Handle(command, CancellationToken.None);

            Assert.NotNull(result);
            Assert.Equal(command.Id, result.Id);
            Assert.Contains("SVCGMMonitorStatus", result.Message);

            _mockMapper.Verify(m => m.Map(command, entity, typeof(UpdateSVCGMMonitorStatusCommand), typeof(SVCGMMonitorStatus)), Times.Once);
            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityNotFound()
        {
            var command = new UpdateSVCGMMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowName = "Updated Monitor",
                Type = "Active"
            };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync((SVCGMMonitorStatus)null);

            await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.GetByReferenceIdAsync(command.Id), Times.Once);
            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()), Times.Never);
            _mockMapper.Verify(m => m.Map(It.IsAny<object>(), It.IsAny<object>(), It.IsAny<Type>(), It.IsAny<Type>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ShouldCallUpdateAsync_Once()
        {
            var command = new UpdateSVCGMMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowName = "Updated Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = command.Id,
                WorkflowName = "Old Monitor",
                Type = "Inactive"
            };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync(entity) ;

            await _handler.Handle(command, CancellationToken.None);

            _mockSVCGMMonitorStatusRepository.Verify(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldCallMapperOnce()
        {
            var command = new UpdateSVCGMMonitorStatusCommand
            {
                Id = Guid.NewGuid().ToString(),
                WorkflowName = "Updated Monitor",
                Type = "Active"
            };

            var entity = new SVCGMMonitorStatus
            {
                ReferenceId = command.Id,
                WorkflowName = "Old Monitor",
                Type = "Inactive"
            };

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.GetByReferenceIdAsync(command.Id))
                .ReturnsAsync(entity);

            _mockSVCGMMonitorStatusRepository.Setup(repo => repo.UpdateAsync(It.IsAny<SVCGMMonitorStatus>()))
                .ReturnsAsync((SVCGMMonitorStatus)null);

            await _handler.Handle(command, CancellationToken.None);

            _mockMapper.Verify(m => m.Map(command, entity, typeof(UpdateSVCGMMonitorStatusCommand), typeof(SVCGMMonitorStatus)), Times.Once);
        }
        [Fact]
        public void Should_Assign_All_Properties_Correctly()
        {
            // Arrange
            var command = new UpdateSVCGMMonitorStatusCommand
            {
                Id = "status-001",
                Type = "SQL",
                InfraObjectId = "infra-123",
                InfraObjectName = "InfraNode01",
                WorkflowId = "wf-999",
                WorkflowName = "ReplicationMonitor",
                ConfiguredRPO = "10",
                DataLagValue = "5",
                Properties = "{\"ThresholdWarning\":\"90\"}",
                Threshold = "15"
            };

            // Assert
            command.Id.Should().Be("status-001");
            command.Type.Should().Be("SQL");
            command.InfraObjectId.Should().Be("infra-123");
            command.InfraObjectName.Should().Be("InfraNode01");
            command.WorkflowId.Should().Be("wf-999");
            command.WorkflowName.Should().Be("ReplicationMonitor");
            command.ConfiguredRPO.Should().Be("10");
            command.DataLagValue.Should().Be("5");
            command.Properties.Should().Be("{\"ThresholdWarning\":\"90\"}");
            command.Threshold.Should().Be("15");
        }
        [Fact]
        public void Should_Assign_All_Response_Properties_Correctly()
        {
            // Arrange
            var response = new UpdateSVCGMMonitorStatusCommandResponse
            {
                Id = "svcgm-001",
                Success = true,
                Message = "Created successfully"
            };

            // Assert
            response.Id.Should().Be("svcgm-001");
            response.Success.Should().BeTrue();
            response.Message.Should().Be("Created successfully");
        }
    }
}
