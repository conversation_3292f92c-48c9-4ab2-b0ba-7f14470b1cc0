﻿using ContinuityPatrol.Application.Features.InfraSummary.Commands.Delete;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.InfraSummary.Commands;

public class DeleteInfraSummaryTests : IClassFixture<InfraSummaryFixture>
{
    private readonly InfraSummaryFixture _infraSummaryFixture;
    private readonly Mock<IInfraSummaryRepository> _mockInfraSummaryRepository;
    private readonly DeleteInfraSummaryCommandHandler _handler;

    public DeleteInfraSummaryTests(InfraSummaryFixture infraSummaryFixture)
    {
        _infraSummaryFixture = infraSummaryFixture;

        _mockInfraSummaryRepository = new Mock<IInfraSummaryRepository>();

        _mockInfraSummaryRepository = InfraSummaryRepositoryMocks.DeleteInfraSummaryRepository(_infraSummaryFixture.InfraSummaries);

        _handler = new DeleteInfraSummaryCommandHandler(_mockInfraSummaryRepository.Object);

        _infraSummaryFixture.InfraSummaries[0].IsActive = true;
    }

    [Fact]
    public async Task Handle_UpdateIsActiveFalse_When_InfraSummaryDeleted()
    {
        var validGuid = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        _infraSummaryFixture.InfraSummaries[0].ReferenceId = validGuid;

        _mockInfraSummaryRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_infraSummaryFixture.InfraSummaries[0]);

        var result = await _handler.Handle(new DeleteInfraSummaryCommand { Type = validGuid }, CancellationToken.None);

        result.ToString();

        _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_DeleteInfraSummaryResponse_When_InfraSummaryDeleted()
    {
        var validGuid = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        _infraSummaryFixture.InfraSummaries[0].ReferenceId = validGuid;

        _mockInfraSummaryRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_infraSummaryFixture.InfraSummaries[0]);

        var result = await _handler.Handle(new DeleteInfraSummaryCommand { Type = validGuid }, CancellationToken.None);
        result.ShouldBeOfType(typeof(DeleteInfraSummaryResponse));
        result.Success.ShouldBeTrue();
        result.Message.ShouldContain(CommonConstants.DeletedSuccessfully);
        _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Update_IsActiveFalse_When_InfraSummaryDeleted()
    {
        var validGuid = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        _infraSummaryFixture.InfraSummaries[0].ReferenceId = validGuid;

        await _handler.Handle(new DeleteInfraSummaryCommand { Type = validGuid }, CancellationToken.None);

        _mockInfraSummaryRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_infraSummaryFixture.InfraSummaries[0]);

        var infraSummary = await _mockInfraSummaryRepository.Object.GetInfraSummaryByType(_infraSummaryFixture.InfraSummaries[0].ReferenceId);

        infraSummary.IsActive.ShouldBeFalse();

        _mockInfraSummaryRepository.Verify(repo => repo.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ThrowNotFoundException_When_InvalidInfraSummaryId()
    {
        var invalidGuid = Guid.NewGuid().ToString();

        await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(new DeleteInfraSummaryCommand { Type = invalidGuid }, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_Call_UpdateAsyncMethod_OnlyOnce()
    {
        var validGuid = _infraSummaryFixture.InfraSummaries[0].ReferenceId;

        _infraSummaryFixture.InfraSummaries[0].ReferenceId = validGuid;

        _mockInfraSummaryRepository
            .Setup(repo => repo.GetByReferenceIdAsync(validGuid))
            .ReturnsAsync(_infraSummaryFixture.InfraSummaries[0]);

        var result = await _handler.Handle(new DeleteInfraSummaryCommand { Type = _infraSummaryFixture.InfraSummaries[0].ReferenceId }, CancellationToken.None);

        _mockInfraSummaryRepository.Verify(x => x.GetInfraSummaryByType(It.IsAny<string>()), Times.Once);

        _mockInfraSummaryRepository.Verify(x => x.UpdateAsync(It.IsAny<Domain.Entities.InfraSummary>()), Times.Once);
    }
}