﻿using ContinuityPatrol.Application.Features.PageSolutionMapping.Events.Delete;

namespace ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Delete;

public class
    DeletePageSolutionMappingCommandHandler : IRequestHandler<DeletePageSolutionMappingCommand,
        DeletePageSolutionMappingResponse>
{
    private readonly IPageSolutionMappingRepository _pageSolutionMappingRepository;
    private readonly IPublisher _publisher;

    public DeletePageSolutionMappingCommandHandler(IPageSolutionMappingRepository pageSolutionMappingRepository,
        IPublisher publisher)
    {
        _pageSolutionMappingRepository = pageSolutionMappingRepository;
        _publisher = publisher;
    }

    public async Task<DeletePageSolutionMappingResponse> Handle(DeletePageSolutionMappingCommand request,
        CancellationToken cancellationToken)
    {
        var eventToDelete = await _pageSolutionMappingRepository.GetByReferenceIdAsync(request.Id);

        Guard.Against.Null(eventToDelete, nameof(Domain.Entities.PageSolutionMapping),
            new NotFoundException(nameof(Domain.Entities.PageSolutionMapping), request.Id));

        eventToDelete.IsActive = false;

        await _pageSolutionMappingRepository.UpdateAsync(eventToDelete);

        var response = new DeletePageSolutionMappingResponse
        {
            Message = Message.Delete(nameof(Domain.Entities.PageSolutionMapping), eventToDelete.Name),

            IsActive = eventToDelete.IsActive
        };

        await _publisher.Publish(new PageSolutionMappingDeletedEvent { Name = eventToDelete.Name }, cancellationToken);

        return response;
    }
}