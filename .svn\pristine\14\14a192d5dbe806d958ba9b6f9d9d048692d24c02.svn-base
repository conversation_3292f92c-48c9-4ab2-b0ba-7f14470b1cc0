﻿using ContinuityPatrol.Shared.Core.Specifications;

namespace ContinuityPatrol.Application.Specifications;

public class LicenseManagerFilterSpecification : Specification<LicenseManager>
{
    public LicenseManagerFilterSpecification(string searchString)
    {
        if (!string.IsNullOrEmpty(searchString))
        {
            if (searchString.Contains("="))
            {
                var stringArray = searchString.Split(';');

                foreach (var stringItem in stringArray)
                    if (stringItem.Contains("companyid=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.CompanyId.Contains(stringItem.Replace("companyid=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("ipaddress=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.IpAddress.Contains(stringItem.Replace("ipaddress=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("ponumber=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.PoNumber.Contains(stringItem.Replace("ponumber=", "",
                            StringComparison.OrdinalIgnoreCase)));

                    else if (stringItem.Contains("cphostname=", StringComparison.OrdinalIgnoreCase))
                        Or(p => p.HostName.Contains(stringItem.Replace("cphostname=", "",
                            StringComparison.OrdinalIgnoreCase)));
            }
            else
            {
                Criteria = p => p.PoNumber.Contains(searchString);
                // Criteria = p => p.CompanyId.Contains(searchString) || p.ReferenceId.Contains(searchString) || p.IPAddress.Contains(searchString) || p.PONumber.Contains(searchString) || p.CPHostName.Contains(searchString) ;
            }
        }
        else
        {
            Criteria = p => p.ReferenceId != null;
        }
    }
}