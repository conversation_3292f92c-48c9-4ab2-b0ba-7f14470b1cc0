﻿namespace ContinuityPatrol.Application.Features.Form.Queries.GetNameUnique;

public class GetFormNameUniqueQueryHandler : IRequestHandler<GetFormNameUniqueQuery, bool>
{
    private readonly IFormRepository _formRepository;

    public GetFormNameUniqueQueryHandler(IFormRepository formRepository)
    {
        _formRepository = formRepository;
    }

    public async Task<bool> Handle(GetFormNameUniqueQuery request, CancellationToken cancellationToken)
    {
        return await _formRepository.IsFormNameExist(request.FormName, request.FormId);
    }
}