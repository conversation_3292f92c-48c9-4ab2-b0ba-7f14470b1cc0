﻿using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;

namespace ContinuityPatrol.Application.UnitTests.Features.MSSQLDBMirroringStatus.Queries
{
    public class SQLDbMirroingStatusDetailQueryHandlerTests
    {
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IMsSqlDbMirroringStatusRepository> _repositoryMock;
        private readonly SQLDbMirroingStatusDetailQueryHandler _handler;

        public SQLDbMirroingStatusDetailQueryHandlerTests()
        {
            _mapperMock = new Mock<IMapper>();
            _repositoryMock = new Mock<IMsSqlDbMirroringStatusRepository>();
            _handler = new SQLDbMirroingStatusDetailQueryHandler(_mapperMock.Object, _repositoryMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedResult_WhenEntityExists()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "abc-123" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "abc-123" };
            var mapped = new MSSQLDBMirroingStatuslistVM { Id = "abc-123" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Returns(mapped);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be("abc-123");
            _repositoryMock.Verify(r => r.GetByReferenceIdAsync(query.Id), Times.Once);
            _mapperMock.Verify(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenEntityIsNull()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "not-found-id" };
            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync((Domain.Entities.MSSQLDBMirroringStatus)null);

            // Act & Assert
            var act = () => _handler.Handle(query, CancellationToken.None);

            await act.Should().ThrowAsync<NotFoundException>()
                .WithMessage("*MSSQLDBMirroringStatus*");
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFoundException_WhenMapperReturnsNull()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "valid-id" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "valid-id" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Returns((MSSQLDBMirroingStatuslistVM)null);

            // Act & Assert
            var act = () => _handler.Handle(query, CancellationToken.None);

            await act.Should().ThrowAsync<NotFoundException>()
                .WithMessage("*MSSQLDBMirroringStatus*");
        }

        [Fact]
        public async Task Handle_ShouldWorkWithEmptyId()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "" };
            var mapped = new MSSQLDBMirroingStatuslistVM { Id = "" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Returns(mapped);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be("");
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryAndMapperExactlyOnce()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "once-call" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "once-call" };
            var mapped = new MSSQLDBMirroingStatuslistVM { Id = "once-call" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Returns(mapped);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(r => r.GetByReferenceIdAsync(query.Id), Times.Once);
            _mapperMock.Verify(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldThrowNotFound_WhenEntityIsInactive()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "inactive-1" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "inactive-1", IsActive = false };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);

            // Act & Assert
            await Assert.ThrowsAsync<NotFoundException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenRepositoryThrows()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "throw-repo" };
            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ThrowsAsync(new Exception("DB failure"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldThrowException_WhenMapperThrows()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "map-crash" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus { ReferenceId = "map-crash" };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Throws(new InvalidOperationException("Mapper config broken"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _handler.Handle(query, CancellationToken.None));
        }

        [Fact]
        public async Task Handle_ShouldMapLargeEntitySuccessfully()
        {
            // Arrange
            var query = new SQLDbMirroingStatusDetailQuery { Id = "large-entity" };
            var entity = new Domain.Entities.MSSQLDBMirroringStatus
            {
                ReferenceId = "large-entity",
                InfraObjectName = new string('A', 10_000) // simulate large string
            };

            var viewModel = new MSSQLDBMirroingStatuslistVM
            {
                Id = "large-entity",
                InfraObjectName = entity.InfraObjectName
            };

            _repositoryMock.Setup(r => r.GetByReferenceIdAsync(query.Id)).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<MSSQLDBMirroingStatuslistVM>(entity)).Returns(viewModel);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.InfraObjectName.Length.Should().Be(10_000);
        }
    }
}
