﻿using ContinuityPatrol.Application.Features.DB2HADRMonitorLog.Queries.GetList;

namespace ContinuityPatrol.Application.UnitTests.Features.DB2HADRMonitorLog.Queries;

public class GetDB2HADRMonitorLogListQueryHandlerTests
{
    private readonly Mock<IDb2HadrMonitorLogRepository> _repositoryMock;
    private readonly IMapper _mapper;
    private readonly Fixture _fixture;
    private readonly GetDB2HADRMonitorLogListQueryHandler _handler;

    public GetDB2HADRMonitorLogListQueryHandlerTests()
    {
        _fixture = new Fixture();
        _repositoryMock = new Mock<IDb2HadrMonitorLogRepository>();

        var config = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Domain.Entities.DB2HADRMonitorLog, DB2HADRMonitorLogListVm>();
        });

        _mapper = config.CreateMapper();
        _handler = new GetDB2HADRMonitorLogListQueryHandler(_repositoryMock.Object, _mapper);
    }

    [Fact]
    public async Task Handle_WhenDataExists_ReturnsMappedList()
    {
        // Arrange
        var query = new GetDB2HADRMonitorLogListQuery();
        var db2Logs = _fixture.CreateMany<Domain.Entities.DB2HADRMonitorLog>(3).ToList();

        _repositoryMock.Setup(r => r.ListAllAsync())
                       .ReturnsAsync(db2Logs);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(db2Logs.Count, result.Count);
        Assert.All(result, item => Assert.IsType<DB2HADRMonitorLogListVm>(item));
        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
    }

    [Fact]
    public async Task Handle_WhenNoDataExists_ReturnsEmptyList()
    {
        // Arrange
        var query = new GetDB2HADRMonitorLogListQuery();

        _repositoryMock.Setup(r => r.ListAllAsync())
                       .ReturnsAsync(new List<Domain.Entities.DB2HADRMonitorLog>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
        _repositoryMock.Verify(r => r.ListAllAsync(), Times.Once);
    }

    [Fact]
    public void Should_Assign_All_Properties_Correctly()
    {
        // Arrange
        var vm = new DB2HADRMonitorLogListVm
        {
            Id = "log123",
            Type = "DB2-HADR",
            InfraObjectId = "infra001",
            InfraObjectName = "NodeA",
            WorkflowId = "wf789",
            WorkflowName = "HADRMonitorWorkflow",
            Properties = "{ \"syncMode\": \"async\" }",
            ConfiguredRPO = "10m",
            DataLagValue = "5m"
        };

        // Assert
        vm.Id.Should().Be("log123");
        vm.Type.Should().Be("DB2-HADR");
        vm.InfraObjectId.Should().Be("infra001");
        vm.InfraObjectName.Should().Be("NodeA");
        vm.WorkflowId.Should().Be("wf789");
        vm.WorkflowName.Should().Be("HADRMonitorWorkflow");
        vm.Properties.Should().Be("{ \"syncMode\": \"async\" }");
        vm.ConfiguredRPO.Should().Be("10m");
        vm.DataLagValue.Should().Be("5m");
    }
}
