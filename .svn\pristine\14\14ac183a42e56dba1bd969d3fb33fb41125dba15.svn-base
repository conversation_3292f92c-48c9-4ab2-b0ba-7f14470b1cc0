﻿using ContinuityPatrol.Application.Features.AlertInformation.Queries.GetList;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Domain.ViewModels.AlertInformationModel;

namespace ContinuityPatrol.Application.UnitTests.Features.AlertInformation.Queries;

public class GetAlertInformationListQueryHandlerTests : IClassFixture<AlertInformationFixture>
{
    private readonly AlertInformationFixture _alertInformationFixture;
    private Mock<IAlertInformationRepository> _mockAlertInformationRepository;
    private readonly GetAlertInformationListQueryHandler _handler;

    public GetAlertInformationListQueryHandlerTests(AlertInformationFixture alertInformationFixture)
    {
        _alertInformationFixture = alertInformationFixture;
    
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetAlertInformationRepository(_alertInformationFixture.AlertInformations);
        
        _handler = new GetAlertInformationListQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);
    }

    [Fact]
    public async Task Handle_Return_ActiveAlertInformation_Counts()
    {
        var result = await _handler.Handle(new GetAlertInformationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertInformationListVm>>();

        result.Count.ShouldBe(3);
    }

    [Fact]
    public async Task Handle_Valid_AlertInformationList()
    {
        var result = await _handler.Handle(new GetAlertInformationListQuery(), CancellationToken.None);

        result.ShouldBeOfType<List<AlertInformationListVm>>();

        result.Count.ShouldBe(3);

        result[0].Id.ShouldBe(_alertInformationFixture.AlertInformations[0].ReferenceId);
        result[0].AlertFrequency.ShouldBe(_alertInformationFixture.AlertInformations[0].AlertFrequency);
        result[0].Code.ShouldBe(_alertInformationFixture.AlertInformations[0].Code);
        result[0].Severity.ShouldBe(_alertInformationFixture.AlertInformations[0].Severity);
        result[0].Type.ShouldBe(_alertInformationFixture.AlertInformations[0].Type);
    }

    [Fact]
    public async Task Handle_Return_EmptyList_When_NoRecords()
    {
        _mockAlertInformationRepository = AlertInformationRepositoryMocks.GetAlertInformationEmptyRepository();

        var handler = new GetAlertInformationListQueryHandler(_mockAlertInformationRepository.Object, _alertInformationFixture.Mapper);

        var result = await handler.Handle(new GetAlertInformationListQuery(), CancellationToken.None);

        result.Count.ShouldBe(0);
    }

    [Fact]
    public async Task Handle_Call_ListAllAsyncMethod_OneTime()
    {
        await _handler.Handle(new GetAlertInformationListQuery(), CancellationToken.None);

        _mockAlertInformationRepository.Verify(x=>x.ListAllAsync(), Times.Once);
    }

}