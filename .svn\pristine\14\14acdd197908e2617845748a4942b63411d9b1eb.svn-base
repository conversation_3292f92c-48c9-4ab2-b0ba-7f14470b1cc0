using ContinuityPatrol.Domain.ViewModels.DynamicDashboardModel;

namespace ContinuityPatrol.Application.Features.DynamicDashboard.Queries.GetList;

public class
    GetDynamicDashboardListQueryHandler : IRequestHandler<GetDynamicDashboardListQuery, List<DynamicDashboardListVm>>
{
    private readonly IDynamicDashboardRepository _dynamicDashboardRepository;
    private readonly IMapper _mapper;

    public GetDynamicDashboardListQueryHandler(IMapper mapper, IDynamicDashboardRepository dynamicDashboardRepository)
    {
        _mapper = mapper;
        _dynamicDashboardRepository = dynamicDashboardRepository;
    }

    public async Task<List<DynamicDashboardListVm>> Handle(GetDynamicDashboardListQuery request,
        CancellationToken cancellationToken)
    {
        var dynamicDashboards = await _dynamicDashboardRepository.ListAllAsync();


        if (dynamicDashboards.Count <= 0) return new List<DynamicDashboardListVm>();

        return _mapper.Map<List<DynamicDashboardListVm>>(dynamicDashboards);
    }
}