using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Create;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Delete;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Commands.Update;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetByUserId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDefaultDashboardByRoleId;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetList;
using ContinuityPatrol.Application.Features.DynamicDashboardMap.Queries.GetNameUnique;
using ContinuityPatrol.Services.Db.Impl.Admin;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Admin;

public class DynamicDashboardMapServiceTests : BaseServiceTestSetup<DynamicDashboardMapService>, IClassFixture<DynamicDashboardMapFixture>
{
    private readonly DynamicDashboardMapFixture _fixture;

    public DynamicDashboardMapServiceTests(DynamicDashboardMapFixture fixture)
    {
        InitializeService(accessor => new DynamicDashboardMapService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetDynamicDashboardMapList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapListVm);

        var result = await ServiceUnderTest.GetDynamicDashboardMapList();

        Assert.Equal(_fixture.DynamicDashboardMapListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetByReferenceId_Should_Return_Detail()
    {
        var dynamicDashboardMapId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDynamicDashboardMapDetailQuery>(q => q.Id == dynamicDashboardMapId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapDetailVm);

        var result = await ServiceUnderTest.GetByReferenceId(dynamicDashboardMapId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DynamicDashboardMapDetailVm.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateDynamicDashboardMapResponse
        {
            Message = "Created",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateDynamicDashboardMapCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateDynamicDashboardMapCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateDynamicDashboardMapResponse
        {
            Message = "Updated",
            Id = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateDynamicDashboardMapCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateDynamicDashboardMapCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var dynamicDashboardMapId = Guid.NewGuid().ToString();
        var response = new DeleteDynamicDashboardMapResponse
        {
            Message = "Deleted",
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteDynamicDashboardMapCommand>(c => c.Id == dynamicDashboardMapId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(dynamicDashboardMapId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetDefaultDashboardByRoleId_Should_Return_Data()
    {
        var roleId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDefaultDashboardByRoleIdQuery>(q => q.RoleId == roleId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapByRoleIdVm);

        var result = await ServiceUnderTest.GetDefaultDashboardByRoleId(roleId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DynamicDashboardMapByRoleIdVm.Id, result.Id);
    }

    [Fact]
    public async Task GetDefaultDashboardByUserId_Should_Return_Data()
    {
        var userId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDynamicDashboardMapByUserIdQuery>(q => q.UserId == userId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapByUserIdVm);

        var result = await ServiceUnderTest.GetDefaultDashboardByUserId(userId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DynamicDashboardMapByUserIdVm.Id, result.Id);
    }

    [Theory]
    [InlineData("DashboardMap1", null)]
    [InlineData("AnotherDashboardMap", "some-guid")]
    public async Task IsDynamicDashboardMapNameExist_Should_Return_True(string name, string? id)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await ServiceUnderTest.IsDynamicDashboardMapNameExist(name, id);

        Assert.True(result);
    }

    [Fact]
    public async Task GetPaginatedDynamicDashboardMaps_Should_Return_Result()
    {
        var query = _fixture.GetDynamicDashboardMapPaginatedListQuery;

        MediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedDynamicDashboardMapListVm);

        var result = await ServiceUnderTest.GetPaginatedDynamicDashboardMaps(query);

        Assert.Equal(_fixture.DynamicDashboardMapListVm.Count, result.Data.Count);
    }

    [Fact]
    public async Task GetByReferenceId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetByReferenceId(null!));
    }

    [Fact]
    public async Task GetByReferenceId_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetByReferenceId(""));
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.IsDynamicDashboardMapNameExist(null!, null));
    }

    [Fact]
    public async Task IsDynamicDashboardMapNameExist_EmptyName_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.IsDynamicDashboardMapNameExist("", null));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }

    [Theory]
    [InlineData("admin-role")]
    [InlineData("user-role")]
    [InlineData("manager-role")]
    public async Task GetDefaultDashboardByRoleId_Should_Return_Data_For_Different_Roles(string roleId)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDefaultDashboardByRoleIdQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapByRoleIdVm);

        var result = await ServiceUnderTest.GetDefaultDashboardByRoleId(roleId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DynamicDashboardMapByRoleIdVm.Id, result.Id);
    }

    [Theory]
    [InlineData("user-123")]
    [InlineData("user-456")]
    [InlineData("user-789")]
    public async Task GetDefaultDashboardByUserId_Should_Return_Data_For_Different_Users(string userId)
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDynamicDashboardMapByUserIdQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DynamicDashboardMapByUserIdVm);

        var result = await ServiceUnderTest.GetDefaultDashboardByUserId(userId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DynamicDashboardMapByUserIdVm.Id, result.Id);
    }
}
