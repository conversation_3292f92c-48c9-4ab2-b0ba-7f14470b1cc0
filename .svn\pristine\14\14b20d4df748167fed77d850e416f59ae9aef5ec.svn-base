using ContinuityPatrol.Application.Features.FormType.Commands.Create;
using ContinuityPatrol.Application.Features.FormType.Commands.Update;
using ContinuityPatrol.Application.Features.FormType.Queries.GetDetail;
using ContinuityPatrol.Application.Features.FormType.Queries.GetNames;
using ContinuityPatrol.Application.Features.FormType.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.FormTypeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Services.DbTests.Fixtures;

public class FormTypeFixture
{
    public List<FormTypeNameVm> FormTypeNameVm { get; }
    public List<FormTypeListVm> FormTypeListVm { get; }
    public PaginatedResult<FormTypeListVm> PaginatedFormTypeListVm { get; }
    public FormTypeDetailVm FormTypeDetailVm { get; }
    public CreateFormTypeCommand CreateFormTypeCommand { get; }
    public UpdateFormTypeCommand UpdateFormTypeCommand { get; }
    public GetFormTypePaginatedListQuery GetFormTypePaginatedListQuery { get; }

    public FormTypeFixture()
    {
        var fixture = new Fixture();

        FormTypeNameVm = fixture.Create<List<FormTypeNameVm>>();
        FormTypeListVm = fixture.Create<List<FormTypeListVm>>();
        PaginatedFormTypeListVm = fixture.Create<PaginatedResult<FormTypeListVm>>();
        FormTypeDetailVm = fixture.Create<FormTypeDetailVm>();
        CreateFormTypeCommand = fixture.Create<CreateFormTypeCommand>();
        UpdateFormTypeCommand = fixture.Create<UpdateFormTypeCommand>();
        GetFormTypePaginatedListQuery = fixture.Create<GetFormTypePaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
