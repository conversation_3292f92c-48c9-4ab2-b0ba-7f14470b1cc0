using ContinuityPatrol.Application.Features.BiaRules.Events.Create;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;
using ContinuityPatrol.Shared.Core.Enums;

namespace ContinuityPatrol.Application.UnitTests.Features.BiaRules.Events;

public class BiaRulesCreatedEventTests : IClassFixture<BiaRulesFixture>
{
    private readonly BiaRulesFixture _biaRulesFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly Mock<ILoggedInUserService> _mockLoggedInUserService;
    private readonly Mock<ILogger<BiaRulesCreatedEventHandler>> _mockLogger;
    private readonly BiaRulesCreatedEventHandler _handler;

    public BiaRulesCreatedEventTests(BiaRulesFixture biaRulesFixture)
    {
        _biaRulesFixture = biaRulesFixture;
        _mockUserActivityRepository = BiaRulesRepositoryMocks.CreateUserActivityRepository(_biaRulesFixture.UserActivities);
        _mockLoggedInUserService = new Mock<ILoggedInUserService>();
        _mockLogger = new Mock<ILogger<BiaRulesCreatedEventHandler>>();

        // Setup logged in user service
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(Guid.NewGuid().ToString());
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns("TestUser");
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns("/api/biarules");
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns("127.0.0.1");

        _handler = new BiaRulesCreatedEventHandler(
            _mockLoggedInUserService.Object,
            _mockLogger.Object,
            _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateUserActivity_When_BiaRulesCreatedEventReceived()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RTOBiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == _mockLoggedInUserService.Object.UserId &&
            ua.LoginName == _mockLoggedInUserService.Object.LoginName &&
            ua.RequestUrl == _mockLoggedInUserService.Object.RequestedUrl &&
            ua.HostAddress == _mockLoggedInUserService.Object.IpAddress &&
            ua.Action == $"{ActivityType.Create} {Modules.BiaRules}" &&
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.ActivityType == ActivityType.Create.ToString() &&
            ua.ActivityDetails == "BiaRules 'RTO' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateCorrectUserActivity_When_RPOBiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RPO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'RPO' created successfully." &&
            ua.Action == $"{ActivityType.Create} {Modules.BiaRules}" &&
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectUserInfo_When_BiaRulesCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var loginName = "TestUser123";
        var requestUrl = "/api/v6/biarules/create";
        var ipAddress = "*************";

        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);
        _mockLoggedInUserService.Setup(x => x.LoginName).Returns(loginName);
        _mockLoggedInUserService.Setup(x => x.RequestedUrl).Returns(requestUrl);
        _mockLoggedInUserService.Setup(x => x.IpAddress).Returns(ipAddress);

        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.UserId == userId &&
            ua.LoginName == loginName &&
            ua.RequestUrl == requestUrl &&
            ua.HostAddress == ipAddress)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityDetails_When_BiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "Custom Rule Type" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules 'Custom Rule Type' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectEntityAndModule_When_BiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.Entity == Modules.BiaRules.ToString() &&
            ua.Action.Contains(Modules.BiaRules.ToString()))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCorrectActivityType_When_BiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityType == ActivityType.Create.ToString())), Times.Once);
    }

    [Fact]
    public async Task Handle_SetCreatedByAndModifiedBy_When_BiaRulesCreated()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(userId);

        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.CreatedBy == userId &&
            ua.LastModifiedBy == userId)), Times.Once);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdEmpty_When_BiaRulesCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns(string.Empty);

        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        //_mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
        //    !string.IsNullOrEmpty(ua.CreatedBy) &&
        //    !string.IsNullOrEmpty(ua.LastModifiedBy) &&
        //    Guid.TryParse(ua.CreatedBy, out _) &&
        //    Guid.TryParse(ua.LastModifiedBy, out _))), Times.Once);
    }

    [Fact]
    public async Task Handle_SetDefaultGuidWhenUserIdNull_When_BiaRulesCreated()
    {
        // Arrange
        _mockLoggedInUserService.Setup(x => x.UserId).Returns((string)null);

        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        //_mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
        //    !string.IsNullOrEmpty(ua.CreatedBy) &&
        //    !string.IsNullOrEmpty(ua.LastModifiedBy) &&
        //    Guid.TryParse(ua.CreatedBy, out _) &&
        //    Guid.TryParse(ua.LastModifiedBy, out _))), Times.Once);
    }

    [Fact]
    public async Task Handle_LogInformation_When_BiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        //_mockLogger.Verify(
        //    x => x.Log(
        //        LogLevel.Information,
        //        It.IsAny<EventId>(),
        //       // It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("BiaRules 'RTO' created successfully.")),
        //        It.IsAny<Exception>(),
        //       // It.IsAny<Func<It.IsAnyType, Exception, string>>()),
        //    Times.Once);
    }

    [Fact]
    public async Task Handle_HandleNullEventName_When_BiaRulesCreatedWithNullName()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = null };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_HandleEmptyEventName_When_BiaRulesCreatedWithEmptyName()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = string.Empty };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == "BiaRules '' created successfully.")), Times.Once);
    }


    [Fact]
    public async Task Handle_CallRepositoryOnce_When_EventHandled()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
        _mockUserActivityRepository.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_CreateEventWithComplexRuleName_When_BiaRulesCreated()
    {
        // Arrange
        var complexRuleName = "Complex RTO Rule with Special Characters & Numbers 123";
        var createdEvent = new BiaRulesCreatedEvent { Name = complexRuleName };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        _mockUserActivityRepository.Verify(x => x.AddAsync(It.Is<Domain.Entities.UserActivity>(ua =>
            ua.ActivityDetails == $"BiaRules '{complexRuleName}' created successfully.")), Times.Once);
    }

    [Fact]
    public async Task Handle_VerifyEventType_When_BiaRulesCreated()
    {
        // Arrange
        var createdEvent = new BiaRulesCreatedEvent { Name = "RTO" };

        // Act
        await _handler.Handle(createdEvent, CancellationToken.None);

        // Assert
        createdEvent.ShouldBeOfType<BiaRulesCreatedEvent>();
        createdEvent.ShouldBeAssignableTo<INotification>();
    }
}
