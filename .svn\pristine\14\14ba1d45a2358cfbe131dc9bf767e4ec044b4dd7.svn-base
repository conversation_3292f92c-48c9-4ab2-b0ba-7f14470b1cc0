﻿using ContinuityPatrol.Application.Features.Workflow.Commands.Create;
using ContinuityPatrol.Application.UnitTests.Attributes;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.Workflow.Validators;

public class CreateWorkflowValidatorTests
{
    public List<Domain.Entities.Workflow> Workflows { get; set; }

    private readonly Mock<IWorkflowRepository> _mockWorkflowRepository;
    private CreateWorkflowCommandValidator _validator;
	public CreateWorkflowValidatorTests()
    {
        Workflows = new Fixture().Create<List<Domain.Entities.Workflow>>();

        _mockWorkflowRepository = WorkflowRepositoryMocks.CreateWorkflowRepository(Workflows);
    }

    //NAME

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Name_WithEmpty(CreateWorkflowCommand createWorkflowCommand)
    {
        var validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "";

        var validateResult = await validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Name is required", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameRequired, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Name_IsNull(CreateWorkflowCommand createWorkflowCommand)
    {
        var validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = null;

        var validateResult = await validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("'Name' must not be empty.", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameNotEmpty, (string)validateResult.Errors[1].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Name_MinimumRange(CreateWorkflowCommand createWorkflowCommand)
    {
        var validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "AB";

        var validateResult = await validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Name should contain between 3 to 200 characters", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameRange, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Name_MaxiMumRange(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "ABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZABCDEFGHIJKLMNOPQRSTUVWXYZ_ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        createWorkflowCommand.Properties = "{\"key\":\"value\"}";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);
		Assert.Contains(validateResult.Errors, e => e.ErrorMessage == "Name should contain between 3 to 200 characters");

		
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = " PTS ";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }


    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_Numbers_Only(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "123415447";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_SpecialCharacters_Only(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "!@#$^%^&*(><?";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_SpecialCharacters_InFront(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "!@PTS";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_SpecialCharacters_InBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "PTSINDIA%$";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_DoubleSpace_InFront(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "  PTsINDIA";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_OneSpace_InBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "PTsINDIA ";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_TripleSpace_InBetween(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "PTS   INDIA";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_DoubleSpace_InBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "PTS INDIA  ";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "_PTINDIA";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront_andBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "_PTINDIA_";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_Numbers_InFront(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "123PTINDIA";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_UnderScoreAndNumbers_InFront_AndUnderScore_InBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "_123PTINDIA_";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }

    [Theory]
    [AutoWorkflowData]
    public async Task Verify_CreateWorkflowCommandValidator_Valid_Name_With_UnderScore_InFront_AndNumbers_InBack(CreateWorkflowCommand createWorkflowCommand)
    {
		_validator = new CreateWorkflowCommandValidator(_mockWorkflowRepository.Object);

        createWorkflowCommand.Name = "_PTINDIA123";

        var validateResult = await _validator.ValidateAsync(createWorkflowCommand, CancellationToken.None);

        var result = await _validator.ValidateAsync(createWorkflowCommand);

        Assert.Contains("Please Enter Valid Name", result.Errors.Select(e => e.ErrorMessage));

        Assert.Equal(ValidatorConstants.Workflow.WorkflowNameValid, (string)validateResult.Errors[0].ErrorMessage);
    }
}