using ContinuityPatrol.Application.Features.DashboardView.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardView.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetBusinessViewPaginatedList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByEntityId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetByLast7Days;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessFunctionId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDashboardViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDatalagByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDcMappingList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetImpactAvailabilityByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetItViewList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetNames;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetResilienceHealthStatus;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetServiceTopologyList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.GetSitePropertiesByBusinessServiceId;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetBreachDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetDCMappingBySites;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetLastDrillDetails;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetSiteList;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetTotalSiteDetailForOneView;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.GetVerifyWorkflowDetail;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewEntitiesEvent;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationCyberSecurity;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OneView.OneViewRiskMitigationFailedDrill;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetBusinessImpactAnalysis;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetComponentFailureAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetDrillAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalAvailabilityAnalytics;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetOperationalHealthSummary;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetSlaBreach;
using ContinuityPatrol.Application.Features.DashboardView.Queries.OperationalAnalytics.GetWorkflowAnalytics;
using ContinuityPatrol.Application.Features.DrReady.Queries.GetDrReadyByBusinessServiceId;
using ContinuityPatrol.Application.Features.Rto.Queries.GetRTOByBusinessServiceId;
using ContinuityPatrol.Services.Db.Impl.Dashboard;
using ContinuityPatrol.Services.DbTests.Fixtures;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Services.DbTests.Impl.Dashboard;

public class DashboardViewServiceTests : BaseServiceTestSetup<DashboardViewService>, IClassFixture<DashboardViewFixture>
{
    private readonly DashboardViewFixture _fixture;

    public DashboardViewServiceTests(DashboardViewFixture fixture)
    {
        InitializeService(accessor => new DashboardViewService(accessor));
        _fixture = fixture;
    }

    [Fact]
    public async Task GetDashboardViews_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDashboardViewListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewListVm);

        var result = await ServiceUnderTest.GetDashboardViews();

        Assert.Equal(_fixture.DashboardViewListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetDashboardViewById_Should_Return_Detail()
    {
        var dashboardViewId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDashboardViewDetailQuery>(q => q.Id == dashboardViewId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewDetailVm);

        var result = await ServiceUnderTest.GetDashboardViewById(dashboardViewId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DashboardViewDetailVm.Id, result.Id);
    }

    [Fact]
    public async Task CreateAsync_Should_Return_Success()
    {
        var response = new CreateDashboardViewResponse
        {
            Message = "Created",
            BusinessViewId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.CreateDashboardViewCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.CreateAsync(_fixture.CreateDashboardViewCommand);

        Assert.True(result.Success);
        Assert.Equal("Created", result.Message);
    }

    [Fact]
    public async Task UpdateAsync_Should_Return_Success()
    {
        var response = new UpdateDashboardViewResponse
        {
            Message = "Updated",
            BusinessViewId = Guid.NewGuid().ToString()
        };

        MediatorMock.Setup(m => m.Send(_fixture.UpdateDashboardViewCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.UpdateAsync(_fixture.UpdateDashboardViewCommand);

        Assert.True(result.Success);
        Assert.Equal("Updated", result.Message);
    }

    [Fact]
    public async Task DeleteAsync_Should_Call_Mediator()
    {
        var dashboardViewId = Guid.NewGuid().ToString();
        var response = new DeleteDashboardViewResponse
        {
            Message = "Deleted",
            IsActive = false
        };

        MediatorMock.Setup(m => m.Send(It.Is<DeleteDashboardViewCommand>(c => c.BusinessViewId == dashboardViewId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await ServiceUnderTest.DeleteAsync(dashboardViewId);

        Assert.True(result.Success);
        Assert.Equal("Deleted", result.Message);
    }

    [Fact]
    public async Task GetBusinessViews_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetBusinessViewPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessViewPaginatedList);

        var result = await ServiceUnderTest.GetBusinessViews();

        Assert.Equal(_fixture.BusinessViewPaginatedList.Count, result.Count);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDashboardViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetDashboardViewListByBusinessServiceId(businessServiceId);

        Assert.Equal(_fixture.DashboardViewByBusinessServiceIdVm.Count, result.Count);
    }

    [Fact]
    public async Task GetDashboardViewListByBusinessFunctionId_Should_Return_Data()
    {
        var businessFunctionId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDashboardViewByBusinessFunctionIdQuery>(q => q.BusinessFunctionId == businessFunctionId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewByBusinessFunctionIdVm);

        var result = await ServiceUnderTest.GetDashboardViewListByBusinessFunctionId(businessFunctionId);

        Assert.Equal(_fixture.DashboardViewByBusinessFunctionIdVm.Count, result.Count);
    }

    [Fact]
    public async Task GetDashboardViewListByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDashboardViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetDashboardViewByInfraObjectIdVm);

        var result = await ServiceUnderTest.GetDashboardViewListByInfraObjectId(infraObjectId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetDashboardViewByInfraObjectIdVm.Id, result.Id);
    }

    [Fact]
    public async Task GetITViewByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetItViewByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ItViewByInfraObjectIdVm);

        var result = await ServiceUnderTest.GetITViewByInfraObjectId(infraObjectId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.ItViewByInfraObjectIdVm.Id, result.Id);
    }

    [Fact]
    public async Task GetDashboardViewById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetDashboardViewById(null!));
    }

    [Fact]
    public async Task GetDashboardViewById_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetDashboardViewById(""));
    }

    [Fact]
    public async Task DeleteAsync_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.DeleteAsync(null!));
    }

    [Fact]
    public async Task DeleteAsync_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.DeleteAsync(""));
    }



    [Fact]
    public async Task GetItViewByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetItViewByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ItViewByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetItViewByBusinessServiceId(businessServiceId);

        Assert.Equal(_fixture.ItViewByBusinessServiceIdVm.Count, result.Count);
    }

    [Fact]
    public async Task GetMonitorServiceStatusByIdAndType_Should_Return_Data()
    {
        var monitorId = Guid.NewGuid().ToString();
        var type = "TestType";

        MediatorMock.Setup(m => m.Send(It.Is<GetByEntityIdQuery>(q => q.EntityId == monitorId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetByEntityIdVm);

        var result = await ServiceUnderTest.GetMonitorServiceStatusByIdAndType(monitorId, type);

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetByEntityIdVm.Id, result.Id);
    }

    [Fact]
    public async Task GetDcMappingDetails_Should_Return_Data()
    {
        var siteId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDcMappingListQuery>(q => q.SiteId == siteId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetDcMappingListVm);

        var result = await ServiceUnderTest.GetDcMappingDetails(siteId);

        Assert.Equal(_fixture.GetDcMappingListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetDatalagStatusByLast7DaysList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDataLagStatusbyLast7DaysQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DataLagStatusbyLast7DaysVm);

        var result = await ServiceUnderTest.GetDatalagStatusByLast7DaysList();

        Assert.Equal(_fixture.DataLagStatusbyLast7DaysVm.Count, result.Count);
    }

    [Fact]
    public async Task GetItViewList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetItViewListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetItViewListVm);

        var result = await ServiceUnderTest.GetItViewList();

        Assert.Equal(_fixture.GetItViewListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetDashboardNames_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDashboardNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewNameVm);

        var result = await ServiceUnderTest.GetDashboardNames();

        Assert.Equal(_fixture.DashboardViewNameVm.Count, result.Count);
    }



    [Fact]
    public async Task GetBusinessServiceTopologyByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetServiceTopologyListQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetServiceTopologyListVm);

        var result = await ServiceUnderTest.GetBusinessServiceTopologyByBusinessServiceId(businessServiceId);

        Assert.Equal(_fixture.GetServiceTopologyListVm.Count, result.Count);
    }

    [Fact]
    public async Task GetRTOByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetRTOByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.RtoByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetRTOByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.RtoByBusinessServiceIdVm.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetDrReadyByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDrReadyByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DrReadyByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetDrReadyByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DrReadyByBusinessServiceIdVm.TotalBusinessFunction, result.TotalBusinessFunction);
    }

    [Fact]
    public async Task GetImpactAvailabilityByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetImpactAvailabilityByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardImpactAvailabilityDetailVm);

        var result = await ServiceUnderTest.GetImpactAvailabilityByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DashboardImpactAvailabilityDetailVm.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetDashboardImpactAvailabilityByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetImpactAvailabilityByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardImpactAvailabilityDetailVm);

        var result = await ServiceUnderTest.GetDashboardImpactAvailabilityByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DashboardImpactAvailabilityDetailVm.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetDataLagByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetDataLagDetailByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DatalagByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetDataLagByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.DatalagByBusinessServiceIdVm.BusinessServiceId, result.BusinessServiceId);
    }

    [Fact]
    public async Task GetSitePropertiesByBusinessServiceId_Should_Return_Data()
    {
        var businessServiceId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<GetSitePropertiesByBusinessServiceIdQuery>(q => q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.SitePropertiesByBusinessServiceIdVm);

        var result = await ServiceUnderTest.GetSitePropertiesByBusinessServiceId(businessServiceId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.SitePropertiesByBusinessServiceIdVm.SiteProperties, result.SiteProperties);
    }

    [Fact]
    public async Task GetResilienceHealthStatusByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        MediatorMock.Setup(m => m.Send(It.Is<ResilienceHealthStatusDetailQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ResilienceHealthStatusDetailVm);

        var result = await ServiceUnderTest.GetResilienceHealthStatusByInfraObjectId(infraObjectId);

        Assert.NotNull(result);
        Assert.Equal(_fixture.ResilienceHealthStatusDetailVm.InfraObjectName, result.InfraObjectName);
    }

    [Fact]
    public async Task GetResilienceHealthStatusByInfraObjectId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => ServiceUnderTest.GetResilienceHealthStatusByInfraObjectId(null!));
    }

    [Fact]
    public async Task GetResilienceHealthStatusByInfraObjectId_EmptyId_Should_Throw_ArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => ServiceUnderTest.GetResilienceHealthStatusByInfraObjectId(""));
    }

    #region OneView Tests

    [Fact]
    public async Task GetSiteCountList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetSiteCountListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.SiteCountListVm);

        var result = await ServiceUnderTest.GetSiteCountList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetVerifiedWorkflowList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetVerifyWorkflowDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.VerifyWorkflowDetailVm);

        var result = await ServiceUnderTest.GetVerifiedWorkflowList();

        Assert.NotNull(result);
        Assert.Equal(_fixture.VerifyWorkflowDetailVm.TotalWorkflowCount, result.TotalWorkflowCount);
    }

    [Fact]
    public async Task GetBreachDetails_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetBreachDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BreachDetailVm);

        var result = await ServiceUnderTest.GetBreachDetails();

        Assert.NotNull(result);
        Assert.Equal(_fixture.BreachDetailVm.RpoAchievedCount, result.RpoAchievedCount);
    }

    [Fact]
    public async Task GetDcMappingSiteDetails_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetDcMappingSitesQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetDcMappingSitesVm);

        var result = await ServiceUnderTest.GetDcMappingSiteDetails();

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetDcMappingSitesVm.TotalSiteCount, result.TotalSiteCount);
    }

    [Fact]
    public async Task GetTotalSiteDetailsForOneView_Should_Return_Data()
    {
        var siteIds = new List<string> { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() };
        var categoryType = "Database";

        MediatorMock.Setup(m => m.Send(It.Is<GetTotalSiteDetailForOneViewQuery>(q => q.SiteIds == siteIds && q.CategoryType == categoryType), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.TotalSiteDetailForOneViewListVm);

        var result = await ServiceUnderTest.GetTotalSiteDetailsForOneView(siteIds, categoryType);

        Assert.NotNull(result);
        Assert.Equal(_fixture.TotalSiteDetailForOneViewListVm, result);
    }

    [Fact]
    public async Task GetLastDrillDetails_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<LastDrillDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.LastDrillDetailVm);

        var result = await ServiceUnderTest.GetLastDrillDetails();

        Assert.NotNull(result);
        Assert.Equal(_fixture.LastDrillDetailVm.ProfileName, result.ProfileName);
    }

    [Fact]
    public async Task GetOneViewEntitiesEventViewList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetOneViewEntitiesEventQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.OneViewEntitiesEventViewList);

        var result = await ServiceUnderTest.GetOneViewEntitiesEventViewList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetOneViewRiskmitigationCyberSecurityList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationCyberSecurityQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.OneViewRiskMitigationCyberSecurityViewList);

        var result = await ServiceUnderTest.GetOneViewRiskmitigationCyberSecurityList();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetOneViewRiskmitigationFailedDrillList_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetOneViewRiskMitigationFailedDrillQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.OneViewRiskMitigationFailedDrillViewList);

        var result = await ServiceUnderTest.GetOneViewRiskmitigationFailedDrillList();

        Assert.Equal(3, result.Count);
    }

    #endregion

    #region Analytics Tests

    [Fact]
    public async Task GetDrillAnalytics_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetOperationalReadinessQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DrillAnalyticsDetailVm);

        var result = await ServiceUnderTest.GetDrillAnalytics();

        Assert.NotNull(result);
        Assert.Equal(_fixture.DrillAnalyticsDetailVm.ExecutedProfileCount, result.ExecutedProfileCount);
    }

    [Fact]
    public async Task GetComponentFailureAnalytics_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<ComponentFailureAnalyticsDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.ComponentFailureAnalyticsDetailVm);

        var result = await ServiceUnderTest.GetComponentFailureAnalytics();

        Assert.NotNull(result);
        Assert.Equal(_fixture.ComponentFailureAnalyticsDetailVm.TotalComponent, result.TotalComponent);
    }

    [Fact]
    public async Task GetSlaBreach_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetSlaBreachListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetSlaBreachListVm);

        var result = await ServiceUnderTest.GetSlaBreach();

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetSlaBreachListVm.SlaBreachCount, result.SlaBreachCount);
    }

    [Fact]
    public async Task GetOperationalAvailabilityAnalytics_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetOperationalAvailabilityAnalyticsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetOperationalAvailabilityAnalyticsDetailVm);

        var result = await ServiceUnderTest.GetOperationalAvailabilityAnalytics();

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetOperationalAvailabilityAnalyticsDetailVm.TotalBusinessServiceCount, result.TotalBusinessServiceCount);
    }

    [Fact]
    public async Task GetWorkflowAnalytics_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetWorkflowAnalyticsQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GetWorkflowAnalyticsDetailVm);

        var result = await ServiceUnderTest.GetWorkflowAnalytics();

        Assert.NotNull(result);
        Assert.Equal(_fixture.GetWorkflowAnalyticsDetailVm.ExecutedWorkFlowCount, result.ExecutedWorkFlowCount);
    }

    [Fact]
    public async Task GetOperationalServiceHealthSummary_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<OperationalHealthSummaryQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.OperationalHealthSummaryDetailVmList);

        var result = await ServiceUnderTest.GetOperationalServiceHealthSummary();

        Assert.Equal(3, result.Count);
    }

    [Fact]
    public async Task GetBusinessImpactAnalysisAsync_Should_Return_Data()
    {
        MediatorMock.Setup(m => m.Send(It.IsAny<GetBusinessImpactAnalysisQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.BusinessImpactAnalysisVmList);

        var result = await ServiceUnderTest.GetBusinessImpactAnalysisAsync();

        Assert.Equal(3, result.Count);
    }

    #endregion
}
