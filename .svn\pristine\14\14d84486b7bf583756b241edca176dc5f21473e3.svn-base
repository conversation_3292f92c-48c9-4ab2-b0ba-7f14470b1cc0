﻿using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;

namespace ContinuityPatrol.Shared.Infrastructure.Persistence;

public abstract class ModuleDbContext : DbContext
{
    private readonly ILoggedInUserService _loggedInUserService;

    protected ModuleDbContext(DbContextOptions options, ILoggedInUserService loggedInUserService) : base(options)
    {
        _loggedInUserService = loggedInUserService;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ModuleDbContext).Assembly);

        modelBuilder.RemovePluralizingTableNameConvention();

        modelBuilder.ApplyColumnNamesToUpper();
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = new())
    {
        foreach (var entry in ChangeTracker.Entries<AuditableEntity>())
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.ReferenceId = entry.Entity.ReferenceId.IsNotNullOrWhiteSpace() ? entry.Entity.ReferenceId : Guid.NewGuid().ToString();
                    entry.Entity.CreatedDate = DateTime.Now;
                    entry.Entity.CreatedBy = entry.Entity.CreatedBy.IsNullOrEmpty()
                        ? _loggedInUserService.UserId
                        : entry.Entity.CreatedBy;
                    entry.Entity.LastModifiedDate = DateTime.Now;
                    entry.Entity.LastModifiedBy = entry.Entity.CreatedBy.IsNullOrEmpty()
                        ? _loggedInUserService.UserId
                        : entry.Entity.CreatedBy;
                    entry.Entity.IsActive = true;
                    break;
                case EntityState.Modified:
                    entry.Entity.LastModifiedDate = DateTime.Now;
                    entry.Entity.LastModifiedBy = _loggedInUserService.UserId.IsNullOrEmpty()
                        ? entry.Entity.LastModifiedBy
                        : _loggedInUserService.UserId;
                    break;
                case EntityState.Detached:
                    break;
                case EntityState.Unchanged:
                    break;
                case EntityState.Deleted:
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

        return base.SaveChangesAsync(cancellationToken);
    }
}