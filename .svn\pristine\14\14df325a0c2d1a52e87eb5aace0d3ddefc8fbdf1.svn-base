﻿using ContinuityPatrol.Application.Features.Node.Commands.Create;
using ContinuityPatrol.Application.Features.Node.Commands.Update;
using ContinuityPatrol.Domain.ViewModels.NodeModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Application.Features.Node.Queries.GetPaginatedList;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Application.Features.Node.Events.PaginatedView;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Configuration.Controllers;

[Area("Configuration")]
public class NodeController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly ILogger<NodeController> _logger;
    private readonly IDataProvider _dataProvider;
    private readonly IMapper _mapper;

    public NodeController(IPublisher publisher, ILogger<NodeController> logger, IDataProvider dataProvider, IMapper mapper)
    {
        _publisher = publisher;
        _logger = logger;
        _dataProvider = dataProvider;
        _mapper = mapper;
    }

    [AntiXss]
    [EventCode(EventCodes.Node.List)]
    public async Task<IActionResult> List()
    {
        _logger.LogDebug("Entering List method in Node");

        await _publisher.Publish(new NodePaginatedEvent());
        var nodeList = await _dataProvider.Node.GetNodeList();
        var node = new NodeViewModel
        {
            Node = nodeList
        };
        return View(node);
    }

    [HttpGet]
    [EventCode(EventCodes.Node.GetPagination)]
    public async Task<JsonResult> GetPagination(GetNodePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in Node");

        try
        {
            var nodeLists = await _dataProvider.Node.GetPaginatedNodes(query);

            _logger.LogDebug("Successfully retrieved node paginated list on node page");

            return Json(new { success = true, data = nodeLists });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on node page while processing the pagination request.", ex);

            return ex.GetJsonException();
        }
    }
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.Node.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(NodeViewModel nodelist)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in Node");

        var nodeId = Request.Form["id"].ToString();

        try
        {
            if (nodeId.IsNullOrWhiteSpace())
            {
                var formModel = _mapper.Map<CreateNodeCommand>(nodelist);

                var result = await _dataProvider.Node.CreateAsync(formModel);

                _logger.LogDebug($"Creating Node '{formModel.Name}'");

                TempData.NotifySuccess(result.Message);
            }
            else
            {
                var formModel = _mapper.Map<UpdateNodeCommand>(nodelist);

                var result = await _dataProvider.Node.UpdateAsync(formModel);

                _logger.LogDebug($"Updating Node '{formModel.Name}'");

                TempData.NotifySuccess(result.Message);
            }
            _logger.LogDebug("CreateOrUpdate operation completed successfully in Node, returning view.");

            return RedirectToAction("List");
        }
        catch (ValidationException ex)
        {
            _logger.LogError($"Validation error on node page: {ex.ValidationErrors.FirstOrDefault()}");

            TempData.NotifyWarning(ex.ValidationErrors.FirstOrDefault());

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on node page while processing the request for create or update.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [Authorize(Policy = Permissions.Configuration.Delete)]
    [EventCode(EventCodes.Node.CreateOrUpdate)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in Node");

        try
        {
            var infra = await _dataProvider.Node.DeleteAsync(id);

            _logger.LogDebug("Successfully deleted record in node");

            TempData.NotifySuccess(infra.Message);

            return RedirectToAction("List");
        }
        catch (Exception ex)
        {

            _logger.Exception("An error occurred while deleting record on node.", ex);

            TempData.NotifyWarning(ex.GetMessage());

            return RedirectToAction("List");
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Node.IsNodeNameExist)]
    public async Task<bool> IsNodeNameExist(string nodeName, string id)
    {
        _logger.LogDebug("Entering IsNodeNameExist method in Node");
        try
        {
            _logger.LogDebug("Returning result for IsNodeNameExist on node");

            return await _dataProvider.Node.IsNodeNameExist(nodeName, id);
        }
        catch (Exception ex)
        {
            _logger.Exception($"An error occurred on node while checking if node name exists for : {nodeName}.", ex);

            return false;
        }
    }


    [HttpGet]
    [EventCode(EventCodes.Node.GetNodeNames)]
    public async Task<JsonResult> GetNodeNames()
    {
        _logger.LogDebug("Entering GetNodeNames method in Node");
        try
        {
            var nodeNames = await _dataProvider.Node.GetNodeNames();

            _logger.LogDebug("Successfully retrieved node names in Node");

            return Json(new { success = true, data = nodeNames });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on node page while retrieving node names.", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.Node.GetByReferenceId)]
    public async Task<JsonResult> GetByReferenceId(string id)
    {
        _logger.LogDebug("Entering GetByReferenceId method in Node");
        try
        {
            var getNodeById = await _dataProvider.Node.GetByReferenceId(id);

            _logger.LogDebug($"Successfully retrieved Node by Id '{id}'");

            return Json(new { success = true, data = getNodeById });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on node page while retrieving node details.", ex);

            return ex.GetJsonException();
        }
    }
}