﻿using ContinuityPatrol.Application.Features.FormType.Events.Update;
using ContinuityPatrol.Application.UnitTests.Fixtures;
using ContinuityPatrol.Application.UnitTests.Mocks;

namespace ContinuityPatrol.Application.UnitTests.Features.FormType.Events;

public class FormTypeUpdatedEventHandlerTests : IClassFixture<FormTypeFixture>
{
    private readonly FormTypeFixture _formTypeFixture;
    private readonly Mock<IUserActivityRepository> _mockUserActivityRepository;
    private readonly FormTypeUpdatedEventHandler _handler;

    public FormTypeUpdatedEventHandlerTests(FormTypeFixture formTypeFixture)
    {
        _formTypeFixture = formTypeFixture;

        var mockLoggedInUserService = new Mock<ILoggedInUserService>();

        var mockFormTypeEventLogger = new Mock<ILogger<FormTypeUpdatedEventHandler>>();

        _mockUserActivityRepository = FormTypeRepositoryMocks.CreateFormTypeEventRepository(_formTypeFixture.UserActivities);

        _handler = new FormTypeUpdatedEventHandler(mockLoggedInUserService.Object, mockFormTypeEventLogger.Object, _mockUserActivityRepository.Object);
    }

    [Fact]
    public async Task Handle_IncreaseUserActivityCount_When_UpdateFormTypeEventUpdated()
    {
        _formTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_formTypeFixture.FormTypeUpdatedEvent, CancellationToken.None);

        result.Equals(_formTypeFixture.UserActivities[0].LoginName);

        await Task.CompletedTask;
    }

    [Fact]
    public async Task Handle_Call_AddAsyncMethod_OnlyOnce()
    {
        await _handler.Handle(_formTypeFixture.FormTypeUpdatedEvent, CancellationToken.None);

        _mockUserActivityRepository.Verify(x => x.AddAsync(It.IsAny<Domain.Entities.UserActivity>()), Times.Once);
    }

    [Fact]
    public async Task Handle_Return_UserActivityCount_When_UpdateFormTypeEventUpdated()
    {
        _formTypeFixture.UserActivities[0].LoginName = "Test";

        var result = _handler.Handle(_formTypeFixture.FormTypeUpdatedEvent, CancellationToken.None);

        result.Equals(_formTypeFixture.UserActivities[0].Id);

        result.Equals(_formTypeFixture.FormTypeUpdatedEvent.FormTypeName);

        await Task.CompletedTask;
    }
}