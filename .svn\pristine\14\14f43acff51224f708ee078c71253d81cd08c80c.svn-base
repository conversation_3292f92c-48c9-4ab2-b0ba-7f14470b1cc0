﻿using ContinuityPatrol.Shared.Core.Contracts.Persistence;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Application.Contracts.Persistence;

public interface IFormRepository : IRepository<Form>
{
    Task<List<Form>> GetFormsByIds(List<string> ids);
    Task<List<string>> IsFormNamesUnique(List<string> name);
    Task<List<Form>> GetFormNames();
    Task<bool> IsFormNameExist(string name, string id);
    Task<List<Form>> GetFormType(string type);
    Task<bool> IsFormNameUnique(string name);
    Task<PaginatedResult<Form>> GetFormByTypeQueryable(string type, int pageNumber, int pageSize, Specification<Form> productFilterSpec,string sortColumn,string sortOrder);
}