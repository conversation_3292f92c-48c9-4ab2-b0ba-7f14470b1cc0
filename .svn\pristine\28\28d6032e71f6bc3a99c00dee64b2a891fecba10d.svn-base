using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Application.Features.User.Commands.Delete;
using ContinuityPatrol.Application.Features.User.Commands.ForgotPassword;
using ContinuityPatrol.Application.Features.User.Commands.ResetPassword;
using ContinuityPatrol.Application.Features.User.Commands.Update;
using ContinuityPatrol.Application.Features.User.Commands.UpdatePassword;
using ContinuityPatrol.Application.Features.User.Commands.UserLock;
using ContinuityPatrol.Application.Features.User.Commands.UserUnLock;
using ContinuityPatrol.Application.Features.User.Queries.GetDetail;
using ContinuityPatrol.Application.Features.User.Queries.GetDomain;
using ContinuityPatrol.Application.Features.User.Queries.GetDomainGroup;
using ContinuityPatrol.Application.Features.User.Queries.GetDomainUser;
using ContinuityPatrol.Application.Features.User.Queries.GetHasUser;
using ContinuityPatrol.Application.Features.User.Queries.GetLastFivePassword;
using ContinuityPatrol.Application.Features.User.Queries.GetList;
using ContinuityPatrol.Application.Features.User.Queries.GetLoginName;
using ContinuityPatrol.Application.Features.User.Queries.GetNames;
using ContinuityPatrol.Application.Features.User.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.User.Queries.GetUserProfile;
using ContinuityPatrol.Application.Features.User.Queries.GetUserRole;
using ContinuityPatrol.Application.Features.User.Queries.GetUsersByUserRole;
using ContinuityPatrol.Domain.ViewModels.UserModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class UsersControllerTests : IClassFixture<UserFixture>
{
    private readonly UserFixture _userFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IUserRepository> _userRepositoryMock;
    private readonly UsersController _controller;

    public UsersControllerTests(UserFixture userFixture)
    {
        _userFixture = userFixture;
        _userRepositoryMock = new Mock<IUserRepository>();
        _mapperMock = new Mock<IMapper>();

        var testBuilder = new ControllerTestBuilder<UsersController>();
        _controller = testBuilder.CreateController(
            _ => new UsersController(_userRepositoryMock.Object),
            out _mediatorMock, _mapperMock);
    }

    #region GetUsers Tests

    [Fact]
    public async Task GetUsers_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_userFixture.UserListVm);

        // Act
        var result = await _controller.GetUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var users = Assert.IsAssignableFrom<List<UserListVm>>(okResult.Value);
        Assert.Equal(3, users.Count);
        Assert.Contains(users, u => u.LoginName == "admin");
        Assert.Contains(users, u => u.LoginName == "user1");
        Assert.Contains(users, u => u.LoginName == "manager");
        Assert.All(users, u => Assert.NotNull(u.CompanyName));
    }

    [Fact]
    public async Task GetUsers_ReturnsEmptyList_WhenNoUsersExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserListQuery>(), default))
            .ReturnsAsync(new List<UserListVm>());

        // Act
        var result = await _controller.GetUsers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<UserListVm>)okResult.Value!));
    }

    #endregion

    #region GetUserById Tests

    [Fact]
    public async Task GetUserById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _userFixture.UserDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetUserById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<UserDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.LoginName, returnedDetail.LoginName);
        Assert.Equal(expectedDetail.LoginType, returnedDetail.LoginType);
    }

    [Fact]
    public async Task GetUserById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUserById("invalid-guid"));
    }

    [Fact]
    public async Task GetUserById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUserById(""));
    }

    [Fact]
    public async Task GetUserById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("User", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetUserById(nonExistentId));
    }

    #endregion

    #region GetUsersByUserRole Tests

    [Fact]
    public async Task GetUsersByUserRole_WithValidRole_ReturnsOkResult()
    {
        // Arrange
        var role = Guid.NewGuid().ToString();
        var expectedList = new List<UsersByUserRoleVm>
    {
        new UsersByUserRoleVm
        {
            Id = "1",
            LoginName = "user1",
            InfraObjectAllFlag = true,
            UserInfraObject = new UserInfraObjectListVm
            {
                UserId = "1",
                Properties = "{}",
                IsApplication = 1
            }
        }
    };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetUsersByUserRoleQuery>(q => q.UserRole == role),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetUsersByUserRole(role);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<UsersByUserRoleVm>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.Equal(expectedList[0].LoginName, returnedList[0].LoginName);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetUsersByUserRoleQuery>(q => q.UserRole == role),
            It.IsAny<CancellationToken>()), Times.Once);
    }


    [Fact]
    public async Task GetUsersByUserRole_WithInvalidRole_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUsersByUserRole("invalid-guid"));
    }

    #endregion

    #region GetUserNames Tests

    [Fact]
    public async Task GetUserNames_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetUserNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_userFixture.UserNameVm);

        // Act
        var result = await _controller.GetUserNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var userNames = Assert.IsAssignableFrom<List<UserNameVm>>(okResult.Value);
        Assert.Equal(3, userNames.Count);
        Assert.All(userNames, u => Assert.NotNull(u.RoleName));
    }

    #endregion

    #region CreateDefaultUser Tests

    [Fact]
    public async Task CreateDefaultUser_WithNoExistingUser_ReturnsOkResult()
    {
        var expectedResponse = new CreateUserResponse
        {
            UserId = "USER_001",
            Success = true,
            Message = "User created successfully"
        };

        _mediatorMock.Setup(m => m.Send(It.IsAny<CreateUserCommand>(), default)).ReturnsAsync(expectedResponse);
        _mapperMock
            .Setup(m => m.Map<CreateUserCommand>(It.IsAny<CreateDefaultUserCommand>()))
            .Returns(_userFixture.CreateUserCommand);

        var result = await _controller.CreateDefaultUser(_userFixture.CreateDefaultUserCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateUserResponse>(okResult.Value);
        Assert.Equal(expectedResponse.UserId, returnedResponse.UserId);
        Assert.True(returnedResponse.Success);
    }



    [Fact]
    public async Task CreateDefaultUser_WhenUsersExist_ThrowsInvalidException()
    {
        // Arrange
        var command = _userFixture.CreateDefaultUserCommand;

        _userRepositoryMock
            .Setup(r => r.HasUserAsync())
            .ReturnsAsync(true);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidException>(() =>
            _controller.CreateDefaultUser(command));
    }

    #endregion

    #region HasUser Tests

    [Fact]
    public async Task HasUser_ReturnsExpectedResult()
    {
        // Arrange
        var expectedResult = _userFixture.HasUserResult;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetHasUserQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.HasUser();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    #endregion

    #region UpdateUserAccessKey Tests

    [Fact]
    public async Task UpdateUserAccessKey_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _userFixture.UpdatePasswordCommand;
        var expectedResponse = _userFixture.UpdatePasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUserAccessKey(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePasswordResponse>(okResult.Value);
        Assert.Equal("Password updated successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region CreateUser Tests

    [Fact]
    public async Task CreateUser_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userFixture.CreateUserCommand;
        var expectedResponse = _userFixture.CreateUserResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateUser(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateUserResponse>(createdResult.Value);
        Assert.Equal("User created successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.UserId);
    }

   

    #endregion

    #region UpdateUser Tests

    [Fact]
    public async Task UpdateUser_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _userFixture.UpdateUserCommand;
        var expectedResponse = _userFixture.UpdateUserResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateUser(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateUserResponse>(okResult.Value);
        Assert.Equal("User updated successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.NotNull(returnedResponse.UserId);
    }

    [Fact]
    public async Task UpdateUser_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _userFixture.UpdateUserCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("User", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateUser(command));
    }

    #endregion

    #region DeleteUser Tests

    [Fact]
    public async Task DeleteUser_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _userFixture.DeleteUserResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteUserCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteUser(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteUserResponse>(okResult.Value);
        Assert.Equal("User deleted successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteUser_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteUser("invalid-guid"));
    }

    [Fact]
    public async Task DeleteUser_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteUser(""));
    }

    [Fact]
    public async Task DeleteUser_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteUserCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("User", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteUser(nonExistentId));
    }

    #endregion

    #region GetUserRoleById Tests

    [Fact]
    public async Task GetUserRoleById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedRole = _userFixture.UserRoleVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserRoleQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedRole);

        // Act
        var result = await _controller.GetUserRoleById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.IsType<UserRoleVm>(okResult.Value);
    }

    [Fact]
    public async Task GetUserRoleById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetUserRoleById("invalid-guid"));
    }

    #endregion

    #region ForgotPassword Tests

    [Fact]
    public async Task ForgotPassword_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userFixture.ForgotPasswordCommand;
        var expectedResponse = _userFixture.ForgotPasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.ForgotPassword(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<ForgotPasswordResponse>(createdResult.Value);
        Assert.Equal("Password reset email sent", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region ResetPassword Tests

    [Fact]
    public async Task ResetPassword_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userFixture.ResetPasswordCommand;
        var expectedResponse = _userFixture.ResetPasswordResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.ResetPassword(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<ResetPasswordResponse>(createdResult.Value);
        Assert.Equal("Password reset successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region GetPaginatedUsers Tests

    [Fact]
    public async Task GetPaginatedUsers_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _userFixture.GetUserPaginatedListQuery;
        var expectedResult = _userFixture.PaginatedUsers;

        _mediatorMock
            .Setup(m => m.Send(query, default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedUsers(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsType<PaginatedResult<UserViewListVm>>(okResult.Value);
        Assert.Equal(1, paginatedResult.Data.Count);
        Assert.Equal(10, paginatedResult.PageSize);
        Assert.Equal(1, paginatedResult.TotalCount);
        Assert.Equal(1, paginatedResult.TotalPages);
    }

    #endregion

    #region IsUserNameExist Tests

    [Fact]
    public async Task IsUserNameExist_WithValidLoginName_ReturnsOkResult()
    {
        // Arrange
        var loginName = "testuser";
        var id = "0";
        var expectedResult = _userFixture.IsUserNameExistResult;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserNameUniqueQuery>(
                q => q.LoginName == loginName && q.UserId == id), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.IsUserNameExist(loginName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }


    [Fact]
    public async Task IsUserNameExist_WithEmptyLoginName_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.IsUserNameExist("", "0"));
    }

    #endregion

    #region IsNewPasswordInLastFive Tests

    [Fact]
    public async Task IsNewPasswordInLastFive_WithValidParameters_ReturnsOkResult()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var newPassword = "TestPassword123!";
        var expectedResult = _userFixture.IsPasswordInLastFiveResult;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserLastFivePasswordQuery>(
                q => q.UserId == userId && q.NewPassword == newPassword), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.IsNewPasswordInLastFive(userId, newPassword);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    #endregion

    #region GetLoginName Tests

    [Fact]
    public async Task GetLoginName_WithValidLoginName_ReturnsOkResult()
    {
        // Arrange
        var loginName = "admin";
        var expectedResult = _userFixture.UserLoginNameVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserLoginNameQuery>(q => q.LoginName == loginName), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetLoginName(loginName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<UserLoginNameVm>(okResult.Value);
        Assert.Equal(expectedResult.LoginName, returnedResult.LoginName);
    }

   

    #endregion

    #region UserLock Tests

    [Fact]
    public async Task UserLock_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userFixture.UserLockCommand;
        var expectedResponse = _userFixture.UserLockResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UserLock(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<UserLockResponse>(createdResult.Value);
        Assert.Equal("User locked successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region UserUnLock Tests

    [Fact]
    public async Task UserUnLock_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _userFixture.UserUnLockCommand;
        var expectedResponse = _userFixture.UserUnLockResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UserUnLock(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<UserUnLockResponse>(createdResult.Value);
        Assert.Equal("User unlocked successfully", returnedResponse.Message);
        Assert.True(returnedResponse.Success);
    }

    #endregion

    #region GetUserProfile Tests

    [Fact]
    public async Task GetUserProfile_WithValidUserId_ReturnsOkResult()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var expectedProfile = _userFixture.UserProfileDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetUserProfileDetailQuery>(q => q.UserId == userId), default))
            .ReturnsAsync(expectedProfile);

        // Act
        var result = await _controller.GetUserProfile(userId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.IsType<UserProfileDetailVm>(okResult.Value);
    }

    #endregion

    #region GetDomain Tests

    [Fact]
    public async Task GetDomain_ReturnsExpectedList()
    {
        // Arrange
        var expectedDomains = _userFixture.DomainList;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDomainListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedDomains);

        // Act
        var result = await _controller.GetDomain();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var domains = Assert.IsAssignableFrom<List<string>>(okResult.Value);
        Assert.Equal(3, domains.Count);
        Assert.Contains("DOMAIN1.COM", domains);
        Assert.Contains("DOMAIN2.COM", domains);
        Assert.Contains("TESTDOMAIN.LOCAL", domains);
    }

    #endregion

    #region GetDomainUserName Tests

    [Fact]
    public async Task GetDomainUserName_WithValidDomainName_ReturnsOkResult()
    {
        // Arrange
        var domainName = "TestDomain";
        var domainUserName = "TestUser";
        var expectedList = new List<string> { "User1", "User2" };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetDomainUserListQuery>(q => q.Name == domainName && q.DomainUserName == domainUserName),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDomainUserName(domainName, domainUserName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<string>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.Equal(expectedList[0], returnedList[0]);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetDomainUserListQuery>(q => q.Name == domainName && q.DomainUserName == domainUserName),
            It.IsAny<CancellationToken>()), Times.Once);
    }




    #endregion

    #region GetDomainGroups Tests

    [Fact]
    public async Task GetDomainGroups_WithValidDomainName_ReturnsOkResult()
    {
        // Arrange
        var domainName = "TestDomain";
        var groupName = "TestGroup";
        var expectedList = new List<string> { "Group1", "Group2" };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetDomainGroupListQuery>(q => q.DomainName == domainName && q.GroupName == groupName),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);

        // Act
        var result = await _controller.GetDomainGroups(domainName, groupName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedList = Assert.IsType<List<string>>(okResult.Value);
        Assert.Equal(expectedList.Count, returnedList.Count);
        Assert.Equal(expectedList[0], returnedList[0]);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetDomainGroupListQuery>(q => q.DomainName == domainName && q.GroupName == groupName),
            It.IsAny<CancellationToken>()), Times.Once);
    }



    #endregion
}
