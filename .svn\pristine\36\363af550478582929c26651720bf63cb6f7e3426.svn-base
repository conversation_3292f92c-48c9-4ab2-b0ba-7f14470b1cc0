﻿using AutoFixture;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PageBuilderFixture
{
    public CreatePageBuilderCommand CreateCommand { get; }
    public UpdatePageBuilderCommand UpdateCommand { get; }
    public DeletePageBuilderCommand DeleteCommand { get; }
    public PageBuilderDetailVm DetailVm { get; }
    public GetPageBuilderPaginatedListQuery PaginatedQuery { get; }
    public PaginatedResult<PageBuilderListVm> PaginatedResult { get; }

    public PageBuilderFixture()
    {
        var fixture = new Fixture();

        CreateCommand = fixture.Create<CreatePageBuilderCommand>();
        UpdateCommand = fixture.Create<UpdatePageBuilderCommand>();
        DeleteCommand = fixture.Create<DeletePageBuilderCommand>();
        DetailVm = fixture.Create<PageBuilderDetailVm>();
        PaginatedQuery = fixture.Create<GetPageBuilderPaginatedListQuery>();

        var listVm = fixture.CreateMany<PageBuilderListVm>(3).ToList();

        PaginatedResult = new PaginatedResult<PageBuilderListVm>(
            //items: listVm,
            //totalCount: listVm.Count,
            //currentPage: 1,
            //pageSize: 10
        );
    }
}