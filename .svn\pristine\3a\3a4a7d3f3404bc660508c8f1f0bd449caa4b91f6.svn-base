using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class GroupPolicyFixture : IDisposable
{
    public List<GroupPolicyListVm> GroupPolicyListVm { get; }
    public PaginatedResult<GroupPolicyListVm> PaginatedGroupPolicyListVm { get; }
    public GetGroupPolicyDetailVm GroupPolicyDetailVm { get; }
    public List<GroupPolicyNameVm> GroupPolicyNameVmList { get; }
    public List<GroupPolicyTypeVm> GroupPolicyTypeVmList { get; }
    public CreateGroupPolicyCommand CreateGroupPolicyCommand { get; }
    public UpdateGroupPolicyCommand UpdateGroupPolicyCommand { get; }
    public GetGroupPolicyPaginatedListQuery GetGroupPolicyPaginatedListQuery { get; }

    public GroupPolicyFixture()
    {
        var fixture = new Fixture();

        GroupPolicyListVm = fixture.Create<List<GroupPolicyListVm>>();
        PaginatedGroupPolicyListVm = fixture.Create<PaginatedResult<GroupPolicyListVm>>();
        GroupPolicyDetailVm = fixture.Create<GetGroupPolicyDetailVm>();
        GroupPolicyNameVmList = fixture.Create<List<GroupPolicyNameVm>>();
        GroupPolicyTypeVmList = fixture.Create<List<GroupPolicyTypeVm>>();
        CreateGroupPolicyCommand = fixture.Create<CreateGroupPolicyCommand>();
        UpdateGroupPolicyCommand = fixture.Create<UpdateGroupPolicyCommand>();
        GetGroupPolicyPaginatedListQuery = fixture.Create<GetGroupPolicyPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
