﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.PageBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetList;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PageBuildersControllerTests : IClassFixture<PageBuilderFixture>
{
    private readonly PageBuilderFixture _fixture;
    private readonly PageBuildersController _controller;
    private readonly Mock<IMediator> _mediatorMock;

    public PageBuildersControllerTests(PageBuilderFixture fixture)
    {
        _fixture = fixture;

        var testBuilder = new ControllerTestBuilder<PageBuildersController>();
        _controller = testBuilder.CreateController(
            _ => new PageBuildersController(),
            out _mediatorMock
        );
    }

    [Fact]
    public async Task GetPaginatedPageBuilders_ReturnsPaginatedResult()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageBuilderPaginatedListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedResult);

        var result = await _controller.GetPaginatedPageBuilders(_fixture.PaginatedQuery);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actual = Assert.IsType<PaginatedResult<PageBuilderListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedResult.TotalCount, actual.TotalCount);
    }

    [Fact]
    public async Task GetPageBuilderById_ReturnsDetailVm()
    {
        var id = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageBuilderDetailQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DetailVm);

        var result = await _controller.GetPageBuilderById(id);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var actual = Assert.IsType<PageBuilderDetailVm>(okResult.Value);
        Assert.Equal(_fixture.DetailVm.Id, actual.Id);
    }

    [Fact]
    public async Task CreatePageBuilder_ReturnsCreatedAtAction()
    {
        var expectedResponse = new CreatePageBuilderResponse { Id = "new-id" };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CreatePageBuilderCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        var result = await _controller.CreatePageBuilder(_fixture.CreateCommand);

    }

    [Fact]
    public async Task UpdatePageBuilder_ReturnsUpdatedResponse()
    {
        var expectedResponse = new UpdatePageBuilderResponse { Id = _fixture.UpdateCommand.Id };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<UpdatePageBuilderCommand>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);

        var result = await _controller.UpdatePageBuilder(_fixture.UpdateCommand);

       
    }

    [Fact]
    public async Task DeletePageBuilder_ReturnsOkResult_WithExpectedResponse()
    {
        // Arrange
        var id = Guid.NewGuid().ToString();
        var expectedResponse = new DeletePageBuilderResponse
        {
            Success = true,
            Message = "Deleted successfully",
            IsActive = false
        };
        
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<DeletePageBuilderCommand>(cmd => cmd.Id == id),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResponse);
        
        // Act
        var result = await _controller.DeletePageBuilder(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var response = Assert.IsType<DeletePageBuilderResponse>(okResult.Value);
        Assert.Equal(expectedResponse.Success, response.Success);
        Assert.Equal(expectedResponse.Message, response.Message);
        Assert.Equal(expectedResponse.IsActive, response.IsActive);
    }
    [Fact]
    public async Task GetPageBuilders_ReturnsOkResult_WithExpectedList()
    {
        // Arrange
        var expectedList = new List<PageBuilderListVm>
        {
            new PageBuilderListVm
            {
                Id = "1",
                Name = "Builder1",
                Type = "TypeA",
                Properties = "{}",
                IsLock = false,
                IsPublish = true
            },
            new PageBuilderListVm
            {
                Id = "2",
                Name = "Builder2",
                Type = "TypeB",
                Properties = "{\"key\":\"value\"}",
                IsLock = true,
                IsPublish = false
            }
        };

        var mediatorMock = new Mock<IMediator>();
        mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedList);
        
        // Act
        var result = await _controller.GetPageBuilders();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
       
    }

    [Fact]
    public async Task IsPageBuilderNameExist_ReturnsOkResult_WithExpectedValue()
    {
        // Arrange
        var pageBuilderName = "TestBuilder";
        var id = "123";
        var expectedExists = true;

        
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetPageBuilderNameUniqueQuery>(q => q.Name == pageBuilderName && q.Id == id),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedExists);
        
        var result = await _controller.IsPageBuilderNameExist(pageBuilderName, id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        var value = Assert.IsType<bool>(okResult.Value);
        Assert.Equal(expectedExists, value);
    }
}
