using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class GlobalVariableFixture : IDisposable
{
    public List<GlobalVariableListVm> GlobalVariableListVm { get; }
    public PaginatedResult<GlobalVariableListVm> PaginatedGlobalVariableListVm { get; }
    public GlobalVariableDetailVm GlobalVariableDetailVm { get; }
    public List<GlobalVariableDetailVm> GlobalVariableDetailVmList { get; }
    public CreateGlobalVariableCommand CreateGlobalVariableCommand { get; }
    public UpdateGlobalVariableCommand UpdateGlobalVariableCommand { get; }
    public GetGlobalVariablePaginatedListQuery GetGlobalVariablePaginatedListQuery { get; }

    public GlobalVariableFixture()
    {
        var fixture = new Fixture();

        GlobalVariableListVm = fixture.Create<List<GlobalVariableListVm>>();
        PaginatedGlobalVariableListVm = fixture.Create<PaginatedResult<GlobalVariableListVm>>();
        GlobalVariableDetailVm = fixture.Create<GlobalVariableDetailVm>();
        GlobalVariableDetailVmList = fixture.Create<List<GlobalVariableDetailVm>>();
        CreateGlobalVariableCommand = fixture.Create<CreateGlobalVariableCommand>();
        UpdateGlobalVariableCommand = fixture.Create<UpdateGlobalVariableCommand>();
        GetGlobalVariablePaginatedListQuery = fixture.Create<GetGlobalVariablePaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
