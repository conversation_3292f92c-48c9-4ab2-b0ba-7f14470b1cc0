using AutoFixture;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPostgresMonitorStatusByInfraObjectId;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PostgresMonitorStatusFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<PostgresMonitorStatus> PostgresMonitorStatuses { get; set; }
    public List<PostgresMonitorStatus> InvalidPostgresMonitorStatuses { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<PostgresMonitorStatusListVm> PostgresMonitorStatusListVm { get; }
    public PostgresMonitorStatusDetailVm PostgresMonitorStatusDetailVm { get; }
    public List<PostgresMonitorStatusDetailByTypeVm> PostgresMonitorStatusDetailByTypeVm { get; }
    public PaginatedResult<PostgresMonitorStatusListVm> PostgresMonitorStatusPaginatedListVm { get; }
    public string InfraObjectIdResult { get; }

    // Commands
    public CreatePostgresMonitorStatusCommand CreatePostgresMonitorStatusCommand { get; set; }
    public UpdatePostgresMonitorStatusCommand UpdatePostgresMonitorStatusCommand { get; set; }

    // Queries
    public GetPostgresMonitorStatusDetailQuery GetPostgresMonitorStatusDetailQuery { get; set; }
    public GetPostgresMonitorStatusListQuery GetPostgresMonitorStatusListQuery { get; set; }
    public GetPostgresMonitorStatusDetailByTypeQuery GetPostgresMonitorStatusByTypeQuery { get; set; }
    public GetPostgresMonitorStatusPaginatedListQuery GetPostgresMonitorStatusPaginatedListQuery { get; set; }
    public GetPostgresMonitorStatusByInfraObjectIdQuery GetPostgresMonitorStatusByInfraObjectIdQuery { get; set; }

    // Responses
    public CreatePostgresMonitorStatusResponse CreatePostgresMonitorStatusResponse { get; set; }
    public UpdatePostgresMonitorStatusResponse UpdatePostgresMonitorStatusResponse { get; set; }

    public PostgresMonitorStatusFixture()
    {
        try
        {
            // Create test data using AutoFixture
            PostgresMonitorStatuses = AutoPostgresMonitorStatusFixture.Create<List<PostgresMonitorStatus>>();
            InvalidPostgresMonitorStatuses = AutoPostgresMonitorStatusFixture.Create<List<PostgresMonitorStatus>>();
            UserActivities = AutoPostgresMonitorStatusFixture.Create<List<UserActivity>>();

            // Set invalid postgres monitor statuses to inactive
            foreach (var invalidStatus in InvalidPostgresMonitorStatuses)
            {
                invalidStatus.IsActive = false;
            }

            // Commands
            CreatePostgresMonitorStatusCommand = AutoPostgresMonitorStatusFixture.Create<CreatePostgresMonitorStatusCommand>();
            UpdatePostgresMonitorStatusCommand = AutoPostgresMonitorStatusFixture.Create<UpdatePostgresMonitorStatusCommand>();

            // Set command IDs to match existing entities
            if (PostgresMonitorStatuses.Any())
            {
                UpdatePostgresMonitorStatusCommand.Id = PostgresMonitorStatuses.First().ReferenceId;
            }

            // Queries
            GetPostgresMonitorStatusDetailQuery = AutoPostgresMonitorStatusFixture.Create<GetPostgresMonitorStatusDetailQuery>();
            GetPostgresMonitorStatusListQuery = AutoPostgresMonitorStatusFixture.Create<GetPostgresMonitorStatusListQuery>();
            GetPostgresMonitorStatusByTypeQuery = AutoPostgresMonitorStatusFixture.Create<GetPostgresMonitorStatusDetailByTypeQuery>();
            GetPostgresMonitorStatusPaginatedListQuery = AutoPostgresMonitorStatusFixture.Create<GetPostgresMonitorStatusPaginatedListQuery>();
            GetPostgresMonitorStatusByInfraObjectIdQuery = AutoPostgresMonitorStatusFixture.Create<GetPostgresMonitorStatusByInfraObjectIdQuery>();

            // Set query IDs to match existing entities
            if (PostgresMonitorStatuses.Any())
            {
                GetPostgresMonitorStatusDetailQuery.Id = PostgresMonitorStatuses.First().ReferenceId;
                GetPostgresMonitorStatusByTypeQuery.Type = PostgresMonitorStatuses.First().Type;
                GetPostgresMonitorStatusByInfraObjectIdQuery.InfraObjectId = PostgresMonitorStatuses.First().InfraObjectId;
            }

            // Responses
            CreatePostgresMonitorStatusResponse = AutoPostgresMonitorStatusFixture.Create<CreatePostgresMonitorStatusResponse>();
            UpdatePostgresMonitorStatusResponse = AutoPostgresMonitorStatusFixture.Create<UpdatePostgresMonitorStatusResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            PostgresMonitorStatuses = new List<PostgresMonitorStatus>();
            InvalidPostgresMonitorStatuses = new List<PostgresMonitorStatus>();
            UserActivities = new List<UserActivity>();
            CreatePostgresMonitorStatusCommand = new CreatePostgresMonitorStatusCommand();
            UpdatePostgresMonitorStatusCommand = new UpdatePostgresMonitorStatusCommand();
            GetPostgresMonitorStatusDetailQuery = new GetPostgresMonitorStatusDetailQuery();
            GetPostgresMonitorStatusListQuery = new GetPostgresMonitorStatusListQuery();
            GetPostgresMonitorStatusByTypeQuery = new GetPostgresMonitorStatusDetailByTypeQuery();
            GetPostgresMonitorStatusPaginatedListQuery = new GetPostgresMonitorStatusPaginatedListQuery();
            GetPostgresMonitorStatusByInfraObjectIdQuery = new GetPostgresMonitorStatusByInfraObjectIdQuery();
            CreatePostgresMonitorStatusResponse = new CreatePostgresMonitorStatusResponse();
            UpdatePostgresMonitorStatusResponse = new UpdatePostgresMonitorStatusResponse();
        }

        // Configure View Models
        PostgresMonitorStatusListVm = new List<PostgresMonitorStatusListVm>
        {
            new PostgresMonitorStatusListVm
            {
                Id = "PMS_001",
                Type = "POSTGRES_STREAMING_REPLICATION",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "PostgreSQL Primary Server",
                WorkflowId = "WF_001",
                WorkflowName = "PostgreSQL Streaming Replication",
                ConfiguredRPO = "30",
                DataLagValue = "5",
                Properties = "{\"status\": \"healthy\", \"role\": \"primary\", \"replication_state\": \"streaming\"}",
                Threshold = "60"
            },
            new PostgresMonitorStatusListVm
            {
                Id = "PMS_002",
                Type = "POSTGRES_LOGICAL_REPLICATION",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "PostgreSQL Secondary Server",
                WorkflowId = "WF_002",
                WorkflowName = "PostgreSQL Logical Replication",
                ConfiguredRPO = "60",
                DataLagValue = "10",
                Properties = "{\"status\": \"healthy\", \"role\": \"subscriber\", \"replication_state\": \"logical\"}",
                Threshold = "120"
            },
            new PostgresMonitorStatusListVm
            {
                Id = "PMS_003",
                Type = "POSTGRES_HOT_STANDBY",
                InfraObjectId = "INFRA_003",
                InfraObjectName = "PostgreSQL Hot Standby Server",
                WorkflowId = "WF_003",
                WorkflowName = "PostgreSQL Hot Standby",
                ConfiguredRPO = "15",
                DataLagValue = "3",
                Properties = "{\"status\": \"healthy\", \"role\": \"standby\", \"replication_state\": \"hot_standby\"}",
                Threshold = "30"
            },
            new PostgresMonitorStatusListVm
            {
                Id = "PMS_004",
                Type = "POSTGRES_WARM_STANDBY",
                InfraObjectId = "INFRA_004",
                InfraObjectName = "PostgreSQL Warm Standby Server",
                WorkflowId = "WF_004",
                WorkflowName = "PostgreSQL Warm Standby",
                ConfiguredRPO = "120",
                DataLagValue = "25",
                Properties = "{\"status\": \"warning\", \"role\": \"standby\", \"replication_state\": \"warm_standby\"}",
                Threshold = "180"
            },
            new PostgresMonitorStatusListVm
            {
                Id = "PMS_005",
                Type = "POSTGRES_ARCHIVE_RECOVERY",
                InfraObjectId = "INFRA_005",
                InfraObjectName = "PostgreSQL Archive Recovery Server",
                WorkflowId = "WF_005",
                WorkflowName = "PostgreSQL Archive Recovery",
                ConfiguredRPO = "240",
                DataLagValue = "45",
                Properties = "{\"status\": \"healthy\", \"role\": \"recovery\", \"replication_state\": \"archive_recovery\"}",
                Threshold = "300"
            }
        };

        PostgresMonitorStatusDetailVm = new PostgresMonitorStatusDetailVm
        {
            Id = "PMS_001",
            Type = "POSTGRES_STREAMING_REPLICATION",
            InfraObjectId = "INFRA_001",
            InfraObjectName = "PostgreSQL Primary Server",
            WorkflowId = "WF_001",
            WorkflowName = "PostgreSQL Streaming Replication",
            ConfiguredRPO = "30",
            DataLagValue = "5",
            Properties = "{\"status\": \"healthy\", \"role\": \"primary\", \"replication_state\": \"streaming\"}"
        };

        PostgresMonitorStatusDetailByTypeVm = new List<PostgresMonitorStatusDetailByTypeVm>
        {
            new PostgresMonitorStatusDetailByTypeVm
            {
                Id = "PMS_001",
                Type = "POSTGRES_STREAMING_REPLICATION",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "PostgreSQL Primary Server",
                WorkflowId = "WF_001",
                WorkflowName = "PostgreSQL Streaming Replication",
                ConfiguredRPO = "30",
                DataLagValue = "5",
                Properties = "{\"status\": \"healthy\", \"role\": \"primary\", \"replication_state\": \"streaming\"}"
            },
            new PostgresMonitorStatusDetailByTypeVm
            {
                Id = "PMS_006",
                Type = "POSTGRES_STREAMING_REPLICATION",
                InfraObjectId = "INFRA_006",
                InfraObjectName = "PostgreSQL Streaming Replica",
                WorkflowId = "WF_006",
                WorkflowName = "PostgreSQL Streaming Replication Replica",
                ConfiguredRPO = "30",
                DataLagValue = "8",
                Properties = "{\"status\": \"healthy\", \"role\": \"replica\", \"replication_state\": \"streaming\"}"
            }
        };

        PostgresMonitorStatusPaginatedListVm = new PaginatedResult<PostgresMonitorStatusListVm>
        {
            Data = PostgresMonitorStatusListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        InfraObjectIdResult = "PMS_001";

        // Configure AutoMapper for PostgresMonitorStatus mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PostgresMonitorStatusProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoPostgresMonitorStatusFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize PostgresMonitorStatus entity
            fixture.Customize<PostgresMonitorStatus>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Type, "POSTGRES_STREAMING_REPLICATION")
                .With(b => b.InfraObjectId, "INFRA_TEST")
                .With(b => b.InfraObjectName, "Test PostgreSQL Server")
                .With(b => b.WorkflowId, "WF_TEST")
                .With(b => b.WorkflowName, "Test PostgreSQL Workflow")
                .With(b => b.ConfiguredRPO, "30")
                .With(b => b.DataLagValue, "5")
                .With(b => b.Properties, "{\"status\": \"healthy\", \"role\": \"primary\"}")
                .With(b => b.Threshold, "60"));

            // Customize CreatePostgresMonitorStatusCommand
            fixture.Customize<CreatePostgresMonitorStatusCommand>(c => c
                .With(b => b.Type, "POSTGRES_STREAMING_REPLICATION")
                .With(b => b.InfraObjectId, "INFRA_NEW")
                .With(b => b.InfraObjectName, "New PostgreSQL Server")
                .With(b => b.WorkflowId, "WF_NEW")
                .With(b => b.WorkflowName, "New PostgreSQL Workflow")
                .With(b => b.ConfiguredRPO, "30")
                .With(b => b.DataLagValue, "5")
                .With(b => b.Properties, "{\"status\": \"healthy\", \"role\": \"primary\"}")
                .With(b => b.Threshold, "60"));

            // Customize UpdatePostgresMonitorStatusCommand
            fixture.Customize<UpdatePostgresMonitorStatusCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Type, "POSTGRES_LOGICAL_REPLICATION")
                .With(b => b.InfraObjectId, "INFRA_UPD")
                .With(b => b.InfraObjectName, "Updated PostgreSQL Server")
                .With(b => b.WorkflowId, "WF_UPD")
                .With(b => b.WorkflowName, "Updated PostgreSQL Workflow")
                .With(b => b.ConfiguredRPO, "60")
                .With(b => b.DataLagValue, "10")
                .With(b => b.Properties, "{\"status\": \"healthy\", \"role\": \"subscriber\"}")
                .With(b => b.Threshold, "120"));

            // Customize Queries
            fixture.Customize<GetPostgresMonitorStatusDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetPostgresMonitorStatusDetailByTypeQuery>(c => c
                .With(b => b.Type, "POSTGRES_STREAMING_REPLICATION"));

            fixture.Customize<GetPostgresMonitorStatusPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Type")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetPostgresMonitorStatusByInfraObjectIdQuery>(c => c
                .With(b => b.InfraObjectId, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreatePostgresMonitorStatusResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "PostgresMonitorStatus has been created successfully"));

            fixture.Customize<UpdatePostgresMonitorStatusResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "PostgresMonitorStatus has been updated successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "PostgresMonitorStatus")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
