{
  "Logging": {
    "LogLevel": {
      "Default": "Error",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "None",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "None",
      "Microsoft.EntityFrameworkCore": "None"
    }
  },
  "Twilio": {
    "AccountSid": "**********************************",
    "AuthToken": "7b4ae07e897f2140c650c23ee6763606"
  },
  "ApiSettings": {
    "GatewayUrl": "https://localhost:1001/"
  },
  "SeqConfig": {
    "ServerUrl": "http://localhost:5341",
    "ApiKey": "YNgDU0FtfvHkChDdO2xP",
    "Username": "eP9LNIYSBVwVV4WQ6MducnR//dnewZrRbWBEmjjVHEA=$H3r86NBjEQ3rGpB0mqTxQg8jzo5ghek6mHeq3N+a8vrZ",
    "Password": "drXltNHflna07X6TalHC08EBRH3HKFuTNroCm4LVD7k=$LZKU8c5Upf+0Pt1XGTkmgARhE3Ccf81YC+ClrkH8daXv55AT+Q==",
    "path": "C:\\CP\\Logs\\SeqLogs\\"
  },
  "ConnectionStrings": {
    //"DBProvider": "rdxaGwrUzOG64J3DEms4GQ==",
    //"Default": "VQ/a7fq43aT5wO2G3jErUOm4ExfJ/Tq9C2FE1f0xQnZnX/SK+LqZnzY6m7XnDRseT3gynxvSWa92iIMGYo2ttq/2UjtmkEMvxWOw/6hJQH0/YRl6zFktJwjH6rE6BAPVRifY0Qx9xcfSxfmW8pX+kw=="

    "DBProvider": "905clI+aAqPTFEWo83NkxQ==",
    "Default": "T8ungq28y9VO2qW7tCqkoWLojWOoS6BA0ZgOkEHcFQO5O9pLZSPTb5quqGwX20A38VY72/VN1YyB4vy7Y5ZhUzJkNRHC0LMt8lxoTFPdg6RAqRCbBEmVvQtCQTyaOFlCaB7fqHERlHOHUIRtbF8+1wasCzC2ok7NsxzOEV8Rmc5CImhzl2355PnEM6AVndJvNsrbjTREfZOt6kNt2/XunYwXkNMnV5EyOeQ4LI+sa0U="
  },
  "SignalR": {
    "Url": "https://localhost:7079/"
  },
  "CP": {
    "Version": "6.0"
  },
  "UpdateApiVersion": {
    "Version": "6.0.0"
  },
  "Modules": {
    "Path": "Modules\\net6.0",
    "ModulePart": "ContinuityPatrol.Modules.*.dll"
  },
  "x-api-key": "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp",
  "WireMockApi": {
    "BaseAddress": "https://localhost:2000"
  },

  "CacheSettings": {
    "SlidingExpiration": 60
  },
  "JwtSettings": {
    "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
    "Issuer": "http://localhost:8047",
    "Audience": "http://localhost:8047",
    "DurationInMinutes": 60
  },

  "Policies": {
    "Default": "localhost"
  },
  "App": {
    "CorsOrigins": "http://localhost:3000,http://localhost:3001,http://subdomain1.localhost:3000,http://subdomain2.localhost:3000"
  },


  "Serilog": {
    "Using": [ "Serilog.Sinks.File", "Serilog.Enrichers.ClientInfo" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Quartz": "Error"
      }
    },

    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\CP\\Logs\\Configuration_API_log-.txt",
          "fileSizeLimitBytes": "524288000",
          "rollOnFileSizeLimit": true,
          "retainedFileCountLimit": null,
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{Level:u3}] : [{CorrelationId}] [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": [ "WithClientIp", "WithMachineName", "WithThreadId" ]
  },
  "RedisCacheUrl": "127.0.0.1:6379,abortConnect=false,connectTimeout=30000,responseTimeout=30000",
  "AllowedHosts": "*"
}