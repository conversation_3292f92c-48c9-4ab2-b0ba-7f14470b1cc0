﻿using AutoFixture;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures
{
    public class PageSolutionMappingFixture
    {
        public CreatePageSolutionMappingCommand CreateCommand { get; }
        public UpdatePageSolutionMappingCommand UpdateCommand { get; }
        public DeletePageSolutionMappingCommand DeleteCommand { get; }
        public GetPageSolutionMappingPaginatedListQuery PaginatedQuery { get; }
        public PageSolutionMappingDetailVm DetailVm { get; }
        public List<PageSolutionMappingListVm> ListVm { get; }
        public PaginatedResult<PageSolutionMappingListVm> PaginatedResult { get; }

        public PageSolutionMappingFixture()
        {
            var fixture = new Fixture();

            CreateCommand = fixture.Create<CreatePageSolutionMappingCommand>();
            UpdateCommand = fixture.Create<UpdatePageSolutionMappingCommand>();
            DeleteCommand = fixture.Create<DeletePageSolutionMappingCommand>();
            PaginatedQuery = fixture.Create<GetPageSolutionMappingPaginatedListQuery>();
            DetailVm = fixture.Create<PageSolutionMappingDetailVm>();
            ListVm = fixture.CreateMany<PageSolutionMappingListVm>(3).ToList();

            PaginatedResult = new PaginatedResult<PageSolutionMappingListVm>(
              //  items: ListVm,
               // totalCount: ListVm.Count,
               // currentPage: 1,
               // pageSize: 10
            );
        }
    }
}
