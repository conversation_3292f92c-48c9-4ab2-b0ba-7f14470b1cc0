using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class DashboardViewLogFixture : IDisposable
{
    public List<DashboardViewLogListVm> DashboardViewLogListVm { get; }
    public PaginatedResult<DashboardViewLogListVm> PaginatedDashboardViewLogListVm { get; }
    public DashboardViewLogDetailVm DashboardViewLogDetailVm { get; }
    public CreateDashboardViewLogCommand CreateDashboardViewLogCommand { get; }
    public UpdateDashboardViewLogCommand UpdateDashboardViewLogCommand { get; }
    public GetDashboardViewLogPaginatedListQuery GetDashboardViewLogPaginatedListQuery { get; }
    public DataLagStatusReport DataLagStatusReport { get; }
    public List<InfraObjectHealthScore> InfraObjectHealthScoreList { get; }

    public DashboardViewLogFixture()
    {
        var fixture = new Fixture();

        DashboardViewLogListVm = fixture.Create<List<DashboardViewLogListVm>>();
        PaginatedDashboardViewLogListVm = fixture.Create<PaginatedResult<DashboardViewLogListVm>>();
        DashboardViewLogDetailVm = fixture.Create<DashboardViewLogDetailVm>();
        CreateDashboardViewLogCommand = fixture.Create<CreateDashboardViewLogCommand>();
        UpdateDashboardViewLogCommand = fixture.Create<UpdateDashboardViewLogCommand>();
        GetDashboardViewLogPaginatedListQuery = fixture.Create<GetDashboardViewLogPaginatedListQuery>();
        DataLagStatusReport = fixture.Create<DataLagStatusReport>();
        InfraObjectHealthScoreList = fixture.Create<List<InfraObjectHealthScore>>();
    }

    public void Dispose()
    {

    }
}
