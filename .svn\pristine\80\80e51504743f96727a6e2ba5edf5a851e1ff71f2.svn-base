using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class TestBusinessServiceSpecification : Specification<BusinessService>
{
    public TestBusinessServiceSpecification(string searchTerm = null)
    {
        if (string.IsNullOrEmpty(searchTerm))
        {
            Criteria = x => x.IsActive;
        }
        else
        {
            Criteria = x => x.IsActive && x.Name.Contains(searchTerm);
        }
    }
}

public class BusinessServiceRepositoryTests : IClassFixture<BusinessServiceFixture>
{
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessServiceRepository _repository;
    private readonly BusinessServiceRepository _repositoryNotParent;

    public BusinessServiceRepositoryTests(BusinessServiceFixture businessServiceFixture)
    {
        _businessServiceFixture = businessServiceFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessServiceRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new BusinessServiceRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;

        // Act
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.Name, result.Name);
        Assert.Equal(businessService.Description, result.Description);
        Assert.Single(_dbContext.BusinessServices);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        businessService.Name = "UpdatedName";
        businessService.Description = "UpdatedDescription";
        businessService.Priority = 1;

        // Act
        _dbContext.BusinessServices.Update(businessService);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal(1, result.Priority);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        // Act
        businessService.IsActive = false;

        _dbContext.BusinessServices.Update(businessService);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(businessService.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.ReferenceId, result.ReferenceId);
        Assert.Equal(businessService.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServices.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsBusinessServiceNameExist Tests

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "ExistingName";
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessServiceNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.IsBusinessServiceNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "SameName";
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessServiceNameExist("SameName", businessService.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsBusinessServiceNameUnique Tests

    [Fact]
    public async Task IsBusinessServiceNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.Name = "UniqueName";
        await _dbContext.BusinessServices.AddAsync(businessService);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessServiceNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessServiceNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.IsBusinessServiceNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceList;
        var businessService1 = businessService[0];
        var businessService2 = businessService[1];


        // Act
        await _dbContext.BusinessServices.AddAsync(businessService1);
        await _dbContext.BusinessServices.AddAsync(businessService2);
        await _dbContext.SaveChangesAsync();
        var results = await _repository.ListAllAsync();
        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BusinessServices.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(businessServices);
        var initialCount = businessServices.Count;

        var toUpdate = businessServices.Take(2).ToList();
        toUpdate.ForEach(x => x.Priority = 1);
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = businessServices.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Priority == 1).ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.IsBusinessServiceNameExist(null, "valid-guid");
        var result2 = await _repository.IsBusinessServiceNameExist("TestName", null);
        var result3 = await _repository.IsBusinessServiceNameUnique(null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandlePriorityFiltering()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;

        businessServices[0].Priority = 1;
        businessServices[1].Priority = 2;
        businessServices[2].Priority = 1;
       
        _dbContext.BusinessServices.AddRange(businessServices);

        _dbContext.SaveChanges();

        // Act
        var criticalServices = await _repository.FindByFilterAsync(x => x.Priority == 1);
        var highServices = await _repository.FindByFilterAsync(x => x.Priority == 2);

        // Assert
        Assert.Equal(2, criticalServices.Count);
        Assert.Single(highServices);
        Assert.All(criticalServices, x => Assert.Equal(1, x.Priority));
        Assert.All(highServices, x => Assert.Equal(2, x.Priority));
    }

    #endregion

    #region GetByReferenceIdsAsync Tests

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnMatchingServices_WhenIsParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        var ids = businessServices.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Contains(x.ReferenceId, ids));
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnFilteredServices_WhenIsNotParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        businessServices[0].ReferenceId = "c9b3cd51-f688-4667-be33-47f82b7086fa";
        businessServices[1].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        businessServices.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _repositoryNotParent.AddRangeAsync(businessServices);

        var ids = businessServices.Take(2).Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);

    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        var nonExistentIds = new List<string> { "NON_EXISTENT_1", "NON_EXISTENT_2" };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetFilterByReferenceIdAsync Tests

    [Fact]
    public async Task GetFilterByReferenceIdAsync_ShouldReturnService_WhenIsParent()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        await _repository.AddAsync(businessService);

        // Act
        var result = await _repository.GetFilterByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.ReferenceId, result.ReferenceId);
    }

    [Fact]
    public async Task GetFilterByReferenceIdAsync_ShouldReturnService_WhenIsNotParentAndSameCompany()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.CompanyId = "ChHILD_COMPANY_123";
        await _repositoryNotParent.AddAsync(businessService);

        // Act
        var result = await _repositoryNotParent.GetFilterByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessService.ReferenceId, result.ReferenceId);
       
    }

    [Fact]
    public async Task GetFilterByReferenceIdAsync_ShouldReturnNull_WhenIsNotParentAndDifferentCompany()
    {
        // Arrange
        var businessService = _businessServiceFixture.BusinessServiceDto;
        businessService.CompanyId = "OTHER_COMPANY_456";
        await _repositoryNotParent.AddAsync(businessService);

        // Act
        var result = await _repositoryNotParent.GetFilterByReferenceIdAsync(businessService.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetBusinessServiceNames Tests

    [Fact]
    public async Task GetBusinessServiceNames_ShouldReturnNamesOnly_WhenIsParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBusinessServiceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessServices.Count, result.Count);
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
        Assert.All(result, x => Assert.NotNull(x.Name));
        // Should only have ReferenceId and Name populated
    }

    [Fact]
    public async Task GetBusinessServiceNames_ShouldReturnFilteredNames_WhenIsNotParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        businessServices[0].ReferenceId = "c9b3cd51-f688-4667-be33-47f82b7086fa";
        businessServices[1].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        businessServices[2].ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fb";
        businessServices.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _repositoryNotParent.AddRangeAsync(businessServices);

        // Act
        var result = await _repositoryNotParent.GetBusinessServiceNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
        Assert.All(result, x => Assert.NotNull(x.Name));
    }

    #endregion

    #region GetBySiteIds Tests

    [Fact]
    public async Task GetBySiteIds_ShouldReturnMatchingServices()
    {
        // Arrange

        var siteIds = new List<string> { "a2ed9482-799d-484e-8e2a-7467aa16c571", "a2ed9482-799d-484e-8e2a-7467aa16c581" };

        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Service1",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c571\",\"Name\":\"PR_Site\",\"Location\":\"Vedasandur\",\"Lng\":\"77.9476\",\"Lat\":\"10.5311\",\"category\":\"Primary\",\"TypeId\":\"1738f813-6090-40cc-8869-25741a156f73\",\"type\":\"PR Site\"},\"DR Site\":{\"Id\":\"c97b8580-957b-45a4-b4d5-10492ce7c031\",\"Name\":\"DR_Site\",\"Location\":\"Visakhapatnam\",\"Lng\":\"83.2185\",\"Lat\":\"17.6868\",\"category\":\"DR\",\"TypeId\":\"2914c9b2-91d3-4e03-baed-77824f16327c\",\"type\":\"DR Site\"},\"test\":{\"Id\":\"c9283e47-bb44-4430-a3dd-3957278c42f3\",\"Name\":\"fghfhg\",\"Location\":\"Delhi\",\"Lng\":\"77.23\",\"Lat\":\" 28.61\",\"category\":\"Custom DR\",\"TypeId\":\"64df3f78-c7b7-457b-a065-9ef858a0918f\",\"type\":\"test\"},\"TEST1\":{\"Id\":\"a99991ec-6c8b-4663-a65c-6d228832d597\",\"Name\":\"cdvc\",\"Location\":\"Tongren\",\"Lng\":\"109.1885\",\"Lat\":\"27.7233\",\"category\":\"Custom DR\",\"TypeId\":\"b4ab8fca-9c19-40f3-beda-8b168a4b9786\",\"type\":\"TEST1\"},\"TEST2\":{\"Id\":\"1475095d-323d-4134-ab0c-f6f9a779fad2\",\"Name\":\"ser\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"e6da4a0b-ed07-4148-96c4-89f55b619b08\",\"type\":\"TEST2\"},\"TEST3\":{\"Id\":\"b2c7d27c-aef6-4d77-a04c-54bbd91b1753\",\"Name\":\"ytst\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"Custom DR\",\"TypeId\":\"060b8425-002a-4079-99e8-949cf1604c41\",\"type\":\"TEST3\"},\"TEST4\":{\"Id\":\"873c50ad-6f6a-42d3-85cf-9bd4976c9cbb\",\"Name\":\"demos\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"fc316c4b-201a-444d-ac2d-367e062a96c3\",\"type\":\"TEST4\"},\"TEST5\":{\"Id\":\"020bc76a-a910-447a-b0a4-ab7a2d7d6103\",\"Name\":\"dgdfg\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"6a598e92-55cf-42a2-82d4-736c9fcb2067\",\"type\":\"TEST5\"}}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BusinessService
            {
                Name = "Service2",
                SiteProperties = "{\"PR Site\":{\"Id\":\"d319a4c1-ebf0-43ae-92f8-3c04fa60b06b\",\"Name\":\"test\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"Primary\",\"TypeId\":\"1738f813-6090-40cc-8869-25741a156f73\",\"type\":\"PR Site\"},\"DR Site\":{\"Id\":\"3168814f-6451-4289-9ac1-1ccfca46335a\",\"Name\":\"dfgth\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"DR\",\"TypeId\":\"2914c9b2-91d3-4e03-baed-77824f16327c\",\"type\":\"DR Site\"}}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BusinessService
            {
                Name = "Service3",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c581\",\"Name\":\"PR_Site\",\"Location\":\"Vedasandur\",\"Lng\":\"77.9476\",\"Lat\":\"10.5311\",\"category\":\"Primary\",\"TypeId\":\"1738f813-6090-40cc-8869-25741a156f73\",\"type\":\"PR Site\"},\"DR Site\":{\"Id\":\"c97b8580-957b-45a4-b4d5-10492ce7c031\",\"Name\":\"DR_Site\",\"Location\":\"Visakhapatnam\",\"Lng\":\"83.2185\",\"Lat\":\"17.6868\",\"category\":\"DR\",\"TypeId\":\"2914c9b2-91d3-4e03-baed-77824f16327c\",\"type\":\"DR Site\"},\"test\":{\"Id\":\"c9283e47-bb44-4430-a3dd-3957278c42f3\",\"Name\":\"fghfhg\",\"Location\":\"Delhi\",\"Lng\":\"77.23\",\"Lat\":\" 28.61\",\"category\":\"Custom DR\",\"TypeId\":\"64df3f78-c7b7-457b-a065-9ef858a0918f\",\"type\":\"test\"},\"TEST1\":{\"Id\":\"a99991ec-6c8b-4663-a65c-6d228832d597\",\"Name\":\"cdvc\",\"Location\":\"Tongren\",\"Lng\":\"109.1885\",\"Lat\":\"27.7233\",\"category\":\"Custom DR\",\"TypeId\":\"b4ab8fca-9c19-40f3-beda-8b168a4b9786\",\"type\":\"TEST1\"},\"TEST2\":{\"Id\":\"1475095d-323d-4134-ab0c-f6f9a779fad2\",\"Name\":\"ser\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"e6da4a0b-ed07-4148-96c4-89f55b619b08\",\"type\":\"TEST2\"},\"TEST3\":{\"Id\":\"b2c7d27c-aef6-4d77-a04c-54bbd91b1753\",\"Name\":\"ytst\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"Custom DR\",\"TypeId\":\"060b8425-002a-4079-99e8-949cf1604c41\",\"type\":\"TEST3\"},\"TEST4\":{\"Id\":\"873c50ad-6f6a-42d3-85cf-9bd4976c9cbb\",\"Name\":\"demos\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"fc316c4b-201a-444d-ac2d-367e062a96c3\",\"type\":\"TEST4\"},\"TEST5\":{\"Id\":\"020bc76a-a910-447a-b0a4-ab7a2d7d6103\",\"Name\":\"dgdfg\",\"Location\":\"Tokyo\",\"Lng\":\"139.6922\",\"Lat\":\"35.6897\",\"category\":\"Custom DR\",\"TypeId\":\"6a598e92-55cf-42a2-82d4-736c9fcb2067\",\"type\":\"TEST5\"}}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBySiteIds(siteIds);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.Name == "Service1");
        Assert.Contains(result, x => x.Name == "Service2");
        Assert.DoesNotContain(result, x => x.Name == "Service3");
    }

    [Fact]
    public async Task GetBySiteIds_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        businessServices.ForEach(x => x.SiteProperties = "{\"PR Site\":{\"Id\":\"d319a4c1-ebf0-43ae-92f8-3c04fa60b06b\",\"Name\":\"test\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"Primary\",\"TypeId\":\"1738f813-6090-40cc-8869-25741a156f73\",\"type\":\"PR Site\"},\"DR Site\":{\"Id\":\"3168814f-6451-4289-9ac1-1ccfca46335a\",\"Name\":\"dfgth\",\"Location\":\"Jakarta\",\"Lng\":\"106.8275\",\"Lat\":\"-6.175\",\"category\":\"DR\",\"TypeId\":\"2914c9b2-91d3-4e03-baed-77824f16327c\",\"type\":\"DR Site\"}}");
        await _repository.AddRangeAsync(businessServices);

        var nonExistentSiteIds = new List<string> { "NON_EXISTENT_SITE_1", "NON_EXISTENT_SITE_2" };

        // Act
        var result = await _repository.GetBySiteIds(nonExistentSiteIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetBusinessServicesBySiteId Tests

    [Fact]
    public async Task GetBusinessServicesBySiteId_ShouldReturnMatchingServices()
    {
        // Arrange
        var siteId = "SITE_001";
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Service1",
                SiteProperties = $"SITE_000,{siteId},SITE_002",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BusinessService
            {
                Name = "Service2",
                SiteProperties = $"{siteId}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BusinessService
            {
                Name = "Service3",
                SiteProperties = "SITE_002,SITE_003",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBusinessServicesBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.Contains(result, x => x.Name == "Service1");
        Assert.Contains(result, x => x.Name == "Service2");
        Assert.DoesNotContain(result, x => x.Name == "Service3");
    }

    [Fact]
    public async Task GetBusinessServicesBySiteId_ShouldReturnEmpty_WhenNoMatches()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBusinessServicesBySiteId("NON_EXISTENT_SITE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetBusinessServicesBySiteId_ShouldFilterByCompany_WhenIsNotParent()
    {
        // Arrange
        var siteId = "SITE_001";
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Service1",
                SiteProperties = siteId,
                CompanyId = "ChHILD_COMPANY_123", // Should be included
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                IsActive = true
            },
            new BusinessService
            {
                Name = "Service2",
                SiteProperties = siteId,
                CompanyId = "OTHER_COMPANY_456", // Should be excluded
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repositoryNotParent.AddRangeAsync(businessServices);

        // Act
        var result = await _repositoryNotParent.GetBusinessServicesBySiteId(siteId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Service1", result.First().Name);

    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServicePaginationList;
        await _repository.AddRangeAsync(businessServices);

        var specification = new TestBusinessServiceSpecification();

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnFilteredResults_WhenIsNotParent()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServicePaginationList;
        businessServices.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");

        await _repositoryNotParent.AddRangeAsync(businessServices);

        var specification = new TestBusinessServiceSpecification();

        // Act
        var result = await _repositoryNotParent.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldApplySpecificationFilter()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServicePaginationList;
        businessServices[0].Name = "FilteredService";
        businessServices[1].Name = "AnotherFilteredService";
        businessServices[2].Name = "DifferentService";

        await _repository.AddRangeAsync(businessServices);

        var specification = new TestBusinessServiceSpecification("Filtered");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.All(result.Data, x => Assert.Contains("Filtered", x.Name));
    }

    #endregion

    #region Parent/Child Company Logic Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllServices_WhenIsParent()
    {
        // Arrange
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Parent Service 1",
                CompanyId = "PARENT_COMPANY",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new BusinessService
            {
                Name = "Child Service 1",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Parent can see all services
        Assert.Contains(result, x => x.Name == "Parent Service 1");
        Assert.Contains(result, x => x.Name == "Child Service 1");
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnFilteredServices_WhenIsNotParent()
    {
        // Arrange
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
          
                Name = "Parent Service 1",
                CompanyId = "PARENT_COMPANY",
                ReferenceId ="c9b3cd51-f688-4667-be33-46f82b7086f4",
                IsActive = true
            },
            new BusinessService
            {
                Name = "Child Service 1",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "c9b3cd51-f688-4667-be33-47f82b7086fa",
                IsActive = true
            },
            new BusinessService
            {
                Name = "Child Service 2",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                IsActive = true
            }
        };

        await _repositoryNotParent.AddRangeAsync(businessServices);

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); // Child can only see their company services
        Assert.All(result, x => Assert.Contains("Child Service", x.Name));
        Assert.DoesNotContain(result, x => x.Name == "Parent Service 1");
    }

    #endregion

    #region Uncovered Methods Tests - 100% Coverage

    [Fact]
    public async Task GetBySiteIds_ShouldReturnEmpty_WhenSiteIdsIsEmpty()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBySiteIds(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
    [Fact]
    public async Task GetBySiteIds_ShouldReturnAssigned_WhenUserIsNotAllInfra()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;

        // Make sure each service has a site ID in SiteProperties and correct company
        businessServices[0].SiteProperties = "SITE1";
        businessServices[1].SiteProperties = "SITE2";
        businessServices[2].SiteProperties = "SITE3";
        businessServices.ForEach(x => x.CompanyId = "COMPANY_123");

        await _repository.AddRangeAsync(businessServices);

        var siteIds = new List<string> { "SITE1", "SITE2", "SITE3" };

        // Act
        var result = await _repository.GetBySiteIds(siteIds);

        // Assert
        Assert.NotNull(result); // Covers "return result;"
        Assert.Equal(2, result.Count); // Covers filtering
        Assert.Contains(result, r => r.SiteProperties.Contains("SITE1"));
        Assert.Contains(result, r => r.SiteProperties.Contains("SITE2"));
    }


    [Fact]
    public async Task GetBySiteIds_ShouldFilterByCompany_WhenIsNotParent()
    {
        // Arrange
        var siteIds = new List<string> { "a2ed9482-799d-484e-8e2a-7467aa16c571" };

        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Child Company Service",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c571\",\"Name\":\"PR_Site\"}}",
                CompanyId = "ChHILD_COMPANY_123", // Should be included
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                IsActive = true
            },
            new BusinessService
            {
                Name = "Other Company Service",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c571\",\"Name\":\"PR_Site\"}}",
                CompanyId = "OTHER_COMPANY_456", // Should be excluded
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repositoryNotParent.AddRangeAsync(businessServices);

        // Act
        var result = await _repositoryNotParent.GetBySiteIds(siteIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Child Company Service", result.First().Name);
        Assert.Equal("ChHILD_COMPANY_123", result.First().CompanyId);
    }

    [Fact]
    public async Task GetBySiteIds_ShouldReturnAssignedBusinessServices_WhenIsAllInfraFalse()
    {
        // Arrange
        await ClearDatabase();
        var siteIds = new List<string> { "a2ed9482-799d-484e-8e2a-7467aa16c571" };

        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Assigned Service",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c571\",\"Name\":\"PR_Site\"}}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa", // This is in assigned business services
                IsActive = true
            },
            new BusinessService
            {
                Name = "Unassigned Service",
                SiteProperties = "{\"PR Site\":{\"Id\":\"a2ed9482-799d-484e-8e2a-7467aa16c571\",\"Name\":\"PR_Site\"}}",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "UNASSIGNED_BS_ID", // Not in assigned business services
                IsActive = true
            }
        };

        await _dbContext.BusinessServices.AddRangeAsync(businessServices);
        await _dbContext.SaveChangesAsync();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new BusinessServiceRepository(_dbContext, mockUserService.Object);

        // Act
        var result = await repositoryNotAllInfra.GetBySiteIds(siteIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only assigned business service should be returned
        Assert.Equal("Assigned Service", result.First().Name);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery_WhenIsAllInfraTrue()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        _repository.AddRangeAsync(businessServices).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.All(resultList, x => Assert.True(x.IsActive));

        // Verify ordering (should be OrderByDescending(x => x.Id))
        if (resultList.Count > 1)
        {
            for (int i = 0; i < resultList.Count - 1; i++)
            {
                Assert.True(resultList[i].Id >= resultList[i + 1].Id);
            }
        }
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnAssignedBusinessServices_WhenIsAllInfraFalse()
    {
        // Arrange
        ClearDatabase().Wait();
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Assigned Service",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa", // This is in assigned business services
                IsActive = true
            },
            new BusinessService
            {
                Name = "Unassigned Service",
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = "UNASSIGNED_BS_ID", // Not in assigned business services
                IsActive = true
            }
        };

        _dbContext.BusinessServices.AddRange(businessServices);
        _dbContext.SaveChanges();

        // Create repository with IsAllInfra = false
        var mockUserService = new Mock<ILoggedInUserService>();
        var assignedInfra = System.Text.Json.JsonSerializer.Serialize(DbContextFactory.GetAssignedEntityForIsAllinfraFalse());
        mockUserService.Setup(x => x.CompanyId).Returns("ChHILD_COMPANY_123");
        mockUserService.Setup(x => x.UserId).Returns("USER_456");
        mockUserService.Setup(x => x.IsParent).Returns(false);
        mockUserService.Setup(x => x.IsAllInfra).Returns(false);
        mockUserService.Setup(x => x.IsAuthenticated).Returns(true);
        mockUserService.Setup(x => x.AssignedInfras).Returns(assignedInfra);

        var repositoryNotAllInfra = new BusinessServiceRepository(_dbContext, mockUserService.Object);

        // Act
        var result = repositoryNotAllInfra.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList); // Only assigned business service should be returned
        Assert.Equal("Assigned Service", resultList.First().Name);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnNoTrackingResults()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        _repository.AddRangeAsync(businessServices).Wait();

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);

        // Verify that the query is set to AsNoTracking
        var resultList = result.ToList();
        Assert.NotEmpty(resultList);

        // Modify one of the entities and verify it's not tracked
        var firstEntity = resultList.First();
        var originalName = firstEntity.Name;
        firstEntity.Name = "Modified Name";

        // Get the same entity again - should not reflect the change since it's not tracked
        var resultAgain = _repository.GetPaginatedQuery().ToList();
        var sameEntity = resultAgain.First(x => x.ReferenceId == firstEntity.ReferenceId);
        Assert.Equal(originalName, sameEntity.Name); // Should still have original name
    }

    [Fact]
    public void GetPaginatedQuery_ShouldFilterByCompanyId()
    {
        // Arrange
        var businessServices = new List<BusinessService>
        {
            new BusinessService
            {
                Name = "Child Company Service",
                CompanyId = "ChHILD_COMPANY_123", // Should be included
                ReferenceId = "c9b3cd51-f688-4667-be33-46f82b7086fa",
                IsActive = true
            },
            new BusinessService
            {
                Name = "Other Company Service",
                CompanyId = "OTHER_COMPANY_456", // Should be excluded
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        _repositoryNotParent.AddRangeAsync(businessServices).Wait();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList); // Only child company service should be returned
        Assert.Equal("Child Company Service", resultList.First().Name);
        Assert.Equal("ChHILD_COMPANY_123", resultList.First().CompanyId);
    }

    [Fact]
    public void GetPaginatedQuery_ShouldHandleEmptyResults()
    {
        // Arrange - No business services added

        // Act
        var result = _repository.GetPaginatedQuery();

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Empty(resultList);
    }
    [Fact]
    public async Task GetBySiteIds_ShouldReturnBusinessServices_WhenValidSiteIds()
    {
        // Arrange
        var testSiteIds = new List<string> { "SITE123" };

        var mockUserService = new Mock<ILoggedInUserService>();
        mockUserService.Setup(x => x.IsParent).Returns(false);         
        mockUserService.Setup(x => x.IsAllInfra).Returns(true);        
        mockUserService.Setup(x => x.CompanyId).Returns("COMP123");

        var testData = new List<BusinessService>
    {
        new BusinessService { CompanyId = "COMP123", SiteProperties = "SITE123,SITE456" },
        new BusinessService { CompanyId = "COMP123", SiteProperties = "SITE123" }
    }.AsQueryable();


        var service = new BusinessServiceRepository(_dbContext, mockUserService.Object);

        // Act
        var result = await service.GetBySiteIds(testSiteIds);

        // Assert
        Assert.NotNull(result);                     // Ensures the method reaches return result;
        Assert.NotEmpty(result);                   // Makes sure actual query ran
        Assert.All(result, r => Assert.Contains("SITE123", r.SiteProperties));
    }


    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldHandleEmptyIdsList()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetByReferenceIdsAsync(new List<string>());

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }


    [Fact]
    public async Task GetBySiteIds_ShouldHandleNullSiteIds()
    {
        // Arrange
        var businessServices = _businessServiceFixture.BusinessServiceList;
        await _repository.AddRangeAsync(businessServices);

        // Act
        var result = await _repository.GetBySiteIds(null);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }


    #endregion

    private async Task ClearDatabase()
    {
        _dbContext.BusinessServices.RemoveRange(_dbContext.BusinessServices);
        await _dbContext.SaveChangesAsync();
    }
}
