using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using System.Linq.Expressions;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

// Simple concrete specification for testing
public class CyberSnapsRepositoryTests : IClassFixture<CyberSnapsFixture>
{
    private readonly CyberSnapsFixture _cyberSnapsFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly CyberSnapsRepository _repository;
    private readonly CyberSnapsRepository _repositoryNotParent;

    public CyberSnapsRepositoryTests(CyberSnapsFixture cyberSnapsFixture)
    {
        _cyberSnapsFixture = cyberSnapsFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new CyberSnapsRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new CyberSnapsRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region IsNameExist Tests

    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var cyberSnap = _cyberSnapsFixture.CyberSnapsDto;
        cyberSnap.Name = "ExistingSnapName";
        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = await _repository.IsNameExist("ExistingSnapName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var snaps = _cyberSnapsFixture.CyberSnapsList;
        await _repository.AddRangeAsync(snaps);

        // Act
        var result = await _repository.IsNameExist("NonExistentSnapName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var cyberSnap = _cyberSnapsFixture.CyberSnapsDto;
        cyberSnap.Name = "SameSnapName";
        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        await _dbContext.SaveChangesAsync();
        // Act
        var result = !await _repository.IsNameExist("SameSnapName", cyberSnap.ReferenceId);

        // Assert
        Assert.False(result);
    }
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public async Task IsNameExist_ShouldThrowArgumentException_WhenNameIsNullOrWhiteSpace(string invalidName)
    {
        // Act & Assert
        var ex = await Assert.ThrowsAsync<ArgumentException>(() => _repository.IsNameExist(invalidName, Guid.NewGuid().ToString()));
        Assert.Equal("Name must be provided (Parameter 'name')", ex.Message);
    }
    [Fact]
    public async Task IsNameExist_ShouldReturnTrue_WhenNameExistsForAnotherEntityWithValidId()
    {
        // Arrange
        var snap1 = _cyberSnapsFixture.CyberSnapsDto;
        var snap2 = _cyberSnapsFixture.CyberSnapsDto;
        snap2.Name = "DuplicateName";
        snap2.ReferenceId = Guid.NewGuid().ToString();

        await _repository.AddRangeAsync(new List<CyberSnaps> { snap1, snap2 });

        // Act
        var result = await _repository.IsNameExist("DuplicateName", snap1.ReferenceId);

        // Assert
        Assert.True(result);
    }

    #endregion

    #region GetCyberSnapsBySnapTagName Tests

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnSnapsForSpecificTag()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");
        var tagName = "SPECIFIC_TAG";

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps[0].TimeStamp = $"Mon {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[0].Tag = tagName;
        snaps[1].Tag = tagName;
        snaps[0].IsActive = true;
        snaps[1].IsActive = true;
        snaps[1].TimeStamp = $"Tue {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[1].CreatedDate = baseDate.AddDays(-1);
        snaps[2].TimeStamp = $"Wed {DateTime.Now.ToString("MMM dd HH:mm:ss yyyy")}";
        snaps[2].CreatedDate = baseDate.AddDays(-2);

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName(tagName, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(tagName, x.Tag));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnAllSnapsWhenTagIsAll()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps.ForEach(x => x.IsActive = true);
        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[1].CreatedDate = baseDate.AddDays(-1);
        snaps[2].CreatedDate = baseDate.AddDays(-10);

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName("all", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    #endregion

    #region GetCyberSnapsListByDate Tests

    [Fact]
    public async Task GetCyberSnapsListByDate_ShouldReturnSnapsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-7).ToString("yyyy-MM-dd");
        var endDate = baseDate.ToString("yyyy-MM-dd");

        var snaps = _cyberSnapsFixture.CyberSnapsList;

        snaps[0].CreatedDate = baseDate.AddDays(-3);
        snaps[1].CreatedDate = baseDate.AddDays(-15);
        snaps[2].CreatedDate = baseDate.AddDays(-15);
        snaps[0].IsActive = true;
        snaps[1].IsActive = true;

        _dbContext.CyberSnaps.AddRange(snaps);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetCyberSnapsListByDate(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Only one snap within date range
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= DateTime.Parse(startDate)));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= DateTime.Parse(endDate)));
    }

    #endregion

    #region GetCyberSnapsBySnapTagName Tests

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnMatchingSnaps_WhenTagExists()
    {
        // Arrange
        var tagName = "TestTag";
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var cyberSnaps = new List<CyberSnaps>
        {
            new CyberSnaps
            {
                Name = "Snap1",
                Tag = tagName,
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberSnaps
            {
                Name = "Snap2",
                Tag = tagName,
                CreatedDate = baseDate.AddDays(1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberSnaps
            {
                Name = "Snap3",
                Tag = "DifferentTag",
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberSnaps);

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName(tagName, startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(tagName, x.Tag));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date >= baseDate.AddDays(-5).Date));
        Assert.All(result, x => Assert.True(x.CreatedDate.Date <= baseDate.AddDays(5).Date));
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnAllSnaps_WhenTagIsAll()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var cyberSnaps = new List<CyberSnaps>
        {
            new CyberSnaps
            {
                Name = "Snap1",
                Tag = "Tag1",
                CreatedDate = baseDate,
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberSnaps
            {
                Name = "Snap2",
                Tag = "Tag2",
                CreatedDate = baseDate.AddDays(1),
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberSnaps);

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName("All", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldReturnEmpty_WhenTagNotExists()
    {
        // Arrange
        var cyberSnaps = _cyberSnapsFixture.CyberSnapsList;
        await _repository.AddRangeAsync(cyberSnaps);

        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        // Act
        var result = await _repository.GetCyberSnapsBySnapTagName("NonExistentTag", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldThrowArgumentException_WhenDatesAreInvalid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<FormatException>(() =>
            _repository.GetCyberSnapsBySnapTagName("TestTag", "invalid-date", "2023-12-31"));

        await Assert.ThrowsAsync<FormatException>(() =>
            _repository.GetCyberSnapsBySnapTagName("TestTag", "2023-01-01", "invalid-date"));
    }

    [Fact]
    public async Task GetCyberSnapsBySnapTagName_ShouldThrowArgumentNullException_WhenDatesAreNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() =>
            _repository.GetCyberSnapsBySnapTagName("TestTag", null, "2023-12-31"));

        await Assert.ThrowsAsync<ArgumentException>(() =>
            _repository.GetCyberSnapsBySnapTagName("TestTag", "2023-01-01", null));
    }

    #endregion

    #region GetCyberSnapsByStorageGroupNameAndLinkedStatus Tests

    [Fact]
    public async Task GetCyberSnapsByStorageGroupNameAndLinkedStatus_ShouldReturnMatchingSnaps()
    {
        // Arrange
        var cyberSnaps = new List<CyberSnaps>
        {
            new CyberSnaps
            {
                Name = "Snap1",
                StorageGroupName = "StorageGroup1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberSnaps
            {
                Name = "Snap2",
                StorageGroupName = "StorageGroup1",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            },
            new CyberSnaps
            {
                Name = "Snap3",
                StorageGroupName = "StorageGroup2",
                ReferenceId = Guid.NewGuid().ToString(),
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(cyberSnaps);

        // Act
        Expression<Func<CyberSnaps, bool>> expression = x => x.Name == "Snap1";
        var result = await _repository.GetCyberSnapsByStorageGroupNameAndLinkedStatus(expression);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Snap1", result.First().Name);
        Assert.Equal("StorageGroup1", result.First().StorageGroupName);

    }

    [Fact]
    public async Task GetCyberSnapsByStorageGroupNameAndLinkedStatus_ShouldThrowArgumentNullException_WhenExpressionIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _repository.GetCyberSnapsByStorageGroupNameAndLinkedStatus(null));
    }

    #endregion

    #region GetCyberSnapsByDateTime Tests

    [Fact]
    public async Task GetCyberSnapsByDateTime_ShouldReturnPaginatedResults()
    {
        // Arrange
        var cyberSnaps = _cyberSnapsFixture.CyberSnapsPaginationList;
        await _repository.AddRangeAsync(cyberSnaps);

        var specification = new CyberSnapsFilterSpecification("");
        var startDate = DateTime.Now.AddDays(-30);
        var endDate = DateTime.Now.AddDays(30);

        // Act
        var result = await _repository.GetCyberSnapsByDateTime(1, 10, specification, "Name", "asc", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.True(result.Data.Count <= 10);
        Assert.Equal(1, result.CurrentPage);
    }

    [Fact]
    public async Task GetCyberSnapsByDateTime_ShouldThrowArgumentNullException_WhenSpecificationIsNull()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-30);
        var endDate = DateTime.Now.AddDays(30);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _repository.GetCyberSnapsByDateTime(1, 10, null, "Name", "asc", startDate, endDate));
    }

    #endregion

    #region Edge Cases and Error Handling Tests

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var cyberSnaps = _cyberSnapsFixture.CyberSnapsList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(cyberSnaps);
        var initialCount = cyberSnaps.Count;

        var toUpdate = cyberSnaps.Take(2).ToList();
        toUpdate.ForEach(x => x.Name = "UpdatedSnapName");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = cyberSnaps.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Name == "UpdatedSnapName").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion

    [Fact]
    public async Task GetCyberSnapsByDateTime_ShouldMapToCyberSnapsListVmCorrectly()
    {
        // Arrange
        var cyberSnap = new CyberSnaps
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "SnapA",
            Gen = "s",
            TimeStamp = new DateTime(2024, 01, 01).ToString("yyyy-MM-dd HH:mm:ss"),
            StorageGroupName = "SG-A",
            LinkedStatus = "Linked",
            LinkedSGTime = new DateTime(2024, 01, 01, 12, 0, 0).ToString("yyyy-MM-dd HH:mm:ss"),
            LinkSG = "LSG-1"
        };

        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        _dbContext.SaveChanges();

        var spec = new CyberSnapsFilterSpecification("");

        var pageNumber = 1;
        var pageSize = 10;
        var startDate = new DateTime(2023, 12, 31);
        var endDate = new DateTime(2024, 01, 02);

        // Act
        var result = await _repository.GetCyberSnapsByDateTime(
            pageNumber, pageSize, spec, "Name", "asc", startDate, endDate);

        // Assert
        var item = Assert.Single(result.Data); // only 1 snap expected
        Assert.Equal(cyberSnap.ReferenceId, item.Id);
        Assert.Equal(cyberSnap.Name, item.Name);
        Assert.Equal(cyberSnap.Gen, item.Gen);
        Assert.Equal(cyberSnap.TimeStamp, item.TimeStamp);
        Assert.Equal(cyberSnap.StorageGroupName, item.StorageGroupName);
        Assert.Equal(cyberSnap.LinkedStatus, item.LinkedStatus);
        Assert.Equal(cyberSnap.LinkedSGTime, item.LinkedSGTime);
        Assert.Equal(cyberSnap.LinkSG, item.LinkSG);
    }
    [Fact]
    public async Task GetCyberSnapsByDateTime_ShouldMapToCyberSnapsInrageDateTime()
    {
        // Arrange
        var timestamp = "PR 2024-01-01 12:00:00";
        var startDate = new DateTime(2023, 12, 31);
        var endDate = new DateTime(2024, 01, 01);
        var cyberSnap = new CyberSnaps
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "SnapA",
            Gen = "s",
            TimeStamp = timestamp,
            StorageGroupName = "SG-A",
            LinkedStatus = "Linked",
            LinkedSGTime = new DateTime(2024, 01, 01, 12, 0, 0).ToString("yyyy-MM-dd HH:mm:ss"),
            LinkSG = "LSG-1"
        };

        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        _dbContext.SaveChanges();

        var spec = new CyberSnapsFilterSpecification("");

        var pageNumber = 1;
        var pageSize = 10;
     
        // Act
        var result = await _repository.GetCyberSnapsByDateTime(
            pageNumber, pageSize, spec, "Name", "asc", startDate, endDate);

        // Assert
        var item = Assert.Single(result.Data); // only 1 snap expected
        Assert.Equal(cyberSnap.ReferenceId, item.Id);
        Assert.Equal(cyberSnap.Name, item.Name);
        Assert.Equal(cyberSnap.Gen, item.Gen);
        Assert.Equal(cyberSnap.TimeStamp, item.TimeStamp);
        Assert.Equal(cyberSnap.StorageGroupName, item.StorageGroupName);
        Assert.Equal(cyberSnap.LinkedStatus, item.LinkedStatus);
        Assert.Equal(cyberSnap.LinkedSGTime, item.LinkedSGTime);
        Assert.Equal(cyberSnap.LinkSG, item.LinkSG);
    }
    [Fact]
    public async Task GetCyberSnapsByDateTime_ShouldReturnEmpty()
    {
        // Arrange
        var cyberSnap = new CyberSnaps
        {
            ReferenceId = Guid.NewGuid().ToString(),
            Name = "SnapA",
            Gen = "s",
            TimeStamp = "45:5454ada",
            StorageGroupName = "SG-A",
            LinkedStatus = "Linked",
            LinkedSGTime = "asdsad455",
            LinkSG = "LSG-1"
        };

        await _dbContext.CyberSnaps.AddAsync(cyberSnap);
        _dbContext.SaveChanges();

        var spec = new CyberSnapsFilterSpecification("");

        var pageNumber = 1;
        var pageSize = 10;
        var startDate = new DateTime(2023, 12, 31);
        var endDate = new DateTime(2024, 01, 02);

        // Act
        var result = await _repository.GetCyberSnapsByDateTime(
            pageNumber, pageSize, spec, "Name", "asc", startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.Data);
    }
    [Fact]
    public async Task GetGetCyberSnapsBySnapTagNameShouldThrowException()
    {
        // Arrange
        await Assert.ThrowsAsync<ArgumentException>(async () => await _repository.GetCyberSnapsBySnapTagName(null, "2023-01-01", "2023-12-31"));

    }
}

    
