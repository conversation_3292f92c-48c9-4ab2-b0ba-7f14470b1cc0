﻿using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class GlobalSettingsFixture
{
    public List<GlobalSettingListVm> GlobalSettingListVms { get; }
    public List<Application.Features.GlobalSetting.Queries.GetNames.GlobalSettingNameVm> GlobalSettingNameVms { get; }
    public GlobalSettingDetailVm GlobalSettingDetailVm { get; }
    public CreateGlobalSettingCommand CreateGlobalSettingCommand { get; }
    public UpdateGlobalSettingCommand UpdateGlobalSettingCommand { get; }
    public AuthenticationCommand AuthenticationCommand { get; }
    public PaginatedResult<GlobalSettingListVm> PaginatedGlobalSettingListVm { get; }
    public GlobalSettingsFixture()
    {
        var fixture = new Fixture();
        GlobalSettingListVms = fixture.Create<List<GlobalSettingListVm>>();
        GlobalSettingNameVms = fixture.Create<List<Application.Features.GlobalSetting.Queries.GetNames.GlobalSettingNameVm>>();
        GlobalSettingDetailVm = fixture.Create<GlobalSettingDetailVm>();
        CreateGlobalSettingCommand = fixture.Create<CreateGlobalSettingCommand>();
        UpdateGlobalSettingCommand = fixture.Create<UpdateGlobalSettingCommand>();
        AuthenticationCommand = fixture.Create<AuthenticationCommand>();
        PaginatedGlobalSettingListVm = fixture.Create<PaginatedResult<GlobalSettingListVm>>();
    }

    public void Dispose()
    {

    }
}