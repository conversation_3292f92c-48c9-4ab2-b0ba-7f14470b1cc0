using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Delete;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobStatus;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.IsNameUnique;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ReplicationJobControllerTests : IClassFixture<ReplicationJobFixture>
{
    private readonly ReplicationJobFixture _replicationJobFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ReplicationJobController _controller;

    public ReplicationJobControllerTests(ReplicationJobFixture replicationJobFixture)
    {
        _replicationJobFixture = replicationJobFixture;
        
        var testBuilder = new ControllerTestBuilder<ReplicationJobController>();
        _controller = testBuilder.CreateController(
            _ => new ReplicationJobController(),
            out _mediatorMock);
    }

    #region GetReplicationJobs Tests

    [Fact]
    public async Task GetReplicationJobs_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationJobListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationJobFixture.ReplicationJobListVm);

        // Act
        var result = await _controller.GetReplicationJobList();
        
    }

    [Fact]
    public async Task GetReplicationJobs_ReturnsEmptyList_WhenNoReplicationJobsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationJobListQuery>(), default))
            .ReturnsAsync(new List<ReplicationJobListVm>());

        // Act
        var result = await _controller.GetReplicationJobList();
        
    }

    [Fact]
    public async Task GetReplicationJobs_ReturnsReplicationJobsWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationJobListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationJobFixture.ReplicationJobListVm);

        // Act
        var result = await _controller.GetReplicationJobList();

       
    }

    #endregion

    #region CreateReplicationJob Tests

    [Fact]
    public async Task CreateReplicationJob_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _replicationJobFixture.CreateReplicationJobCommand;
        var expectedResponse = _replicationJobFixture.CreateReplicationJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplicationJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationJobResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }


    [Fact]
    public async Task CreateReplicationJob_WithCompleteReplicationJobData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateReplicationJobCommand
        {
            Name = "Custom Enterprise Replication Job",
            CompanyId = "COMP_CUSTOM",
            TemplateId = "TMPL_CUSTOM",
            TemplateName = "Custom Enterprise Replication Template",
            SolutionTypeId = "ST_CUSTOM",
            SolutionType = "Custom Enterprise Replication",
            NodeId = "NODE_CUSTOM",
            NodeName = "Custom Enterprise Replication Node",
            CronExpression = "0 0 3 * * ?",
            GroupPolicyId = "GP_CUSTOM",
            GroupPolicyName = "Custom Enterprise Replication Policy",
            ScheduleTime = "03:00",
            IsSchedule = 1,
            ScheduleType = 1,
            Type = "Scheduled",
            ExecutionPolicy = "Sequential"
        };

        var expectedResponse = new CreateReplicationJobResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "ReplicationJob has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplicationJob(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationJobResponse>(createdResult.Value);
        Assert.Equal("ReplicationJob has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    #endregion

    #region UpdateReplicationJob Tests

    [Fact]
    public async Task UpdateReplicationJob_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobCommand;
        var expectedResponse = _replicationJobFixture.UpdateReplicationJobResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationJob(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationJobResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateReplicationJob_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("ReplicationJob", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateReplicationJob(command));
    }

    #endregion

    #region DeleteReplicationJob Tests

    [Fact]
    public async Task DeleteReplicationJob_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _replicationJobFixture.DeleteReplicationJobResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationJobCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteReplicationJob(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteReplicationJobResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteReplicationJob_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplicationJob("invalid-guid"));
    }

    [Fact]
    public async Task DeleteReplicationJob_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplicationJob(""));
    }

    [Fact]
    public async Task DeleteReplicationJob_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationJobCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("ReplicationJob", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteReplicationJob(nonExistentId));
    }

    #endregion

    #region GetReplicationJobById Tests

    [Fact]
    public async Task GetReplicationJobById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _replicationJobFixture.ReplicationJobDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationJobDetailQuery>(q => q.JobId == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetReplicationJobById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<ReplicationJobListVm>(okResult.Value);
        Assert.Equal(expectedDetail.Name, returnedDetail.Name);
        Assert.Equal(expectedDetail.SolutionType, returnedDetail.SolutionType);
        Assert.Equal(expectedDetail.NodeName, returnedDetail.NodeName);
        Assert.Equal(expectedDetail.Status, returnedDetail.Status);
        Assert.Equal(expectedDetail.State, returnedDetail.State);
        Assert.Equal(expectedDetail.CronExpression, returnedDetail.CronExpression);
        Assert.Equal(expectedDetail.ExecutionPolicy, returnedDetail.ExecutionPolicy);
    }

    [Fact]
    public async Task GetReplicationJobById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationJobById("invalid-guid"));
    }

    [Fact]
    public async Task GetReplicationJobById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationJobById(""));
    }

    [Fact]
    public async Task GetReplicationJobById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationJobDetailQuery>(q => q.JobId == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("ReplicationJob", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetReplicationJobById(nonExistentId));
    }

    #endregion

    #region GetPaginatedReplicationJobs Tests

    [Fact]
    public async Task GetPaginatedReplicationJobs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _replicationJobFixture.GetReplicationJobPaginatedListQuery;
        var expectedResult = _replicationJobFixture.ReplicationJobPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginated(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationJobListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedReplicationJobs_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetReplicationJobPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Database",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<ReplicationJobListVm>
        {
            Data = _replicationJobFixture.ReplicationJobListVm.Where(rj => rj.Name.Contains("Database")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginated(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationJobListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Database", returnedResult.Data.First().Name);
    }

    #endregion

    #region IsReplicationJobNameExist Tests

    [Fact]
    public async Task IsReplicationJobNameExist_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Replication Job Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationJobNameUniqueQuery>(q => q.Name == uniqueName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsReplicationJobNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsReplicationJobNameExist_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Replication Job Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationJobNameUniqueQuery>(q => q.Name == existingName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsReplicationJobNameExist(existingName, id);

    }

    #endregion

    #region UpdateReplicationJobState Tests

    [Fact]
    public async Task UpdateReplicationJobState_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobStateCommand;
        var expectedResponse = _replicationJobFixture.UpdateReplicationJobStateResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationJobStateResponse>(okResult.Value);
        Assert.Contains("state has been updated successfully", returnedResponse.Message);
    }

    
    [Fact]
    public async Task UpdateReplicationJobState_WithValidStateTransition_UpdatesSuccessfully()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobStateCommand;

        var expectedResponse = new UpdateReplicationJobStateResponse
        {
          
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationJobState(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationJobStateResponse>(okResult.Value);
       
    }

    #endregion

    #region ResetJobStatus Tests

    [Fact]
    public async Task ResetJobStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobStatusCommand;
        var expectedResponse = _replicationJobFixture.UpdateReplicationJobStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationJobStatusResponse>(okResult.Value);
        Assert.Contains("status has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task ResetJobStatus_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _replicationJobFixture.UpdateReplicationJobStatusCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("ReplicationJob", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateReplicationJobStatus(command));
    }

    [Fact]
    public async Task ResetJobStatus_WithValidStatusReset_ResetsSuccessfully()
    {
        // Arrange
        var command = new UpdateReplicationJobStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            Status = "Pending"
        };

        var expectedResponse = new UpdateReplicationJobStatusResponse
        {
            Id = command.Id,
            Message = "ReplicationJob status has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationJobStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationJobStatusResponse>(okResult.Value);
        Assert.Equal("ReplicationJob status has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.Id);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsReplicationJobCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
