using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;
using Newtonsoft.Json;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class AlertRepositoryTests : IClassFixture<AlertFixture>,IClassFixture<InfraObjectFixture>
{
    private readonly AlertFixture _alertFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly AlertRepository _repository;
    private readonly AlertRepository _repositoryNotParent;
    private readonly InfraObjectFixture _infraObjectFixture;    

    public AlertRepositoryTests(AlertFixture alertFixture, InfraObjectFixture infraObjectFixture)
    {
        _alertFixture = alertFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new AlertRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new AlertRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
        _infraObjectFixture = infraObjectFixture;
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;

        // Act
      await   _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(alert.ReferenceId); 
        // Assert
        Assert.NotNull(result);
        Assert.Equal(alert.Type, result.Type);
        Assert.Equal(alert.Severity, result.Severity);
        Assert.Equal(alert.CompanyId, result.CompanyId);
        Assert.Single(_dbContext.Alerts);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        alert.Type = "Updated Type";
        alert.Severity = "Critical";
        alert.IsResolve = 1;

         _dbContext.Alerts.Update(alert);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.GetByReferenceIdAsync(alert.ReferenceId);

        // Assert
        Assert.Equal("Updated Type", result.Type);
        Assert.Equal("Critical", result.Severity);
        Assert.Equal(1, result.IsResolve);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act

        alert.IsActive = false;
         _dbContext.Alerts.Update(alert);
        await _dbContext.SaveChangesAsync();
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var alert = _alertFixture.AlertDto;
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByIdAsync(alert.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(alert.Id, result.Id);
        Assert.Equal(alert.Type, result.Type);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnFirstEntity_WhenIdIsZero()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(3).ToList();
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetByIdAsync(0);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetAlertByMaxId Tests

    [Fact]
    public async Task GetAlertByMaxId_ShouldReturnMaxId_WhenAlertsExist()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(5).ToList();
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByMaxId();

        // Assert
        Assert.True(result > 0);
    }

    [Fact]
    public async Task GetAlertByMaxId_ShouldReturnZero_WhenNoAlertsExist()
    {
        // Act
        var result = await _repository.GetAlertByMaxId();

        // Assert
        Assert.Equal(0, result);
    }

    #endregion

    #region GetAlertByInfraObjectId Tests

    [Fact]
    public async Task GetAlertByInfraObjectId_ShouldReturnMatchingAlerts()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var entityId = "ENTITY_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                InfraObjectId = "INFRA_002", 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByInfraObjectId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(entityId, x.EntityId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }
    [Fact]
    public async Task GetAlertByInfraObjectId_ShouldReturnMatchingAlertsWhenIsParentFalse()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var entityId = "ENTITY_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                InfraObjectId = infraObjectId, 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                InfraObjectId = "INFRA_002", 
                EntityId = entityId, 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repositoryNotParent.GetAlertByInfraObjectId(infraObjectId, entityId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.Equal(entityId, x.EntityId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByInfraObjectId("NON_EXISTENT_INFRA", "NON_EXISTENT_ENTITY");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetAlertByClientAlertId Tests

    [Fact]
    public async Task GetAlertByClientAlertId_ShouldReturnMatchingAlerts()
    {
        // Arrange
        var clientAlertId = "CLIENT_ALERT_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                ClientAlertId = "CLIENT_ALERT_002", 
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByClientAlertId(clientAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(clientAlertId, x.ClientAlertId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }
    [Fact]
    public async Task GetAlertByClientAlertId_ShouldReturnMatchingAlertsWhenParentFalse()
    {
        // Arrange
        var clientAlertId = "CLIENT_ALERT_001";
        
        var alerts = new List<Alert>
        {
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type1"
            },
            new Alert 
            { 
                ClientAlertId = clientAlertId, 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type2"
            },
            new Alert 
            { 
                ClientAlertId = "CLIENT_ALERT_002", 
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "Type3"
            }
        };

        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repositoryNotParent.GetAlertByClientAlertId(clientAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(clientAlertId, x.ClientAlertId));
        Assert.All(result, x => Assert.True(x.IsActive));
    }

    [Fact]
    public async Task GetAlertByClientAlertId_ShouldReturnEmpty_WhenNoMatch()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        await _repository.AddRangeAsync(alerts);

        // Act
        var result = await _repository.GetAlertByClientAlertId("NON_EXISTENT_CLIENT_ALERT");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region GetLastAlertByInfraObject Tests

    [Fact]
    public async Task GetLastAlertByInfraObject_ShouldReturnAlertsInDateRange()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var userLastAlertDate = baseDate.AddDays(-5);
        var alertDate = baseDate.AddDays(5);

        var alerts = new List<Alert>
        {
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-3),
                LastModifiedDate = baseDate.AddDays(-2),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange1"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-1),
                LastModifiedDate = baseDate.AddDays(1),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange2"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-10),
                LastModifiedDate = baseDate.AddDays(-8),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "OutOfRange"
            }
        };

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetLastAlertByInfraObject(infraObjectId, userLastAlertDate, alertDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.True(x.CreatedDate >= userLastAlertDate));
        Assert.All(result, x => Assert.True(x.LastModifiedDate <= alertDate));
    }

    [Fact]
    public async Task GetLastAlertByInfraObject_ShouldReturnAlertsInDateRangeAndnotParent()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var userLastAlertDate = baseDate.AddDays(-5);
        var alertDate = baseDate.AddDays(5);

        var alerts = new List<Alert>
        {
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-3),
                LastModifiedDate = baseDate.AddDays(-2),
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange1"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-1),
                LastModifiedDate = baseDate.AddDays(1),
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange2"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-10),
                LastModifiedDate = baseDate.AddDays(-8),
                IsActive = true,
                CompanyId = "ChHILD_COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "OutOfRange"
            }
        };

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();
        // Act
        var result = await _repositoryNotParent.GetLastAlertByInfraObject(infraObjectId, userLastAlertDate, alertDate);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(infraObjectId, x.InfraObjectId));
        Assert.All(result, x => Assert.True(x.CreatedDate >= userLastAlertDate));
        Assert.All(result, x => Assert.True(x.LastModifiedDate <= alertDate));
    }

    #endregion

    #region GetAlertByUserLastAlertId Tests

    [Fact]
    public async Task GetAlertByUserLastAlertId_ShouldReturnAlertsAfterGivenId_WhenPreviousAlertsExist()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList.Take(5).ToList();
        await _repository.AddRangeAsync(alerts);

        var addedAlerts = await _repository.ListAllAsync();
        var sortedAlerts = addedAlerts.OrderBy(x => x.Id).ToList();
        var userLastAlertId = sortedAlerts[2].Id; 

        // Act
        var result = await _repository.GetAlertByUserLastAlertId(userLastAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count >= 1);
        Assert.All(result, x => Assert.True(x.Id > userLastAlertId));
    }

    [Fact]
    public async Task GetAlertByUserLastAlertId_ShouldReturnAlertsFromGivenId_WhenNoPreviousAlerts()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(3).ToList();
        await _repository.AddRangeAsync(alerts);

        var addedAlerts = await _repository.ListAllAsync();
        var firstAlertId = addedAlerts.Min(x => x.Id);

        // Act
        var result = await _repository.GetAlertByUserLastAlertId(firstAlertId);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.Id >= firstAlertId));
    }

    #endregion

    #region GetAlertByUserLastInfraObjectId Tests

    [Fact]
    public async Task GetAlertByUserLastInfraObjectId_ShouldReturnAlertsAfterDate()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var baseDate = DateTime.Now.Date;
        var createdDate = baseDate.AddDays(-2);

        var alerts = new List<Alert>
        {
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-1),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "AfterDate"
            },
            new Alert
            {
                InfraObjectId = infraObjectId,
                CreatedDate = baseDate.AddDays(-5),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "BeforeDate"
            }
        };

         _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = await _repository.GetAlertByUserLastInfraObjectId(infraObjectId, createdDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("AfterDate", result.First().Type);
        Assert.True(result.First().CreatedDate >= createdDate);
    }

    #endregion

    #region GetAlertListFilterByDate Tests

    [Fact]
    public async Task GetAlertListFilterByDate_ShouldReturnAlertsInDateRange()
    {
        // Arrange
        var baseDate = DateTime.Now.Date;
        var startDate = baseDate.AddDays(-5).ToString("yyyy-MM-dd");
        var endDate = baseDate.AddDays(5).ToString("yyyy-MM-dd");

        var alerts = new List<Alert>
        {
            new Alert
            {
                CreatedDate = baseDate.AddDays(-3),
                LastModifiedDate = baseDate.AddDays(-2),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "InRange"
            },
            new Alert
            {
                CreatedDate = baseDate.AddDays(-10),
                LastModifiedDate = baseDate.AddDays(-8),
                IsActive = true,
                CompanyId = "COMPANY_123",
                ReferenceId = Guid.NewGuid().ToString(),
                Type = "OutOfRange"
            }
        };

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();
        // Act
        var result = await _repository.GetAlertListFilterByDate(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("InRange", result.First().Type);
    }

    #endregion

    #region PaginatedListAllAsync Tests

    [Fact]
    public void PaginatedListAllAsync_ShouldReturnQueryable()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList;
        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Count() > 0);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterByCompanyId_WhenNotParent()
    {
        var infra = _infraObjectFixture.InfraObjectList;
        infra.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.InfraObjects.AddRangeAsync(infra);

        var repository = new AlertRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());

        var alerts = _alertFixture.AlertPaginationList;
        // Set some with different company IDs
        alerts.Take(5).ToList().ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        alerts.Take(5).ToList().ForEach(x => x.InfraObjectId = infra[0].ReferenceId);

        alerts[3].InfraObjectId = infra[1].ReferenceId;
        alerts[4].InfraObjectId = infra[2].ReferenceId;
        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = repository.GetPaginatedQuery();;

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.All(filteredResults, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    #endregion

    #region GetPaginatedBySeverity Tests

    [Fact]
    public void GetPaginatedBySeverity_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;

        alerts[0].Severity = "Critical";

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedBySeverity("Critical");

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal("Critical", filteredResults.First().Severity);
    }

    #endregion

    #region GetPaginatedByType Tests

    [Fact]
    public void GetPaginatedByType_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var type = "Database";
        var alerts = _alertFixture.AlertList;
        alerts[0].Type = type;

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedByType(type);

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(type, filteredResults.First().Type);
    }

    #endregion

    #region GetPaginatedByInfraObjectId Tests

    [Fact]
    public void GetPaginatedByInfraObjectId_ShouldReturnFilteredAlerts()
    {
        // Arrange
        var infraObjectId = "INFRA_001";
        var alerts = _alertFixture.AlertList;
        alerts[0].InfraObjectId = infraObjectId;

        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedByInfraObjectId(infraObjectId);

        // Assert
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
        Assert.Equal(infraObjectId, filteredResults.First().InfraObjectId);
    }
    [Fact]
    public async Task GetPaginatedByInfraObjectId_ShouldReturnFilteredWhenIsParentFalse()
    {
        // Arrange
        var infraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c";
        var alerts = _alertFixture.AlertList;
        alerts[0].InfraObjectId = infraObjectId;
        alerts[0].CompanyId = "ChHILD_COMPANY_123";
        await _dbContext.Alerts.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = _repositoryNotParent.GetPaginatedByInfraObjectId(infraObjectId);

        // Assert           
        Assert.NotNull(result);
        var filteredResults = result.ToList();
        Assert.Single(filteredResults);
       
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var alerts = _alertFixture.AlertList;
        var alert1 = alerts[0];
        var alert2 = alerts[1];


        // Act
        
        await _dbContext.Alerts.AddAsync(alert1);
        await _dbContext.Alerts.AddAsync(alert2);
        await _dbContext.SaveChangesAsync();

        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.Alerts.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var alerts = _alertFixture.AlertList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(alerts);
        var initialCount = alerts.Count;

        var toUpdate = alerts.Take(2).ToList();
        toUpdate.ForEach(x => x.Type = "Updated Type");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = alerts.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.Type == "Updated Type").ToList();
        Assert.Equal(2, updated.Count);
    }

    #endregion
    public void Dispose() => _dbContext?.Dispose();

    private async Task ClearDb()
    {
        _dbContext.Alerts.RemoveRange(_dbContext.Alerts);
        await _dbContext.SaveChangesAsync();
    }

    [Fact]
    public async Task GetAlertByMaxId_ReturnsMaxIdOrZero()
    {
        await ClearDb();
        var alert = new Alert { Id = 5, IsActive = true };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetAlertByMaxId();
        Assert.Equal(5, result);

        await ClearDb();
        var resultEmpty = await _repository.GetAlertByMaxId();
        Assert.Equal(0, resultEmpty);
    }

    [Fact]
    public async Task GetByIdAsync_ReturnsAlertOrNull()
    {
        await ClearDb();
        var alert = new Alert { Id = 10, IsActive = true };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var found = await _repository.GetByIdAsync(10);
        Assert.NotNull(found);
        Assert.Equal(10, found.Id);

        var notFound = await _repository.GetByIdAsync(999);
        Assert.Null(notFound);

        var first = await _repository.GetByIdAsync(0);
        Assert.NotNull(first);
    }

    [Fact]
    public void GetPaginatedQuery_ReturnsCorrectQuery_ForParentAndChild()
    {
        var alert1 = new Alert { Id = 1, IsActive = true, CompanyId = "COMPANY_1", InfraObjectId = "INFRA_1" };
        var alert2 = new Alert { Id = 2, IsActive = true, CompanyId = "COMPANY_2", InfraObjectId = "INFRA_2" };
        _dbContext.Alerts.AddRange(alert1, alert2);
        _dbContext.SaveChanges();

        var parentQuery = _repository.GetPaginatedQuery();
        Assert.True(parentQuery.Any());

        var childQuery = _repository.GetPaginatedQuery();
        Assert.True(childQuery.Any());
    }

    [Fact]
    public async Task GetAlertByInfraObjectId_ReturnsAlerts_FilteredByParent()
    {
        await ClearDb();
        var alert = new Alert { InfraObjectId = "INFRA_1", EntityId = "ENTITY_1", IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetAlertByInfraObjectId("INFRA_1", "ENTITY_1");
        Assert.Single(parentResult);

    }

    [Fact]
    public async Task GetAlertByClientAlertId_ReturnsAlerts_FilteredByParent()
    {
        await ClearDb();
        var alert = new Alert { ClientAlertId = "CLIENT_1", IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetAlertByClientAlertId("CLIENT_1");
        Assert.Single(parentResult);

   
    }

    [Fact]
    public async Task GetLastAlertByInfraObject_ReturnsAlerts_FilteredByParent()
    {
        await ClearDb();
        var now = DateTime.UtcNow;
        var alert = new Alert
        {
            InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c",
            CreatedDate = now,
            LastModifiedDate = now,
            IsActive = true,
            CompanyId = "ChHILD_COMPANY_123"
        };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetLastAlertByInfraObject("70bb97c9-1193-4e86-98ab-bebc88fb438c", now.AddDays(-11), now.AddDays(12));
        Assert.Single(parentResult);

       
    }

    [Fact]
    public async Task GetByAlertId_ReturnsAlerts_FilteredByParent()
    {
        await ClearDb();
        var now = DateTime.UtcNow;
        var alert = new Alert
        {
            ReferenceId = Guid.NewGuid().ToString(),
            CreatedDate = now,
            LastModifiedDate = now,
            IsActive = true,
            CompanyId = "COMPANY_1"
        };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetByAlertId(123, now.AddDays(-1), now.AddDays(1));
        Assert.Single(parentResult);

    }

    [Fact]
    public void GetAlertByUserLastAlertIdAndDate_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { Id = 5, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetAlertByUserLastAlertIdAndDate(5);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetAlertByUserLastAlertIdAndDate(5);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetAlertByUserLastAlertIdAndDate_ParentUser_WithPreviousAlerts()
    {
        // Arrange
        var alerts = new List<Alert>
        {
            new Alert { Id = 4, IsActive = true },
            new Alert { Id = 5, IsActive = true },
            new Alert { Id = 6, IsActive = true }
        };
        _dbContext.Alerts.AddRange(alerts);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetAlertByUserLastAlertIdAndDate(5).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.Id > 5 || x.Id == 5));
    }

    [Fact]
    public async Task GetAlertByUserLastAlertIdAndDate_ParentUser_WithoutPreviousAlerts()
    {
        // Arrange
        var alert = new Alert { Id = 5, IsActive = true };
        await  _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = _repository.GetAlertByUserLastAlertIdAndDate(5).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Contains(result, x => x.Id == 5);
    }

    [Fact]
    public async Task GetAlertByUserLastAlertIdAndDate_ChildUser_WithPreviousAlerts()
    {
        // Arrange
        var alerts = new List<Alert>
        {
            new Alert { Id = 3, IsActive = true, CompanyId = "COMPANY_1" },
            new Alert { Id = 4, IsActive = true, CompanyId = "COMPANY_1" },
            new Alert { Id = 5, IsActive = true, CompanyId = "COMPANY_1" }
        };
      await  _dbContext.Alerts.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = _repositoryNotParent.GetAlertByUserLastAlertIdAndDate(5).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.Id >= 5));
        Assert.All(result, x => Assert.Equal("COMPANY_1", x.CompanyId));
    }

    [Fact]
    public async  Task GetAlertByUserLastAlertIdAndDate_ChildUser_WithoutPreviousAlerts()
    {
        // Arrange
        var alert = new Alert { Id = 5, IsActive = true, CompanyId = "ChHILD_COMPANY_123" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = _repositoryNotParent.GetAlertByUserLastAlertIdAndDate(5).ToList();

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(5, result[0].Id);
        Assert.Equal("ChHILD_COMPANY_123", result[0].CompanyId);
    }
    [Fact]
    public async Task GetAlertByUserLastInfraObjectIdAndDate_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", CreatedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await  _dbContext.SaveChangesAsync();

        var parentResult = _repository.GetAlertByUserLastInfraObjectIdAndDate("INFRA_1", now.AddMinutes(-1));
        Assert.NotNull(parentResult);

        var childResult = _repository.GetAlertByUserLastInfraObjectIdAndDate("INFRA_1", now.AddMinutes(-1));
        Assert.NotNull(childResult);
    }

    [Fact]
    public async Task GetAlertByUserLastAlertId_ReturnsAlerts_FilteredByParentAndChild()
    {
        await ClearDb();
        var alert = new Alert { Id = 5, IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetAlertByUserLastAlertId(5);
        Assert.NotNull(parentResult);

        var childResult = await _repository.GetAlertByUserLastAlertId(5);
        Assert.NotNull(childResult);
    }

    [Fact]
    public async Task GetAlertByUserLastInfraObjectId_ReturnsAlerts_FilteredByParentAndChild()
    {
        await ClearDb();
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", CreatedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var parentResult = await _repository.GetAlertByUserLastInfraObjectId("INFRA_1", now.AddMinutes(-1));
        Assert.NotNull(parentResult);

        var childResult = await _repository.GetAlertByUserLastInfraObjectId("INFRA_1", now.AddMinutes(-1));
        Assert.NotNull(childResult);
    }

    [Fact]
    public async Task GetAlertListFilterByDate_ReturnsAlerts_FilteredByParentAndChild()
    {
        await ClearDb();
        var now = DateTime.UtcNow;
        var alert = new Alert { CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        await _dbContext.Alerts.AddAsync(alert);
        await _dbContext.SaveChangesAsync();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = await _repository.GetAlertListFilterByDate(startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = await _repository.GetAlertListFilterByDate(startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectId_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { InfraObjectId = "INFRA_1", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedByInfraObjectId("INFRA_1");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectId("INFRA_1");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedBySeverity_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { Severity = "High", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedBySeverity("High");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedBySeverity("High");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByType_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { Type = "Error", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedByType("Error");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByType("Error");
        Assert.NotNull(childResult);
    }
    [Fact]
    public void GetPaginatedByType_ReturnsQueryable_FilteredByIsNotParent()
    {
        var alert = new Alert { Type = "Error", IsActive = true, CompanyId = "ChHILD_COMPANY_123" ,InfraObjectId= "70bb97c9-1193-4e86-98ab-bebc88fb438c" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var childResult = _repository.GetPaginatedByType("Error");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByCreateAndEndDate_ReturnsQueryable_FilteredByParent()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedByCreateAndEndDate(startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByCreateAndEndDate(startDate, endDate);
        Assert.NotNull(childResult);
    }
    [Fact]
    public void GetPaginatedByCreateAndEndDate_ReturnsQueryable_FilteredByChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "ChHILD_COMPANY_123",InfraObjectId= "70bb97c9-1193-4e86-98ab-bebc88fb438c" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");


        var childResult = _repositoryNotParent.GetPaginatedByCreateAndEndDate(startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectAndSeverity_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { InfraObjectId = "INFRA_1", Severity = "High", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedByInfraObjectAndSeverity("INFRA_1", "High");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectAndSeverity("INFRA_1", "High");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectAndType_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { InfraObjectId = "INFRA_1", Type = "Error", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedByInfraObjectAndType("INFRA_1", "Error");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectAndType("INFRA_1", "Error");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectAndDates_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedByInfraObjectAndDates("INFRA_1", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectAndDates("INFRA_1", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectSeverityType_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { InfraObjectId = "INFRA_1", Severity = "High", Type = "Error", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedByInfraObjectSeverityType("INFRA_1", "High", "Error");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectSeverityType("INFRA_1", "High", "Error");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectWithSeverityAndDates_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", Severity = "High", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedByInfraObjectWithSeverityAndDates("INFRA_1", "High", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectWithSeverityAndDates("INFRA_1", "High", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByInfraObjectWithTypeAndDates_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", Type = "Error", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedByInfraObjectWithTypeAndDates("INFRA_1", "Error", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByInfraObjectWithTypeAndDates("INFRA_1", "Error", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedByAllUniqueData_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { InfraObjectId = "INFRA_1", Severity = "High", Type = "Error", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedByAllUniqueData("INFRA_1", "High", "Error", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedByAllUniqueData("INFRA_1", "High", "Error", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedBySeverityAndType_ReturnsQueryable_FilteredByParentAndChild()
    {
        var alert = new Alert { Severity = "High", Type = "Error", IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var parentResult = _repository.GetPaginatedBySeverityAndType("High", "Error");
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedBySeverityAndType("High", "Error");
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedSeverityByDates_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { Severity = "High", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedSeverityByDates("High", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedSeverityByDates("High", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedBySeverityTypeAndDates_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { Severity = "High", Type = "Error", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedBySeverityTypeAndDates("High", "Error", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedBySeverityTypeAndDates("High", "Error", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public void GetPaginatedTypeAndDate_ReturnsQueryable_FilteredByParentAndChild()
    {
        var now = DateTime.UtcNow;
        var alert = new Alert { Type = "Error", CreatedDate = now, LastModifiedDate = now, IsActive = true, CompanyId = "COMPANY_1" };
        _dbContext.Alerts.Add(alert);
        _dbContext.SaveChanges();

        var startDate = now.AddMinutes(-1).ToString("yyyy-MM-dd");
        var endDate = now.AddMinutes(1).ToString("yyyy-MM-dd");

        var parentResult = _repository.GetPaginatedTypeAndDate("Error", startDate, endDate);
        Assert.NotNull(parentResult);

        var childResult = _repository.GetPaginatedTypeAndDate("Error", startDate, endDate);
        Assert.NotNull(childResult);
    }

    [Fact]
    public async Task GetPaginatedInfraObject_ReturnsFilteredQueryable()
    {
        var alert = new Alert { InfraObjectId = "INFRA_1", IsActive = true, CompanyId = "COMPANY_1" };
       await _dbContext.Alerts.AddAsync(alert);
       await  _dbContext.SaveChangesAsync();

        var query = _dbContext.Alerts.AsQueryable();
        var result = _repositoryNotParent.GetPaginatedInfraObject(query);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnCorrectResults_WhenParentAndAllInfraTrue()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList;

        alerts[0].Severity="High";
        alerts[1].Severity="High";

        await _dbContext.Alerts.AddRangeAsync(alerts);
        await _dbContext.SaveChangesAsync();

        var specification = new AlertFilterSpecification("");
        Expression<Func<Alert, bool>> expression = x => true;

        // Act
        var (result, severityCounts) = await _repository.PaginatedListAllAsync(1, 10, specification, expression, "Id", "desc");

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result.Data);
        Assert.True(severityCounts.ContainsKey("High"));
        Assert.Equal(2, severityCounts["High"]);
    }
    [Fact]
    public async Task PaginatedListAllAsync_ShouldCallGetPaginatedInfraObject_WhenParentAndAllInfraFalse()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList;
        alerts.ForEach(x => x.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c");

        await _repository.AddRangeAsync(alerts);
        var mockUsers =new Mock<ILoggedInUserService>();

        mockUsers.Setup(x => x.IsParent).Returns(true);
        mockUsers.Setup(x => x.IsAllInfra).Returns(false);
        mockUsers.Setup(x => x.CompanyId).Returns(alerts[0].CompanyId);
        mockUsers.Setup(x => x.IsAllInfra).Returns(false);
        mockUsers.Setup(x => x.IsAuthenticated).Returns(true);
        mockUsers.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(DbContextFactory.GetAssignedEntityForIsAllinfraFalse()));
        var repo = new AlertRepository(_dbContext, mockUsers.Object);
       

        var spec = new AlertFilterSpecification("");
        Expression<Func<Alert, bool>> expr = x => true;

        // Act
        var (result, severityCounts) = await repo.PaginatedListAllAsync(1, 10, spec, expr, "Id", "desc");

        // Assert
        Assert.NotEmpty(result.Data);
    }
    [Fact]
    public async Task PaginatedListAllAsync_ShouldFilterByCompanyId_WhenChildAndAllInfraTrue()
    {
        // Arrange
        var alerts = _alertFixture.AlertPaginationList;
        alerts.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
       
        await _repository.AddRangeAsync(alerts);
        var mockUsers = new Mock<ILoggedInUserService>();

        mockUsers.Setup(x => x.IsParent).Returns(false);
        mockUsers.Setup(x => x.IsAllInfra).Returns(true);
        mockUsers.Setup(x => x.CompanyId).Returns(alerts[0].CompanyId);
        mockUsers.Setup(x => x.IsAllInfra).Returns(false);
        mockUsers.Setup(x => x.IsAuthenticated).Returns(true);
        mockUsers.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(DbContextFactory.GetAssignedEntityForIsAllinfraFalse()));
        var repo = new AlertRepository(_dbContext, mockUsers.Object);

        var spec = new AlertFilterSpecification("");
        Expression<Func<Alert, bool>> expr = x => true;

        // Act
        var (result, severityCounts) = await repo.PaginatedListAllAsync(1, 10, spec, expr, "Id", "desc");

        // Assert
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }
    [Fact]
    public async Task PaginatedListAllAsync_ShouldCallGetPaginatedInfraObject_WhenChildAndAllInfraFalse()
    {
        var alerts = _alertFixture.AlertPaginationList;
        alerts.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        alerts.ForEach(x => x.InfraObjectId = "70bb97c9-1193-4e86-98ab-bebc88fb438c");
        alerts[4].InfraObjectId = "70bb97c9-1193-4e86-98ab-jhbc88fb438d";
        await _repository.AddRangeAsync(alerts);
        var mockUsers = new Mock<ILoggedInUserService>();

        mockUsers.Setup(x => x.IsParent).Returns(false);
        mockUsers.Setup(x => x.IsAllInfra).Returns(true);
        mockUsers.Setup(x => x.CompanyId).Returns(alerts[0].CompanyId);
        mockUsers.Setup(x => x.IsAllInfra).Returns(false);
        mockUsers.Setup(x => x.AssignedInfras).Returns(JsonConvert.SerializeObject(DbContextFactory.GetAssignedEntityForIsAllinfraFalse()));
        var repo = new AlertRepository(_dbContext, mockUsers.Object);

        var spec = new AlertFilterSpecification("");
        Expression<Func<Alert, bool>> expr = x => true;

        // Act
        var (result, severityCounts) = await _repositoryNotParent.PaginatedListAllAsync(1, 10, spec, expr, "Id", "desc");

        // Assert
        Assert.All(result.Data, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

}
