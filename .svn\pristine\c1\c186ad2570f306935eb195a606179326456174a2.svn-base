using AutoFixture;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Delete;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobStatus;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.IsNameUnique;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ReplicationJobFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ReplicationJob> ReplicationJobs { get; set; }
    public List<ReplicationJob> InvalidReplicationJobs { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<ReplicationJobListVm> ReplicationJobListVm { get; }
    public ReplicationJobListVm ReplicationJobDetailVm { get; }
    public PaginatedResult<ReplicationJobListVm> ReplicationJobPaginatedListVm { get; }

    // Commands
    public CreateReplicationJobCommand CreateReplicationJobCommand { get; set; }
    public UpdateReplicationJobCommand UpdateReplicationJobCommand { get; set; }
    public DeleteReplicationJobCommand DeleteReplicationJobCommand { get; set; }
    public UpdateReplicationJobStateCommand UpdateReplicationJobStateCommand { get; set; }
    public UpdateReplicationJobStatusCommand UpdateReplicationJobStatusCommand { get; set; }

    // Queries
    public GetReplicationJobDetailQuery GetReplicationJobDetailQuery { get; set; }
    public GetReplicationJobListQuery GetReplicationJobListQuery { get; set; }
    public GetReplicationJobPaginatedListQuery GetReplicationJobPaginatedListQuery { get; set; }
    public GetReplicationJobNameUniqueQuery GetReplicationJobNameUniqueQuery { get; set; }

    // Responses
    public CreateReplicationJobResponse CreateReplicationJobResponse { get; set; }
    public UpdateReplicationJobResponse UpdateReplicationJobResponse { get; set; }
    public DeleteReplicationJobResponse DeleteReplicationJobResponse { get; set; }
    public UpdateReplicationJobStateResponse UpdateReplicationJobStateResponse { get; set; }
    public UpdateReplicationJobStatusResponse UpdateReplicationJobStatusResponse { get; set; }

    public ReplicationJobFixture()
    {
        try
        {
            // Create test data using AutoFixture
            ReplicationJobs = AutoReplicationJobFixture.Create<List<ReplicationJob>>();
            InvalidReplicationJobs = AutoReplicationJobFixture.Create<List<ReplicationJob>>();
            UserActivities = AutoReplicationJobFixture.Create<List<UserActivity>>();

            // Set invalid replication jobs to inactive
            foreach (var invalidJob in InvalidReplicationJobs)
            {
                invalidJob.IsActive = false;
            }

            // Commands
            CreateReplicationJobCommand = AutoReplicationJobFixture.Create<CreateReplicationJobCommand>();
            UpdateReplicationJobCommand = AutoReplicationJobFixture.Create<UpdateReplicationJobCommand>();
            DeleteReplicationJobCommand = AutoReplicationJobFixture.Create<DeleteReplicationJobCommand>();
            UpdateReplicationJobStateCommand = AutoReplicationJobFixture.Create<UpdateReplicationJobStateCommand>();
            UpdateReplicationJobStatusCommand = AutoReplicationJobFixture.Create<UpdateReplicationJobStatusCommand>();

            // Set command IDs to match existing entities
            if (ReplicationJobs.Any())
            {
                UpdateReplicationJobCommand.Id = ReplicationJobs.First().ReferenceId;
                DeleteReplicationJobCommand.Id = ReplicationJobs.First().ReferenceId;
            }

            // Queries
            GetReplicationJobDetailQuery = AutoReplicationJobFixture.Create<GetReplicationJobDetailQuery>();
            GetReplicationJobListQuery = AutoReplicationJobFixture.Create<GetReplicationJobListQuery>();
            GetReplicationJobPaginatedListQuery = AutoReplicationJobFixture.Create<GetReplicationJobPaginatedListQuery>();
            GetReplicationJobNameUniqueQuery = AutoReplicationJobFixture.Create<GetReplicationJobNameUniqueQuery>();

            // Set query IDs to match existing entities
            if (ReplicationJobs.Any())
            {
                GetReplicationJobDetailQuery.JobId = ReplicationJobs.First().ReferenceId;
                GetReplicationJobNameUniqueQuery.Name = ReplicationJobs.First().Name;
            }

            // Responses
            CreateReplicationJobResponse = AutoReplicationJobFixture.Create<CreateReplicationJobResponse>();
            UpdateReplicationJobResponse = AutoReplicationJobFixture.Create<UpdateReplicationJobResponse>();
            DeleteReplicationJobResponse = AutoReplicationJobFixture.Create<DeleteReplicationJobResponse>();
            UpdateReplicationJobStateResponse = AutoReplicationJobFixture.Create<UpdateReplicationJobStateResponse>();
            UpdateReplicationJobStatusResponse = AutoReplicationJobFixture.Create<UpdateReplicationJobStatusResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            ReplicationJobs = new List<ReplicationJob>();
            InvalidReplicationJobs = new List<ReplicationJob>();
            UserActivities = new List<UserActivity>();
            CreateReplicationJobCommand = new CreateReplicationJobCommand();
            UpdateReplicationJobCommand = new UpdateReplicationJobCommand();
            DeleteReplicationJobCommand = new DeleteReplicationJobCommand();
            UpdateReplicationJobStateCommand = new UpdateReplicationJobStateCommand();
            UpdateReplicationJobStatusCommand = new UpdateReplicationJobStatusCommand();
            GetReplicationJobDetailQuery = new GetReplicationJobDetailQuery();
            GetReplicationJobListQuery = new GetReplicationJobListQuery();
            GetReplicationJobPaginatedListQuery = new GetReplicationJobPaginatedListQuery();
            GetReplicationJobNameUniqueQuery = new GetReplicationJobNameUniqueQuery();
            CreateReplicationJobResponse = new CreateReplicationJobResponse();
            UpdateReplicationJobResponse = new UpdateReplicationJobResponse();
            DeleteReplicationJobResponse = new DeleteReplicationJobResponse();
            UpdateReplicationJobStateResponse = new UpdateReplicationJobStateResponse();
            UpdateReplicationJobStatusResponse = new UpdateReplicationJobStatusResponse();
        }

        // Configure View Models
        ReplicationJobListVm = new List<ReplicationJobListVm>
        {
            new ReplicationJobListVm
            {
                Id = "RJ_001",
                CompanyId = "COMP_001",
                Name = "Database Replication Job",
                TemplateId = "TMPL_001",
                TemplateName = "Database Replication Template",
                SolutionTypeId = "ST_001",
                SolutionType = "Database Replication",
                NodeId = "NODE_001",
                NodeName = "Primary Replication Node",
                Status = "Running",
                CronExpression = "0 0 2 * * ?",
                GroupPolicyId = "GP_001",
                GroupPolicyName = "Database Replication Policy",
                LastExecutionTime = "2024-01-15T02:00:00Z",
                ExceptionMessage = "",
                ScheduleTime = "02:00",
                IsSchedule = 1,
                ScheduleType = 1,
                Type = "Scheduled",
                State = "Active",
                ExecutionPolicy = "Sequential"
            },
            new ReplicationJobListVm
            {
                Id = "RJ_002",
                CompanyId = "COMP_001",
                Name = "File System Replication Job",
                TemplateId = "TMPL_002",
                TemplateName = "File System Replication Template",
                SolutionTypeId = "ST_002",
                SolutionType = "File System Replication",
                NodeId = "NODE_002",
                NodeName = "Secondary Replication Node",
                Status = "Completed",
                CronExpression = "0 0 */4 * * ?",
                GroupPolicyId = "GP_002",
                GroupPolicyName = "File System Replication Policy",
                LastExecutionTime = "2024-01-15T04:00:00Z",
                ExceptionMessage = "",
                ScheduleTime = "Every 4 hours",
                IsSchedule = 1,
                ScheduleType = 2,
                Type = "Scheduled",
                State = "Active",
                ExecutionPolicy = "Parallel"
            },
            new ReplicationJobListVm
            {
                Id = "RJ_003",
                CompanyId = "COMP_001",
                Name = "Application Data Replication Job",
                TemplateId = "TMPL_003",
                TemplateName = "Application Data Replication Template",
                SolutionTypeId = "ST_003",
                SolutionType = "Application Data Replication",
                NodeId = "NODE_001",
                NodeName = "Primary Replication Node",
                Status = "Failed",
                CronExpression = "0 */15 * * * ?",
                GroupPolicyId = "GP_003",
                GroupPolicyName = "Application Data Replication Policy",
                LastExecutionTime = "2024-01-15T03:15:00Z",
                ExceptionMessage = "Connection timeout to target database",
                ScheduleTime = "Every 15 minutes",
                IsSchedule = 1,
                ScheduleType = 3,
                Type = "Scheduled",
                State = "Error",
                ExecutionPolicy = "Sequential"
            },
            new ReplicationJobListVm
            {
                Id = "RJ_004",
                CompanyId = "COMP_001",
                Name = "Storage Array Replication Job",
                TemplateId = "TMPL_004",
                TemplateName = "Storage Array Replication Template",
                SolutionTypeId = "ST_004",
                SolutionType = "Storage Array Replication",
                NodeId = "NODE_003",
                NodeName = "Storage Replication Node",
                Status = "Pending",
                CronExpression = "0 0 1 * * ?",
                GroupPolicyId = "GP_004",
                GroupPolicyName = "Storage Array Replication Policy",
                LastExecutionTime = "",
                ExceptionMessage = "",
                ScheduleTime = "01:00",
                IsSchedule = 1,
                ScheduleType = 1,
                Type = "Scheduled",
                State = "Inactive",
                ExecutionPolicy = "Sequential"
            },
            new ReplicationJobListVm
            {
                Id = "RJ_005",
                CompanyId = "COMP_001",
                Name = "VM Replication Job",
                TemplateId = "TMPL_005",
                TemplateName = "VM Replication Template",
                SolutionTypeId = "ST_005",
                SolutionType = "VM Replication",
                NodeId = "NODE_004",
                NodeName = "VM Replication Node",
                Status = "Running",
                CronExpression = "0 0 0 * * ?",
                GroupPolicyId = "GP_005",
                GroupPolicyName = "VM Replication Policy",
                LastExecutionTime = "2024-01-15T00:00:00Z",
                ExceptionMessage = "",
                ScheduleTime = "00:00",
                IsSchedule = 1,
                ScheduleType = 1,
                Type = "Scheduled",
                State = "Active",
                ExecutionPolicy = "Parallel"
            }
        };

        ReplicationJobDetailVm = new ReplicationJobListVm
        {
            Id = "RJ_001",
            CompanyId = "COMP_001",
            Name = "Database Replication Job",
            TemplateId = "TMPL_001",
            TemplateName = "Database Replication Template",
            SolutionTypeId = "ST_001",
            SolutionType = "Database Replication",
            NodeId = "NODE_001",
            NodeName = "Primary Replication Node",
            Status = "Running",
            CronExpression = "0 0 2 * * ?",
            GroupPolicyId = "GP_001",
            GroupPolicyName = "Database Replication Policy",
            LastExecutionTime = "2024-01-15T02:00:00Z",
            ExceptionMessage = "",
            ScheduleTime = "02:00",
            IsSchedule = 1,
            ScheduleType = 1,
            Type = "Scheduled",
            State = "Active",
            ExecutionPolicy = "Sequential"
        };

        ReplicationJobPaginatedListVm = new PaginatedResult<ReplicationJobListVm>
        {
            Data = ReplicationJobListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        // Configure AutoMapper for ReplicationJob mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ReplicationJobProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoReplicationJobFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize ReplicationJob entity
            fixture.Customize<ReplicationJob>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Replication Job")
                .With(b => b.CompanyId, "COMP_TEST")
                .With(b => b.TemplateId, "TMPL_TEST")
                .With(b => b.TemplateName, "Test Template")
                .With(b => b.SolutionTypeId, "ST_TEST")
                .With(b => b.SolutionType, "Test Solution Type")
                .With(b => b.NodeId, "NODE_TEST")
                .With(b => b.NodeName, "Test Node")
                .With(b => b.Status, "Running")
                .With(b => b.CronExpression, "0 0 * * * ?")
                .With(b => b.GroupPolicyId, "GP_TEST")
                .With(b => b.GroupPolicyName, "Test Group Policy")
                .With(b => b.ScheduleTime, "00:00")
                .With(b => b.IsSchedule, 1)
                .With(b => b.ScheduleType, 1)
                .With(b => b.Type, "Scheduled")
                .With(b => b.State, "Active")
                .With(b => b.ExecutionPolicy, "Sequential"));

            // Customize CreateReplicationJobCommand
            fixture.Customize<CreateReplicationJobCommand>(c => c
                .With(b => b.Name, "New Test Replication Job")
                .With(b => b.CompanyId, "COMP_NEW")
                .With(b => b.TemplateId, "TMPL_NEW")
                .With(b => b.TemplateName, "New Test Template")
                .With(b => b.SolutionTypeId, "ST_NEW")
                .With(b => b.SolutionType, "New Test Solution Type")
                .With(b => b.NodeId, "NODE_NEW")
                .With(b => b.NodeName, "New Test Node")
                .With(b => b.CronExpression, "0 0 2 * * ?")
                .With(b => b.GroupPolicyId, "GP_NEW")
                .With(b => b.GroupPolicyName, "New Test Group Policy")
                .With(b => b.ScheduleTime, "02:00")
                .With(b => b.IsSchedule, 1)
                .With(b => b.ScheduleType, 1)
                .With(b => b.Type, "Scheduled")
                .With(b => b.ExecutionPolicy, "Sequential"));

            // Customize UpdateReplicationJobCommand
            fixture.Customize<UpdateReplicationJobCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Replication Job")
                .With(b => b.TemplateId, "TMPL_UPD")
                .With(b => b.TemplateName, "Updated Test Template")
                .With(b => b.SolutionTypeId, "ST_UPD")
                .With(b => b.SolutionType, "Updated Test Solution Type")
                .With(b => b.NodeId, "NODE_UPD")
                .With(b => b.NodeName, "Updated Test Node")
                .With(b => b.CronExpression, "0 0 3 * * ?")
                .With(b => b.GroupPolicyId, "GP_UPD")
                .With(b => b.GroupPolicyName, "Updated Test Group Policy")
                .With(b => b.ScheduleTime, "03:00")
                .With(b => b.IsSchedule, 1)
                .With(b => b.ScheduleType, 1)
                .With(b => b.Type, "Scheduled")
                .With(b => b.ExecutionPolicy, "Parallel"));

            // Customize DeleteReplicationJobCommand
            fixture.Customize<DeleteReplicationJobCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize UpdateReplicationJobStateCommand
            //fixture.Customize<UpdateReplicationJobStateCommand>(c => c
            //    .With(b => b.Id, Guid.NewGuid().ToString())
            //    .With(b => b.State, "Active"));

            // Customize UpdateReplicationJobStatusCommand
            fixture.Customize<UpdateReplicationJobStatusCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Status, "Running"));

            // Customize Queries
            fixture.Customize<GetReplicationJobDetailQuery>(c => c
                .With(b => b.JobId, Guid.NewGuid().ToString()));

            fixture.Customize<GetReplicationJobPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetReplicationJobNameUniqueQuery>(c => c
                .With(b => b.Name, "Test Replication Job")
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreateReplicationJobResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationJob has been created successfully"));

            fixture.Customize<UpdateReplicationJobResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationJob has been updated successfully"));

            fixture.Customize<DeleteReplicationJobResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "ReplicationJob has been deleted successfully"));

            fixture.Customize<UpdateReplicationJobStateResponse>(c => c
                //.With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationJob state has been updated successfully"));

            fixture.Customize<UpdateReplicationJobStatusResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationJob status has been updated successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "ReplicationJob")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
