﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Create;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Delete;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Commands.Update;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetList;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageSolutionMapping.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageSolutionMappingModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers
{
    public class PageSolutionMappingControllerTests : IClassFixture<PageSolutionMappingFixture>
    {
        private readonly PageSolutionMappingFixture _fixture;
        private readonly PageSolutionMappingController _controller;
        private readonly Mock<IMediator> _mediatorMock;

        public PageSolutionMappingControllerTests(PageSolutionMappingFixture fixture)
        {
            _fixture = fixture;

            var testBuilder = new ControllerTestBuilder<PageSolutionMappingController>();
            _controller = testBuilder.CreateController(
                _ => new PageSolutionMappingController(),
                out _mediatorMock
            );
        }

        [Fact]
        public async Task GetPaginatedPageSolutionMapping_ReturnsOkWithPaginatedResult()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetPageSolutionMappingPaginatedListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_fixture.PaginatedResult);

            var result = await _controller.GetPaginatedPageSolutionMapping(_fixture.PaginatedQuery);

            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var actual = Assert.IsType<PaginatedResult<PageSolutionMappingListVm>>(okResult.Value);
            Assert.Equal(_fixture.PaginatedResult.TotalCount, actual.TotalCount);
        }

        [Fact]
        public async Task GetPageSolutionMappingById_ReturnsDetail()
        {
            var id = Guid.NewGuid().ToString();

            var expectedDetail = _fixture.DetailVm;

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetPageSolutionMappingDetailQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedDetail);

            var result = await _controller.GetPageSolutionMappingById(id);

            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var actual = Assert.IsType<PageSolutionMappingDetailVm>(okResult.Value);
            Assert.Equal(_fixture.DetailVm.Id, actual.Id);
        }

        [Fact]
        public async Task CreatePageSolutionMapping_ReturnsCreatedAtAction()
        {
            var expectedResponse = new CreatePageSolutionMappingResponse { Id = "test-id" };

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<CreatePageSolutionMappingCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            var result = await _controller.CreatePageSolutionMapping(_fixture.CreateCommand);
            
        }

        [Fact]
        public async Task UpdatePageSolutionMapping_ReturnsOkWithResponse()
        {
            var expectedResponse = new UpdatePageSolutionMappingResponse { Id = _fixture.UpdateCommand.Id };

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<UpdatePageSolutionMappingCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);

            var result = await _controller.UpdatePageSolutionMapping(_fixture.UpdateCommand);

        }

        [Fact]
        public async Task IsPageSolutionMappingNameExist_ReturnsOkResult_WithExpectedValue()
        {
            // Arrange
            var pageSolutionName = "TestName";
            var id = Guid.NewGuid().ToString();
            var expectedExists = true;
            
            _mediatorMock
                .Setup(m => m.Send(
                    It.Is<GetPageSolutionMappingNameUniqueQuery>(q => q.Name == pageSolutionName && q.Id == id),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedExists);
            
            // Act
            var result = await _controller.IsPageSolutionMappingNameExist(pageSolutionName, id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var value = Assert.IsType<bool>(okResult.Value);
            Assert.Equal(expectedExists, value);
        }

        [Fact]
        public async Task GetPageSolutionMapping_ReturnsOkResult_WithExpectedList()
        {
            // Arrange
            var expectedList = new List<PageSolutionMappingListVm>
    {
        new PageSolutionMappingListVm
        {
            Id = "1",
            Name = "Mapping1",
            PageBuilderId = "PB1",
            PageBuilderName = "PageBuilder1",
            MonitorType = "TypeA",
            Type = 1,
            TypeName = "TypeNameA",
            SubTypeId = "ST1",
            SubType = "SubTypeA",
            ReplicationTypeId = "RT1",
            ReplicationTypeName = "ReplicationTypeA",
            ReplicationCategoryTypeId = "RCT1",
            ReplicationCategoryType = "ReplicationCategoryA"
        },
        new PageSolutionMappingListVm
        {
            Id = "2",
            Name = "Mapping2",
            PageBuilderId = "PB2",
            PageBuilderName = "PageBuilder2",
            MonitorType = "TypeB",
            Type = 2,
            TypeName = "TypeNameB",
            SubTypeId = "ST2",
            SubType = "SubTypeB",
            ReplicationTypeId = "RT2",
            ReplicationTypeName = "ReplicationTypeB",
            ReplicationCategoryTypeId = "RCT2",
            ReplicationCategoryType = "ReplicationCategoryB"
        }
    };
            
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetPageSolutionMappingListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedList);
            
            // Act
            var result = await _controller.GetPageSolutionMapping();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var list = Assert.IsAssignableFrom<List<PageSolutionMappingListVm>>(okResult.Value);
            Assert.Equal(expectedList.Count, list.Count);
            Assert.Equal(expectedList[0].Id, list[0].Id);
            Assert.Equal(expectedList[1].Name, list[1].Name);
        }

        [Fact]
        public async Task DeletePageSolutionMapping_ReturnsOkResult_WithExpectedResponse()
        {
            // Arrange
            var id = Guid.NewGuid().ToString();
            var expectedResponse = new DeletePageSolutionMappingResponse
            {
                Message = "Deleted successfully",
                
            };

            var mediatorMock = new Mock<IMediator>();
            mediatorMock
                .Setup(m => m.Send(
                    It.Is<DeletePageSolutionMappingCommand>(cmd => cmd.Id == id),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResponse);
            
            // Act
            var result = await _controller.DeletePageSolutionMapping(id);

           
        }
    }
}
