using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Delete;
using ContinuityPatrol.Application.Features.PluginManager.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetList;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNames;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PluginManager.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PluginManagerModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PluginManagerControllerTests : IClassFixture<PluginManagerFixture>
{
    private readonly PluginManagerFixture _pluginManagerFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly PluginManagerController _controller;

    public PluginManagerControllerTests(PluginManagerFixture pluginManagerFixture)
    {
        _pluginManagerFixture = pluginManagerFixture;
        
        var testBuilder = new ControllerTestBuilder<PluginManagerController>();
        _controller = testBuilder.CreateController(
            _ => new PluginManagerController(),
            out _mediatorMock);
    }

    #region GetPluginManagers Tests

    [Fact]
    public async Task GetPluginManagers_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pluginManagerFixture.PluginManagerListVm);

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pluginManagers = Assert.IsAssignableFrom<List<PluginManagerListVm>>(okResult.Value);
        Assert.Equal(5, pluginManagers.Count);
        Assert.All(pluginManagers, pm => Assert.NotNull(pm.Name));
        Assert.All(pluginManagers, pm => Assert.NotNull(pm.Version));
        Assert.All(pluginManagers, pm => Assert.NotNull(pm.Description));
        Assert.All(pluginManagers, pm => Assert.NotNull(pm.Properties));
    }

    [Fact]
    public async Task GetPluginManagers_ReturnsEmptyList_WhenNoPluginManagersExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerListQuery>(), default))
            .ReturnsAsync(new List<PluginManagerListVm>());

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<PluginManagerListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetPluginManagers_ReturnsPluginManagersWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pluginManagerFixture.PluginManagerListVm);

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pluginManagers = Assert.IsAssignableFrom<List<PluginManagerListVm>>(okResult.Value);
        
        // Verify different plugin types
        Assert.Contains(pluginManagers, pm => pm.Name == "Database Backup Plugin");
        Assert.Contains(pluginManagers, pm => pm.Name == "File Replication Plugin");
        Assert.Contains(pluginManagers, pm => pm.Name == "Monitoring Alert Plugin");
        Assert.Contains(pluginManagers, pm => pm.Name == "Security Audit Plugin");
        Assert.Contains(pluginManagers, pm => pm.Name == "Data Migration Plugin");
        
        // Verify different companies
        Assert.Contains(pluginManagers, pm => pm.CompanyId == "COMP_001");
        Assert.Contains(pluginManagers, pm => pm.CompanyId == "COMP_002");
        
        // Verify specific plugin details
        var backupPlugin = pluginManagers.First(pm => pm.Name == "Database Backup Plugin");
        Assert.Equal("2.1.0", backupPlugin.Version);
        Assert.Equal("Automated database backup plugin with compression support", backupPlugin.Description);
        Assert.Contains("backup", backupPlugin.Properties);
        Assert.Contains("postgresql", backupPlugin.Properties);
        Assert.Contains("compression", backupPlugin.Properties);
        
        var replicationPlugin = pluginManagers.First(pm => pm.Name == "File Replication Plugin");
        Assert.Equal("1.5.3", replicationPlugin.Version);
        Assert.Equal("Secure file replication plugin with encryption", replicationPlugin.Description);
        Assert.Contains("replication", replicationPlugin.Properties);
        Assert.Contains("encryption", replicationPlugin.Properties);
        
        var monitoringPlugin = pluginManagers.First(pm => pm.Name == "Monitoring Alert Plugin");
        Assert.Equal("3.0.1", monitoringPlugin.Version);
        Assert.Equal("Multi-channel monitoring and alerting plugin", monitoringPlugin.Description);
        Assert.Contains("monitoring", monitoringPlugin.Properties);
        Assert.Contains("email", monitoringPlugin.Properties);
        Assert.Contains("sms", monitoringPlugin.Properties);
        
        var securityPlugin = pluginManagers.First(pm => pm.Name == "Security Audit Plugin");
        Assert.Equal("4.2.0", securityPlugin.Version);
        Assert.Equal("Comprehensive security auditing and compliance plugin", securityPlugin.Description);
        Assert.Contains("security", securityPlugin.Properties);
        Assert.Contains("SOX", securityPlugin.Properties);
        Assert.Contains("GDPR", securityPlugin.Properties);
        
        var migrationPlugin = pluginManagers.First(pm => pm.Name == "Data Migration Plugin");
        Assert.Equal("1.8.2", migrationPlugin.Version);
        Assert.Equal("Enterprise data migration plugin with validation", migrationPlugin.Description);
        Assert.Contains("migration", migrationPlugin.Properties);
        Assert.Contains("cloud", migrationPlugin.Properties);
        Assert.Contains("validation", migrationPlugin.Properties);
    }

    #endregion

    #region CreatePluginManager Tests

    [Fact]
    public async Task CreatePluginManager_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _pluginManagerFixture.CreatePluginManagerCommand;
        var expectedResponse = _pluginManagerFixture.CreatePluginManagerResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePluginManager(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePluginManagerResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginId);
    }

    [Fact]
    public async Task CreatePluginManager_WithCompletePluginManagerData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreatePluginManagerCommand
        {
            Name = "Custom Enterprise Analytics Plugin",
            CompanyId = "COMP_CUSTOM",
            Properties = "{\"type\": \"analytics\", \"features\": [\"reporting\", \"dashboards\", \"alerts\"], \"license\": \"enterprise\"}",
            Description = "Advanced analytics plugin with enterprise features and reporting capabilities",
            Version = "1.0.0"
        };

        var expectedResponse = new CreatePluginManagerResponse
        {
            PluginId = Guid.NewGuid().ToString(),
            Message = "PluginManager has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePluginManager(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePluginManagerResponse>(createdResult.Value);
        Assert.Equal("PluginManager has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginId);
    }

    #endregion

    #region UpdatePluginManager Tests

    [Fact]
    public async Task UpdatePluginManager_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _pluginManagerFixture.UpdatePluginManagerCommand;
        var expectedResponse = _pluginManagerFixture.UpdatePluginManagerResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePluginManager(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePluginManagerResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginId);
    }

    [Fact]
    public async Task UpdatePluginManager_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _pluginManagerFixture.UpdatePluginManagerCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("PluginManager", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdatePluginManager(command));
    }

    [Fact]
    public async Task UpdatePluginManager_WithUpdatedPluginManagerData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdatePluginManagerCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Enterprise Analytics Plugin",
            Properties = "{\"type\": \"analytics\", \"features\": [\"reporting\", \"dashboards\", \"alerts\", \"ml\"], \"license\": \"enterprise\"}",
            Description = "Enhanced analytics plugin with machine learning capabilities",
            Version = "2.0.0"
        };

        var expectedResponse = new UpdatePluginManagerResponse
        {
            PluginId = command.Id,
            Message = "PluginManager has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePluginManager(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePluginManagerResponse>(okResult.Value);
        Assert.Equal("PluginManager has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.PluginId);
    }

    #endregion

    #region DeletePluginManager Tests

    [Fact]
    public async Task DeletePluginManager_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _pluginManagerFixture.DeletePluginManagerResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePluginManagerCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeletePluginManager(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeletePluginManagerResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeletePluginManager_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePluginManager("invalid-guid"));
    }

    [Fact]
    public async Task DeletePluginManager_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePluginManager(""));
    }

    [Fact]
    public async Task DeletePluginManager_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePluginManagerCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PluginManager", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeletePluginManager(nonExistentId));
    }

    #endregion

    #region GetPluginManagerById Tests

    [Fact]
    public async Task GetPluginManagerById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _pluginManagerFixture.PluginManagerDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetPluginManagerById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<PluginManagerDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Name, returnedDetail.Name);
        Assert.Equal(expectedDetail.Version, returnedDetail.Version);
        Assert.Equal(expectedDetail.Description, returnedDetail.Description);
        Assert.Equal(expectedDetail.Properties, returnedDetail.Properties);
    }

    [Fact]
    public async Task GetPluginManagerById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPluginManagerById("invalid-guid"));
    }

    [Fact]
    public async Task GetPluginManagerById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPluginManagerById(""));
    }

    [Fact]
    public async Task GetPluginManagerById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PluginManager", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPluginManagerById(nonExistentId));
    }

    #endregion

    #region GetPaginatedPluginManager Tests

    [Fact]
    public async Task GetPaginatedPluginManager_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _pluginManagerFixture.GetPluginManagerPaginatedListQuery;
        var expectedResult = _pluginManagerFixture.PluginManagerPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedPluginManager(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PluginManagerListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedPluginManager_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetPluginManagerPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Backup",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<PluginManagerListVm>
        {
            Data = _pluginManagerFixture.PluginManagerListVm.Where(pm => pm.Name.Contains("Backup")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedPluginManager(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PluginManagerListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Backup", returnedResult.Data.First().Name);
    }

    [Fact]
    public async Task GetPaginatedPluginManager_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var query = new GetPluginManagerPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 2,
            SearchString = "",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var paginatedResult = new PaginatedResult<PluginManagerListVm>
        {
            Data = _pluginManagerFixture.PluginManagerListVm.Skip(2).Take(2).ToList(),
            CurrentPage = 2,
            TotalPages = 3,
            TotalCount = 5,
            PageSize = 2
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedPluginManager(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PluginManagerListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.Equal(5, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.PageSize);
        Assert.True(returnedResult.HasPreviousPage);
        Assert.True(returnedResult.HasNextPage);
        Assert.Equal(2, returnedResult.Data.Count);
    }

    #endregion

    #region GetPluginManagerNames Tests

    [Fact]
    public async Task GetPluginManagerNames_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pluginManagerFixture.PluginManagerNameListVm);

        // Act
        var result = await _controller.GetPluginManagerNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pluginManagerNames = Assert.IsAssignableFrom<List<PluginManagerNameVm>>(okResult.Value);
        Assert.Equal(5, pluginManagerNames.Count);
        Assert.All(pluginManagerNames, pmn => Assert.NotNull(pmn.Name));
        Assert.All(pluginManagerNames, pmn => Assert.NotNull(pmn.Id));
    }

    #endregion

    #region IsPluginManagerNameExist Tests

    [Fact]
    public async Task IsPluginManagerNameExist_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Plugin Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerNameUniqueQuery>(q => q.Name == uniqueName && q.PluginId == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsPluginManagerNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsPluginManagerNameExist_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Plugin Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerNameUniqueQuery>(q => q.Name == existingName && q.PluginId == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsPluginManagerNameExist(existingName, id);

    }
    

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsPluginManagerCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
