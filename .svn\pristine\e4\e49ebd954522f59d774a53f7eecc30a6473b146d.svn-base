﻿using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetList;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Infrastructure.Controllers;

namespace ContinuityPatrol.Api.Controllers;

[ApiVersion("6")]
    
public class DashboardViewLogController : CommonBaseController
{
    [HttpPost]
    [Authorize(Policy = Permissions.Dashboard.Create)]
    public async Task<ActionResult<CreateDashboardViewLogResponse>> CreateDashboardViewLog(
        [FromBody] CreateDashboardViewLogCommand createDashboardViewLogCommand)
    {
        Logger.LogDebug($"Create DashboardViewLog Log '{createDashboardViewLogCommand.InfraObjectName}'");

        ClearDataCache();

        return CreatedAtAction(nameof(CreateDashboardViewLog), await Mediator.Send(createDashboardViewLogCommand));
    }

    [HttpPut]
    [Authorize(Policy = Permissions.Dashboard.Edit)]
    public async Task<ActionResult<UpdateDashboardViewLogResponse>> UpdateDashboardViewLog(
        [FromBody] UpdateDashboardViewLogCommand updateDashboardViewLogCommand)
    {
        Logger.LogDebug($"Update DashboardViewLog Log '{updateDashboardViewLogCommand.InfraObjectName}'");

        ClearDataCache();

        return Ok(await Mediator.Send(updateDashboardViewLogCommand));
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = Permissions.Dashboard.Delete)]
    public async Task<ActionResult<DeleteDashboardViewLogResponse>> DeleteDashboardViewLog(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardViewLog Log Id");

        Logger.LogDebug($"Delete DashboardViewLog Log Details by Id '{id}'");

        ClearDataCache();

        return Ok(await Mediator.Send(new DeleteDashboardViewLogCommand { Id = id }));
    }


    [HttpGet("{id}", Name = "GetDashboardViewLog")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DashboardViewLogDetailVm>> GetDashboardViewLogById(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "DashboardViewLog Log Id");

        Logger.LogDebug($"Get DashboardViewLog Log Detail by Id '{id}'");

        return Ok(await Mediator.Send(new GetDashboardViewLogDetailQuery { Id = id }));
    }

    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<DashboardViewLogListVm>>> GetDashboardViewLogs()
    {
        Logger.LogDebug("Get All DashboardViewLog");

        return Ok(await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllDashboardViewLogCacheKey + LoggedInUserService.CompanyId, () => Mediator.Send(new GetDashboardViewLogListQuery()), CacheExpiry));
    }

    [Route("paginated-list")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<PaginatedResult<DashboardViewLogListVm>>> GetPaginatedDashboardViewLogs(
        [FromQuery] GetDashboardViewLogPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in DashboardViewLog Log Paginated List");

        return Ok(await Mediator.Send(query));
    }


    [HttpGet]
    [Route("datalagstatus-report")]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<DataLagStatusReport>> GetDatalagStatusReport()
    {
        Logger.LogDebug("Get All DashboardViewLog");

        return Ok(await Mediator.Send(new GetDataLagStatusReportQuery()));
    }

    [Route("infraObjectId")]
    [HttpGet]
    [Authorize(Policy = Permissions.Dashboard.View)]
    public async Task<ActionResult<List<InfraObjectHealthScore>>>GetDashboardViewLogByInfraObjectId(string infraObjectId)
    {
        Logger.LogDebug($"Get DashboardViewLog by infraObjectId'{infraObjectId}'");

        return Ok(await Mediator.Send(new GetDashboardViewLogByInfraObjectIdQuery { InfraObjectId = infraObjectId }));
    }

    [NonAction]
    public override void ClearDataCache()
    {
        string[] cacheKeys = { ApplicationConstants.Cache.AllDashboardViewLogCacheKey + LoggedInUserService.CompanyId };

        ClearCache(cacheKeys);
    }
}