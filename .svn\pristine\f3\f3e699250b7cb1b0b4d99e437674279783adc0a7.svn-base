using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.Features.Report.Commands.Delete;
using ContinuityPatrol.Application.Features.Report.Commands.Update;
using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetList;
using ContinuityPatrol.Application.Features.Report.Queries.GetNames;
using ContinuityPatrol.Application.Features.Report.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Report.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Domain.ViewModels.BulkImportOperationModel;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Components.Forms;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ReportsControllerTests : IClassFixture<ReportsFixture>
{
    private readonly ReportsFixture _reportsFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ReportsController _controller;

    public ReportsControllerTests(ReportsFixture reportsFixture)
    {
        _reportsFixture = reportsFixture;
        
        var testBuilder = new ControllerTestBuilder<ReportsController>();
        _controller = testBuilder.CreateController(
            _ => new ReportsController(),
            out _mediatorMock);
    }

    #region GetReports Tests

    [Fact]
    public async Task GetReports_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReportListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_reportsFixture.ReportListVm);

        // Act
        var result = await _controller.GetReports();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var reports = Assert.IsAssignableFrom<List<ReportListVm>>(okResult.Value);
        Assert.Equal(5, reports.Count);
        Assert.All(reports, r => Assert.NotNull(r.Name));
        Assert.All(reports, r => Assert.NotNull(r.Description));
        Assert.All(reports, r => Assert.NotNull(r.FilterColumn));
        Assert.All(reports, r => Assert.NotNull(r.HeaderColumn));
    }

    [Fact]
    public async Task GetReports_ReturnsEmptyList_WhenNoReportsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReportListQuery>(), default))
            .ReturnsAsync(new List<ReportListVm>());

        // Act
        var result = await _controller.GetReports();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<ReportListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetReports_ReturnsReportsWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReportListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_reportsFixture.ReportListVm);

        // Act
        var result = await _controller.GetReports();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var reports = Assert.IsAssignableFrom<List<ReportListVm>>(okResult.Value);
        
        // Verify different report types
        Assert.Contains(reports, r => r.Name == "Business Service Summary Report");
        Assert.Contains(reports, r => r.Name == "Infrastructure Object Report");
        Assert.Contains(reports, r => r.Name == "RTO Performance Report");
        Assert.Contains(reports, r => r.Name == "License Utilization Report");
        Assert.Contains(reports, r => r.Name == "User Activity Report");
        
        // Verify different designs
        Assert.Contains(reports, r => r.Design == "Summary");
        Assert.Contains(reports, r => r.Design == "Detailed");
        Assert.Contains(reports, r => r.Design == "Performance");
        Assert.Contains(reports, r => r.Design == "Utilization");
        Assert.Contains(reports, r => r.Design == "Audit");
        
        // Verify different data sets
        Assert.Contains(reports, r => r.DataSet == "BusinessServices");
        Assert.Contains(reports, r => r.DataSet == "InfraObjects");
        Assert.Contains(reports, r => r.DataSet == "WorkflowExecutions");
        Assert.Contains(reports, r => r.DataSet == "Licenses");
        Assert.Contains(reports, r => r.DataSet == "UserActivities");
        
        // Verify filter and header columns are properly configured
        var businessServiceReport = reports.First(r => r.Name == "Business Service Summary Report");
        Assert.Contains("BusinessServiceName", businessServiceReport.FilterColumn);
        Assert.Contains("Status", businessServiceReport.FilterColumn);
        Assert.Contains("Service Name", businessServiceReport.HeaderColumn);
        Assert.Contains("Current Status", businessServiceReport.HeaderColumn);
        
        var infraReport = reports.First(r => r.Name == "Infrastructure Object Report");
        Assert.Contains("InfraObjectName", infraReport.FilterColumn);
        Assert.Contains("Type", infraReport.FilterColumn);
        Assert.Contains("Object Name", infraReport.HeaderColumn);
        Assert.Contains("Type", infraReport.HeaderColumn);
        
        var rtoReport = reports.First(r => r.Name == "RTO Performance Report");
        Assert.Contains("WorkflowName", rtoReport.FilterColumn);
        Assert.Contains("ExecutionTime", rtoReport.FilterColumn);
        Assert.Contains("Workflow", rtoReport.HeaderColumn);
        Assert.Contains("Execution Time", rtoReport.HeaderColumn);
        
        var licenseReport = reports.First(r => r.Name == "License Utilization Report");
        Assert.Contains("LicenseType", licenseReport.FilterColumn);
        Assert.Contains("Used", licenseReport.FilterColumn);
        Assert.Contains("License Type", licenseReport.HeaderColumn);
        Assert.Contains("Used Count", licenseReport.HeaderColumn);
        
        var userActivityReport = reports.First(r => r.Name == "User Activity Report");
        Assert.Contains("UserName", userActivityReport.FilterColumn);
        Assert.Contains("Activity", userActivityReport.FilterColumn);
        Assert.Contains("User", userActivityReport.HeaderColumn);
        Assert.Contains("Activity Type", userActivityReport.HeaderColumn);
    }

    #endregion

    #region CreateReport Tests

    [Fact]
    public async Task CreateReport_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _reportsFixture.CreateReportCommand;
        var expectedResponse = _reportsFixture.CreateReportResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReport(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReportResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
    }

    [Fact]
    public async Task CreateReport_WithCompleteReportData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateReportCommand
        {
            Name = "Custom Performance Report",
            Description = "Custom report for monitoring system performance and availability metrics",
            FilterColumn = "SystemName,PerformanceMetric,Date,Status,Threshold",
            HeaderColumn = "System Name,Metric Type,Date/Time,Status,Threshold Value,Current Value",
            Design = "Performance Dashboard with real-time charts and alerts",
            DataSet = "PerformanceMetrics,SystemLogs,AlertHistory"
        };

        var expectedResponse = new CreateReportResponse
        {
           
            Message = "Report has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReport(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReportResponse>(createdResult.Value);
        Assert.Equal("Report has been created successfully", returnedResponse.Message);
    }

    #endregion

    #region UpdateReport Tests

    [Fact]
    public async Task UpdateReport_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _reportsFixture.UpdateReportCommand;
        var expectedResponse = _reportsFixture.UpdateReportResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReport(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReportResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdateReport_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _reportsFixture.UpdateReportCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("Report", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateReport(command));
    }

    #endregion

    #region DeleteReport Tests

    [Fact]
    public async Task DeleteReport_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _reportsFixture.DeleteReportResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReportCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteReport(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteReportResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }


    [Fact]
    public async Task DeleteReport_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReportCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("Report", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteReport(nonExistentId));
    }

    #endregion

    #region GetReportById Tests

    [Fact]
    public async Task GetReportById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _reportsFixture.ReportDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReportDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetReportById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<ReportDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Name, returnedDetail.Name);
        Assert.Equal(expectedDetail.Description, returnedDetail.Description);
        Assert.Equal(expectedDetail.FilterColumn, returnedDetail.FilterColumn);
        Assert.Equal(expectedDetail.HeaderColumn, returnedDetail.HeaderColumn);
        Assert.Equal(expectedDetail.Design, returnedDetail.Design);
        Assert.Equal(expectedDetail.DataSet, returnedDetail.DataSet);

        // Verify detailed content
        Assert.Contains("Comprehensive summary", returnedDetail.Description);
        Assert.Contains("drill-down capabilities", returnedDetail.Design);
        Assert.Contains("BusinessServices,InfraObjects", returnedDetail.DataSet);
    }


    [Fact]
    public async Task GetReportById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReportDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("Report", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetReportById(nonExistentId));
    }

    #endregion

    #region GetPaginatedReports Tests

    [Fact]
    public async Task GetPaginatedReports_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _reportsFixture.GetReportPaginatedListQuery;
        var expectedResult = _reportsFixture.ReportPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedReports(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReportListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedReports_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetReportPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Business",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<ReportListVm>
        {
            Data = _reportsFixture.ReportListVm.Where(r => r.Name.Contains("Business")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedReports(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReportListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Business", returnedResult.Data.First().Name);
    }

    #endregion

    #region GetReportNames Tests

    [Fact]
    public async Task GetReportNames_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReportNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_reportsFixture.ReportNameListVm);

        // Act
        var result = await _controller.GetReportNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var reportNames = Assert.IsAssignableFrom<List<GetReportNameVm>>(okResult.Value);
        Assert.Equal(5, reportNames.Count);
        Assert.All(reportNames, rn => Assert.NotNull(rn.Name));
        Assert.All(reportNames, rn => Assert.NotNull(rn.Id));
    }

    #endregion

    #region IsReportNameUnique Tests

    [Fact]
    public async Task IsReportNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Report Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReportNameUniqueQuery>(q => q.ReportName == uniqueName && q.ReportId == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsReportNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsReportNameUnique_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Report Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReportNameUniqueQuery>(q => q.ReportName == existingName && q.ReportId == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsReportNameExist(existingName, id);

    }

    #endregion

    #region Specific Report Tests

    [Fact]
    public async Task GetRTOReport_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedReport = _reportsFixture.RTOReports;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetRtoReportQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetRtoReportByWorkflowOperationId(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<RTOReports>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);
        Assert.Equal(expectedReport.RtoReportVm.WorkflowName, returnedReport.RtoReportVm.WorkflowName);
        Assert.Equal(expectedReport.RtoReportVm.TotalProfilesExecutedCount, returnedReport.RtoReportVm.TotalProfilesExecutedCount);
    }

    [Fact]
    public async Task GetLicenseReport_ReturnsOkResult()
    {
        // Arrange
        var expectedReport = _reportsFixture.LicenseReport;
        var id = Guid.NewGuid().ToString();
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetLicenseReportQuery>(), default))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetLicenseReportById(id);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<LicenseReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);
        Assert.Equal(expectedReport.LicenseType, returnedReport.LicenseType);
        Assert.Equal(expectedReport.LicenseReportVms.Count, returnedReport.LicenseReportVms.Count);
    }

    [Fact]
    public async Task GetBusinessServiceSummaryReport_ReturnsOkResult()
    {
        // Arrange
        var expectedReport = new BusinessServiceSummaryReport
        {
            ReportGeneratedBy = "System Administrator",
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            BusinessServiceSummaryReportVms = new List<BusinessServiceSummaryReportVm>()
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetBusinessServiceSummaryReportQuery>(), default))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetBusinessServiceSummaryReport();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<BusinessServiceSummaryReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsReportsCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
    [Fact]
    public async Task GetRpoSlaReportByInfraObjectId_ReturnsExpectedResult()
    {
        // Arrange
        var infraObjectId = Guid.NewGuid().ToString();
        var type = "VM";
        var reportStartDate = "2024-01-01";
        var reportEndDate = "2024-01-31";
        var expectedResult = new { Success = true, Data = "ReportData" };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetRPOSLAReportQuery>(q =>
                    q.InfraObjectId == infraObjectId &&
                    q.Type == type &&
                    q.ReportStartDate == reportStartDate &&
                    q.ReportEndDate == reportEndDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        var result = await _controller.GetRpoSlaReportByInfraObjectId(infraObjectId, type, reportStartDate, reportEndDate);

        
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Equal(expectedResult, okResult.Value);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetRPOSLAReportQuery>(q =>
                q.InfraObjectId == infraObjectId &&
                q.Type == type &&
                q.ReportStartDate == reportStartDate &&
                q.ReportEndDate == reportEndDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetRunbookReportByWorkflowId_ReturnsExpectedResult()
    {
        // Arrange
        var workflowId = Guid.NewGuid().ToString();
        var expectedVm = new GetRunBookReportVm
        {
            WorkflowId = workflowId,
            WorkflowName = "Test Workflow",
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            GetRunBookReportListVms = new List<GetRunBookReportListVm>
        {
            new GetRunBookReportListVm { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetRunBookReportQuery>(q => q.WorkflowId == workflowId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await _controller.GetRunbookReportByWorkflowId(workflowId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedVm = Assert.IsType<GetRunBookReportVm>(okResult.Value);
        Assert.Equal(expectedVm.WorkflowId, returnedVm.WorkflowId);
        Assert.Equal(expectedVm.WorkflowName, returnedVm.WorkflowName);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetRunBookReportQuery>(q => q.WorkflowId == workflowId),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetUserActivityReport_ReturnsExpectedResult()
    {
        // Arrange
        var userId = "user-123";
        var createDate = "2024-07-01";
        var lastModifiedDate = "2024-07-22";
        var expectedReport = new UserActivityReport
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            UserName = "Test User",
            ActiveStartDate = createDate,
            ActiveEndDate = lastModifiedDate,
            UserActivityReportVms = new List<UserActivityReportVm>
        {
            new UserActivityReportVm { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<UserActivityReportQuery>(q =>
                    q.UserId == userId &&
                    q.CreatedDate == createDate &&
                    q.LastModifiedDate == lastModifiedDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetUserActivityReport(userId, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<UserActivityReport>(okResult.Value);
        Assert.Equal(expectedReport.UserName, returnedReport.UserName);
        Assert.Equal(expectedReport.ActiveStartDate, returnedReport.ActiveStartDate);

        _mediatorMock.Verify(m => m.Send(
            It.Is<UserActivityReportQuery>(q =>
                q.UserId == userId &&
                q.CreatedDate == createDate &&
                q.LastModifiedDate == lastModifiedDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetAirGapReport_ReturnsExpectedResult()
    {
        // Arrange
        var airGapId = "AG-001";
        var expectedVm = new AirGapLogReportVm
        {
            ReportGenerated = "2024-07-22",
            CyberAirGapLogLists = new List<AirGapListReportVm>
        {
            new AirGapListReportVm { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetAirGapQuery>(q => q.Id == airGapId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await _controller.GetAirGapReport(airGapId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedVm = Assert.IsType<AirGapLogReportVm>(okResult.Value);
        Assert.Equal(expectedVm.ReportGenerated, returnedVm.ReportGenerated);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetAirGapQuery>(q => q.Id == airGapId),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetBulkImportReport_ReturnsExpectedResult()
    {
        // Arrange
        var operationId = "OP-123";
        var expectedVm = new GetBulkImportReportVm
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            SuccessInfra = 2,
            RunningInfra = 1,
            FailureInfra = 1,
            TotalInfra = 4,
            BulkImportOperationGroupListVms = new List<BulkImportOperationGroupList>
        {
            new BulkImportOperationGroupList { Id = "1", InfraObjectName = "Infra1", Status = "Success" },
            new BulkImportOperationGroupList { Id = "2", InfraObjectName = "Infra2", Status = "Failure" }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetBulkImportReportListQuery>(q => q.Id == operationId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);

        // Act
        var result = await _controller.GetBulkImportReport(operationId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedVm = Assert.IsType<GetBulkImportReportVm>(okResult.Value);
        Assert.Equal(expectedVm.ReportGeneratedBy, returnedVm.ReportGeneratedBy);
        Assert.Equal(expectedVm.TotalInfra, returnedVm.TotalInfra);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetBulkImportReportListQuery>(q => q.Id == operationId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId_ReturnsExpectedResult()
    {
        // Arrange
        var businessServiceId = "BS-001";
        var infraObjectId = "INFRA-001";
        var createDate = "2024-07-01";
        var lastModifiedDate = "2024-07-22";
        var expectedVm = new GetRpoSlaDeviationReportVm
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            REPOSLADeviationReportDataName = "DeviationReport",
            ActiveStartDate = createDate,
            ActiveEndDate = lastModifiedDate,
            RpoSlaDeviationReportByStartTimeAndEndTimeVms = new List<GetRpoSlaDeviationReportByStartTimeAndEndTimeVm>
        {
            new GetRpoSlaDeviationReportByStartTimeAndEndTimeVm { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery>(q =>
                    q.BusinessServiceId == businessServiceId &&
                    q.InfraObjectId == infraObjectId &&
                    q.CreatedDate == createDate &&
                    q.LastModifiedDate == lastModifiedDate),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);


        // Act
        var result = await _controller.GetByStartTimeEndTimeAndBusinessServiceIdAndInfraObjectId(
            businessServiceId, infraObjectId, createDate, lastModifiedDate);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedVm = Assert.IsType<GetRpoSlaDeviationReportVm>(okResult.Value);
        Assert.Equal(expectedVm.ReportGeneratedBy, returnedVm.ReportGeneratedBy);
        Assert.Equal(expectedVm.ActiveStartDate, returnedVm.ActiveStartDate);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetRpoSlaDeviationReportByStartTimeAndEndTimeQuery>(q =>
                q.BusinessServiceId == businessServiceId &&
                q.InfraObjectId == infraObjectId &&
                q.CreatedDate == createDate &&
                q.LastModifiedDate == lastModifiedDate),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetDrDrillReportByWorkflowOperationId_ReturnsExpectedResult()
    {
        // Arrange
        var workflowOperationId = "WO-123";
        var runMode = "Auto";
        var expectedReport = new DrDrillReport
        {
            IsCustom = false,
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            WorkflowOperationDrDrillReportVm = new WorkflowOperationDrDrillReportVm
            {
                WorkflowOperationId = workflowOperationId
               
            }
        };

     
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetWorkflowOperationDrDrillReportQuery>(q =>
                    q.Id == workflowOperationId && q.RunMode == runMode),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);


        // Act
        var result = await _controller.GetDrDrillReportByWorkflowOperationId(workflowOperationId, runMode);
        
    }
    [Fact]
    public async Task GetDriftReport_ReturnsExpectedResult()
    {
        // Arrange
        var expectedVm = new DriftReportVm
        {
            ReportGeneratedBy = "admin",
            ReportGeneratedTime = "2024-07-22T10:00:00Z",
            DriftEventReportVm = new List<DriftEventReportVm>
        {
            new DriftEventReportVm { /* set properties as needed */ }
        },
            DriftResourceSummaryVms = new List<DriftResourceVm>
        {
            new DriftResourceVm { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDriftReportQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedVm);


        // Act
        var result = await _controller.GetDriftReport();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedVm = Assert.IsType<DriftReportVm>(okResult.Value);
        Assert.Equal(expectedVm.ReportGeneratedBy, returnedVm.ReportGeneratedBy);
        Assert.Equal(expectedVm.ReportGeneratedTime, returnedVm.ReportGeneratedTime);

        _mediatorMock.Verify(m => m.Send(It.IsAny<GetDriftReportQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetDrReadyExecutionLogReport_ReturnsExpectedResult()
    {
        // Arrange
        var businessServiceId = "BS-001";
        var startTime = "2024-07-01T00:00:00Z";
        var endTime = "2024-07-22T23:59:59Z";
        var expectedReport = new DRReadyExecutionReport
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            DRReadyExecutionReportDataName = "DRReadyReport",
            ActiveStartDate = startTime,
            ActiveEndDate = endTime,
            DRReadyExecutionReportVm = new List<DRReadyExecutionReportVm>
        {
            new DRReadyExecutionReportVm { /* set properties as needed */ }
        }
        };

       
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetDRReadyExecutionReportQuery>(q =>
                    q.BusinessServiceId == businessServiceId &&
                    q.StartTime == startTime &&
                    q.EndTime == endTime),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetDrReadyExecutionLogReport(businessServiceId, startTime, endTime);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<DRReadyExecutionReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);
        Assert.Equal(expectedReport.ActiveStartDate, returnedReport.ActiveStartDate);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetDRReadyExecutionReportQuery>(q =>
                q.BusinessServiceId == businessServiceId &&
                q.StartTime == startTime &&
                q.EndTime == endTime),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetDrReadyStatusReportByBusinessServiceId_ReturnsExpectedResult()
    {
        // Arrange
        var businessServiceId = "BS-001";
        var expectedReport = new DrReadyStatusReport
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            BusinessServiceName = "Business Service 1",
            DrReadyStatusForDrReadyReportVms = new List<DrReadyStatusForDrReadyReportVm>
        {
            new DrReadyStatusForDrReadyReportVm { /* set properties as needed */ }
        },
            InfraCountList = new List<InfraObjectCountList>
        {
            new InfraObjectCountList { /* set properties as needed */ }
        }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<DRReadyStatusForDRReadyReportQuery>(q => q.BusinessServiceId == businessServiceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetDrReadyStatusReportByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<DrReadyStatusReport>(okResult.Value);
        Assert.Equal(expectedReport.BusinessServiceName, returnedReport.BusinessServiceName);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);

        _mediatorMock.Verify(m => m.Send(
            It.Is<DRReadyStatusForDRReadyReportQuery>(q => q.BusinessServiceId == businessServiceId),
            It.IsAny<CancellationToken>()), Times.Once);
    }
    [Fact]
    public async Task GetInfraObjectConfigurationReport_ReturnsExpectedResult()
    {
        // Arrange
        var infraObjectId = "INFRA-001";
        var expectedReport = new InfraReport
        {
            ReportGeneratedBy = "admin",
            Date = "2024-07-22",
            InfraObjectConfigurationReportVm = new InfraObjectConfigurationReportVm
            {
                // Set properties as needed for your test
            }
        };

        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetInfraObjectConfigurationReportQuery>(q => q.InfraObjectId == infraObjectId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);

        // Act
        var result = await _controller.GetInfraObjectConfigurationReport(infraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReport = Assert.IsType<InfraReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, returnedReport.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, returnedReport.Date);

        _mediatorMock.Verify(m => m.Send(
            It.Is<GetInfraObjectConfigurationReportQuery>(q => q.InfraObjectId == infraObjectId),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetLicenseReportByBusinessServiceId_ReturnsOkResult_WithReport()
    {
        // Arrange
        var businessServiceId = "test-id";
        var expectedReport = new LicenseReportByBusinessServiceReport
        {
            ReportGeneratedBy = "TestUser",
            Date = "2025-07-22",
            LicenseType = "Enterprise",
            LicenseReportByBusinessServiceVms = new List<LicenseReportByBusinessServiceVm>()
        };
        
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetLicenseReportByBusinessServiceQuery>(q => q.BusinessServiceId == businessServiceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);
        
        // Act
        var result = await _controller.GetLicenseReportByBusinessServiceId(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var report = Assert.IsType<LicenseReportByBusinessServiceReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, report.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, report.Date);
        Assert.Equal(expectedReport.LicenseType, report.LicenseType);
    }

    [Fact]
    public async Task GetInfraObjectSummaryReport_ReturnsOkResult_WithReport()
    {
        // Arrange
        var businessServiceId = "test-service-id";
        var expectedReport = new InfraObjectSummaryReport
        {
            ReportGeneratedBy = "TestUser",
            Date = "2025-07-22",
            InfraSummaryReportDataName = "TestData",
            InfraObjectSummaryReportVms = new List<InfraObjectSummaryReportVm>()
        };

       
        _mediatorMock
            .Setup(m => m.Send(
                It.Is<GetInfraObjectSummaryReportQuery>(q => q.BusinessServiceId == businessServiceId),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedReport);
        
        // Act
        var result = await _controller.GetInfraObjectSummaryReport(businessServiceId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var report = Assert.IsType<InfraObjectSummaryReport>(okResult.Value);
        Assert.Equal(expectedReport.ReportGeneratedBy, report.ReportGeneratedBy);
        Assert.Equal(expectedReport.Date, report.Date);
        Assert.Equal(expectedReport.InfraSummaryReportDataName, report.InfraSummaryReportDataName);
        Assert.Equal(expectedReport.InfraObjectSummaryReportVms, report.InfraObjectSummaryReportVms);
    }
}

