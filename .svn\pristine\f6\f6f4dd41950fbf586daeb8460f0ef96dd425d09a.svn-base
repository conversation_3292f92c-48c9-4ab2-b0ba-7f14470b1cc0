using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class EmployeeFixture : IDisposable
{
    public List<EmployeeListVm> EmployeeListVm { get; }
    public PaginatedResult<EmployeeListVm> PaginatedEmployeeListVm { get; }
    public EmployeeDetailVm EmployeeDetailVm { get; }
    public CreateEmployeeCommand CreateEmployeeCommand { get; }
    public UpdateEmployeeCommand UpdateEmployeeCommand { get; }
    public GetEmployeePaginatedListQuery GetEmployeePaginatedListQuery { get; }

    public EmployeeFixture()
    {
        var fixture = new Fixture();

        EmployeeListVm = fixture.Create<List<EmployeeListVm>>();
        PaginatedEmployeeListVm = fixture.Create<PaginatedResult<EmployeeListVm>>();
        EmployeeDetailVm = fixture.Create<EmployeeDetailVm>();
        CreateEmployeeCommand = fixture.Create<CreateEmployeeCommand>();
        UpdateEmployeeCommand = fixture.Create<UpdateEmployeeCommand>();
        GetEmployeePaginatedListQuery = fixture.Create<GetEmployeePaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
