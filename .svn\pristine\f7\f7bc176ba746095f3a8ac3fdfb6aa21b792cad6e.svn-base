using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.PostgresMonitorStatus.Queries.GetPostgresMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PostgresMonitorStatusControllerTests : IClassFixture<PostgresMonitorStatusFixture>
{
    private readonly PostgresMonitorStatusFixture _postgresMonitorStatusFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly PostgresMonitorStatusController _controller;

    public PostgresMonitorStatusControllerTests(PostgresMonitorStatusFixture postgresMonitorStatusFixture)
    {
        _postgresMonitorStatusFixture = postgresMonitorStatusFixture;
        
        var testBuilder = new ControllerTestBuilder<PostgresMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new PostgresMonitorStatusController(),
            out _mediatorMock);
    }

    #region CreatePostgresMonitorStatus Tests

    [Fact]
    public async Task CreatePostgresMonitorStatus_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusCommand;
        var expectedResponse = _postgresMonitorStatusFixture.CreatePostgresMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePostgresMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePostgresMonitorStatusResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreatePostgresMonitorStatus_WithCompletePostgresMonitorStatusData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreatePostgresMonitorStatusCommand
        {
            Type = "POSTGRES_STREAMING_REPLICATION",
            InfraObjectId = "INFRA_CUSTOM",
            InfraObjectName = "Custom PostgreSQL Server",
            WorkflowId = "WF_CUSTOM",
            WorkflowName = "Custom PostgreSQL Streaming Replication",
            ConfiguredRPO = "30",
            DataLagValue = "5",
            Properties = "{\"status\": \"healthy\", \"role\": \"primary\", \"replication_state\": \"streaming\"}",
            Threshold = "60"
        };

        var expectedResponse = new CreatePostgresMonitorStatusResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "PostgresMonitorStatus has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePostgresMonitorStatus(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePostgresMonitorStatusResponse>(createdResult.Value);
        Assert.Equal("PostgresMonitorStatus has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    #endregion

    #region UpdatePostgresMonitorStatus Tests

    [Fact]
    public async Task UpdatePostgresMonitorStatus_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _postgresMonitorStatusFixture.UpdatePostgresMonitorStatusCommand;
        var expectedResponse = _postgresMonitorStatusFixture.UpdatePostgresMonitorStatusResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePostgresMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePostgresMonitorStatusResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdatePostgresMonitorStatus_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _postgresMonitorStatusFixture.UpdatePostgresMonitorStatusCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("PostgresMonitorStatus", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdatePostgresMonitorStatus(command));
    }

    [Fact]
    public async Task UpdatePostgresMonitorStatus_WithUpdatedPostgresMonitorStatusData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdatePostgresMonitorStatusCommand
        {
            Id = Guid.NewGuid().ToString(),
            Type = "POSTGRES_LOGICAL_REPLICATION",
            InfraObjectId = "INFRA_UPD",
            InfraObjectName = "Updated PostgreSQL Server",
            WorkflowId = "WF_UPD",
            WorkflowName = "Updated PostgreSQL Logical Replication",
            ConfiguredRPO = "60",
            DataLagValue = "10",
            Properties = "{\"status\": \"healthy\", \"role\": \"subscriber\", \"replication_state\": \"logical\"}",
            Threshold = "120"
        };

        var expectedResponse = new UpdatePostgresMonitorStatusResponse
        {
            Id = command.Id,
            Message = "PostgresMonitorStatus has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePostgresMonitorStatus(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePostgresMonitorStatusResponse>(okResult.Value);
        Assert.Equal("PostgresMonitorStatus has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.Id);
    }

    #endregion

    #region GetAllPostgresMonitorStatus Tests

    [Fact]
    public async Task GetAllPostgresMonitorStatus_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_postgresMonitorStatusFixture.PostgresMonitorStatusListVm);

        // Act
        var result = await _controller.GetAllPostgresMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var postgresMonitorStatuses = Assert.IsAssignableFrom<List<PostgresMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(5, postgresMonitorStatuses.Count);
        Assert.All(postgresMonitorStatuses, pms => Assert.NotNull(pms.Type));
        Assert.All(postgresMonitorStatuses, pms => Assert.NotNull(pms.InfraObjectName));
        Assert.All(postgresMonitorStatuses, pms => Assert.NotNull(pms.WorkflowName));
    }

    [Fact]
    public async Task GetAllPostgresMonitorStatus_ReturnsEmptyList_WhenNoPostgresMonitorStatusesExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorStatusListQuery>(), default))
            .ReturnsAsync(new List<PostgresMonitorStatusListVm>());

        // Act
        var result = await _controller.GetAllPostgresMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<PostgresMonitorStatusListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetAllPostgresMonitorStatus_ReturnsPostgresMonitorStatusesWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_postgresMonitorStatusFixture.PostgresMonitorStatusListVm);

        // Act
        var result = await _controller.GetAllPostgresMonitorStatus();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var postgresMonitorStatuses = Assert.IsAssignableFrom<List<PostgresMonitorStatusListVm>>(okResult.Value);
        
        // Verify different PostgreSQL replication types
        Assert.Contains(postgresMonitorStatuses, pms => pms.Type == "POSTGRES_STREAMING_REPLICATION");
        Assert.Contains(postgresMonitorStatuses, pms => pms.Type == "POSTGRES_LOGICAL_REPLICATION");
        Assert.Contains(postgresMonitorStatuses, pms => pms.Type == "POSTGRES_HOT_STANDBY");
        Assert.Contains(postgresMonitorStatuses, pms => pms.Type == "POSTGRES_WARM_STANDBY");
        Assert.Contains(postgresMonitorStatuses, pms => pms.Type == "POSTGRES_ARCHIVE_RECOVERY");
        
        // Verify different infrastructure objects
        Assert.Contains(postgresMonitorStatuses, pms => pms.InfraObjectName == "PostgreSQL Primary Server");
        Assert.Contains(postgresMonitorStatuses, pms => pms.InfraObjectName == "PostgreSQL Secondary Server");
        Assert.Contains(postgresMonitorStatuses, pms => pms.InfraObjectName == "PostgreSQL Hot Standby Server");
        Assert.Contains(postgresMonitorStatuses, pms => pms.InfraObjectName == "PostgreSQL Warm Standby Server");
        Assert.Contains(postgresMonitorStatuses, pms => pms.InfraObjectName == "PostgreSQL Archive Recovery Server");
        
        // Verify different workflows
        Assert.Contains(postgresMonitorStatuses, pms => pms.WorkflowName == "PostgreSQL Streaming Replication");
        Assert.Contains(postgresMonitorStatuses, pms => pms.WorkflowName == "PostgreSQL Logical Replication");
        Assert.Contains(postgresMonitorStatuses, pms => pms.WorkflowName == "PostgreSQL Hot Standby");
        Assert.Contains(postgresMonitorStatuses, pms => pms.WorkflowName == "PostgreSQL Warm Standby");
        Assert.Contains(postgresMonitorStatuses, pms => pms.WorkflowName == "PostgreSQL Archive Recovery");
        
        // Verify specific monitor status details
        var streamingReplication = postgresMonitorStatuses.First(pms => pms.Type == "POSTGRES_STREAMING_REPLICATION");
        Assert.Equal("30", streamingReplication.ConfiguredRPO);
        Assert.Equal("5", streamingReplication.DataLagValue);
        Assert.Equal("60", streamingReplication.Threshold);
        Assert.Contains("primary", streamingReplication.Properties);
        Assert.Contains("streaming", streamingReplication.Properties);
        
        var logicalReplication = postgresMonitorStatuses.First(pms => pms.Type == "POSTGRES_LOGICAL_REPLICATION");
        Assert.Equal("60", logicalReplication.ConfiguredRPO);
        Assert.Equal("10", logicalReplication.DataLagValue);
        Assert.Equal("120", logicalReplication.Threshold);
        Assert.Contains("subscriber", logicalReplication.Properties);
        Assert.Contains("logical", logicalReplication.Properties);
        
        var hotStandby = postgresMonitorStatuses.First(pms => pms.Type == "POSTGRES_HOT_STANDBY");
        Assert.Equal("15", hotStandby.ConfiguredRPO);
        Assert.Equal("3", hotStandby.DataLagValue);
        Assert.Equal("30", hotStandby.Threshold);
        Assert.Contains("standby", hotStandby.Properties);
        Assert.Contains("hot_standby", hotStandby.Properties);
        
        var warmStandby = postgresMonitorStatuses.First(pms => pms.Type == "POSTGRES_WARM_STANDBY");
        Assert.Equal("120", warmStandby.ConfiguredRPO);
        Assert.Equal("25", warmStandby.DataLagValue);
        Assert.Equal("180", warmStandby.Threshold);
        Assert.Contains("warning", warmStandby.Properties);
        Assert.Contains("warm_standby", warmStandby.Properties);
        
        var archiveRecovery = postgresMonitorStatuses.First(pms => pms.Type == "POSTGRES_ARCHIVE_RECOVERY");
        Assert.Equal("240", archiveRecovery.ConfiguredRPO);
        Assert.Equal("45", archiveRecovery.DataLagValue);
        Assert.Equal("300", archiveRecovery.Threshold);
        Assert.Contains("recovery", archiveRecovery.Properties);
        Assert.Contains("archive_recovery", archiveRecovery.Properties);
    }

    #endregion

    #region GetPostgresLMonitorStatusById Tests

    [Fact]
    public async Task GetPostgresLMonitorStatusById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _postgresMonitorStatusFixture.PostgresMonitorStatusDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetPostgresLMonitorStatusById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<PostgresMonitorStatusDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Type, returnedDetail.Type);
        Assert.Equal(expectedDetail.InfraObjectName, returnedDetail.InfraObjectName);
        Assert.Equal(expectedDetail.WorkflowName, returnedDetail.WorkflowName);
        Assert.Equal(expectedDetail.ConfiguredRPO, returnedDetail.ConfiguredRPO);
        Assert.Equal(expectedDetail.DataLagValue, returnedDetail.DataLagValue);
        Assert.Equal(expectedDetail.Properties, returnedDetail.Properties);
    }

    [Fact]
    public async Task GetPostgresLMonitorStatusById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresLMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPostgresLMonitorStatusById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresLMonitorStatusById(""));
    }

    [Fact]
    public async Task GetPostgresLMonitorStatusById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PostgresMonitorStatus", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPostgresLMonitorStatusById(nonExistentId));
    }

    #endregion

    #region GetPaginatedPostgresMonitorStatus Tests

    [Fact]
    public async Task GetPaginatedPostgresMonitorStatus_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _postgresMonitorStatusFixture.GetPostgresMonitorStatusPaginatedListQuery;
        var expectedResult = _postgresMonitorStatusFixture.PostgresMonitorStatusPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedPostgresMonitorStatus_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetPostgresMonitorStatusPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Streaming",
            SortColumn = "Type",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<PostgresMonitorStatusListVm>
        {
            Data = _postgresMonitorStatusFixture.PostgresMonitorStatusListVm.Where(pms => pms.Type.Contains("STREAMING")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("STREAMING", returnedResult.Data.First().Type);
    }

    [Fact]
    public async Task GetPaginatedPostgresMonitorStatus_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var query = new GetPostgresMonitorStatusPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 2,
            SearchString = "",
            SortColumn = "Type",
            SortOrder = "asc"
        };

        var paginatedResult = new PaginatedResult<PostgresMonitorStatusListVm>
        {
            Data = _postgresMonitorStatusFixture.PostgresMonitorStatusListVm.Skip(2).Take(2).ToList(),
            CurrentPage = 2,
            TotalPages = 3,
            TotalCount = 5,
            PageSize = 2
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorStatus(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.Equal(5, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.PageSize);
        Assert.True(returnedResult.HasPreviousPage);
        Assert.True(returnedResult.HasNextPage);
        Assert.Equal(2, returnedResult.Data.Count);
    }

    #endregion

    #region GetPostgresMonitorStatusByType Tests

    [Fact]
    public async Task GetPostgresMonitorStatusByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var validType = "POSTGRES_STREAMING_REPLICATION";
        var expectedResult = _postgresMonitorStatusFixture.PostgresMonitorStatusDetailByTypeVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusDetailByTypeQuery>(q => q.Type == validType), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPostgresMonitorStatusByType(validType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<PostgresMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
        Assert.All(returnedResult, pms => Assert.Equal("POSTGRES_STREAMING_REPLICATION", pms.Type));
        Assert.Contains(returnedResult, pms => pms.InfraObjectName == "PostgreSQL Primary Server");
        Assert.Contains(returnedResult, pms => pms.InfraObjectName == "PostgreSQL Streaming Replica");
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByType_WithEmptyType_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorStatusByType(""));
    }
    

    [Fact]
    public async Task GetPostgresMonitorStatusByType_WithNonExistentType_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentType = "NON_EXISTENT_TYPE";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusDetailByTypeQuery>(q => q.Type == nonExistentType), default))
            .ReturnsAsync(new List<PostgresMonitorStatusDetailByTypeVm>());

        // Act
        var result = await _controller.GetPostgresMonitorStatusByType(nonExistentType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<PostgresMonitorStatusDetailByTypeVm>>(okResult.Value);
        Assert.Empty(returnedResult);
    }

    #endregion

    #region GetPostgresMonitorStatusByInfraObjectId Tests

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectId_WithValidInfraObjectId_ReturnsOkResult()
    {
        // Arrange
        var validInfraObjectId = Guid.NewGuid().ToString();
        var expectedResult = _postgresMonitorStatusFixture.InfraObjectIdResult;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == validInfraObjectId), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPostgresMonitorStatusByInfraObjectId(validInfraObjectId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<string>(okResult.Value);
        Assert.Equal(expectedResult, returnedResult);
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectId_WithInvalidInfraObjectId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorStatusByInfraObjectId("invalid-guid"));
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectId_WithEmptyInfraObjectId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorStatusByInfraObjectId(""));
    }

    [Fact]
    public async Task GetPostgresMonitorStatusByInfraObjectId_WithNonExistentInfraObjectId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentInfraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == nonExistentInfraObjectId), default))
            .ThrowsAsync(new NotFoundException("PostgresMonitorStatus", nonExistentInfraObjectId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPostgresMonitorStatusByInfraObjectId(nonExistentInfraObjectId));
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsPostgresMonitorStatusCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
