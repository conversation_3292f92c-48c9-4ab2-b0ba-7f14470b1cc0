{"Version": 1, "WorkspaceRootPath": "D:\\UITestcase\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\grouppolicycontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\grouppolicycontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\dashboardviewlogfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\dashboardviewlogfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\dashboardviewlogcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\dashboardviewlogcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\grouppolicyfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\grouppolicyfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\globalvariablefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\globalvariablefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\globalvariablescontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\globalvariablescontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\globalsettingsfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\globalsettingsfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\globalsettingscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\globalsettingscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\employeefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\employeefixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\employeescontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\employeescontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|d:\\uitestcase\\api\\continuitypatrol.api\\controllers\\dashboardviewlogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|solutionrelative:api\\continuitypatrol.api\\controllers\\dashboardviewlogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\dashboardviewlog\\queries\\getpaginatedlist\\getdashboardviewlogpaginatedlistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\dashboardviewlog\\queries\\getpaginatedlist\\getdashboardviewlogpaginatedlistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\hacmpclusterscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\hacmpclusterscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|d:\\uitestcase\\api\\continuitypatrol.api\\controllers\\hacmpclustercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|solutionrelative:api\\continuitypatrol.api\\controllers\\hacmpclustercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{29AE1C19-8258-4BD3-90A9-D844CB85E214}|Services\\ContinuityPatrol.Services.Api\\ContinuityPatrol.Services.Api.csproj|d:\\uitestcase\\services\\continuitypatrol.services.api\\impl\\admin\\archiveservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29AE1C19-8258-4BD3-90A9-D844CB85E214}|Services\\ContinuityPatrol.Services.Api\\ContinuityPatrol.Services.Api.csproj|solutionrelative:services\\continuitypatrol.services.api\\impl\\admin\\archiveservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|d:\\uitestcase\\api\\continuitypatrol.api\\controllers\\globalsettingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|solutionrelative:api\\continuitypatrol.api\\controllers\\globalsettingscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\controllers\\userscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\controllers\\userscontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\userfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\userfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|d:\\uitestcase\\api\\continuitypatrol.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{88F3C959-6EB8-49EC-A078-346C5B281B86}|Api\\ContinuityPatrol.Api\\ContinuityPatrol.Api.csproj|solutionrelative:api\\continuitypatrol.api\\controllers\\userscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\user\\commands\\create\\createusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\user\\commands\\create\\createusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\user\\commands\\createdefaultuser\\createdefaultusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\user\\commands\\createdefaultuser\\createdefaultusercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|d:\\uitestcase\\tests\\shared\\continuitypatrol.shared.tests\\helper\\controllertestbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D68A3FFB-172E-4E39-A101-B47235C31F8C}|Tests\\Shared\\ContinuityPatrol.Shared.Tests\\ContinuityPatrol.Shared.Tests.csproj|solutionrelative:tests\\shared\\continuitypatrol.shared.tests\\helper\\controllertestbuilder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|d:\\uitestcase\\core\\continuitypatrol.domain\\viewmodels\\globalsetting\\globalsettingnamevm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|solutionrelative:core\\continuitypatrol.domain\\viewmodels\\globalsetting\\globalsettingnamevm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\getglobalsettingnamequeryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\getglobalsettingnamequeryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\globalsettingnamevm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\globalsettingnamevm.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\getglobalsettingnamequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnames\\getglobalsettingnamequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationcommandhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnameunique\\getglobalsettingnameuniquequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\queries\\getnameunique\\getglobalsettingnameuniquequery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|d:\\uitestcase\\tests\\api\\continuitypatrol.api.unittests\\fixtures\\hacmpclusterfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BBEF926-0EDB-4D9F-A8F8-3B575EAC5AD5}|Tests\\API\\ContinuityPatrol.Api.UnitTests\\ContinuityPatrol.Api.UnitTests.csproj|solutionrelative:tests\\api\\continuitypatrol.api.unittests\\fixtures\\hacmpclusterfixture.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\queries\\getlist\\getglobalsettinglistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\queries\\getlist\\getglobalsettinglistquery.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|d:\\uitestcase\\core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{6C6E3860-67E9-4FD9-8673-D3B154CD8806}|Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj|solutionrelative:core\\continuitypatrol.application\\features\\globalsetting\\commands\\authentication\\authenticationcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 14, "Title": "ArchiveService.cs", "DocumentMoniker": "D:\\UITestcase\\Services\\ContinuityPatrol.Services.Api\\Impl\\Admin\\ArchiveService.cs", "RelativeDocumentMoniker": "Services\\ContinuityPatrol.Services.Api\\Impl\\Admin\\ArchiveService.cs", "ToolTip": "D:\\UITestcase\\Services\\ContinuityPatrol.Services.Api\\Impl\\Admin\\ArchiveService.cs", "RelativeToolTip": "Services\\ContinuityPatrol.Services.Api\\Impl\\Admin\\ArchiveService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T13:02:42.586Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "HacmpClusterController.cs", "DocumentMoniker": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\HacmpClusterController.cs", "RelativeDocumentMoniker": "Api\\ContinuityPatrol.Api\\Controllers\\HacmpClusterController.cs", "ToolTip": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\HacmpClusterController.cs", "RelativeToolTip": "Api\\ContinuityPatrol.Api\\Controllers\\HacmpClusterController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAcwDEAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T07:10:57.451Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "HacmpClusterFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\HacmpClusterFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\HacmpClusterFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\HacmpClusterFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\HacmpClusterFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAmwA8AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:14:41.08Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "HacmpClustersControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\HacmpClustersControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\HacmpClustersControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\HacmpClustersControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\HacmpClustersControllerTests.cs", "ViewState": "AgIAAMgAAAAAAAAAAAAiwNYAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T09:58:38.033Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "GlobalSettingsControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalSettingsControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalSettingsControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalSettingsControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalSettingsControllerTests.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwCsAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T09:41:21.544Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "GlobalVariableFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalVariableFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:24:17.934Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "GroupPolicyControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GroupPolicyControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GroupPolicyControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GroupPolicyControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GroupPolicyControllerTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAABsAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:24:26.483Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "GroupPolicyFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GroupPolicyFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GroupPolicyFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GroupPolicyFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GroupPolicyFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:24:37.952Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "DashboardViewLogControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\DashboardViewLogControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\DashboardViewLogControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\DashboardViewLogControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\DashboardViewLogControllerTests.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAgwCoAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:19:47.942Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DashboardViewLogFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\DashboardViewLogFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\DashboardViewLogFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\DashboardViewLogFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\DashboardViewLogFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:21:01.327Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "GlobalVariablesControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalVariablesControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalVariablesControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalVariablesControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\GlobalVariablesControllerTests.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwCoAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:24:08.768Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "GlobalSettingsFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalSettingsFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalSettingsFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalSettingsFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\GlobalSettingsFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T09:41:13.173Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "EmployeeFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\EmployeeFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\EmployeeFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\EmployeeFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\EmployeeFixture.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:23:55.948Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "EmployeesControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\EmployeesControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\EmployeesControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\EmployeesControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\EmployeesControllerTests.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwCkAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:23:38.502Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "GetDashboardViewLogPaginatedListQuery.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\DashboardViewLog\\Queries\\GetPaginatedList\\GetDashboardViewLogPaginatedListQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\DashboardViewLog\\Queries\\GetPaginatedList\\GetDashboardViewLogPaginatedListQuery.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\DashboardViewLog\\Queries\\GetPaginatedList\\GetDashboardViewLogPaginatedListQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\DashboardViewLog\\Queries\\GetPaginatedList\\GetDashboardViewLogPaginatedListQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAABnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:21:51.881Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "DashboardViewLogController.cs", "DocumentMoniker": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\DashboardViewLogController.cs", "RelativeDocumentMoniker": "Api\\ContinuityPatrol.Api\\Controllers\\DashboardViewLogController.cs", "ToolTip": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\DashboardViewLogController.cs", "RelativeToolTip": "Api\\ContinuityPatrol.Api\\Controllers\\DashboardViewLogController.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAEwFIAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T12:21:35.173Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 18, "Title": "UsersController.cs", "DocumentMoniker": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\UsersController.cs", "RelativeDocumentMoniker": "Api\\ContinuityPatrol.Api\\Controllers\\UsersController.cs", "ToolTip": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\UsersController.cs", "RelativeToolTip": "Api\\ContinuityPatrol.Api\\Controllers\\UsersController.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAswG4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:53:09.152Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "UsersControllerTests.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\UsersControllerTests.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\UsersControllerTests.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\UsersControllerTests.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Controllers\\UsersControllerTests.cs", "ViewState": "AgIAALECAAAAAAAAAAAvwLoCAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:23:21.492Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "UserFixture.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\UserFixture.cs", "RelativeDocumentMoniker": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\UserFixture.cs", "ToolTip": "D:\\UITestcase\\Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\UserFixture.cs", "RelativeToolTip": "Tests\\API\\ContinuityPatrol.Api.UnitTests\\Fixtures\\UserFixture.cs", "ViewState": "AgIAACsAAAAAAAAAAAA4wDYAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:40:29.58Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "CreateUserCommand.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\Create\\CreateUserCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\Create\\CreateUserCommand.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\Create\\CreateUserCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\Create\\CreateUserCommand.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAYAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:31:43.243Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "GlobalSettingsController.cs", "DocumentMoniker": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\GlobalSettingsController.cs", "RelativeDocumentMoniker": "Api\\ContinuityPatrol.Api\\Controllers\\GlobalSettingsController.cs", "ToolTip": "D:\\UITestcase\\Api\\ContinuityPatrol.Api\\Controllers\\GlobalSettingsController.cs", "RelativeToolTip": "Api\\ContinuityPatrol.Api\\Controllers\\GlobalSettingsController.cs", "ViewState": "AgIAAEsAAAAAAAAAAADgv1wAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T09:41:31.331Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "CreateDefaultUserCommand.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\CreateDefaultUser\\CreateDefaultUserCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\CreateDefaultUser\\CreateDefaultUserCommand.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\CreateDefaultUser\\CreateDefaultUserCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\User\\Commands\\CreateDefaultUser\\CreateDefaultUserCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:49:44.402Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "ControllerTestBuilder.cs", "DocumentMoniker": "D:\\UITestcase\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Helper\\ControllerTestBuilder.cs", "RelativeDocumentMoniker": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Helper\\ControllerTestBuilder.cs", "ToolTip": "D:\\UITestcase\\Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Helper\\ControllerTestBuilder.cs", "RelativeToolTip": "Tests\\Shared\\ContinuityPatrol.Shared.Tests\\Helper\\ControllerTestBuilder.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAiwCUAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:24:26.641Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "GetGlobalSettingNameQuery.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQuery.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:25:02.216Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "GlobalSettingNameVm.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GlobalSettingNameVm.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GlobalSettingNameVm.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GlobalSettingNameVm.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GlobalSettingNameVm.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:30:39.845Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "GlobalSettingNameVm.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\GlobalSetting\\GlobalSettingNameVm.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Domain\\ViewModels\\GlobalSetting\\GlobalSettingNameVm.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\GlobalSetting\\GlobalSettingNameVm.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Domain\\ViewModels\\GlobalSetting\\GlobalSettingNameVm.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:40:03.507Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "GetGlobalSettingNameQueryHandler.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQueryHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQueryHandler.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQueryHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNames\\GetGlobalSettingNameQueryHandler.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T11:00:06.634Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "AuthenticationResponse.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationResponse.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationResponse.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationResponse.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:35:51.393Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "AuthenticationCommandHandler.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommandHandler.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommandHandler.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommandHandler.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommandHandler.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAowBIAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:33:35.844Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "GetGlobalSettingNameUniqueQuery.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNameUnique\\GetGlobalSettingNameUniqueQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNameUnique\\GetGlobalSettingNameUniqueQuery.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNameUnique\\GetGlobalSettingNameUniqueQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetNameUnique\\GetGlobalSettingNameUniqueQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:27:04.043Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "GetGlobalSettingListQuery.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetList\\GetGlobalSettingListQuery.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetList\\GetGlobalSettingListQuery.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetList\\GetGlobalSettingListQuery.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Queries\\GetList\\GetGlobalSettingListQuery.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:14:08.715Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "AuthenticationCommand.cs", "DocumentMoniker": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommand.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommand.cs", "ToolTip": "D:\\UITestcase\\Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommand.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Application\\Features\\GlobalSetting\\Commands\\Authentication\\AuthenticationCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T10:03:10.733Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}]}]}