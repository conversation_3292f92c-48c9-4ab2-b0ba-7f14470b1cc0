﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class AlertRepository : BaseRepository<Alert>, IAlertRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public AlertRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<int> GetAlertByMaxId()
    {
        var lastAlert = await _dbContext.Alerts.AsNoTracking().Active().OrderByDescending(x => x.Id).FirstOrDefaultAsync();
        return lastAlert?.Id ?? 0;
    }


    public override async Task<Alert> GetByIdAsync(int id)
    {
        if (id == 0)
            return await _dbContext.Alerts.AsNoTracking().FirstOrDefaultAsync();

        return await _dbContext.Alerts.FindAsync(id);
    }

    public override IQueryable<Alert> GetPaginatedQuery()
    {
        var alertsQuery = IsParent
            ? base.QueryAll(x => x.IsActive)
            : base.QueryAll(x => x.IsActive && x.CompanyId == _loggedInUserService.CompanyId);

        alertsQuery = alertsQuery.AsNoTracking();

        if (IsParent)
        {
            return _loggedInUserService.IsAllInfra
                ? alertsQuery.OrderByDescending(x => x.Id)
                : GetPaginatedInfraObject(alertsQuery).OrderByDescending(x => x.Id);
        }

        // Get distinct infraObjectIds from alerts for the company
        var infraObjectIds = _dbContext.InfraObjects
            .Where(io => io.CompanyId == _loggedInUserService.CompanyId)
            .Select(io => io.ReferenceId)
            .Distinct()
            .ToList();

        return alertsQuery
            .Where(alert => infraObjectIds.Contains(alert.InfraObjectId))
            .OrderByDescending(alert => alert.Id);
    }

    public async Task<List<Alert>> GetAlertByInfraObjectId(string infraObjectId, string entityId)
    {
        var query = base.FilterBy(alert => alert.InfraObjectId == infraObjectId && alert.EntityId == entityId && alert.IsActive);

        if (!IsParent)
        {
            query = query.Where(alert => alert.CompanyId != null && alert.CompanyId == _loggedInUserService.CompanyId);
        }
        return await query.AsNoTracking().OrderByDescending(alert => alert.LastModifiedDate).ToListAsync();
    }

    public async Task<List<Alert>> GetAlertByClientAlertId(string clientAlertId)
    {
        var query = base.FilterBy(alert => alert.ClientAlertId == clientAlertId && alert.IsActive);

        if (!IsParent)
        {
            query = query.Where(alert => alert.CompanyId != null && alert.CompanyId == _loggedInUserService.CompanyId);
        }
        return await query.AsNoTracking().ToListAsync();
    }


    public async Task<List<Alert>> GetLastAlertByInfraObject(string infraObjectId, DateTime userLastAlertDate, DateTime alertDate)
    {
        var query = base.FilterBy(alert => alert.InfraObjectId == infraObjectId && alert.CreatedDate >= userLastAlertDate &&
                                           alert.LastModifiedDate <= alertDate &&
                                           alert.IsActive);

        if (!IsParent)
        {
            query = query.Where(alert => alert.CompanyId != null && alert.CompanyId == _loggedInUserService.CompanyId);
        }
        return await query.AsNoTracking().ToListAsync();
    }

    public async Task<List<Alert>> GetByAlertId(int userLastAlertId, DateTime createdDate, DateTime lastModifiedDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.ReferenceId.Equals(userLastAlertId) ||
                        (x.CreatedDate >= createdDate && x.LastModifiedDate <= lastModifiedDate)).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.ReferenceId.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)
                        || (x.CreatedDate >= createdDate && x.LastModifiedDate <= lastModifiedDate)).Active().AsNoTracking().ToListAsync();
         
    }

    public IQueryable<Alert> GetAlertByUserLastAlertIdAndDate(int userLastAlertId)
    {
        var alertPreviousList = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Id < userLastAlertId).Active().AsNoTracking().ToList()
            : base.FilterBy(x => x.Id < userLastAlertId
                            && x.CompanyId !=null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().ToList();
         

        if (alertPreviousList.Count > 0)
            return _loggedInUserService.IsParent ?
                base.FilterBy(x => x.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(x => x.Id).Skip(1)
                : base.FilterBy(x => x.Id.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Active().AsNoTracking()
                .OrderBy(x => x.Id)
                .Skip(1);

         
        return _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(x => x.Id)
            : base.FilterBy(x => x.Id.Equals(userLastAlertId) && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Active().AsNoTracking()
            .OrderBy(x => x.Id);

        
    }

    public IQueryable<Alert> GetAlertByUserLastInfraObjectIdAndDate(string infraObjectId, DateTime createdDate)
    {
        return _loggedInUserService.IsParent
             ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate
                        && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
         
    }

    public async Task<List<Alert>> GetAlertByUserLastAlertId(int userLastAlertId)
    {
        var alertPreviousList = _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.Id < userLastAlertId).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.Id < userLastAlertId
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                    .Active().AsNoTracking().ToListAsync();
         

        if (alertPreviousList.Count > 0)
            return _loggedInUserService.IsParent
                ? await base.FilterBy(a => a.Id >= userLastAlertId).Active().AsNoTracking().OrderBy(a => a.Id).Skip(1).ToListAsync()
                : await base.FilterBy(a => a.Id >= userLastAlertId && a.CompanyId != null && a.CompanyId.Equals(_loggedInUserService.CompanyId))
                .Active().AsNoTracking()
                .OrderBy(a => a.Id)
                .Skip(1)
                .ToListAsync();
         
        return _loggedInUserService.IsParent
            ? await base.FilterBy(a => a.Id >= userLastAlertId).OrderBy(a => a.Id).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(a => a.Id >= userLastAlertId && a.CompanyId != null && a.CompanyId.Equals(_loggedInUserService.CompanyId))
              .Active().AsNoTracking()
              .OrderBy(a => a.Id)
              .ToListAsync();
         
    }

    public async Task<List<Alert>> GetAlertByUserLastInfraObjectId(string infraObjectId, DateTime createdDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate).Active().AsNoTracking().ToListAsync()
            : await base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate >= createdDate
             && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking()
            .Active()
            .ToListAsync();
    }

    public async Task<List<Alert>> GetAlertListFilterByDate(string startDate, string endDate)
    {
        return _loggedInUserService.IsParent
            ? await base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).AsNoTracking().Active().ToListAsync()
            : await base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().Active().ToListAsync();
    }

    //
    public IQueryable<Alert> GetPaginatedByInfraObjectId(string infraObjectId)
    {
        var alert = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId))
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId)
                    && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId));
        
        return _loggedInUserService.IsAllInfra
            ? alert : GetPaginatedInfraObject(alert);
    }

    public IQueryable<Alert> GetPaginatedBySeverity(string severity)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) &&
                     x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                        .Active().AsNoTracking()
                        .OrderByDescending(x => x.Id);
        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByType(string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Type.Equals(type) &&
                     x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
            .Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByCreateAndEndDate(string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                      && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
      
        return _loggedInUserService.IsAllInfra
            ? alerts
            : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndSeverity(string infraObjectId, string severity)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.CompanyId != null &&
                 x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndType(string infraObjectId, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type)
                && x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectAndDates(string infraObjectId, string startDate, string endDate)
    {

        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId)).AsNoTracking().Active().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectSeverityType(string infraObjectId, string severity, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) && x.CompanyId != null
                 && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
       
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectWithSeverityAndDates(string infraObjectId, string severity,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() &&
                            x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId))
                            .Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByInfraObjectWithTypeAndDates(string infraObjectId, string type,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null &&
                             x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedByAllUniqueData(string infraObjectId, string severity, string type,
        string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) &&
                   x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking()
                .OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.InfraObjectId.Equals(infraObjectId) && x.Severity.Equals(severity) && x.Type.Equals(type) &&
                   x.CreatedDate.Date >= startDate.ToDateTime() && x.LastModifiedDate.Date <= endDate.ToDateTime() &&
                   x.CompanyId != null && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking()
                .OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
    }

    public IQueryable<Alert> GetPaginatedBySeverityAndType(string severity, string type)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type)).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) && x.CompanyId != null
                 && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
         
    }

    public IQueryable<Alert> GetPaginatedSeverityByDates(string severity, string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
         
    }

    public IQueryable<Alert> GetPaginatedBySeverityTypeAndDates(string severity, string type, string startDate,
        string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Severity.Equals(severity) && x.Type.Equals(type) &&
                            x.CreatedDate.Date >= startDate.ToDateTime() &&
                            x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);
        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
         
    }

    public IQueryable<Alert> GetPaginatedTypeAndDate(string type, string startDate, string endDate)
    {
        var alerts = _loggedInUserService.IsParent
            ? base.FilterBy(x => x.Type.Equals(type) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                           x.LastModifiedDate.Date <= endDate.ToDateTime()).Active().AsNoTracking().OrderByDescending(x => x.Id)
            : base.FilterBy(x => x.Type.Equals(type) && x.CreatedDate.Date >= startDate.ToDateTime() &&
                           x.LastModifiedDate.Date <= endDate.ToDateTime() && x.CompanyId != null
                            && x.CompanyId.Equals(_loggedInUserService.CompanyId)).Active().AsNoTracking().OrderByDescending(x => x.Id);

        return _loggedInUserService.IsAllInfra
            ? alerts : GetPaginatedInfraObject(alerts);
         
    }
    //Filter
    public IQueryable<Alert> GetPaginatedInfraObject(IQueryable<Alert> infraObjects)
    {
        var assignedInfraObjectIds = AssignedEntity.AssignedBusinessServices
            .SelectMany(businessService => businessService.AssignedBusinessFunctions)
            .SelectMany(businessFunction => businessFunction.AssignedInfraObjects)
            .Select(infraObject => infraObject.Id);

        return infraObjects.Where(infraObject => assignedInfraObjectIds.Contains(infraObject.InfraObjectId));
    }


    public async Task<(PaginatedResult<Alert>,Dictionary<string,int>)> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<Alert> specification, Expression<Func<Alert, bool>> expression, string sortColumn, string sortOrder)
    {
        
        IQueryable<Alert> query;

        if (_loggedInUserService.IsParent)
        {
            query = _loggedInUserService.IsAllInfra
                ? Entities.DescOrderById().Specify(specification).Where(expression) // Parent & AllInfra = True
                : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression)).AsQueryable(); // Parent = True, AllInfra = False
        }
        else
        {
            query = _loggedInUserService.IsAllInfra
                ? Entities.DescOrderById().Specify(specification).Where(expression)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId) // Parent = False, AllInfra = True
                : GetPaginatedInfraObject(Entities.DescOrderById().Specify(specification).Where(expression)
                    .Where(x => x.CompanyId == _loggedInUserService.CompanyId)).AsQueryable(); // Parent = False, AllInfra = False
        }

        var severityCounts = await query
            .GroupBy(x => x.Severity)
            .Select(g => new { g.Key, Count = g.Count() })
            .ToDictionaryAsync(g => g.Key, g => g.Count);

        var paginatedResult = await query.ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);

        return (paginatedResult, severityCounts);
    }
}