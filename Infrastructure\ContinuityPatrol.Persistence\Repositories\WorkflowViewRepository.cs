﻿using ContinuityPatrol.Application.Contracts.Persistence;
using ContinuityPatrol.Application.Features.Workflow.Queries.GetDetail;
using ContinuityPatrol.Domain.Extensions;
using ContinuityPatrol.Domain.Views;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Domain;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Persistence.Repositories;

public class WorkflowViewRepository : BaseRepository<WorkflowView>, IWorkflowViewRepository
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILoggedInUserService _loggedInUserService;

    public WorkflowViewRepository(ApplicationDbContext dbContext, ILoggedInUserService loggedInUserService) : base(dbContext, loggedInUserService)
    {
        _dbContext = dbContext;
        _loggedInUserService = loggedInUserService;
    }

    public async Task<List<WorkflowView>> GetWorkflowNames()
    {
        if (_loggedInUserService.IsParent)
        {
           return _loggedInUserService.IsAllInfra
                ? await _dbContext.WorkflowViews.AsNoTracking().Active()
                    .Where(x => (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
                    .Select(x => new WorkflowView
                    {
                        ReferenceId = x.ReferenceId,
                        WorkflowName = x.WorkflowName,
                        IsLock = x.IsLock,
                        IsPublish = x.IsPublish,
                        IsVerify = x.IsVerify,
                        CreatedBy = x.CreatedBy,
                        InfraObjectId = x.InfraObjectId
                    }).OrderBy(x => x.WorkflowName).ToListAsync()
                : AssignedInfraObjects(_dbContext.WorkflowViews.AsNoTracking().Active()
                    .Where(x => (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
                    .Select(x => new WorkflowView
                    {
                        ReferenceId = x.ReferenceId,
                        WorkflowName = x.WorkflowName,
                        IsLock = x.IsLock,
                        IsPublish = x.IsPublish,
                        IsVerify = x.IsVerify,
                        CreatedBy = x.CreatedBy,
                        InfraObjectId = x.InfraObjectId
                    })).OrderBy(x => x.WorkflowName).ToList();

        }

        return _loggedInUserService.IsAllInfra
            ? await _dbContext.WorkflowViews.AsNoTracking().Active()
            .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                        x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                        ((!_loggedInUserService.IsParent && x.IsPublish) || !x.IsPublish))
            .Select(x => new WorkflowView
            {
                ReferenceId = x.ReferenceId,
                WorkflowName = x.WorkflowName,
                IsLock = x.IsLock,
                IsPublish = x.IsPublish,
                IsVerify = x.IsVerify,
                CreatedBy = x.CreatedBy
            })
            .OrderBy(x => x.WorkflowName).ToListAsync()
           : AssignedInfraObjects(_dbContext.WorkflowViews.AsNoTracking().Active()
                .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                            x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                            ((!_loggedInUserService.IsParent && x.IsPublish) || !x.IsPublish))
                .Select(x => new WorkflowView
                {
                    ReferenceId = x.ReferenceId,
                    WorkflowName = x.WorkflowName,
                    IsLock = x.IsLock,
                    IsPublish = x.IsPublish,
                    IsVerify = x.IsVerify,
                    CreatedBy = x.CreatedBy
                })).OrderBy(x => x.WorkflowName).ToList();




        //var workflow = _loggedInUserService.IsParent
        //    ?  _dbContext.WorkflowViews.Active()
        //        .Where(x => (x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
        //        .Select(x => new WorkflowView
        //        {
        //            ReferenceId = x.ReferenceId,
        //            WorkflowName = x.WorkflowName,
        //            IsLock = x.IsLock,
        //            IsPublish = x.IsPublish,
        //            IsVerify = x.IsVerify,
        //            CreatedBy = x.CreatedBy,
        //            InfraObjectId = x.InfraObjectId
        //        })
        //        .OrderBy(x => x.WorkflowName)
        //    : _dbContext.WorkflowViews.Active()
        //        .Where(x => x.CreatedBy.Equals(_loggedInUserService.UserId) &&
        //                    x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
        //                    ((!_loggedInUserService.IsParent && x.IsPublish) || !x.IsPublish))
        //        .Select(x => new WorkflowView
        //        {
        //            ReferenceId = x.ReferenceId,
        //            WorkflowName = x.WorkflowName,
        //            IsLock = x.IsLock,
        //            IsPublish = x.IsPublish,
        //            IsVerify = x.IsVerify,
        //            CreatedBy = x.CreatedBy
        //        })
        //        .OrderBy(x => x.WorkflowName);

        //return workflowPermission.Count > 0
        //    ? workflow.Concat(await _dbContext.WorkFlows.Active().Where(x => workflowPermission.Contains(x.ReferenceId))
        //                   .Select(x => new Workflow
        //                   {
        //                       ReferenceId = x.ReferenceId,
        //                       Name = x.Name,
        //                       IsLock = x.IsLock,
        //                       IsPublish = x.IsPublish,
        //                       IsVerify = x.IsVerify,
        //                       CreatedBy = x.CreatedBy
        //                   }).ToListAsync()).DistinctBy(x => x.ReferenceId).ToList()
        //    : workflow;

    }



    public async Task<WorkflowDetailVm> GetWorkflowByIdAsync(string id)
    {
        var workflowProfiles =
            _loggedInUserService.IsParent
                ? base.FilterBy(workflow => workflow.ReferenceId.Equals(id))
                : base.FilterBy(workflow => workflow.ReferenceId.Equals(id) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        return await workflowProfiles.Select(x => new WorkflowDetailVm
        {
                Id = x.ReferenceId,
                Name = x.WorkflowName,
                ProfileName = x.ProfileName,
                IsLock = x.IsLock,
                IsVerify = x.IsVerify,
                WorkflowType = x.WorkflowType,
                IsPublish = x.IsPublish,
                IsDraft = x.IsDraft,
                Version = x.Version,
                Properties = x.Properties,
                IsAttach = !string.IsNullOrWhiteSpace(x.InfraObjectId),
                IsRunning = x.IsRunning,
                RunningCount = x.ProgressBar

        }).FirstOrDefaultAsync();
    }

    public override async Task<PaginatedResult<WorkflowView>> PaginatedListAllAsync(int pageNumber, int pageSize, Specification<WorkflowView> productFilterSpec, string sortColumn, string sortOrder)
    {
        if (_loggedInUserService.IsParent)
        {
            return await (_loggedInUserService.IsAllInfra
                    ? Entities.Specify(productFilterSpec)
                        .Where(x => (x.CreatedBy != null && x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) || x.IsPublish)
                        .DescOrderById()
                    : PaginatedAssignedInfraObjects(Entities.Specify(productFilterSpec).Where(x => (x.CreatedBy != null && x.CreatedBy.Equals(_loggedInUserService.UserId) && !x.IsPublish) ||
                        (x.IsPublish)).DescOrderById()))
                .Select(w=> new WorkflowView
                {
                    ReferenceId = w.ReferenceId,
                    WorkflowName = w.WorkflowName,
                    ProfileId = w.ProfileId,
                    ProfileName = w.ProfileName,
                    InfraObjectId = w.InfraObjectId,
                    InfraObjectName = w.InfraObjectName,
                    BusinessServiceName=w.BusinessServiceName,
                    BusinessFunctionName=w.BusinessFunctionName,
                    WorkflowType = w.WorkflowType,
                    IsLock = w.IsLock,
                    IsPublish = w.IsPublish,
                    IsVerify = w.IsVerify,
                    Version = w.Version,
                    CreatedByUserName = w.CreatedByUserName,
                    LastExecutionDate = w.LastExecutionDate
                })
                .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
        }

        return await (_loggedInUserService.IsAllInfra
                  ? Entities.Specify(productFilterSpec)
                      .Where(x => x.CreatedBy != null && x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                                  x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                                  (x.IsPublish || (!x.IsPublish)))
                      .DescOrderById()
                  : PaginatedAssignedInfraObjects(Entities.Specify(productFilterSpec)
                      .Where(x => x.CreatedBy != null && x.CreatedBy.Equals(_loggedInUserService.UserId) &&
                                  x.CompanyId.Equals(_loggedInUserService.CompanyId) &&
                                  (x.IsPublish || (!x.IsPublish))).DescOrderById()))
            .Select(w => new WorkflowView
            {
                ReferenceId = w.ReferenceId,
                WorkflowName = w.WorkflowName,
                ProfileId = w.ProfileId,
                ProfileName = w.ProfileName,
                InfraObjectId = w.InfraObjectId,
                InfraObjectName = w.InfraObjectName,
                BusinessServiceName = w.BusinessServiceName,
                BusinessFunctionName = w.BusinessFunctionName,
                WorkflowType = w.WorkflowType,
                IsLock = w.IsLock,
                IsPublish = w.IsPublish,
                IsVerify = w.IsVerify,
                Version = w.Version,
                CreatedByUserName = w.CreatedByUserName
            })
              .ToSortedPaginatedListAsync(pageNumber, pageSize, sortColumn, sortOrder);
    }

    public Task<List<string>> GetWorkflowByProfileIds(List<string> profileIds)
    {
        var workflowProfiles =
             _loggedInUserService.IsParent
                ? base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId))
                : base.FilterBy(workflow => profileIds.Contains(workflow.ProfileId) && workflow.CompanyId.Equals(_loggedInUserService.CompanyId));

        
            var assignedInfra = AssignedInfraObjects(workflowProfiles).Select(x=>x.ProfileId).ToList();

            var notMatchedIds = profileIds.Except(workflowProfiles.Select(w => w.ProfileId)).ToList();

            return Task.FromResult(assignedInfra
                .Union(notMatchedIds)
                .Distinct()
                .ToList());
    }


    private IReadOnlyList<WorkflowView> AssignedInfraObjects(IQueryable<WorkflowView> infraObjects)
    {
        var infraObjectList = new List<WorkflowView>();

        var assignedBusinessFunctions = new List<AssignedBusinessFunctions>();

        if (AssignedEntity?.AssignedBusinessServices?.Count > 0)
            foreach (var assignedBusinessServices in AssignedEntity?.AssignedBusinessServices!)
                assignedBusinessFunctions.AddRange(assignedBusinessServices.AssignedBusinessFunctions);

        var assignedBusinessInfraObjects = new List<AssignedInfraObjects>();

        if (assignedBusinessFunctions.Count > 0)
            foreach (var assignedBusinessFunction in assignedBusinessFunctions)
                assignedBusinessInfraObjects.AddRange(assignedBusinessFunction.AssignedInfraObjects);

        foreach (var infraObject in infraObjects)
            if (assignedBusinessInfraObjects.Count > 0)
                infraObjectList.AddRange(from assignedInfraObject in assignedBusinessInfraObjects
                    where infraObject.InfraObjectId == assignedInfraObject.Id select infraObject);
        return infraObjectList;
    }



    private IQueryable<WorkflowView> PaginatedAssignedInfraObjects(IQueryable<WorkflowView> infraObjects)
    {
        if (AssignedEntity == null) return infraObjects;
        var isNullInfraObjects = infraObjects.Where(x => x.InfraObjectId.IsNullOrWhiteSpace());

        var assignedInfraIds = AssignedEntity!.AssignedBusinessServices.SelectMany(x => x.AssignedBusinessFunctions.SelectMany(y => y.AssignedInfraObjects.Select(z => z.Id))).ToList();

        var filteredInfraObjects = infraObjects.Where(s => assignedInfraIds.Contains(s.InfraObjectId));

        infraObjects = filteredInfraObjects.Concat(isNullInfraObjects);

        return infraObjects;
    }
}
