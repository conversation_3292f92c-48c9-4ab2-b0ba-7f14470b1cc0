﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
	  <PackageReference Include="AutoMapper" Version="14.0.0" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
	  <PackageReference Include="Microsoft.Data.SqlClient.SNI.runtime" Version="6.0.2" />
	  <PackageReference Include="Microsoft.Extensions.ObjectPool" Version="9.0.4" />
	  <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
	  <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Infrastructure\ContinuityPatrol.Persistence\ContinuityPatrol.Persistence.csproj" />
		<ProjectReference Include="..\..\Shared\ContinuityPatrol.Shared.Services\ContinuityPatrol.Shared.Services.csproj" />
	</ItemGroup>

</Project>