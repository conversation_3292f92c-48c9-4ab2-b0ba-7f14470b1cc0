﻿using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Db.Impl.Manage;

public class MSSQLDbMirroringMonitorStatusService : BaseService, IMSSQLDbMirroringMonitorStatusService
{
    public MSSQLDbMirroringMonitorStatusService(IHttpContextAccessor accessor) : base(accessor)
    {
    }

    public async Task<BaseResponse> CreateAsync(CreateSQLDBMirroringStatusCommand createSqlDbMirroringStatusCommand)
    {
        Logger.LogDebug($"Create SqlDb MirroringStatus '{createSqlDbMirroringStatusCommand.InfraObjectName}'");

        return await Mediator.Send(createSqlDbMirroringStatusCommand);
    }

    public async Task<BaseResponse> UpdateAsync(UpdateSQLDBMirroringStatusCommand updateSqlDbMirroringStatusCommand)
    {
        Logger.LogDebug($"Update SQL DBMirroringStatus '{updateSqlDbMirroringStatusCommand.InfraObjectName}'");

        return await Mediator.Send(updateSqlDbMirroringStatusCommand);
    }

    public async Task<List<MSSQLDBMirroingStatuslistVM>> GetAllSqlDbMirroringStatus()
    {
        Logger.LogDebug("Get All SqlDb MirroringStatus");

        return await Mediator.Send(new SQLDbMirroingStatusListQuery());
    }

    public async Task<MSSQLDBMirroingStatuslistVM> GetByReferenceId(string id)
    {
        Guard.Against.InvalidGuidOrEmpty(id, "SqlDb MirroringStatusById");

        Logger.LogDebug($"Get SqlDb MirroringStatus Detail By Id '{id}' ");

        return await Mediator.Send(new SQLDbMirroingStatusDetailQuery { Id = id });
    }

    public async Task<List<SQLDBMirroringMonitorStatusDetailByTypeVm>> GetSqlDbMirroringStatusByType(string type)
    {
        Guard.Against.InvalidGuidOrEmpty(type, "SqlDb MirroringStatus Type");

        Logger.LogDebug($"Get SqlDb MirroringStatus Detail by Type '{type}'");

        return await Mediator.Send(new SQLDBMirroringMonitorStatusDetailByTypeQuery { Type = type });
    }

    public async Task<PaginatedResult<MSSQLDBMirroingStatuslistVM>> GetPaginatedMSSQLDBMirroringMonitorStatus(
        GetMSSQLDbMonitorStatusPaginatedListQuery query)
    {
        Logger.LogDebug("Get Searching Details in SqlDb MirroringStatus Paginated List");

        return await Cache.GetOrAddAsync(ApplicationConstants.Cache.AllSQLDbMirroingStatusCachekey,
            () => Mediator.Send(query));
    }
}