﻿using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLDBMirroringStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLDBMirroingStatusModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Shared.Services.Contract;

public interface IMSSQLDbMirroringMonitorStatusService
{
    Task<BaseResponse> CreateAsync(CreateSQLDBMirroringStatusCommand createSqlDbMirroringStatusCommand);
    Task<BaseResponse> UpdateAsync(UpdateSQLDBMirroringStatusCommand updateSqlDbMirroringStatusCommand);
    Task<List<MSSQLDBMirroingStatuslistVM>> GetAllSqlDbMirroringStatus();
    Task<MSSQLDBMirroingStatuslistVM> GetByReferenceId(string id);
    Task<List<SQLDBMirroringMonitorStatusDetailByTypeVm>> GetSqlDbMirroringStatusByType(string type); Task<PaginatedResult<MSSQLDBMirroingStatuslistVM>> GetPaginatedMSSQLDBMirroringMonitorStatus(GetMSSQLDbMonitorStatusPaginatedListQuery query);
}
