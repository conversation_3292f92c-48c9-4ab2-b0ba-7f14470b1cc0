using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Create;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Delete;
using ContinuityPatrol.Application.Features.DashboardViewLog.Commands.Update;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetByInfraObjectId;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDataLagStatusReport;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.DashboardViewLog.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.DashboardViewLogModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class DashboardViewLogControllerTests : IClassFixture<DashboardViewLogFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly DashboardViewLogController _controller;
    private readonly DashboardViewLogFixture _fixture;

    public DashboardViewLogControllerTests(DashboardViewLogFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<DashboardViewLogController>();
        _controller = testBuilder.CreateController(
            _ => new DashboardViewLogController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetDashboardViewLogs_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewLogListVm);

        var result = await _controller.GetDashboardViewLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViewLogList = Assert.IsAssignableFrom<List<DashboardViewLogListVm>>(okResult.Value);
        Assert.Equal(_fixture.DashboardViewLogListVm.Count, dashboardViewLogList.Count);
    }

    [Fact]
    public async Task GetDashboardViewLogs_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<DashboardViewLogListVm>());

        var result = await _controller.GetDashboardViewLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViewLogList = Assert.IsAssignableFrom<List<DashboardViewLogListVm>>(okResult.Value);
        Assert.Empty(dashboardViewLogList);
    }

    [Fact]
    public async Task GetDashboardViewLogById_Should_Return_Detail()
    {
        var dashboardViewLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewLogDetailQuery>(q => q.Id == dashboardViewLogId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DashboardViewLogDetailVm);

        var result = await _controller.GetDashboardViewLogById(dashboardViewLogId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var dashboardViewLogDetail = Assert.IsAssignableFrom<DashboardViewLogDetailVm>(okResult.Value);
        Assert.NotNull(dashboardViewLogDetail);
        Assert.Equal(_fixture.DashboardViewLogDetailVm.Id, dashboardViewLogDetail.Id);
    }

    [Fact]
    public async Task GetDashboardViewLogById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetDashboardViewLogById(null!));
    }

    [Fact]
    public async Task GetDashboardViewLogById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDashboardViewLogById(""));
    }

    [Fact]
    public async Task GetDashboardViewLogById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetDashboardViewLogById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedDashboardViewLogs_Should_Return_Result()
    {
        var query = _fixture.GetDashboardViewLogPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedDashboardViewLogListVm);

        var result = await _controller.GetPaginatedDashboardViewLogs(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<DashboardViewLogListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedDashboardViewLogListVm, paginatedResult);
    }

    [Fact]
    public async Task CreateDashboardViewLog_Should_Return_Success()
    {
        var response = new CreateDashboardViewLogResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateDashboardViewLogCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateDashboardViewLog(_fixture.CreateDashboardViewLogCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateDashboardViewLogResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateDashboardViewLog_Should_Return_Success()
    {
        var response = new UpdateDashboardViewLogResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateDashboardViewLogCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateDashboardViewLog(_fixture.UpdateDashboardViewLogCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateDashboardViewLogResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteDashboardViewLog_Should_Call_Mediator()
    {
        var dashboardViewLogId = Guid.NewGuid().ToString();
        var response = new DeleteDashboardViewLogResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteDashboardViewLogCommand>(c => c.Id == dashboardViewLogId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteDashboardViewLog(dashboardViewLogId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteDashboardViewLogResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteDashboardViewLog_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteDashboardViewLog(null!));
    }

    [Fact]
    public async Task DeleteDashboardViewLog_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteDashboardViewLog(""));
    }

    [Fact]
    public async Task GetDatalagStatusReport_Should_Return_Report()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDataLagStatusReportQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.DataLagStatusReport);

        var result = await _controller.GetDatalagStatusReport();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var report = Assert.IsAssignableFrom<DataLagStatusReport>(okResult.Value);
        Assert.NotNull(report);
    }

    [Fact]
    public async Task GetDashboardViewLogByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetDashboardViewLogByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.InfraObjectHealthScoreList);

        var result = await _controller.GetDashboardViewLogByInfraObjectId(infraObjectId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var healthScoreList = Assert.IsAssignableFrom<List<InfraObjectHealthScore>>(okResult.Value);
        Assert.Equal(_fixture.InfraObjectHealthScoreList.Count, healthScoreList.Count);
    }

    [Fact]
    public async Task GetDashboardViewLogs_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.DashboardViewLogListVm);

        await _controller.GetDashboardViewLogs();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetDashboardViewLogs_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetDashboardViewLogs());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetDashboardViewLogs_VerifiesQueryType()
    {
        GetDashboardViewLogListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetDashboardViewLogListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<DashboardViewLogListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetDashboardViewLogListQuery;
            })
            .ReturnsAsync(_fixture.DashboardViewLogListVm);

        await _controller.GetDashboardViewLogs();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetDashboardViewLogListQuery>(capturedQuery);
    }
}
