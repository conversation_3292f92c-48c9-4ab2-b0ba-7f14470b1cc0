using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Employee.Commands.Create;
using ContinuityPatrol.Application.Features.Employee.Commands.Delete;
using ContinuityPatrol.Application.Features.Employee.Commands.Update;
using ContinuityPatrol.Application.Features.Employee.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Employee.Queries.GetList;
using ContinuityPatrol.Application.Features.Employee.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.EmployeeModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class EmployeesControllerTests : IClassFixture<EmployeeFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly EmployeesController _controller;
    private readonly EmployeeFixture _fixture;

    public EmployeesControllerTests(EmployeeFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<EmployeesController>();
        _controller = testBuilder.CreateController(
            _ => new EmployeesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetEmployees_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.EmployeeListVm);

        var result = await _controller.GetEmployees();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var employeeList = Assert.IsAssignableFrom<List<EmployeeListVm>>(okResult.Value);
        Assert.Equal(_fixture.EmployeeListVm.Count, employeeList.Count);
    }

    [Fact]
    public async Task GetEmployees_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<EmployeeListVm>());

        var result = await _controller.GetEmployees();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var employeeList = Assert.IsAssignableFrom<List<EmployeeListVm>>(okResult.Value);
        Assert.Empty(employeeList);
    }

    [Fact]
    public async Task GetEmployeeById_Should_Return_Detail()
    {
        var employeeId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetEmployeeDetailQuery>(q => q.Id == employeeId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.EmployeeDetailVm);

        var result = await _controller.GetEmployeeById(employeeId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var employeeDetail = Assert.IsAssignableFrom<EmployeeDetailVm>(okResult.Value);
        Assert.NotNull(employeeDetail);
        Assert.Equal(_fixture.EmployeeDetailVm.Id, employeeDetail.Id);
    }

    [Fact]
    public async Task GetEmployeeById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetEmployeeById(null!));
    }

    [Fact]
    public async Task GetEmployeeById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetEmployeeById(""));
    }

    [Fact]
    public async Task GetEmployeeById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetEmployeeById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedEmployees_Should_Return_Result()
    {
        var query = _fixture.GetEmployeePaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedEmployeeListVm);

        var result = await _controller.GetPaginatedEmployees(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<EmployeeListVm>>(okResult.Value);
        Assert.Equal(_fixture.EmployeeListVm.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateEmployee_Should_Return_Success()
    {
        var response = new CreateEmployeeResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateEmployeeCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateEmployee(_fixture.CreateEmployeeCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateEmployeeResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateEmployee_Should_Return_Success()
    {
        var response = new UpdateEmployeeResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateEmployeeCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateEmployee(_fixture.UpdateEmployeeCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateEmployeeResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteEmployee_Should_Call_Mediator()
    {
        var employeeId = Guid.NewGuid().ToString();
        var response = new DeleteEmployeeResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteEmployeeCommand>(c => c.Id == employeeId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteEmployee(employeeId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteEmployeeResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteEmployee_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteEmployee(null!));
    }

    [Fact]
    public async Task DeleteEmployee_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteEmployee(""));
    }

    [Theory]
    [InlineData("EmployeeName1", null)]
    [InlineData("AnotherName", "some-guid")]
    public async Task IsEmployeeNameExist_Should_Return_True(string name, string? id)
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await _controller.IsEmployeeNameExist(name, id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsEmployeeNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsEmployeeNameExist(null!, Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task IsEmployeeNameExist_EmptyName_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsEmployeeNameExist("", Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task GetEmployees_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.EmployeeListVm);

        await _controller.GetEmployees();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetEmployees_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetEmployees());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetEmployees_VerifiesQueryType()
    {
        GetEmployeeListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetEmployeeListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<EmployeeListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetEmployeeListQuery;
            })
            .ReturnsAsync(_fixture.EmployeeListVm);

        await _controller.GetEmployees();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetEmployeeListQuery>(capturedQuery);
    }
}
