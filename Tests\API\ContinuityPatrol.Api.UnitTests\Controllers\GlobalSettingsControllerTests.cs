﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Delete;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetList;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNames;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using GlobalSettingNameVm = ContinuityPatrol.Domain.ViewModels.GlobalSetting.GlobalSettingNameVm;

namespace ContinuityPatrol.Api.UnitTests.Controllers
{
    public class GlobalSettingsControllerTests : IClassFixture<GlobalSettingsFixture>
    {
        private readonly GlobalSettingsController _controller;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly GlobalSettingsFixture _globalSettingsFixture;

        public GlobalSettingsControllerTests(GlobalSettingsFixture globalSettingsFixture)
        {
            _globalSettingsFixture = globalSettingsFixture;
            var testBuilder = new ControllerTestBuilder<GlobalSettingsController>();
            _controller = testBuilder.CreateController(
                _ => new GlobalSettingsController(),
                out _mediatorMock);
        }

        [Fact]
        public async Task GetGlobalSettings_Should_Return_List()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingListVms);

            var result = await _controller.GetGlobalSettings();
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnList = Assert.IsAssignableFrom<List<GlobalSettingListVm>>(okResult.Value);
            Assert.Single(returnList);
        }

        [Fact]
        public async Task GetGlobalSettingById_Should_Return_Detail()
        {
            var id = Guid.NewGuid().ToString();

            _mediatorMock
                .Setup(m => m.Send(It.Is<GetGlobalSettingDetailQuery>(q => q.Id == id), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingDetailVm);

            var result = await _controller.GetGlobalSettingById(id);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnDetail = Assert.IsAssignableFrom<GlobalSettingDetailVm>(okResult.Value);
            Assert.Equal(id, returnDetail.Id);
        }

        [Fact]
        public async Task GetGlobalSettingById_NullId_Should_Throw()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetGlobalSettingById(null!));
        }

        [Fact]
        public async Task GetGlobalSettingById_EmptyId_Should_Throw()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalSettingById(""));
        }

        [Fact]
        public async Task CreateGlobalSetting_Should_Return_Created()
        {
            var response = new CreateGlobalSettingResponse { Message = "Created", Success = true };

            _mediatorMock
                .Setup(m => m.Send(_globalSettingsFixture.CreateGlobalSettingCommand, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.CreateGlobalSetting(_globalSettingsFixture.CreateGlobalSettingCommand);
            var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
            var responseVal = Assert.IsAssignableFrom<CreateGlobalSettingResponse>(createdResult.Value);
            Assert.True(responseVal.Success);
        }

        [Fact]
        public async Task UpdateGlobalSetting_Should_Return_Success()
        {
            var response = new UpdateGlobalSettingResponse { Message = "Updated", Success = true };

            _mediatorMock
                .Setup(m => m.Send(_globalSettingsFixture.UpdateGlobalSettingCommand, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.UpdateGlobalSetting(_globalSettingsFixture.UpdateGlobalSettingCommand);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var updateResponse = Assert.IsAssignableFrom<UpdateGlobalSettingResponse>(okResult.Value);
            Assert.True(updateResponse.Success);
        }

        [Fact]
        public async Task DeleteGlobalSetting_Should_Return_Success()
        {
            var id = Guid.NewGuid().ToString();
            var response = new DeleteGlobalSettingResponse { Message = "Deleted", Success = true };

            _mediatorMock
                .Setup(m => m.Send(It.Is<DeleteGlobalSettingCommand>(x => x.Id == id), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.DeleteGlobalSetting(id);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var deleteResponse = Assert.IsAssignableFrom<DeleteGlobalSettingResponse>(okResult.Value);
            Assert.True(deleteResponse.Success);
        }

        [Fact]
        public async Task DeleteGlobalSetting_NullId_Should_Throw()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteGlobalSetting(null!));
        }

        [Fact]
        public async Task DeleteGlobalSetting_EmptyId_Should_Throw()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteGlobalSetting(""));
        }

        [Fact]
        public async Task GetPaginatedGlobalSettings_Should_Return_Data()
        {
            var query = new GetGlobalSettingPaginatedListQuery();

            _mediatorMock
                .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.PaginatedGlobalSettingListVm);

            var result = await _controller.GetPaginatedGlobalSettings(query);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var resultData = Assert.IsAssignableFrom<PaginatedResult<GlobalSettingListVm>>(okResult.Value);
            Assert.Single(resultData.Data);
        }

        [Fact]
        public async Task GetGlobalSettingNames_Should_Return_List()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingNameQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingNameVms);

            var result = await _controller.GetGlobalSettingNames();
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnList = Assert.IsAssignableFrom<List<GlobalSettingNameVm>>(okResult.Value);
            Assert.Single(returnList);
        }

        [Fact]
        public async Task IsGlobalSettingNameExist_Should_Return_True()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingNameUniqueQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            var result = await _controller.IsGlobalSettingNameExist("key123", null);
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value!);
        }

        [Fact]
        public async Task IsGlobalSettingNameExist_NullKey_Should_Throw()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsGlobalSettingNameExist(null!, "some-id"));
        }

        [Fact]
        public async Task IsGlobalSettingNameExist_EmptyKey_Should_Throw()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsGlobalSettingNameExist("", "some-id"));
        }

        [Fact]
        public async Task Authentication_Should_Return_Result()
        {
            var response=new AuthenticationResponse { Message = "Deleted", Success = true };

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<AuthenticationCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.Authentication(_globalSettingsFixture.AuthenticationCommand);
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value!);
        }

        [Fact]
        public void ClearDataCache_CallsClearCache()
        {
            _controller.ClearDataCache();
            Assert.True(true); 
        }

    }
}
