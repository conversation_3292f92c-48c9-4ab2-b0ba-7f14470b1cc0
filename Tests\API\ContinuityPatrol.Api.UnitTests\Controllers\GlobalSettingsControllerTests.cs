﻿using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Authentication;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Delete;
using ContinuityPatrol.Application.Features.GlobalSetting.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetList;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNames;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GlobalSetting.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.GlobalSetting;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers
{
    public class GlobalSettingsControllerTests : IClassFixture<GlobalSettingsFixture>
    {
        private readonly GlobalSettingsController _controller;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly GlobalSettingsFixture _globalSettingsFixture;

        public GlobalSettingsControllerTests(GlobalSettingsFixture globalSettingsFixture)
        {
            _globalSettingsFixture = globalSettingsFixture;
            var testBuilder = new ControllerTestBuilder<GlobalSettingsController>();
            _controller = testBuilder.CreateController(
                _ => new GlobalSettingsController(),
                out _mediatorMock);
        }

        [Fact]
        public async Task GetGlobalSettings_Should_Return_Data()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingListVms);

            var result = await _controller.GetGlobalSettings();
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnList = Assert.IsAssignableFrom<List<GlobalSettingListVm>>(okResult.Value);
            Assert.Equal(_globalSettingsFixture.GlobalSettingListVms.Count, returnList.Count);
        }

        [Fact]
        public async Task GetGlobalSettings_Should_Return_EmptyList_When_NoData()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<GlobalSettingListVm>());

            var result = await _controller.GetGlobalSettings();

            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var globalSettingList = Assert.IsAssignableFrom<List<GlobalSettingListVm>>(okResult.Value);
            Assert.Empty(globalSettingList);
        }

        [Fact]
        public async Task GetGlobalSettingById_Should_Return_Detail()
        {
            var id = Guid.NewGuid().ToString();

            _mediatorMock
                .Setup(m => m.Send(It.Is<GetGlobalSettingDetailQuery>(q => q.Id == id), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingDetailVm);

            var result = await _controller.GetGlobalSettingById(id);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnDetail = Assert.IsAssignableFrom<GlobalSettingDetailVm>(okResult.Value);
            Assert.Equal(_globalSettingsFixture.GlobalSettingDetailVm, returnDetail);
        }

        [Fact]
        public async Task GetGlobalSettingById_NullId_Should_Throw_ArgumentNullException()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetGlobalSettingById(null!));
        }

        [Fact]
        public async Task GetGlobalSettingById_EmptyId_Should_Throw_InvalidArgumentException()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalSettingById(""));
        }

        [Fact]
        public async Task GetGlobalSettingById_InvalidGuid_Should_Throw_InvalidArgumentException()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalSettingById("invalid-guid"));
        }

        [Fact]
        public async Task CreateGlobalSetting_Should_Return_Success()
        {
            var response = new CreateGlobalSettingResponse { Message = "Created", Success = true };

            _mediatorMock
                .Setup(m => m.Send(_globalSettingsFixture.CreateGlobalSettingCommand, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.CreateGlobalSetting(_globalSettingsFixture.CreateGlobalSettingCommand);
            var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
            var createResponse = Assert.IsAssignableFrom<CreateGlobalSettingResponse>(createdResult.Value);
            Assert.True(createResponse.Success);
            Assert.Equal("Created", createResponse.Message);
        }

        [Fact]
        public async Task UpdateGlobalSetting_Should_Return_Success()
        {
            var response = new UpdateGlobalSettingResponse { Message = "Updated", Success = true };

            _mediatorMock
                .Setup(m => m.Send(_globalSettingsFixture.UpdateGlobalSettingCommand, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.UpdateGlobalSetting(_globalSettingsFixture.UpdateGlobalSettingCommand);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var updateResponse = Assert.IsAssignableFrom<UpdateGlobalSettingResponse>(okResult.Value);
            Assert.True(updateResponse.Success);
            Assert.Equal("Updated", updateResponse.Message);
        }

        [Fact]
        public async Task DeleteGlobalSetting_Should_Call_Mediator()
        {
            var id = Guid.NewGuid().ToString();
            var response = new DeleteGlobalSettingResponse { Message = "Deleted", Success = true };

            _mediatorMock
                .Setup(m => m.Send(It.Is<DeleteGlobalSettingCommand>(x => x.Id == id), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.DeleteGlobalSetting(id);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var deleteResponse = Assert.IsAssignableFrom<DeleteGlobalSettingResponse>(okResult.Value);
            Assert.True(deleteResponse.Success);
            Assert.Equal("Deleted", deleteResponse.Message);
        }

        [Fact]
        public async Task DeleteGlobalSetting_NullId_Should_Throw_ArgumentNullException()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteGlobalSetting(null!));
        }

        [Fact]
        public async Task DeleteGlobalSetting_EmptyId_Should_Throw_InvalidArgumentException()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteGlobalSetting(""));
        }

        [Fact]
        public async Task GetPaginatedGlobalSettings_Should_Return_Data()
        {
            var query = new GetGlobalSettingPaginatedListQuery();

            _mediatorMock
                .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.PaginatedGlobalSettingListVm);

            var result = await _controller.GetPaginatedGlobalSettings(query);
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var resultData = Assert.IsAssignableFrom<PaginatedResult<GlobalSettingListVm>>(okResult.Value);
            Assert.Equal(_globalSettingsFixture.PaginatedGlobalSettingListVm, resultData);
        }

        [Fact]
        public async Task GetGlobalSettingNames_Should_Return_List()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingNameQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingNameVms);

            var result = await _controller.GetGlobalSettingNames();
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnList = Assert.IsAssignableFrom<List<Application.Features.GlobalSetting.Queries.GetNames.GlobalSettingNameVm>>(okResult.Value);
            Assert.Equal(_globalSettingsFixture.GlobalSettingNameVms.Count, returnList.Count);
        }



        [Theory]
        [InlineData("SettingKey1", null)]
        [InlineData("AnotherKey", "some-guid")]
        public async Task IsGlobalSettingNameExist_Should_Return_True(string key, string? id)
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingNameUniqueQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            var result = await _controller.IsGlobalSettingNameExist(key, id);

            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value!);
        }

        [Fact]
        public async Task IsGlobalSettingNameExist_NullKey_Should_Throw_ArgumentNullException()
        {
            await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsGlobalSettingNameExist(null!, "some-id"));
        }

        [Fact]
        public async Task IsGlobalSettingNameExist_EmptyKey_Should_Throw_InvalidArgumentException()
        {
            await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsGlobalSettingNameExist("", "some-id"));
        }

        [Fact]
        public async Task Authentication_Should_Return_Result()
        {
            var response=new AuthenticationResponse { Message = "Deleted", Success = true };

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<AuthenticationCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            var result = await _controller.Authentication(_globalSettingsFixture.AuthenticationCommand);
            var okResult = Assert.IsType<OkObjectResult>(result);
            var authenResponse = Assert.IsAssignableFrom<AuthenticationResponse>(okResult.Value);
            Assert.True(authenResponse.Success);
            Assert.Equal("Deleted", authenResponse.Message);
        }

        [Fact]
        public async Task GetGlobalSettings_CallsCorrectQuery()
        {
            var queryExecuted = false;

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .Callback(() => queryExecuted = true)
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingListVms);

            await _controller.GetGlobalSettings();

            Assert.True(queryExecuted);
            _mediatorMock.Verify(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetGlobalSettings_HandlesException_WhenMediatorThrows()
        {
            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Database connection failed"));

            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _controller.GetGlobalSettings());

            Assert.Equal("Database connection failed", exception.Message);
        }

        [Fact]
        public void ClearDataCache_CallsClearCacheWithCorrectKeys()
        {
            _controller.ClearDataCache();

            Assert.True(true);
        }

        [Fact]
        public async Task GetGlobalSettings_VerifiesQueryType()
        {
            GetGlobalSettingListQuery? capturedQuery = null;

            _mediatorMock
                .Setup(m => m.Send(It.IsAny<GetGlobalSettingListQuery>(), It.IsAny<CancellationToken>()))
                .Callback<IRequest<List<GlobalSettingListVm>>, CancellationToken>((request, _) =>
                {
                    capturedQuery = request as GetGlobalSettingListQuery;
                })
                .ReturnsAsync(_globalSettingsFixture.GlobalSettingListVms);

            await _controller.GetGlobalSettings();

            Assert.NotNull(capturedQuery);
            Assert.IsType<GetGlobalSettingListQuery>(capturedQuery);
        }

    }
}
