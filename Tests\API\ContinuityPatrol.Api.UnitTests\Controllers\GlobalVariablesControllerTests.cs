using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Create;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Delete;
using ContinuityPatrol.Application.Features.GlobalVariable.Commands.Update;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetDetailByName;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetList;
using ContinuityPatrol.Application.Features.GlobalVariable.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.GlobalVariableModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class GlobalVariablesControllerTests : IClassFixture<GlobalVariableFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly GlobalVariablesController _controller;
    private readonly GlobalVariableFixture _fixture;

    public GlobalVariablesControllerTests(GlobalVariableFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<GlobalVariablesController>();
        _controller = testBuilder.CreateController(
            _ => new GlobalVariablesController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetGlobalVariables_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GlobalVariableListVm);

        var result = await _controller.GetGlobalVariables();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var globalVariableList = Assert.IsAssignableFrom<List<GlobalVariableListVm>>(okResult.Value);
        Assert.Equal(_fixture.GlobalVariableListVm.Count, globalVariableList.Count);
    }

    [Fact]
    public async Task GetGlobalVariables_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<GlobalVariableListVm>());

        var result = await _controller.GetGlobalVariables();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var globalVariableList = Assert.IsAssignableFrom<List<GlobalVariableListVm>>(okResult.Value);
        Assert.Empty(globalVariableList);
    }

    [Fact]
    public async Task GetGlobalVariableById_Should_Return_Detail()
    {
        var globalVariableId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetGlobalVariableDetailQuery>(q => q.Id == globalVariableId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GlobalVariableDetailVm);

        var result = await _controller.GetGlobalVariableById(globalVariableId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var globalVariableDetail = Assert.IsAssignableFrom<GlobalVariableDetailVm>(okResult.Value);
        Assert.NotNull(globalVariableDetail);
        Assert.Equal(_fixture.GlobalVariableDetailVm.Id, globalVariableDetail.Id);
    }

    [Fact]
    public async Task GetGlobalVariableById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetGlobalVariableById(null!));
    }

    [Fact]
    public async Task GetGlobalVariableById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalVariableById(""));
    }

    [Fact]
    public async Task GetGlobalVariableById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalVariableById("invalid-guid"));
    }

    [Fact]
    public async Task GetGlobalVariableByVariableName_Should_Return_Detail()
    {
        var variableName = "TestVariable";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetGlobalVariableDetailByNameQuery>(q => q.Name == variableName), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GlobalVariableDetailVmList);

        var result = await _controller.GetGlobalVariableByVariableName(variableName);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var globalVariableDetailList = Assert.IsAssignableFrom<List<GlobalVariableDetailVm>>(okResult.Value);
        Assert.Equal(_fixture.GlobalVariableDetailVmList.Count, globalVariableDetailList.Count);
    }

    [Fact]
    public async Task GetGlobalVariableByVariableName_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetGlobalVariableByVariableName(null!));
    }

    [Fact]
    public async Task GetGlobalVariableByVariableName_EmptyName_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGlobalVariableByVariableName(""));
    }

    [Fact]
    public async Task GetPaginatedGlobalVariables_Should_Return_Result()
    {
        var query = _fixture.GetGlobalVariablePaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedGlobalVariableListVm);

        var result = await _controller.GetPaginatedGlobalVariables(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<GlobalVariableListVm>>(okResult.Value);
        Assert.Equal(_fixture.GlobalVariableListVm.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateGlobalVariable_Should_Return_Success()
    {
        var response = new CreateGlobalVariableResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateGlobalVariableCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateGlobalVariable(_fixture.CreateGlobalVariableCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateGlobalVariableResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateGlobalVariable_Should_Return_Success()
    {
        var response = new UpdateGlobalVariableResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateGlobalVariableCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateGlobalVariable(_fixture.UpdateGlobalVariableCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateGlobalVariableResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteGlobalVariable_Should_Call_Mediator()
    {
        var globalVariableId = Guid.NewGuid().ToString();
        var response = new DeleteGlobalVariableResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteGlobalVariableCommand>(c => c.Id == globalVariableId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteGlobalVariable(globalVariableId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteGlobalVariableResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteGlobalVariable_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteGlobalVariable(null!));
    }

    [Fact]
    public async Task DeleteGlobalVariable_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteGlobalVariable(""));
    }

    [Theory]
    [InlineData("VariableName1", null)]
    [InlineData("AnotherVariable", "some-guid")]
    public async Task IsGlobalVariableNameExist_Should_Return_True(string name, string? id)
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await _controller.IsGlobalVariableNameExist(name, id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsGlobalVariableNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsGlobalVariableNameExist(null!, Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task IsGlobalVariableNameExist_EmptyName_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsGlobalVariableNameExist("", Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task GetGlobalVariables_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.GlobalVariableListVm);

        await _controller.GetGlobalVariables();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetGlobalVariables_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetGlobalVariables());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetGlobalVariables_VerifiesQueryType()
    {
        GetGlobalVariableListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGlobalVariableListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<GlobalVariableListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetGlobalVariableListQuery;
            })
            .ReturnsAsync(_fixture.GlobalVariableListVm);

        await _controller.GetGlobalVariables();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetGlobalVariableListQuery>(capturedQuery);
    }
}
