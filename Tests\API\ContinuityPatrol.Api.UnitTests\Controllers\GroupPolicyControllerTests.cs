using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Create;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Delete;
using ContinuityPatrol.Application.Features.GroupPolicy.Commands.Update;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetDetail;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetList;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class GroupPolicyControllerTests : IClassFixture<GroupPolicyFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly GroupPolicyController _controller;
    private readonly GroupPolicyFixture _fixture;

    public GroupPolicyControllerTests(GroupPolicyFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<GroupPolicyController>();
        _controller = testBuilder.CreateController(
            _ => new GroupPolicyController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetGroupPolicies_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GroupPolicyListVm);

        var result = await _controller.GetGroupPolicies();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groupPolicyList = Assert.IsAssignableFrom<List<GroupPolicyListVm>>(okResult.Value);
        Assert.Equal(_fixture.GroupPolicyListVm.Count, groupPolicyList.Count);
    }

    [Fact]
    public async Task GetGroupPolicies_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<GroupPolicyListVm>());

        var result = await _controller.GetGroupPolicies();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groupPolicyList = Assert.IsAssignableFrom<List<GroupPolicyListVm>>(okResult.Value);
        Assert.Empty(groupPolicyList);
    }

    [Fact]
    public async Task GetGroupPolicyById_Should_Return_Detail()
    {
        var groupPolicyId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetGroupPolicyDetailQuery>(q => q.Id == groupPolicyId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GroupPolicyDetailVm);

        var result = await _controller.GetGroupPolicyById(groupPolicyId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groupPolicyDetail = Assert.IsAssignableFrom<GetGroupPolicyDetailVm>(okResult.Value);
        Assert.NotNull(groupPolicyDetail);
        Assert.Equal(_fixture.GroupPolicyDetailVm.Id, groupPolicyDetail.Id);
    }

    [Fact]
    public async Task GetGroupPolicyById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetGroupPolicyById(null!));
    }

    [Fact]
    public async Task GetGroupPolicyById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGroupPolicyById(""));
    }

    [Fact]
    public async Task GetGroupPolicyById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetGroupPolicyById("invalid-guid"));
    }

    [Fact]
    public async Task GetGroupPolicyNames_Should_Return_Names()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GroupPolicyNameVmList);

        var result = await _controller.GetGroupPolicyNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groupPolicyNames = Assert.IsAssignableFrom<List<GroupPolicyNameVm>>(okResult.Value);
        Assert.Equal(_fixture.GroupPolicyNameVmList.Count, groupPolicyNames.Count);
    }

    [Fact]
    public async Task GetGroupPolicyByType_Should_Return_TypeData()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetGroupPolicyTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.GroupPolicyTypeVmList);

        var result = await _controller.GetGroupPolicyByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var groupPolicyTypes = Assert.IsAssignableFrom<List<GroupPolicyTypeVm>>(okResult.Value);
        Assert.Equal(_fixture.GroupPolicyTypeVmList.Count, groupPolicyTypes.Count);
    }

    [Fact]
    public async Task GetPaginatedGroupPolicies_Should_Return_Result()
    {
        var query = _fixture.GetGroupPolicyPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedGroupPolicyListVm);

        var result = await _controller.GetPaginatedGroupPolicies(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<GroupPolicyListVm>>(okResult.Value);
        Assert.Equal(_fixture.GroupPolicyListVm.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateGroupPolicy_Should_Return_Success()
    {
        var response = new CreateGroupPolicyResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateGroupPolicyCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateGroupPolicy(_fixture.CreateGroupPolicyCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateGroupPolicyResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateGroupPolicy_Should_Return_Success()
    {
        var response = new UpdateGroupPolicyResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateGroupPolicyCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateGroupPolicy(_fixture.UpdateGroupPolicyCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateGroupPolicyResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteGroupPolicy_Should_Call_Mediator()
    {
        var groupPolicyId = Guid.NewGuid().ToString();
        var response = new DeleteGroupPolicyResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteGroupPolicyCommand>(c => c.Id == groupPolicyId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteGroupPolicy(groupPolicyId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteGroupPolicyResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteGroupPolicy_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteGroupPolicy(null!));
    }

    [Fact]
    public async Task DeleteGroupPolicy_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteGroupPolicy(""));
    }

    [Theory]
    [InlineData("PolicyName1", null)]
    [InlineData("AnotherPolicy", "some-guid")]
    public async Task IsGroupPolicyNameExist_Should_Return_True(string name, string? id)
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await _controller.IsGroupPolicyNameExist(name, id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsGroupPolicyNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsGroupPolicyNameExist(null!, Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task IsGroupPolicyNameExist_EmptyName_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsGroupPolicyNameExist("", Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task GetGroupPolicies_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.GroupPolicyListVm);

        await _controller.GetGroupPolicies();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetGroupPolicies_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetGroupPolicies());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetGroupPolicies_VerifiesQueryType()
    {
        GetGroupPolicyListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetGroupPolicyListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<GroupPolicyListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetGroupPolicyListQuery;
            })
            .ReturnsAsync(_fixture.GroupPolicyListVm);

        await _controller.GetGroupPolicies();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetGroupPolicyListQuery>(capturedQuery);
    }
}
