using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Delete;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetList;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MenuBuildersControllerTests : IClassFixture<MenuBuilderFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MenuBuildersController _controller;
    private readonly MenuBuilderFixture _fixture;

    public MenuBuildersControllerTests(MenuBuilderFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MenuBuildersController>();
        _controller = testBuilder.CreateController(
            _ => new MenuBuildersController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetMenuBuilders_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MenuBuilderListVm);

        var result = await _controller.GetMenuBuilders();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var menuBuilderList = Assert.IsAssignableFrom<List<MenuBuilderListVm>>(okResult.Value);
        Assert.Equal(_fixture.MenuBuilderListVm.Count, menuBuilderList.Count);
    }

    [Fact]
    public async Task GetMenuBuilders_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MenuBuilderListVm>());

        var result = await _controller.GetMenuBuilders();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var menuBuilderList = Assert.IsAssignableFrom<List<MenuBuilderListVm>>(okResult.Value);
        Assert.Empty(menuBuilderList);
    }

    [Fact]
    public async Task GetMenuBuilderById_Should_Return_Detail()
    {
        var menuBuilderId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMenuBuilderDetailQuery>(q => q.Id == menuBuilderId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MenuBuilderDetailVm);

        var result = await _controller.GetMenuBuilderById(menuBuilderId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var menuBuilderDetail = Assert.IsAssignableFrom<MenuBuilderDetailVm>(okResult.Value);
        Assert.NotNull(menuBuilderDetail);
        Assert.Equal(_fixture.MenuBuilderDetailVm.Id, menuBuilderDetail.Id);
    }

    [Fact]
    public async Task GetMenuBuilderById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMenuBuilderById(null!));
    }

    [Fact]
    public async Task GetMenuBuilderById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMenuBuilderById(""));
    }

    [Fact]
    public async Task GetMenuBuilderById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMenuBuilderById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMenuBuilders_Should_Return_Result()
    {
        var query = _fixture.GetMenuBuilderPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMenuBuilderListVm);

        var result = await _controller.GetPaginatedMenuBuilders(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<MenuBuilderListVm>>(okResult.Value);
        Assert.Equal(_fixture.MenuBuilderListVm.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateMenuBuilder_Should_Return_Success()
    {
        var response = new CreateMenuBuilderResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMenuBuilderCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMenuBuilder(_fixture.CreateMenuBuilderCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMenuBuilderResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMenuBuilder_Should_Return_Success()
    {
        var response = new UpdateMenuBuilderResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMenuBuilderCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMenuBuilder(_fixture.UpdateMenuBuilderCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMenuBuilderResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteMenuBuilder_Should_Call_Mediator()
    {
        var menuBuilderId = Guid.NewGuid().ToString();
        var response = new DeleteMenuBuilderResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteMenuBuilderCommand>(c => c.Id == menuBuilderId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteMenuBuilder(menuBuilderId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteMenuBuilderResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteMenuBuilder_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteMenuBuilder(null!));
    }

    [Fact]
    public async Task DeleteMenuBuilder_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteMenuBuilder(""));
    }

    [Theory]
    [InlineData("MenuName1", null)]
    [InlineData("AnotherMenu", "some-guid")]
    public async Task IsMenuBuilderNameExist_Should_Return_True(string name, string? id)
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await _controller.IsMenuBuilderNameExist(name, id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task IsMenuBuilderNameExist_NullName_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.IsMenuBuilderNameExist(null!, Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task IsMenuBuilderNameExist_EmptyName_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.IsMenuBuilderNameExist("", Guid.NewGuid().ToString()));
    }

    [Fact]
    public async Task GetMenuBuilders_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MenuBuilderListVm);

        await _controller.GetMenuBuilders();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetMenuBuilders_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetMenuBuilders());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetMenuBuilders_VerifiesQueryType()
    {
        GetMenuBuilderListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMenuBuilderListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MenuBuilderListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMenuBuilderListQuery;
            })
            .ReturnsAsync(_fixture.MenuBuilderListVm);

        await _controller.GetMenuBuilders();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMenuBuilderListQuery>(capturedQuery);
    }
}
