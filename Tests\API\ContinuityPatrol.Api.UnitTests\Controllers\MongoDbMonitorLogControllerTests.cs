using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetList;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MongoDbMonitorLogControllerTests : IClassFixture<MongoDbMonitorLogFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MongoDbMonitorLogController _controller;
    private readonly MongoDbMonitorLogFixture _fixture;

    public MongoDbMonitorLogControllerTests(MongoDbMonitorLogFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MongoDbMonitorLogController>();
        _controller = testBuilder.CreateController(
            _ => new MongoDbMonitorLogController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorLog_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorLogListVm);

        var result = await _controller.GetAllMongoDbMonitorLog();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorLogList = Assert.IsAssignableFrom<List<MongoDBMonitorLogListVm>>(okResult.Value);
        Assert.Equal(_fixture.MongoDbMonitorLogListVm.Count, mongoDbMonitorLogList.Count);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorLog_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MongoDBMonitorLogListVm>());

        var result = await _controller.GetAllMongoDbMonitorLog();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorLogList = Assert.IsAssignableFrom<List<MongoDBMonitorLogListVm>>(okResult.Value);
        Assert.Empty(mongoDbMonitorLogList);
    }

    [Fact]
    public async Task GetMongoDbMonitorLogById_Should_Return_Detail()
    {
        var mongoDbMonitorLogId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMongoDBMonitorLogDetailQuery>(q => q.Id == mongoDbMonitorLogId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorLogDetailVm);

        var result = await _controller.GetMongoDbMonitorLogById(mongoDbMonitorLogId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorLogDetail = Assert.IsAssignableFrom<MongoDBMonitorLogDetailVm>(okResult.Value);
        Assert.NotNull(mongoDbMonitorLogDetail);
        Assert.Equal(_fixture.MongoDbMonitorLogDetailVm.Id, mongoDbMonitorLogDetail.Id);
    }

    [Fact]
    public async Task GetMongoDbMonitorLogById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMongoDbMonitorLogById(null!));
    }

    [Fact]
    public async Task GetMongoDbMonitorLogById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorLogById(""));
    }

    [Fact]
    public async Task GetMongoDbMonitorLogById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorLogById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMongoDbMonitorLog_Should_Return_Result()
    {
        var query = _fixture.GetMongoDbMonitorLogPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMongoDbMonitorLogListVm);

        var result = await _controller.GetPaginatedMongoDbMonitorLog(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MongoDBMonitorLogPaginatedListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMongoDbMonitorLogListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMongoDbMonitorLog_Should_Return_Success()
    {
        var response = new CreateMongoDBMonitorLogResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMongoDbMonitorLogCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMongoDbMonitorLog(_fixture.CreateMongoDbMonitorLogCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMongoDBMonitorLogResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task GetMongoDbMonitorLogByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMongoDBMonitorLogDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorLogDetailByTypeVm);

        var result = await _controller.GetMongoDbMonitorLogByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MongoDBMonitorLogDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMongoDbMonitorLogByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMongoDbMonitorLogByType(null!));
    }

    [Fact]
    public async Task GetMongoDbMonitorLogByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorLogByType(""));
    }

    [Fact]
    public async Task GetAllMongoDbMonitorLog_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MongoDbMonitorLogListVm);

        await _controller.GetAllMongoDbMonitorLog();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorLog_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMongoDbMonitorLog());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorLog_VerifiesQueryType()
    {
        GetMongoDBMonitorLogListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMongoDBMonitorLogListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MongoDBMonitorLogListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMongoDBMonitorLogListQuery;
            })
            .ReturnsAsync(_fixture.MongoDbMonitorLogListVm);

        await _controller.GetAllMongoDbMonitorLog();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMongoDBMonitorLogListQuery>(capturedQuery);
    }
}
