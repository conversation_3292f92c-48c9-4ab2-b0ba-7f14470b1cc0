using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetMongoDbMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MongoDbMonitorStatusControllerTests : IClassFixture<MongoDbMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MongoDbMonitorStatusController _controller;
    private readonly MongoDbMonitorStatusFixture _fixture;

    public MongoDbMonitorStatusControllerTests(MongoDbMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MongoDbMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new MongoDbMonitorStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorStatus_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorStatusListVm);

        var result = await _controller.GetAllMongoDbMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorStatusList = Assert.IsAssignableFrom<List<MongoDbMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.MongoDbMonitorStatusListVm.Count, mongoDbMonitorStatusList.Count);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorStatus_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MongoDbMonitorStatusListVm>());

        var result = await _controller.GetAllMongoDbMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorStatusList = Assert.IsAssignableFrom<List<MongoDbMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(mongoDbMonitorStatusList);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusById_Should_Return_Detail()
    {
        var mongoDbMonitorStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<MongoDbMonitorStatusDetailQuery>(q => q.Id == mongoDbMonitorStatusId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorStatusDetailVm);

        var result = await _controller.GetMongoDbMonitorStatusById(mongoDbMonitorStatusId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mongoDbMonitorStatusDetail = Assert.IsAssignableFrom<MongoDbMonitorStatusDetailVm>(okResult.Value);
        Assert.NotNull(mongoDbMonitorStatusDetail);
        Assert.Equal(_fixture.MongoDbMonitorStatusDetailVm.Id, mongoDbMonitorStatusDetail.Id);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMongoDbMonitorStatusById(null!));
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorStatusById(""));
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMongoDbMonitorStatus_Should_Return_Result()
    {
        var query = _fixture.GetMongoDbMonitorStatusPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMongoDbMonitorStatusListVm);

        var result = await _controller.GetPaginatedMongoDbMonitorStatus(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MongoDbMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMongoDbMonitorStatusListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMongoDbMonitorStatus_Should_Return_Success()
    {
        var response = new CreateMongoDbMonitorStatusResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMongoDbMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMongoDbMonitorStatus(_fixture.CreateMongoDbMonitorStatusCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMongoDbMonitorStatusResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMongoDbMonitorStatus_Should_Return_Success()
    {
        var response = new UpdateMongoDbMonitorStatusResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMongoDbMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMongoDbMonitorStatus(_fixture.UpdateMongoDbMonitorStatusCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMongoDbMonitorStatusResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<MongoDbMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MongoDbMonitorStatusDetailByTypeVm);

        var result = await _controller.GetMongoDbMonitorStatusByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MongoDbMonitorStatusDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMongoDbMonitorStatusByType(null!));
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorStatusByType(""));
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMongoDbMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-result");

        var result = await _controller.GetMongoDbMonitorStatusByInfraObjectId(infraObjectId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectResult = Assert.IsAssignableFrom<string>(okResult.Value);
        Assert.Equal("test-result", infraObjectResult);
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMongoDbMonitorStatusByInfraObjectId(null!));
    }

    [Fact]
    public async Task GetMongoDbMonitorStatusByInfraObjectId_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMongoDbMonitorStatusByInfraObjectId(""));
    }

    [Fact]
    public async Task GetAllMongoDbMonitorStatus_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MongoDbMonitorStatusListVm);

        await _controller.GetAllMongoDbMonitorStatus();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorStatus_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMongoDbMonitorStatus());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMongoDbMonitorStatus_VerifiesQueryType()
    {
        MongoDbMonitorStatusListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MongoDbMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MongoDbMonitorStatusListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as MongoDbMonitorStatusListQuery;
            })
            .ReturnsAsync(_fixture.MongoDbMonitorStatusListVm);

        await _controller.GetAllMongoDbMonitorStatus();

        Assert.NotNull(capturedQuery);
        Assert.IsType<MongoDbMonitorStatusListQuery>(capturedQuery);
    }
}
