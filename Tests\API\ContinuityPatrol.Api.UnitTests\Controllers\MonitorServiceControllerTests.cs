using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MonitorService.Command.Create;
using ContinuityPatrol.Application.Features.MonitorService.Command.Delete;
using ContinuityPatrol.Application.Features.MonitorService.Command.Update;
using ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetByInfraObjectIdAndBusinessServiceId;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetList;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetNames;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetNameUnique;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MonitorServiceControllerTests : IClassFixture<MonitorServiceFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MonitorServiceController _controller;
    private readonly MonitorServiceFixture _fixture;

    public MonitorServiceControllerTests(MonitorServiceFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MonitorServiceController>();
        _controller = testBuilder.CreateController(
            _ => new MonitorServiceController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetMonitorServices_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MonitorServiceListVm);

        var result = await _controller.GetMonitorServices();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var monitorServiceList = Assert.IsAssignableFrom<List<MonitorServiceListVm>>(okResult.Value);
        Assert.Equal(_fixture.MonitorServiceListVm.Count, monitorServiceList.Count);
    }

    [Fact]
    public async Task GetMonitorServices_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MonitorServiceListVm>());

        var result = await _controller.GetMonitorServices();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var monitorServiceList = Assert.IsAssignableFrom<List<MonitorServiceListVm>>(okResult.Value);
        Assert.Empty(monitorServiceList);
    }

    [Fact]
    public async Task GetMonitorServiceById_Should_Return_Detail()
    {
        var monitorServiceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMonitorServiceDetailQuery>(q => q.Id == monitorServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MonitorServiceDetailVm);

        var result = await _controller.GetMonitorServiceById(monitorServiceId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var monitorServiceDetail = Assert.IsAssignableFrom<GetMonitorServiceDetailVm>(okResult.Value);
        Assert.NotNull(monitorServiceDetail);
        Assert.Equal(_fixture.MonitorServiceDetailVm.Id, monitorServiceDetail.Id);
    }

    [Fact]
    public async Task GetMonitorServiceById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMonitorServiceById(null!));
    }

    [Fact]
    public async Task GetMonitorServiceById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMonitorServiceById(""));
    }

    [Fact]
    public async Task GetMonitorServiceById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMonitorServiceById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMonitorServices_Should_Return_Result()
    {
        var query = _fixture.GetMonitorServicePaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMonitorServiceListVm);

        var result = await _controller.GetPaginatedMonitorServices(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<GetMonitorServicePaginatedListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMonitorServiceListVm.Data.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateMonitorService_Should_Return_Success()
    {
        var response = new CreateMonitorServiceResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMonitorServiceCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMonitorService(_fixture.CreateMonitorServiceCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMonitorServiceResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMonitorService_Should_Return_Success()
    {
        var response = new UpdateMonitorServiceResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMonitorServiceCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMonitorService(_fixture.UpdateMonitorServiceCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMonitorServiceResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task DeleteMonitorService_Should_Call_Mediator()
    {
        var monitorServiceId = Guid.NewGuid().ToString();
        var response = new DeleteMonitorServiceResponse
        {
            Message = "Deleted",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteMonitorServiceCommand>(c => c.Id == monitorServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.DeleteMonitorService(monitorServiceId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var deleteResponse = Assert.IsAssignableFrom<DeleteMonitorServiceResponse>(okResult.Value);
        Assert.True(deleteResponse.Success);
        Assert.Equal("Deleted", deleteResponse.Message);
    }

    [Fact]
    public async Task DeleteMonitorService_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.DeleteMonitorService(null!));
    }

    [Fact]
    public async Task DeleteMonitorService_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.DeleteMonitorService(""));
    }

    [Fact]
    public async Task GetMonitorServiceNames_Should_Return_Names()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MonitorServiceNameVmList);

        var result = await _controller.GetMonitorServiceNames();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var monitorServiceNames = Assert.IsAssignableFrom<List<GetMonitorServiceNameVm>>(okResult.Value);
        Assert.Equal(_fixture.MonitorServiceNameVmList.Count, monitorServiceNames.Count);
    }

    [Fact]
    public async Task GetByInfraObjectIdAndBusinessServiceId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();
        var businessServiceId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByInfraObjectIdAndBusinessServiceIdQuery>(q => 
                q.InfraObjectId == infraObjectId && q.BusinessServiceId == businessServiceId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MonitorServiceListVm);

        var result = await _controller.GetByInfraObjectIdAndBusinessServiceId(infraObjectId, businessServiceId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var monitorServiceList = Assert.IsAssignableFrom<List<MonitorServiceListVm>>(okResult.Value);
        Assert.Equal(_fixture.MonitorServiceListVm.Count, monitorServiceList.Count);
    }

    [Fact]
    public async Task UpdateMonitorStatus_Should_Return_Success()
    {
        var response = new UpdateMonitorServiceStatusResponse
        {
            Message = "Status Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMonitorServiceStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMonitorStatus(_fixture.UpdateMonitorServiceStatusCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMonitorServiceStatusResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Status Updated", updateResponse.Message);
    }

    [Theory]
    [InlineData("ServicePath1", "infraId1", "serverId1", "type1", null, null, null, null, null)]
    [InlineData("ServicePath2", "infraId2", "serverId2", "type2", "threadType", "workflowId", "workflowType", "WorkflowName", "some-guid")]
    public async Task IsMonitorServiceNameExist_Should_Return_True(string servicePath, string infraObjectId, string serverId, string type, string? threadType, string? workflowId, string? workflowType, string? workflowName, string? id)
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceNameUniqueQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var result = await _controller.IsMonitorServiceNameExist(servicePath, infraObjectId, serverId, type, threadType, workflowId, workflowType, workflowName, id);

        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task GetMonitorServices_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MonitorServiceListVm);

        await _controller.GetMonitorServices();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetMonitorServices_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetMonitorServices());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetMonitorServices_VerifiesQueryType()
    {
        GetMonitorServiceListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMonitorServiceListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MonitorServiceListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMonitorServiceListQuery;
            })
            .ReturnsAsync(_fixture.MonitorServiceListVm);

        await _controller.GetMonitorServices();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMonitorServiceListQuery>(capturedQuery);
    }
}
