using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MsSqlNativeLogShippingMonitorStatusControllerTests : IClassFixture<MsSqlNativeLogShippingMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MsSqlNativeLogShippingMonitorStatusController _controller;
    private readonly MsSqlNativeLogShippingMonitorStatusFixture _fixture;

    public MsSqlNativeLogShippingMonitorStatusControllerTests(MsSqlNativeLogShippingMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MsSqlNativeLogShippingMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new MsSqlNativeLogShippingMonitorStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMsSqlNativeLogShippingMonitorStatus_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MsSqlNativeLogShippingMonitorStatusListVm);

        var result = await _controller.GetAllMsSqlNativeLogShippingMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var msSqlNativeLogShippingMonitorStatusList = Assert.IsAssignableFrom<List<MsSqlNativeLogShippingMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.MsSqlNativeLogShippingMonitorStatusListVm.Count, msSqlNativeLogShippingMonitorStatusList.Count);
    }

    [Fact]
    public async Task GetAllMsSqlNativeLogShippingMonitorStatus_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MsSqlNativeLogShippingMonitorStatusListVm>());

        var result = await _controller.GetAllMsSqlNativeLogShippingMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var msSqlNativeLogShippingMonitorStatusList = Assert.IsAssignableFrom<List<MsSqlNativeLogShippingMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(msSqlNativeLogShippingMonitorStatusList);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusById_Should_Return_Detail()
    {
        var msSqlNativeLogShippingMonitorStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<MsSqlNativeLogShippingMonitorStatusDetailQuery>(q => q.Id == msSqlNativeLogShippingMonitorStatusId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MsSqlNativeLogShippingMonitorStatusDetailVm);

        var result = await _controller.GetMsSqlNativeLogShippingMonitorStatusById(msSqlNativeLogShippingMonitorStatusId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var msSqlNativeLogShippingMonitorStatusDetail = Assert.IsAssignableFrom<MsSqlNativeLogShippingMonitorStatusDetailVm>(okResult.Value);
        Assert.NotNull(msSqlNativeLogShippingMonitorStatusDetail);
        Assert.Equal(_fixture.MsSqlNativeLogShippingMonitorStatusDetailVm.Id, msSqlNativeLogShippingMonitorStatusDetail.Id);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusById(null!));
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusById(""));
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMsSqlNativeLogShippingMonitorStatus_Should_Return_Result()
    {
        var query = _fixture.GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMsSqlNativeLogShippingMonitorStatusListVm);

        var result = await _controller.GetPaginatedMsSqlNativeLogShippingMonitorStatus(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MsSqlNativeLogShippingMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMsSqlNativeLogShippingMonitorStatusListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMsSqlNativeLogShippingMonitorStatus_Should_Return_Success()
    {
        var response = new CreateMssqlNativeLogShippingMonitorStatusResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMsSqlNativeLogShippingMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMsSqlNativeLogShippingMonitorStatus(_fixture.CreateMsSqlNativeLogShippingMonitorStatusCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMssqlNativeLogShippingMonitorStatusResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMsSqlNativeLogShippingMonitorStatus_Should_Return_Success()
    {
        var response = new UpdateMssqlNativeLogShippingMonitorStatusResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMsSqlNativeLogShippingMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMsSqlNativeLogShippingMonitorStatus(_fixture.UpdateMsSqlNativeLogShippingMonitorStatusCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMssqlNativeLogShippingMonitorStatusResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMsSqlNativeLogShippingMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MsSqlNativeLogShippingMonitorStatusDetailByTypeVm);

        var result = await _controller.GetMsSqlNativeLogShippingMonitorStatusByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusByType(null!));
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusByType(""));
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMsSqlNativeLogShippingMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-result");

        var result = await _controller.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(infraObjectId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectResult = Assert.IsAssignableFrom<string>(okResult.Value);
        Assert.Equal("test-result", infraObjectResult);
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(null!));
    }

    [Fact]
    public async Task GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMsSqlNativeLogShippingMonitorStatusByInfraObjectId(""));
    }

    [Fact]
    public async Task GetAllMsSqlNativeLogShippingMonitorStatus_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MsSqlNativeLogShippingMonitorStatusListVm);

        await _controller.GetAllMsSqlNativeLogShippingMonitorStatus();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMsSqlNativeLogShippingMonitorStatus_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMsSqlNativeLogShippingMonitorStatus());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMsSqlNativeLogShippingMonitorStatus_VerifiesQueryType()
    {
        MsSqlNativeLogShippingMonitorStatusListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<MsSqlNativeLogShippingMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MsSqlNativeLogShippingMonitorStatusListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as MsSqlNativeLogShippingMonitorStatusListQuery;
            })
            .ReturnsAsync(_fixture.MsSqlNativeLogShippingMonitorStatusListVm);

        await _controller.GetAllMsSqlNativeLogShippingMonitorStatus();

        Assert.NotNull(capturedQuery);
        Assert.IsType<MsSqlNativeLogShippingMonitorStatusListQuery>(capturedQuery);
    }
}
