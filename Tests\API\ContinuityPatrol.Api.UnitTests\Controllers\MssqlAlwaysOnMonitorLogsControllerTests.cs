using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MssqlAlwaysOnMonitorLogsControllerTests : IClassFixture<MssqlAlwaysOnMonitorLogsFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MssqlAlwaysOnMonitorLogsController _controller;
    private readonly MssqlAlwaysOnMonitorLogsFixture _fixture;

    public MssqlAlwaysOnMonitorLogsControllerTests(MssqlAlwaysOnMonitorLogsFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MssqlAlwaysOnMonitorLogsController>();
        _controller = testBuilder.CreateController(
            _ => new MssqlAlwaysOnMonitorLogsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorLogs_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorLogsListVm);

        var result = await _controller.GetAllMSSQLAlwaysOnMonitorLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorLogsList = Assert.IsAssignableFrom<List<MssqlAlwaysOnMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(_fixture.MssqlAlwaysOnMonitorLogsListVm.Count, mssqlAlwaysOnMonitorLogsList.Count);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorLogs_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MssqlAlwaysOnMonitorLogsListVm>());

        var result = await _controller.GetAllMSSQLAlwaysOnMonitorLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorLogsList = Assert.IsAssignableFrom<List<MssqlAlwaysOnMonitorLogsListVm>>(okResult.Value);
        Assert.Empty(mssqlAlwaysOnMonitorLogsList);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsById_Should_Return_Detail()
    {
        var mssqlAlwaysOnMonitorLogsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLAlwaysOnMonitorLogsDetailQuery>(q => q.Id == mssqlAlwaysOnMonitorLogsId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorLogsDetailVm);

        var result = await _controller.GetMSSQLAlwaysOnMonitorLogsById(mssqlAlwaysOnMonitorLogsId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorLogsDetail = Assert.IsAssignableFrom<MSSQLAlwaysOnMonitorLogsDetailVm>(okResult.Value);
        Assert.NotNull(mssqlAlwaysOnMonitorLogsDetail);
        Assert.Equal(_fixture.MssqlAlwaysOnMonitorLogsDetailVm.Id, mssqlAlwaysOnMonitorLogsDetail.Id);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMSSQLAlwaysOnMonitorLogsById(null!));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorLogsById(""));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorLogsById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMSSQLAlwaysOnMonitorLogs_Should_Return_Result()
    {
        var query = _fixture.GetMssqlAlwaysOnMonitorLogsPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMssqlAlwaysOnMonitorLogsListVm);

        var result = await _controller.GetPaginatedMSSQLAlwaysOnMonitorLogs(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(_fixture.MssqlAlwaysOnMonitorLogsListVm.Count, paginatedResult.Data.Count);
    }

    [Fact]
    public async Task CreateMSSQLAlwaysOnMonitorLog_Should_Return_Success()
    {
        var response = new CreateMSSQLAlwaysOnMonitorLogResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMssqlAlwaysOnMonitorLogCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMSSQLAlwaysOnMonitorLog(_fixture.CreateMssqlAlwaysOnMonitorLogCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMSSQLAlwaysOnMonitorLogResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLAlwaysOnMonitorLogsDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorLogsDetailByTypeVm);

        var result = await _controller.GetMSSQLAlwaysOnMonitorLogsByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MSSQLAlwaysOnMonitorLogsDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMSSQLAlwaysOnMonitorLogsByType(null!));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorLogsByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorLogsByType(""));
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorLogs_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorLogsListVm);

        await _controller.GetAllMSSQLAlwaysOnMonitorLogs();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorLogs_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMSSQLAlwaysOnMonitorLogs());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorLogs_VerifiesQueryType()
    {
        GetMSSQLAlwaysOnMonitorLogsListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MssqlAlwaysOnMonitorLogsListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMSSQLAlwaysOnMonitorLogsListQuery;
            })
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorLogsListVm);

        await _controller.GetAllMSSQLAlwaysOnMonitorLogs();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMSSQLAlwaysOnMonitorLogsListQuery>(capturedQuery);
    }
}
