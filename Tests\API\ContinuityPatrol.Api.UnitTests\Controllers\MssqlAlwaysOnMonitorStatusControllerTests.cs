using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MssqlAlwaysOnMonitorStatusControllerTests : IClassFixture<MssqlAlwaysOnMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MssqlAlwaysOnMonitorStatusController _controller;
    private readonly MssqlAlwaysOnMonitorStatusFixture _fixture;

    public MssqlAlwaysOnMonitorStatusControllerTests(MssqlAlwaysOnMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MssqlAlwaysOnMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new MssqlAlwaysOnMonitorStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorStatus_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorStatusListVm);

        var result = await _controller.GetAllMSSQLAlwaysOnMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorStatusList = Assert.IsAssignableFrom<List<MSSQLAlwaysOnMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.MssqlAlwaysOnMonitorStatusListVm.Count, mssqlAlwaysOnMonitorStatusList.Count);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorStatus_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MSSQLAlwaysOnMonitorStatusListVm>());

        var result = await _controller.GetAllMSSQLAlwaysOnMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorStatusList = Assert.IsAssignableFrom<List<MSSQLAlwaysOnMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(mssqlAlwaysOnMonitorStatusList);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusById_Should_Return_Detail()
    {
        var mssqlAlwaysOnMonitorStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLAlwaysOnMonitorStatusDetailQuery>(q => q.Id == mssqlAlwaysOnMonitorStatusId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorStatusDetailVm);

        var result = await _controller.GetMSSQLAlwaysOnMonitorStatusById(mssqlAlwaysOnMonitorStatusId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlAlwaysOnMonitorStatusDetail = Assert.IsAssignableFrom<MSSQLAlwaysOnMonitorStatusDetailVm>(okResult.Value);
        Assert.NotNull(mssqlAlwaysOnMonitorStatusDetail);
        Assert.Equal(_fixture.MssqlAlwaysOnMonitorStatusDetailVm.Id, mssqlAlwaysOnMonitorStatusDetail.Id);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMSSQLAlwaysOnMonitorStatusById(null!));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorStatusById(""));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMSSQLAlwaysOnMonitorStatus_Should_Return_Result()
    {
        var query = _fixture.GetMssqlAlwaysOnMonitorStatusPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMssqlAlwaysOnMonitorStatusListVm);

        var result = await _controller.GetPaginatedMSSQLAlwaysOnMonitorStatus(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MSSQLAlwaysOnMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMssqlAlwaysOnMonitorStatusListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMSSQLAlwaysOnMonitorStatus_Should_Return_Success()
    {
        var response = new CreateMSSQLAlwaysOnMonitorStatusResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMssqlAlwaysOnMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMSSQLAlwaysOnMonitorStatus(_fixture.CreateMssqlAlwaysOnMonitorStatusCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMSSQLAlwaysOnMonitorStatusResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLAlwaysOnMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorStatusDetailByTypeVm);

        var result = await _controller.GetMSSQLAlwaysOnMonitorStatusByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MSSQLAlwaysOnMonitorStatusDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMSSQLAlwaysOnMonitorStatusByType(null!));
    }

    [Fact]
    public async Task GetMSSQLAlwaysOnMonitorStatusByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMSSQLAlwaysOnMonitorStatusByType(""));
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorStatus_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorStatusListVm);

        await _controller.GetAllMSSQLAlwaysOnMonitorStatus();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorStatus_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMSSQLAlwaysOnMonitorStatus());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMSSQLAlwaysOnMonitorStatus_VerifiesQueryType()
    {
        GetMSSQLAlwaysOnMonitorStatusListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLAlwaysOnMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MSSQLAlwaysOnMonitorStatusListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMSSQLAlwaysOnMonitorStatusListQuery;
            })
            .ReturnsAsync(_fixture.MssqlAlwaysOnMonitorStatusListVm);

        await _controller.GetAllMSSQLAlwaysOnMonitorStatus();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMSSQLAlwaysOnMonitorStatusListQuery>(capturedQuery);
    }
}
