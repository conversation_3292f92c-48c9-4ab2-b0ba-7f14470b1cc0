using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MssqlMonitorLogsControllerTests : IClassFixture<MssqlMonitorLogsFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MssqlMonitorLogsController _controller;
    private readonly MssqlMonitorLogsFixture _fixture;

    public MssqlMonitorLogsControllerTests(MssqlMonitorLogsFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MssqlMonitorLogsController>();
        _controller = testBuilder.CreateController(
            _ => new MssqlMonitorLogsController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMssqlMonitorLogs_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorLogsListVm);

        var result = await _controller.GetAllMssqlMonitorLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorLogsList = Assert.IsAssignableFrom<List<MSSQLMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(_fixture.MssqlMonitorLogsListVm.Count, mssqlMonitorLogsList.Count);
    }

    [Fact]
    public async Task GetAllMssqlMonitorLogs_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MSSQLMonitorLogsListVm>());

        var result = await _controller.GetAllMssqlMonitorLogs();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorLogsList = Assert.IsAssignableFrom<List<MSSQLMonitorLogsListVm>>(okResult.Value);
        Assert.Empty(mssqlMonitorLogsList);
    }

    [Fact]
    public async Task GetMssqlMonitorLogsById_Should_Return_Detail()
    {
        var mssqlMonitorLogsId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLMonitorLogsDetailQuery>(q => q.Id == mssqlMonitorLogsId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorLogsDetailVm);

        var result = await _controller.GetMssqlMonitorLogsById(mssqlMonitorLogsId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorLogsDetail = Assert.IsAssignableFrom<MSSQLMonitorLogsDetailVm>(okResult.Value);
        Assert.NotNull(mssqlMonitorLogsDetail);
        Assert.Equal(_fixture.MssqlMonitorLogsDetailVm.Id, mssqlMonitorLogsDetail.Id);
    }

    [Fact]
    public async Task GetMssqlMonitorLogsById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMssqlMonitorLogsById(null!));
    }

    [Fact]
    public async Task GetMssqlMonitorLogsById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorLogsById(""));
    }

    [Fact]
    public async Task GetMssqlMonitorLogsById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorLogsById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMssqlMonitorLogs_Should_Return_Result()
    {
        var query = _fixture.GetMssqlMonitorLogsPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMssqlMonitorLogsListVm);

        var result = await _controller.GetPaginatedMssqlMonitorLogs(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MSSQLMonitorLogsDetailVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMssqlMonitorLogsListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMssqlMonitorLog_Should_Return_Success()
    {
        var response = new CreateMSSQLMonitorLogResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMssqlMonitorLogCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMssqlMonitorLog(_fixture.CreateMssqlMonitorLogCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMSSQLMonitorLogResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task GetMssqlMonitorLogsByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLMonitorLogsDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorLogsDetailByTypeVm);

        var result = await _controller.GetMssqlMonitorLogsByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MSSQLMonitorLogsDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMssqlMonitorLogsByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMssqlMonitorLogsByType(null!));
    }

    [Fact]
    public async Task GetMssqlMonitorLogsByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorLogsByType(""));
    }

    [Fact]
    public async Task GetAllMssqlMonitorLogs_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MssqlMonitorLogsListVm);

        await _controller.GetAllMssqlMonitorLogs();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMssqlMonitorLogs_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMssqlMonitorLogs());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMssqlMonitorLogs_VerifiesQueryType()
    {
        GetMSSQLMonitorLogsListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MSSQLMonitorLogsListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMSSQLMonitorLogsListQuery;
            })
            .ReturnsAsync(_fixture.MssqlMonitorLogsListVm);

        await _controller.GetAllMssqlMonitorLogs();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMSSQLMonitorLogsListQuery>(capturedQuery);
    }
}
