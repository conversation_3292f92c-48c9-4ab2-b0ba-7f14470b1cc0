using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetByType;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetList;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetMSSQLMonitorStatusByInfraObjectId;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class MssqlMonitorStatusControllerTests : IClassFixture<MssqlMonitorStatusFixture>
{
    private readonly Mock<IMediator> _mediatorMock;
    private readonly MssqlMonitorStatusController _controller;
    private readonly MssqlMonitorStatusFixture _fixture;

    public MssqlMonitorStatusControllerTests(MssqlMonitorStatusFixture fixture)
    {
        _fixture = fixture;
        var testBuilder = new ControllerTestBuilder<MssqlMonitorStatusController>();
        _controller = testBuilder.CreateController(
            _ => new MssqlMonitorStatusController(),
            out _mediatorMock);
    }

    [Fact]
    public async Task GetAllMssqlMonitorStatus_Should_Return_Data()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorStatusListVm);

        var result = await _controller.GetAllMssqlMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorStatusList = Assert.IsAssignableFrom<List<MSSQLMonitorStatusListVm>>(okResult.Value);
        Assert.Equal(_fixture.MssqlMonitorStatusListVm.Count, mssqlMonitorStatusList.Count);
    }

    [Fact]
    public async Task GetAllMssqlMonitorStatus_Should_Return_EmptyList_When_NoData()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<MSSQLMonitorStatusListVm>());

        var result = await _controller.GetAllMssqlMonitorStatus();

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorStatusList = Assert.IsAssignableFrom<List<MSSQLMonitorStatusListVm>>(okResult.Value);
        Assert.Empty(mssqlMonitorStatusList);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusById_Should_Return_Detail()
    {
        var mssqlMonitorStatusId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLMonitorStatusDetailQuery>(q => q.Id == mssqlMonitorStatusId), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorStatusDetailVm);

        var result = await _controller.GetMssqlMonitorStatusById(mssqlMonitorStatusId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var mssqlMonitorStatusDetail = Assert.IsAssignableFrom<MSSQLMonitorStatusDetailVm>(okResult.Value);
        Assert.NotNull(mssqlMonitorStatusDetail);
        Assert.Equal(_fixture.MssqlMonitorStatusDetailVm.Id, mssqlMonitorStatusDetail.Id);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusById_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMssqlMonitorStatusById(null!));
    }

    [Fact]
    public async Task GetMssqlMonitorStatusById_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorStatusById(""));
    }

    [Fact]
    public async Task GetMssqlMonitorStatusById_InvalidGuid_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorStatusById("invalid-guid"));
    }

    [Fact]
    public async Task GetPaginatedMssqlMonitorStatus_Should_Return_Result()
    {
        var query = _fixture.GetMssqlMonitorStatusPaginatedListQuery;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.PaginatedMssqlMonitorStatusListVm);

        var result = await _controller.GetPaginatedMssqlMonitorStatus(query);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var paginatedResult = Assert.IsAssignableFrom<List<MSSQLMonitorStatusDetailVm>>(okResult.Value);
        Assert.Equal(_fixture.PaginatedMssqlMonitorStatusListVm.Count, paginatedResult.Count);
    }

    [Fact]
    public async Task CreateMssqlMonitorStatus_Should_Return_Success()
    {
        var response = new CreateMSSQLMonitorStatusResponse
        {
            Message = "Created",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.CreateMssqlMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.CreateMssqlMonitorStatus(_fixture.CreateMssqlMonitorStatusCommand);

        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var createResponse = Assert.IsAssignableFrom<CreateMSSQLMonitorStatusResponse>(createdResult.Value);
        Assert.True(createResponse.Success);
        Assert.Equal("Created", createResponse.Message);
    }

    [Fact]
    public async Task UpdateMssqlMonitorStatus_Should_Return_Success()
    {
        var response = new UpdateMSSQLMonitorStatusResponse
        {
            Message = "Updated",
            Success = true
        };

        _mediatorMock
            .Setup(m => m.Send(_fixture.UpdateMssqlMonitorStatusCommand, It.IsAny<CancellationToken>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateMssqlMonitorStatus(_fixture.UpdateMssqlMonitorStatusCommand);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var updateResponse = Assert.IsAssignableFrom<UpdateMSSQLMonitorStatusResponse>(okResult.Value);
        Assert.True(updateResponse.Success);
        Assert.Equal("Updated", updateResponse.Message);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByType_Should_Return_Data()
    {
        var type = "TestType";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLMonitorStatusDetailByTypeQuery>(q => q.Type == type), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_fixture.MssqlMonitorStatusDetailByTypeVm);

        var result = await _controller.GetMssqlMonitorStatusByType(type);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var typeResult = Assert.IsAssignableFrom<MSSQLMonitorStatusDetailByTypeVm>(okResult.Value);
        Assert.NotNull(typeResult);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByType_NullType_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMssqlMonitorStatusByType(null!));
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByType_EmptyType_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorStatusByType(""));
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_Should_Return_Data()
    {
        var infraObjectId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetMSSQLMonitorStatusByInfraObjectIdQuery>(q => q.InfraObjectId == infraObjectId), It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-result");

        var result = await _controller.GetMssqlMonitorStatusByInfraObjectId(infraObjectId);

        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var infraObjectResult = Assert.IsAssignableFrom<string>(okResult.Value);
        Assert.Equal("test-result", infraObjectResult);
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_NullId_Should_Throw_ArgumentNullException()
    {
        await Assert.ThrowsAsync<ArgumentNullException>(() => _controller.GetMssqlMonitorStatusByInfraObjectId(null!));
    }

    [Fact]
    public async Task GetMssqlMonitorStatusByInfraObjectId_EmptyId_Should_Throw_InvalidArgumentException()
    {
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _controller.GetMssqlMonitorStatusByInfraObjectId(""));
    }

    [Fact]
    public async Task GetAllMssqlMonitorStatus_CallsCorrectQuery()
    {
        var queryExecuted = false;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback(() => queryExecuted = true)
            .ReturnsAsync(_fixture.MssqlMonitorStatusListVm);

        await _controller.GetAllMssqlMonitorStatus();

        Assert.True(queryExecuted);
        _mediatorMock.Verify(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAllMssqlMonitorStatus_HandlesException_WhenMediatorThrows()
    {
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _controller.GetAllMssqlMonitorStatus());

        Assert.Equal("Database connection failed", exception.Message);
    }

    [Fact]
    public void ClearDataCache_CallsClearCacheWithCorrectKeys()
    {
        _controller.ClearDataCache();

        Assert.True(true);
    }

    [Fact]
    public async Task GetAllMssqlMonitorStatus_VerifiesQueryType()
    {
        GetMSSQLMonitorStatusListQuery? capturedQuery = null;

        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetMSSQLMonitorStatusListQuery>(), It.IsAny<CancellationToken>()))
            .Callback<IRequest<List<MSSQLMonitorStatusListVm>>, CancellationToken>((request, _) =>
            {
                capturedQuery = request as GetMSSQLMonitorStatusListQuery;
            })
            .ReturnsAsync(_fixture.MssqlMonitorStatusListVm);

        await _controller.GetAllMssqlMonitorStatus();

        Assert.NotNull(capturedQuery);
        Assert.IsType<GetMSSQLMonitorStatusListQuery>(capturedQuery);
    }
}
