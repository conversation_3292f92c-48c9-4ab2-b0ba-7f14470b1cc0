using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PageWidgetsControllerTests : IClassFixture<PageWidgetFixture>
{
    private readonly PageWidgetFixture _pageWidgetFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly PageWidgetsController _controller;

    public PageWidgetsControllerTests(PageWidgetFixture pageWidgetFixture)
    {
        _pageWidgetFixture = pageWidgetFixture;
        
        var testBuilder = new ControllerTestBuilder<PageWidgetsController>();
        _controller = testBuilder.CreateController(
            _ => new PageWidgetsController(),
            out _mediatorMock);
    }

    #region GetPageWidgets Tests

    [Fact]
    public async Task GetPageWidgets_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageWidgetListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pageWidgetFixture.PageWidgetListVm);

        // Act
        var result = await _controller.GetPageWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pageWidgets = Assert.IsAssignableFrom<List<PageWidgetListVm>>(okResult.Value);
        Assert.Equal(5, pageWidgets.Count);
        Assert.All(pageWidgets, pw => Assert.NotNull(pw.Name));
        Assert.All(pageWidgets, pw => Assert.NotNull(pw.Properties));
        Assert.All(pageWidgets, pw => Assert.NotNull(pw.Id));
    }

    [Fact]
    public async Task GetPageWidgets_ReturnsEmptyList_WhenNoPageWidgetsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageWidgetListQuery>(), default))
            .ReturnsAsync(new List<PageWidgetListVm>());

        // Act
        var result = await _controller.GetPageWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<PageWidgetListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetPageWidgets_ReturnsPageWidgetsWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPageWidgetListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pageWidgetFixture.PageWidgetListVm);

        // Act
        var result = await _controller.GetPageWidgets();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pageWidgets = Assert.IsAssignableFrom<List<PageWidgetListVm>>(okResult.Value);
        
        // Verify different widget types
        Assert.Contains(pageWidgets, pw => pw.Name == "System Performance Widget");
        Assert.Contains(pageWidgets, pw => pw.Name == "Disk Usage Widget");
        Assert.Contains(pageWidgets, pw => pw.Name == "Network Traffic Widget");
        Assert.Contains(pageWidgets, pw => pw.Name == "Alert Summary Widget");
        Assert.Contains(pageWidgets, pw => pw.Name == "Server Status Widget");
        
        // Verify specific widget details
        var performanceWidget = pageWidgets.First(pw => pw.Name == "System Performance Widget");
        Assert.Contains("chart", performanceWidget.Properties);
        Assert.Contains("line", performanceWidget.Properties);
        Assert.Contains("performance", performanceWidget.Properties);
        Assert.Contains("refreshInterval", performanceWidget.Properties);
        
        var diskWidget = pageWidgets.First(pw => pw.Name == "Disk Usage Widget");
        Assert.Contains("gauge", diskWidget.Properties);
        Assert.Contains("threshold", diskWidget.Properties);
        Assert.Contains("90", diskWidget.Properties);
        Assert.Contains("percentage", diskWidget.Properties);
        
        var networkWidget = pageWidgets.First(pw => pw.Name == "Network Traffic Widget");
        Assert.Contains("area", networkWidget.Properties);
        Assert.Contains("inbound", networkWidget.Properties);
        Assert.Contains("outbound", networkWidget.Properties);
        Assert.Contains("24h", networkWidget.Properties);
        
        var alertWidget = pageWidgets.First(pw => pw.Name == "Alert Summary Widget");
        Assert.Contains("table", alertWidget.Properties);
        Assert.Contains("severity", alertWidget.Properties);
        Assert.Contains("message", alertWidget.Properties);
        Assert.Contains("timestamp", alertWidget.Properties);
        
        var serverWidget = pageWidgets.First(pw => pw.Name == "Server Status Widget");
        Assert.Contains("status", serverWidget.Properties);
        Assert.Contains("cpu", serverWidget.Properties);
        Assert.Contains("memory", serverWidget.Properties);
        Assert.Contains("grid", serverWidget.Properties);
    }

    #endregion

    #region GetPageWidgetById Tests

    [Fact]
    public async Task GetPageWidgetById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _pageWidgetFixture.PageWidgetDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPageWidgetDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetPageWidgetById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<PageWidgetDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Name, returnedDetail.Name);
        Assert.Equal(expectedDetail.Properties, returnedDetail.Properties);
        Assert.Equal(expectedDetail.IsActive, returnedDetail.IsActive);
        Assert.Equal(expectedDetail.CreatedBy, returnedDetail.CreatedBy);
    }

    [Fact]
    public async Task GetPageWidgetById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPageWidgetById("invalid-guid"));
    }

    [Fact]
    public async Task GetPageWidgetById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPageWidgetById(""));
    }

    [Fact]
    public async Task GetPageWidgetById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPageWidgetDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PageWidget", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPageWidgetById(nonExistentId));
    }

    #endregion

    #region GetPaginatedPageWidgets Tests

    [Fact]
    public async Task GetPaginatedPageWidgets_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _pageWidgetFixture.GetPageWidgetPaginatedListQuery;
        var expectedResult = _pageWidgetFixture.PageWidgetPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedPageWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PageWidgetListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedPageWidgets_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetPageWidgetPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Performance",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<PageWidgetListVm>
        {
            Data = _pageWidgetFixture.PageWidgetListVm.Where(pw => pw.Name.Contains("Performance")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedPageWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PageWidgetListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Performance", returnedResult.Data.First().Name);
    }

    [Fact]
    public async Task GetPaginatedPageWidgets_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var query = new GetPageWidgetPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 2,
            SearchString = "",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var paginatedResult = new PaginatedResult<PageWidgetListVm>
        {
            Data = _pageWidgetFixture.PageWidgetListVm.Skip(2).Take(2).ToList(),
            CurrentPage = 2,
            TotalPages = 3,
            TotalCount = 5,
            PageSize = 2
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedPageWidgets(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PageWidgetListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.Equal(5, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.PageSize);
        Assert.True(returnedResult.HasPreviousPage);
        Assert.True(returnedResult.HasNextPage);
        Assert.Equal(2, returnedResult.Data.Count);
    }

    #endregion

    #region CreatePageWidget Tests

    [Fact]
    public async Task CreatePageWidget_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _pageWidgetFixture.CreatePageWidgetCommand;
        var expectedResponse = _pageWidgetFixture.CreatePageWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePageWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePageWidgetResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task CreatePageWidget_WithCompletePageWidgetData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreatePageWidgetCommand
        {
            Name = "Custom Analytics Widget",
            Properties = "{\"type\": \"analytics\", \"chartType\": \"bar\", \"dataSource\": \"custom\", \"filters\": [\"date\", \"category\"], \"refreshInterval\": 60}"
        };

        var expectedResponse = new CreatePageWidgetResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "PageWidget has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePageWidget(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePageWidgetResponse>(createdResult.Value);
        Assert.Equal("PageWidget has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    #endregion

    #region UpdatePageWidget Tests

    [Fact]
    public async Task UpdatePageWidget_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _pageWidgetFixture.UpdatePageWidgetCommand;
        var expectedResponse = _pageWidgetFixture.UpdatePageWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePageWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePageWidgetResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task UpdatePageWidget_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _pageWidgetFixture.UpdatePageWidgetCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("PageWidget", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdatePageWidget(command));
    }

    [Fact]
    public async Task UpdatePageWidget_WithUpdatedPageWidgetData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdatePageWidgetCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Analytics Widget",
            Properties = "{\"type\": \"analytics\", \"chartType\": \"pie\", \"dataSource\": \"updated\", \"filters\": [\"date\", \"category\", \"status\"], \"refreshInterval\": 120}"
        };

        var expectedResponse = new UpdatePageWidgetResponse
        {
            Id = command.Id,
            Message = "PageWidget has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePageWidget(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePageWidgetResponse>(okResult.Value);
        Assert.Equal("PageWidget has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.Id);
    }

    #endregion

    #region DeletePageWidget Tests

    [Fact]
    public async Task DeletePageWidget_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _pageWidgetFixture.DeletePageWidgetResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePageWidgetCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeletePageWidget(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeletePageWidgetResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeletePageWidget_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePageWidget("invalid-guid"));
    }

    [Fact]
    public async Task DeletePageWidget_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePageWidget(""));
    }

    [Fact]
    public async Task DeletePageWidget_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePageWidgetCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PageWidget", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeletePageWidget(nonExistentId));
    }

    #endregion

    #region IsPageWidgetNameExist Tests

    [Fact]
    public async Task IsPageWidgetNameExist_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Widget Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPageWidgetNameUniqueQuery>(q => q.Name == uniqueName && q.Id == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsPageWidgetNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsPageWidgetNameExist_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Widget Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPageWidgetNameUniqueQuery>(q => q.Name == existingName && q.Id == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsPageWidgetNameExist(existingName, id);

    }
    

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsPageWidgetCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
