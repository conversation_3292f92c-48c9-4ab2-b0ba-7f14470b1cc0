using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Delete;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.PluginManagerHistoryModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PluginManagerHistoryControllerTests : IClassFixture<PluginManagerHistoryFixture>
{
    private readonly PluginManagerHistoryFixture _pluginManagerHistoryFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly PluginManagerHistoryController _controller;

    public PluginManagerHistoryControllerTests(PluginManagerHistoryFixture pluginManagerHistoryFixture)
    {
        _pluginManagerHistoryFixture = pluginManagerHistoryFixture;
        
        var testBuilder = new ControllerTestBuilder<PluginManagerHistoryController>();
        _controller = testBuilder.CreateController(
            _ => new PluginManagerHistoryController(),
            out _mediatorMock);
    }

    #region CreatePluginManagerHistory Tests

    [Fact]
    public async Task CreatePluginManagerHistory_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _pluginManagerHistoryFixture.CreatePluginManagerHistoryCommand;
        var expectedResponse = _pluginManagerHistoryFixture.CreatePluginManagerHistoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePluginManagerHistory(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePluginManagerHistoryResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginManagerHistoryId);
    }

    [Fact]
    public async Task CreatePluginManagerHistory_WithCompletePluginManagerHistoryData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreatePluginManagerHistoryCommand
        {
            CompanyId = "COMP_CUSTOM",
            LoginName = "<EMAIL>",
            PluginManagerId = "PM_CUSTOM",
            PluginManagerName = "Custom Enterprise Plugin",
            Properties = "{\"type\": \"enterprise\", \"features\": [\"backup\", \"monitoring\"], \"license\": \"commercial\"}",
            Version = "3.0.0",
            UpdaterId = "USER_CUSTOM",
            Description = "Custom enterprise plugin for comprehensive data management",
            Comments = "Enterprise version with advanced features and commercial license"
        };

        var expectedResponse = new CreatePluginManagerHistoryResponse
        {
            PluginManagerHistoryId = Guid.NewGuid().ToString(),
            Message = "PluginManagerHistory has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePluginManagerHistory(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePluginManagerHistoryResponse>(createdResult.Value);
        Assert.Equal("PluginManagerHistory has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginManagerHistoryId);
    }

    #endregion

    #region UpdatePluginManagerHistory Tests

    [Fact]
    public async Task UpdatePluginManagerHistory_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _pluginManagerHistoryFixture.UpdatePluginManagerHistoryCommand;
        var expectedResponse = _pluginManagerHistoryFixture.UpdatePluginManagerHistoryResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePluginManagerHistory(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePluginManagerHistoryResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.PluginManagerHistoryId);
    }

    [Fact]
    public async Task UpdatePluginManagerHistory_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _pluginManagerHistoryFixture.UpdatePluginManagerHistoryCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("PluginManagerHistory", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdatePluginManagerHistory(command));
    }

    [Fact]
    public async Task UpdatePluginManagerHistory_WithUpdatedPluginManagerHistoryData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdatePluginManagerHistoryCommand
        {
            Id = Guid.NewGuid().ToString(),
            CompanyId = "COMP_UPD",
            LoginName = "<EMAIL>",
            PluginManagerId = "PM_UPD",
            PluginManagerName = "Updated Enterprise Plugin",
            Properties = "{\"type\": \"enterprise\", \"features\": [\"backup\", \"monitoring\", \"analytics\"], \"license\": \"commercial\"}",
            Version = "3.1.0",
            UpdaterId = "USER_UPD",
            Description = "Updated enterprise plugin with analytics features",
            Comments = "Added analytics module and performance improvements"
        };

        var expectedResponse = new UpdatePluginManagerHistoryResponse
        {
            PluginManagerHistoryId = command.Id,
            Message = "PluginManagerHistory has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdatePluginManagerHistory(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdatePluginManagerHistoryResponse>(okResult.Value);
        Assert.Equal("PluginManagerHistory has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.PluginManagerHistoryId);
    }

    #endregion

    #region DeletePluginHistoryManager Tests

    [Fact]
    public async Task DeletePluginHistoryManager_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _pluginManagerHistoryFixture.DeletePluginManagerHistoryResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePluginManagerHistoryCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeletePluginHistoryManager(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeletePluginManagerHistoryResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeletePluginHistoryManager_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePluginHistoryManager("invalid-guid"));
    }

    [Fact]
    public async Task DeletePluginHistoryManager_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeletePluginHistoryManager(""));
    }

    [Fact]
    public async Task DeletePluginHistoryManager_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeletePluginManagerHistoryCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PluginManagerHistory", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeletePluginHistoryManager(nonExistentId));
    }

    #endregion

    #region GetPluginManagers Tests

    [Fact]
    public async Task GetPluginManagers_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerHistoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pluginManagerHistoryFixture.PluginManagerHistoryListVm);

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pluginManagerHistories = Assert.IsAssignableFrom<List<PluginManagerHistoryListVm>>(okResult.Value);
        Assert.Equal(5, pluginManagerHistories.Count);
        Assert.All(pluginManagerHistories, pmh => Assert.NotNull(pmh.PluginManagerName));
        Assert.All(pluginManagerHistories, pmh => Assert.NotNull(pmh.Version));
        Assert.All(pluginManagerHistories, pmh => Assert.NotNull(pmh.Description));
        Assert.All(pluginManagerHistories, pmh => Assert.NotNull(pmh.Properties));
    }

    [Fact]
    public async Task GetPluginManagers_ReturnsEmptyList_WhenNoPluginManagerHistoriesExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerHistoryListQuery>(), default))
            .ReturnsAsync(new List<PluginManagerHistoryListVm>());

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<PluginManagerHistoryListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetPluginManagers_ReturnsPluginManagerHistoriesWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPluginManagerHistoryListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_pluginManagerHistoryFixture.PluginManagerHistoryListVm);

        // Act
        var result = await _controller.GetPluginManagers();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var pluginManagerHistories = Assert.IsAssignableFrom<List<PluginManagerHistoryListVm>>(okResult.Value);

        // Verify different plugin types
        Assert.Contains(pluginManagerHistories, pmh => pmh.PluginManagerName == "Database Backup Plugin");
        Assert.Contains(pluginManagerHistories, pmh => pmh.PluginManagerName == "File Replication Plugin");
        Assert.Contains(pluginManagerHistories, pmh => pmh.PluginManagerName == "Monitoring Alert Plugin");
        Assert.Contains(pluginManagerHistories, pmh => pmh.PluginManagerName == "Security Audit Plugin");
        Assert.Contains(pluginManagerHistories, pmh => pmh.PluginManagerName == "Data Migration Plugin");

        // Verify different companies
        Assert.Contains(pluginManagerHistories, pmh => pmh.CompanyId == "COMP_001");
        Assert.Contains(pluginManagerHistories, pmh => pmh.CompanyId == "COMP_002");

        // Verify different login names
        Assert.Contains(pluginManagerHistories, pmh => pmh.LoginName == "<EMAIL>");
        Assert.Contains(pluginManagerHistories, pmh => pmh.LoginName == "<EMAIL>");
        Assert.Contains(pluginManagerHistories, pmh => pmh.LoginName == "<EMAIL>");
        Assert.Contains(pluginManagerHistories, pmh => pmh.LoginName == "<EMAIL>");

        // Verify specific plugin details
        var backupPlugin = pluginManagerHistories.First(pmh => pmh.PluginManagerName == "Database Backup Plugin");
        Assert.Equal("1.0.0", backupPlugin.Version);
        Assert.Equal("Plugin for automated database backup operations", backupPlugin.Description);
        Assert.Contains("backup", backupPlugin.Properties);
        Assert.Contains("postgresql", backupPlugin.Properties);
        Assert.Equal("Initial version with basic backup functionality", backupPlugin.Comments);

        var replicationPlugin = pluginManagerHistories.First(pmh => pmh.PluginManagerName == "File Replication Plugin");
        Assert.Equal("2.1.0", replicationPlugin.Version);
        Assert.Equal("Plugin for file system replication and synchronization", replicationPlugin.Description);
        Assert.Contains("replication", replicationPlugin.Properties);
        Assert.Contains("remote", replicationPlugin.Properties);
        Assert.Equal("Updated version with improved error handling", replicationPlugin.Comments);

        var monitoringPlugin = pluginManagerHistories.First(pmh => pmh.PluginManagerName == "Monitoring Alert Plugin");
        Assert.Equal("1.5.2", monitoringPlugin.Version);
        Assert.Equal("Plugin for system monitoring and alerting", monitoringPlugin.Description);
        Assert.Contains("monitoring", monitoringPlugin.Properties);
        Assert.Contains("email", monitoringPlugin.Properties);
        Assert.Equal("Bug fix release for alert notification issues", monitoringPlugin.Comments);

        var securityPlugin = pluginManagerHistories.First(pmh => pmh.PluginManagerName == "Security Audit Plugin");
        Assert.Equal("3.0.0", securityPlugin.Version);
        Assert.Equal("Plugin for security auditing and compliance reporting", securityPlugin.Description);
        Assert.Contains("security", securityPlugin.Properties);
        Assert.Contains("compliance", securityPlugin.Properties);
        Assert.Equal("Major version upgrade with new compliance features", securityPlugin.Comments);

        var migrationPlugin = pluginManagerHistories.First(pmh => pmh.PluginManagerName == "Data Migration Plugin");
        Assert.Equal("1.2.1", migrationPlugin.Version);
        Assert.Equal("Plugin for data migration between different systems", migrationPlugin.Description);
        Assert.Contains("migration", migrationPlugin.Properties);
        Assert.Contains("legacy", migrationPlugin.Properties);
        Assert.Equal("Performance improvements and additional data type support", migrationPlugin.Comments);
    }

    #endregion

    #region GetPluginManagerById Tests

    [Fact]
    public async Task GetPluginManagerById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _pluginManagerHistoryFixture.PluginManagerHistoryDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerHistoryDetailQuery>(q => q.PluginManagerId == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetPluginManagerById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<List<PluginManagerHistoryDetailVm>>(okResult.Value);
        Assert.Equal(2, returnedDetail.Count);
        Assert.All(returnedDetail, pmh => Assert.Equal("Database Backup Plugin", pmh.PluginManagerName));
        Assert.All(returnedDetail, pmh => Assert.Equal("PM_001", pmh.PluginManagerId));
        Assert.Contains(returnedDetail, pmh => pmh.Version == "1.0.0");
        Assert.Contains(returnedDetail, pmh => pmh.Version == "1.1.0");
    }

    [Fact]
    public async Task GetPluginManagerById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPluginManagerById("invalid-guid"));
    }

    [Fact]
    public async Task GetPluginManagerById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPluginManagerById(""));
    }

    [Fact]
    public async Task GetPluginManagerById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerHistoryDetailQuery>(q => q.PluginManagerId == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PluginManagerHistory", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPluginManagerById(nonExistentId));
    }

    [Fact]
    public async Task GetPluginManagerById_WithValidId_ReturnsVersionHistory()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var versionHistory = new List<PluginManagerHistoryDetailVm>
        {
            new PluginManagerHistoryDetailVm
            {
                Id = "PMH_001",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Version = "1.0.0",
                Description = "Initial version",
                Comments = "Basic backup functionality"
            },
            new PluginManagerHistoryDetailVm
            {
                Id = "PMH_002",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Version = "1.1.0",
                Description = "Enhanced version",
                Comments = "Added compression support"
            },
            new PluginManagerHistoryDetailVm
            {
                Id = "PMH_003",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Version = "1.2.0",
                Description = "Latest version",
                Comments = "Added encryption and scheduling"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPluginManagerHistoryDetailQuery>(q => q.PluginManagerId == validId), default))
            .ReturnsAsync(versionHistory);

        // Act
        var result = await _controller.GetPluginManagerById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedHistory = Assert.IsType<List<PluginManagerHistoryDetailVm>>(okResult.Value);
        Assert.Equal(3, returnedHistory.Count);
        Assert.All(returnedHistory, pmh => Assert.Equal("PM_001", pmh.PluginManagerId));
        Assert.All(returnedHistory, pmh => Assert.Equal("Database Backup Plugin", pmh.PluginManagerName));

        // Verify version progression
        Assert.Contains(returnedHistory, pmh => pmh.Version == "1.0.0");
        Assert.Contains(returnedHistory, pmh => pmh.Version == "1.1.0");
        Assert.Contains(returnedHistory, pmh => pmh.Version == "1.2.0");

        // Verify comments progression
        Assert.Contains(returnedHistory, pmh => pmh.Comments == "Basic backup functionality");
        Assert.Contains(returnedHistory, pmh => pmh.Comments == "Added compression support");
        Assert.Contains(returnedHistory, pmh => pmh.Comments == "Added encryption and scheduling");
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsPluginManagerHistoryCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
