using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class PostgresMonitorLogsControllerTests : IClassFixture<PostgresMonitorLogsFixture>
{
    private readonly PostgresMonitorLogsFixture _postgresMonitorLogsFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly PostgresMonitorLogsController _controller;

    public PostgresMonitorLogsControllerTests(PostgresMonitorLogsFixture postgresMonitorLogsFixture)
    {
        _postgresMonitorLogsFixture = postgresMonitorLogsFixture;
        
        var testBuilder = new ControllerTestBuilder<PostgresMonitorLogsController>();
        _controller = testBuilder.CreateController(
            _ => new PostgresMonitorLogsController(),
            out _mediatorMock);
    }

    #region CreatePostgresMonitorLog Tests

    [Fact]
    public async Task CreatePostgresMonitorLog_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _postgresMonitorLogsFixture.CreatePostgresMonitorLogCommand;
        var expectedResponse = _postgresMonitorLogsFixture.CreatePostgresMonitorLogResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePostgresMonitorLog(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePostgresMonitorLogResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }


    [Fact]
    public async Task CreatePostgresMonitorLog_WithCompletePostgresMonitorLogData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreatePostgresMonitorLogCommand
        {
            Type = "POSTGRES_REPLICATION_ERROR",
            InfraObjectId = "INFRA_CUSTOM",
            InfraObjectName = "Custom PostgreSQL Server",
            WorkflowId = "WF_CUSTOM",
            WorkflowName = "Custom PostgreSQL Replication Workflow",
            ConfiguredRPO = "30",
            DataLagValue = "5",
            Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG_CUSTOM\", \"message\": \"Custom replication error\"}",
            Threshold = "60"
        };

        var expectedResponse = new CreatePostgresMonitorLogResponse
        {
            Id = Guid.NewGuid().ToString(),
            Message = "PostgresMonitorLog has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreatePostgresMonitorLog(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreatePostgresMonitorLogResponse>(createdResult.Value);
        Assert.Equal("PostgresMonitorLog has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    #endregion

    #region GetAllPostgresMonitorLogs Tests

    [Fact]
    public async Task GetAllPostgresMonitorLogs_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_postgresMonitorLogsFixture.PostgresMonitorLogsListVm);

        // Act
        var result = await _controller.GetAllPostgresMonitorLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var postgresMonitorLogs = Assert.IsAssignableFrom<List<PostgresMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(5, postgresMonitorLogs.Count);
        Assert.All(postgresMonitorLogs, pml => Assert.NotNull(pml.Type));
        Assert.All(postgresMonitorLogs, pml => Assert.NotNull(pml.InfraObjectName));
        Assert.All(postgresMonitorLogs, pml => Assert.NotNull(pml.WorkflowName));
        Assert.All(postgresMonitorLogs, pml => Assert.NotNull(pml.Properties));
    }

    [Fact]
    public async Task GetAllPostgresMonitorLogs_ReturnsEmptyList_WhenNoPostgresMonitorLogsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorLogsListQuery>(), default))
            .ReturnsAsync(new List<PostgresMonitorLogsListVm>());

        // Act
        var result = await _controller.GetAllPostgresMonitorLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<PostgresMonitorLogsListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetAllPostgresMonitorLogs_ReturnsPostgresMonitorLogsWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetPostgresMonitorLogsListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_postgresMonitorLogsFixture.PostgresMonitorLogsListVm);

        // Act
        var result = await _controller.GetAllPostgresMonitorLogs();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var postgresMonitorLogs = Assert.IsAssignableFrom<List<PostgresMonitorLogsListVm>>(okResult.Value);
        
        // Verify different PostgreSQL log types
        Assert.Contains(postgresMonitorLogs, pml => pml.Type == "POSTGRES_REPLICATION_ERROR");
        Assert.Contains(postgresMonitorLogs, pml => pml.Type == "POSTGRES_REPLICATION_WARNING");
        Assert.Contains(postgresMonitorLogs, pml => pml.Type == "POSTGRES_REPLICATION_INFO");
        Assert.Contains(postgresMonitorLogs, pml => pml.Type == "POSTGRES_BACKUP_ERROR");
        Assert.Contains(postgresMonitorLogs, pml => pml.Type == "POSTGRES_RECOVERY_SUCCESS");
        
        // Verify different infrastructure objects
        Assert.Contains(postgresMonitorLogs, pml => pml.InfraObjectName == "PostgreSQL Primary Server");
        Assert.Contains(postgresMonitorLogs, pml => pml.InfraObjectName == "PostgreSQL Secondary Server");
        Assert.Contains(postgresMonitorLogs, pml => pml.InfraObjectName == "PostgreSQL Hot Standby Server");
        Assert.Contains(postgresMonitorLogs, pml => pml.InfraObjectName == "PostgreSQL Backup Server");
        Assert.Contains(postgresMonitorLogs, pml => pml.InfraObjectName == "PostgreSQL Recovery Server");
        
        // Verify different workflows
        Assert.Contains(postgresMonitorLogs, pml => pml.WorkflowName == "PostgreSQL Streaming Replication");
        Assert.Contains(postgresMonitorLogs, pml => pml.WorkflowName == "PostgreSQL Logical Replication");
        Assert.Contains(postgresMonitorLogs, pml => pml.WorkflowName == "PostgreSQL Hot Standby");
        Assert.Contains(postgresMonitorLogs, pml => pml.WorkflowName == "PostgreSQL Backup Process");
        Assert.Contains(postgresMonitorLogs, pml => pml.WorkflowName == "PostgreSQL Point-in-Time Recovery");
        
        // Verify specific monitor log details
        var replicationError = postgresMonitorLogs.First(pml => pml.Type == "POSTGRES_REPLICATION_ERROR");
        Assert.Equal("30", replicationError.ConfiguredRPO);
        Assert.Equal("5", replicationError.DataLagValue);
        Assert.Equal("60", replicationError.Threshold);
        Assert.Contains("ERROR", replicationError.Properties);
        Assert.Contains("PG001", replicationError.Properties);
        Assert.Contains("Replication slot disconnected", replicationError.Properties);
        
        var replicationWarning = postgresMonitorLogs.First(pml => pml.Type == "POSTGRES_REPLICATION_WARNING");
        Assert.Equal("60", replicationWarning.ConfiguredRPO);
        Assert.Equal("45", replicationWarning.DataLagValue);
        Assert.Equal("120", replicationWarning.Threshold);
        Assert.Contains("WARNING", replicationWarning.Properties);
        Assert.Contains("PG002", replicationWarning.Properties);
        Assert.Contains("High replication lag detected", replicationWarning.Properties);
        
        var replicationInfo = postgresMonitorLogs.First(pml => pml.Type == "POSTGRES_REPLICATION_INFO");
        Assert.Equal("15", replicationInfo.ConfiguredRPO);
        Assert.Equal("3", replicationInfo.DataLagValue);
        Assert.Equal("30", replicationInfo.Threshold);
        Assert.Contains("INFO", replicationInfo.Properties);
        Assert.Contains("PG003", replicationInfo.Properties);
        Assert.Contains("Replication status healthy", replicationInfo.Properties);
        
        var backupError = postgresMonitorLogs.First(pml => pml.Type == "POSTGRES_BACKUP_ERROR");
        Assert.Equal("240", backupError.ConfiguredRPO);
        Assert.Equal("300", backupError.DataLagValue);
        Assert.Equal("300", backupError.Threshold);
        Assert.Contains("ERROR", backupError.Properties);
        Assert.Contains("PG004", backupError.Properties);
        Assert.Contains("Backup process failed", backupError.Properties);
        
        var recoverySuccess = postgresMonitorLogs.First(pml => pml.Type == "POSTGRES_RECOVERY_SUCCESS");
        Assert.Equal("120", recoverySuccess.ConfiguredRPO);
        Assert.Equal("0", recoverySuccess.DataLagValue);
        Assert.Equal("180", recoverySuccess.Threshold);
        Assert.Contains("SUCCESS", recoverySuccess.Properties);
        Assert.Contains("PG005", recoverySuccess.Properties);
        Assert.Contains("Recovery completed successfully", recoverySuccess.Properties);
    }

    #endregion

    #region GetPostgresMonitorLogById Tests

    [Fact]
    public async Task GetPostgresMonitorLogById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _postgresMonitorLogsFixture.PostgresMonitorLogsDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetPostgresMonitorLogsById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<PostgresMonitorLogsDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Type, returnedDetail.Type);
        Assert.Equal(expectedDetail.InfraObjectName, returnedDetail.InfraObjectName);
        Assert.Equal(expectedDetail.WorkflowName, returnedDetail.WorkflowName);
        Assert.Equal(expectedDetail.ConfiguredRPO, returnedDetail.ConfiguredRPO);
        Assert.Equal(expectedDetail.DataLagValue, returnedDetail.DataLagValue);
        Assert.Equal(expectedDetail.Properties, returnedDetail.Properties);
    }

    [Fact]
    public async Task GetPostgresMonitorLogById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorLogsById("invalid-guid"));
    }

    [Fact]
    public async Task GetPostgresMonitorLogById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorLogsById(""));
    }

    [Fact]
    public async Task GetPostgresMonitorLogById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("PostgresMonitorLogs", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetPostgresMonitorLogsById(nonExistentId));
    }

    #endregion

    #region GetPaginatedPostgresMonitorLogs Tests

    [Fact]
    public async Task GetPaginatedPostgresMonitorLogs_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _postgresMonitorLogsFixture.GetPostgresMonitorLogsPaginatedListQuery;
        var expectedResult = _postgresMonitorLogsFixture.PostgresMonitorLogsPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorlogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedPostgresMonitorLogs_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetPostgresMonitorLogsPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "ERROR",
            SortColumn = "Type",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<PostgresMonitorLogsListVm>
        {
            Data = _postgresMonitorLogsFixture.PostgresMonitorLogsListVm.Where(pml => pml.Type.Contains("ERROR")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 2,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorlogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.Data.Count);
        Assert.All(returnedResult.Data, pml => Assert.Contains("ERROR", pml.Type));
    }

    [Fact]
    public async Task GetPaginatedPostgresMonitorLogs_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var query = new GetPostgresMonitorLogsPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 2,
            SearchString = "",
            SortColumn = "Type",
            SortOrder = "asc"
        };

        var paginatedResult = new PaginatedResult<PostgresMonitorLogsListVm>
        {
            Data = _postgresMonitorLogsFixture.PostgresMonitorLogsListVm.Skip(2).Take(2).ToList(),
            CurrentPage = 2,
            TotalPages = 3,
            TotalCount = 5,
            PageSize = 2
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedPostgresMonitorlogs(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<PostgresMonitorLogsListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.Equal(5, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.PageSize);
        Assert.True(returnedResult.HasPreviousPage);
        Assert.True(returnedResult.HasNextPage);
        Assert.Equal(2, returnedResult.Data.Count);
    }

    #endregion

    #region GetPostgresMonitorLogsByType Tests

    [Fact]
    public async Task GetPostgresMonitorLogsByType_WithValidType_ReturnsOkResult()
    {
        // Arrange
        var validType = "POSTGRES_REPLICATION_ERROR";
        var expectedResult = _postgresMonitorLogsFixture.PostgresMonitorLogsDetailByTypeVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailByTypeQuery>(q => q.Type == validType), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPostgresMonitorLogsByType(validType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<PostgresMonitorLogsDetailByTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
        Assert.All(returnedResult, pml => Assert.Equal("POSTGRES_REPLICATION_ERROR", pml.Type));
        Assert.Contains(returnedResult, pml => pml.InfraObjectName == "PostgreSQL Primary Server");
        Assert.Contains(returnedResult, pml => pml.InfraObjectName == "PostgreSQL Replica Server");
        Assert.All(returnedResult, pml => Assert.Contains("ERROR", pml.Properties));
    }

    [Fact]
    public async Task GetPostgresMonitorLogsByType_WithEmptyType_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetPostgresMonitorLogsByType(""));
    }
    
    [Fact]
    public async Task GetPostgresMonitorLogsByType_WithNonExistentType_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentType = "NON_EXISTENT_TYPE";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailByTypeQuery>(q => q.Type == nonExistentType), default))
            .ReturnsAsync(new List<PostgresMonitorLogsDetailByTypeVm>());

        // Act
        var result = await _controller.GetPostgresMonitorLogsByType(nonExistentType);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<PostgresMonitorLogsDetailByTypeVm>>(okResult.Value);
        Assert.Empty(returnedResult);
    }

    [Fact]
    public async Task GetPostgresMonitorLogsByType_WithDifferentLogTypes_ReturnsCorrectLogs()
    {
        // Arrange
        var errorType = "POSTGRES_REPLICATION_ERROR";
        var warningType = "POSTGRES_REPLICATION_WARNING";
        var infoType = "POSTGRES_REPLICATION_INFO";

        var errorLogs = new List<PostgresMonitorLogsDetailByTypeVm>
        {
            new PostgresMonitorLogsDetailByTypeVm
            {
                Id = "PML_001",
                Type = "POSTGRES_REPLICATION_ERROR",
                Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG001\"}"
            }
        };

        var warningLogs = new List<PostgresMonitorLogsDetailByTypeVm>
        {
            new PostgresMonitorLogsDetailByTypeVm
            {
                Id = "PML_002",
                Type = "POSTGRES_REPLICATION_WARNING",
                Properties = "{\"logLevel\": \"WARNING\", \"warningCode\": \"PG002\"}"
            }
        };

        var infoLogs = new List<PostgresMonitorLogsDetailByTypeVm>
        {
            new PostgresMonitorLogsDetailByTypeVm
            {
                Id = "PML_003",
                Type = "POSTGRES_REPLICATION_INFO",
                Properties = "{\"logLevel\": \"INFO\", \"infoCode\": \"PG003\"}"
            }
        };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailByTypeQuery>(q => q.Type == errorType), default))
            .ReturnsAsync(errorLogs);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailByTypeQuery>(q => q.Type == warningType), default))
            .ReturnsAsync(warningLogs);

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetPostgresMonitorLogsDetailByTypeQuery>(q => q.Type == infoType), default))
            .ReturnsAsync(infoLogs);

        // Act
        var errorResult = await _controller.GetPostgresMonitorLogsByType(errorType);
        var warningResult = await _controller.GetPostgresMonitorLogsByType(warningType);
        var infoResult = await _controller.GetPostgresMonitorLogsByType(infoType);

        // Assert
        var errorOkResult = Assert.IsType<OkObjectResult>(errorResult.Result);
        var errorReturnedResult = Assert.IsType<List<PostgresMonitorLogsDetailByTypeVm>>(errorOkResult.Value);
        Assert.Single(errorReturnedResult);
        Assert.Contains("ERROR", errorReturnedResult.First().Properties);

        var warningOkResult = Assert.IsType<OkObjectResult>(warningResult.Result);
        var warningReturnedResult = Assert.IsType<List<PostgresMonitorLogsDetailByTypeVm>>(warningOkResult.Value);
        Assert.Single(warningReturnedResult);
        Assert.Contains("WARNING", warningReturnedResult.First().Properties);

        var infoOkResult = Assert.IsType<OkObjectResult>(infoResult.Result);
        var infoReturnedResult = Assert.IsType<List<PostgresMonitorLogsDetailByTypeVm>>(infoOkResult.Value);
        Assert.Single(infoReturnedResult);
        Assert.Contains("INFO", infoReturnedResult.First().Properties);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsPostgresMonitorLogsCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
