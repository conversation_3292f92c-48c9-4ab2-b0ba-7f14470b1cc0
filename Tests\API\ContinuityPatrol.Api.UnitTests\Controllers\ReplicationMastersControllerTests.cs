using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNames;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ReplicationMastersControllerTests : IClassFixture<ReplicationMastersFixture>
{
    private readonly ReplicationMastersFixture _replicationMastersFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ReplicationMastersController _controller;

    public ReplicationMastersControllerTests(ReplicationMastersFixture replicationMastersFixture)
    {
        _replicationMastersFixture = replicationMastersFixture;
        
        var testBuilder = new ControllerTestBuilder<ReplicationMastersController>();
        _controller = testBuilder.CreateController(
            _ => new ReplicationMastersController(),
            out _mediatorMock);
    }

    #region GetReplicationMasterList Tests

    [Fact]
    public async Task GetReplicationMasterList_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationMasterListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationMastersFixture.ReplicationMasterListVm);

        // Act
        var result = await _controller.GetReplicationMasterList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replicationMasters = Assert.IsAssignableFrom<List<ReplicationMasterListVm>>(okResult.Value);
        Assert.Equal(5, replicationMasters.Count);
        Assert.All(replicationMasters, rm => Assert.NotNull(rm.Name));
        Assert.All(replicationMasters, rm => Assert.NotNull(rm.InfraMasterName));
    }

    [Fact]
    public async Task GetReplicationMasterList_ReturnsEmptyList_WhenNoReplicationMastersExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationMasterListQuery>(), default))
            .ReturnsAsync(new List<ReplicationMasterListVm>());

        // Act
        var result = await _controller.GetReplicationMasterList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<ReplicationMasterListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetReplicationMasterList_ReturnsReplicationMastersWithDifferentInfrastructures()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationMasterListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationMastersFixture.ReplicationMasterListVm);

        // Act
        var result = await _controller.GetReplicationMasterList();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replicationMasters = Assert.IsAssignableFrom<List<ReplicationMasterListVm>>(okResult.Value);
        
        // Verify different replication master types
        Assert.Contains(replicationMasters, rm => rm.Name == "Primary Database Replication Master");
        Assert.Contains(replicationMasters, rm => rm.Name == "File System Replication Master");
        Assert.Contains(replicationMasters, rm => rm.Name == "Application Server Replication Master");
        Assert.Contains(replicationMasters, rm => rm.Name == "Storage Array Replication Master");
        Assert.Contains(replicationMasters, rm => rm.Name == "Virtual Machine Replication Master");
        
        // Verify different infrastructure masters
        Assert.Contains(replicationMasters, rm => rm.InfraMasterName == "Primary Database Infrastructure");
        Assert.Contains(replicationMasters, rm => rm.InfraMasterName == "File Storage Infrastructure");
        Assert.Contains(replicationMasters, rm => rm.InfraMasterName == "Application Server Infrastructure");
        Assert.Contains(replicationMasters, rm => rm.InfraMasterName == "Storage Array Infrastructure");
        Assert.Contains(replicationMasters, rm => rm.InfraMasterName == "Virtual Infrastructure Platform");
        
        // Verify infrastructure master IDs are properly set
        var dbReplicationMaster = replicationMasters.First(rm => rm.Name == "Primary Database Replication Master");
        Assert.Equal("IM_001", dbReplicationMaster.InfraMasterId);
        Assert.Equal("Primary Database Infrastructure", dbReplicationMaster.InfraMasterName);
        
        var fileReplicationMaster = replicationMasters.First(rm => rm.Name == "File System Replication Master");
        Assert.Equal("IM_002", fileReplicationMaster.InfraMasterId);
        Assert.Equal("File Storage Infrastructure", fileReplicationMaster.InfraMasterName);
        
        var appReplicationMaster = replicationMasters.First(rm => rm.Name == "Application Server Replication Master");
        Assert.Equal("IM_003", appReplicationMaster.InfraMasterId);
        Assert.Equal("Application Server Infrastructure", appReplicationMaster.InfraMasterName);
        
        var storageReplicationMaster = replicationMasters.First(rm => rm.Name == "Storage Array Replication Master");
        Assert.Equal("IM_004", storageReplicationMaster.InfraMasterId);
        Assert.Equal("Storage Array Infrastructure", storageReplicationMaster.InfraMasterName);
        
        var vmReplicationMaster = replicationMasters.First(rm => rm.Name == "Virtual Machine Replication Master");
        Assert.Equal("IM_005", vmReplicationMaster.InfraMasterId);
        Assert.Equal("Virtual Infrastructure Platform", vmReplicationMaster.InfraMasterName);
    }

    #endregion

    #region CreateReplicationMaster Tests

    [Fact]
    public async Task CreateReplicationMaster_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _replicationMastersFixture.CreateReplicationMasterCommand;
        var expectedResponse = _replicationMastersFixture.CreateReplicationMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplicationMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationMasterResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationMasterId);
    }

    [Fact]
    public async Task CreateReplicationMaster_WithCompleteReplicationMasterData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateReplicationMasterCommand
        {
            Name = "Custom Hybrid Replication Master",
            InfraMasterId = "IM_CUSTOM_001",
            InfraMasterName = "Custom Hybrid Infrastructure Platform"
        };

        var expectedResponse = new CreateReplicationMasterResponse
        {
            ReplicationMasterId = Guid.NewGuid().ToString(),
            Message = "ReplicationMaster has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplicationMaster(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationMasterResponse>(createdResult.Value);
        Assert.Equal("ReplicationMaster has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationMasterId);
    }

    #endregion

    #region UpdateReplicationMaster Tests

    [Fact]
    public async Task UpdateReplicationMaster_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationMastersFixture.UpdateReplicationMasterCommand;
        var expectedResponse = _replicationMastersFixture.UpdateReplicationMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationMasterResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationMasterId);
    }

    [Fact]
    public async Task UpdateReplicationMaster_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _replicationMastersFixture.UpdateReplicationMasterCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("ReplicationMaster", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateReplicationMaster(command));
    }

    [Fact]
    public async Task UpdateReplicationMaster_WithUpdatedReplicationMasterData_UpdatesSuccessfully()
    {
        // Arrange
        var command = new UpdateReplicationMasterCommand
        {
            Id = Guid.NewGuid().ToString(),
            Name = "Updated Enterprise Replication Master",
            InfraMasterId = "IM_UPD_001",
            InfraMasterName = "Updated Enterprise Infrastructure Platform"
        };

        var expectedResponse = new UpdateReplicationMasterResponse
        {
            ReplicationMasterId = command.Id,
            Message = "ReplicationMaster has been updated successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplicationMaster(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationMasterResponse>(okResult.Value);
        Assert.Equal("ReplicationMaster has been updated successfully", returnedResponse.Message);
        Assert.Equal(command.Id, returnedResponse.ReplicationMasterId);
    }

    #endregion

    #region DeleteReplicationMaster Tests

    [Fact]
    public async Task DeleteReplicationMaster_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _replicationMastersFixture.DeleteReplicationMasterResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationMasterCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteReplicationMaster(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteReplicationMasterResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteReplicationMaster_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplicationMaster("invalid-guid"));
    }

    [Fact]
    public async Task DeleteReplicationMaster_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplicationMaster(""));
    }

    [Fact]
    public async Task DeleteReplicationMaster_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationMasterCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("ReplicationMaster", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteReplicationMaster(nonExistentId));
    }

    #endregion

    #region GetReplicationMasterById Tests

    [Fact]
    public async Task GetReplicationMasterById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = _replicationMastersFixture.ReplicationMasterDetailVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationMasterDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(expectedDetail);

        // Act
        var result = await _controller.GetReplicationMasterById(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedDetail = Assert.IsType<ReplicationMasterDetailVm>(okResult.Value);
        Assert.Equal(expectedDetail.Name, returnedDetail.Name);
        Assert.Equal(expectedDetail.InfraMasterId, returnedDetail.InfraMasterId);
        Assert.Equal(expectedDetail.InfraMasterName, returnedDetail.InfraMasterName);
    }

    [Fact]
    public async Task GetReplicationMasterById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationMasterById("invalid-guid"));
    }

    [Fact]
    public async Task GetReplicationMasterById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationMasterById(""));
    }

    [Fact]
    public async Task GetReplicationMasterById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationMasterDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("ReplicationMaster", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetReplicationMasterById(nonExistentId));
    }

    #endregion

    #region GetPaginatedReplicationMaster Tests

    [Fact]
    public async Task GetPaginatedReplicationMaster_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _replicationMastersFixture.GetReplicationMasterPaginatedListQuery;
        var expectedResult = _replicationMastersFixture.ReplicationMasterPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedReplicationMaster(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationMasterListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedReplicationMaster_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetReplicationMasterPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Database",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<ReplicationMasterListVm>
        {
            Data = _replicationMastersFixture.ReplicationMasterListVm.Where(rm => rm.Name.Contains("Database")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedReplicationMaster(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationMasterListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Database", returnedResult.Data.First().Name);
    }

    [Fact]
    public async Task GetPaginatedReplicationMaster_WithPagination_ReturnsCorrectPage()
    {
        // Arrange
        var query = new GetReplicationMasterPaginatedListQuery
        {
            PageNumber = 2,
            PageSize = 2,
            SearchString = "",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var paginatedResult = new PaginatedResult<ReplicationMasterListVm>
        {
            Data = _replicationMastersFixture.ReplicationMasterListVm.Skip(2).Take(2).ToList(),
            CurrentPage = 2,
            TotalPages = 3,
            TotalCount = 5,
            PageSize = 2
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paginatedResult);

        // Act
        var result = await _controller.GetPaginatedReplicationMaster(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationMasterListVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.CurrentPage);
        Assert.Equal(3, returnedResult.TotalPages);
        Assert.Equal(5, returnedResult.TotalCount);
        Assert.Equal(2, returnedResult.PageSize);
        Assert.True(returnedResult.HasPreviousPage);
        Assert.True(returnedResult.HasNextPage);
        Assert.Equal(2, returnedResult.Data.Count);
    }

    #endregion

    #region GetReplicationMasterNames Tests

    [Fact]
    public async Task GetReplicationMasterNames_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationMasterNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationMastersFixture.ReplicationMasterNameListVm);

        // Act
        var result = await _controller.GetReplicationMasterNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replicationMasterNames = Assert.IsAssignableFrom<List<ReplicationMasterNameVm>>(okResult.Value);
        Assert.Equal(5, replicationMasterNames.Count);
        Assert.All(replicationMasterNames, rmn => Assert.NotNull(rmn.Name));
        Assert.All(replicationMasterNames, rmn => Assert.NotNull(rmn.Id));
    }

    #endregion

    #region IsReplicationMasterNameExist Tests

    [Fact]
    public async Task IsReplicationMasterNameExist_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Replication Master Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationMasterNameUniqueQuery>(q => q.ReplicationMasterName == uniqueName && q.ReplicationMasterId == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsReplicationMasterNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsReplicationMasterNameExist_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Replication Master Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationMasterNameUniqueQuery>(q => q.ReplicationMasterName == existingName && q.ReplicationMasterId == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsReplicationMasterNameExist(existingName, id);

    }
    
    #endregion

    #region GetReplicationMasterByInfraMasterName Tests

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_WithValidName_ReturnsOkResult()
    {
        // Arrange
        var infraMasterName = "Primary Database Infrastructure";
        var expectedResult = _replicationMastersFixture.ReplicationMasterByInfraMasterNameListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByInfraMasterNameQuery>(q => q.InfraMasterName == infraMasterName), default))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetReplicationMasterByInfraMasterName(infraMasterName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetByInfraMasterNameVm>>(okResult.Value);
        Assert.Equal(2, returnedResult.Count);
        Assert.All(returnedResult, rm => Assert.Equal("Primary Database Infrastructure", rm.InfraMasterName));
        Assert.All(returnedResult, rm => Assert.Equal("IM_001", rm.InfraMasterId));
    }

    [Fact]
    public async Task GetReplicationMasterByInfraMasterName_WithNonExistentName_ReturnsEmptyList()
    {
        // Arrange
        var nonExistentName = "Non-Existent Infrastructure";

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetByInfraMasterNameQuery>(q => q.InfraMasterName == nonExistentName), default))
            .ReturnsAsync(new List<GetByInfraMasterNameVm>());

        // Act
        var result = await _controller.GetReplicationMasterByInfraMasterName(nonExistentName);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<List<GetByInfraMasterNameVm>>(okResult.Value);
        Assert.Empty(returnedResult);
    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsReplicationMasterCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
