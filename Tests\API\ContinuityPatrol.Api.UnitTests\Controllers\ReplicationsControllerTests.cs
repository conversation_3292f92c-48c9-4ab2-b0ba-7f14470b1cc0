using ContinuityPatrol.Api.Controllers;
using ContinuityPatrol.Api.UnitTests.Fixtures;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Delete;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNames;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace ContinuityPatrol.Api.UnitTests.Controllers;

public class ReplicationsControllerTests : IClassFixture<ReplicationsFixture>
{
    private readonly ReplicationsFixture _replicationsFixture;
    private readonly Mock<IMediator> _mediatorMock;
    private readonly ReplicationsController _controller;

    public ReplicationsControllerTests(ReplicationsFixture replicationsFixture)
    {
        _replicationsFixture = replicationsFixture;
        
        var testBuilder = new ControllerTestBuilder<ReplicationsController>();
        _controller = testBuilder.CreateController(
            _ => new ReplicationsController(),
            out _mediatorMock);
    }

    #region GetReplications Tests

    [Fact]
    public async Task GetReplications_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationsFixture.ReplicationListVm);

        // Act
        var result = await _controller.GetReplications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replications = Assert.IsAssignableFrom<List<ReplicationListVm>>(okResult.Value);
        Assert.Equal(5, replications.Count);
        Assert.All(replications, r => Assert.NotNull(r.Name));
        Assert.All(replications, r => Assert.NotNull(r.Type));
        Assert.All(replications, r => Assert.NotNull(r.SiteName));
        Assert.All(replications, r => Assert.NotNull(r.BusinessServiceName));
    }

    [Fact]
    public async Task GetReplications_ReturnsEmptyList_WhenNoReplicationsExist()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationListQuery>(), default))
            .ReturnsAsync(new List<ReplicationListVm>());

        // Act
        var result = await _controller.GetReplications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        Assert.Empty(((List<ReplicationListVm>)okResult.Value!));
    }

    [Fact]
    public async Task GetReplications_ReturnsReplicationsWithDifferentTypes()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationListQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationsFixture.ReplicationListVm);

        // Act
        var result = await _controller.GetReplications();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replications = Assert.IsAssignableFrom<List<ReplicationListVm>>(okResult.Value);
        
        // Verify different replication types
        Assert.Contains(replications, r => r.Name == "Primary Database Replication");
        Assert.Contains(replications, r => r.Name == "File System Replication");
        Assert.Contains(replications, r => r.Name == "Application Server Replication");
        Assert.Contains(replications, r => r.Name == "Storage Array Replication");
        Assert.Contains(replications, r => r.Name == "Virtual Machine Replication");
        
        // Verify different replication types
        Assert.Contains(replications, r => r.Type == "Database Replication");
        Assert.Contains(replications, r => r.Type == "File Replication");
        Assert.Contains(replications, r => r.Type == "Application Replication");
        Assert.Contains(replications, r => r.Type == "Storage Replication");
        Assert.Contains(replications, r => r.Type == "VM Replication");
        
        // Verify different sites
        Assert.Contains(replications, r => r.SiteName == "Primary Data Center");
        Assert.Contains(replications, r => r.SiteName == "Secondary Data Center");
        Assert.Contains(replications, r => r.SiteName == "Disaster Recovery Site");
        
        // Verify different business services
        Assert.Contains(replications, r => r.BusinessServiceName == "Core Banking System");
        Assert.Contains(replications, r => r.BusinessServiceName == "Document Management System");
        Assert.Contains(replications, r => r.BusinessServiceName == "Customer Portal");
        Assert.Contains(replications, r => r.BusinessServiceName == "Enterprise Storage");
        Assert.Contains(replications, r => r.BusinessServiceName == "Virtual Infrastructure");
        
        // Verify properties contain expected data
        var dbReplication = replications.First(r => r.Name == "Primary Database Replication");
        Assert.Contains("synchronous", dbReplication.Properties);
        Assert.Contains("db-primary-01", dbReplication.Properties);
        Assert.Contains("encryption", dbReplication.Properties);
        Assert.Equal("DB-REP-2024-001", dbReplication.LicenseKey);
        
        var fileReplication = replications.First(r => r.Name == "File System Replication");
        Assert.Contains("asynchronous", fileReplication.Properties);
        Assert.Contains("/data/shared", fileReplication.Properties);
        Assert.Contains("hourly", fileReplication.Properties);
        Assert.Equal("FILE-REP-2024-002", fileReplication.LicenseKey);
        
        var appReplication = replications.First(r => r.Name == "Application Server Replication");
        Assert.Contains("real-time", appReplication.Properties);
        Assert.Contains("load_balancing", appReplication.Properties);
        Assert.Contains("failover_automatic", appReplication.Properties);
        Assert.Equal("APP-REP-2024-003", appReplication.LicenseKey);
        
        var storageReplication = replications.First(r => r.Name == "Storage Array Replication");
        Assert.Contains("storage-array-01", storageReplication.Properties);
        Assert.Contains("snapshot_frequency", storageReplication.Properties);
        Assert.Contains("deduplication", storageReplication.Properties);
        Assert.Equal("STORAGE-REP-2024-004", storageReplication.LicenseKey);
        
        var vmReplication = replications.First(r => r.Name == "Virtual Machine Replication");
        Assert.Contains("vmware-host-01", vmReplication.Properties);
        Assert.Contains("vm_list", vmReplication.Properties);
        Assert.Contains("quiesce_guest", vmReplication.Properties);
        Assert.Equal("VM-REP-2024-005", vmReplication.LicenseKey);
    }

    #endregion

    #region CreateReplication Tests

    [Fact]
    public async Task CreateReplication_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _replicationsFixture.CreateReplicationCommand;
        var expectedResponse = _replicationsFixture.CreateReplicationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplication(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationResponse>(createdResult.Value);
        Assert.Contains("has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationId);
    }

    [Fact]
    public async Task CreateReplication_WithCompleteReplicationData_CreatesSuccessfully()
    {
        // Arrange
        var command = new CreateReplicationCommand
        {
            Name = "Custom Database Replication",
            Type = "Database Replication",
            TypeId = "TYPE_CUSTOM_DB",
            CompanyId = "COMP_CUSTOM",
            SiteId = "SITE_CUSTOM",
            SiteName = "Custom Data Center",
            Properties = "{\"source_server\": \"custom-db-01\", \"target_server\": \"custom-db-02\", \"replication_mode\": \"synchronous\", \"compression\": true, \"encryption\": true, \"monitoring\": true, \"auto_failover\": true, \"backup_integration\": true}",
            LicenseId = "LIC_CUSTOM",
            LicenseKey = "CUSTOM-DB-REP-2024",
            BusinessServiceId = "BS_CUSTOM",
            BusinessServiceName = "Custom Business Service",
            FormVersion = "2.1"
        };

        var expectedResponse = new CreateReplicationResponse
        {
            ReplicationId = Guid.NewGuid().ToString(),
            Message = "Replication has been created successfully"
        };

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.CreateReplication(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<CreateReplicationResponse>(createdResult.Value);
        Assert.Equal("Replication has been created successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationId);
    }

    #endregion

    #region UpdateReplication Tests

    [Fact]
    public async Task UpdateReplication_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationsFixture.UpdateReplicationCommand;
        var expectedResponse = _replicationsFixture.UpdateReplicationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.UpdateReplication(command);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<UpdateReplicationResponse>(okResult.Value);
        Assert.Contains("has been updated successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.ReplicationId);
    }

    [Fact]
    public async Task UpdateReplication_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _replicationsFixture.UpdateReplicationCommand;
        command.Id = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("Replication", command.Id));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.UpdateReplication(command));
    }

    #endregion

    #region DeleteReplication Tests

    [Fact]
    public async Task DeleteReplication_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedResponse = _replicationsFixture.DeleteReplicationResponse;

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationCommand>(cmd => cmd.Id == validId), default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.DeleteReplication(validId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResponse = Assert.IsType<DeleteReplicationResponse>(okResult.Value);
        Assert.Contains("has been deleted successfully", returnedResponse.Message);
        Assert.False(returnedResponse.IsActive);
    }

    [Fact]
    public async Task DeleteReplication_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplication("invalid-guid"));
    }

    [Fact]
    public async Task DeleteReplication_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.DeleteReplication(""));
    }

    [Fact]
    public async Task DeleteReplication_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<DeleteReplicationCommand>(cmd => cmd.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("Replication", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.DeleteReplication(nonExistentId));
    }

    #endregion

    #region GetReplicationById Tests

    [Fact]
    public async Task GetReplicationById_WithValidId_ReturnsOkResult()
    {
        // Arrange
        var validId = Guid.NewGuid().ToString();
        var expectedDetail = new List<ReplicationDetailVm> { _replicationsFixture.ReplicationDetailVm };

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationDetailQuery>(q => q.Id == validId), default))
            .ReturnsAsync(new ReplicationDetailVm());

        // Act
        var result = await _controller.GetReplicationById(validId);
        
    }

    [Fact]
    public async Task GetReplicationById_WithInvalidId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationById("invalid-guid"));
    }

    [Fact]
    public async Task GetReplicationById_WithEmptyId_ThrowsInvalidArgumentException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() =>
            _controller.GetReplicationById(""));
    }

    [Fact]
    public async Task GetReplicationById_WithNonExistentId_ThrowsNotFoundException()
    {
        // Arrange
        var nonExistentId = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationDetailQuery>(q => q.Id == nonExistentId), default))
            .ThrowsAsync(new NotFoundException("Replication", nonExistentId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.GetReplicationById(nonExistentId));
    }

    #endregion

    #region SaveAsReplication Tests

    [Fact]
    public async Task SaveAsReplication_WithValidCommand_ReturnsCreatedResult()
    {
        // Arrange
        var command = _replicationsFixture.SaveAsReplicationCommand;
        var expectedResponse = _replicationsFixture.SaveAsReplicationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SaveAsReplication(command);

        // Assert
        var createdResult = Assert.IsType<CreatedAtActionResult>(result.Result);
        var returnedResponse = Assert.IsType<SaveAsReplicationResponse>(createdResult.Value);
        Assert.Contains("has been saved as successfully", returnedResponse.Message);
        Assert.NotNull(returnedResponse.Id);
    }

    [Fact]
    public async Task SaveAsReplication_WithNonExistentSourceId_ThrowsNotFoundException()
    {
        // Arrange
        var command = _replicationsFixture.SaveAsReplicationCommand;
        command.ReplicationId = "non-existent-id";

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ThrowsAsync(new NotFoundException("Replication", command.ReplicationId));

        // Act & Assert
        await Assert.ThrowsAsync<NotFoundException>(() =>
            _controller.SaveAsReplication(command));
    }

    #endregion

    #region SaveAllReplication Tests

    [Fact]
    public async Task SaveAllReplication_WithValidCommand_ReturnsOkResult()
    {
        // Arrange
        var command = _replicationsFixture.SaveAllReplicationCommand;
        var expectedResponse = _replicationsFixture.SaveAllReplicationResponse;

        _mediatorMock
            .Setup(m => m.Send(command, default))
            .ReturnsAsync(expectedResponse);

        // Act
        var result = await _controller.SaveAllReplication(command);

        // Assert
        var returnedResponse = Assert.IsType<SaveAllReplicationResponse>(result.Value);
        Assert.Contains("have been saved successfully", returnedResponse.Message);
    }

    #endregion

    #region GetReplicationByType Tests

    [Fact]
    public async Task GetReplicationByType_WithValidTypeId_ReturnsOkResult()
    {
        // Arrange
        var typeId = "TYPE_DB_001";
        var expectedTypes = _replicationsFixture.ReplicationTypeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationTypeQuery>(q => q.TypeId == typeId), default))
            .ReturnsAsync(expectedTypes);

        // Act
        var result = await _controller.GetReplicationByType(typeId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedTypes = Assert.IsType<List<ReplicationTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedTypes.Count);
        Assert.All(returnedTypes, rt => Assert.NotNull(rt.Name));
        Assert.All(returnedTypes, rt => Assert.NotNull(rt.Type));
    }

    [Fact]
    public async Task GetReplicationByType_WithNullTypeId_ReturnsAllTypes()
    {
        // Arrange
        var expectedTypes = _replicationsFixture.ReplicationTypeListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationTypeQuery>(q => q.TypeId == null), default))
            .ReturnsAsync(expectedTypes);

        // Act
        var result = await _controller.GetReplicationByType(null);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedTypes = Assert.IsType<List<ReplicationTypeVm>>(okResult.Value);
        Assert.Equal(2, returnedTypes.Count);
    }

    #endregion

    #region GetReplicationByLicenseKey Tests

    [Fact]
    public async Task GetReplicationByLicenseKey_WithValidLicenseId_ReturnsOkResult()
    {
        // Arrange
        var licenseId = "LIC_001";
        var expectedReplications = _replicationsFixture.ReplicationByLicenseKeyListVm;

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationByLicenseKeyQuery>(q => q.LicenseId == licenseId), default))
            .ReturnsAsync(expectedReplications);

        // Act
        var result = await _controller.GetReplicationByLicenseKey(licenseId);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedReplications = Assert.IsType<List<GetReplicationByLicenseKeyListVm>>(okResult.Value);
        Assert.Single(returnedReplications);
        var replication = returnedReplications.First();
        Assert.Equal("Primary Database Replication", replication.Name);
        Assert.Equal("LIC_001", replication.LicenseId);
    }

    #endregion

    #region GetPaginatedReplications Tests

    [Fact]
    public async Task GetPaginatedReplications_WithValidQuery_ReturnsOkResult()
    {
        // Arrange
        var query = _replicationsFixture.GetReplicationPaginatedListQuery;
        var expectedResult = _replicationsFixture.ReplicationPaginatedListVm;

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedResult);

        // Act
        var result = await _controller.GetPaginatedReplications(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationListVm>>(okResult.Value);
        Assert.Equal(expectedResult.TotalCount, returnedResult.TotalCount);
        Assert.Equal(expectedResult.CurrentPage, returnedResult.CurrentPage);
        Assert.Equal(expectedResult.PageSize, returnedResult.PageSize);
        Assert.Equal(5, returnedResult.Data.Count);
    }

    [Fact]
    public async Task GetPaginatedReplications_WithSearchString_ReturnsFilteredResults()
    {
        // Arrange
        var query = new GetReplicationPaginatedListQuery
        {
            PageNumber = 1,
            PageSize = 10,
            SearchString = "Database",
            SortColumn = "Name",
            SortOrder = "asc"
        };

        var filteredResult = new PaginatedResult<ReplicationListVm>
        {
            Data = _replicationsFixture.ReplicationListVm.Where(r => r.Name.Contains("Database")).ToList(),
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 1,
            PageSize = 10
        };

        _mediatorMock
            .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
            .ReturnsAsync(filteredResult);

        // Act
        var result = await _controller.GetPaginatedReplications(query);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var returnedResult = Assert.IsType<PaginatedResult<ReplicationListVm>>(okResult.Value);
        Assert.Equal(1, returnedResult.TotalCount);
        Assert.Single(returnedResult.Data);
        Assert.Contains("Database", returnedResult.Data.First().Name);
    }

    #endregion

    #region GetReplicationNames Tests

    [Fact]
    public async Task GetReplicationNames_ReturnsExpectedList()
    {
        // Arrange
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<GetReplicationNameQuery>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(_replicationsFixture.ReplicationNameListVm);

        // Act
        var result = await _controller.GetReplicationNames();

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result.Result);
        var replicationNames = Assert.IsAssignableFrom<List<ReplicationNameVm>>(okResult.Value);
        Assert.Equal(5, replicationNames.Count);
        Assert.All(replicationNames, rn => Assert.NotNull(rn.Name));
        Assert.All(replicationNames, rn => Assert.NotNull(rn.Id));
    }

    #endregion

    #region IsReplicationNameUnique Tests

    [Fact]
    public async Task IsReplicationNameUnique_WithUniqueName_ReturnsTrue()
    {
        // Arrange
        var uniqueName = "Unique Replication Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationNameUniqueQuery>(q => q.ReplicationName == uniqueName && q.ReplicationId == id), default))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.IsReplicationNameExist(uniqueName, id);

    }

    [Fact]
    public async Task IsReplicationNameUnique_WithExistingName_ReturnsFalse()
    {
        // Arrange
        var existingName = "Existing Replication Name";
        var id = Guid.NewGuid().ToString();

        _mediatorMock
            .Setup(m => m.Send(It.Is<GetReplicationNameUniqueQuery>(q => q.ReplicationName == existingName && q.ReplicationId == id), default))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.IsReplicationNameExist(existingName, id);

    }

    #endregion

    #region ClearDataCache Tests

    [Fact]
    public void ClearDataCache_ClearsReplicationCache()
    {
        // Act & Assert - Should not throw exception
        _controller.ClearDataCache();
    }

    #endregion
}
