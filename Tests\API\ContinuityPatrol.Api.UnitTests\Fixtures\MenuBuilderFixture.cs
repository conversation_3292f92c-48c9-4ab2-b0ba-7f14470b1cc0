using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Create;
using ContinuityPatrol.Application.Features.MenuBuilder.Commands.Update;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MenuBuilder.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MenuBuilderModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MenuBuilderFixture : IDisposable
{
    public List<MenuBuilderListVm> MenuBuilderListVm { get; }
    public PaginatedResult<MenuBuilderListVm> PaginatedMenuBuilderListVm { get; }
    public MenuBuilderDetailVm MenuBuilderDetailVm { get; }
    public CreateMenuBuilderCommand CreateMenuBuilderCommand { get; }
    public UpdateMenuBuilderCommand UpdateMenuBuilderCommand { get; }
    public GetMenuBuilderPaginatedListQuery GetMenuBuilderPaginatedListQuery { get; }

    public MenuBuilderFixture()
    {
        var fixture = new Fixture();

        MenuBuilderListVm = fixture.Create<List<MenuBuilderListVm>>();
        PaginatedMenuBuilderListVm = fixture.Create<PaginatedResult<MenuBuilderListVm>>();
        MenuBuilderDetailVm = fixture.Create<MenuBuilderDetailVm>();
        CreateMenuBuilderCommand = fixture.Create<CreateMenuBuilderCommand>();
        UpdateMenuBuilderCommand = fixture.Create<UpdateMenuBuilderCommand>();
        GetMenuBuilderPaginatedListQuery = fixture.Create<GetMenuBuilderPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
