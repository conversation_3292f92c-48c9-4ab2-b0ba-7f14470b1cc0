using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDBMonitorLog.Queries.GetPaginatedList;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MongoDbMonitorLogFixture : IDisposable
{
    public List<MongoDBMonitorLogListVm> MongoDbMonitorLogListVm { get; }
    public List<MongoDBMonitorLogPaginatedListVm> PaginatedMongoDbMonitorLogListVm { get; }
    public MongoDBMonitorLogDetailVm MongoDbMonitorLogDetailVm { get; }
    public MongoDBMonitorLogDetailByTypeVm MongoDbMonitorLogDetailByTypeVm { get; }
    public CreateMongoDBMonitorLogCommand CreateMongoDbMonitorLogCommand { get; }
    public GetMongoDBMonitorLogPaginatedListQuery GetMongoDbMonitorLogPaginatedListQuery { get; }

    public MongoDbMonitorLogFixture()
    {
        var fixture = new Fixture();

        MongoDbMonitorLogListVm = fixture.Create<List<MongoDBMonitorLogListVm>>();
        PaginatedMongoDbMonitorLogListVm = fixture.Create<List<MongoDBMonitorLogPaginatedListVm>>();
        MongoDbMonitorLogDetailVm = fixture.Create<MongoDBMonitorLogDetailVm>();
        MongoDbMonitorLogDetailByTypeVm = fixture.Create<MongoDBMonitorLogDetailByTypeVm>();
        CreateMongoDbMonitorLogCommand = fixture.Create<CreateMongoDBMonitorLogCommand>();
        GetMongoDbMonitorLogPaginatedListQuery = fixture.Create<GetMongoDBMonitorLogPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
