using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MongoDbMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MongoDbMonitorStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MongoDbMonitorStatusFixture : IDisposable
{
    public List<MongoDbMonitorStatusListVm> MongoDbMonitorStatusListVm { get; }
    public List<MongoDbMonitorStatusListVm> PaginatedMongoDbMonitorStatusListVm { get; }
    public MongoDbMonitorStatusDetailVm MongoDbMonitorStatusDetailVm { get; }
    public MongoDbMonitorStatusDetailByTypeVm MongoDbMonitorStatusDetailByTypeVm { get; }
    public CreateMongoDbMonitorStatusCommand CreateMongoDbMonitorStatusCommand { get; }
    public UpdateMongoDbMonitorStatusCommand UpdateMongoDbMonitorStatusCommand { get; }
    public GetMongoDbMonitorStatusPaginatedListQuery GetMongoDbMonitorStatusPaginatedListQuery { get; }

    public MongoDbMonitorStatusFixture()
    {
        var fixture = new Fixture();

        MongoDbMonitorStatusListVm = fixture.Create<List<MongoDbMonitorStatusListVm>>();
        PaginatedMongoDbMonitorStatusListVm = fixture.Create<List<MongoDbMonitorStatusListVm>>();
        MongoDbMonitorStatusDetailVm = fixture.Create<MongoDbMonitorStatusDetailVm>();
        MongoDbMonitorStatusDetailByTypeVm = fixture.Create<MongoDbMonitorStatusDetailByTypeVm>();
        CreateMongoDbMonitorStatusCommand = fixture.Create<CreateMongoDbMonitorStatusCommand>();
        UpdateMongoDbMonitorStatusCommand = fixture.Create<UpdateMongoDbMonitorStatusCommand>();
        GetMongoDbMonitorStatusPaginatedListQuery = fixture.Create<GetMongoDbMonitorStatusPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
