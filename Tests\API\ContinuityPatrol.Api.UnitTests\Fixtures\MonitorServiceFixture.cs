using ContinuityPatrol.Application.Features.MonitorService.Command.Create;
using ContinuityPatrol.Application.Features.MonitorService.Command.Update;
using ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MonitorServiceFixture : IDisposable
{
    public List<MonitorServiceListVm> MonitorServiceListVm { get; }
    public PaginatedResult<GetMonitorServicePaginatedListVm> PaginatedMonitorServiceListVm { get; }
    public GetMonitorServiceDetailVm MonitorServiceDetailVm { get; }
    public List<GetMonitorServiceNameVm> MonitorServiceNameVmList { get; }
    public CreateMonitorServiceCommand CreateMonitorServiceCommand { get; }
    public UpdateMonitorServiceCommand UpdateMonitorServiceCommand { get; }
    public UpdateMonitorServiceStatusCommand UpdateMonitorServiceStatusCommand { get; }
    public GetMonitorServicePaginatedListQuery GetMonitorServicePaginatedListQuery { get; }

    public MonitorServiceFixture()
    {
        var fixture = new Fixture();

        MonitorServiceListVm = fixture.Create<List<MonitorServiceListVm>>();
        PaginatedMonitorServiceListVm = fixture.Create<PaginatedResult<GetMonitorServicePaginatedListVm>>();
        MonitorServiceDetailVm = fixture.Create<GetMonitorServiceDetailVm>();
        MonitorServiceNameVmList = fixture.Create<List<GetMonitorServiceNameVm>>();
        CreateMonitorServiceCommand = fixture.Create<CreateMonitorServiceCommand>();
        UpdateMonitorServiceCommand = fixture.Create<UpdateMonitorServiceCommand>();
        UpdateMonitorServiceStatusCommand = fixture.Create<UpdateMonitorServiceStatusCommand>();
        GetMonitorServicePaginatedListQuery = fixture.Create<GetMonitorServicePaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
