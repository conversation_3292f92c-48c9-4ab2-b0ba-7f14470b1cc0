using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLNativeLogShippingMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MsSqlNativeLogShippingMonitorStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MsSqlNativeLogShippingMonitorStatusFixture : IDisposable
{
    public List<MsSqlNativeLogShippingMonitorStatusListVm> MsSqlNativeLogShippingMonitorStatusListVm { get; }
    public List<MsSqlNativeLogShippingMonitorStatusListVm> PaginatedMsSqlNativeLogShippingMonitorStatusListVm { get; }
    public MsSqlNativeLogShippingMonitorStatusDetailVm MsSqlNativeLogShippingMonitorStatusDetailVm { get; }
    public MsSqlNativeLogShippingMonitorStatusDetailByTypeVm MsSqlNativeLogShippingMonitorStatusDetailByTypeVm { get; }
    public CreateMsSqlNativeLogShippingMonitorStatusCommand CreateMsSqlNativeLogShippingMonitorStatusCommand { get; }
    public UpdateMssqlNativeLogShippingMonitorStatusCommand UpdateMsSqlNativeLogShippingMonitorStatusCommand { get; }
    public GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery { get; }

    public MsSqlNativeLogShippingMonitorStatusFixture()
    {
        var fixture = new Fixture();

        MsSqlNativeLogShippingMonitorStatusListVm = fixture.Create<List<MsSqlNativeLogShippingMonitorStatusListVm>>();
        PaginatedMsSqlNativeLogShippingMonitorStatusListVm = fixture.Create<List<MsSqlNativeLogShippingMonitorStatusListVm>>();
        MsSqlNativeLogShippingMonitorStatusDetailVm = fixture.Create<MsSqlNativeLogShippingMonitorStatusDetailVm>();
        MsSqlNativeLogShippingMonitorStatusDetailByTypeVm = fixture.Create<MsSqlNativeLogShippingMonitorStatusDetailByTypeVm>();
        CreateMsSqlNativeLogShippingMonitorStatusCommand = fixture.Create<CreateMsSqlNativeLogShippingMonitorStatusCommand>();
        UpdateMsSqlNativeLogShippingMonitorStatusCommand = fixture.Create<UpdateMssqlNativeLogShippingMonitorStatusCommand>();
        GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery = fixture.Create<GetMsSqlNativeLogShippingMonitorStatusPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
