using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MssqlAlwaysOnMonitorLogsFixture : IDisposable
{
    public List<MssqlAlwaysOnMonitorLogsListVm> MssqlAlwaysOnMonitorLogsListVm { get; }
    public PaginatedResult<MssqlAlwaysOnMonitorLogsListVm> PaginatedMssqlAlwaysOnMonitorLogsListVm { get; }
    public MSSQLAlwaysOnMonitorLogsDetailVm MssqlAlwaysOnMonitorLogsDetailVm { get; }
    public MSSQLAlwaysOnMonitorLogsDetailByTypeVm MssqlAlwaysOnMonitorLogsDetailByTypeVm { get; }
    public CreateMSSQLAlwaysOnMonitorLogCommand CreateMssqlAlwaysOnMonitorLogCommand { get; }
    public GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery GetMssqlAlwaysOnMonitorLogsPaginatedListQuery { get; }

    public MssqlAlwaysOnMonitorLogsFixture()
    {
        var fixture = new Fixture();

        MssqlAlwaysOnMonitorLogsListVm = fixture.Create<List<MssqlAlwaysOnMonitorLogsListVm>>();
        PaginatedMssqlAlwaysOnMonitorLogsListVm = fixture.Create<PaginatedResult<MssqlAlwaysOnMonitorLogsListVm>>();
        MssqlAlwaysOnMonitorLogsDetailVm = fixture.Create<MSSQLAlwaysOnMonitorLogsDetailVm>();
        MssqlAlwaysOnMonitorLogsDetailByTypeVm = fixture.Create<MSSQLAlwaysOnMonitorLogsDetailByTypeVm>();
        CreateMssqlAlwaysOnMonitorLogCommand = fixture.Create<CreateMSSQLAlwaysOnMonitorLogCommand>();
        GetMssqlAlwaysOnMonitorLogsPaginatedListQuery = fixture.Create<GetMSSQLAlwaysOnMonitorLogsPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
