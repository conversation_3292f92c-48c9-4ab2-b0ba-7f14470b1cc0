using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLAlwaysOnMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLAlwaysOnMonitorStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MssqlAlwaysOnMonitorStatusFixture : IDisposable
{
    public List<MSSQLAlwaysOnMonitorStatusListVm> MssqlAlwaysOnMonitorStatusListVm { get; }
    public List<MSSQLAlwaysOnMonitorStatusListVm> PaginatedMssqlAlwaysOnMonitorStatusListVm { get; }
    public MSSQLAlwaysOnMonitorStatusDetailVm MssqlAlwaysOnMonitorStatusDetailVm { get; }
    public MSSQLAlwaysOnMonitorStatusDetailByTypeVm MssqlAlwaysOnMonitorStatusDetailByTypeVm { get; }
    public CreateMSSQLAlwaysOnMonitorStatusCommand CreateMssqlAlwaysOnMonitorStatusCommand { get; }
    public GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery GetMssqlAlwaysOnMonitorStatusPaginatedListQuery { get; }

    public MssqlAlwaysOnMonitorStatusFixture()
    {
        var fixture = new Fixture();

        MssqlAlwaysOnMonitorStatusListVm = fixture.Create<List<MSSQLAlwaysOnMonitorStatusListVm>>();
        PaginatedMssqlAlwaysOnMonitorStatusListVm = fixture.Create<List<MSSQLAlwaysOnMonitorStatusListVm>>();
        MssqlAlwaysOnMonitorStatusDetailVm = fixture.Create<MSSQLAlwaysOnMonitorStatusDetailVm>();
        MssqlAlwaysOnMonitorStatusDetailByTypeVm = fixture.Create<MSSQLAlwaysOnMonitorStatusDetailByTypeVm>();
        CreateMssqlAlwaysOnMonitorStatusCommand = fixture.Create<CreateMSSQLAlwaysOnMonitorStatusCommand>();
        GetMssqlAlwaysOnMonitorStatusPaginatedListQuery = fixture.Create<GetMSSQLAlwaysOnMonitorStatusPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
