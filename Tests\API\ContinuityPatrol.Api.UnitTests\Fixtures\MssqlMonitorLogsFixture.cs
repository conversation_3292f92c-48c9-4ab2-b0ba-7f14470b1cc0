using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorLogsModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MssqlMonitorLogsFixture : IDisposable
{
    public List<MSSQLMonitorLogsListVm> MssqlMonitorLogsListVm { get; }
    public List<MSSQLMonitorLogsDetailVm> PaginatedMssqlMonitorLogsListVm { get; }
    public MSSQLMonitorLogsDetailVm MssqlMonitorLogsDetailVm { get; }
    public MSSQLMonitorLogsDetailByTypeVm MssqlMonitorLogsDetailByTypeVm { get; }
    public CreateMSSQLMonitorLogCommand CreateMssqlMonitorLogCommand { get; }
    public GetMSSQLMonitorLogsPaginatedListQuery GetMssqlMonitorLogsPaginatedListQuery { get; }

    public MssqlMonitorLogsFixture()
    {
        var fixture = new Fixture();

        MssqlMonitorLogsListVm = fixture.Create<List<MSSQLMonitorLogsListVm>>();
        PaginatedMssqlMonitorLogsListVm = fixture.Create<List<MSSQLMonitorLogsDetailVm>>();
        MssqlMonitorLogsDetailVm = fixture.Create<MSSQLMonitorLogsDetailVm>();
        MssqlMonitorLogsDetailByTypeVm = fixture.Create<MSSQLMonitorLogsDetailByTypeVm>();
        CreateMssqlMonitorLogCommand = fixture.Create<CreateMSSQLMonitorLogCommand>();
        GetMssqlMonitorLogsPaginatedListQuery = fixture.Create<GetMSSQLMonitorLogsPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
