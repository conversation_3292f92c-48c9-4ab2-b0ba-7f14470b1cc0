using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Create;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Commands.Update;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetDetail;
using ContinuityPatrol.Application.Features.MSSQLMonitorStatus.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MSSQLMonitorStatusModel;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class MssqlMonitorStatusFixture : IDisposable
{
    public List<MSSQLMonitorStatusListVm> MssqlMonitorStatusListVm { get; }
    public List<MSSQLMonitorStatusDetailVm> PaginatedMssqlMonitorStatusListVm { get; }
    public MSSQLMonitorStatusDetailVm MssqlMonitorStatusDetailVm { get; }
    public MSSQLMonitorStatusDetailByTypeVm MssqlMonitorStatusDetailByTypeVm { get; }
    public CreateMSSQLMonitorStatusCommand CreateMssqlMonitorStatusCommand { get; }
    public UpdateMSSQLMonitorStatusCommand UpdateMssqlMonitorStatusCommand { get; }
    public GetMSSQLMonitorStatusPaginatedListQuery GetMssqlMonitorStatusPaginatedListQuery { get; }

    public MssqlMonitorStatusFixture()
    {
        var fixture = new Fixture();

        MssqlMonitorStatusListVm = fixture.Create<List<MSSQLMonitorStatusListVm>>();
        PaginatedMssqlMonitorStatusListVm = fixture.Create<List<MSSQLMonitorStatusDetailVm>>();
        MssqlMonitorStatusDetailVm = fixture.Create<MSSQLMonitorStatusDetailVm>();
        MssqlMonitorStatusDetailByTypeVm = fixture.Create<MSSQLMonitorStatusDetailByTypeVm>();
        CreateMssqlMonitorStatusCommand = fixture.Create<CreateMSSQLMonitorStatusCommand>();
        UpdateMssqlMonitorStatusCommand = fixture.Create<UpdateMSSQLMonitorStatusCommand>();
        GetMssqlMonitorStatusPaginatedListQuery = fixture.Create<GetMSSQLMonitorStatusPaginatedListQuery>();
    }

    public void Dispose()
    {

    }
}
