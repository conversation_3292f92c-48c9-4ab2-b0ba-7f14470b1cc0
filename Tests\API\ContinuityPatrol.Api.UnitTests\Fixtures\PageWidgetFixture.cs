using AutoFixture;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Create;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Delete;
using ContinuityPatrol.Application.Features.PageWidget.Commands.Update;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetList;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.PageWidget.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.PageWidgetModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PageWidgetFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<PageWidget> PageWidgets { get; set; }
    public List<PageWidget> InvalidPageWidgets { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<PageWidgetListVm> PageWidgetListVm { get; }
    public PageWidgetDetailVm PageWidgetDetailVm { get; }
    public PaginatedResult<PageWidgetListVm> PageWidgetPaginatedListVm { get; }

    // Commands
    public CreatePageWidgetCommand CreatePageWidgetCommand { get; set; }
    public UpdatePageWidgetCommand UpdatePageWidgetCommand { get; set; }
    public DeletePageWidgetCommand DeletePageWidgetCommand { get; set; }

    // Queries
    public GetPageWidgetDetailQuery GetPageWidgetDetailQuery { get; set; }
    public GetPageWidgetListQuery GetPageWidgetListQuery { get; set; }
    public GetPageWidgetPaginatedListQuery GetPageWidgetPaginatedListQuery { get; set; }
    public GetPageWidgetNameUniqueQuery GetPageWidgetNameUniqueQuery { get; set; }

    // Responses
    public CreatePageWidgetResponse CreatePageWidgetResponse { get; set; }
    public UpdatePageWidgetResponse UpdatePageWidgetResponse { get; set; }
    public DeletePageWidgetResponse DeletePageWidgetResponse { get; set; }

    public PageWidgetFixture()
    {
        try
        {
            // Create test data using AutoFixture
            PageWidgets = AutoPageWidgetFixture.Create<List<PageWidget>>();
            InvalidPageWidgets = AutoPageWidgetFixture.Create<List<PageWidget>>();
            UserActivities = AutoPageWidgetFixture.Create<List<UserActivity>>();

            // Set invalid page widgets to inactive
            foreach (var invalidWidget in InvalidPageWidgets)
            {
                invalidWidget.IsActive = false;
            }

            // Commands
            CreatePageWidgetCommand = AutoPageWidgetFixture.Create<CreatePageWidgetCommand>();
            UpdatePageWidgetCommand = AutoPageWidgetFixture.Create<UpdatePageWidgetCommand>();
            DeletePageWidgetCommand = AutoPageWidgetFixture.Create<DeletePageWidgetCommand>();

            // Set command IDs to match existing entities
            if (PageWidgets.Any())
            {
                UpdatePageWidgetCommand.Id = PageWidgets.First().ReferenceId;
                DeletePageWidgetCommand.Id = PageWidgets.First().ReferenceId;
            }

            // Queries
            GetPageWidgetDetailQuery = AutoPageWidgetFixture.Create<GetPageWidgetDetailQuery>();
            GetPageWidgetListQuery = AutoPageWidgetFixture.Create<GetPageWidgetListQuery>();
            GetPageWidgetPaginatedListQuery = AutoPageWidgetFixture.Create<GetPageWidgetPaginatedListQuery>();
            GetPageWidgetNameUniqueQuery = AutoPageWidgetFixture.Create<GetPageWidgetNameUniqueQuery>();

            // Set query IDs to match existing entities
            if (PageWidgets.Any())
            {
                GetPageWidgetDetailQuery.Id = PageWidgets.First().ReferenceId;
                GetPageWidgetNameUniqueQuery.Name = PageWidgets.First().Name;
            }

            // Responses
            CreatePageWidgetResponse = AutoPageWidgetFixture.Create<CreatePageWidgetResponse>();
            UpdatePageWidgetResponse = AutoPageWidgetFixture.Create<UpdatePageWidgetResponse>();
            DeletePageWidgetResponse = AutoPageWidgetFixture.Create<DeletePageWidgetResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            PageWidgets = new List<PageWidget>();
            InvalidPageWidgets = new List<PageWidget>();
            UserActivities = new List<UserActivity>();
            CreatePageWidgetCommand = new CreatePageWidgetCommand();
            UpdatePageWidgetCommand = new UpdatePageWidgetCommand();
            DeletePageWidgetCommand = new DeletePageWidgetCommand();
            GetPageWidgetDetailQuery = new GetPageWidgetDetailQuery();
            GetPageWidgetListQuery = new GetPageWidgetListQuery();
            GetPageWidgetPaginatedListQuery = new GetPageWidgetPaginatedListQuery();
            GetPageWidgetNameUniqueQuery = new GetPageWidgetNameUniqueQuery();
            CreatePageWidgetResponse = new CreatePageWidgetResponse();
            UpdatePageWidgetResponse = new UpdatePageWidgetResponse();
            DeletePageWidgetResponse = new DeletePageWidgetResponse();
        }

        // Configure View Models
        PageWidgetListVm = new List<PageWidgetListVm>
        {
            new PageWidgetListVm
            {
                Id = "PW_001",
                Name = "System Performance Widget",
                Properties = "{\"type\": \"chart\", \"chartType\": \"line\", \"dataSource\": \"performance\", \"refreshInterval\": 30}"
            },
            new PageWidgetListVm
            {
                Id = "PW_002",
                Name = "Disk Usage Widget",
                Properties = "{\"type\": \"gauge\", \"threshold\": 90, \"dataSource\": \"disk\", \"unit\": \"percentage\"}"
            },
            new PageWidgetListVm
            {
                Id = "PW_003",
                Name = "Network Traffic Widget",
                Properties = "{\"type\": \"area\", \"metrics\": [\"inbound\", \"outbound\"], \"dataSource\": \"network\", \"timeRange\": \"24h\"}"
            },
            new PageWidgetListVm
            {
                Id = "PW_004",
                Name = "Alert Summary Widget",
                Properties = "{\"type\": \"table\", \"columns\": [\"severity\", \"message\", \"timestamp\"], \"dataSource\": \"alerts\", \"maxRows\": 10}"
            },
            new PageWidgetListVm
            {
                Id = "PW_005",
                Name = "Server Status Widget",
                Properties = "{\"type\": \"status\", \"indicators\": [\"cpu\", \"memory\", \"disk\"], \"dataSource\": \"servers\", \"layout\": \"grid\"}"
            }
        };

        PageWidgetDetailVm = new PageWidgetDetailVm
        {
            Id = 1,
            ReferenceId = "PW_001",
            Name = "System Performance Widget",
            Properties = "{\"type\": \"chart\", \"chartType\": \"line\", \"dataSource\": \"performance\", \"refreshInterval\": 30}",
            IsActive = true,
            CreatedBy = "<EMAIL>",
            CreatedDate = DateTime.UtcNow.AddDays(-30),
            LastModifiedBy = "<EMAIL>",
            LastModifiedDate = DateTime.UtcNow.AddDays(-1)
        };

        PageWidgetPaginatedListVm = new PaginatedResult<PageWidgetListVm>
        {
            Data = PageWidgetListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        // Configure AutoMapper for PageWidget mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PageWidgetProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoPageWidgetFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize PageWidget entity
            fixture.Customize<PageWidget>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Widget")
                .With(b => b.Properties, "{\"type\": \"test\", \"status\": \"active\"}"));

            // Customize CreatePageWidgetCommand
            fixture.Customize<CreatePageWidgetCommand>(c => c
                .With(b => b.Name, "New Test Widget")
                .With(b => b.Properties, "{\"type\": \"new\", \"status\": \"active\"}"));

            // Customize UpdatePageWidgetCommand
            fixture.Customize<UpdatePageWidgetCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Widget")
                .With(b => b.Properties, "{\"type\": \"updated\", \"status\": \"active\"}"));

            // Customize DeletePageWidgetCommand
            fixture.Customize<DeletePageWidgetCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetPageWidgetDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetPageWidgetPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetPageWidgetNameUniqueQuery>(c => c
                .With(b => b.Name, "Test Widget")
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreatePageWidgetResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "PageWidget has been created successfully"));

            fixture.Customize<UpdatePageWidgetResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "PageWidget has been updated successfully"));

            fixture.Customize<DeletePageWidgetResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "PageWidget has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "PageWidget")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
