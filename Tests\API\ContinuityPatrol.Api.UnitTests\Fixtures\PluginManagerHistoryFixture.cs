using AutoFixture;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Create;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Delete;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Commands.Update;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PluginManagerHistory.Queries.GetList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.PluginManagerHistoryModel;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PluginManagerHistoryFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<PluginManagerHistory> PluginManagerHistories { get; set; }
    public List<PluginManagerHistory> InvalidPluginManagerHistories { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<PluginManagerHistoryListVm> PluginManagerHistoryListVm { get; }
    public List<PluginManagerHistoryDetailVm> PluginManagerHistoryDetailVm { get; }

    // Commands
    public CreatePluginManagerHistoryCommand CreatePluginManagerHistoryCommand { get; set; }
    public UpdatePluginManagerHistoryCommand UpdatePluginManagerHistoryCommand { get; set; }
    public DeletePluginManagerHistoryCommand DeletePluginManagerHistoryCommand { get; set; }

    // Queries
    public GetPluginManagerHistoryDetailQuery GetPluginManagerHistoryDetailQuery { get; set; }
    public GetPluginManagerHistoryListQuery GetPluginManagerHistoryListQuery { get; set; }

    // Responses
    public CreatePluginManagerHistoryResponse CreatePluginManagerHistoryResponse { get; set; }
    public UpdatePluginManagerHistoryResponse UpdatePluginManagerHistoryResponse { get; set; }
    public DeletePluginManagerHistoryResponse DeletePluginManagerHistoryResponse { get; set; }

    public PluginManagerHistoryFixture()
    {
        try
        {
            // Create test data using AutoFixture
            PluginManagerHistories = AutoPluginManagerHistoryFixture.Create<List<PluginManagerHistory>>();
            InvalidPluginManagerHistories = AutoPluginManagerHistoryFixture.Create<List<PluginManagerHistory>>();
            UserActivities = AutoPluginManagerHistoryFixture.Create<List<UserActivity>>();

            // Set invalid plugin manager histories to inactive
            foreach (var invalidHistory in InvalidPluginManagerHistories)
            {
                invalidHistory.IsActive = false;
            }

            // Commands
            CreatePluginManagerHistoryCommand = AutoPluginManagerHistoryFixture.Create<CreatePluginManagerHistoryCommand>();
            UpdatePluginManagerHistoryCommand = AutoPluginManagerHistoryFixture.Create<UpdatePluginManagerHistoryCommand>();
            DeletePluginManagerHistoryCommand = AutoPluginManagerHistoryFixture.Create<DeletePluginManagerHistoryCommand>();

            // Set command IDs to match existing entities
            if (PluginManagerHistories.Any())
            {
                UpdatePluginManagerHistoryCommand.Id = PluginManagerHistories.First().ReferenceId;
                DeletePluginManagerHistoryCommand.Id = PluginManagerHistories.First().ReferenceId;
            }

            // Queries
            GetPluginManagerHistoryDetailQuery = AutoPluginManagerHistoryFixture.Create<GetPluginManagerHistoryDetailQuery>();
            GetPluginManagerHistoryListQuery = AutoPluginManagerHistoryFixture.Create<GetPluginManagerHistoryListQuery>();

            // Set query IDs to match existing entities
            if (PluginManagerHistories.Any())
            {
                GetPluginManagerHistoryDetailQuery.PluginManagerId = PluginManagerHistories.First().PluginManagerId;
            }

            // Responses
            CreatePluginManagerHistoryResponse = AutoPluginManagerHistoryFixture.Create<CreatePluginManagerHistoryResponse>();
            UpdatePluginManagerHistoryResponse = AutoPluginManagerHistoryFixture.Create<UpdatePluginManagerHistoryResponse>();
            DeletePluginManagerHistoryResponse = AutoPluginManagerHistoryFixture.Create<DeletePluginManagerHistoryResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            PluginManagerHistories = new List<PluginManagerHistory>();
            InvalidPluginManagerHistories = new List<PluginManagerHistory>();
            UserActivities = new List<UserActivity>();
            CreatePluginManagerHistoryCommand = new CreatePluginManagerHistoryCommand();
            UpdatePluginManagerHistoryCommand = new UpdatePluginManagerHistoryCommand();
            DeletePluginManagerHistoryCommand = new DeletePluginManagerHistoryCommand();
            GetPluginManagerHistoryDetailQuery = new GetPluginManagerHistoryDetailQuery();
            GetPluginManagerHistoryListQuery = new GetPluginManagerHistoryListQuery();
            CreatePluginManagerHistoryResponse = new CreatePluginManagerHistoryResponse();
            UpdatePluginManagerHistoryResponse = new UpdatePluginManagerHistoryResponse();
            DeletePluginManagerHistoryResponse = new DeletePluginManagerHistoryResponse();
        }

        // Configure View Models
        PluginManagerHistoryListVm = new List<PluginManagerHistoryListVm>
        {
            new PluginManagerHistoryListVm
            {
                Id = "PMH_001",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Properties = "{\"type\": \"backup\", \"database\": \"postgresql\", \"schedule\": \"daily\"}",
                Version = "1.0.0",
                UpdaterId = "USER_001",
                Description = "Plugin for automated database backup operations",
                Comments = "Initial version with basic backup functionality"
            },
            new PluginManagerHistoryListVm
            {
                Id = "PMH_002",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_002",
                PluginManagerName = "File Replication Plugin",
                Properties = "{\"type\": \"replication\", \"source\": \"local\", \"target\": \"remote\"}",
                Version = "2.1.0",
                UpdaterId = "USER_002",
                Description = "Plugin for file system replication and synchronization",
                Comments = "Updated version with improved error handling"
            },
            new PluginManagerHistoryListVm
            {
                Id = "PMH_003",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_003",
                PluginManagerName = "Monitoring Alert Plugin",
                Properties = "{\"type\": \"monitoring\", \"alerts\": \"email\", \"threshold\": \"critical\"}",
                Version = "1.5.2",
                UpdaterId = "USER_003",
                Description = "Plugin for system monitoring and alerting",
                Comments = "Bug fix release for alert notification issues"
            },
            new PluginManagerHistoryListVm
            {
                Id = "PMH_004",
                CompanyId = "COMP_002",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_004",
                PluginManagerName = "Security Audit Plugin",
                Properties = "{\"type\": \"security\", \"audit\": \"compliance\", \"reports\": \"weekly\"}",
                Version = "3.0.0",
                UpdaterId = "USER_004",
                Description = "Plugin for security auditing and compliance reporting",
                Comments = "Major version upgrade with new compliance features"
            },
            new PluginManagerHistoryListVm
            {
                Id = "PMH_005",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_005",
                PluginManagerName = "Data Migration Plugin",
                Properties = "{\"type\": \"migration\", \"source\": \"legacy\", \"target\": \"modern\"}",
                Version = "1.2.1",
                UpdaterId = "USER_005",
                Description = "Plugin for data migration between different systems",
                Comments = "Performance improvements and additional data type support"
            }
        };

        PluginManagerHistoryDetailVm = new List<PluginManagerHistoryDetailVm>
        {
            new PluginManagerHistoryDetailVm
            {
                Id = "PMH_001",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Properties = "{\"type\": \"backup\", \"database\": \"postgresql\", \"schedule\": \"daily\"}",
                Version = "1.0.0",
                UpdaterId = "USER_001",
                Description = "Plugin for automated database backup operations",
                Comments = "Initial version with basic backup functionality"
            },
            new PluginManagerHistoryDetailVm
            {
                Id = "PMH_006",
                CompanyId = "COMP_001",
                LoginName = "<EMAIL>",
                PluginManagerId = "PM_001",
                PluginManagerName = "Database Backup Plugin",
                Properties = "{\"type\": \"backup\", \"database\": \"postgresql\", \"schedule\": \"daily\", \"compression\": \"enabled\"}",
                Version = "1.1.0",
                UpdaterId = "USER_001",
                Description = "Plugin for automated database backup operations with compression",
                Comments = "Added compression support for backup files"
            }
        };

        // Configure AutoMapper for PluginManagerHistory mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PluginManagerHistoryProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoPluginManagerHistoryFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize PluginManagerHistory entity
            fixture.Customize<PluginManagerHistory>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.CompanyId, "COMP_TEST")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.PluginManagerId, "PM_TEST")
                .With(b => b.PluginManagerName, "Test Plugin")
                .With(b => b.Properties, "{\"type\": \"test\", \"status\": \"active\"}")
                .With(b => b.Version, "1.0.0")
                .With(b => b.UpdaterId, "USER_TEST")
                .With(b => b.Description, "Test plugin description")
                .With(b => b.Comments, "Test plugin comments"));

            // Customize CreatePluginManagerHistoryCommand
            fixture.Customize<CreatePluginManagerHistoryCommand>(c => c
                .With(b => b.CompanyId, "COMP_NEW")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.PluginManagerId, "PM_NEW")
                .With(b => b.PluginManagerName, "New Test Plugin")
                .With(b => b.Properties, "{\"type\": \"new\", \"status\": \"active\"}")
                .With(b => b.Version, "1.0.0")
                .With(b => b.UpdaterId, "USER_NEW")
                .With(b => b.Description, "New test plugin description")
                .With(b => b.Comments, "New test plugin comments"));

            // Customize UpdatePluginManagerHistoryCommand
            fixture.Customize<UpdatePluginManagerHistoryCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.CompanyId, "COMP_UPD")
                .With(b => b.LoginName, "<EMAIL>")
                .With(b => b.PluginManagerId, "PM_UPD")
                .With(b => b.PluginManagerName, "Updated Test Plugin")
                .With(b => b.Properties, "{\"type\": \"updated\", \"status\": \"active\"}")
                .With(b => b.Version, "2.0.0")
                .With(b => b.UpdaterId, "USER_UPD")
                .With(b => b.Description, "Updated test plugin description")
                .With(b => b.Comments, "Updated test plugin comments"));

            // Customize DeletePluginManagerHistoryCommand
            fixture.Customize<DeletePluginManagerHistoryCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetPluginManagerHistoryDetailQuery>(c => c
                .With(b => b.PluginManagerId, "PM_TEST"));

            // Customize Responses
            fixture.Customize<CreatePluginManagerHistoryResponse>(c => c
                .With(b => b.PluginManagerHistoryId, Guid.NewGuid().ToString())
                .With(b => b.Message, "PluginManagerHistory has been created successfully"));

            fixture.Customize<UpdatePluginManagerHistoryResponse>(c => c
                .With(b => b.PluginManagerHistoryId, Guid.NewGuid().ToString())
                .With(b => b.Message, "PluginManagerHistory has been updated successfully"));

            fixture.Customize<DeletePluginManagerHistoryResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "PluginManagerHistory has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "PluginManagerHistory")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
