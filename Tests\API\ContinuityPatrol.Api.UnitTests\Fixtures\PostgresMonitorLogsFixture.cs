using AutoFixture;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Commands.Create;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetByType;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetDetail;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetList;
using ContinuityPatrol.Application.Features.PostgresMonitorLogs.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.PostgresMonitorLogsModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class PostgresMonitorLogsFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<PostgresMonitorLogs> PostgresMonitorLogs { get; set; }
    public List<PostgresMonitorLogs> InvalidPostgresMonitorLogs { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<PostgresMonitorLogsListVm> PostgresMonitorLogsListVm { get; }
    public PostgresMonitorLogsDetailVm PostgresMonitorLogsDetailVm { get; }
    public List<PostgresMonitorLogsDetailByTypeVm> PostgresMonitorLogsDetailByTypeVm { get; }
    public PaginatedResult<PostgresMonitorLogsListVm> PostgresMonitorLogsPaginatedListVm { get; }

    // Commands
    public CreatePostgresMonitorLogCommand CreatePostgresMonitorLogCommand { get; set; }

    // Queries
    public GetPostgresMonitorLogsDetailQuery GetPostgresMonitorLogsDetailQuery { get; set; }
    public GetPostgresMonitorLogsListQuery GetPostgresMonitorLogsListQuery { get; set; }
    public GetPostgresMonitorLogsDetailByTypeQuery GetPostgresMonitorLogsDetailByTypeQuery { get; set; }
    public GetPostgresMonitorLogsPaginatedListQuery GetPostgresMonitorLogsPaginatedListQuery { get; set; }

    // Responses
    public CreatePostgresMonitorLogResponse CreatePostgresMonitorLogResponse { get; set; }

    public PostgresMonitorLogsFixture()
    {
        try
        {
            // Create test data using AutoFixture
            PostgresMonitorLogs = AutoPostgresMonitorLogsFixture.Create<List<PostgresMonitorLogs>>();
            InvalidPostgresMonitorLogs = AutoPostgresMonitorLogsFixture.Create<List<PostgresMonitorLogs>>();
            UserActivities = AutoPostgresMonitorLogsFixture.Create<List<UserActivity>>();

            // Set invalid postgres monitor logs to inactive
            foreach (var invalidLog in InvalidPostgresMonitorLogs)
            {
                invalidLog.IsActive = false;
            }

            // Commands
            CreatePostgresMonitorLogCommand = AutoPostgresMonitorLogsFixture.Create<CreatePostgresMonitorLogCommand>();

            // Queries
            GetPostgresMonitorLogsDetailQuery = AutoPostgresMonitorLogsFixture.Create<GetPostgresMonitorLogsDetailQuery>();
            GetPostgresMonitorLogsListQuery = AutoPostgresMonitorLogsFixture.Create<GetPostgresMonitorLogsListQuery>();
            GetPostgresMonitorLogsDetailByTypeQuery = AutoPostgresMonitorLogsFixture.Create<GetPostgresMonitorLogsDetailByTypeQuery>();
            GetPostgresMonitorLogsPaginatedListQuery = AutoPostgresMonitorLogsFixture.Create<GetPostgresMonitorLogsPaginatedListQuery>();

            // Set query IDs to match existing entities
            if (PostgresMonitorLogs.Any())
            {
                GetPostgresMonitorLogsDetailQuery.Id = PostgresMonitorLogs.First().ReferenceId;
                GetPostgresMonitorLogsDetailByTypeQuery.Type = PostgresMonitorLogs.First().Type;
            }

            // Responses
            CreatePostgresMonitorLogResponse = AutoPostgresMonitorLogsFixture.Create<CreatePostgresMonitorLogResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            PostgresMonitorLogs = new List<PostgresMonitorLogs>();
            InvalidPostgresMonitorLogs = new List<PostgresMonitorLogs>();
            UserActivities = new List<UserActivity>();
            CreatePostgresMonitorLogCommand = new CreatePostgresMonitorLogCommand();
            GetPostgresMonitorLogsDetailQuery = new GetPostgresMonitorLogsDetailQuery();
            GetPostgresMonitorLogsListQuery = new GetPostgresMonitorLogsListQuery();
            GetPostgresMonitorLogsDetailByTypeQuery = new GetPostgresMonitorLogsDetailByTypeQuery();
            GetPostgresMonitorLogsPaginatedListQuery = new GetPostgresMonitorLogsPaginatedListQuery();
            CreatePostgresMonitorLogResponse = new CreatePostgresMonitorLogResponse();
        }

        // Configure View Models
        PostgresMonitorLogsListVm = new List<PostgresMonitorLogsListVm>
        {
            new PostgresMonitorLogsListVm
            {
                Id = "PML_001",
                Type = "POSTGRES_REPLICATION_ERROR",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "PostgreSQL Primary Server",
                WorkflowId = "WF_001",
                WorkflowName = "PostgreSQL Streaming Replication",
                ConfiguredRPO = "30",
                DataLagValue = "5",
                Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG001\", \"message\": \"Replication slot disconnected\"}",
                Threshold = "60"
            },
            new PostgresMonitorLogsListVm
            {
                Id = "PML_002",
                Type = "POSTGRES_REPLICATION_WARNING",
                InfraObjectId = "INFRA_002",
                InfraObjectName = "PostgreSQL Secondary Server",
                WorkflowId = "WF_002",
                WorkflowName = "PostgreSQL Logical Replication",
                ConfiguredRPO = "60",
                DataLagValue = "45",
                Properties = "{\"logLevel\": \"WARNING\", \"warningCode\": \"PG002\", \"message\": \"High replication lag detected\"}",
                Threshold = "120"
            },
            new PostgresMonitorLogsListVm
            {
                Id = "PML_003",
                Type = "POSTGRES_REPLICATION_INFO",
                InfraObjectId = "INFRA_003",
                InfraObjectName = "PostgreSQL Hot Standby Server",
                WorkflowId = "WF_003",
                WorkflowName = "PostgreSQL Hot Standby",
                ConfiguredRPO = "15",
                DataLagValue = "3",
                Properties = "{\"logLevel\": \"INFO\", \"infoCode\": \"PG003\", \"message\": \"Replication status healthy\"}",
                Threshold = "30"
            },
            new PostgresMonitorLogsListVm
            {
                Id = "PML_004",
                Type = "POSTGRES_BACKUP_ERROR",
                InfraObjectId = "INFRA_004",
                InfraObjectName = "PostgreSQL Backup Server",
                WorkflowId = "WF_004",
                WorkflowName = "PostgreSQL Backup Process",
                ConfiguredRPO = "240",
                DataLagValue = "300",
                Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG004\", \"message\": \"Backup process failed\"}",
                Threshold = "300"
            },
            new PostgresMonitorLogsListVm
            {
                Id = "PML_005",
                Type = "POSTGRES_RECOVERY_SUCCESS",
                InfraObjectId = "INFRA_005",
                InfraObjectName = "PostgreSQL Recovery Server",
                WorkflowId = "WF_005",
                WorkflowName = "PostgreSQL Point-in-Time Recovery",
                ConfiguredRPO = "120",
                DataLagValue = "0",
                Properties = "{\"logLevel\": \"SUCCESS\", \"successCode\": \"PG005\", \"message\": \"Recovery completed successfully\"}",
                Threshold = "180"
            }
        };

        PostgresMonitorLogsDetailVm = new PostgresMonitorLogsDetailVm
        {
            Id = "PML_001",
            Type = "POSTGRES_REPLICATION_ERROR",
            InfraObjectId = "INFRA_001",
            InfraObjectName = "PostgreSQL Primary Server",
            WorkflowId = "WF_001",
            WorkflowName = "PostgreSQL Streaming Replication",
            ConfiguredRPO = "30",
            DataLagValue = "5",
            Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG001\", \"message\": \"Replication slot disconnected\"}"
        };

        PostgresMonitorLogsDetailByTypeVm = new List<PostgresMonitorLogsDetailByTypeVm>
        {
            new PostgresMonitorLogsDetailByTypeVm
            {
                Id = "PML_001",
                Type = "POSTGRES_REPLICATION_ERROR",
                InfraObjectId = "INFRA_001",
                InfraObjectName = "PostgreSQL Primary Server",
                WorkflowId = "WF_001",
                WorkflowName = "PostgreSQL Streaming Replication",
                ConfiguredRPO = "30",
                DataLagValue = "5",
                Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG001\", \"message\": \"Replication slot disconnected\"}"
            },
            new PostgresMonitorLogsDetailByTypeVm
            {
                Id = "PML_006",
                Type = "POSTGRES_REPLICATION_ERROR",
                InfraObjectId = "INFRA_006",
                InfraObjectName = "PostgreSQL Replica Server",
                WorkflowId = "WF_006",
                WorkflowName = "PostgreSQL Streaming Replication Replica",
                ConfiguredRPO = "30",
                DataLagValue = "8",
                Properties = "{\"logLevel\": \"ERROR\", \"errorCode\": \"PG006\", \"message\": \"Connection timeout to primary\"}"
            }
        };

        PostgresMonitorLogsPaginatedListVm = new PaginatedResult<PostgresMonitorLogsListVm>
        {
            Data = PostgresMonitorLogsListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        // Configure AutoMapper for PostgresMonitorLogs mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PostgresMonitorLogsProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoPostgresMonitorLogsFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize PostgresMonitorLogs entity
            fixture.Customize<PostgresMonitorLogs>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Type, "POSTGRES_REPLICATION_INFO")
                .With(b => b.InfraObjectId, "INFRA_TEST")
                .With(b => b.InfraObjectName, "Test PostgreSQL Server")
                .With(b => b.WorkflowId, "WF_TEST")
                .With(b => b.WorkflowName, "Test PostgreSQL Workflow")
                .With(b => b.ConfiguredRPO, "30")
                .With(b => b.DataLagValue, "5")
                .With(b => b.Properties, "{\"logLevel\": \"INFO\", \"status\": \"healthy\"}")
                .With(b => b.Threshold, "60"));

            // Customize CreatePostgresMonitorLogCommand
            fixture.Customize<CreatePostgresMonitorLogCommand>(c => c
                .With(b => b.Type, "POSTGRES_REPLICATION_INFO")
                .With(b => b.InfraObjectId, "INFRA_NEW")
                .With(b => b.InfraObjectName, "New PostgreSQL Server")
                .With(b => b.WorkflowId, "WF_NEW")
                .With(b => b.WorkflowName, "New PostgreSQL Workflow")
                .With(b => b.ConfiguredRPO, "30")
                .With(b => b.DataLagValue, "5")
                .With(b => b.Properties, "{\"logLevel\": \"INFO\", \"status\": \"healthy\"}")
                .With(b => b.Threshold, "60"));

            // Customize Queries
            fixture.Customize<GetPostgresMonitorLogsDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetPostgresMonitorLogsDetailByTypeQuery>(c => c
                .With(b => b.Type, "POSTGRES_REPLICATION_INFO"));

            fixture.Customize<GetPostgresMonitorLogsPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Type")
                .With(b => b.SortOrder, "asc"));

            // Customize Responses
            fixture.Customize<CreatePostgresMonitorLogResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "PostgresMonitorLog has been created successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "PostgresMonitorLogs")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
