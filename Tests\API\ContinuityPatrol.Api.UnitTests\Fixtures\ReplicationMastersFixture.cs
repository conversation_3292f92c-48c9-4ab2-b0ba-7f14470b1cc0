using AutoFixture;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Delete;
using ContinuityPatrol.Application.Features.ReplicationMaster.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetDetail;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNames;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.ReplicationMaster.Queries.GetReplicationMasterByInfraMasterName;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.ReplicationMasterModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ReplicationMastersFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<ReplicationMaster> ReplicationMasters { get; set; }
    public List<ReplicationMaster> InvalidReplicationMasters { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<ReplicationMasterListVm> ReplicationMasterListVm { get; }
    public ReplicationMasterDetailVm ReplicationMasterDetailVm { get; }
    public PaginatedResult<ReplicationMasterListVm> ReplicationMasterPaginatedListVm { get; }
    public List<ReplicationMasterNameVm> ReplicationMasterNameListVm { get; }
    public List<GetByInfraMasterNameVm> ReplicationMasterByInfraMasterNameListVm { get; }

    // Commands
    public CreateReplicationMasterCommand CreateReplicationMasterCommand { get; set; }
    public UpdateReplicationMasterCommand UpdateReplicationMasterCommand { get; set; }
    public DeleteReplicationMasterCommand DeleteReplicationMasterCommand { get; set; }

    // Queries
    public GetReplicationMasterDetailQuery GetReplicationMasterDetailQuery { get; set; }
    public GetReplicationMasterListQuery GetReplicationMasterListQuery { get; set; }
    public GetReplicationMasterPaginatedListQuery GetReplicationMasterPaginatedListQuery { get; set; }
    public GetReplicationMasterNameQuery GetReplicationMasterNameQuery { get; set; }
    public GetReplicationMasterNameUniqueQuery GetReplicationMasterNameUniqueQuery { get; set; }
    public GetByInfraMasterNameQuery GetByInfraMasterNameQuery { get; set; }

    // Responses
    public CreateReplicationMasterResponse CreateReplicationMasterResponse { get; set; }
    public UpdateReplicationMasterResponse UpdateReplicationMasterResponse { get; set; }
    public DeleteReplicationMasterResponse DeleteReplicationMasterResponse { get; set; }

    public ReplicationMastersFixture()
    {
        try
        {
            // Create test data using AutoFixture
            ReplicationMasters = AutoReplicationMastersFixture.Create<List<ReplicationMaster>>();
            InvalidReplicationMasters = AutoReplicationMastersFixture.Create<List<ReplicationMaster>>();
            UserActivities = AutoReplicationMastersFixture.Create<List<UserActivity>>();

            // Set invalid replication masters to inactive
            foreach (var invalidReplicationMaster in InvalidReplicationMasters)
            {
                invalidReplicationMaster.IsActive = false;
            }

            // Commands
            CreateReplicationMasterCommand = AutoReplicationMastersFixture.Create<CreateReplicationMasterCommand>();
            UpdateReplicationMasterCommand = AutoReplicationMastersFixture.Create<UpdateReplicationMasterCommand>();
            DeleteReplicationMasterCommand = AutoReplicationMastersFixture.Create<DeleteReplicationMasterCommand>();

            // Set command IDs to match existing entities
            if (ReplicationMasters.Any())
            {
                UpdateReplicationMasterCommand.Id = ReplicationMasters.First().ReferenceId;
                DeleteReplicationMasterCommand.Id = ReplicationMasters.First().ReferenceId;
            }

            // Queries
            GetReplicationMasterDetailQuery = AutoReplicationMastersFixture.Create<GetReplicationMasterDetailQuery>();
            GetReplicationMasterListQuery = AutoReplicationMastersFixture.Create<GetReplicationMasterListQuery>();
            GetReplicationMasterPaginatedListQuery = AutoReplicationMastersFixture.Create<GetReplicationMasterPaginatedListQuery>();
            GetReplicationMasterNameQuery = AutoReplicationMastersFixture.Create<GetReplicationMasterNameQuery>();
            GetReplicationMasterNameUniqueQuery = AutoReplicationMastersFixture.Create<GetReplicationMasterNameUniqueQuery>();
            GetByInfraMasterNameQuery = AutoReplicationMastersFixture.Create<GetByInfraMasterNameQuery>();

            // Set query IDs to match existing entities
            if (ReplicationMasters.Any())
            {
                GetReplicationMasterDetailQuery.Id = ReplicationMasters.First().ReferenceId;
                GetReplicationMasterNameUniqueQuery.ReplicationMasterName = ReplicationMasters.First().Name;
                GetByInfraMasterNameQuery.InfraMasterName = ReplicationMasters.First().InfraMasterName;
            }

            // Responses
            CreateReplicationMasterResponse = AutoReplicationMastersFixture.Create<CreateReplicationMasterResponse>();
            UpdateReplicationMasterResponse = AutoReplicationMastersFixture.Create<UpdateReplicationMasterResponse>();
            DeleteReplicationMasterResponse = AutoReplicationMastersFixture.Create<DeleteReplicationMasterResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            ReplicationMasters = new List<ReplicationMaster>();
            InvalidReplicationMasters = new List<ReplicationMaster>();
            UserActivities = new List<UserActivity>();
            CreateReplicationMasterCommand = new CreateReplicationMasterCommand();
            UpdateReplicationMasterCommand = new UpdateReplicationMasterCommand();
            DeleteReplicationMasterCommand = new DeleteReplicationMasterCommand();
            GetReplicationMasterDetailQuery = new GetReplicationMasterDetailQuery();
            GetReplicationMasterListQuery = new GetReplicationMasterListQuery();
            GetReplicationMasterPaginatedListQuery = new GetReplicationMasterPaginatedListQuery();
            GetReplicationMasterNameQuery = new GetReplicationMasterNameQuery();
            GetReplicationMasterNameUniqueQuery = new GetReplicationMasterNameUniqueQuery();
            GetByInfraMasterNameQuery = new GetByInfraMasterNameQuery();
            CreateReplicationMasterResponse = new CreateReplicationMasterResponse();
            UpdateReplicationMasterResponse = new UpdateReplicationMasterResponse();
            DeleteReplicationMasterResponse = new DeleteReplicationMasterResponse();
        }

        // Configure View Models
        ReplicationMasterListVm = new List<ReplicationMasterListVm>
        {
            new ReplicationMasterListVm
            {
                Id = "RM_001",
                Name = "Primary Database Replication Master",
                InfraMasterId = "IM_001",
                InfraMasterName = "Primary Database Infrastructure"
            },
            new ReplicationMasterListVm
            {
                Id = "RM_002",
                Name = "File System Replication Master",
                InfraMasterId = "IM_002",
                InfraMasterName = "File Storage Infrastructure"
            },
            new ReplicationMasterListVm
            {
                Id = "RM_003",
                Name = "Application Server Replication Master",
                InfraMasterId = "IM_003",
                InfraMasterName = "Application Server Infrastructure"
            },
            new ReplicationMasterListVm
            {
                Id = "RM_004",
                Name = "Storage Array Replication Master",
                InfraMasterId = "IM_004",
                InfraMasterName = "Storage Array Infrastructure"
            },
            new ReplicationMasterListVm
            {
                Id = "RM_005",
                Name = "Virtual Machine Replication Master",
                InfraMasterId = "IM_005",
                InfraMasterName = "Virtual Infrastructure Platform"
            }
        };

        ReplicationMasterDetailVm = new ReplicationMasterDetailVm
        {
            Id = "RM_001",
            Name = "Primary Database Replication Master",
            InfraMasterId = "IM_001",
            InfraMasterName = "Primary Database Infrastructure"
        };

        ReplicationMasterPaginatedListVm = new PaginatedResult<ReplicationMasterListVm>
        {
            Data = ReplicationMasterListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        ReplicationMasterNameListVm = new List<ReplicationMasterNameVm>
        {
            new ReplicationMasterNameVm { Id = "RM_001", Name = "Primary Database Replication Master" },
            new ReplicationMasterNameVm { Id = "RM_002", Name = "File System Replication Master" },
            new ReplicationMasterNameVm { Id = "RM_003", Name = "Application Server Replication Master" },
            new ReplicationMasterNameVm { Id = "RM_004", Name = "Storage Array Replication Master" },
            new ReplicationMasterNameVm { Id = "RM_005", Name = "Virtual Machine Replication Master" }
        };

        ReplicationMasterByInfraMasterNameListVm = new List<GetByInfraMasterNameVm>
        {
            new GetByInfraMasterNameVm
            {
                Id = "RM_001",
                Name = "Primary Database Replication Master",
                InfraMasterId = "IM_001",
                InfraMasterName = "Primary Database Infrastructure"
            },
            new GetByInfraMasterNameVm
            {
                Id = "RM_002",
                Name = "Secondary Database Replication Master",
                InfraMasterId = "IM_001",
                InfraMasterName = "Primary Database Infrastructure"
            }
        };

        // Configure AutoMapper for ReplicationMaster mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ReplicationMasterProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoReplicationMastersFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize ReplicationMaster entity
            fixture.Customize<ReplicationMaster>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Replication Master")
                .With(b => b.InfraMasterId, "IM_TEST")
                .With(b => b.InfraMasterName, "Test Infrastructure Master"));

            // Customize CreateReplicationMasterCommand
            fixture.Customize<CreateReplicationMasterCommand>(c => c
                .With(b => b.Name, "New Test Replication Master")
                .With(b => b.InfraMasterId, "IM_NEW")
                .With(b => b.InfraMasterName, "New Test Infrastructure Master"));

            // Customize UpdateReplicationMasterCommand
            fixture.Customize<UpdateReplicationMasterCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Replication Master")
                .With(b => b.InfraMasterId, "IM_UPD")
                .With(b => b.InfraMasterName, "Updated Test Infrastructure Master"));

            // Customize DeleteReplicationMasterCommand
            fixture.Customize<DeleteReplicationMasterCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetReplicationMasterDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetReplicationMasterPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetReplicationMasterNameUniqueQuery>(c => c
                .With(b => b.ReplicationMasterName, "Test Replication Master")
                .With(b => b.ReplicationMasterId, Guid.NewGuid().ToString()));

            fixture.Customize<GetByInfraMasterNameQuery>(c => c
                .With(b => b.InfraMasterName, "Test Infrastructure Master"));

            // Customize Responses
            fixture.Customize<CreateReplicationMasterResponse>(c => c
                .With(b => b.ReplicationMasterId, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationMaster has been created successfully"));

            fixture.Customize<UpdateReplicationMasterResponse>(c => c
                .With(b => b.ReplicationMasterId, Guid.NewGuid().ToString())
                .With(b => b.Message, "ReplicationMaster has been updated successfully"));

            fixture.Customize<DeleteReplicationMasterResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "ReplicationMaster has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "ReplicationMaster")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
