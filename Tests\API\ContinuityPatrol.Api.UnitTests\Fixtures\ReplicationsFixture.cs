using AutoFixture;
using ContinuityPatrol.Application.Features.Replication.Commands.Create;
using ContinuityPatrol.Application.Features.Replication.Commands.Delete;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAll;
using ContinuityPatrol.Application.Features.Replication.Commands.SaveAs;
using ContinuityPatrol.Application.Features.Replication.Commands.Update;
using ContinuityPatrol.Application.Features.Replication.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Replication.Queries.GetList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNames;
using ContinuityPatrol.Application.Features.Replication.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Replication.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Replication.Queries.GetReplicationByLicensekey;
using ContinuityPatrol.Application.Features.Replication.Queries.GetType;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.ReplicationModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ReplicationsFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Replication> Replications { get; set; }
    public List<Replication> InvalidReplications { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<ReplicationListVm> ReplicationListVm { get; }
    public ReplicationDetailVm ReplicationDetailVm { get; }
    public PaginatedResult<ReplicationListVm> ReplicationPaginatedListVm { get; }
    public List<ReplicationNameVm> ReplicationNameListVm { get; }
    public List<ReplicationTypeVm> ReplicationTypeListVm { get; }
    public List<GetReplicationByLicenseKeyListVm> ReplicationByLicenseKeyListVm { get; }

    // Commands
    public CreateReplicationCommand CreateReplicationCommand { get; set; }
    public UpdateReplicationCommand UpdateReplicationCommand { get; set; }
    public DeleteReplicationCommand DeleteReplicationCommand { get; set; }
    public SaveAsReplicationCommand SaveAsReplicationCommand { get; set; }
    public SaveAllReplicationCommand SaveAllReplicationCommand { get; set; }

    // Queries
    public GetReplicationDetailQuery GetReplicationDetailQuery { get; set; }
    public GetReplicationListQuery GetReplicationListQuery { get; set; }
    public GetReplicationPaginatedListQuery GetReplicationPaginatedListQuery { get; set; }
    public GetReplicationNameQuery GetReplicationNameQuery { get; set; }
    public GetReplicationNameUniqueQuery GetReplicationNameUniqueQuery { get; set; }
    public GetReplicationTypeQuery GetReplicationTypeQuery { get; set; }
    public GetReplicationByLicenseKeyQuery GetReplicationByLicenseKeyQuery { get; set; }

    // Responses
    public CreateReplicationResponse CreateReplicationResponse { get; set; }
    public UpdateReplicationResponse UpdateReplicationResponse { get; set; }
    public DeleteReplicationResponse DeleteReplicationResponse { get; set; }
    public SaveAsReplicationResponse SaveAsReplicationResponse { get; set; }
    public SaveAllReplicationResponse SaveAllReplicationResponse { get; set; }

    public ReplicationsFixture()
    {
        try
        {
            // Create test data using AutoFixture
            Replications = AutoReplicationsFixture.Create<List<Replication>>();
            InvalidReplications = AutoReplicationsFixture.Create<List<Replication>>();
            UserActivities = AutoReplicationsFixture.Create<List<UserActivity>>();

            // Set invalid replications to inactive
            foreach (var invalidReplication in InvalidReplications)
            {
                invalidReplication.IsActive = false;
            }

            // Commands
            CreateReplicationCommand = AutoReplicationsFixture.Create<CreateReplicationCommand>();
            UpdateReplicationCommand = AutoReplicationsFixture.Create<UpdateReplicationCommand>();
            DeleteReplicationCommand = AutoReplicationsFixture.Create<DeleteReplicationCommand>();
            SaveAsReplicationCommand = AutoReplicationsFixture.Create<SaveAsReplicationCommand>();
            SaveAllReplicationCommand = AutoReplicationsFixture.Create<SaveAllReplicationCommand>();

            // Set command IDs to match existing entities
            if (Replications.Any())
            {
                UpdateReplicationCommand.Id = Replications.First().ReferenceId;
                DeleteReplicationCommand.Id = Replications.First().ReferenceId;
                SaveAsReplicationCommand.ReplicationId = Replications.First().ReferenceId;
            }

            // Queries
            GetReplicationDetailQuery = AutoReplicationsFixture.Create<GetReplicationDetailQuery>();
            GetReplicationListQuery = AutoReplicationsFixture.Create<GetReplicationListQuery>();
            GetReplicationPaginatedListQuery = AutoReplicationsFixture.Create<GetReplicationPaginatedListQuery>();
            GetReplicationNameQuery = AutoReplicationsFixture.Create<GetReplicationNameQuery>();
            GetReplicationNameUniqueQuery = AutoReplicationsFixture.Create<GetReplicationNameUniqueQuery>();
            GetReplicationTypeQuery = AutoReplicationsFixture.Create<GetReplicationTypeQuery>();
            GetReplicationByLicenseKeyQuery = AutoReplicationsFixture.Create<GetReplicationByLicenseKeyQuery>();

            // Set query IDs to match existing entities
            if (Replications.Any())
            {
                GetReplicationDetailQuery.Id = Replications.First().ReferenceId;
                GetReplicationNameUniqueQuery.ReplicationName = Replications.First().Name;
                GetReplicationTypeQuery.TypeId = Replications.First().TypeId;
                GetReplicationByLicenseKeyQuery.LicenseId = Replications.First().LicenseId;
            }

            // Responses
            CreateReplicationResponse = AutoReplicationsFixture.Create<CreateReplicationResponse>();
            UpdateReplicationResponse = AutoReplicationsFixture.Create<UpdateReplicationResponse>();
            DeleteReplicationResponse = AutoReplicationsFixture.Create<DeleteReplicationResponse>();
            SaveAsReplicationResponse = AutoReplicationsFixture.Create<SaveAsReplicationResponse>();
            SaveAllReplicationResponse = AutoReplicationsFixture.Create<SaveAllReplicationResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            Replications = new List<Replication>();
            InvalidReplications = new List<Replication>();
            UserActivities = new List<UserActivity>();
            CreateReplicationCommand = new CreateReplicationCommand();
            UpdateReplicationCommand = new UpdateReplicationCommand();
            DeleteReplicationCommand = new DeleteReplicationCommand();
            SaveAsReplicationCommand = new SaveAsReplicationCommand();
            SaveAllReplicationCommand = new SaveAllReplicationCommand();
            GetReplicationDetailQuery = new GetReplicationDetailQuery();
            GetReplicationListQuery = new GetReplicationListQuery();
            GetReplicationPaginatedListQuery = new GetReplicationPaginatedListQuery();
            GetReplicationNameQuery = new GetReplicationNameQuery();
            GetReplicationNameUniqueQuery = new GetReplicationNameUniqueQuery();
            GetReplicationTypeQuery = new GetReplicationTypeQuery();
            GetReplicationByLicenseKeyQuery = new GetReplicationByLicenseKeyQuery();
            CreateReplicationResponse = new CreateReplicationResponse();
            UpdateReplicationResponse = new UpdateReplicationResponse();
            DeleteReplicationResponse = new DeleteReplicationResponse();
            SaveAsReplicationResponse = new SaveAsReplicationResponse();
            SaveAllReplicationResponse = new SaveAllReplicationResponse();
        }

        // Configure View Models
        ReplicationListVm = new List<ReplicationListVm>
        {
            new ReplicationListVm
            {
                Id = "REP_001",
                Name = "Primary Database Replication",
                Type = "Database Replication",
                TypeId = "TYPE_DB_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_001",
                SiteName = "Primary Data Center",
                Properties = "{\"source_server\": \"db-primary-01\", \"target_server\": \"db-replica-01\", \"replication_mode\": \"synchronous\", \"compression\": true, \"encryption\": true, \"bandwidth_limit\": \"100MB\", \"retry_count\": 3, \"timeout\": 300}",
                LicenseId = "LIC_001",
                LicenseKey = "DB-REP-2024-001",
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Core Banking System",
                FormVersion = "2.1"
            },
            new ReplicationListVm
            {
                Id = "REP_002",
                Name = "File System Replication",
                Type = "File Replication",
                TypeId = "TYPE_FILE_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_002",
                SiteName = "Secondary Data Center",
                Properties = "{\"source_path\": \"/data/shared\", \"target_path\": \"/backup/shared\", \"replication_mode\": \"asynchronous\", \"compression\": false, \"encryption\": true, \"schedule\": \"hourly\", \"exclude_patterns\": [\"*.tmp\", \"*.log\"], \"verify_checksum\": true}",
                LicenseId = "LIC_002",
                LicenseKey = "FILE-REP-2024-002",
                BusinessServiceId = "BS_002",
                BusinessServiceName = "Document Management System",
                FormVersion = "2.1"
            },
            new ReplicationListVm
            {
                Id = "REP_003",
                Name = "Application Server Replication",
                Type = "Application Replication",
                TypeId = "TYPE_APP_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_001",
                SiteName = "Primary Data Center",
                Properties = "{\"source_server\": \"app-server-01\", \"target_server\": \"app-server-02\", \"replication_mode\": \"real-time\", \"load_balancing\": true, \"failover_automatic\": true, \"health_check_interval\": 30, \"session_replication\": true}",
                LicenseId = "LIC_003",
                LicenseKey = "APP-REP-2024-003",
                BusinessServiceId = "BS_003",
                BusinessServiceName = "Customer Portal",
                FormVersion = "2.1"
            },
            new ReplicationListVm
            {
                Id = "REP_004",
                Name = "Storage Array Replication",
                Type = "Storage Replication",
                TypeId = "TYPE_STORAGE_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_003",
                SiteName = "Disaster Recovery Site",
                Properties = "{\"source_array\": \"storage-array-01\", \"target_array\": \"storage-array-dr\", \"replication_mode\": \"asynchronous\", \"snapshot_frequency\": \"4h\", \"retention_policy\": \"30d\", \"bandwidth_throttling\": true, \"deduplication\": true}",
                LicenseId = "LIC_004",
                LicenseKey = "STORAGE-REP-2024-004",
                BusinessServiceId = "BS_004",
                BusinessServiceName = "Enterprise Storage",
                FormVersion = "2.1"
            },
            new ReplicationListVm
            {
                Id = "REP_005",
                Name = "Virtual Machine Replication",
                Type = "VM Replication",
                TypeId = "TYPE_VM_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_002",
                SiteName = "Secondary Data Center",
                Properties = "{\"source_host\": \"vmware-host-01\", \"target_host\": \"vmware-host-dr\", \"replication_mode\": \"asynchronous\", \"vm_list\": [\"web-server-01\", \"app-server-03\", \"db-server-02\"], \"snapshot_schedule\": \"daily\", \"compression\": true, \"quiesce_guest\": true}",
                LicenseId = "LIC_005",
                LicenseKey = "VM-REP-2024-005",
                BusinessServiceId = "BS_005",
                BusinessServiceName = "Virtual Infrastructure",
                FormVersion = "2.1"
            }
        };

        ReplicationDetailVm = new ReplicationDetailVm
        {
            Id = "REP_001",
            Name = "Primary Database Replication",
            Type = "Database Replication",
            TypeId = "TYPE_DB_001",
            CompanyId = "COMP_001",
            SiteId = "SITE_001",
            SiteName = "Primary Data Center",
            Properties = "{\"source_server\": \"db-primary-01\", \"target_server\": \"db-replica-01\", \"replication_mode\": \"synchronous\", \"compression\": true, \"encryption\": true, \"bandwidth_limit\": \"100MB\", \"retry_count\": 3, \"timeout\": 300, \"monitoring_enabled\": true, \"alert_threshold\": 95, \"backup_integration\": true, \"log_shipping\": true, \"consistency_check\": \"daily\", \"performance_tuning\": {\"buffer_size\": \"64MB\", \"thread_count\": 4, \"batch_size\": 1000}}",
            LicenseId = "LIC_001",
            LicenseKey = "DB-REP-2024-001",
            BusinessServiceId = "BS_001",
            BusinessServiceName = "Core Banking System",
            FormVersion = "2.1"
        };

        ReplicationPaginatedListVm = new PaginatedResult<ReplicationListVm>
        {
            Data = ReplicationListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10,
        };

        ReplicationNameListVm = new List<ReplicationNameVm>
        {
            new ReplicationNameVm { Id = "REP_001", Name = "Primary Database Replication" },
            new ReplicationNameVm { Id = "REP_002", Name = "File System Replication" },
            new ReplicationNameVm { Id = "REP_003", Name = "Application Server Replication" },
            new ReplicationNameVm { Id = "REP_004", Name = "Storage Array Replication" },
            new ReplicationNameVm { Id = "REP_005", Name = "Virtual Machine Replication" }
        };

        ReplicationTypeListVm = new List<ReplicationTypeVm>
        {
            new ReplicationTypeVm
            {
                Id = "REP_001",
                Name = "Primary Database Replication",
                Type = "Database Replication",
                TypeId = "TYPE_DB_001",
                SiteId = "SITE_001",
                SiteName = "Primary Data Center",
                Properties = "{\"replication_mode\": \"synchronous\", \"compression\": true}",
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Core Banking System",
                FormVersion = "2.1"
            },
            new ReplicationTypeVm
            {
                Id = "REP_002",
                Name = "File System Replication",
                Type = "File Replication",
                TypeId = "TYPE_FILE_001",
                SiteId = "SITE_002",
                SiteName = "Secondary Data Center",
                Properties = "{\"replication_mode\": \"asynchronous\", \"schedule\": \"hourly\"}",
                BusinessServiceId = "BS_002",
                BusinessServiceName = "Document Management System",
                FormVersion = "2.1"
            }
        };

        ReplicationByLicenseKeyListVm = new List<GetReplicationByLicenseKeyListVm>
        {
            new GetReplicationByLicenseKeyListVm
            {
                Id = "REP_001",
                Name = "Primary Database Replication",
                Type = "Database Replication",
                TypeId = "TYPE_DB_001",
                CompanyId = "COMP_001",
                SiteId = "SITE_001",
                SiteName = "Primary Data Center",
                Properties = "{\"replication_mode\": \"synchronous\"}",
                LicenseId = "LIC_001",
                LicenseKey = "DB-REP-2024-001",
                BusinessServiceId = "BS_001",
                BusinessServiceName = "Core Banking System",
                FormVersion = "2.1"
            }
        };

        // Configure AutoMapper for Replication mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ReplicationProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoReplicationsFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize Replication entity
            fixture.Customize<Replication>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Replication")
                .With(b => b.Type, "Database Replication")
                .With(b => b.TypeId, "TYPE_TEST")
                .With(b => b.CompanyId, "COMP_TEST")
                .With(b => b.SiteId, "SITE_TEST")
                .With(b => b.SiteName, "Test Site")
                .With(b => b.Properties, "{\"test\": \"properties\"}")
                .With(b => b.LicenseId, "LIC_TEST")
                .With(b => b.LicenseKey, "TEST-KEY-001")
                .With(b => b.BusinessServiceId, "BS_TEST")
                .With(b => b.BusinessServiceName, "Test Business Service")
                .With(b => b.FormVersion, "2.1"));

            // Customize CreateReplicationCommand
            fixture.Customize<CreateReplicationCommand>(c => c
                .With(b => b.Name, "New Test Replication")
                .With(b => b.Type, "New Database Replication")
                .With(b => b.TypeId, "TYPE_NEW")
                .With(b => b.CompanyId, "COMP_NEW")
                .With(b => b.SiteId, "SITE_NEW")
                .With(b => b.SiteName, "New Test Site")
                .With(b => b.Properties, "{\"new\": \"properties\"}")
                .With(b => b.LicenseId, "LIC_NEW")
                .With(b => b.LicenseKey, "NEW-KEY-001")
                .With(b => b.BusinessServiceId, "BS_NEW")
                .With(b => b.BusinessServiceName, "New Test Business Service")
                .With(b => b.FormVersion, "2.1"));

            // Customize UpdateReplicationCommand
            fixture.Customize<UpdateReplicationCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Replication")
                .With(b => b.Type, "Updated Database Replication")
                .With(b => b.TypeId, "TYPE_UPD")
                .With(b => b.SiteId, "SITE_UPD")
                .With(b => b.SiteName, "Updated Test Site")
                .With(b => b.Properties, "{\"updated\": \"properties\"}")
                .With(b => b.LicenseId, "LIC_UPD")
                .With(b => b.LicenseKey, "UPD-KEY-001")
                .With(b => b.BusinessServiceId, "BS_UPD")
                .With(b => b.BusinessServiceName, "Updated Test Business Service")
                .With(b => b.FormVersion, "2.1"));

            // Customize DeleteReplicationCommand
            fixture.Customize<DeleteReplicationCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize SaveAsReplicationCommand
            fixture.Customize<SaveAsReplicationCommand>(c => c
                .With(b => b.ReplicationId, Guid.NewGuid().ToString())
                .With(b => b.Name, "SaveAs Test Replication"));

            // Customize SaveAllReplicationCommand
            //fixture.Customize<SaveAllReplicationCommand>(c => c
            //    .With(b => b.ReplicationViewModels, new List<ReplicationViewModel>()));

            // Customize Queries
            fixture.Customize<GetReplicationDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetReplicationPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetReplicationNameUniqueQuery>(c => c
                .With(b => b.ReplicationName, "Test Replication")
                .With(b => b.ReplicationId, Guid.NewGuid().ToString()));

            fixture.Customize<GetReplicationTypeQuery>(c => c
                .With(b => b.TypeId, "TYPE_TEST"));

            fixture.Customize<GetReplicationByLicenseKeyQuery>(c => c
                .With(b => b.LicenseId, "LIC_TEST"));

            // Customize Responses
            fixture.Customize<CreateReplicationResponse>(c => c
                .With(b => b.ReplicationId, Guid.NewGuid().ToString())
                .With(b => b.Message, "Replication has been created successfully"));

            fixture.Customize<UpdateReplicationResponse>(c => c
                .With(b => b.ReplicationId, Guid.NewGuid().ToString())
                .With(b => b.Message, "Replication has been updated successfully"));

            fixture.Customize<DeleteReplicationResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "Replication has been deleted successfully"));

            fixture.Customize<SaveAsReplicationResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "Replication has been saved as successfully"));

            fixture.Customize<SaveAllReplicationResponse>(c => c
                .With(b => b.Message, "All replications have been saved successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "Replication")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
