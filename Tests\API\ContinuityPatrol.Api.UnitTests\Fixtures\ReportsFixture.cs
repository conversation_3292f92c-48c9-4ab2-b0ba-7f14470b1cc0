using AutoFixture;
using ContinuityPatrol.Application.Features.Report.Commands.Create;
using ContinuityPatrol.Application.Features.Report.Commands.Delete;
using ContinuityPatrol.Application.Features.Report.Commands.Update;
using ContinuityPatrol.Application.Features.Report.Queries.AirGapReport;
using ContinuityPatrol.Application.Features.Report.Queries.BulkImportReport;
using ContinuityPatrol.Application.Features.Report.Queries.BusinessServiceSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.DriftReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDetail;
using ContinuityPatrol.Application.Features.Report.Queries.GetDrDrillReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyExecutionReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetDRReadyReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetInfraObjectConfigurationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetLicenseReportByBusinessService;
using ContinuityPatrol.Application.Features.Report.Queries.GetList;
using ContinuityPatrol.Application.Features.Report.Queries.GetNames;
using ContinuityPatrol.Application.Features.Report.Queries.GetNameUnique;
using ContinuityPatrol.Application.Features.Report.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Report.Queries.GetRpoSlaDeviationReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRPOSLAReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRTOReport;
using ContinuityPatrol.Application.Features.Report.Queries.GetRunBookReport;
using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Application.Features.Report.Queries.UserActivityReport;
using ContinuityPatrol.Application.Mappings;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.ReportModel;
using ContinuityPatrol.Domain.ViewModels.RpoSlaDeviationReportModel;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Tests.SpecimenBuilders;

namespace ContinuityPatrol.Api.UnitTests.Fixtures;

public class ReportsFixture : IDisposable
{
    public IMapper Mapper { get; }

    public List<Report> Reports { get; set; }
    public List<Report> InvalidReports { get; set; }
    public List<UserActivity> UserActivities { get; set; }

    // View Models
    public List<ReportListVm> ReportListVm { get; }
    public ReportDetailVm ReportDetailVm { get; }
    public PaginatedResult<ReportListVm> ReportPaginatedListVm { get; }
    public List<GetReportNameVm> ReportNameListVm { get; }

    // Report-specific view models
    public RTOReports RTOReports { get; }
    public LicenseReport LicenseReport { get; }
    public LicenseReportByBusinessServiceReport LicenseReportByBusinessService { get; }
    public DrReadyStatusReport DrReadyStatusReport { get; }
    public DRReadyExecutionReport DRReadyExecutionReport { get; }
    public BusinessServiceSummaryReport BusinessServiceSummaryReport { get; }
    public InfraObjectSummaryReport InfraObjectSummaryReport { get; }
    public GetRpoSlaDeviationReportVm RpoSlaDeviationReport { get; }
    public InfraReport InfraObjectConfigurationReport { get; }
    public UserActivityReport UserActivityReport { get; }
    public List<DrDrillReport> DrDrillReports { get; }
    public GetRunBookReportVm RunBookReport { get; }
    public GetBulkImportReportVm BulkImportReport { get; }
    public AirGapLogReportVm AirGapReport { get; }
    public DriftReportVm DriftReport { get; }

    // Commands
    public CreateReportCommand CreateReportCommand { get; set; }
    public UpdateReportCommand UpdateReportCommand { get; set; }
    public DeleteReportCommand DeleteReportCommand { get; set; }

    // Queries
    public GetReportDetailQuery GetReportDetailQuery { get; set; }
    public GetReportListQuery GetReportListQuery { get; set; }
    public GetReportPaginatedListQuery GetReportPaginatedListQuery { get; set; }
    public GetReportNameQuery GetReportNameQuery { get; set; }
    public GetReportNameUniqueQuery GetReportNameUniqueQuery { get; set; }

    // Responses
    public CreateReportResponse CreateReportResponse { get; set; }
    public UpdateReportResponse UpdateReportResponse { get; set; }
    public DeleteReportResponse DeleteReportResponse { get; set; }

    public ReportsFixture()
    {
        try
        {
            // Create test data using AutoFixture
            Reports = AutoReportsFixture.Create<List<Report>>();
            InvalidReports = AutoReportsFixture.Create<List<Report>>();
            UserActivities = AutoReportsFixture.Create<List<UserActivity>>();

            // Set invalid reports to inactive
            foreach (var invalidReport in InvalidReports)
            {
                invalidReport.IsActive = false;
            }

            // Commands
            CreateReportCommand = AutoReportsFixture.Create<CreateReportCommand>();
            UpdateReportCommand = AutoReportsFixture.Create<UpdateReportCommand>();
            DeleteReportCommand = AutoReportsFixture.Create<DeleteReportCommand>();

            // Set command IDs to match existing entities
            if (Reports.Any())
            {
                UpdateReportCommand.Id = Reports.First().ReferenceId;
                DeleteReportCommand.Id = Reports.First().ReferenceId;
            }

            // Queries
            GetReportDetailQuery = AutoReportsFixture.Create<GetReportDetailQuery>();
            GetReportListQuery = AutoReportsFixture.Create<GetReportListQuery>();
            GetReportPaginatedListQuery = AutoReportsFixture.Create<GetReportPaginatedListQuery>();
            GetReportNameQuery = AutoReportsFixture.Create<GetReportNameQuery>();
            GetReportNameUniqueQuery = AutoReportsFixture.Create<GetReportNameUniqueQuery>();

            // Set query IDs to match existing entities
            if (Reports.Any())
            {
                GetReportDetailQuery.Id = Reports.First().ReferenceId;
                GetReportNameUniqueQuery.ReportName = Reports.First().Name;
            }

            // Responses
            CreateReportResponse = AutoReportsFixture.Create<CreateReportResponse>();
            UpdateReportResponse = AutoReportsFixture.Create<UpdateReportResponse>();
            DeleteReportResponse = AutoReportsFixture.Create<DeleteReportResponse>();
        }
        catch
        {
            // Fallback to minimal setup if AutoFixture fails
            Reports = new List<Report>();
            InvalidReports = new List<Report>();
            UserActivities = new List<UserActivity>();
            CreateReportCommand = new CreateReportCommand();
            UpdateReportCommand = new UpdateReportCommand();
            DeleteReportCommand = new DeleteReportCommand();
            GetReportDetailQuery = new GetReportDetailQuery();
            GetReportListQuery = new GetReportListQuery();
            GetReportPaginatedListQuery = new GetReportPaginatedListQuery();
            GetReportNameQuery = new GetReportNameQuery();
            GetReportNameUniqueQuery = new GetReportNameUniqueQuery();
            CreateReportResponse = new CreateReportResponse();
            UpdateReportResponse = new UpdateReportResponse();
            DeleteReportResponse = new DeleteReportResponse();
        }

        // Configure View Models
        ReportListVm = new List<ReportListVm>
        {
            new ReportListVm
            {
                Id = "RPT_001",
                Name = "Business Service Summary Report",
                Description = "Comprehensive summary of all business services and their status",
                FilterColumn = "BusinessServiceName,Status,LastModified",
                HeaderColumn = "Service Name,Current Status,Last Updated,RTO,RPO",
                Design = "Summary",
                DataSet = "BusinessServices"
            },
            new ReportListVm
            {
                Id = "RPT_002",
                Name = "Infrastructure Object Report",
                Description = "Detailed report of infrastructure objects and their configurations",
                FilterColumn = "InfraObjectName,Type,Site,Status",
                HeaderColumn = "Object Name,Type,Site Location,Status,Last Check",
                Design = "Detailed",
                DataSet = "InfraObjects"
            },
            new ReportListVm
            {
                Id = "RPT_003",
                Name = "RTO Performance Report",
                Description = "Recovery Time Objective performance analysis and trends",
                FilterColumn = "WorkflowName,ExecutionTime,Status,Date",
                HeaderColumn = "Workflow,Execution Time,Status,Date,Target RTO",
                Design = "Performance",
                DataSet = "WorkflowExecutions"
            },
            new ReportListVm
            {
                Id = "RPT_004",
                Name = "License Utilization Report",
                Description = "License usage and availability across all components",
                FilterColumn = "LicenseType,Used,Available,Expiry",
                HeaderColumn = "License Type,Used Count,Available,Expiry Date,Utilization %",
                Design = "Utilization",
                DataSet = "Licenses"
            },
            new ReportListVm
            {
                Id = "RPT_005",
                Name = "User Activity Report",
                Description = "User activity tracking and audit trail",
                FilterColumn = "UserName,Activity,Date,Entity",
                HeaderColumn = "User,Activity Type,Date/Time,Entity,Details",
                Design = "Audit",
                DataSet = "UserActivities"
            }
        };

        ReportDetailVm = new ReportDetailVm
        {
            Id = "RPT_001",
            Name = "Business Service Summary Report",
            Description = "Comprehensive summary of all business services including their current status, RTO/RPO compliance, and recent activity. This report provides executives and IT managers with a high-level overview of business service health.",
            FilterColumn = "BusinessServiceName,Status,LastModified,BusinessServiceType,CriticalityLevel",
            HeaderColumn = "Service Name,Current Status,Last Updated,RTO Target,RPO Target,Criticality,Owner",
            Design = "Executive Summary with drill-down capabilities, color-coded status indicators, and trend analysis charts",
            DataSet = "BusinessServices,InfraObjects,WorkflowExecutions,MonitoringLogs"
        };

        ReportPaginatedListVm = new PaginatedResult<ReportListVm>
        {
            Data = ReportListVm,
            CurrentPage = 1,
            TotalPages = 1,
            TotalCount = 5,
            PageSize = 10
        };

        ReportNameListVm = new List<GetReportNameVm>
        {
            new GetReportNameVm { Id = "RPT_001", Name = "Business Service Summary Report" },
            new GetReportNameVm { Id = "RPT_002", Name = "Infrastructure Object Report" },
            new GetReportNameVm { Id = "RPT_003", Name = "RTO Performance Report" },
            new GetReportNameVm { Id = "RPT_004", Name = "License Utilization Report" },
            new GetReportNameVm { Id = "RPT_005", Name = "User Activity Report" }
        };

        // Configure report-specific view models with realistic data
        RTOReports = new RTOReports
        {
            ReportGeneratedBy = "System Administrator",
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            ActiveStartDate = DateTime.Now.AddDays(-30).ToString("dd-MM-yyyy"),
            ActiveEndDate = DateTime.Now.ToString("dd-MM-yyyy"),
            RtoReportVm = new RtoReportVm
            {
                WorkflowOperationId = "WO_001",
                WorkflowName = "Database Failover Workflow",
                TotalProfilesExecutedCount = 5,
                TotalWorkflowExecutedCount = 25,
                WorkflowActionResultRtoReportVms = new List<WorkflowActionResultDrDrillReportVm>
                {
                    new WorkflowActionResultDrDrillReportVm
                    {
                        WorkflowActionName = "Stop Database Service",
                        Status = "Success",
                        StartTime = "2024-01-15T10:00:00",
                        EndTime = "2024-01-15T10:02:30"
                    }
                }
            }
        };

        LicenseReport = new LicenseReport
        {
            ReportGeneratedBy = "License Administrator",
            Date = DateTime.Now.ToString("dd-MM-yyyy hh:mm:ss tt"),
            LicenseType = "Enterprise",
            ChildLicensePOIds = "PO-2024-001,PO-2024-002",
            LicenseReportVms = new List<LicenseReportVm>
            {
                new LicenseReportVm
                {
                    TotalCount = 100,
                    AvailableCountVm = new AvailableCountViewVm { DatabaseAvailableCount = 25, ReplicationAvailableCount = 15 },
                    UsedCountVm = new UsedCountViewVm { DatabaseUsedCount = 75, ReplicationUsedCount = 35 }
                }
            }
        };

        // Configure AutoMapper for Reports mappings
        var configurationProvider = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<ReportProfile>();
        });
        Mapper = configurationProvider.CreateMapper();
    }

    public Fixture AutoReportsFixture
    {
        get
        {
            var fixture = new Fixture
            {
                RepeatCount = 6
            };

            // Customize Report entity
            fixture.Customize<Report>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.ReferenceId, Guid.NewGuid().ToString())
                .With(b => b.Name, "Test Report")
                .With(b => b.Description, "Test report description")
                .With(b => b.FilterColumn, "Column1,Column2,Column3")
                .With(b => b.HeaderColumn, "Header1,Header2,Header3")
                .With(b => b.Design, "Standard")
                .With(b => b.DataSet, "TestDataSet"));

            // Customize CreateReportCommand
            fixture.Customize<CreateReportCommand>(c => c
                .With(b => b.Name, "New Test Report")
                .With(b => b.Description, "New test report description")
                .With(b => b.FilterColumn, "NewColumn1,NewColumn2")
                .With(b => b.HeaderColumn, "NewHeader1,NewHeader2")
                .With(b => b.Design, "Modern")
                .With(b => b.DataSet, "NewDataSet"));

            // Customize UpdateReportCommand
            fixture.Customize<UpdateReportCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Name, "Updated Test Report")
                .With(b => b.Description, "Updated test report description")
                .With(b => b.FilterColumn, "UpdatedColumn1,UpdatedColumn2")
                .With(b => b.HeaderColumn, "UpdatedHeader1,UpdatedHeader2")
                .With(b => b.Design, "Enhanced")
                .With(b => b.DataSet, "UpdatedDataSet"));

            // Customize DeleteReportCommand
            fixture.Customize<DeleteReportCommand>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            // Customize Queries
            fixture.Customize<GetReportDetailQuery>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString()));

            fixture.Customize<GetReportPaginatedListQuery>(c => c
                .With(b => b.PageNumber, 1)
                .With(b => b.PageSize, 10)
                .With(b => b.SearchString, "")
                .With(b => b.SortColumn, "Name")
                .With(b => b.SortOrder, "asc"));

            fixture.Customize<GetReportNameUniqueQuery>(c => c
                .With(b => b.ReportName, "Test Report")
                .With(b => b.ReportId, Guid.NewGuid().ToString()));

            // Customize Responses
            fixture.Customize<CreateReportResponse>(c => c
                /*.With(b => b.ReportId, Guid.NewGuid().ToString()*///)
                .With(b => b.Message, "Report has been created successfully"));

            fixture.Customize<UpdateReportResponse>(c => c
                .With(b => b.Id, Guid.NewGuid().ToString())
                .With(b => b.Message, "Report has been updated successfully"));

            fixture.Customize<DeleteReportResponse>(c => c
                .With(b => b.IsActive, false)
                .With(b => b.Message, "Report has been deleted successfully"));

            // Customize UserActivity
            fixture.Customize<UserActivity>(c => c
                .With(b => b.IsActive, true)
                .With(b => b.Entity, "Report")
                .With(b => b.ActivityType, "Create"));

            return fixture;
        }
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
