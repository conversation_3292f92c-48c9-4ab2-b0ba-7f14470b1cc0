using ContinuityPatrol.Application.Features.BusinessFunction.Queries.GetBusinessFunctionByBusinessServiceId;
using ContinuityPatrol.Application.Specifications;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Persistence.Persistence;
using ContinuityPatrol.Persistence.Repositories;
using ContinuityPatrol.Persistence.UnitTests.Fixtures;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Exceptions;
using ContinuityPatrol.Shared.Core.Specifications;
using ContinuityPatrol.Shared.Tests.Infrastructure;

namespace ContinuityPatrol.Persistence.UnitTests.Repository;

public class BusinessFunctionRepositoryTests : IClassFixture<BusinessFunctionFixture>, IClassFixture<BusinessServiceFixture>, IClassFixture<CompanyFixture>
{
    private readonly BusinessFunctionFixture _businessFunctionFixture;
    private readonly BusinessServiceFixture _businessServiceFixture;
    private readonly CompanyFixture _companyFixture;
    private readonly ApplicationDbContext _dbContext;
    private readonly BusinessFunctionRepository _repository;
    private readonly BusinessFunctionRepository _repositoryNotParent;

    public BusinessFunctionRepositoryTests(BusinessFunctionFixture businessFunctionFixture, BusinessServiceFixture businessServiceFixture, CompanyFixture companyFixture)
    {
        _businessFunctionFixture = businessFunctionFixture;
        _businessServiceFixture = businessServiceFixture;
        _companyFixture = companyFixture;
        _dbContext = DbContextFactory.CreateInMemoryDbContext();
        _repository = new BusinessFunctionRepository(_dbContext, DbContextFactory.GetMockUserService());
        _repositoryNotParent = new BusinessFunctionRepository(_dbContext, DbContextFactory.GetMockLoggedInUserIsNotParent());
    }

    #region AddAsync Tests

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;

        // Act
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunction.Name, result.Name);
        Assert.Equal(businessFunction.ReferenceId, result.ReferenceId);
        Assert.Single(_dbContext.BusinessFunctions);
    }

    [Fact]
    public async Task AddAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.AddAsync(null));
    }

    #endregion

    #region UpdateAsync Tests

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        businessFunction.Name = "UpdatedName";
        businessFunction.Description = "UpdatedDescription";
        businessFunction.CriticalityLevel = "High";

        // Act
        _dbContext.BusinessFunctions.Update(businessFunction);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert
        Assert.Equal("UpdatedName", result.Name);
        Assert.Equal("UpdatedDescription", result.Description);
        Assert.Equal("High", result.CriticalityLevel);
    }

    [Fact]
    public async Task UpdateAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<System.ArgumentNullException>(() => _repository.UpdateAsync(null));
    }

    #endregion

    #region DeleteAsync Tests

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        businessFunction.IsActive = false;

        _dbContext.BusinessFunctions.Update(businessFunction);
        await _dbContext.SaveChangesAsync();

        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task DeleteAsync_ShouldThrow_WhenEntityIsNull()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _repository.DeleteAsync(null));
    }

    #endregion

    #region GetByIdAsync Tests

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();
        var addedEntity = await _repository.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal(addedEntity.Name, result.Name);
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByIdAsync(999);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByReferenceIdAsync Tests

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnEntity_WhenExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunction.ReferenceId, result.ReferenceId);
        Assert.Equal(businessFunction.Name, result.Name);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenNotExists()
    {
        // Act
        var result = await _repository.GetByReferenceIdAsync("083e0ff4-61b6-4cc5-b84b-4afa49f73d7a");

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldThrow_WhenInvalidGuid()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidArgumentException>(() => _repository.GetByReferenceIdAsync("invalid-guid"));
    }

    #endregion

    #region ListAllAsync Tests

    [Fact]
    public async Task ListAllAsync_ShouldReturnAllEntities()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctions.Count, result.Count);
    }

    [Fact]
    public async Task ListAllAsync_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    #endregion

    #region IsBusinessFunctionNameExist Tests

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnTrue_WhenNameExistsAndIdIsInvalid()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "ExistingName";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("ExistingName", "invalid-guid");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_WhenNameDoesNotExistAndIdIsInvalid()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("NonExistentName", "invalid-guid");

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameExist_ShouldReturnFalse_WhenNameExistsForSameEntity()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "SameName";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessFunctionNameExist("SameName", businessFunction.ReferenceId);

        // Assert
        Assert.False(result);
    }

    #endregion

    #region IsBusinessFunctionNameUnique Tests

    [Fact]
    public async Task IsBusinessFunctionNameUnique_ShouldReturnTrue_WhenNameExists()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.Name = "UniqueName";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.IsBusinessFunctionNameUnique("UniqueName");

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsBusinessFunctionNameUnique_ShouldReturnFalse_WhenNameDoesNotExist()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var result = await _repository.IsBusinessFunctionNameUnique("NonExistentName");

        // Assert
        Assert.False(result);
    }

    #endregion

    #region Edge Cases and Integration Tests

    [Fact]
    public async Task Repository_ShouldHandleConcurrentOperations()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionList;
        var businessFunction1 = businessFunction[0];
        var businessFunction2 = businessFunction[1];
        // Act
        await _dbContext.BusinessFunctions.AddAsync(businessFunction1);
        await _dbContext.BusinessFunctions.AddAsync(businessFunction2);
        await _dbContext.SaveChangesAsync();


        var results = await _repository.ListAllAsync();

        // Assert
        Assert.Equal(2, results.Count);
        Assert.NotNull(results[0]);
        Assert.NotNull(results[1]);
        Assert.Equal(2, _dbContext.BusinessFunctions.Count());
    }

    [Fact]
    public async Task Repository_ShouldMaintainDataIntegrity_OnComplexOperations()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList.Take(5).ToList();

        // Act - Add, then update some, then delete some
        await _repository.AddRangeAsync(businessFunctions);
        var initialCount = businessFunctions.Count;

        var toUpdate = businessFunctions.Take(2).ToList();
        toUpdate.ForEach(x => x.CriticalityLevel = "Critical");
        await _repository.UpdateRangeAsync(toUpdate);

        var toDelete = businessFunctions.Skip(2).Take(1).ToList();
        await _repository.RemoveRangeAsync(toDelete);

        // Assert
        var remaining = await _repository.ListAllAsync();
        Assert.Equal(initialCount - 1, remaining.Count);

        var updated = remaining.Where(x => x.CriticalityLevel == "Critical").ToList();
        Assert.Equal(2, updated.Count);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullParametersGracefully()
    {
        // Act & Assert
        var result1 = await _repository.IsBusinessFunctionNameExist(null, "valid-guid");
        var result2 = await _repository.IsBusinessFunctionNameExist("TestName", null);
        var result3 = await _repository.IsBusinessFunctionNameUnique(null);

        Assert.False(result1);
        Assert.False(result2);
        Assert.False(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleCriticalityLevelFiltering()
    {
        // Arrange
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                CriticalityLevel = "Critical",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                CriticalityLevel = "High",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function3",
                CriticalityLevel = "Critical",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = BusinessFunctionFixture.CompanyId,
                IsActive = true
            }
        };

        await _repository.AddRangeAsync(businessFunctions);

        // Act
        var criticalFunctions = await _repository.FindByFilterAsync(x => x.CriticalityLevel == "Critical");
        var highFunctions = await _repository.FindByFilterAsync(x => x.CriticalityLevel == "High");

        // Assert
        Assert.Equal(2, criticalFunctions.Count);
        Assert.Single(highFunctions);
        Assert.All(criticalFunctions, x => Assert.Equal("Critical", x.CriticalityLevel));
        Assert.All(highFunctions, x => Assert.Equal("High", x.CriticalityLevel));
    }

    #endregion


    [Fact]
    public async Task ListAllAsync_ShouldReturnAssignedBusinessFunctions_WhenIsAllInfraFalse()
    {
        // Arrange
        await _dbContext.BusinessFunctions.AddRangeAsync(_businessFunctionFixture.BusinessFunctionList);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnBusinessFunctionByReferenceId_WhenIsAllInfraFalse()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
  
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.CompanyId = "ChHILD_COMPANY_123";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert - Should return null or filtered result based on assigned infrastructure
    }

    [Fact]
    public async Task GetByReferenceIdAsync_ShouldReturnNull_WhenCompanyIdDoesNotMatch()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);

        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        businessFunction.CompanyId = "DIFFERENT_COMPANY";
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdAsync(businessFunction.ReferenceId);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEntitiesForParent()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList.Take(3).ToList();
        businessFunctions[0].CompanyId = "COMPANY_123";
        businessFunctions[1].CompanyId = "DIFFERENT_COMPANY";
        businessFunctions[2].CompanyId = "ANOTHER_COMPANY";
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var ids = businessFunctions.Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repository.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.Count); // Parent should see all companies
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEntitiesForChildCompanyOnly()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList.Take(3).ToList();

        businessFunctions[0].ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        businessFunctions[2].ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53127e93";
        businessFunctions[0].CompanyId = "ChHILD_COMPANY_123";
        businessFunctions[1].CompanyId = "DIFFERENT_COMPANY";
        businessFunctions[2].CompanyId = "ChHILD_COMPANY_123";
        businessFunctions[0].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        businessFunctions[2].BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var ids = businessFunctions.Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count); 
        Assert.All(result, x => Assert.Equal("ChHILD_COMPANY_123", x.CompanyId));
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnAssignedBusinessFunctions_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList.Take(2).ToList();
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var ids = businessFunctions.Select(x => x.ReferenceId).ToList();

        // Act
        var result = await _repositoryNotParent.GetByReferenceIdsAsync(ids);

        // Assert
        Assert.NotNull(result);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetByReferenceIdsAsync_ShouldReturnEmptyList_WhenNoMatchingIds()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var nonExistentIds = new List<string> { "non-existent-1", "non-existent-2" };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(nonExistentIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ShouldReturnNamesOnly()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetBusinessFunctionNames();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(businessFunctions.Count, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ShouldReturnAssignedBusinessFunctions_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetBusinessFunctionNames();

        // Assert
        Assert.NotNull(result);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetBusinessFunctionNames_ShouldReturnEmptyList_WhenNoEntities()
    {
        // Act
        var result = await _repository.GetBusinessFunctionNames();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsParent()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionPaginationList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var specification = new BusinessFunctionFilterSpecification("");

        // Act
        var result = await _repository.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(10, result.Data.Count);
        Assert.Equal(20, result.TotalCount);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnPaginatedResults_WhenIsNotParent()
    {
        // Arrange

        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessFunctions = _businessFunctionFixture.BusinessFunctionPaginationList;
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        businessFunctions.ForEach(x => x.BusinessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa");

        businessFunctions[0].ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93";
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var specification = new BusinessFunctionFilterSpecification("");

        // Act
        var result = await _repositoryNotParent.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        Assert.Equal(1, result.Data.Count);
    }

    [Fact]
    public async Task PaginatedListAllAsync_ShouldReturnFilteredResults_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionPaginationList;
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        var specification = new BusinessFunctionFilterSpecification("");

        // Act
        var result = await _repositoryNotParent.PaginatedListAllAsync(1, 10, specification, "Name", "asc");

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Succeeded);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnOrderedQuery_WhenIsAllInfra()
    {
        // Arrange
        _dbContext.BusinessFunctions.AddRange(_businessFunctionFixture.BusinessFunctionPaginationList);
        _dbContext.SaveChanges();

        // Act
        var result = _repository.GetPaginatedQuery().ToList();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.IsActive));
        Assert.True(result.SequenceEqual(result.OrderByDescending(x => x.Id)));
    }

    [Fact]
    public void GetPaginatedQuery_ShouldReturnFilteredQuery_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionPaginationList;
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        _dbContext.BusinessFunctions.AddRange(businessFunctions);
        _dbContext.SaveChanges();

        // Act
        var result = _repositoryNotParent.GetPaginatedQuery().ToList();

        // Assert
        Assert.NotNull(result);
        Assert.All(result, x => Assert.True(x.IsActive));
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ShouldReturnFunctionsForService_WhenIsParent()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function3",
                BusinessServiceId = "DIFFERENT_SERVICE",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.Equal(businessServiceId, x.BusinessServiceId));
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ShouldReturnFunctionsForChildCompany_WhenIsNotParent()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                BusinessServiceId = businessServiceId,
                ReferenceId = "1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                CompanyId = "ChHILD_COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
    }

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ShouldReturnAssignedFunctions_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessServiceId = "SERVICE_001";
        var businessFunction = new BusinessFunction
        {
            Name = "Function1",
            BusinessServiceId = businessServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "ChHILD_COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetBusinessFunctionListByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetFilterByBusinessServiceId_ShouldReturnFilteredFunctions_WhenIsParent()
    {
        // Arrange
        var businessServiceId = "SERVICE_001";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = "DIFFERENT_SERVICE",
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFilterByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal(businessServiceId, result.First().BusinessServiceId);
        Assert.NotNull(result.First().Name);
        Assert.NotNull(result.First().ReferenceId);
    }

    [Fact]
    public async Task GetFilterByBusinessServiceId_ShouldReturnFilteredFunctions_WhenIsNotParent()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {

                Name = "Function1",
                BusinessServiceId = businessServiceId,
                ReferenceId ="1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                CompanyId = "ChHILD_COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetFilterByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("1acf2f57-7a9c-4a56-a3bf-26db53117e93", result.First().ReferenceId);
    }

    [Fact]
    public async Task GetFilterByBusinessServiceId_ShouldReturnAssignedFunctions_WhenIsAllInfraFalse()
    {
        // Arrange
        var businessServiceId = "SERVICE_001";
        var businessFunction = new BusinessFunction
        {
            Name = "Function1",
            BusinessServiceId = businessServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "ChHILD_COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetFilterByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        // Should return filtered results based on assigned infrastructure
    }

    [Fact]
    public async Task GetNamesByBusinessServiceId_ShouldReturnNamesOnly_WhenIsParent()
    {
        // Arrange
        var businessServiceId = "SERVICE_001";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "Function1",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count);
        Assert.All(result, x => Assert.NotNull(x.Name));
        Assert.All(result, x => Assert.NotNull(x.ReferenceId));
    }

    [Fact]
    public async Task GetNamesByBusinessServiceId_ShouldReturnNamesForChildCompany_WhenIsNotParent()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                ReferenceId="1acf2f57-7a9c-4a56-a3bf-26db53117e93",
                Name = "Function1",
                BusinessServiceId = businessServiceId,
                CompanyId = "ChHILD_COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "Function2",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "DIFFERENT_COMPANY",
                IsActive = true
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repositoryNotParent.GetNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result);
        Assert.Equal("Function1", result.First().Name);
    }

    #region Edge Cases and Additional Coverage Tests

    [Fact]
    public async Task GetBusinessFunctionListByBusinessServiceId_ShouldReturnEmptyList_WhenServiceNotExists()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetBusinessFunctionListByBusinessServiceId("NON_EXISTENT_SERVICE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetFilterByBusinessServiceId_ShouldReturnEmptyList_WhenServiceNotExists()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetFilterByBusinessServiceId("NON_EXISTENT_SERVICE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task GetNamesByBusinessServiceId_ShouldReturnEmptyList_WhenServiceNotExists()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetNamesByBusinessServiceId("NON_EXISTENT_SERVICE");

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleNullBusinessServiceId()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId(null);
        var result2 = await _repository.GetFilterByBusinessServiceId(null);
        var result3 = await _repository.GetNamesByBusinessServiceId(null);

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyBusinessServiceId()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act & Assert
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId("");
        var result2 = await _repository.GetFilterByBusinessServiceId("");
        var result3 = await _repository.GetNamesByBusinessServiceId("");

        Assert.NotNull(result1);
        Assert.Empty(result1);
        Assert.NotNull(result2);
        Assert.Empty(result2);
        Assert.NotNull(result3);
        Assert.Empty(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleInactiveEntities()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>
        {
            new BusinessFunction
            {
                Name = "ActiveFunction",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            },
            new BusinessFunction
            {
                Name = "InactiveFunction",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = false
            }
        };

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
         _dbContext.SaveChanges();

        // Act
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId(businessServiceId);
        var result2 = await _repository.GetFilterByBusinessServiceId(businessServiceId);
        var result3 = await _repository.GetNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Single(result1);
        Assert.Equal("ActiveFunction", result1.First().Name);
        Assert.Single(result2);
        Assert.Equal("ActiveFunction", result2.First().Name);
        Assert.Single(result3);
        Assert.Equal("ActiveFunction", result3.First().Name);
    }

    [Fact]
    public async Task Repository_ShouldHandleSpecialCharactersInBusinessServiceId()
    {
        // Arrange
        var businessServiceId = "SERVICE@#$%^&*()_+{}|:<>?[]\\;',./";
        var businessFunction = new BusinessFunction
        {
            Name = "TestFunction",
            BusinessServiceId = businessServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId(businessServiceId);
        var result2 = await _repository.GetFilterByBusinessServiceId(businessServiceId);
        var result3 = await _repository.GetNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Single(result1);
        Assert.Single(result2);
        Assert.Single(result3);
    }

    [Fact]
    public async Task Repository_ShouldHandleCaseSensitiveBusinessServiceId()
    {
        // Arrange
        var businessServiceId = "CaseSensitiveService";
        var businessFunction = new BusinessFunction
        {
            Name = "TestFunction",
            BusinessServiceId = businessServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId("CaseSensitiveService");
        var result2 = await _repository.GetBusinessFunctionListByBusinessServiceId("casesensitiveservice");
        var result3 = await _repository.GetBusinessFunctionListByBusinessServiceId("CASESENSITIVESERVICE");

        // Assert
        Assert.Single(result1);   // Exact match
        Assert.Empty(result2);    // Different case
        Assert.Empty(result3);    // Different case
    }

    [Fact]
    public async Task Repository_ShouldHandleWhitespaceInBusinessServiceId()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunction = new BusinessFunction
        {
            Name = "TestFunction",
            BusinessServiceId = businessServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId("   c9b3cd51-f688-4667-be33-46f82b7086fa   ");
        var result2 = await _repository.GetBusinessFunctionListByBusinessServiceId("c9b3cd51-f688-4667-be33-46f82b7086fa");

        // Assert
        Assert.Single(result2);   // Exact match with whitespace
        Assert.Empty(result1);    // Different due to whitespace
    }

    [Fact]
    public async Task Repository_ShouldHandleLongBusinessServiceId()
    {
        // Arrange
        var longServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fasadsadsadsadadasd"; // Very long service ID
        var bservice = _businessServiceFixture.BusinessServiceDto;
        bservice.ReferenceId = longServiceId;
        await _dbContext.BusinessServices.AddAsync(bservice);

        var businessFunction = new BusinessFunction
        {
            Name = "TestFunction",
            BusinessServiceId = longServiceId,
            ReferenceId = Guid.NewGuid().ToString(),
            CompanyId = "COMPANY_123",
            IsActive = true
        };

        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        // Act
        var result = await _repository.GetBusinessFunctionListByBusinessServiceId(longServiceId);

        // Assert
        Assert.Single(result);
        Assert.Equal(longServiceId, result.First().BusinessServiceId);
    }

    [Fact]
    public async Task Repository_ShouldHandleMultipleBusinessFunctionsWithSameServiceId()
    {
        // Arrange
        var bservice = _businessServiceFixture.BusinessServiceDto;

        await _dbContext.BusinessServices.AddAsync(bservice);
        var businessServiceId = "c9b3cd51-f688-4667-be33-46f82b7086fa";
        var businessFunctions = new List<BusinessFunction>();
        for (int i = 1; i <= 10; i++)
        {
            businessFunctions.Add(new BusinessFunction
            {
                Name = $"Function{i}",
                BusinessServiceId = businessServiceId,
                ReferenceId = Guid.NewGuid().ToString(),
                CompanyId = "COMPANY_123",
                IsActive = true
            });
        }

        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act
        var result1 = await _repository.GetBusinessFunctionListByBusinessServiceId(businessServiceId);
        var result2 = await _repository.GetFilterByBusinessServiceId(businessServiceId);
        var result3 = await _repository.GetNamesByBusinessServiceId(businessServiceId);

        // Assert
        Assert.Equal(10, result1.Count);
        Assert.Equal(10, result2.Count);
        Assert.Equal(10, result3.Count);
      
    }
   
    [Fact]
    public async Task Repository_ShouldHandleEmptyAssignedInfrastructure()
    {
        // Arrange
        var businessFunctions = _businessFunctionFixture.BusinessFunctionList;
        businessFunctions.ForEach(x => x.CompanyId = "ChHILD_COMPANY_123");
        await _dbContext.BusinessFunctions.AddRangeAsync(businessFunctions);
        await _dbContext.SaveChangesAsync();

        // Act - Test with repository that has no assigned infrastructure
        var result = await _repositoryNotParent.ListAllAsync();

        // Assert
        Assert.NotNull(result);
        // Result should be empty or filtered based on assigned infrastructure
    }

    [Fact]
    public async Task Repository_ShouldHandleNullReferenceIds()
    {
        // Arrange
        var nullIds = new List<string> { null, "", "valid-id" };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(nullIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // Should handle null/empty IDs gracefully
    }

    [Fact]
    public async Task Repository_ShouldHandleEmptyReferenceIdsList()
    {
        // Arrange
        var emptyIds = new List<string>();

        // Act
        var result = await _repository.GetByReferenceIdsAsync(emptyIds);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact]
    public async Task Repository_ShouldHandleDuplicateReferenceIds()
    {
        // Arrange
        var businessFunction = _businessFunctionFixture.BusinessFunctionDto;
        await _dbContext.BusinessFunctions.AddAsync(businessFunction);
        await _dbContext.SaveChangesAsync();

        var duplicateIds = new List<string>
        {
            businessFunction.ReferenceId,
            businessFunction.ReferenceId,
            businessFunction.ReferenceId
        };

        // Act
        var result = await _repository.GetByReferenceIdsAsync(duplicateIds);

        // Assert
        Assert.NotNull(result);
        Assert.Single(result); // Should return unique entities only
    }

    #endregion

    [Fact]
    public async Task GetByBusinessServiceIds_ShouldReturnAll_WhenParentAndAllInfra()
    {
        // Arrange
        var bService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bService);   
        var bFunction=_businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(bFunction);
        await _dbContext.SaveChangesAsync();
        var businessServiceIds = new List<string> { bService.ReferenceId };
   

        // Act
        var result = await _repository.GetByBusinessServiceIds(businessServiceIds);

        // Assert
        Assert.Single(result);
        Assert.Equal(bService.ReferenceId, result.First().ReferenceId);
    }
    [Fact]
    public async Task GetByBusinessServiceIds_ShouldReturnFilteredByCompany_WhenNotParentAndAllInfra()
    {
        // Arrange

        var bService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bService);
        var bFunction = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(bFunction);
        await _dbContext.SaveChangesAsync();
        var businessServiceIds = new List<string> { bService.ReferenceId }; 

        var _mockUserService = new Mock<ILoggedInUserService>();

        _mockUserService.Setup(x => x.IsParent).Returns(false);
        _mockUserService.Setup(x => x.IsAllInfra).Returns(true);
        _mockUserService.Setup(x => x.CompanyId).Returns(bService.CompanyId);

        var reposirtory=new BusinessFunctionRepository(_dbContext, _mockUserService.Object);

        // Act
        var result = await reposirtory.GetByBusinessServiceIds(businessServiceIds);

        // Assert
        Assert.Single(result);
        Assert.Equal(bService.ReferenceId, result.First().ReferenceId);
    }
    [Fact]
    public async Task GetByBusinessServiceIds_ShouldReturnAssigned_WhenNotAllInfra()
    {
        // Arrange
        var bService = _businessServiceFixture.BusinessServiceDto;
        await _dbContext.BusinessServices.AddAsync(bService);
        var bFunction = _businessFunctionFixture.BusinessFunctionList;
        await _dbContext.BusinessFunctions.AddRangeAsync(bFunction);
        await _dbContext.SaveChangesAsync();
        var businessServiceIds = new List<string> { bService.ReferenceId };

        // Act
        var result = await _repositoryNotParent.GetByBusinessServiceIds(businessServiceIds);

        // Assert
        Assert.Single(result);
        Assert.Equal(bFunction[0].BusinessServiceId, result.First().ReferenceId);
    }


}

