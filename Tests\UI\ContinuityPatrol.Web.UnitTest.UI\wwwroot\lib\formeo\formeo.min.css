
@keyframes PLACEHOLDER {
    0% {
        height: 1px
    }

    to {
        height: 15px
    }
}

@keyframes DRAG_GHOST {
    0% {
        box-shadow: 0 0 0 0 #999
    }

    to {
        box-shadow: 0 0 30px 0 #999
    }
}

@keyframes EDIT_PULSE {
    0%,to {
        border-color: #66afe9
    }

    50% {
        border-color: #bfdef6
    }
}

@keyframes HIDE_CONDITION_FIELD {
    0% {
        display: none
    }

    to {
        display: none
    }
}

@keyframes COMPONENT_HIGHLIGHT_PULSE {
    0% {
        box-shadow: 0 0 1px space(1) #9954bb
    }

    to {
        box-shadow: 0 0 0 0 #9954bb
    }
}

.formeo .f-input-group, .formeo.formeo-editor .formeo-row > .children {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-content: stretch;
    align-items: stretch
}

.formeo .f-addon, .formeo.formeo-editor .field-edit [contenteditable], .formeo input, .formeo select, .formeo textarea {
    font-family: inherit;
    height: 30px;
    line-height: 1.5;
    margin: 0;
    border-bottom: 1px solid var(--bs-gray-300) !important;
    border-radius: 0px;
    background-color: transparent;
    width: 100%;
    padding: 0.3em 0.6em;
    box-sizing: border-box;
    border: 0px solid #ccc;
}

    .formeo .f-addon:focus, .formeo.formeo-editor .field-edit :focus[contenteditable], .formeo input:focus, .formeo select:focus, .formeo textarea:focus {
        border: 0px solid #66afe9;
        outline: none;
        background-color: transparent;
        border-bottom: 1px solid var(--bs-primary) !important;
    }

.formeo.formeo-editor .row-edit input {
    background-color: #fff !important;
}

    .formeo.formeo-editor .row-edit input:focus {
        background-color: #fff !important;
    }

.formeo-controls ul, .formeo-panels-wrap ul, .formeo.formeo-editor .children, .formeo.formeo-editor .field-edit-group, .formeo.formeo-editor .formeo-column, .formeo.formeo-editor .formeo-stage {
    margin: 0;
    padding: 0;
    list-style: none
}

    .formeo.formeo-editor .formeo-column:before, .formeo.formeo-editor .formeo-field:before, .formeo.formeo-editor .formeo-row:before {
        /*font-size: 12px;*/
        position: absolute;
        top: 0;
        width: 0;
        padding: 0;
        height: 22px;
        line-height: 24px;
        text-align: center;
        overflow: hidden;
        z-index: 100;
        transition-property: width;
        transition-duration: .15s;
        content: attr(data-hover-tag);
        background-color: #fff
    }

.field-control, .formeo-controls .field-control {
    cursor: move !important;
    list-style: none;
    margin: -1px 0 0;
    border: 1px solid var(--bs-gray-300);
    text-align: left;
    background: #fff;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    /* font-size: 9pt; */
    border-radius: 5px !important;
}

.control-moving.field-control {
    border-radius: 8px;
    animation: DRAG_GHOST .5s forwards
}

.field-control:before, .formeo-controls .field-control:before {
    margin-right: 8px;
    /*font-size: 16px*/
}

.field-control:hover {
    background-color: #f2f2f2
}

.field-control button, .formeo-controls .field-control button {
    cursor: move;
    /* font-size: 1em;*/
    line-height: 1.8em;
    display: block;
    height: 100%;
    width: 100%;
    background: transparent;
    border: 0;
    text-align: left;
    padding: 10px;
    border-radius: 0
}

    .field-control button:focus {
        outline: 0 none;
        background-color: #f2f2f2;
        /*box-shadow: inset 0 0 0 1px #66afe9;*/
        box-shadow: none;
        border-radius: 0 !important
    }

    .field-control button:active {
        transform: none
    }

    .field-control button:hover {
        filter: none
    }

.field-control .control-icon {
    float: left;
    margin-right: 8px;
    text-align: center;
    width: 24px;
    height: 24px
}

[dir=rtl] .field-control button {
    text-align: right !important
}

[dir=rtl] .field-control svg {
    float: right !important;
    margin: 0 0 0 8px !important
}

/*.formeo button {
    border-radius: 4px;
    border: 1px solid var(--bs-gray-300);
    color: #333;
    background-color: #fff;
    padding: 4px 8px
}*/

.formeo button:active {
    transform: none;
}

.formeo button:hover {
    filter: brightness(.9)
}

.svg-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    pointer-events: none
}

.f-i-remove:hover {
    fill: #d9534f
}

button[class*=-remove]:hover {
    background-color: #d9534f !important
}

    button[class*=-remove]:hover .svg-icon {
        fill: #fff
    }

button[class*=-clone]:hover {
    background-color: #93c54b !important
}

    button[class*=-clone]:hover .svg-icon {
        fill: #fff
    }

.item-edit-toggle:hover {
    background-color: #325d88 !important
}

    .item-edit-toggle:hover .svg-icon {
        fill: #fff
    }

.f-autocomplete-list {
    background-color: #fff;
    display: none;
    list-style: none;
    padding: 0;
    position: absolute;
    z-index: 20;
    max-height: 200px;
    overflow-y: auto;
    width: 100%;
    margin: 0 -1px;
    border: solid #999;
    border-width: 1px 1px 0;
    box-shadow: 0 1px 4px 0 rgba(0,0,0,.5)
}

    .f-autocomplete-list li {
        display: none;
        cursor: default;
        padding: 4px;
        margin: -1px 0 0;
        border: 1px solid #999;
        border-width: 1px 0;
        transition: background-color .1665s ease-in-out;
        will-change: background-color;
        /*font-size: .85em*/
    }

        .f-autocomplete-list li.active-option {
            background-color: #e1cceb
        }

        .f-autocomplete-list li:hover {
            background-color: #bd91d3
        }

    .f-autocomplete-list .component-type {
        color: #666;
        font-style: italic;
        /*font-size: .75em*/
    }

.formeo-panels-wrap h5 {
    margin: 0;
    padding: .55em 0;
    color: #666;
    font-weight: 400;
    display: inline-block;
    width: 100%;
    font-size: var(--bs-nav-menu-font-size);
}

.formeo-panels-wrap nav {
    position: relative;
    padding: 0;
    overflow: hidden
}

    .formeo-panels-wrap nav button {
        position: absolute;
        width: 24px;
        color: #000;
        height: 100%;
        padding: 0;
        line-height: 0;
        z-index: 1
    }

        .formeo-panels-wrap nav button:focus {
            outline: none;
            border: 1px solid #66afe9;
            box-shadow: none
        }

        .formeo-panels-wrap nav button .svg-icon {
            width: 20px;
            height: 20px
        }

        .formeo-panels-wrap nav button.next-group {
            right: 0;
            top: 0;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px
        }

        .formeo-panels-wrap nav button.prev-group {
            left: 0;
            top: 0;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
        }

.formeo-panels-wrap .f-panel {
    vertical-align: top;
    display: inline-block;
    width: 100%;
    flex-direction: column;
    flex: 1 0 100%
}

    .formeo-panels-wrap .f-panel > li:last-child {
        border-radius: 0 0 4px 4px
    }

.formeo-panels-wrap .panels {
    white-space: nowrap;
    transition-property: height;
    transition-duration: .15s;
    transition-timing-function: ease-in-out;
    will-change: transform;
    flex-direction: row
}

.formeo-panels-wrap .panel-labels {
    height: 100%;
    background: #fff;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}

.tabbed-panels .panel-nav {
    height: auto
}

    .tabbed-panels .panel-nav button {
        display: none
    }

.tabbed-panels .f-panel {
    background-color: #fff
}

.tabbed-panels .panel-labels div {
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-content: stretch;
    align-items: stretch;
    display: flex
}

.tabbed-panels .panel-labels h5 {
    flex-direction: column;
    flex: 1;
    cursor: pointer;
    /* background-color: #ccc; */
    /* box-shadow: inset 0 -1px 8px #999; */
    border: 1px solid #ccc;
    margin-left: -1px;
}

    .tabbed-panels .panel-labels h5.active-tab {
        /*        color: #000;
        box-shadow: none;
        background-color: #fff*/
        /*        margin-left: 1px;*/
        color: #0d6efd;
        box-shadow: none;
        background-color: #fff;
        border-bottom: none;
    }

.formeo-sprite {
    display: none !important
}

.formeo * {
    box-sizing: inherit
}

.formeo .pill-buttons > button {
    border-radius: 50px
}

.formeo hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid #ccc
}

.formeo .f-field-group {
    flex-wrap: wrap;
    margin-bottom: 12px
}

    .formeo .f-field-group:last-child {
        margin-bottom: 0
    }

    .formeo .f-field-group label + .badge {
        margin-left: 8px
    }

    .formeo .f-field-group > label {
        color: var(--bs-body-color);
        font-weight: var(--bs-form-font-weight);
        font-size: var(--bs-body-font-size-small);
        display: inline-block;
        margin-bottom: 0px !important;
    }

    .formeo .f-field-group button {
        margin-right: 4px
    }

.formeo input[type=checkbox], .formeo input[type=radio] {
    width: auto;
    height: auto;
    margin-right: 4px
}

.formeo input[type=date] {
    /*max-width: 280px;*/
    display: block
}

.formeo textarea {
    height: auto
}

.formeo button {
    line-height: 1.5em
}

    .formeo button.error, .formeo button.primary, .formeo button.success, .formeo button.warning {
        color: #fff
    }

    .formeo button.primary {
        background-color: #325d88;
        border-color: #244463
    }

    .formeo button.success {
        background-color: #93c54b;
        border-color: #79a736
    }

    .formeo button.warning {
        background-color: #f47c3c;
        border-color: #ef5c0e
    }

    .formeo button.error {
        background-color: #d9534f;
        border-color: #c9302c
    }

    .formeo button[disabled] {
        background-color: #ccc;
        color: #fff
    }

    .formeo button:focus {
        border: 1px solid #66afe9
    }

    .formeo button:focus, .formeo button:hover {
        outline: 0 none
    }

.formeo .f-addon {
    width: auto
}

    .formeo .f-addon label {
        margin: 1px 0 0 3px
    }

    .formeo .f-addon:last-child {
        margin-left: -1px
    }

.formeo .f-btn-group {
    display: inline-flex;
    vertical-align: middle
}

    .formeo .f-btn-group > button {
        flex: 0 1 auto
    }

        .formeo .f-btn-group > button:not(:first-child):not(:last-child):not(.dropdown-toggle) {
            border-radius: 0
        }

        .formeo .f-btn-group > button:last-child:not(:first-child):not(.dropdown-toggle) {
            border-bottom-left-radius: 0;
            border-top-left-radius: 0
        }

        .formeo .f-btn-group > button:first-child {
            margin-left: 0
        }

            .formeo .f-btn-group > button:first-child:not(:last-child):not(.dropdown-toggle) {
                border-bottom-right-radius: 0;
                border-top-right-radius: 0
            }

    .formeo .f-btn-group .f-btn-group + .f-btn-group, .formeo .f-btn-group .f-btn-group + button, .formeo .f-btn-group .f-btn-group-vertical .f-btn-group + .f-btn-group, .formeo .f-btn-group .f-btn-group-vertical .f-btn-group + button, .formeo .f-btn-group .f-btn-group-vertical button + .f-btn-group, .formeo .f-btn-group .f-btn-group-vertical button + button, .formeo .f-btn-group button + .f-btn-group, .formeo .f-btn-group button + button {
        margin-left: -1px
    }

.formeo .f-input-group {
    display: inline-flex;
    vertical-align: bottom
}

    .formeo .f-input-group input + input, .formeo .f-input-group input + select, .formeo .f-input-group select + input, .formeo .f-input-group select + select {
        margin-left: -1px
    }

    .formeo .f-input-group select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIzMiI+PHBhdGggZmlsbD0iIzQ0NCIgZD0iTTAgMTJsMTEuOTkyIDExLjk5MkwyMy45ODQgMTJIMHoiLz48L3N2Zz4=");
        background-position: right 10px top 2px;
        background-repeat: no-repeat;
        background-size: 16px
    }

        .formeo .f-input-group select::-ms-expand {
            display: none
        }

    .formeo .f-input-group .f-addon, .formeo .f-input-group input, .formeo .f-input-group select {
        flex: 0 1 auto;
        border-radius: 0
    }

        .formeo .f-input-group .f-addon:last-child, .formeo .f-input-group input:last-child, .formeo .f-input-group select:last-child {
            border-right-width: 1px;
            border-radius: 0 4px 4px 0
        }

        .formeo .f-input-group .f-addon:first-child, .formeo .f-input-group input:first-child, .formeo .f-input-group select:first-child {
            border-radius: 4px 0 0 4px;
            border-left-width: 1px
        }

            .formeo .f-input-group .f-addon:first-child:last-child, .formeo .f-input-group input:first-child:last-child, .formeo .f-input-group select:first-child:last-child {
                border-radius: 4px
            }

        .formeo .f-input-group .f-addon:focus + input, .formeo .f-input-group .f-addon:focus + select, .formeo .f-input-group input:focus + input, .formeo .f-input-group input:focus + select, .formeo .f-input-group select:focus + input, .formeo .f-input-group select:focus + select {
            border-left: 1px solid #66afe9
        }

.formeo .text-primary {
    color: #325d88
}

.formeo .text-success {
    color: #93c54b
}

.formeo .text-warning {
    color: #f47c3c
}

.formeo .text-error {
    color: white;
}

.formeo:after {
    content: "";
    display: table;
    clear: both
}

.formeo.formeo-editor {
    display: flex;
    flex-direction: row-reverse;
    text-align: left
}

    .formeo.formeo-editor .children {
        height: 100%
    }

    .formeo.formeo-editor .group-actions {
        min-width: 24px;
        width: 24px;
        height: 24px;
        overflow: hidden;
        position: absolute;
        top: 0;
        line-height: 0;
        z-index: 2
    }

        .formeo.formeo-editor .group-actions button {
            width: 24px;
            height: 24px;
            padding: 6px;
            border: 0;
            line-height: 0;
            overflow: hidden;
            background-color: #fff
        }

            .formeo.formeo-editor .group-actions button:focus {
                border: 0;
                outline: 0 none;
                box-shadow: none
            }

        .formeo.formeo-editor .group-actions .svg-icon {
            width: 12px;
            height: 12px
        }

        .formeo.formeo-editor .group-actions .f-i-handle {
            opacity: .5
        }

.last-field .formeo.formeo-editor .group-actions button:last-child {
    border-radius: 0
}

.formeo.formeo-editor .column-editing-field .column-actions, .formeo.formeo-editor .group-actions .f-i-copy, .formeo.formeo-editor .group-actions .f-i-menu, .formeo.formeo-editor .group-actions .f-i-move, .formeo.formeo-editor .group-actions .f-i-move-vertical {
    display: none
}

.formeo.formeo-editor .formeo-field.editing-field .field-actions, .formeo.formeo-editor .formeo-field.hovering-field .field-actions {
    box-shadow: -1px 1px 1px #ccc;
    border-color: #0d6efd;
    border-style: solid;
    border-width: 1px 1px 0 0
}

.formeo.formeo-editor .hovering-column .field-actions, .formeo.formeo-editor .hovering-row .field-actions {
    display: none
}

.formeo.formeo-editor .hovering-column .row-actions, .formeo.formeo-editor .hovering-row .row-actions {
    z-index: 10
}

.formeo.formeo-editor .field-actions {
    right: 0;
    text-align: right;
    transition: width .166s;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 0;
    will-change: width;
    overflow: hidden
}

    .formeo.formeo-editor .field-actions button {
        border-radius: 0;
        position: absolute
    }

        .formeo.formeo-editor .field-actions button:first-of-type {
            right: 0
        }

        .formeo.formeo-editor .field-actions button:nth-of-type(2) {
            right: 24px
        }

        .formeo.formeo-editor .field-actions button:nth-of-type(3) {
            right: 48px
        }

        .formeo.formeo-editor .field-actions button:nth-of-type(4) {
            right: 72px
        }

        .formeo.formeo-editor .field-actions button:nth-of-type(5) {
            right: 96px
        }

        .formeo.formeo-editor .field-actions button:nth-of-type(6) {
            right: 120px
        }

        .formeo.formeo-editor .field-actions button:first-child {
            right: 0
        }

.formeo.formeo-editor .group-config {
    display: none;
    padding: .5rem
}

.formeo.formeo-editor .editing-row .column-actions {
    display: none
}

.formeo.formeo-editor .column-actions {
    width: 24px;
    height: 24px;
    padding: 0;
    right: 50%;
    transform: translateX(12px);
    z-index: 1;
    transition: width .15s
}

    .formeo.formeo-editor .column-actions .action-btn-wrap {
        position: relative;
        white-space: nowrap
    }

    .formeo.formeo-editor .column-actions button {
        position: absolute;
        background-color: transparent;
        border-radius: 0
    }

        .formeo.formeo-editor .column-actions button:first-of-type {
            right: 0
        }

        .formeo.formeo-editor .column-actions button:nth-of-type(2) {
            right: 24px
        }

        .formeo.formeo-editor .column-actions button:nth-of-type(3) {
            right: 48px
        }

        .formeo.formeo-editor .column-actions button:nth-of-type(4) {
            right: 72px
        }

        .formeo.formeo-editor .column-actions button:nth-of-type(5) {
            right: 96px
        }

        .formeo.formeo-editor .column-actions button:nth-of-type(6) {
            right: 120px
        }

        .formeo.formeo-editor .column-actions button:first-child {
            border-bottom-right-radius: 0;
            right: 0
        }

.hovering-column .formeo.formeo-editor .column-actions button:first-child {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.hovering-column .formeo.formeo-editor .column-actions button:last-child {
    border-bottom-left-radius: 4px
}

.formeo.formeo-editor .editing-column .column-actions, .formeo.formeo-editor .hovering-column .column-actions {
    transform: translateX(50%);
    width: auto;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 4px
}

    .formeo.formeo-editor .editing-column .column-actions button:first-child, .formeo.formeo-editor .hovering-column .column-actions button:first-child {
        border-bottom-right-radius: 4px;
        right: 0
    }

    .formeo.formeo-editor .editing-column .column-actions button:last-child, .formeo.formeo-editor .hovering-column .column-actions button:last-child {
        border-bottom-left-radius: 4px
    }

.formeo.formeo-editor .row-actions {
    width: 24px;
    height: 24px;
    left: -23px;
    text-align: right;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    transition: height .15s ease-in-out;
    white-space: normal;
    border: 1px solid var(--bs-gray-300);
    border-right-color: #fff;
    margin: -1px;
}

    .formeo.formeo-editor .row-actions .item-handle .f-i-handle {
        transform: rotate(90deg)
    }

    .formeo.formeo-editor .row-actions button {
        border-radius: 0
    }

.formeo.formeo-editor .editing-row .row-actions, .formeo.formeo-editor .hovering-row .row-actions {
    border: 1px solid #0ff
}

    .formeo.formeo-editor .editing-row .row-actions button:first-child, .formeo.formeo-editor .hovering-row .row-actions button:first-child {
        border-bottom-left-radius: 0
    }

.formeo.formeo-editor [class*=hovering-] > .group-actions .svg-icon.f-i-copy, .formeo.formeo-editor [class*=hovering-] > .group-actions .svg-icon.f-i-menu, .formeo.formeo-editor [class*=hovering-] > .group-actions .svg-icon.f-i-move, .formeo.formeo-editor [class*=hovering-] > .group-actions .svg-icon.f-i-move-vertical {
    display: inline-block !important
}

.formeo.formeo-editor [class*=hovering-] > .group-actions .svg-icon.f-i-handle {
    display: none !important
}

.formeo.formeo-editor .formeo-row {
    transition: background-color 125ms ease-in-out;
    position: relative;
    clear: both;
    margin-left: 0;
    margin-bottom: 10px;
    background-color: #fff;
    padding: 4px;
    /* box-shadow: inset 0 0 0 1px #ccc; */
    border: 1px solid var(--bs-gray-300);
}

    .formeo.formeo-editor .formeo-row > .children {
        min-height: 32px
    }

    .formeo.formeo-editor .formeo-row:after {
        content: "";
        display: table
    }

    .formeo.formeo-editor .formeo-row:before {
        border-bottom-right-radius: 8px;
        border: 1px solid #0ff;
        border-width: 1px 0;
        left: 0
    }

    .formeo.formeo-editor .formeo-row:after {
        clear: both
    }

    .formeo.formeo-editor .formeo-row.control-ghost {
        padding: 10px
    }

    .formeo.formeo-editor .formeo-row:first-child {
        border-top-right-radius: 8px;
        border-top-left-radius: 0
    }

    .formeo.formeo-editor .formeo-row:last-child {
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px
    }

    .formeo.formeo-editor .formeo-row.hovering-row:first-child {
        border-top-left-radius: 0
    }

    .formeo.formeo-editor .formeo-row.hovering-row:before {
        width: 100px
    }

    .formeo.formeo-editor .formeo-row.hovering-row .formeo-column {
        opacity: .5
    }

    .formeo.formeo-editor .formeo-row.editing-row, .formeo.formeo-editor .formeo-row.editing-row .row-edit {
        display: block
    }

    .formeo.formeo-editor .formeo-row.resizing-columns .formeo-column {
        transition: none
    }

    .formeo.formeo-editor .formeo-row.editing-row.hovering-row .formeo-column {
        opacity: 1
    }

    .formeo.formeo-editor .formeo-row.editing-row {
        box-shadow: inset 0 0 0 1px #0ff
    }

        .formeo.formeo-editor .formeo-row.editing-row:before {
            border-width: 1px 0 0;
            width: 80px !important;
            content: attr(data-editing-hover-tag)
        }

    .formeo.formeo-editor .formeo-row.hovering-row {
        box-shadow: inset 0 0 0 1px #0ff
    }

        .formeo.formeo-editor .formeo-row.hovering-row.editing-row:before {
            border-right-width: 0
        }

        .formeo.formeo-editor .formeo-row.hovering-row:before {
            box-shadow: 1px 1px 1px #ccc;
            border-right-width: 1px;
            width: 80px !important
        }

    .formeo.formeo-editor .formeo-row.row-moving {
        box-shadow: inset 0 0 0 1px #0ff,0 0 30px 0 #999
    }

    .formeo.formeo-editor .formeo-row.empty:after {
        left: 0;
        transform: translate(8px,-50%)
    }

    .formeo.formeo-editor .formeo-row .layout-row-control {
        display: none
    }

.formeo.formeo-editor .row-edit {
    padding-top: 2rem
}

.formeo.formeo-editor .input-group-addon label {
    margin-bottom: 0
}

.formeo.formeo-editor .formeo-column {
    transition: background-color 125ms ease-in-out,box-shadow 125ms,width .25s;
    position: relative;
    background-color: #fff;
    max-width: none;
    flex-direction: column;
    will-change: width;
    max-width: 100%
}

    .formeo.formeo-editor .formeo-column[class*=col-] {
        padding: 0
    }

    .formeo.formeo-editor .formeo-column:first-of-type {
        border-top-right-radius: 8px
    }

    .formeo.formeo-editor .formeo-column:last-of-type {
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px
    }

        .formeo.formeo-editor .formeo-column:last-of-type .resize-x-handle {
            display: none !important
        }

    .formeo.formeo-editor .formeo-column .resize-x-handle {
        display: none;
        position: absolute;
        right: -8px;
        top: 0;
        bottom: 0;
        width: 16px;
        z-index: 2;
        cursor: ew-resize
    }

        .formeo.formeo-editor .formeo-column .resize-x-handle:before {
            width: 0;
            right: 6px;
            border: 1px dashed #0ff;
            border-width: 0 2px;
            display: block;
            top: 0;
            position: absolute;
            height: 100%;
            content: ""
        }

        .formeo.formeo-editor .formeo-column .resize-x-handle svg {
            fill: #0ff;
            position: absolute;
            right: 1px;
            width: 14px
        }

            .formeo.formeo-editor .formeo-column .resize-x-handle svg.f-i-triangle-down {
                top: -14px
            }

            .formeo.formeo-editor .formeo-column .resize-x-handle svg.f-i-triangle-up {
                bottom: -14px
            }

        .formeo.formeo-editor .formeo-column .resize-x-handle:hover:before {
            border-color: #00b3b3
        }

        .formeo.formeo-editor .formeo-column .resize-x-handle:hover svg {
            fill: #00b3b3
        }

    .formeo.formeo-editor .formeo-column:before {
        transition-property: height;
        transition-duration: .15s;
        padding: 0 10px;
        left: 50%;
        top: 1px;
        transform: translate(-50%,-100%);
        width: auto;
        height: 0;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px
    }

    .formeo.formeo-editor .formeo-column.hovering-column:first-child {
        border-top-left-radius: 0
    }

    .formeo.formeo-editor .formeo-column.hovering-column .formeo-field {
        opacity: .5
    }

    .formeo.formeo-editor .formeo-column.hovering-column:after {
        opacity: 0
    }

    .formeo.formeo-editor .formeo-column.editing-column, .formeo.formeo-editor .formeo-column.hovering-column {
        box-shadow: inset 0 0 0 1px #00b3b3
    }

        .formeo.formeo-editor .formeo-column.editing-column:before, .formeo.formeo-editor .formeo-column.hovering-column:before {
            height: 23px;
            border-right: 1px solid #00b3b3;
            border-left: 1px solid #00b3b3;
            border-top: 1px solid #00b3b3
        }

    .formeo.formeo-editor .formeo-column.column-moving {
        box-shadow: inset 0 0 0 1px #00b3b3,0 0 30px 0 #999
    }

    .formeo.formeo-editor .formeo-column.editing-column {
        overflow: hidden
    }

        .formeo.formeo-editor .formeo-column.editing-column .column-edit {
            display: block
        }

.formeo.formeo-editor .editing-row .empty, .formeo.formeo-editor .editing-row .formeo-column {
    border-radius: 8px;
    height: 60px;
    background-color: #e6e6e6
}

    .formeo.formeo-editor .editing-row .empty.empty, .formeo.formeo-editor .editing-row .formeo-column.empty {
        min-height: 0
    }

    .formeo.formeo-editor .editing-row .empty .formeo-field, .formeo.formeo-editor .editing-row .formeo-column .formeo-field {
        display: none
    }

    .formeo.formeo-editor .editing-row .empty .resize-x-handle, .formeo.formeo-editor .editing-row .formeo-column .resize-x-handle {
        display: block
    }

    .formeo.formeo-editor .editing-row .empty:after, .formeo.formeo-editor .editing-row .formeo-column:after {
        color: #333 !important;
        line-height: 1em;
        opacity: 1;
        /*font-size: 1.1em;*/
        content: attr(data-col-width) !important;
        display: block;
        width: 100%;
        text-align: center;
        position: absolute;
        left: 50%;
        margin-top: 0;
        top: 50%;
        transform: translate(-50%,-50%)
    }

.formeo.formeo-editor .editing-field-preview .column-actions {
    display: none
}

.formeo.formeo-editor .formeo-field {
    min-height: 24px;
    position: relative;
    padding: 4px;
    transition: background-color 333ms ease-in-out,box-shadow 333ms ease-in-out;
    list-style: none;
    margin: 0;
    will-change: box-shadow
}

    .formeo.formeo-editor .formeo-field:last-child {
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px
    }

    .formeo.formeo-editor .formeo-field.first-field, .formeo.formeo-editor .formeo-field.first-field .field-actions {
        border-top-right-radius: 4px
    }

    .formeo.formeo-editor .formeo-field.last-field {
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px
    }

    .formeo.formeo-editor .formeo-field .prev-label {
        min-height: 24px;
        /* max-width: calc(100% - 24px);*/
        display: flex;
        align-items: flex-end;
        margin-bottom: 4px
    }

    .formeo.formeo-editor .formeo-field [contenteditable] {
        padding: 1px 2px;
        -webkit-user-select: text;
        -ms-user-select: text;
        user-select: text;
        display: inline-block;
        position: relative;
        min-width: 60px;
        max-width: calc(100% - 24px);
        cursor: text;
        /*    padding-left: 25px; */
        margin-bottom: 0px !important;
    }

    /*.formeo.formeo-editor .formeo-field h1[contenteditable] {
    padding: 1px 2px 1px 44px !important;
    }*/

    /*Jira ID CP6-7973 */
    .formeo.formeo-editor .formeo-field .prev-label:before {
        content: "\e92d";
        font-family: 'cp-icon';
        position: static;
        font-size: 16px !important;
        left: 5px;
        top: 50%;
        pointer-events: none;
        margin-right: 5px;
    }

    /*.formeo.formeo-editor .formeo-field [contenteditable]:before {
        content: "\e92d";
        font-family: 'cp-icon';
        position: static;
        left: 5px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        font-size: 16px !important;
        margin-right: 5px;
    }*/

    .formeo.formeo-editor .formeo-field [contenteditable]:after {
        content: "";
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        border-bottom: 1px dashed #ccc
    }

    .formeo.formeo-editor .formeo-field [contenteditable]:focus {
        border-radius: 2px;
        border-bottom-color: transparent;
        outline: none;
        box-shadow: none;
        background-color: var(--bs-gray-100);
    }

        .formeo.formeo-editor .formeo-field [contenteditable]:focus:after {
            display: none
        }

    .formeo.formeo-editor .formeo-field .form-check {
        margin-left: 1.25em
    }

    .formeo.formeo-editor .formeo-field .form-check-input:only-child {
        position: absolute
    }

    .formeo.formeo-editor .formeo-field:before {
        display: none;
        position: absolute;
        top: 0;
        padding: 0 10px;
        right: 0;
        transform: translateX(-72px);
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 8px
    }

    .formeo.formeo-editor .formeo-field.field-type-hidden {
        border: 1px dashed #ccc
    }

.formeo.formeo-editor .editing-field, .formeo.formeo-editor .hovering-field {
    box-shadow: inset 0 0 0 1px #0d6efd
}

    .formeo.formeo-editor .editing-field:before, .formeo.formeo-editor .hovering-field:before {
        border-left: 1px solid #0d6efd;
        border-bottom: 1px solid #0d6efd
    }

.formeo.formeo-editor .field-actions {
    border-color: transparent;
    border-style: solid;
    border-width: 1px 1px 0 0
}

.formeo.formeo-editor .field-moving {
    box-shadow: inset 0 0 0 1px #0d6efd,0 0 30px 0 #999;
    background-color: #fff
}

.formeo.formeo-editor .editing-field {
    background-color: #e6e6e6;
    z-index: 1
}

.formeo.formeo-editor .editing-field-preview .field-actions {
    display: none
}

.formeo.formeo-editor .field-preview p {
    white-space: normal
}

.formeo.formeo-editor .field-edit {
    display: none;
    overflow-x: hidden;
    overflow-y: visible;
}

    .formeo.formeo-editor .field-edit label {
        /*font-size: .825em*/
    }

    .formeo.formeo-editor .field-edit .panel-nav {
        margin-bottom: 0;
        padding: 0;
        overflow: hidden
    }

        .formeo.formeo-editor .field-edit .panel-nav button {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0
        }

    .formeo.formeo-editor .field-edit.field-edit-options {
        list-style: decimal
    }

    .formeo.formeo-editor .field-edit .active-panel {
        background-color: #fff
    }

    .formeo.formeo-editor .field-edit .field-prop {
        display: flex
    }

    .formeo.formeo-editor .field-edit .prop-controls {
        flex-shrink: 0;
        align-items: center;
        display: flex;
        margin-left: 3px
    }

        .formeo.formeo-editor .field-edit .prop-controls button {
            position: relative
        }

        .formeo.formeo-editor .field-edit .prop-controls .svg-icon {
            width: 12px;
            height: 12px;
            left: 50%;
            position: absolute;
            top: 50%;
            transform: translate(-50%,-50%)
        }

    .formeo.formeo-editor .field-edit .prop-control {
        width: 24px;
        height: 24px;
        right: 0;
        bottom: 0;
        position: absolute;
        padding: 0
    }

        .formeo.formeo-editor .field-edit .prop-control:first-of-type {
            right: 0
        }

        .formeo.formeo-editor .field-edit .prop-control:nth-of-type(2) {
            right: 24px
        }

        .formeo.formeo-editor .field-edit .prop-control:nth-of-type(3) {
            right: 48px
        }

        .formeo.formeo-editor .field-edit .prop-control:nth-of-type(4) {
            right: 72px
        }

        .formeo.formeo-editor .field-edit .prop-control:first-child {
            right: 0
        }

:not(.control-count-1) .formeo.formeo-editor .field-edit .prop-control:last-child {
    border-radius: 4px 0 0 4px;
    margin-right: -1px;
    display: none
}

:not(.control-count-1) .formeo.formeo-editor .field-edit .prop-control:first-child {
    border-radius: 4px
}

.formeo.formeo-editor .field-edit .prop-control:hover:first-child {
    border-radius: 0 4px 4px 0
}

    .formeo.formeo-editor .field-edit .prop-control:hover:first-child:last-child {
        border-radius: 4px
    }

.formeo.formeo-editor .field-edit .prop-control:hover:last-child {
    display: inline-block
}

.formeo.formeo-editor .field-edit .prop-control:last-child:first-child {
    display: inline-block;
    border-radius: 4px;
    margin-right: 0
}

.formeo.formeo-editor .field-edit .prop-wrap {
    position: relative;
    margin-bottom: 8px;
    list-style: none;
    margin-left: 0
}

.formeo.formeo-editor .field-edit .field-edit-group {
    padding: 8px 8px 0
}

.formeo.formeo-editor .field-edit .prop-controls, .formeo.formeo-editor .field-edit .prop-inputs {
    transition: width .15s;
    will-change: width
}

    .formeo.formeo-editor .field-edit .prop-controls .f-addon, .formeo.formeo-editor .field-edit .prop-controls [contenteditable], .formeo.formeo-editor .field-edit .prop-controls input:not([type=checkbox]):not([type=radio]), .formeo.formeo-editor .field-edit .prop-controls select, .formeo.formeo-editor .field-edit .prop-controls textarea, .formeo.formeo-editor .field-edit .prop-inputs .f-addon, .formeo.formeo-editor .field-edit .prop-inputs [contenteditable], .formeo.formeo-editor .field-edit .prop-inputs input:not([type=checkbox]):not([type=radio]), .formeo.formeo-editor .field-edit .prop-inputs select, .formeo.formeo-editor .field-edit .prop-inputs textarea {
        /*font-size: .825em;*/
        flex: 1 1 auto
    }

        .formeo.formeo-editor .field-edit .prop-controls .f-addon:focus, .formeo.formeo-editor .field-edit .prop-controls [contenteditable]:focus, .formeo.formeo-editor .field-edit .prop-controls input:not([type=checkbox]):not([type=radio]):focus, .formeo.formeo-editor .field-edit .prop-controls select:focus, .formeo.formeo-editor .field-edit .prop-controls textarea:focus, .formeo.formeo-editor .field-edit .prop-inputs .f-addon:focus, .formeo.formeo-editor .field-edit .prop-inputs [contenteditable]:focus, .formeo.formeo-editor .field-edit .prop-inputs input:not([type=checkbox]):not([type=radio]):focus, .formeo.formeo-editor .field-edit .prop-inputs select:focus, .formeo.formeo-editor .field-edit .prop-inputs textarea:focus {
            z-index: 1
        }

        .formeo.formeo-editor .field-edit .prop-controls [class^=condition-] + select, .formeo.formeo-editor .field-edit .prop-controls select + [class^=condition-], .formeo.formeo-editor .field-edit .prop-inputs [class^=condition-] + select, .formeo.formeo-editor .field-edit .prop-inputs select + [class^=condition-] {
            margin-left: -1px
        }

.formeo.formeo-editor .field-edit .control-count-2 .prop-controls:hover {
    width: 48px
}

    .formeo.formeo-editor .field-edit .control-count-2 .prop-controls:hover + .prop-inputs {
        width: calc(100% - 24px)
    }

.formeo.formeo-editor .field-edit.panel-count-1 .panel-nav {
    border-bottom: 1px solid #999
}

    .formeo.formeo-editor .field-edit.panel-count-1 .panel-nav button {
        display: none
    }

.formeo.formeo-editor .field-edit.panel-count-1 .panel-labels {
    background-color: transparent
}

.formeo.formeo-editor .f-condition-row {
    display: flex;
    width: 100%;
    margin-top: -1px;
    border: 1px solid #ccc
}

    .formeo.formeo-editor .f-condition-row [contenteditable], .formeo.formeo-editor .f-condition-row input, .formeo.formeo-editor .f-condition-row select {
        border-width: 0;
        border-radius: 0;
        width: inherit;
        box-shadow: 0 0 0 1px #ccc
    }

        .formeo.formeo-editor .f-condition-row [contenteditable]:focus, .formeo.formeo-editor .f-condition-row input:focus, .formeo.formeo-editor .f-condition-row select:focus {
            box-shadow: 0 0 0 1px #66afe9
        }

    .formeo.formeo-editor .f-condition-row:first-child input:last-child, .formeo.formeo-editor .f-condition-row:first-child select:last-child {
        border-top-right-radius: 4px
    }

    .formeo.formeo-editor .f-condition-row:last-child input:last-child, .formeo.formeo-editor .f-condition-row:last-child select:last-child {
        border-bottom-right-radius: 4px
    }

    .formeo.formeo-editor .f-condition-row:first-child {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px
    }

    .formeo.formeo-editor .f-condition-row:last-child {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px
    }

    .formeo.formeo-editor .f-condition-row .condition-logical {
        max-width: 56px;
        text-transform: uppercase
    }

    .formeo.formeo-editor .f-condition-row div.condition-source, .formeo.formeo-editor .f-condition-row div.condition-target {
        position: relative;
        width: 100%
    }

    .formeo.formeo-editor .f-condition-row [class^=condition-] {
        max-width: 100%;
        transition: max-width 333ms;
        will-change: max-width
    }

    .formeo.formeo-editor .f-condition-row.condition-source .condition-sourceProperty, .formeo.formeo-editor .f-condition-row.condition-target .condition-targetProperty, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isNotVisible .condition-assignment, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isNotVisible .condition-comparison, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isNotVisible .condition-target, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isNotVisible .condition-targetProperty, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isNotVisible .condition-value, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isVisible .condition-assignment, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isVisible .condition-comparison, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isVisible .condition-target, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isVisible .condition-targetProperty, .formeo.formeo-editor .f-condition-row.if-condition-row.condition-sourceProperty-isVisible .condition-value, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isNotVisible .condition-assignment, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isNotVisible .condition-comparison, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isNotVisible .condition-value, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isVisible .condition-assignment, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isVisible .condition-comparison, .formeo.formeo-editor .f-condition-row.then-condition-row.condition-targetProperty-isVisible .condition-value {
        max-width: 0;
        padding: 0;
        border: 0;
        overflow: hidden
    }

.formeo.formeo-editor .f-autocomplete-display-field + .f-autocomplete-list:focus {
    z-index: 100
}

.condition-target input[placeholder="target / value"] + .f-autocomplete-list {
    display: none !important;
}

.formeo.formeo-editor [class$=-focused] {
    z-index: 1
}

    .formeo.formeo-editor [class$=-focused] .f-autocomplete-list {
        z-index: 100
    }

.formeo.formeo-editor .conditions-prop-inputs label.condition-label {
    display: inline-flex;
    flex: 0 0 auto;
    box-shadow: 0 0 0 1px #ccc;
    align-items: center;
    padding: 4px;
    border-right: 0;
    text-transform: uppercase;
    color: #999
}

    .formeo.formeo-editor .conditions-prop-inputs label.condition-label.if-condition-label {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0
    }

    .formeo.formeo-editor .conditions-prop-inputs label.condition-label.then-condition-label {
        border-bottom-left-radius: 4px;
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        border-top: 0
    }

.formeo.formeo-editor .hidden-property {
    display: none
}

.formeo.formeo-editor .options-panel .prop-wrap {
    margin-bottom: 8px
}

.formeo.formeo-editor .options-panel .input-group-addon {
    line-height: 0
}

.formeo.formeo-editor .options-panel .prop-labels {
    padding: 8px 34px 8px 8px
}

    .formeo.formeo-editor .options-panel .prop-labels .input-group-addon {
        /*font-size: 12px*/
    }

    .formeo.formeo-editor .options-panel .prop-labels label {
        /* font-size: 12px;*/
        width: 50%;
        position: relative;
        display: table-cell
    }

.formeo.formeo-editor .options-panel .prop-label-disabled, .formeo.formeo-editor .options-panel .prop-label-selected {
    width: 1% !important;
    white-space: nowrap;
    vertical-align: middle;
    border: 0;
    background-color: transparent
}

.formeo.formeo-editor .panel-action-buttons {
    padding: 0 8px 8px
}

    .formeo.formeo-editor .panel-action-buttons:after {
        content: "";
        display: table;
        clear: both
    }

    .formeo.formeo-editor .panel-action-buttons [class^=add-] {
        float: right
    }

.formeo.formeo-editor .prop-inputs {
    width: 100%;
    align-items: center;
    vertical-align: bottom;
    display: flex;
    flex-direction: row;
    flex-grow: 1
}

    .formeo.formeo-editor .prop-inputs.conditions-prop-inputs {
        flex-direction: column
    }

    .formeo.formeo-editor .prop-inputs .f-field-group {
        width: 100%;
        margin-bottom: 0;
        display: inline-flex;
        align-items: center
    }

    .formeo.formeo-editor .prop-inputs .f-addon {
        display: flex;
        align-items: center;
        flex: 0
    }

        .formeo.formeo-editor .prop-inputs .f-addon:first-child {
            margin-right: -1px
        }

        .formeo.formeo-editor .prop-inputs .f-addon:last-child {
            margin-left: -1px
        }

    .formeo.formeo-editor .prop-inputs label {
        padding-right: 1em;
        margin-bottom: 0
    }

    .formeo.formeo-editor .prop-inputs.attrs-prop-inputs .f-addon, .formeo.formeo-editor .prop-inputs.attrs-prop-inputs [contenteditable], .formeo.formeo-editor .prop-inputs.attrs-prop-inputs input:not([type=checkbox]):not([type=radio]), .formeo.formeo-editor .prop-inputs.attrs-prop-inputs select, .formeo.formeo-editor .prop-inputs.attrs-prop-inputs textarea {
        flex: 2
    }

.formeo.formeo-editor .highlight-component {
    box-shadow: 0 0 4px 2px #9954bb
}

.formeo.formeo-editor .formeo-stage {
    width: 73%;
    box-sizing: border-box;
    transition: width .25s;
    margin-right: 4px;
    flex: 1 1 auto;
    position: relative;
    transition-property: background-color,border-color;
    transition-duration: .5s,333ms;
    border: 0 dashed transparent;
    background-color: hsla(0,0%,100%,0);
    padding-left: 23px;
    margin-left: 7px;
    padding-bottom: 8px;
    overflow: visible
}

@media (max-width:481px) {
    .formeo.formeo-editor .formeo-stage {
        width: calc(100% - 50px)
    }
}

.formeo.formeo-editor .formeo-stage.empty {
    height: 500px;
    max-height: 490px;
    border: 2px dashed var(--bs-gray-200);
    background-color: hsla(0,0%,100%,.25)
}

.formeo.formeo-editor .formeo-stage.removing-all-fields .formeo-row {
    transition: margin-top .25s ease-in
}

.formeo.formeo-editor .formeo-stage > .formeo-field {
    background-color: #fff
}

.formeo.formeo-editor.editing-stage .formeo-settings {
    display: block
}

.formeo.formeo-editor.editing-stage .formeo-stage {
    display: none
}

.formeo.formeo-editor .f-field-group {
    margin-bottom: 0
}

.formeo.formeo-editor [class$=empty][class*=editing-]:after {
    opacity: 0
}

.formeo.formeo-editor [class$=empty]:after {
    opacity: 1;
    /*font-size: 24px;*/
    position: absolute;
    top: 75%;
    left: 50%;
    color: #999;
    transition: opacity .2s ease-in-out;
    will-change: opacity;
    text-align: center;
    transform: translate(-50%,-50%);
    content: attr(data-hover-tag)
}

.formeo.formeo-editor .formeo-settings {
    display: none
}

.formeo.formeo-editor[dir=rtl] .f-btn-group {
    display: inline-flex;
    vertical-align: middle
}

    .formeo.formeo-editor[dir=rtl] .f-btn-group > button {
        flex: 0 1 auto
    }

        .formeo.formeo-editor[dir=rtl] .f-btn-group > button:not(:first-child):not(:last-child):not(.dropdown-toggle) {
            border-radius: 0
        }

        .formeo.formeo-editor[dir=rtl] .f-btn-group > button:last-child:not(:first-child):not(.dropdown-toggle) {
            border-bottom-right-radius: 0;
            border-top-right-radius: 0;
            border-bottom-left-radius: 4px;
            border-top-left-radius: 4px
        }

        .formeo.formeo-editor[dir=rtl] .f-btn-group > button:first-child {
            margin-left: 0
        }

            .formeo.formeo-editor[dir=rtl] .f-btn-group > button:first-child:not(:last-child):not(.dropdown-toggle) {
                border-bottom-left-radius: 0;
                border-top-left-radius: 0;
                border-bottom-right-radius: 4px;
                border-top-right-radius: 4px
            }

    .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group + .f-btn-group, .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group + button, .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group-vertical .f-btn-group + .f-btn-group, .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group-vertical .f-btn-group + button, .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group-vertical button + .f-btn-group, .formeo.formeo-editor[dir=rtl] .f-btn-group .f-btn-group-vertical button + button, .formeo.formeo-editor[dir=rtl] .f-btn-group button + .f-btn-group, .formeo.formeo-editor[dir=rtl] .f-btn-group button + button {
        margin-right: -1px
    }

.formeo.formeo-editor[dir=rtl] .formeo-controls, .formeo.formeo-editor[dir=rtl] .formeo-controls .form-actions {
    float: left
}

.formeo.formeo-editor[dir=rtl] .formeo-stage-wrap {
    float: right
}

    .formeo.formeo-editor[dir=rtl] .formeo-stage-wrap .formeo-stage {
        padding-left: 4px;
        padding-right: 23px
    }

.formeo.formeo-editor[dir=rtl] .formeo-row:before {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 0;
    right: 0;
    left: auto
}

.formeo.formeo-editor[dir=rtl] .formeo-row:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 0
}

.formeo.formeo-editor[dir=rtl] .formeo-row:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px
}

.formeo.formeo-editor[dir=rtl] .formeo-row.hovering-row:first-child {
    border-top-left-radius: 0
}

.formeo.formeo-editor[dir=rtl] .formeo-row.hovering-row.editing-row:before {
    border-left-width: 0;
    border-right-width: auto;
}

.formeo.formeo-editor[dir=rtl] .formeo-row.hovering-row:before {
    border-left-width: 1px
}

.formeo.formeo-editor[dir=rtl] .formeo-row.empty:after {
    left: 0;
    right: auto
}

.formeo.formeo-editor[dir=rtl] .row-actions {
    right: -23px;
    left: auto;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 1px solid #fff
}

.formeo.formeo-editor[dir=rtl] .field-actions {
    text-align: left;
    left: 0;
    right: auto;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0
}

    .formeo.formeo-editor[dir=rtl] .field-actions button:first-of-type {
        left: 0;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:nth-of-type(2) {
        left: 24px;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:nth-of-type(3) {
        left: 48px;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:nth-of-type(4) {
        left: 72px;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:nth-of-type(5) {
        left: 96px;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:nth-of-type(6) {
        left: 120px;
        right: auto
    }

    .formeo.formeo-editor[dir=rtl] .field-actions button:first-child {
        left: 0
    }

.formeo.formeo-editor[dir=rtl] .formeo-field.editing-field .field-actions, .formeo.formeo-editor[dir=rtl] .formeo-field.hovering-field .field-actions {
    box-shadow: 1px 1px 1px #ccc;
    border-width: 1px 0 0 1px
}

.formeo.formeo-render .f-input-group-wrap > fieldset, .formeo.formeo-render .formeo-column, .formeo.formeo-render .formeo-field, .formeo.formeo-render .formeo-row {
    position: relative
}

    .formeo.formeo-render .f-input-group-wrap > fieldset .remove-input-group {
        top: 8px
    }

.formeo.formeo-render .will-remove {
    background-color: rgba(217,83,79,.25);
    box-shadow: inset 0 0 1px 0 #d9534f
}

.formeo.formeo-render .formeo-row {
    margin-bottom: 1em;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-content: stretch;
    align-items: stretch;
    display: flex;
    border-radius: 8px;
    transition: background-color .2s,padding .2s;
    padding: 4px 0
}

    .formeo.formeo-render .formeo-row.will-remove {
        padding: 4px
    }

    .formeo.formeo-render .formeo-row:last-of-type {
        margin-bottom: 0
    }

.formeo.formeo-render .formeo-row-wrap {
    margin-bottom: 1em
}

    .formeo.formeo-render .formeo-row-wrap:last-child {
        margin-bottom: 0
    }

.formeo.formeo-render .f-input-group-wrap:after {
    content: "";
    display: table;
    clear: both
}

.formeo.formeo-render .f-input-group-wrap .formeo-row:first-of-type .remove-input-group {
    display: none
}

.formeo.formeo-render .f-input-group {
    position: relative
}

    .formeo.formeo-render .f-input-group:first-child .remove-input-group {
        display: none
    }

.formeo.formeo-render .add-input-group {
    float: right;
    margin-top: 10px
}

.formeo.formeo-render .remove-input-group {
    position: absolute;
    right: 0;
    top: 0;
    width: 16px;
    height: 16px;
    border: 0;
    background: transparent;
    outline: 0 none;
    line-height: 0;
    padding: 4px
}

    .formeo.formeo-render .remove-input-group:hover .svg-icon {
        fill: #d9534f
    }

    .formeo.formeo-render .remove-input-group .svg-icon {
        pointer-events: none
    }

.formeo.formeo-render .form-check-input:only-child {
    position: absolute
}

.formeo.formeo-render .svg-icon {
    max-width: 100%;
    max-height: 100%
}

.formeo.formeo-render .formeo-column {
    padding: 0 4px;
    float: left;
    max-width: none;
    flex-direction: column
}

    .formeo.formeo-render .formeo-column:first-of-type {
        padding-left: 0
    }

    .formeo.formeo-render .formeo-column:last-of-type {
        padding-right: 0
    }

.formeo-controls {
    width: 26%;
    overflow: hidden
}

    .formeo-controls.formeo-sticky {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        align-self: flex-start
    }

    .formeo-controls.pull-left .form-actions {
        float: left
    }

    .formeo-controls .filtered-term {
        background-color: #fff;
        text-align: center;
        border-radius: 4px 4px 0 0;
        border: 1px solid #ccc;
        border-bottom: 0;
        width: calc(100% - 2px)
    }

    .formeo-controls .tabbed-panels nav {
        padding: 0
    }

    .formeo-controls nav {
        position: relative;
        padding: 0 24px;
        overflow: hidden
    }

        .formeo-controls nav h5 {
            font-size: 13px;
            /* font-weight: bolder;
            line-height: 18px;*/
        }

        .formeo-controls nav button {
            position: absolute;
            width: 24px;
            color: #000;
            /*height: calc(100% + 1px);*/
            padding: 0;
            line-height: 0;
            margin: 0;
            border: 1px solid var(--bs-gray-300);
            background-color: var(--bs-white);
        }

            .formeo-controls nav button.next-group {
                right: 0;
                top: 0;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
            }

            .formeo-controls nav button.prev-group {
                border-top-left-radius: 5px;
                border-bottom-left-radius: 5px;
                left: 0
            }

    .formeo-controls .formeo-panels-wrap {
        /*font-size: .85em;*/
        line-height: 1.8em
    }

    .formeo-controls .panel-labels {
        border: 1px solid var(--bs-gray-300);
        border-radius: 5px;
        margin: 0 5px;
    }

    .formeo-controls .panel-count-1 {
        border-bottom-left-radius: 0
    }

        .formeo-controls .panel-count-1 .panel-nav {
            display: none
        }

        .formeo-controls .panel-count-1 .control-group li:first-child {
            border-radius: 8px 8px 0 0
        }

    .formeo-controls .control-group {
        vertical-align: top;
        display: inline-block;
        width: 100%;
        border-top: 0px solid #ccc
    }

        .formeo-controls .control-group > li:first-child {
            border-top-right-radius: 0
        }

        .formeo-controls .control-group > li:last-child {
            border-radius: 0 0 8px 8px
        }

    .formeo-controls.filtered .panel-nav {
        display: none
    }

    .formeo-controls.filtered .control-group {
        display: block
    }

        .formeo-controls.filtered .control-group > li {
            border-radius: 0
        }

        .formeo-controls.filtered .control-group:last-child > li:last-child {
            border-radius: 0 0 0 4px
        }

    .formeo-controls .control-groups {
        white-space: nowrap
    }

    .formeo-controls .control-group-labels {
        height: 100%;
        background: #fff;
        overflow: hidden
    }

        .formeo-controls .control-group-labels div {
            white-space: nowrap
        }

        .formeo-controls .control-group-labels h4 {
            display: inline-block;
            width: 100%
        }

    .formeo-controls .form-actions {
        display: none;
        float: right;
        margin-top: 4px
    }

        .formeo-controls .form-actions .svg-icon {
            fill: #666;
            display: none
        }

            .formeo-controls .form-actions .svg-icon:hover {
                fill: #000
            }

        .formeo-controls .form-actions .clear-form:hover, .formeo-controls .form-actions .save-form:hover {
            color: #fff
        }

        .formeo-controls .form-actions .save-form:hover {
            background-color: #325d88
        }

        .formeo-controls .form-actions .clear-form:hover {
            background-color: #d9534f
        }

@media (max-width:481px) {
    .formeo-controls {
        width: 45px
    }

        .formeo-controls .control-group {
            text-indent: -9999px
        }

        .formeo-controls .form-actions {
            display: inline-block;
            width: 100%;
            position: relative;
            vertical-align: middle;
            float: none;
            margin-top: 10px
        }

            .formeo-controls .form-actions > button + button {
                margin-top: -1px;
                margin-left: 0
            }

            .formeo-controls .form-actions > button {
                max-width: 100%;
                padding: 10px;
                border-radius: 0;
                line-height: 0;
                width: 100%;
                float: none;
                position: relative;
                display: block
            }

                .formeo-controls .form-actions > button:not(:first-child):not(:last-child) {
                    border-radius: 0
                }

                .formeo-controls .form-actions > button:first-child:not(:last-child) {
                    border-top-right-radius: 4px;
                    border-top-left-radius: 4px;
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0
                }

                .formeo-controls .form-actions > button:last-child:not(:first-child) {
                    border-bottom-left-radius: 4px;
                    border-bottom-right-radius: 4px;
                    border-top-right-radius: 0;
                    border-top-left-radius: 0
                }

            .formeo-controls .form-actions .control-icon {
                display: inline-block;
                margin-right: 10px
            }
}

.modal-dialog.modal-fullscreen {
    width: 100%;
    max-width: none;
    margin: 0;
}

.field-control .svg-icon, .formeo-controls .field-control .control-icon {
    pointer-events: none
}

.modal-dialog.modal-fullscreen {
    width: 100%;
    max-width: none;
    margin: 0;
}

.formeo-row {
    list-style: none !important;
}
/* Adjust the position of Formeo controls */
