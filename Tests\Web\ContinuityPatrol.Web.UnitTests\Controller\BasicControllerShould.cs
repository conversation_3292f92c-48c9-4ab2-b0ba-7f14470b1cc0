﻿using ContinuityPatrol.Application.Features.Company.Commands.Create;
using ContinuityPatrol.Application.Features.Company.Commands.CreateDefaultCompany;
using ContinuityPatrol.Application.Features.User.Commands.Create;
using ContinuityPatrol.Application.Features.User.Commands.CreateDefaultUser;
using ContinuityPatrol.Domain.ViewModels.CompanyModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Controller;

public class BasicControllerShould
{
    private readonly Mock<IDataProvider> _mockDataProvider = new();
    private readonly Mock<ILogger<BasicController>> _mockLogger = new();
    private readonly BasicController _controller;

    public BasicControllerShould()
    {
        _controller = new BasicController(
            _mockDataProvider.Object, _mockLogger.Object);
        _controller.TempData = new TempDataDictionary(new DefaultHttpContext(), Mock.Of<ITempDataProvider>());

    }


    [Fact]
    public async Task Configuration_ShouldRedirectToLogout_WhenCompanyAlreadyExists()
    {
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm> { new() });

        var result = await _controller.Configuration(new CreateDefaultCompanyCommand(), new CreateDefaultUserCommand());

        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Logout", redirect.ActionName);
        Assert.Equal("Account", redirect.ControllerName);
    }

    [Fact]
    public async Task Configuration_ShouldReturnView_WhenCompanyOrUserNameIsMissing()
    {
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm>());

        var result = await _controller.Configuration(new CreateDefaultCompanyCommand(), new CreateDefaultUserCommand());

        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public async Task Configuration_ShouldReturnView_WhenCreateDefaultCompanyThrowsException()
    {
        // Arrange
        var defaultCompany = new CreateDefaultCompanyCommand { Name = "TestCo" };
        var defaultUser = new CreateDefaultUserCommand { LoginName = "admin" };

        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm>()); // No companies, so creation allowed

        _mockDataProvider.Setup(dp => dp.Company.CreateDefaultCompany(It.IsAny<CreateDefaultCompanyCommand>()))
            .ThrowsAsync(new Exception("Database error during company creation"));

        // Act
        var result = await _controller.Configuration(defaultCompany, defaultUser);

        // Assert
        var view = Assert.IsType<ViewResult>(result);
        Assert.NotNull(view);
    }




    [Fact]
    public async Task Configuration_ShouldRedirectToUserInfo_WhenCompanyCreationSucceeds()
    {
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm>());

        _mockDataProvider.Setup(dp => dp.Company.CreateDefaultCompany(It.IsAny<CreateDefaultCompanyCommand>()))
            .ReturnsAsync(new CreateCompanyResponse { Success = true });

        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);
        _mockDataProvider.Setup(dp => dp.User.CreateDefaultUser(It.IsAny<CreateDefaultUserCommand>()))
            .ReturnsAsync(new CreateUserResponse { Success = true });

        var defaultCompany = new CreateDefaultCompanyCommand { Name = "TestCo" };
        var defaultUser = new CreateDefaultUserCommand { LoginName = "admin" };

        var result = await _controller.Configuration(defaultCompany, defaultUser);

        Assert.IsType<ViewResult>(result);
    }

    [Fact]
    public async Task Configuration_ShouldRedirectToUserInfo_WhenCompanyNotCreated()
    {
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm>());

        _mockDataProvider.Setup(dp => dp.Company.CreateDefaultCompany(It.IsAny<CreateDefaultCompanyCommand>()))
            .ReturnsAsync(new CreateCompanyResponse { Success = false });

        //_mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);
        //_mockDataProvider.Setup(dp => dp.User.CreateDefaultUser(It.IsAny<CreateDefaultUserCommand>()))
        //    .ReturnsAsync(new CreateUserResponse { Success = false });

        var defaultCompany = new CreateDefaultCompanyCommand { Name = "TestCo" };
        var defaultUser = new CreateDefaultUserCommand { LoginName = "admin" };

        var result = await _controller.Configuration(defaultCompany, defaultUser);

        Assert.IsType<ViewResult>(result);
    }



    [Fact]
    public async Task UserInfo_ShouldRedirectToLogout_WhenUserAlreadyExists()
    {
        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(true);

        var result = await _controller.UserInfo(new CreateDefaultUserCommand());

        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Logout", redirect.ActionName);
        Assert.Equal("Account", redirect.ControllerName);
    }

    [Fact]
    public async Task UserInfo_ShouldReturnView_WhenLoginNameMissing()
    {
        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);

        var result = await _controller.UserInfo(new CreateDefaultUserCommand());

        Assert.IsType<ViewResult>(result);
    }
    [Fact]
    public async Task UserInfo_ShouldRedirectToLogin_WhenUserCreatedSuccessfully()
    {
        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm> { new() { Id = "1", DisplayName = "TestCo" } });
        _mockDataProvider.Setup(dp => dp.User.CreateDefaultUser(It.IsAny<CreateDefaultUserCommand>()))
            .ReturnsAsync(new CreateUserResponse { Success = true, Message = "Created" });

        var result = await _controller.UserInfo(new CreateDefaultUserCommand { LoginName = "admin" });

        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Login", redirect.ActionName);
        Assert.Equal("Account", redirect.ControllerName);
    }

    [Fact]
    public async Task UserInfo_ShouldRedirectToUserInfo_WhenUserNotCreated()
    {
        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm> { new() { Id = "1", DisplayName = "TestCo" } });
        _mockDataProvider.Setup(dp => dp.User.CreateDefaultUser(It.IsAny<CreateDefaultUserCommand>()))
            .ReturnsAsync(new CreateUserResponse { Success = false, Message = "Created" });

        var result = await _controller.UserInfo(new CreateDefaultUserCommand { LoginName = "admin" });

        Assert.IsType<ViewResult>(result);
    }


    [Fact]
    public async Task UserInfo_ShouldReturnView_WhenCreateDefaultUserThrowsException()
    {
        // Arrange
        var defaultUser = new CreateDefaultUserCommand { LoginName = "admin" };

        _mockDataProvider.Setup(dp => dp.User.HasUserAsync()).ReturnsAsync(false);

        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ReturnsAsync(new List<CompanyNameVm> { new() { Id = "1", DisplayName = "CompanyX" } });

        _mockDataProvider.Setup(dp => dp.User.CreateDefaultUser(It.IsAny<CreateDefaultUserCommand>()))
            .ThrowsAsync(new Exception("Database error during user creation"));

        // Act
        var result = await _controller.UserInfo(defaultUser);

        // Assert
        var view = Assert.IsType<ViewResult>(result);
        Assert.NotNull(view);
    }

    [Fact]
    public async Task GetCompanies_ShouldReturnList_WhenCompaniesExist()
    {
        var companies = new List<CompanyNameVm> { new() { Id = "1", DisplayName = "Test" } };
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin()).ReturnsAsync(companies);

        var result = await _controller.GetCompanies();

        Assert.NotNull(result);
        Assert.Single(result);
    }


    [Fact]
    public async Task GetCompanies_ShouldReturnNull_WhenExceptionThrown()
    {
        _mockDataProvider.Setup(dp => dp.Company.GetCompanyNamesOnLogin())
            .ThrowsAsync(new Exception("DB error"));

        var result = await _controller.GetCompanies();

        Assert.Null(result);
    }



}