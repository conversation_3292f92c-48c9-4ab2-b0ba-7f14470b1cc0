﻿using ContinuityPatrol.Domain.ViewModels;
using ContinuityPatrol.Web.Controllers;
using Microsoft.AspNetCore.Mvc.Routing;

namespace ContinuityPatrol.Web.UnitTests.Controller;

public class ErrorControllerShould
{
    private readonly ErrorController _controller;

    public ErrorControllerShould()
    {
        Mock<ILogger<ErrorController>> loggerMock = new();
        _controller = new ErrorController(loggerMock.Object)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            }
        };
    }

    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnBadRequestView_For400()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/bad-request");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(400);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("BadRequest", view.ViewName);
    }
    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnUnauthorizedView_For401()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/unauthorized");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(401);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("Unauthorized", view.ViewName);
    }

    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnForbiddenView_For403()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/forbidden");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(403);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("Forbidden", view.ViewName);
    }

    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnNotFoundView_For404()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/not-found");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(404);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("NotFound", view.ViewName);
        Assert.IsType<ErrorViewModel>(view.Model);
    }

    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnServerErrorView_For500()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/server-error");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(500);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("ServerError", view.ViewName);
    }

    [Fact]
    public void HttpStatusCodeHandler_ShouldReturnBadRequestView_ForUnknownCode()
    {
        var feature = new Mock<IStatusCodeReExecuteFeature>();
        feature.Setup(f => f.OriginalPath).Returns("/unknown");

        _controller.HttpContext.Features.Set(feature.Object);

        var result = _controller.HttpStatusCodeHandler(999);

        var view = Assert.IsType<ViewResult>(result);
        Assert.Equal("BadRequest", view.ViewName);
    }

    [Fact]
    public async Task UnhandledException_ShouldReturnView_WhenExceptionIsNull()
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Features.Set<IExceptionHandlerPathFeature>(null); 

        var controller = SetupErrorController(httpContext);

        var result = await controller.UnhandledException();

        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Null(viewResult.ViewName);
    }

    [Fact]
    public async Task UnhandledException_ShouldRedirectToLogout_WhenSessionLoggedOutMessage()
    {
        // Arrange
        var context = new DefaultHttpContext();
        var featureMock = new Mock<IExceptionHandlerPathFeature>();
        featureMock.Setup(f => f.Error).Returns(new Exception("Session logged out due to timeout"));
        featureMock.Setup(f => f.Path).Returns("/Home/Index");
        context.Features.Set(featureMock.Object);

        var controller = SetupErrorController(context);

        var urlHelperMock = new Mock<IUrlHelper>();
        urlHelperMock.Setup(u => u.Action(It.IsAny<UrlActionContext>())).Returns("Logout");
        controller.Url = urlHelperMock.Object;

        // Act
        var result = await controller.UnhandledException();

        // Assert
        var redirect = Assert.IsType<RedirectToActionResult>(result);
        Assert.Equal("Logout", redirect.ActionName);
        Assert.Equal("Account", redirect.ControllerName);
    }


    [Fact]
    public async Task UnhandledException_ShouldReturnViewWithErrorViewModel_WhenExceptionOccurs()
    {
        // Arrange
        var exception = new Exception("Unexpected error occurred");
        var context = new DefaultHttpContext();

        var featureMock = new Mock<IExceptionHandlerPathFeature>();
        featureMock.Setup(f => f.Error).Returns(exception);
        featureMock.Setup(f => f.Path).Returns("/Dashboard/View");
        context.Features.Set(featureMock.Object);

        var controller = SetupErrorController(context);

        // Act
        var result = await controller.UnhandledException();

        // Assert
        var view = Assert.IsType<ViewResult>(result);
        var model = Assert.IsAssignableFrom<ErrorViewModel>(view.Model);
        Assert.Equal("Unexpected error occurred", model.Message);
        Assert.Equal("/Dashboard/View", model.Path);
    }
    

    private static ErrorController SetupErrorController(DefaultHttpContext httpContext)
    {
        var authServiceMock = new Mock<IAuthenticationService>();
        authServiceMock
            .Setup(x => x.SignOutAsync(It.IsAny<HttpContext>(), It.IsAny<string>(), It.IsAny<AuthenticationProperties>()))
            .Returns(Task.CompletedTask);

        httpContext.RequestServices = new ServiceCollection()
            .AddSingleton(authServiceMock.Object)
            .BuildServiceProvider();

        httpContext.Session = new Mock<ISession>().Object;

        var controller = new ErrorController(Mock.Of<ILogger<ErrorController>>())
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = httpContext
            },
            TempData = new TempDataDictionary(httpContext, Mock.Of<ITempDataProvider>())
        };
        return controller;
    }


}