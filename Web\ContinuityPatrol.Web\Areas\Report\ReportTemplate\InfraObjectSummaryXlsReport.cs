﻿using ContinuityPatrol.Application.Features.Report.Queries.InfraObjectSummaryReport;
using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Web.Areas.Report.Controllers;
using DevExpress.XtraCharts;
using Newtonsoft.Json;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{
    [SupportedOSPlatform("windows")]

    public partial class InfraObjectSummaryExcel : DevExpress.XtraReports.UI.XtraReport
    {
        private readonly ILogger<PreBuildReportController> _logger;
        public  InfraObjectSummaryReport infraObjectSummaryReport = new InfraObjectSummaryReport();
        public InfraObjectSummaryExcel(string data)
        {
            try
            {                
                infraObjectSummaryReport = JsonConvert.DeserializeObject<InfraObjectSummaryReport>(data);
                _logger = PreBuildReportController._logger;
                var report = infraObjectSummaryReport.InfraObjectSummaryReportVms;
                report.ForEach(x => x.DRHealth = string.IsNullOrEmpty(x.DRHealth) ? "NA" : (x.DRHealth == "Available" ? "Up" : (x.DRHealth == "Major Impact" ? "Down" : x.DRHealth)));

                var businessServiceName = infraObjectSummaryReport.InfraSummaryReportDataName;
                var maxConfiguredRpo = report
                .Where(item => item.ConfiguredRpo != "NA")
                .Max(item => item.ConfiguredRpo).ToInt64();

                InitializeComponent();
                ClientCompanyLogo();
                this.DisplayName = businessServiceName + "_InfraObjectSummaryReport_" + DateTime.Now.ToString("ddMMyyyy" + "_" + "hhmmsstt");

                this.DataSource = report;

                tableCell11.BeforePrint += tableCell_SerialNumber_BeforePrint;

                foreach (var item in report)
                {
                    string datalagvalue = item.DataLag;
                    if (datalagvalue.Contains("+"))
                    {
                        datalagvalue = datalagvalue.TrimStart('+');
                        datalagvalue = datalagvalue.Replace(" ", ".");
                    }
                    if (TimeSpan.TryParseExact(datalagvalue, "hh\\:mm\\:ss", CultureInfo.InvariantCulture, out TimeSpan dataLagTime))
                    {
                        var tim = Convert.ToDouble(item.ConfiguredRpo);
                        if (dataLagTime.TotalMinutes <= Convert.ToDouble(item.ConfiguredRpo))
                        {
                            item.IsDatalag = true;
                            item.DRHealth = "Up";
                        }
                        else
                        {
                            item.IsDatalag = false;
                            item.DRHealth = "Down";
                        }
                    }
                    else if (TimeSpan.TryParseExact(datalagvalue, "dd\\.hh\\:mm\\:ss", CultureInfo.InvariantCulture, out TimeSpan dataLagTime1))
                    {
                        var tim = Convert.ToDouble(item.ConfiguredRpo);
                        if (dataLagTime1.TotalMinutes <= Convert.ToDouble(item.ConfiguredRpo))
                        {
                            item.IsDatalag = true;
                            item.DRHealth = "Up";
                        }
                        else
                        {
                            item.IsDatalag = false;
                            item.DRHealth = "Down";
                        }
                    }
                    else
                    {
                        Console.WriteLine("Invalid format for DataLag");
                        item.DataLag = "";
                        item.DRHealth = "NA";
                    }
                }

                var HealthUP = report.Where(x => x.DRHealth == "Available" || x.DRHealth == "Up").Count();
                var HealthDown = report.Where(x => x.DRHealth == "Major Impact" || x.DRHealth == "Down").Count();

                lblInfraCount.Text = report.Count.ToString("D2");
                lblHealthUP.Text = HealthUP.ToString("D2");
                lblHealthDown.Text = HealthDown.ToString("D2");
                lblRPO.Text = maxConfiguredRpo.ToString("D2");
                lblBsName.Text = businessServiceName;

                bool bsName = businessServiceName != "All Operational Service";
                xrShape1.Visible = bsName;
                xrLabel14.Visible = bsName;
                xrShape3.Visible = bsName;
                xrPictureBox6.Visible = bsName;
                xrLabel5.Visible = bsName;
                lblRPO.Visible = bsName;
                xrLabel1.Visible = bsName;
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObjectSummary excel Report. The error message : " + ex.Message); throw; }
        }
        private int serialNumber = 1;

        private void tableCell_SerialNumber_BeforePrint(object sender, CancelEventArgs e)
        {
            XRTableCell cell = (XRTableCell)sender;

            cell.Text = serialNumber.ToString();
            serialNumber++;
        }
        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = "Report Generated By: " + infraObjectSummaryReport.ReportGeneratedBy.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObjectSummary excel Report's User name. The error message : " + ex.Message); throw; }
        }
        private void xrChart1_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var report = infraObjectSummaryReport.InfraObjectSummaryReportVms;

                var HealthUP = report.Where(x => x.DRHealth == "Available" || x.DRHealth == "Up").Count();
                var HealthDown = report.Where(x => x.DRHealth == "Major Impact" || x.DRHealth == "Down").Count();

                Int64 NodataFound = 0;

                if (HealthUP == 0 && HealthDown == 0)
                {
                    Series series1 = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series1);
                    NodataFound = 1;
                    series1.DataSource = CreateChartData(HealthUP, HealthDown, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    series1.View = doughnutSeriesView;
                    series1.ArgumentScaleType = ScaleType.Auto;
                    series1.ArgumentDataMember = "Argument";
                    series1.ValueScaleType = ScaleType.Numerical;
                    series1.ValueDataMembers.AddRange(new string[] { "Value" });
                    series1.Label.TextPattern = "{A}";
                    series1.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
                else
                {
                    Series series = new Series("Series1", ViewType.Doughnut);
                    xrChart1.Series.Add(series);
                    series.DataSource = CreateChartData(HealthUP, HealthDown, NodataFound);
                    DoughnutSeriesView doughnutSeriesView = new DoughnutSeriesView();
                    doughnutSeriesView.MinAllowedSizePercentage = 70D;
                    series.View = doughnutSeriesView;
                    series.ArgumentScaleType = ScaleType.Auto;
                    series.ArgumentDataMember = "Argument";
                    series.ValueScaleType = ScaleType.Numerical;
                    series.ValueDataMembers.AddRange(new string[] { "Value" });
                    series.Label.TextPattern = "{A}\n{V}";
                    series.Label.Border.Visibility = DevExpress.Utils.DefaultBoolean.False;
                    xrChart1.Legend.Visibility = DevExpress.Utils.DefaultBoolean.False;
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObjectSummary excel Report's chart. The error message : " + ex.Message); throw; }
        }
        private DataTable CreateChartData(Int64 HealthUP, Int64 HealthDown, Int64 NodataFound)
        {
            DataTable table = new DataTable("Table1");
            table.Columns.Add("Argument", typeof(string));
            table.Columns.Add("Value", typeof(Int64));
            Random rnd = new Random();
            table.Rows.Add("Health Up", HealthUP);
            table.Rows.Add("Health Down", HealthDown);
            table.Rows.Add("No Data Found", NodataFound);

            return table;
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the InfraObjectSummary excel Report's version. The error message : " + ex.Message); throw; }
        }
        public void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(PreBuildReportController.CompanyLogo) ? "NA" : PreBuildReportController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Error occured while display the customer logo in the InfraObjectSummary Excel Report" + ex.Message.ToString());
            }
        }
    }
}
