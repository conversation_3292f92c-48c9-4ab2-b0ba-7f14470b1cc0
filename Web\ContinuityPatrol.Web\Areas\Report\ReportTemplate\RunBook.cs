﻿using ContinuityPatrol.Shared.Core.Extensions;
using ContinuityPatrol.Shared.Core.Helper;
using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Drawing;
using System.Runtime.Versioning;

namespace ContinuityPatrol.Web.Areas.Report.ReportTemplate
{

    [SupportedOSPlatform("windows")]
    public partial class RunBook : DevExpress.XtraReports.UI.XtraReport
    {
        public static string ReportWorkflowName = string.Empty;
        public static string ReportGeneratedName = string.Empty;
        HashSet<string> uniqueTexts = new HashSet<string>();
        HashSet<string> allProperties = new HashSet<string>();
        HashSet<string> orderedProperties = new HashSet<string>();
        List<string> targetPropertyNames = new List<string> { "PRServer_IPAddress", "PRServer_HostName", "DRServer_IPAddress", "DRServer_HostName" };
        List<string> output = new List<string>();
        private readonly ILogger<WorkflowConfigurationController> _logger;
        public RunBook(ILogger<WorkflowConfigurationController> logger)
        {
            try
            {
                _logger = logger;
                var Report = WorkflowConfigurationController.WorkflowActionList;
                InitializeComponent();
                _logger.LogInformation("RunBook Initialized Successfully");
                ClientCompanyLogo();
                this.DataSource = Report.FirstOrDefault().GetRunBookReportListVms;
                foreach (var reportVm in Report)
                {
                    var jsonList = reportVm.GetRunBookReportListVms.Select(item => item.Properties).ToList();
                    var jsonObjects = jsonList.Select(JObject.Parse).ToList();

                    foreach (var jsonObject in jsonObjects)
                    {

                        if (jsonObject.ContainsKey("ScriptBlock"))
                        {
                            var scriptBlockValue = jsonObject["ScriptBlock"];
                            jsonObject.Remove("ScriptBlock");
                            jsonObject["Command"] = scriptBlockValue;
                        }
                        //if (jsonObject.ContainsKey("Server_IPAddress"))
                        //{
                        //    var scriptBlockValue = jsonObject["Server_IPAddress"];
                        //    jsonObject.Remove("Server_IPAddress");
                        //    jsonObject["PRServer_IPAddress"] = scriptBlockValue;
                        //}
                        //if (jsonObject.ContainsKey("Server_HostName"))
                        //{
                        //    var scriptBlockValue = jsonObject["Server_HostName"];
                        //    jsonObject.Remove("Server_HostName");
                        //    jsonObject["PRServer_HostName"] = scriptBlockValue;
                        //}
                    }

                    foreach (var jsonObject in jsonObjects)
                    {
                        foreach (var property in jsonObject.Properties())
                        {
                            if (property.Name.ToString() != "NA" && property.Value.ToString() != "NA")
                            {
                                allProperties.Add(property.Name);
                                if (targetPropertyNames.Contains(property.Name))
                                {
                                    orderedProperties.Add(property.Name);
                                }
                            }
                        }
                    }
                    // Add ordered properties to the output list
                    output.AddRange(orderedProperties.Where(allProperties.Contains));
                    // Add remaining properties to the output list
                    output.AddRange(allProperties.Except(orderedProperties));

                    allProperties.Clear();
                    allProperties.UnionWith(output);

                    foreach (var jsonObject in jsonObjects)
                    {
                        foreach (var propertyName in allProperties)
                        {
                            if (!jsonObject.Properties().Any(p => string.Equals(p.Name, propertyName, StringComparison.OrdinalIgnoreCase)))
                            {
                                jsonObject.Add(propertyName, "NA");
                            }
                        }
                    }
                    foreach (var (reportListVm, jsonObject) in reportVm.GetRunBookReportListVms.Zip(jsonObjects, (listVm, json) => (listVm, json)))
                    {
                        var sortedJson = new JObject(jsonObject.Properties());
                        reportListVm.Properties = sortedJson.ToString(Newtonsoft.Json.Formatting.None).Replace("\r", string.Empty).Replace("\n", string.Empty);
                    }
                }
               
                    ReportGeneratedName = Report.FirstOrDefault().ReportGeneratedBy;
                    ReportWorkflowName = Report.FirstOrDefault().WorkflowName;
                int rowNumber = 0;
                int rowIndex = 0;
                bool grpName = Report.FirstOrDefault().GetRunBookReportListVms.Any(x => x.GroupName != "NA");
                bool parllel = Report.FirstOrDefault().GetRunBookReportListVms.Any(x => x.IsParallel.ToLower() == "true");
                List<string> grpNames = new List<string>();
                int grpNameSpan = 0;
                xrTable2.BeforePrint += (sender, e) =>
                    {
                        var table = (XRTable)sender;
                        table.Rows.Clear();
                        var tablevalue = Report.FirstOrDefault().GetRunBookReportListVms[rowNumber];
                            XRTableRow dataRow = new XRTableRow();
                            bool isOddRow = rowIndex % 2 != 0;
                            XRTableRow currentRowStyle = new XRTableRow();
                            currentRowStyle.BackColor = isOddRow ? System.Drawing.Color.FromArgb(243, 245, 248) : System.Drawing.Color.White;
                            currentRowStyle.BorderColor = System.Drawing.Color.Transparent;
                            currentRowStyle.Borders = DevExpress.XtraPrinting.BorderSide.None;
                            currentRowStyle.BorderWidth = 0F;
                            currentRowStyle.Font = new DevExpress.Drawing.DXFont("SF UI Text", 8F);
                            currentRowStyle.ForeColor = System.Drawing.Color.Black;
                            currentRowStyle.Name = isOddRow ? "Runbook_odd" : "Runbook_odd";
                            currentRowStyle.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
                            dataRow = currentRowStyle;

                            dataRow.Cells.Add(new XRTableCell { Text = tablevalue.SrNo.ToString(), BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WordWrap = false, WidthF = 150.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            var groupName = (!string.IsNullOrEmpty(tablevalue?.GroupName?.ToString()) && tablevalue?.GroupName?.ToString() != "NA") ? "Group Name : " + tablevalue?.GroupName?.ToString() : " - ";
                            var isParallel = (!string.IsNullOrEmpty(tablevalue?.IsParallel?.ToString()) && tablevalue?.IsParallel?.ToString() != "NA") ? "IsParallel : " + tablevalue?.IsParallel?.ToString() : " - ";
                            if (grpName)
                            {
                                // Check for equality using Equals method
                                bool isGroupNamePresent = grpNames.Any(existingGroupName => existingGroupName.Equals(groupName));
                                if (isGroupNamePresent)
                                {
                                    grpNameSpan++;

                                //table.Rows[rowIndex - grpNameSpan].Cells[1].RowSpan = grpNameSpan + 1;
                                if (rowNumber == Report.FirstOrDefault().GetRunBookReportListVms.Count - 1)
                                {
                                    dataRow.Cells.Add(new XRTableCell { Text = " ", BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.Bottom, WordWrap = true, WidthF = 400.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, BackColor = Color.White, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                }
                                else
                                {
                                    dataRow.Cells.Add(new XRTableCell { Text = " ", BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.None, WordWrap = true, WidthF = 400.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, BackColor = Color.White, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                }

                            }
                            else
                                {
                                    if (groupName.Contains("Group Name")) grpNames.Add(groupName);
                                    
                                    grpNameSpan = 0;
                                if (rowNumber < Report.FirstOrDefault().GetRunBookReportListVms.Count - 1 && Report.FirstOrDefault().GetRunBookReportListVms[rowNumber].GroupName.Equals(Report.FirstOrDefault().GetRunBookReportListVms[rowNumber + 1].GroupName))
                                {
                                    dataRow.Cells.Add(new XRTableCell
                                    {
                                        Text = groupName,
                                        BorderWidth = 1,
                                        BorderColor = System.Drawing.Color.Black,
                                        Borders = DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right,
                                        WordWrap = true,
                                        BackColor = Color.White,
                                        WidthF = 400.11F,
                                        Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6),
                                        TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft,
                                        Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F)
                                    });
                                }
                                else
                                {
                                    dataRow.Cells.Add(new XRTableCell
                                    {
                                        Text = groupName,
                                        BorderWidth = 1,
                                        BorderColor = System.Drawing.Color.Black,
                                        Borders = DevExpress.XtraPrinting.BorderSide.All,
                                        WordWrap = true,
                                        WidthF = 400.11F,
                                        Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6),
                                        TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft,
                                        Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F)
                                    });
                                }
                            }
                            }
                            if(parllel) dataRow.Cells.Add(new XRTableCell { Text = isParallel, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WordWrap = true, WidthF = 400.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            dataRow.Cells.Add(new XRTableCell { Text = tablevalue.WorkflowActionName, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WordWrap = true, WidthF = 830.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            dataRow.Cells.Add(new XRTableCell { Text = "Action Type : " + tablevalue.ActionType, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WordWrap = true, WidthF = 830.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            dataRow.Cells.Add(new XRTableCell { Text = "Action : " + tablevalue.WorkflowAction, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WordWrap = true, WidthF = 830.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            dataRow.Cells.Add(new XRTableCell { Text = "RTO : " + tablevalue.RTO, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, Borders = DevExpress.XtraPrinting.BorderSide.All, WidthF = 400.11F, WordWrap = false, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            var orderValue = new List<string>();
                            var emptyValues = new List<string>();

                            var jsonObjects = JObject.Parse(tablevalue.Properties);

                            HashSet<string> jsonSplit = new HashSet<string>();
                            foreach (var property in jsonObjects.Properties())
                            {
                                jsonSplit.AddRange(property.Name + "`" + property.Value.ToString());
                            }

                            foreach (var prop in allProperties)
                            {
                                var ColumnValues = jsonSplit.Where(c => c.Split("`")[0] == prop);
                                foreach (var value in ColumnValues)
                                {
                                    var propertyColumn = value.Split('`');
                                    var matchingColumn = propertyColumn[0];
                                    var parameterValue = propertyColumn[1];
                                    // bool decrypt = (matchingColumn?.Trim().Contains("Command") == true);
                                    if (/*decrypt && */parameterValue.Contains("_encryptedcpl"))
                                    {
                                        parameterValue = SecurityHelper.Decrypt(parameterValue.Replace("_encryptedcpl", ""));
                                    }
                                    var password = (matchingColumn?.Trim().ToLower().Contains("password") == true);
                                    if (password) { parameterValue = SecurityHelper.Encrypt(parameterValue); }
                                    //if (propertyColumn[0].Equals("DRServerName") || propertyColumn[0].Equals("DRDBName") ||propertyColumn[0].Equals("ServerName") || propertyColumn[0].Equals("DBName") ||propertyColumn[0].Equals("PRServerName") || propertyColumn[0].Equals("PRDBName") || propertyColumn[0].Equals("PRServer_ConnectViaHostName") || propertyColumn[0].Equals("DRServer_ConnectViaHostName") || propertyColumn[0].Equals("Server_ConnectViaHostName")) { continue; }
                                    // if (propertyColumn[0].Contains("PRServer_ConnectViaHostName")) { if (propertyColumn[1] == "False") { } }
                                    var matchingValue = matchingColumn + " : " + parameterValue;
                                    if (matchingColumn == prop)
                                    {
                                        if (propertyColumn[1] == "NA") { orderValue.Add("-"); break; }
                                        orderValue.Add(matchingValue);
                                        break;
                                    }
                                }

                            }
                            foreach (var propertyvalue in orderValue)
                            {
                                var property = propertyvalue.Split(":")[0];
                                if (propertyvalue != null)
                                {
                                    var replacestr = propertyvalue.Contains("TimeOut(Sec)") ? "TimeOut(Sec)" : "TimeOut";
                                    dataRow.Cells.Add(new XRTableCell { Text = propertyvalue.Replace(replacestr, "Time Out(Sec)"), Multiline = true, Borders = DevExpress.XtraPrinting.BorderSide.All, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, WordWrap = true, WidthF = 830.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                                }
                            }
                            //if (emptyValues.Count > 0)
                            //{
                            //    foreach (var propertyvalue in emptyValues)
                            //    {
                            //        if (propertyvalue != null)
                            //        {
                            //            dataRow.Cells.Add(new XRTableCell { Text = propertyvalue, Borders = DevExpress.XtraPrinting.BorderSide.All, BorderWidth = 1, BorderColor = System.Drawing.Color.Black, WordWrap = false, WidthF = 500.11F, Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 5, 6), TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter, Font = new DevExpress.Drawing.DXFont("SF UI Text", 9F) });
                            //        }
                            //    }

                            //}
                            table.Rows.Add(dataRow);
                            rowIndex++;
                
                        xrTable2.WidthF = 200 * table.Rows[0].Cells.Count;
                        XRTableCell lastCell = table.Rows[table.Rows.Count - 1].Cells[table.Rows[table.Rows.Count - 1].Cells.Count - 1];
                        PointF lastCellLocation = lastCell.BoundsF.Location;
                        prperpetuuitiLogo.LocationF = new PointF(lastCellLocation.X + lastCell.BoundsF.Width - prperpetuuitiLogo.Width + 20, lastCellLocation.Y + lastCell.BoundsF.Height);
                        prClientLogo.LocationF = new PointF(lastCellLocation.X, lastCellLocation.Y + lastCell.BoundsF.Height);
                        rowNumber++;
                    };

                               }
            catch (Exception ex) { _logger.LogError("Error occured while display the RunBook Report. The error message : " + ex.Message); throw; }
        }

        private void _userName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                _username.Text = ReportGeneratedName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RunBook Report's User name. The error message : " + ex.Message); throw; }
        }

        private void WorkflowName_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                xrLabel4.Text = ReportWorkflowName.ToString();
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RunBook Report's Workflow Name. The error message : " + ex.Message); throw; }
        }
        private void _version_BeforePrint(object sender, CancelEventArgs e)
        {
            try
            {
                var builder = new ConfigurationBuilder()
                     .SetBasePath(Directory.GetCurrentDirectory())
                     .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

                IConfigurationRoot configuration = builder.Build();
                var version = configuration["CP:Version"];
                xrLabel49.Text = "Continuity Patrol Version " + version + " | © 2025 - 2026 Perpetuuiti - All Rights Reserved.";
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the RunBook Report's CP Version. The error message : " + ex.Message); throw; }
        }
        private void ClientCompanyLogo()
        {
            try
            {
                string imgbase64String = string.IsNullOrEmpty(WorkflowConfigurationController.CompanyLogo) ? "NA" : WorkflowConfigurationController.CompanyLogo;
                if (!string.IsNullOrEmpty(imgbase64String) && imgbase64String != "NA")
                {
                    prperpetuuitiLogo.Visible = false;
                    if (imgbase64String.Contains(","))
                    {
                        imgbase64String = imgbase64String.Split(',')[1];
                    }
                    byte[] imageBytes = Convert.FromBase64String(imgbase64String);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        Image image = Image.FromStream(ms);
                        prClientLogo.Image = image;
                    }
                }
                else
                {
                    prClientLogo.Visible = false;
                    prperpetuuitiLogo.Visible = true;
                }
            }
            catch (Exception ex) { _logger.LogError("Error occured while display the customer logo in RunBook Report. The error message : " + ex.Message); throw; throw; }
        }
    }
}