﻿global using AutoMapper;
global using ContinuityPatrol.Web.Base;
global using Microsoft.AspNetCore.Authentication.Cookies;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.Rendering;
global using Microsoft.AspNetCore.Razor.TagHelpers;
global using Microsoft.AspNetCore.ResponseCompression;
global using Microsoft.Net.Http.Headers;
global using Serilog;
global using Serilog.Core;
global using Serilog.Events;
global using System.Diagnostics;
global using System.IO.Compression;
global using System.Security.Cryptography;
global using System.Text;
global using WebMarkupMin.AspNet.Common.Compressors;
global using WebMarkupMin.AspNetCore6;
global using SameSiteMode = Microsoft.AspNetCore.Http.SameSiteMode;
global using Microsoft.AspNetCore.Authentication;
global using System.ComponentModel;
global using MediatR;
global using DevExpress.AspNetCore;
global using DevExpress.Drawing;
global using Microsoft.Extensions.Caching.Distributed;
global using Microsoft.Extensions.Options;
global using Serilog.Filters;
global using System.Text.RegularExpressions;
global using DevExpress.XtraReports.UI;
global using Microsoft.AspNetCore.SignalR;
global using Microsoft.AspNetCore.Mvc.Filters;
global using Microsoft.AspNetCore.Diagnostics;
global using System.Runtime.Versioning;
global using Microsoft.Extensions.FileProviders;
global using System.Security.AccessControl;
global using System.Security.Principal;
global using Microsoft.AspNetCore.Diagnostics.HealthChecks;
global using Serilog.Debugging;