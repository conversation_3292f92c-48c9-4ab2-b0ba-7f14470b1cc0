﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.web>
			<!-- Removes version headers from response -->
			<httpRuntime enableVersionHeader="false" />
		</system.web>
		<system.webServer>
			<httpProtocol>
				<customHeaders>
					<remove name="X-Powered-By" />
					<remove name="Server" />
				</customHeaders>
			</httpProtocol>
			<modules runAllManagedModulesForAllRequests="false">
				<remove name="WebDAVModule" />
			</modules>
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
				<remove name="WebDAV" />
			</handlers>
			<aspNetCore
				processPath="dotnet"
				arguments=".\ContinuityPatrol.Web.dll"
				stdoutLogEnabled="true"
				stdoutLogFile=".\logs\stdout"
				hostingModel="inprocess">
				<environmentVariables>
					<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
				</environmentVariables>
			</aspNetCore>
			<security>
				<requestFiltering>
					<requestLimits maxAllowedContentLength="104857600"
					               maxUrl="10999"
					               maxQueryString="2097151" />
					<!--<requestLimits maxUrl="10999" maxQueryString="2097151" />-->
				</requestFiltering>
			</security>
		</system.webServer>
	</location>
</configuration>