let workflowContainer = [], userRoleContainer = [], profileContainer = [], userContainer = [], userListData = [], userGroupDatas = [], userGroupContainer = [], dates = '', WorkflowId = '', selectedValue = '', WorkflowName = '', datecheck = true, Radiobutton = '', selectedDatas = [], profileData = [], accessTypeData = '', globalUserPrevilageId = '';
const exceptThisSymbol = ["!", "@", "#", "$", "%", "^", "&", "*", "(", ")", "_", "+", "=", "{", "}", "[", "]", "|", ":", ";", "'", "\"", "<", ">", "?", "/", "\\"];

const userPrivilegeURL = {
    PaginatedUrl: "/ITAutomation/UserPrivileges/GetPaginatedWorkflowPermissions",
    workFlowUrl: "ITAutomation/UserPrivileges/GetWorkFlowList",
    ProfileListUrl: "ITAutomation/UserPrivileges/GetProfileList",
    userListUrl: "ITAutomation/UserPrivileges/GetUserNames",
    userGroupListUrl: "ITAutomation/UserPrivileges/GetAllUserGroups",
    createUpdateUrl: "ITAutomation/UserPrivileges/CreateOrUpdate"
}


$(function () {

    let permission = {
        "createPermission": $("#orchestrationCreate").data("create-permission").toLowerCase(),
        "deletePermission": $("#orchestrationDelete").data("delete-permission").toLowerCase()
    }
    if (permission.createPermission == 'false') {
        $("#btnCreate").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
    }
    let selectedValues = [];
    let dataTable = $('#userPrivilegeTable').DataTable(
        {
            language: {
                paginate: {
                    next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                    previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                }
                , infoFiltered: ""
            },
            dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
            scrollY: true,
            deferRender: true,
            scroller: true,
            "processing": true,
            "serverSide": true,
            "filter": true,
            "order": [],

            "ajax": {
                "type": "GET",
                "url": userPrivilegeURL.PaginatedUrl,
                "dataType": "json",

                "data": function (d) {

                    d.PageNumber = Math.ceil(d.start / d.length) + 1;
                    d.pageSize = d.length;
                    d.searchString = selectedValues?.length === 0 ? $('#search-inp').val() : selectedValues.join(';');
                    selectedValues.length = 0;
                },
                "dataSrc": function (json) {

                    json.recordsTotal = json?.totalPages;
                    json.recordsFiltered = json?.totalCount;
                    if (json.data.length === 0) {
                        $(".pagination-column").addClass("disabled")
                    }
                    else {
                        $(".pagination-column").removeClass("disabled")
                    }
                    return json?.data;
                },
            },
            "columnDefs": [
                {
                    "targets": [1, 2],
                    "className": "truncate"
                }
            ],
            "columns": [
                {
                    "data": null,
                    "name": "Sr. No.",
                    "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row, meta) {
                        if (type === 'display') {
                            return meta.row + 1;
                        }
                        return data;
                    },

                },
                {
                    "data": "accessProperties", "name": "Name", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {
                        // let accessdata = JSON.parse(data)
                        if (data) {
                            let name = JSON.parse(data).map(userpre => userpre.name);
                            if (type === 'display') {
                                return '<span title="' + name + '">' + name + '</span>';
                            }
                        }
                        return data;
                    }
                }, {
                    "data": "userProperties", "name": "Name", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {
                        //let userProperties = JSON.parse(data)
                        if (data) {
                            let name = JSON.parse(data).map(userpre => userpre.name);
                            if (type === 'display') {
                                return '<span title="' + name + '">' + name + '</span>';
                            }
                        }

                        return data;
                    }
                },
                {
                    "data": "scheduleTime", "name": "Start Date", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {

                        let startDate = JSON.parse(data);
                        if (type === 'display' && startDate?.length) {
                            return '<span title="' + startDate[0].startDate + '">' + startDate[0].startDate + '</span>';
                        } else {
                            return data ? data : "NA";
                        }

                    }
                }, {
                    "data": "scheduleTime", "name": "End Date", "autoWidth": true,
                    "orderable": false,
                    "render": function (data, type, row) {

                        let endDate = JSON.parse(data);

                        if (type === 'display' && endDate?.length) {
                            return '<span title="' + endDate[0].endDate + '">' + endDate[0].endDate + '</span>';
                        } else {
                            return data ? data : "NA";
                        }

                    }
                },
                {
                    "render": function (data, type, row) {

                        let rows = JSON.parse(row.accessProperties);
                        if (permission.createPermission === 'true' && permission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">

                                            <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>

                                        <span role="button" title="Delete"  class="delete-button" data-user-id="${row.id}" data-user-name="${rows[0]?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>

                            </div>`;
                        }
                        else if (permission.createPermission === 'true' && permission.deletePermission === "false") {
                            return `
                        <div class="d-flex align-items-center gap-2">

                                            <span role="button" title="Edit" class="edit-button" data-user='${JSON.stringify(row)}'>
                                                <i class="cp-edit"></i>
                                            </span>

                                        <span role="button" title="Delete"  class="icon-disabled">
                                            <i class="cp-Delete"></i>
                                        </span>

                            </div>`;
                        }
                        else if (permission.createPermission === 'false' && permission.deletePermission === "true") {
                            return `
                        <div class="d-flex align-items-center gap-2">

                                            <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>

                                        <span role="button" title="Delete"  class="delete-button" data-user-id="${row.id}" data-user-name="${rows[0]?.name}" data-bs-toggle="modal" data-bs-target="#DeleteModal">
                                            <i class="cp-Delete"></i>
                                        </span>

                            </div>`;
                        }
                        else {
                            return `
                        <div class="d-flex align-items-center gap-2">

                                            <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>

                                         <span role="button" title="Delete"  class="icon-disabled">
                                            <i class="cp-Delete"></i>
                                        </span>

                            </div>`;
                        }

                    },
                    "orderable": false
                }

            ],
            "rowCallback": function (row, data, index) {
                let api = this.api();
                let startIndex = api.context[0]._iDisplayStart;
                let counter = startIndex + index + 1;
                $('td:eq(0)', row).html(counter);
            },
            initComplete: function () {
                $('.paginate_button.page-item.previous').attr('title', 'Previous');
                $('.paginate_button.page-item.next').attr('title', 'Next');
            },
        });

    $('#search-inp').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }
        const nameCheckbox = $("#Name");
        const userCheckbox = $("#User");
        const inputValue = $('#search-inp').val();
        if (nameCheckbox.is(':checked')) {
            selectedValues.push(nameCheckbox.val() + inputValue);
        }
        if (userCheckbox.is(':checked')) {
            selectedValues.push(userCheckbox.val() + inputValue);
        }
        dataTable.ajax.reload(function (json) {

            if (json.recordsFiltered === 0) {
                $('.dataTables_empty').text('No matching records found');
            }
        })
    }, 500))
})

$(function () {
    $('.form-select-sm').select2({
        minimumResultsForSearch: Infinity
    });
});

//  GetWorkFlow
const getWorkFlow = async (dataArray) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + userPrivilegeURL.workFlowUrl,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {

                if (result?.data && Array.isArray(result.data) && result.data.length > 0) {

                    userList = result.data
                    $('#workflowList').empty()
                    let html = '';
                    let data = {}
                    userList.forEach((item) => {
                        data = {}
                        html += '<option value="' + item?.id + '">' + item?.name + '</option>'
                        data['id'] = item.id
                        data['name'] = item.name
                        workflowContainer.push(data)
                    })
                    $('#workflowList').append(html)
                }
                if (dataArray?.length > 0) {
                    $('#workflowList').val(dataArray).trigger('change')

                }
            } else {
                errorNotification(result)
                $('#workflowList').append('<option value="No Data"></option>')
            }
        },

    })
};

// GetProfile list
const getProfileList = async (dataArray) => {

    await $.ajax({
        type: "GET",
        url: RootUrl + userPrivilegeURL.ProfileListUrl,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                if (result?.data && Array.isArray(result.data) && result.data.length > 0) {
                    profileList = result.data
                    $('#privilegeProfileList').empty()
                    let html = '';
                    let data = {}
                    profileList.forEach((item) => {
                        data = {}
                        html += '<option value="' + item?.id + '">' + item?.name + '</option>'
                        data['id'] = item.id
                        data['name'] = item.name
                        profileContainer.push(data)
                    })
                    $('#privilegeProfileList').append(html)
                } if (dataArray?.length > 0) {
                    $('#privilegeProfileList').val(dataArray).trigger('change')
                }

            } else {
                errorNotification(result)
                $('#privilegeProfileList').append('<option value="No Data"></option>')
            }
        },

    })

};

//  GetUserList
const getUserList = async (userArray) => {

    await $.ajax({
        type: "GET",
        url: RootUrl + userPrivilegeURL.userListUrl,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                if (result?.data && Array.isArray(result.data) && result.data.length > 0) {

                    userList = result.data
                    $('#privilegeUserList').empty()
                    let html = '';
                    let data = {}
                    userList.map((item) => {
                        data = {}
                        html += '<option value="' + item?.id + '">' + item?.loginName + '</option>'
                        data['id'] = item.id
                        data['name'] = item.loginName
                        userContainer.push(data)
                    })
                    $('#privilegeUserList').append(html)
                }
                if (userArray?.length > 0) {
                    $('#privilegeUserList').val(userArray).trigger('change')
                }
            } else {
                errorNotification(result)
                $('#privilegeUserList').append('<option value="No Data"></option>')
            }
        },

    })
};

// UserGroupList
const UserGroupList = async (userArray) => {
    await $.ajax({
        type: "GET",
        url: RootUrl + userPrivilegeURL.userGroupListUrl,
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result) {

                if (Array.isArray(result) && result.length > 0) {
                    userGroup = result
                    $('#userGroupList').empty();
                    let html = '';
                    let data = {};
                    userGroup.forEach((item) => {
                        data = {};
                        html += '<option value="' + item?.id + '">' + item?.groupName + '</option>';
                        data['id'] = item.id;
                        data['name'] = item.groupName;
                        userGroupContainer.push(data);
                    });
                    $('#userGroupList').append(html);
                }
                if (userArray?.length > 0) {
                    $('#userGroupList').val(userArray).trigger('change');
                }
            } else {
                errorNotification(result);
                $('#userGroupList').append('<option value="No Data"></option>');
            }
        }
    });
};


$(function () {
    Radiobutton = $("#privilegeUserClick").val();
    $("input[name='inlineRadioOptions']").on('change', function () {
        //getUserList()
        //getWorkFlow ()
        Radiobutton = $("input[name='inlineRadioOptions']:checked").val();
        $("#userNameDropdown").toggle(Radiobutton === 'Users');
        $("#userGroupDropdown").toggle(Radiobutton === 'UserGroup');

        if (Radiobutton === 'Users') {
            $('#userError').text('').removeClass('field-validation-error');
            $("#userGroupList").val('').trigger('change');
        } else {
            $("#privilegeUserList").val('').trigger('change');
            $('#UserGroupError').text('').removeClass('field-validation-error');
        }
    });
});

//Update
$('#userPrivilegeTable').on('click', '.edit-button', function () {
    var userPrivilege = $(this).data('user');
    populateModalFields(userPrivilege);
    $('#btnSaveFunction').text('Update')
    $('#CreateModal').modal('show');
});

//delete
$('#userPrivilegeTable').on('click', '.delete-button', function () {
    var userId = $(this).data('user-id');
    var userName = $(this).data('user-name');
    $('#textDeleteId').val(userId);
    $('#deleteData').text(userName);
});


// Workflow list
$("#workflowList").on('change', function () {
    selectedDatas = []
    const value = $(this).val();
    selectedDatas.push(value);
    validateDropDown(value, ' Select Workflow list', 'workflowListError');
});

$("#privilegeProfileList").on('change', function () {
    profileData = [];
    const value = $(this).val();
    profileData.push(value);
    validateDropDown(value, ' Select workflow Profile list', 'profileError');

});

// User Role 
$("#userGroupList").on('change', function () {
    userGroupDatas = []
    const value = $(this).val();
    userGroupDatas.push(value);
    validateDropDown(value, ' Select user group ', 'UserGroupError');
});

// User list
$("#privilegeUserList").on('change', function () {
    userListData = [];
    const value = $(this).val();
    userListData.push(value);
    validateDropDown(value, ' Select User list', 'userError');
});


$('#txtDescription').on('input', async function (event) {
    const value = $(this).val();
    const sanitizedValue = value.replace(/^\s+/, '').replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    if (exceptThisSymbol.includes(event.key)) {
        event.preventDefault();
    }
    let errorElement = $('#DescriptionError');
    await validateDescription(sanitizedValue, "Should not allow more than 250 characters", errorElement);
});


async function validateDescription(value, errorMessage) {
    const errorElement = $('#DescriptionError');
    if (!value || value.length > 250) {
        errorElement.text(value ? errorMessage : '');
        errorElement.toggleClass('field-validation-error', !!value);
        return !value;
    } else {
        errorElement.text('');
        errorElement.removeClass('field-validation-error');
        return true;
    }
}


$(function () {
    $('input[name="switchPlan"]:checked').trigger('change');
    $('input[name="switchPlan"]').on('change', function (event) {
        accessTypeData = $('input[name="switchPlan"]:checked').val();
        if (accessTypeData == 'Temporary') {
            datecheck = true
        } else {
            datecheck = false
            $('#endDateError, #startDateError').text('').removeClass('field-validation-error');
            $('#startDateInput, #endDateInput').val('');
        }
    });

});

$(function () {
    $("#workflowListInput").hide();
    $("#profileInput").show();
    $("#Profile").prop("checked", true);
    $('input[name="type"]').on('click', function () {

        selectedValue = $(this).val();

        if (selectedValue === "Profile") {

            $('#profileError').text('').removeClass('field-validation-error');
            $("#workflowListInput").hide();
            $("#profileInput").show();
            $("#workflowList").val('').trigger('change')

        } else {
            $('#workflowListError').text('').removeClass('field-validation-error');
            $("#profileInput").hide();
            $("#workflowListInput").show();
            $("#privilegeProfileList").val('').trigger('change')
        }
    });
});

/// data and time ///
let date = [], formattedStartDate = '', formattedEndDate = '';
var startDateInput = document.getElementById("startDateInput");
startDateInput.addEventListener("change", function () {
    formatStartDateTime(startDateInput.value)
    validateDates()
});

var formattedEndDates = document.getElementById("endDateInput");
formattedEndDates.addEventListener("change", function () {

    formatEndDateTime(formattedEndDates.value)
    validateDates()

});
function formatStartDateTime(date) {

    var newValue = date
    var date = new Date(newValue);

    var day = date.getDate();
    var month = date.getMonth() + 1;
    var year = date.getFullYear();
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var seconds = date.getSeconds();


    day = day < 10 ? "0" + day : day;
    month = month < 10 ? "0" + month : month;
    hours = hours < 10 ? "0" + hours : hours;
    minutes = minutes < 10 ? "0" + minutes : minutes;
    seconds = seconds < 10 ? "0" + seconds : seconds;

    var amOrPm = hours < 12 ? "AM" : "PM";
    formattedStartDate = day + "/" + month + "/" + year + " " + hours + ":" + minutes + ":" + seconds + " " + amOrPm;
}
function formatEndDateTime(date) {

    var newValue = date
    var date = new Date(newValue);

    var day = date.getDate();
    var month = date.getMonth() + 1;
    var year = date.getFullYear();
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var seconds = date.getSeconds();

    day = day < 10 ? "0" + day : day;
    month = month < 10 ? "0" + month : month;
    hours = hours < 10 ? "0" + hours : hours;
    minutes = minutes < 10 ? "0" + minutes : minutes;
    seconds = seconds < 10 ? "0" + seconds : seconds;

    var amOrPm = hours < 12 ? "AM" : "PM";
    formattedEndDate = day + "/" + month + "/" + year + " " + hours + ":" + minutes + ":" + seconds + " " + amOrPm;
}

function validateDates() {
    const startDate = $("#startDateInput").val();
    const endDate = $("#endDateInput").val();
    const sdate = new Date(startDate);
    const edate = new Date(endDate);

    clearDateError()
    if (!startDate && !endDate) {
        showError("startDateError", "Select start date");
        showError("endDateError", "Select end date");
        return false;

    } else if (!startDate && endDate) {
        showError("startDateError", "Select start date");
        return false;
    }
    else if (sdate.getDate() == edate.getDate() && sdate.getMonth() + 1 == edate.getMonth() + 1 && sdate.getFullYear() == edate.getFullYear() && sdate.getHours() == edate.getHours() && sdate.getMinutes() == edate.getMinutes()) {

        showError("endDateError", "End data or time must be greater than start date or time");
        return false;
    }

    else if (startDate && !endDate) {
        showError("endDateError", "Select end date");
        return false;
    } else if (endDate < startDate) {
        showError("endDateError", "End date must be greater than start date");
        return false;
    }
    return true;
}

function clearInputs() {
    const errorElements = ['#workflowListError,#profileError,#userRoleError,#userError,#endDateError,#startDateError,#DescriptionError'];
    clearInputFields('userPrivilege', errorElements);
    datecheck = true
    selectedValue = '';
    globalUserPrevilageId = ''
    date = [];
    [profileDatas, profileData, profileContainer, savedData, selectedDatas, workflowContainer, userData, userListData,
        userContainer, userRoleData, userRoleDatas, userRoleContainer, userGroupContainer] = Array(13).fill([]);
    $('#btnSaveFunction').text('Save');
    $('#privilegeProfileList, #startDateInput, #endDateInput, #workflowList, #txtDescription').empty();
    $('#userError, #userRoleError, #profileError, #workflowListError').text('').removeClass('field-validation-error');

}

///// Save function 
$("#btnSaveFunction").on('click', async function () {
    var profile = $("#privilegeProfileList").val();
    var user = $("#privilegeUserList").val();
    elementDescription = $("#txtDescription").val()
    errorElement = $('#DescriptionError');
    var isDescription = await validateDescription(elementDescription, "Should not allow more than 250 characters", errorElement);
    var userGroup = $("#userGroupList").val();
    var workflow = $("#workflowList").val();
    var isProfile = await validateDropDown(profile, ' Select workflow profile list', 'profileError');
    var isUser = await validateDropDown(user, ' Select user list', 'userError');
    var isUserGroup = await validateDropDown(userGroup, ' Select user group', 'UserGroupError');
    var isworkflow = await validateDropDown(workflow, ' Select workflow list', 'workflowListError');
    var isStartDate = validateDates()

    if (($("#privilegeProfileList").is(":visible") ? isProfile : true) && ($("#userGroupDropdown").is(":visible") ? isUserGroup : true) && ($("#userNameDropdown").is(":visible") ? isUser : true) && ($("#workflowList").is(":visible") ? isworkflow : true) && isDescription && ($("#monthgroup").is(":visible") ? isStartDate : true)) {
        await savePermission()
    }
});

const savePermission = async () => {
    date.push({ startDate: formattedStartDate, endDate: formattedEndDate });
    let savedData = [],userData = [],userRoleData = [], userGroupData = [], profileDatas = [];

    let permissionData = {
        'AccessProperties': $("#privilegeProfileList").is(":visible")
            ? getStrJSON(profileDatas, profileData, profileContainer)
            : getStrJSON(savedData, selectedDatas, workflowContainer),
        'UserProperties': $("#userNameDropdown").is(":visible")
            ? getStrJSON(userData, userListData, userContainer)
            : ($("#UserRole_dropdown").is(":visible")
                ? getStrJSON(userRoleData, userRoleDatas, userRoleContainer)
                : getStrJSON(userGroupData, userGroupDatas, userGroupContainer)),
        'Description': $('#txtDescription').val(),
        'AccessType': accessTypeData,
        'ScheduleTime': datecheck ? JSON.stringify(date) : null,
        'UserAccess': Radiobutton,
        'Type': selectedValue,
        '__RequestVerificationToken': gettoken()
    };


    if (globalUserPrevilageId !== '') {
        permissionData['id'] = globalUserPrevilageId

    }

    await $.ajax({
        type: "POST",
        url: RootUrl + userPrivilegeURL.createUpdateUrl,
        data: permissionData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                $("#CreateModal").modal('hide');
                clearInputs();
                notificationAlert("success", result.data.message)
                setTimeout(() => {
                    window.location.reload();
                }, 2000);

            } else {
                errorNotification(result)

            }

        }
    })
}

function getStrJSON(savedData, selectedDatas, workflowContainer) {

    selectedDatas = selectedDatas.flat(1)
    if (selectedDatas.length > 0) {
        workflowContainer.forEach((item) => {
            if (selectedDatas.includes(item.id)) {
                savedData.push({ 'id': item.id, 'name': item.name })
            }
        })
    }
    return JSON.stringify(savedData)
}

$('#btnCreate').on('click', function () {
    getUserList();
    UserGroupList();
    getWorkFlow();
    getProfileList();
    $('input[name="switchPlan"]:checked').trigger('change');
    clearInputs();
    selectedValue = $("#Profile").val();
    Radiobutton = $("#privilegeUserClick").val();
    $("#Profile,#privilegeUserClick").prop("checked", true);
    $('#monthgroup').show();
    $('#userNameDropdown,#profileInput').show();
    $('#workflowListInput,#userGroupDropdown').hide();
});


//$('#Cancel').on('click', function () {
//    clearInputs();
//})
//$('#close').on('click', function () {
//    clearInputs();
//})


function validateDropDown(value, errormessage, errorElements) {
    if (value.length === 0) {
        $('#' + errorElements).text(errormessage).addClass('field-validation-error');
        return false;
    } else {
        $('#' + errorElements).text('').removeClass('field-validation-error');
        return true;
    }
}
function clearDateError() {
    $("#endDateError, #startDateError").attr('hidden', true).text("");
}

function showError(elementId, errorMessage) {
    document.getElementById(elementId).removeAttribute('hidden');
    $("#" + elementId).text(errorMessage).addClass('field-validation-error');;
}

function populateModalFields(userPrivilege) {

    globalUserPrevilageId = userPrivilege.id;
    accessTypeData = userPrivilege.accessType
    selectedValue = userPrivilege.type
    Radiobutton = userPrivilege.userAccess

    $("#txtDescription").val(userPrivilege.description).trigger('change');

    if (userPrivilege.scheduleTime) {
        let scheduleTime = JSON.parse(userPrivilege.scheduleTime);

        let startDateString = scheduleTime[0]?.startDate;
        var parts = startDateString.split(/[\/\s:]/);
        var date = new Date(parts[2], parts[1] - 1, parts[0], parts[3], parts[4]);
        var formattedStart = new Date(date.getTime() - (date.getTimezoneOffset() * 60000)).toISOString().slice(0, 16);
        formatStartDateTime(formattedStart)
        $("#startDateInput").val(formattedStart);

        let endDateString = scheduleTime[0]?.endDate;
        var parts = endDateString.split(/[\/\s:]/);
        var date = new Date(parts[2], parts[1] - 1, parts[0], parts[3], parts[4]);
        var formattedEnd = new Date(date.getTime() - (date.getTimezoneOffset() * 60000)).toISOString().slice(0, 16);
        formatEndDateTime(formattedEnd)
        $("#endDateInput").val(formattedEnd);
        validateDates();
    }

    let accessPropertie = JSON.parse(userPrivilege.accessProperties);
    let appendArray = []
    for (let i = 0; i < accessPropertie.length; i++) {

        appendArray.push(accessPropertie[i].id);
    }
    if (selectedValue == 'Profile') {
        getProfileList(appendArray);
    } else if (selectedValue == 'Workflow') {
        getWorkFlow(appendArray);
    }
    let userArray = []
    let userPropertie = JSON.parse(userPrivilege.userProperties);
    for (let i = 0; i < userPropertie.length; i++) {

        userArray.push(userPropertie[i].id);
    }
    if (Radiobutton == 'Users') {
        getUserList(userArray);
    } else if (Radiobutton == 'UserGroup') {
        UserGroupList(userArray)
    }

    // UserRoleList(userArray);   
    if (userPrivilege.type === "Workflow") {
        $("#workflowList").prop("checked", true);
        $("#profileInput").hide()
        $("#workflowListInput").show()
        $("#Profile").prop("checked", false);
    } else {
        $("#Profile").prop("checked", true);
        $("#profileInput").show();
        $("#workflowListInput").hide();
        $("#workflowList").prop("checked", false);
    }

    if (userPrivilege.userAccess === "Users") {
        $('#privilegeUserClick').prop("checked", true);
        $('#UserRoleclick, #userGroupClick').prop("checked", false);
        $('#userNameDropdown').show();
        $('#UserRole_dropdown, #userGroupDropdown').hide();
    } else {
        $('#privilegeUserClick, #UserRoleclick').prop("checked", false);
        $('#userGroupClick').prop("checked", true);
        $('#userNameDropdown, #UserRole_dropdown').hide();
        $('#userGroupDropdown').show();       
    }

    if (userPrivilege.accessType === "Temporary") {
        $('#switchMonthly').prop('checked', true).trigger('change');
        $('#monthgroup').show();
    } else {
        $('#switchYearly').prop('checked', true).trigger('change');
        $('#monthgroup').hide();
    }
    $('#startDateInput').val();
}
// start date
var datetimeInput = $('#startDateInput');

var minYear = new Date().getFullYear()
var maxYear = new Date().getFullYear() + 76;
var minDateString = minYear + "-01-01T00:00";
var maxDateString = maxYear + "-12-31T23:59";

datetimeInput.attr('min', minDateString);
datetimeInput.attr('max', maxDateString);

var today = new Date().toISOString().slice(0, 16);

document.getElementsByName("sdate")[0].min = today;
// end date
var datetimeInput = $('#endDateInput');

var minYear = new Date().getFullYear()
var maxYear = new Date().getFullYear() + 76;
var minDateString = minYear + "-01-01T00:00";
var maxDateString = maxYear + "-12-31T23:59";

datetimeInput.attr('min', minDateString);
datetimeInput.attr('max', maxDateString);

var today = new Date().toISOString().slice(0, 16);

document.getElementsByName("edate")[0].min = today;