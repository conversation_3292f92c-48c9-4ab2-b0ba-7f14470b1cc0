﻿
let comparePrimaryDisplay = $('#primaryCompareContainer')

const compareJson = () => {
    let leftBlockId = $('#secondManageConatiner').attr('id');
    let rightBlockId = $('#primaryManageConatiner').attr('id');
    $(document).jdd(leftBlockId, rightBlockId);
}
$('#btnVersionManage').on('click', async function () {
    btnDisableWhileClick()
    $('#workflowVersionType').empty()
    let data = {}
    data.workflowId = GlobalWorkflowId

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.GetWorkflowVersions,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (Array.isArray(result.data) && result.data.length > 0) {
                    let html = '';
                    versionDetails = result.data

                    result.data.forEach((item, index) => {
                        if (index !== 0) {
                            html += `<option value='${item.id}'>${item.version}</option>`
                        }
                    })
                    $('#workflowVersionType').append(html)
                    let actionProperty = JSON.parse(result.data[0].properties)
                    // let actionProperty1 = JSON.parse(result.data[1].properties)
                    let primaryId = $('#primaryCompareContainer')
                    // let secondaryId = $('#secondaryCompareContainer')
                    createCompareDiagram(actionProperty.nodes, primaryId)
                    // createCompareDiagram(actionProperty1.nodes, secondaryId)
                    //  $('#secondManageConatiner').text(result.data[0].properties)
                    //  $('#primaryManageConatiner').text(result.data[1].properties)
                    let primaryVersionHtml = `<div><span class="text-primary">${GlobalWorkflowName} </span><span class="fs-8"> v${result.data[0].version}</span><small> (latest)</small></div>`;
                    // let secondaryVersionHtml = `<div><span class="text-primary">${GlobalWorkflowName} </span><span class="fs-8"> v${result.data[1].version}</span></div>`
                    $('.versionComFirst').html(primaryVersionHtml)
                    // $('.versionComSecond').html(secondaryVersionHtml)

                    if (versionDetails.length > 1) {
                        setTimeout(() => {
                            //compareJson()
                            $('#CompareVersionModal').modal('show')
                        }, 300)
                        //setTimeout(() => {
                        //    if (versionDetails.length > 1) $('#workflowVersionType').val(versionDetails[1]?.id).trigger('change')
                        //}, 1000)
                    } else {
                        notificationAlert('warning', 'Need more than one version to compare')
                    }

                }
            } else {
                errorNotification(result)
            }
        }

    })
})

$('#workflowVersionType').on('change', function () {
    let id = $('#workflowVersionType').val()
    let data = versionDetails.filter((d) => d.id === id)
    let properties = JSON.parse(data[0].properties)
    let secondaryId = $('#secondaryCompareContainer')
    createCompareDiagram(properties.nodes, secondaryId)
    let versionContainer = `<div><span class="text-primary">${GlobalWorkflowName} </span><span class="fs-8"> v${data[0].version}</span></div>`
    $('.versionComSecond').html(versionContainer)

})

$('#WFRestoreConfirmation').on('click', function () {
    let id = $('#workflowVersionType').val()
    let data = versionDetails.filter((d) => d.id === id)
    let details = JSON.parse(data[0].properties)
    globalWorkflowArray = details.nodes
    totalEstimatedRto = details.estimatedrto
    $('#workflowRestoreData').text(GlobalWorkflowName + ' ' + data[0].version)
    updateMode = 'compare';
    $('#CompareVersionModal').modal('hide')
    $('#WorkflowComapareModal').modal('show')
})

$('#btnWFVersionRetore').on('click', function () {
    workflowSaveMode = 'Update'
    $('#CompareVersionModal').modal('hide')
    $('#WorkflowComapareModal').modal('hide')
    dataDisplay.empty()
    loadWorkFlow(globalWorkflowArray)
    $("#btnSaveWorkflow").trigger('click')

})

$('#btnVersionRestoreCancel').on('click', function () {
    $('#CompareVersionModal').modal('show')
    $('#WorkflowComapareModal').modal('hide')
})

const createCompareDiagram = (json, id) => {
    id.empty();
    let workflowDataLength = json.length;
    for (let i = 0; i < workflowDataLength; ++i) {
        setTimeout(async () => {
            conditionalCompareAppending(json[i], i, id)
        }, 40)
    }
}

const conditionalCompareAppending = (workflowValues, i, id) => {
    if (workflowValues.hasOwnProperty('groupName')) {
        let groupId = getRandomId('group')
        let accordionId = getRandomId("accordion")
        let parentId = getRandomId('parent')
        workflowValues.groupActions.forEach(function (obj, idx) {
            if (obj.hasOwnProperty('children')) {
                compareParellelGroup(obj, i, id)
            } else {
                appendDataForCompareParallel(obj, i, id)
            }
        })
        $("#groupName").val(workflowValues.groupName)
        $('.loadCompareSelected').parent().wrapAll("<div id=" + groupId + " groupName=" + workflowValues.groupName + " groupColor=" + workflowValues.groupColor + " class='parentGroup'>" + GenerateGroupName(workflowValues.groupName, workflowValues.groupColor, parentId) + "</div>")
        $('#' + groupId + ' .accordion-body').append($('.loadCompareSelected').parent())
        $('#' + groupId + ' .accordionClass').attr('id', accordionId)
        $('#' + groupId + ' .btnAccordion').attr('data-bs-target', '#' + accordionId)
        $('#' + groupId + ' .loadCompareSelected').before(connectImage)
        $('#' + groupId + ' .loadCompareSelected').first().prev().remove()
        $('#' + groupId + ' .groupClass').removeClass('loadCompareSelected')
        $('#' + groupId).wrapAll('<div class="ui-sortable-handle"></div>')
        $('#' + groupId).before(connectImage)
        $('#' + groupId + ' .workflowActions').removeClass('loadCompareSelected')
        $('#' + groupId + ' .groupCheckBox').addClass('d-none')
        $('.parallelCont').removeClass('loadCompareSelected')
    } else if (workflowValues.hasOwnProperty('children') || workflowValues.hasOwnProperty('parallelActions')) {
        let paralleActions = workflowValues.hasOwnProperty('children') ? workflowValues.children : workflowValues.parallelActions
        if (paralleActions.length && !paralleActions[0].actionInfo.hasOwnProperty('IsGroup')) {
            let ParallelId = getRandomId("parallel")
            paralleActions.forEach(function (obj, i) {
                appendDataForCompareParallel(obj, i, id);
            })
            $('.loadCompareSelected').unwrap()
            $('.loadCompareSelected').wrapAll("<div id=" + ParallelId + " class='parallelCont loadCompareSelected parallel-icon gap-2'></div>")
            $('#' + ParallelId).wrapAll('<div class="ui-sortable-handle"></div>')
            $('#' + ParallelId).parent().prepend(connectImage)
            $('#' + ParallelId).removeClass('loadCompareSelected')
            $(".workflowActions").removeClass("loadCompareSelected")
        }
    } else if (!workflowValues.actionInfo.hasOwnProperty('IsGroup')) {
        appendCompareData(workflowValues, i, id)
    }
}

function compareParellelGroup(newData, index, id) {
    let childLength = newData.children.length
    for (let i = 0; i < childLength; i++) {
        let encodedData = btoa(JSON.stringify(newData))
        let ColorData = $('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentColor')
        let iconData = `${$('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`

        ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;

        const newItemElement = `<div class='ui-sortable-handle'>
            <div class='workflowActions forGroupParallel loadCompareSelected' role="button" parentId='${newData.children[i]?.actionInfo?.parentActionId || ''}' id='${getRandomId('node')}' details='${encodedData}'>
                <i class='${iconData} workflowIcon' style='background: ${ColorData ?? '#3562AA'} '></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'>${index + 1}</span>
                    . 
                    <span class='actionSpan' title=${newData.children[i]?.actionInfo?.actionName ?? 'NA'}>${newData.children[i]?.actionInfo?.actionName ?? 'NA'}</span>
                </div>
               
            </div>
        </div>`;
        id.append(newItemElement);
    }

    let ParallelId = getRandomId("parallel")
    $('.forGroupParallel.loadCompareSelected').unwrap()
    $('.forGroupParallel.loadCompareSelected').wrapAll("<div id=" + ParallelId + " class='parallelCont loadCompareSelected parallel-icon gap-2'></div>")
    $('#' + ParallelId).wrapAll('<div class="ui-sortable-handle"></div>')
    $('#' + ParallelId + ' .workflowActions').removeClass('loadCompareSelected')
}

function appendDataForCompareParallel(newData, index, id) {

    let actionType = $('.nodeSummaryData[id=' + newData.actionInfo.actionType + ']').attr('actionType')
    if (!newData.actionInfo.hasOwnProperty('type')) newData.actionInfo.type = actionType || ''

    let encodedData = btoa(JSON.stringify(newData))
    let ColorData = $('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentColor')
    let iconData = `${$('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`
    // newData?.actionInfo?.uniqueId = getRandomId('node')
    ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;

    let backGroundColor = actionType && actionType.toLowerCase() === 'common' ? '#0e97ff' : actionType.toLowerCase() === 'operation' ? '#f30' : actionType.toLowerCase() === 'monitoring' ? '#009' : actionType.toLowerCase() === 'monitoring' ? '#f90' : '#08a200'

    const newItemElement = `
        <div class='ui-sortable-handle'>
            <div class='workflowActions loadCompareSelected' role="button" parentId='${newData?.actionInfo?.parentActionId || ''}' id='${getRandomId('node')}' details='${encodedData}'>
                <i class='${iconData} workflowIcon' style='color:white;background: ${backGroundColor}'></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'>${index + 1}</span>
                    . 
                    <span class='actionSpan' title=${newData?.actionInfo?.actionName}>${newData?.actionInfo?.actionName}</span>
                </div>
               
            </div>
        </div>`;

    id.append(newItemElement);
}

function appendCompareData(newData, index, id) {

    let encodedData = btoa(JSON.stringify(newData))
    let ColorData = $('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentColor')
    let iconData = `${$('.nodeSummaryData[nodeId=' + newData.actionInfo.nodeId + ']').attr('parentIcon') || 'cp-flow'} workflowTextClass circle fs-7`

    ColorData = ColorData === 'rgb(255,255,255)' ? '#3562AA' : ColorData;
    //newData?.actionInfo?.uniqueId = getRandomId('node')
    const newItemElement = `
        <div class='ui-sortable-handle'>
            <i class='cp-workflow-line fs-7'></i>
            <div class="workflowActions justify-content-between" role="button" parentId="${newData?.actionInfo?.parentActionId || ''}" id=${getRandomId('node')} details='${encodedData}'>
                <i class='${iconData} workflowIcon ${ColorData}' style='background: ${ColorData ?? '#3562AA'}'></i>
                <div class='flex-fill text-truncate mx-2'>
                    <span class='workflowIndex'>${index + 1}</span>
                    . 
                    <span class='actionSpan' title=${newData?.actionInfo?.actionName}>${newData?.actionInfo?.actionName}</span>
                </div>
               
            </div>
        </div>`;

    id.append(newItemElement);
}



// Template Load

const loadWorkflowTemplate = async () => {

    await $.ajax({
        type: "GET",
        url: RootUrl + Urls.getTemplateList,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                if (result && result.data && Array.isArray(result.data) && result.data.length) {
                    let templateData = '';
                    let sortedData = result?.data?.sort((a, b) => (a.replicationTypeName?.toLowerCase() || '').localeCompare(b.replicationTypeName?.toLowerCase() || ''));

                    sortedData?.length && sortedData?.forEach((d) => {
                        templateData += `<details>
                                <summary parentId="" id="" class="filterTitle categorysummary text-truncate" title="">
                                    <i class=""></i> ${d?.replicationTypeName}
                                </summary>
                                ${d?.templateListVm && loadTemplateToList(d?.templateListVm)}
                            </details>`
                    })           
                    $('#templateTree').append(templateData)
                } else {
                  
                }
            }
        }
    })
}

const loadTemplateToList = (listArray) => {
    let data = ''
    let sortedList = listArray?.length && listArray?.sort((a, b) => (a?.name?.toLowerCase() || '').localeCompare(b?.name?.toLowerCase() || ''))

    sortedList?.length && sortedList?.forEach((d) => {
        let type = d?.actionType || 'Custom'
        let encodedData = btoa(JSON.stringify(d))

        let icon = type === 'SwitchOver' ? 'cp-switch-over' : type === 'SwitchBack' ? 'cp-switch-back' : type === 'Monitoring' ? 'cp-monitoring' : type == 'Resiliency Readiness'
                       ? 'cp-resiliency-readiness' : type === 'Custom' ? 'cp-custom' : 'cp-workflow-templates'

        data += `<div class="d-grid ms-3">
        <span role="button" class="actiontype childWorkflowTemplate categorysummary nodeSummaryData text-truncate" draggable="true" ondragstart="drag(event)" actionType="" title=""
            nodeId="" parentId="" parentIcon="" parentColor="" data-template=${encodedData}>
           <i class="${icon} me-1"></i> ${d?.name}
        </span>
    </div>`
    })
    return data
}


loadWorkflowTemplate()

$('#confirmationTemplateGenerate').on('click', function (e) {
    e.preventDefault();

    getRestoreObj('templateActionData');

    if (actionRestoreValidation('templateActionData', 'restoreTemplate')) {

        $('#templateComponentModal').modal('hide')
        $('#RestoreAnimationModal').modal('show')

        let interval = setInterval(() => {
            let progressBarValue = +$('.restoreProgressBar').attr('aria-valuenow')
            progressBarStatus(progressBarValue, interval, 'templateCustom')
        }, 1700)

    }
})

$('#confirmationTemplateDiscard').on('click', function (e) {
    e.preventDefault()

    $('.templateActionData').empty()
    $('#templateComponentModal').modal('hide')
})

  //`  < details >
  //                              <summary class="categorysummary thirdChild text-truncate" id="" title="">
  //                                  <i class=""></i> hello
  //                              </summary>
  //                              <div class="d-grid ms-3">
  //                                  <span role="button" id="" class="actiontype categorysummary nodeSummaryData text-truncate" draggable="true" ondragstart="drag(event)" actionType="" title=""
  //                                      nodeId="" parentId="" parentIcon="" parentColor="">
  //                                      hello
  //                                  </span>
  //                              </div>
  //                          </details >`