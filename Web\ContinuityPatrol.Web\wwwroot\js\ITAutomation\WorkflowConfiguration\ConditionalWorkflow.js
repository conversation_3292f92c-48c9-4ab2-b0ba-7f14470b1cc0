﻿let targetConditionalId = '';

//function reset(canvasId) {
//    let canvas = document.getElementById(canvasId);
//    let context = canvas.getContext("2d");
//    context.clearRect(0, 0, canvas.width, canvas.height);
//    context.reset();
//    context.restore();
//}

$('#btnConditional').on('click', function () {
    let selected = $('.selectedWfContainer.workflowActions')
    targetConditionalId = selected[0].id
    conncetionString = getRandomId('condition')
    $('.selectedWfContainer').parent().after(`<div class="ui-sortable-handle" ><div class="parentConditionCont" id='${conncetionString}'><div>${connectImage}<div class="justify-content-center align-items-center d-flex flex-direction-row conditionalParent" ><span class="conditionalDot bgm-danger d-none" style="background-color:#D2042D"></span><div class=""><img class="conditionDiagram" src="/../img/genie_bot/WF_Condition.svg" height="50" width="50" /></div><span class="conditionalDot bgm-success d-none" style="background-color:#4F7942"></span></div></div></div></div>`)
    $('#filter_property').dropdown('hide')
    $('.workflowActions').removeClass("selectedWfContainer")
    $('.groupClass').removeClass("selectedWfContainer")
    $('.parallelCont').removeClass("selectedWfContainer")
    checkPositionChanged();
})


$('#btnGoToConditional').on('click', function () {
    $("select option[value='ifcondition'], select option[value='elsecondition']").attr('disabled', false);
    if (ConditionDetails.If) {
        $("select option[value='ifcondition']").attr('disabled', true);
    }
    if (ConditionDetails.Else) {
        $("select option[value='elsecondition']").attr('disabled', true);
    }
    $('#gotoActionList').empty();
    $('#failureCount').text('').val('')
    $('.GoToCheck').prop('checked', false)
    $('.GoToCheck[name=workflowActions]').prop('checked', true)
    $('#gotoActionCondition').val('selectCondition')
    $('#GoToConditionalModal').modal('show')
    $('#failCountConainer').hide()
    $('#workflowActionDropdown-error, #gotoDropdown-error, #failCount-error').text('').removeClass('field-validation-error')

    let html = '<option value="">Select action</option>'
    actionArray.forEach((obj, idx) => {
        if (obj && obj.type === "sequential" && obj.actionId !== targetConditionalId) {
            if ($(`#${obj.actionId}`).parents('.parallelCont').length > 0) {
                html += `<option value=${obj.actionId}>${idx + 1}. ${obj.actionName}(P)</option>`
            } else {
                html += `<option value=${obj.actionId}>${idx + 1}. ${obj.actionName}</option>`
            }

        }
    })
    $('#gotoActionList').append(html);
    $('#filter_property').dropdown('hide')
})

$('#failureCount').on('input keyup', function (event) {
    let inputVal = event?.target?.value;

    if (event.originalEvent.code === 'Minus' || event.originalEvent.code === 'ShiftRight' || event.originalEvent.code === 'Equal' || event.originalEvent.code === 'KeyE' || inputVal === '0') {
        $('#failureCount').text('').val('')
        return false;
    }

    failCountInputValidation($(this).val(), 'failCount-error', 'Enter failure count', event)
})

$(document).on('change', '#gotoActionCondition', function (e) {

    $('#failureCount').text('').val('')
    $('#gotoActionList').val('').trigger('change')
    $('#failCount-error, #workflowActionDropdown-error').text('').removeClass('field-validation-error')

    if ($('#gotoActionCondition').find(':selected').val() === 'elsecondition') {
        $('#failCountConainer').show()
    } else {
        $('#failCountConainer').hide()
    }
})

$(document).on('change', '.GoToCheck', function (e) {
    $('#gotoActionList').empty()
    let html = '';
    if ($(this).val() === 'workflowAction') {
        $('#gotoActionList').data('select2').selection.placeholder.text = 'Select Action'
        $('#gotoActionList').attr('data-placeholder', 'Select Action')
        $("#change_icon").removeClass("cp-group").addClass("cp-action-name");
        html = '<option value="">Select Action</option>';
        $('#workflowGroupId').prop('checked', false)
        actionArray.forEach((obj, idx) => {
            if (obj && obj.type === "sequential" && obj.actionId !== targetConditionalId) {
                html += `<option value=${obj.actionId}>${idx + 1}. ${obj.actionName}</option>`
            }
        })
    } else {
        $('#gotoActionList').data('select2').selection.placeholder.text = 'Select Group'
        $('#gotoActionList').attr('data-placeholder', 'Select Group')
        $("#change_icon").removeClass("cp-action-name").addClass("cp-group");
        html = '<option value="">Select Group</option>';
        $('#workflowActionId').prop('checked', false)
        groupArray.forEach((obj, idx) => {
            if (obj && obj.type === "group") {
                html += `<option value=${obj.groupId}>${obj.groupName}</option>`
            }

        })
    }
    $('#gotoActionList').append(html).trigger('change');
})

$('#btnCreateCondition').on('click', function () {
    let placeholder = $('#gotoActionList').attr('data-placeholder')
    placeholder = placeholder.toLowerCase().includes('action') ? 'action' : 'group'
    let isActionCount = true;
    let isCondition = dropDownValidations($('#gotoActionCondition').val(), 'gotoDropdown-error', 'Select condition')
    let isAction = dropDownValidations($('#gotoActionList').val(), 'workflowActionDropdown-error', `Select ${placeholder}`)

    if ($('#gotoActionCondition').val() === 'elsecondition') {
        isActionCount = failCountInputValidation($('#failureCount').val(), 'failCount-error', 'Enter failure count')
    }

    if (isCondition && isAction && isActionCount) {
        let flowLineId = getRandomId('flowLine')
        let endArrowId = getRandomId('arrow')
        //$('#workflowActions').prepend(`<canvas id=${CanvasId} class="workflowCanvas" connectionString=${lastGoToConditional} height='${$('#workflowActions').height()}' width='${$('#workflowActions').width()}'></canvas>`)

        let svgLine = `<svg id="${flowLineId}" class="workflowCanvas" connectionString=${lastGoToConditional} width="${$('#workflowActions').width()}" height="${$('#workflowActions').height()}" > <defs>
                        <marker id='${endArrowId}' markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="grey" />
                        </marker>
                      </defs></svg>`;

        $('#workflowActions').prepend(svgLine)



        // $('#workflowActions').prepend(`<svg id=${CanvasId} class="workflowCanvas"  height='${$('#workflowActions').height()}' width='${$('#workflowActions').width()}'></svg>`)

        setTimeout(() => {
            drawCanvasDiaram(flowLineId, lastGoToConditional, $('#gotoActionList').val(), $('#gotoActionCondition').val(), endArrowId)
        })
        $('#GoToConditionalModal').modal('hide')
        $('#filter_property').dropdown('hide')
        $(".checkSaveWorkflow").show()
    }
})


const checkPositionChanged = (connectId = '') => {
    let enable = $('.flowLineEnable')
    for (let i = 0; i < enable.length; i++) {
        let Id = enable[i].id
        let getFlowLineArray = JSON.parse($(`#${Id}`).attr('conditionArray'))

        getFlowLineArray.forEach((d) => {
            let flowLineId = d.flowLineId
            let latestConnectionStringId = d.conditionalConnectionId
            let conditionState = d.condition
            let endArrowId = $(`#${flowLineId} defs marker`)[0].id

            $(`#${flowLineId} polyline`).remove();
            drawCanvasDiaram(flowLineId, latestConnectionStringId, Id, conditionState, endArrowId, '', 'sort')
        })
    }
}

function randomNumber(min, max) {
    return Math.random() * (max - min) + min;
}

const drawCanvasDiaram = (flowLineId, latestConnectionStringId, gotoAction, conditionState, endArrowId, count = '', mode = '') => {
    let conditionPositionX = 0;
    let conditionPositionY = 0;
    let gotoActionPositionX = 0;
    let gotoActionPositionY = 0;
    let conditionSuccessDangerX = 0;
    let textPositionX = 0;
    let textPositionY = 0;
    let imagePositionX = 0
    let ConditionalText = 'Success';
    $(`#${flowLineId}`).attr('height', $('#workflowActions').height()).attr('width', $('#workflowActions').width())
    //const c = document.getElementById(CanvasId);
    //const ctx = c.getContext("2d");
    let color = "#4F7942";
    //ctx.strokeStyle = "#4F7942";
    //let image = new Image();

    if (conditionState === 'elsecondition') {
        color = '#D2042D';
    }
    if ($(`#${gotoAction}`).parents('.parallelCont').length > 0) {
        gotoAction = $(`#${gotoAction}`).parents('.parallelCont')[0].id
    }
    if ($(`#${latestConnectionStringId}`).parent().next().find('.workflowActions').length > 0) {
        let id = $(`#${latestConnectionStringId}`).parent().next().find('.workflowActions')[0].id
        let conditionalSuccessDot = $('#' + latestConnectionStringId + ' .conditionalDot.bgm-success')
        let conditionalDangerDot = $('#' + latestConnectionStringId + ' .conditionalDot.bgm-danger')
        if (id === gotoAction) {
            if (conditionState === 'ifcondition') {
                $(`#${id}`).parent().find('.cp-workflow-line').addClass('text-success')
                $(`#${latestConnectionStringId} .conditionalDot.bgm-success`).addClass('d-none')
            } else {
                $(`#${id}`).parent().find('.cp-workflow-line').addClass('text-danger')
                $(`#${latestConnectionStringId} .conditionalDot.bgm-danger`).addClass('d-none')
            }

            //ctx.fillText('Success', $(`#${gotoAction}`).position().left + ($(`#${gotoAction}`)[0].offsetWidth/2) + 10, $(`#${gotoAction}`).position().top - 20 + ($(`#${gotoAction}`)[0].offsetHeight / 2));
        } else {
            if (conditionState === 'ifcondition') {
                if (conditionalSuccessDot.hasClass('d-none')) {
                    conditionalSuccessDot.removeClass('d-none')
                    //$('.conditionDaigram').addClass('ms-1')
                }
            }
            if (conditionState === 'elsecondition') {
                if (conditionalDangerDot.hasClass('d-none')) {
                    conditionalDangerDot.removeClass('d-none')
                }
            }

            if (conditionState === 'ifcondition') {
                let randomnumber = randomNumber(83, 95)
                let rightWidth = $('#workflowActions').width() * randomnumber / 100 - conditionalSuccessDot.position().left + conditionalSuccessDot.width();
                conditionPositionX = conditionalSuccessDot.position().left + conditionalSuccessDot.width()
                conditionPositionY = conditionalSuccessDot.position().top + (conditionalSuccessDot.height() / 2)
                conditionSuccessDangerX = conditionPositionX + rightWidth
                gotoActionPositionX = $(`#${gotoAction}`).position().left + $(`#${gotoAction}`)[0].offsetWidth
                gotoActionPositionY = $(`#${gotoAction}`).position().top + ($(`#${gotoAction}`)[0].offsetHeight / 2)
                textPositionX = (conditionPositionX) + rightWidth / 2
                textPositionY = conditionalSuccessDot.position().top + 20
                ConditionalText = 'Success'
                //image.src = '/../img/genie_bot/LeftArrow.svg';
                //imagePositionX = gotoActionPositionX - 3
            } else {
                let randomnumber = randomNumber(83, 95)
                let rightWidth = $('#workflowActions').width() * randomnumber / 100 - conditionalDangerDot.position().left + conditionalDangerDot.width() - $('.conditionDiagram').width();
                conditionPositionX = conditionalDangerDot.position().left
                conditionPositionY = conditionalDangerDot.position().top + (conditionalDangerDot.height() / 2)
                conditionSuccessDangerX = conditionPositionX - rightWidth
                gotoActionPositionX = $(`#${gotoAction}`).position().left
                gotoActionPositionY = $(`#${gotoAction}`).position().top + ($(`#${gotoAction}`)[0].offsetHeight / 2)
                textPositionX = conditionPositionX - (rightWidth / 2);
                textPositionY = conditionalDangerDot.position().top + 20;
                ConditionalText = 'Failure'
                //image.src = '/../img/genie_bot/RightArrow.svg';
                //imagePositionX = gotoActionPositionX - 15
            }

            let connectionPoints = [];
            connectionPoints.push([conditionPositionX, conditionPositionY])
            connectionPoints.push([conditionSuccessDangerX, conditionPositionY])
            connectionPoints.push([conditionSuccessDangerX, gotoActionPositionY])
            connectionPoints.push([gotoActionPositionX, gotoActionPositionY])


            //ctx.moveTo(conditionPositionX, conditionPositionY)
            //ctx.setLineDash([3, 4]);
            //ctx.lineTo(conditionSuccessDangerX, conditionPositionY)          
            //ctx.lineTo(conditionSuccessDangerX, gotoActionPositionY);
            //ctx.lineTo(gotoActionPositionX, gotoActionPositionY);                  
            // ctx.font = "9px Arial";
            //ctx.fillText(ConditionalText, textPositionX, textPositionY);

            //image.onload = function () {
            //    ctx.drawImage(image, imagePositionX, gotoActionPositionY - 8);
            //}

            createPolyLine(flowLineId, connectionPoints, color, endArrowId)

        }
    }

    //ctx.stroke();
    if (!$(`#${gotoAction}`).hasClass('flowLineEnable')) {
        $(`#${gotoAction}`).addClass('flowLineEnable')
    }
    if ($(`#${latestConnectionStringId}`).attr('conditionDetails') === undefined) {
        let arr;
        arr = [{ 'condition': conditionState, 'gotoAction': gotoAction }]
        if (conditionState === 'elsecondition') {
            arr[0]['failureCount'] = count.length ? count : $('#failureCount').val();
        }

        $(`#${latestConnectionStringId}`).attr('conditionDetails', JSON.stringify(arr))
    } else {
        let getArr = JSON.parse($(`#${latestConnectionStringId}`).attr('conditionDetails'))
        if (getArr[0].condition !== conditionState) {
            if (getArr.length < 2) {
                if (conditionState === 'elsecondition') {
                    getArr.push({ 'condition': conditionState, 'gotoAction': gotoAction, 'failureCount': count.length ? count : $('#failureCount').val() })
                } else {
                    getArr.push({ 'condition': conditionState, 'gotoAction': gotoAction })
                }
                $(`#${latestConnectionStringId}`).attr('conditionDetails', JSON.stringify(getArr))
            }
        }
    }


    if (mode !== 'sort') {
        if ($(`#${gotoAction}`).attr('conditionArray')) {
            let details = { 'condition': conditionState, 'flowLineId': flowLineId, 'conditionalConnectionId': latestConnectionStringId }
            let getDetails = JSON.parse($(`#${gotoAction}`).attr('conditionArray'))
            getDetails.push(details)
            $(`#${gotoAction}`).attr('conditionArray', JSON.stringify(getDetails))
        } else {
            let details = [{ 'condition': conditionState, 'flowLineId': flowLineId, 'conditionalConnectionId': latestConnectionStringId }]
            $(`#${gotoAction}`).attr('conditionArray', JSON.stringify(details))
        }
    }



    //$(`#${gotoAction}`).attr('flowLineId', flowLineId)
    //$(`#${gotoAction}`).attr('conditionalConnectionId', latestConnectionStringId)
}

const createPolyLine = (lineId, points, color, endArrowId) => {
    const polyline = document.createElementNS("http://www.w3.org/2000/svg", "polyline");
    color = color || 'blue'
    $(polyline).attr({
        points: points.join(" "),
        fill: "none",
        stroke: color,
        "stroke-width": 1,
        "stroke-dasharray": "4, 4",
        "marker-end": `url(#${endArrowId})`,
    });

    $(`#${lineId}`).append(polyline);
    $(`#${lineId} path`).attr('fill', color);
}

$('#removeConditional').on('click', function () {

    if ($(`#${lastGoToConditional}`).attr('conditiondetails')?.length) {
        let getConditionDetails = JSON.parse($(`#${lastGoToConditional}`).attr('conditiondetails'))
        getConditionDetails.forEach((d) => {
            let conditionArray = [];
            let targetActionId = d.gotoAction
            let getFlowLine = $(`#${targetActionId}`).attr('conditionArray')
            if (getFlowLine?.length) {
                let flowLineDetails = JSON.parse(getFlowLine)
                flowLineDetails.forEach((x) => {
                    let flowLineId = x.flowLineId
                    if (lastGoToConditional === x.conditionalConnectionId) {
                        if (x?.condition == 'ifcondition') {
                            $(`#${targetActionId}`)?.siblings('.cp-workflow-line')?.removeClass('text-success')
                        } else {
                            $(`#${targetActionId}`)?.siblings('.cp-workflow-line')?.removeClass('text-danger')

                        }
                        $(`#${flowLineId}`).remove()
                    } else {
                        conditionArray.push(x)
                    }
                })
            }
            if (conditionArray.length) {
                $(`#${targetActionId}`).attr('conditionArray', JSON.stringify(conditionArray))
            } else {
                $(`#${targetActionId}`).removeAttr('conditionArray')
                $(`#${targetActionId}`).removeClass('flowLineEnable')
            }

        })
    }
    $(`#${lastGoToConditional}`).parent().remove()
    $('.workflowActions').removeClass("selectedWfContainer")
    $('#filter_property').dropdown('hide')
    setTimeout(() => {
        checkPositionChanged()
    })

})

$('#removeIfConditional').on('click', function () {
    if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
        let conditionData = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
        let filterCondition = conditionData.filter(x => x.condition === 'ifcondition')
        let goToId = filterCondition[0].gotoAction
        let getSvgDetails = JSON.parse($('#' + goToId).attr('conditionArray'))
        let filterdGoToCondition = getSvgDetails.filter((d) => d.condition === 'ifcondition')
        let getSvg = filterdGoToCondition[0].flowLineId
        $('#' + getSvg).remove()
        let filterSvg = conditionData.filter(x => x.condition !== 'ifcondition')
        if (filterSvg.length > 0) {
            $(`#${lastGoToConditional}`).attr('conditionDetails', JSON.stringify(filterSvg))

        } else {
            $(`#${lastGoToConditional}`).removeAttr('conditionDetails')
        }

        let filterSvgTarget = getSvgDetails.filter(x => x.condition !== 'ifcondition')
        if (filterSvgTarget.length) {
            $(`#${goToId}`).attr('conditionArray', JSON.stringify(filterSvgTarget))
        } else {
            $(`#${goToId}`).removeAttr('conditionArray')
            $('#' + goToId).removeClass('canvasEnable');
        }

        $(`#${lastGoToConditional} .conditionalDot.bgm-success`).addClass('d-none')
        $(`#${goToId}`)?.siblings('.cp-workflow-line')?.removeClass('text-success');
        $('#filter_property').dropdown('hide')
        //removeAttribute(goToId)
    }
})

//const removeAttribute = (goToId) => {
//    $('#' + goToId).removeClass('canvasEnable');
//    $('#' + goToId).removeAttr('canvasId')
//    $('#' + goToId).removeAttr('condition')
//    $('#' + goToId).removeAttr('conditionalconnectionid')

//}

$('#removeElseConditional').on('click', function () {
    if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
        let conditionData = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
        let filterCondition = conditionData.filter(x => x.condition === 'elsecondition')
        let goToId = filterCondition[0].gotoAction


        let getSvgDetails = JSON.parse($('#' + goToId).attr('conditionArray'))
        let filterdGoToCondition = getSvgDetails.filter((d) => d.condition === 'elsecondition')
        let getSvg = filterdGoToCondition[0].flowLineId
        $('#' + getSvg).remove()
        let filterSvg = conditionData.filter(x => x.condition !== 'elsecondition')
        if (filterSvg.length > 0) {
            $(`#${lastGoToConditional}`).attr('conditionDetails', JSON.stringify(filterSvg))

        } else {
            $(`#${lastGoToConditional}`).removeAttr('conditionDetails')
        }

        let filterSvgTarget = getSvgDetails.filter(x => x.condition !== 'elsecondition')
        if (filterSvgTarget.length) {
            $(`#${goToId}`).attr('conditionArray', JSON.stringify(filterSvgTarget))
        } else {
            $(`#${goToId}`).removeAttr('conditionArray')
            $('#' + goToId).removeClass('canvasEnable');
        }



        //  let getCanvas = $('#' + goToId).attr('canvasId')
        //$('#' + getCanvas).remove()
        //let filterCanvas = conditionData.filter(x => x.condition !== 'elsecondition')
        //if (filterCanvas.length > 0) {
        //    $(`#${lastGoToConditional}`).attr('conditionDetails', JSON.stringify(filterCanvas))
        //} else {
        //    $(`#${lastGoToConditional}`).removeAttr('conditionDetails')
        //}
        $(`#${lastGoToConditional} .conditionalDot.bgm-danger`).addClass('d-none')
        $('.cp-workflow-line').removeClass('text-danger');
        $('#filter_property').dropdown('hide')
        // removeAttribute(goToId)
    }
})

const getLoadCondition = () => {
    if (conditionalArray && conditionalArray.length) {
        conditionalArray.forEach((d) => {
            let goToAction = d.actionId
            let connectionString = getRandomId('condition')
            $(`#${goToAction}`).parent().after(`<div class="ui-sortable-handle" ><div class="parentConditionCont" id='${connectionString}'><div>${connectImage}<div class="justify-content-center align-items-center d-flex flex-direction-row conditionalParent" ><span class="conditionalDot bgm-danger d-none" style="background-color:#D2042D"></span><div class=""><img class="conditionDiagram" src="/../img/genie_bot/WF_Condition.svg" height="50" width="50" /></div><span class="conditionalDot bgm-success d-none" style="background-color:#4F7942"></span></div></div></div></div>`)
            $('#filter_property').dropdown('hide');
            checkPositionChanged();
            d.condionDetail.forEach((h) => {
                let flowLineId = getRandomId('flowLine')
                let endArrowId = getRandomId('arrow')
                let count = h?.failureCount || ''
                //$('#workflowActions').prepend(`<canvas id=${CanvasId} class="workflowCanvas" connectionString=${lastGoToConditional} height='${$('#workflowActions').height()}' width='${$('#workflowActions').width()}'></canvas>`)

                let svgLine = `<svg id="${flowLineId}" class="workflowCanvas" connectionString='${connectionString}' width="${$('#workflowActions').width()}" height="${$('#workflowActions').height()}" > <defs>
                        <marker id='${endArrowId}' markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="grey" />
                        </marker>
                      </defs></svg>`;

                $('#workflowActions').prepend(svgLine)

                let goToActionId = h.gotoAction;
                if ($('#' + h.gotoAction).parents('.parallelCont').length > 0) {
                    goToActionId = $('#' + h.gotoAction).parents('.parallelCont')[0].id
                }
                drawCanvasDiaram(flowLineId, connectionString, goToActionId, h.condition, endArrowId, count, 'load')
            })
        })
    }
}

const checkConditionDiagramExist = () => {
    let isExist = false
    $('.workflowActions.selectedWfContainer').not(':last').each(function (idx, obj) {
        if ($(this).parents('.ui-sortable-handle').next().find('.conditionDiagram').length > 0) isExist = true;
    })
    return isExist
}

//  -------   Conditional Modal Validations  -------   //

function dropDownValidations(value, errorId, text) {
    const errorElement = $('#' + errorId)
    if (!value) {
        errorElement.text(text)
            .addClass('field-validation-error')
        return false;
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return true;
    }
}

$('#gotoActionCondition').on('change', function () {
    dropDownValidations($(this).val(), 'gotoDropdown-error', 'Select condition')
})

$('#gotoActionList').on('change', function () {
    let placeholder = $('#gotoActionList').attr('data-placeholder')
    placeholder = placeholder.toLowerCase().includes('action') ? 'action' : 'group'
    dropDownValidations($(this).val(), 'workflowActionDropdown-error', `Select ${placeholder}`)
})


const failCountInputValidation = (value, errorId, text, event) => {
    //else if (event) {
    //if (event?.type === 'keyup') {
    //    if (event.which < 48 || event.which > 57) {
    //        event.preventDefault();
    //        return false;
    //    }
    //}

    const errorElement = $('#' + errorId)
    if (!value) {
        errorElement.text(text)
            .addClass('field-validation-error')
        return false;
    } else {
        const numericValue = parseInt(value, 10);
        if (isNaN(numericValue) || numericValue < 1) {
            $(this).val('');
            return false;
        } else if (value.length >= 2 && event) {
            event.preventDefault();
            return false;
        } else if (value?.toLowerCase().includes('e')) {
            $('#failureCount').val('').text('')
            return false
        } else {
            errorElement.removeClass('field-validation-error').text('');
            return true;
        }
    }
}
