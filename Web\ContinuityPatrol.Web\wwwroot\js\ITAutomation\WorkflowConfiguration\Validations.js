﻿//////////// Open modal valiadtion ////////////////

var globalExtractArray = [];
var globalExtractData = false;

const formValidation = (newObj) => {
    const numbersOnlyRegex = /^[0-9]+$/;
    let formIsValid = true;

    if (newObj?.actionName === '' || !newObj.hasOwnProperty('actionName')) {
        actionInfoValidation(newObj.actionName, 'actionName-error', 'Enter action name')
        formIsValid = false;
    } else if (newObj?.actionName) {
        formIsValid = actionInfoValidation(newObj.actionName, 'actionName-error', '')
    }

    if (newObj.rto === '' || !newObj.hasOwnProperty('rto')) {
        actionInfoValidation(newObj.rto, 'rto-error', 'Enter RTO')
        formIsValid = false;
    } else if (!numbersOnlyRegex.test(newObj.rto)) {
        actionInfoValidation(newObj.rto, 'rto-error', 'Enter only numbers')
        formIsValid = false;
    } else if (newObj.rto.length > 3) {
        $('#rto-error').text('Only 3 numbers are allowed')
            .addClass('field-validation-error');

        formIsValid = false;
    }

    if (newObj?.description.includes('<')) {
        $('#description-error').text('Special characters not allowed')
            .addClass('field-validation-error');

        formIsValid = false;
    }

    if (newObj.actionType === '' || !newObj.hasOwnProperty('actionType') || !newObj.actionType) {
        actionInfoValidation(newObj.actionType, 'actionType-error', 'Select action category')
        formIsValid = false;
    }
    return formIsValid;
}

const actionFormValidation = (newObj) => {
    let formIsValid = true
    let isTrueFound = [];

    if (newObj) {
        let properties = newObj?.actionInfo?.properties ? newObj?.actionInfo?.properties : newObj;
        let checkScriptType = $('select[name="scriptType"]')?.val()?.toLowerCase() === 'cpsl';

        for (const key in properties) {
            if (properties.hasOwnProperty(key) && properties[key] === "" && key !== undefined) {
                const errorText = $(`[data-id="${key}-error"]`).data('error');
                const checkRequired = $(`[data-id="${key}-error"]`)?.data('required') ?? true

                if (checkRequired) {
                    actionFormDataValidation(properties[key], `${key}-error`, errorText)
                    formIsValid = false;
                }
            } else {
                const $input = $(`[name="${key}"]`);
                const inputType = $input.attr('type')

                const checkIsIp = $input?.length && $input?.attr('isipaddress') &&
                    $input?.attr('isipaddress')?.toLowerCase() == 'ipaddressfield'

                if ($input && $input.is('input') && inputType == 'text') {
                    if (properties[key].includes('<')) {
                        actionFormDataValidation('', `${key}-error`, 'Special characters not allowed')
                        formIsValid = false;
                    } else if (checkIsIp) {
                        const validationResults = IpaddressReg(properties[key]);
                        formIsValid = validationResults && typeof (validationResults) == 'boolean' ? validationResults : false;

                        if (!formIsValid) $(`[data-id='${key}-error']`).text(validationResults).addClass('field-validation-error');
                    } else {
                        isTrueFound = actionFormDataValidation(properties[key], `${key}-error`, '', inputType, isTrueFound)
                    }
                }

            }
        }

        if (checkScriptType && !$('#cpslTextArea').is(':visible')) {
            formIsValid = false
            $('#scriptParent')?.parent()?.find('span:not(.input-group-text)')?.text('check script and convert to cp#').removeAttr('style').addClass('field-validation-error');

        }

        if (isTrueFound?.includes(false)) formIsValid = false

    }
    return formIsValid;
}

const actionRestoreValidation = (parentId, id) => {
    let formIsValid = true
    $("." + parentId).find("." + id).each(function () {
        let $select = $(this);
        let inputId = $select.attr("id");
        let inputType = $select.attr("type");
        let inputValue = '';

        if ($select.is('select')) {
            let selectedOption = $select[0].selectize.options[$select.val()];

            let filteredData = restoreFieldObj.length && restoreFieldObj.filter((obj) => obj.id == inputId)

            if (!selectedOption && filteredData.length) {

                const errorText = $(`[data-id="${inputId}-error"]`).data('error');
                actionFormDataValidation(inputValue, `${inputId}-error`, errorText)
                formIsValid = false;
            } else {
                $(`[data-id="${inputId}-error"]`).text('').removeClass('field-validation-error')
            }
        } else if (inputType === 'text' || inputType === 'password') {
            inputValue = $select.val();

            if (inputValue === '') {
                const errorText = $(`[data-id="${inputId}-error"]`).data('error');
                actionFormDataValidation(inputValue, `${inputId}-error`, errorText)
                formIsValid = false;
            } 
        }
    });
    return formIsValid;
}

function handleActionChange(e, errorId = '', text = '') {
    let hasExpo = false;
    if (e.target.name == 'actionName') {
        let sanitizedValue = e.target.value.replace(/\s{2,}/g, ' ');
        e.target.value = sanitizedValue
        actionInfoValidation(e?.target?.value, 'actionName-error', '');
    }

    if (errorId === 'rto-error' && e?.data || errorId === 'rto-error' && e.target.value) {
        if (e?.target?.value?.length > 5) {
            e.preventDefault();
            let sanitizedValue = e.target.value.slice(0, -1);
            $('#rto').val(sanitizedValue);
            return false;
        }

        if (e?.data === 'E') {
            $('#rto').val('').text('')
            return false;
        }
        if (Number(e.target.value) <= 0) {
            $('#rto').val('1').text('1')
            return false;
        }
        if (e?.data?.toLowerCase().includes('e') || hasExpo) {
            actionInfoValidation('e', errorId, 'Exponential not allowed');
            hasExpo = true;
        } else {
            actionInfoValidation(e.target.value, errorId, text);
            if (e.target.value) {
                hasExpo = false;
            }
        }

    } else if (e.target.name == 'description') {
        if (e.target.value.includes('<')) {
            actionInfoValidation('', errorId, 'Special characters not allowed');
        } else {
            $(`#${errorId}`).text('').removeClass('field-validation-error')
        }
    }
    //else if (e?.target?.name == 'email' || e?.target?.name == 'sms') {
    // let otherValue = e.target.name == 'email' ? 'sms' : 'email';

    // if (e?.target?.checked || $(`#${otherValue}`)?.prop('checked')) {
    //     $('#userDiv').removeClass('d-none')
    // } else {
    //     $('#userDiv').addClass('d-none')
    // $('#userList').val('')
    // }

    //}
    else {
        actionInfoValidation(e.target.value, errorId, text);
    }
}

// text area auto resizer
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = (textarea.scrollHeight) + 'px';
}

function handleFormDataChange(e, errorId, mode = '', restoreLabel = '') {
    let selectedValue = e.target.value;
    var selectedId = e.target.id;
    var type = e.target.type;
    let inputName = e.target.name
    let fields = Object.keys(formFields)

    let $select = $(e.target);


    if (type === 'textarea' && e?.target?.style?.fontWeight == '') {
        if (selectedId !== 'cpslTextArea') autoResizeTextarea(e.target);

        let id = $select[0]?.id

        if (id && inputName?.toLowerCase() == '@@cpslscript') {
            let getParent = $('#scriptParent')

            if (getParent && getParent?.length) {

                $('#scriptCheck')?.text('Validate Script')
                $('#cpslConvert, #cpslLabel')?.addClass('d-none')
                $('#cpslTextArea')?.val('')?.parent()?.parent()?.addClass('d-none')
            }
        }
    }

    if ($select.is('select')) {
        let selectizeInstance = $select[0].selectize;
        let selectedOption = selectizeInstance?.options && selectizeInstance.options[selectedValue];
        if (selectedOption) {
            let inputValue = selectedOption.value;
            let inputServerType = selectedOption.servertype;
            if (inputServerType === 'workflow') {
                loadWorflowAction(inputValue, '')
            }
            const isServerName = inputName?.toLowerCase() === '@@servername';
            const isPRServerName = inputName?.toLowerCase() === '@@prservername' || inputName?.toLowerCase()?.includes('@@masterserver');
            const isDRServerName = inputName?.toLowerCase() === '@@drservername' || inputName?.toLowerCase()?.includes('@@slaveserver');
            const isSourceServer = (restoreLabel && restoreLabel?.includes('Source Server')) || inputName?.toLowerCase() === '@@sourceserver';
            const isNotRestoreMode = mode !== 'restore' && (isServerName || isPRServerName);
            const isRestoreModeWithSourceServer = (mode === 'restore' && restoreLabel) && isDRServerName && isSourceServer;

            if (inputName?.toLowerCase() === 'scripttype') {

                let cpslElement = $('#scriptParent')?.parent()?.find('textarea')
                let cpSharpElement = $('#cpslTextArea');

                if (cpslElement.length && cpSharpElement.length) {
                    let cpslName = cpslElement.attr('name');
                    let cpSharpName = cpSharpElement.attr('name');

                    if (inputValue?.toLowerCase() == 'cp#' && cpslName === '@@cpslscript') {
                        cpslElement.attr('name', cpSharpName).val('').trigger('input');
                        cpSharpElement.attr('name', cpslName).val('').trigger('input');

                        // autoResizeTextarea(cpSharpElement[0])
                        $('#scriptCheck')?.text('Validate CP#')

                    } else if (inputValue?.toLowerCase() == 'cpsl' && cpslName !== '@@cpslscript') {
                        cpslElement.attr('name', cpSharpName).val('').trigger('input');
                        cpSharpElement.attr('name', cpslName).val('').trigger('input');

                        // autoResizeTextarea(cpslElement[0])
                        $('#scriptCheck')?.text('Validate Script')
                    }

                    $('#cpslConvert, #cpslLabel')?.addClass('d-none')
                    $('#cpslTextArea')?.val('')?.parent()?.parent()?.addClass('d-none')
                    $(`[data-id= "${cpSharpName}-error"], [data-id="${cpslName}-error"]`).text('').removeClass('field-validation-error')
                }
            }

            if (isNotRestoreMode || isRestoreModeWithSourceServer || isSourceServer) {
                let filteredData
                if (mode === 'restore') {
                    let restLabel = restoreLabel?.toLowerCase()?.split(' ')

                    filteredData = restoreFieldObj.filter((obj) =>
                    (obj?.name && (['@@dbname', '@@prdatabase', '@@sourcedatabase', '@@prdbname', '@@drdbname', '@@masterdatabase']?.includes(obj?.name?.toLowerCase()) ||
                        ((inputName?.toLowerCase() == '@@slaveserver' || inputName?.toLowerCase() == '@@slaveserver1') && (['@@slavedatabase', '@@slavedatabase1']?.includes(obj?.name?.toLowerCase()))) ||
                        (inputName?.toLowerCase() == '@@slaveserver2' && obj?.name?.toLowerCase() == '@@slavedatabase2')) && restLabel?.includes('source') &&
                        (restLabel[2] ? obj.label?.toLowerCase()?.includes(`source database ${restLabel[2]}`) :
                            obj?.label?.toLowerCase()?.includes(`source database`))));

                } else {
                    filteredData = fields.filter((fieldKey) =>
                    (formFields[fieldKey]?.attrs?.name &&
                        ['@@dbname', '@@prdatabase', '@@prdbname', '@@sourcedatabase', '@@masterdatabase']?.includes(formFields[fieldKey]?.attrs?.name?.toLowerCase())));
                }
                loadDatabaseByServerId(filteredData, selectedValue, mode)
            } else if (inputName && (inputName?.toLowerCase() == '@@drservername' || inputName?.toLowerCase() == '@@targetserver' || inputName?.toLowerCase()?.includes('@@slaveserver') || (restoreLabel && restoreLabel?.includes('Target Server')))) {
                let filteredData
                if (mode === 'restore') {
                    let restLabel = restoreLabel?.toLowerCase()?.split(' ')

                    filteredData = restoreFieldObj.filter((obj) =>
                    (obj?.name && (['@@drdbname', 'drdatabase', '@@dbname', '@@prdbname', '@@targetdatabase']?.includes(obj?.name?.toLowerCase()) ||
                        ((inputName?.toLowerCase() == '@@slaveserver' || inputName?.toLowerCase() == '@@slaveserver1') && (['@@slavedatabase', '@@slavedatabase1']?.includes(obj?.name?.toLowerCase()))) ||
                        (inputName?.toLowerCase() == '@@slaveserver2' && obj?.name?.toLowerCase() == '@@slavedatabase2'))
                        && restLabel?.includes('target') && (restLabel[2] ? obj?.label?.toLowerCase()?.includes(`target database ${restLabel[2]}`) :
                            obj?.label?.toLowerCase()?.includes(`target database`))));

                } else {

                    filteredData = fields.filter(fieldKey => {
                        const fieldName = formFields[fieldKey]?.attrs?.name?.toLowerCase();

                        if (!fieldName) return false;

                        const isSlaveServer =
                            (inputName?.toLowerCase() === '@@slaveserver' || inputName?.toLowerCase() === '@@slaveserver1') &&
                            ['@@slavedatabase', '@@slavedatabase1'].includes(fieldName);

                        const isSlaveServer2 =
                            inputName?.toLowerCase() === '@@slaveserver2' &&
                            fieldName === '@@slavedatabase2';

                        const isDRorTarget = ['@@drdatabase', '@@targetdatabase', '@@drdbname'].includes(fieldName);
                        return isSlaveServer || isSlaveServer2 || isDRorTarget;
                    });
                }
                loadDatabaseByServerId(filteredData, selectedValue, mode)
            }
        }
    }

    if (type === "radio" || type === "checkbox") {
        var field = formFields && formFields[selectedId];
        selectedValue = e.target.checked

        if (field?.conditions && field?.conditions.length) {
            field.conditions.forEach(function (condition) {
                var isMatchingCondition = condition.if.some(function (ifClause) {
                    sourceField = formFields[ifClause.source.substring(7)];
                    return ifClause.target === selectedValue;
                });
                if (isMatchingCondition) {
                    isVisible = true;
                }
            });

            field.conditions.forEach(function (condition) {
                condition.then.forEach(function (thenClause) {
                    condition.if.forEach(function (ifClause) {

                        let getId = thenClause.target.replace('fields.', '');
                        var targetElement = document.getElementById(`${getId}`);
                        // var targetElementChk = document.getElementById(`f-${thenClause.target.substring(7)}-0`);
                        if (targetElement) {
                            if (thenClause.targetProperty === 'isVisible' && ifClause.comparison !== "notEquals") {

                                if (!e.target.checked && (selectedId === `${ifClause.source.substring(7)}`)) {

                                    targetElement.value = ""
                                    targetElement.parentNode.parentNode.classList.add("d-none")
                                } else {
                                    targetElement.parentNode.parentNode.classList.remove("d-none")
                                }
                            } if (thenClause.targetProperty === 'isNotVisible' && ifClause.comparison !== "notEquals") {

                                if (!e.target.checked && (selectedId === `${ifClause.source.substring(7)}`)) {
                                    targetElement.parentNode.parentNode.classList.remove("d-none")
                                } else {

                                    targetElement.value = ""
                                    targetElement.parentNode.parentNode.classList.add("d-none")
                                }
                            } else if (ifClause.comparison === "notEquals") {

                                if (targetElement && e.target.checked && (selectedId === `${ifClause.source.substring(7)}`)) {
                                    targetElement.parentNode.parentNode.classList.add('d-none')
                                } else if (!e.target.checked && (selectedId === `${ifClause.source.substring(7)}`)) {
                                    var isFound = Object.keys(formFields).some(function (id) {
                                        var field = formFields[id];

                                        if ((selectedId !== id) && field.conditions && field.conditions.length) {
                                            return field.conditions.some(function (condition) {
                                                var isMatchingCondition = condition.then.some(function (ifClauses) {
                                                    return ifClauses.target === thenClause.target;
                                                });
                                                return isMatchingCondition && condition.if.some(function (ifClauses) {
                                                    return ifClauses.target === $('#' + id).val();
                                                });

                                            });
                                        }
                                        return false;
                                    });

                                    if (isFound) {
                                        targetElement.parentNode.parentNode.classList.remove('d-none')
                                    }
                                }

                            }

                        }
                    });
                });
            });
        }
    };

    if (type === "select-one") {
        let field = formFields && formFields[selectedId];

        if (field?.conditions && field?.conditions.length > 0) {
            let isMatchingCondition = field.conditions.some(function (condition) {
                return condition.if.some(function (ifClause) {
                    if (ifClause.source === `fields.${selectedId}` && ifClause.target === selectedValue) {
                        return true;
                    }
                });
            });
            let selectflag = [];
            if (isMatchingCondition) {
                field.conditions.forEach(function (condition) {
                    condition.then.forEach(function (thenClause) {
                        condition.if.forEach(function (ifClause) {

                            let getId = thenClause.target.replace('fields.', '');
                            var targetElement = document.getElementById(`${getId}`);

                            //targetElement.parentNode.parentNode.classList.add('d-none');

                            if (targetElement && thenClause.targetProperty === 'isVisible') {
                                //selectflag = false;
                                if (ifClause.target === selectedValue && thenClause.assignment === 'equals' && (targetElement.parentNode.parentNode.classList.contains("d-none"))) {
                                    ///if (ifClause.target === selectedValue && thenClause.assignment === 'equals') {
                                    var isFound = Object.keys(formFields).some(function (id) {
                                        var field = formFields[id];

                                        if ((getId !== id) && field.conditions && field.conditions.length) {
                                            return field.conditions.some(function (condition) {
                                                return condition.then.some(function (ifClauses) {
                                                    return ifClauses.target === thenClause.target && $('#' + id).prop('checked');
                                                });
                                            });
                                        }

                                        return false;
                                    });

                                    if (!isFound) {
                                        targetElement.parentNode.parentNode.classList.remove('d-none');
                                        selectflag.push(targetElement)
                                    }
                                    //else {
                                    //    selectflag = false
                                    //}
                                } else if (ifClause.target !== selectedValue) {

                                    if (!selectflag.includes(targetElement)) {
                                        targetElement.value = ""
                                        targetElement.parentNode.parentNode.classList.add('d-none');
                                    }
                                    //selectflag = true;
                                }
                            }
                        });
                    });
                });
            } else {

                if (field.conditions && field.conditions.length > 0) {
                    field.conditions.forEach(function (condition) {
                        condition.then.forEach(function (thenClause) {
                            condition.if.forEach(function (ifClause) {
                                var targetElement = document.getElementById(`${thenClause.target.substring(7)}`);

                                if (targetElement && selectedValue !== ifClause.target && ifClause.comparison !== 'notEquals') {
                                    targetElement.value = ""
                                    targetElement.parentNode.parentNode.classList.add('d-none');
                                }
                            });
                        });
                    });
                }
            }

        }
    }


    if (type == 'text') {
        // const checkIsIp = $(`#${selectedId}`)?.length && $(`#${selectedId}`)?.attr('isipaddress') &&
        // $(`#${selectedId}`)?.attr('isipaddress')?.toLowerCase() == 'ipaddressfield'

        // if (checkIsIp) {
        // const validationResults = IpaddressReg(selectedValue);
        // formIsValid = validationResults && typeof (validationResults) == 'boolean' ? validationResults : false;

        //if (!formIsValid) $(`[data-id='${inputName}-error']`).text(validationResults).addClass('field-validation-error');
        // }

        let sanitizedValue = e.target.value.replace(/\s{2,}/g, ' ');
        selectedValue = sanitizedValue

    } else if (type == 'number') {
        e.target.value = e?.target?.value?.replace(/[^0-9]/g, '');
    }

    //if (type === "password") {

    //} 

    if (newActionObj?.actionInfo?.properties && newActionObj?.actionInfo?.uniqueId) {
        if (newActionObj.actionInfo.properties.hasOwnProperty(inputName) && newActionObj.actionInfo.properties[inputName] != '') {
            selectedValue = newActionObj.actionInfo.properties[inputName];
        }
    }

    actionFormDataValidation(selectedValue, errorId, '', type);
}

const checkValidCpl = async (originalVal, errorId, mode = '', scriptType) => {
    let errorElement = $(`[data-id='${errorId}']`);
    let filteredAction = actionList.find((action) => action.id === newActionObj?.actionInfo?.actionType)
    let selectedActionText = filteredAction ? $(`#${filteredAction?.nodeId}`)?.text() : '';

    if ((filteredAction.actionName.toLowerCase() === 'executecpl' || selectedActionText?.trim()?.toLowerCase() == "as400") && originalVal) {
        let data = {
            "script": originalVal
        }
        try {
            const encryptedValue = await $.ajax({
                type: "GET",
                url: RootUrl + Urls.GetCPLValidation,
                data: data,
                dataType: 'text'
            });

            if (encryptedValue) {
                let splitEncryptedValue = encryptedValue && encryptedValue.split(':');
                let errorLine = findErrorLine(originalVal, splitEncryptedValue);

                errorElement.html(`<p style="width: 85%; text-align: start; font-size: 12px">${encryptedValue} on Line Number: ${errorLine}</p>`)
                    .addClass('field-validation-error').css({
                        'color': '',
                        'border-top': ''
                    });
                return false

            } else {
                if (mode === 'script') {
                    errorElement.html(`<p style="width: 85%; text-align: start; font-size: 12px">The CPSL script has been validated and is good to go.</p>`)
                        .addClass('field-validation-error').css({
                            'color': 'green',
                            'border-top': '1px solid green'
                        });

                    if (scriptType?.toLowerCase() == 'cpsl') $('#cpslConvert').removeClass('d-none')
                }
                return true
            }
        } catch (error) {
            console.error("Error occurred during encryption:", error);
        }
    } else {
        errorElement.html('<p style="width: 85%; text-align: start; font-size: 14px">Enter Script</p>')
            .addClass('field-validation-error');
    }
}
function findErrorLine(text, splitEncryptedValue) {
    let lines = text.split('\n');
    let errorText = splitEncryptedValue[1]?.trimStart() ?? ''

    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes(errorText)) {
            return i + 1;
        }
    }
    return -1;
}


const checkValidCPSharp = async (originalVal, errorId, mode = '') => {
    let errorElement = $(`[data-id='${errorId}']`);
    let filteredAction = actionList.find((action) => action.id === newActionObj?.actionInfo?.actionType)

    if ((filteredAction.actionName.toLowerCase() === 'executecpl') && originalVal) {

        let data = {
            "script": originalVal
        }
        try {

            const checkIsValidScript = await $.ajax({
                type: "GET",
                url: RootUrl + Urls.GetCPSharpValidation,
                data: data,
                dataType: 'text'
            });

            if (checkIsValidScript) {

                let errorLines = checkIsValidScript?.split('\r\n');
                errorElement.empty()
                errorLines?.length && errorLines?.forEach(line => {
                    if (line?.trim() !== '') {
                        errorElement.append(
                            `<p class="mb-0" style="width: 85%; text-align: start; font-size: 12px;">${line}</p>`
                        );
                    }
                });
                errorElement.addClass('field-validation-error').css({ 'color': '', 'border-top': '' });
                return false

            } else {
                if (mode === 'script') {
                    errorElement.html(`<p style="width: 85%; text-align: start; font-size: 12px">The CP# script has been validated and is good to go.</p>`)
                        .addClass('field-validation-error').css({
                            'color': 'green',
                            'border-top': '1px solid green'
                        });

                }
                return true
            }
        } catch (error) {
            console.error("Error occurred during encryption:", error);
        }

    } else {
        errorElement.html('<p style="width: 85%; text-align: start; font-size: 14px">Enter Script</p>')
            .addClass('field-validation-error');
    }
}

$(document).on('click', '#cpslConvert', function (e) {

    e.preventDefault();

    let script = $(this)?.parent()?.parent()?.find('textarea')?.val()
    let scriptName = $(this)?.parent()?.parent()?.find('#cpslTextArea')?.attr('name')
    let getSubAuthValue = $("select[name='@@SubstituteAuthentication']").val();

    let auth = getSubAuthValue ? getSubAuthValue : false

    if (scriptName) $(`[data-id="${scriptName}-error"]`).removeClass('field-validation-error').text('');

    $.ajax({
        type: "GET",
        url: RootUrl + Urls.CplScriptToCPSharp,
        data: { Script: script, IsSubstituteAuth: auth },
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.success) {

                if (result?.data) {
                    $('#cpslTextArea').val(result?.data?.script)?.parent()?.parent()?.removeClass('d-none')
                    $('#cpslLabel').removeClass('d-none')
                    $('#cpslConvert').addClass('d-none')
                    $('#scriptCheck').text('Validate CP#')
                }

            } else {
                errorNotification(result)
            }
        },

    })

})

const loadDatabaseByServerId = (filteredData, selectedValue, mode = '') => {
    let data = {}
    let url = 'ITAutomation/WorkflowConfiguration/GetDatabaseByServerId'
    filteredData.forEach(async (key) => {
        let $selectize = mode === 'restore' ? $('#' + key.id)[0].selectize : $('#' + key)[0].selectize;
        let inputName = mode === 'restore' ? key?.name : formFields[key]?.attrs?.name

        if ($selectize) {
            $selectize.clear();
            $selectize.clearOptions();
        }

        data.serverId = selectedValue
        let getDatabaseByServerId = await getAysncWithHandler(RootUrl + url, data, OnError)

        if (getDatabaseByServerId.length) {
            for (let k = 0; k < getDatabaseByServerId.length; k++) {
                let servertype = getDatabaseByServerId[k].type ? getDatabaseByServerId[k].type : ''

                $selectize.addOption({
                    value: getDatabaseByServerId[k].id, servertype: servertype,
                    roletype: '', text: getDatabaseByServerId[k].name
                });

            }
        } else {
            $selectize.addOption({ value: 'No data found', text: 'No data found', disabled: true });
        }

        if (newActionObj?.actionInfo?.properties && newActionObj?.actionInfo?.uniqueId) {
            $selectize.setValue(newActionObj.actionInfo.properties[inputName]);
        } else if (infraRoleReverseDetails.length) {
            let componentData = infraRoleReverseDetails[0]

            if (restoreFieldObj.length > 0) {
                restoreFieldObj.forEach((item) => {
                    if (item?.optionRoleType !== "application") {
                        if (item?.optionType === "PRDB") {
                            let drdbData = JSON.parse(componentData?.databaseProperties);

                            if (drdbData?.DR?.id?.includes(',')) {
                                let splitData = drdbData?.DR?.id?.split(',');
                                $("#" + item?.id)?.eq(0)?.data('selectize')?.setValue(splitData[0]);
                            } else {
                                $("#" + item?.id)?.eq(0)?.data('selectize')?.setValue(drdbData?.DR?.id);
                            }
                        } else if (item?.optionType === "DRDB") {
                            let prdbData = JSON.parse(componentData?.databaseProperties);

                            if (prdbData?.PR?.id?.includes(',')) {
                                let splitData = prdbData?.PR?.id?.split(',')
                                $("#" + item?.id)?.eq(0)?.data('selectize')?.setValue(splitData[0]);
                            } else {
                                $("#" + item?.id)?.eq(0)?.data('selectize')?.setValue(prdbData?.PR?.id);
                            }

                        }
                    }
                })
            }

        }
    });

}

async function loadWorflowAction(inputValue, workActionValue = '') {
    let $workflowSelect = $('select[name="@@DependentWorkflowAction"]:not(:disabled)');
    if ($workflowSelect.length == 0) {
        $workflowSelect = $('select[data-typeName="@@DependentWorkflowAction"]');
    }

    if (inputValue) {

        let url = 'ITAutomation/WorkflowConfiguration/GetWorkflowById'
        let data = { workflowId: inputValue }
        let serverTypes = await getAysncWithHandler(RootUrl + url, data)
        let properties = serverTypes && serverTypes?.properties ? JSON.parse(serverTypes.properties) : {}
        let previouslySelectedOption = $workflowSelect[0].selectize.getValue();

        if ($workflowSelect[0]?.selectize) {
            $workflowSelect[0].selectize.options = {};
            $workflowSelect[0].selectize.clearOptions();
        }

        if (properties?.nodes && properties?.nodes.length) {

            for (var i = 0; i < properties?.nodes.length; i++) {

                if (properties?.nodes[i]?.children) {
                    properties?.nodes[i]?.children.forEach((pa) => {
                        $workflowSelect[0].selectize.addOption({ value: pa.stepId, text: pa?.actionInfo.actionName });
                    });
                } else if (properties?.nodes[i].hasOwnProperty('groupName')) {

                    properties?.nodes[i]?.groupActions.forEach((ga) => {
                        if (ga.hasOwnProperty('children')) {
                            ga?.children.forEach((pa) => {
                                $workflowSelect[0].selectize.addOption({ value: pa.stepId, text: pa?.actionInfo.actionName });
                            });
                        } else {
                            $workflowSelect[0].selectize.addOption({ value: ga.stepId, text: ga?.actionInfo.actionName });
                        }
                    });

                } else {
                    $workflowSelect[0].selectize.addOption({ value: properties?.nodes[i].stepId, text: properties?.nodes[i]?.actionInfo.actionName });
                }

            }

        } else {
            $workflowSelect[0].selectize.addOption({ value: 'No data found', text: 'No data found', disabled: true });
        }

        if (workActionValue !== '') {
            $workflowSelect[0].selectize.setValue(workActionValue);
        } else {
            $workflowSelect[0].selectize.clear();
            $workflowSelect[0].selectize.removeOption(previouslySelectedOption);
        }
        $(`[data-id="@@DependentWorkflowAction-error"]`).removeClass('field-validation-error').text('');
    }
}

function actionInfoValidation(value, errorId, text = '', e) {
    let formValid = true;
    const errorElement = $('#' + errorId);
    if (!value) {
        errorElement.text(text)
            .addClass('field-validation-error');
        formValid = false;
    } else {
        if (errorId === 'actionName-error') {
            const validationResults = [
                SpaceWithUnderScore(value),
                ShouldNotBeginWithSpace(value),
                ShouldNotEndWithSpace(value),
                SpaceAndUnderScoreRegex(value),
                ShouldNotEndWithUnderScore(value),
                OnlyNumericsValidate(value),
                MultiUnderScoreRegex(value),
                SpecialCharCustomValidate(value),
                secondChar(value),
                minMaxlength(value, 200)
            ];
            formValid = CommonValidation(errorElement, validationResults);
        } else if (errorId === 'rto-error') {
            if (value?.toLowerCase().includes('e')) {
                $('#rto').val('').text('')
                formValid = false;
            } else {
                errorElement.removeClass('field-validation-error').text('');
                formValid = true;
            }
        } else {
            errorElement.removeClass('field-validation-error').text('');
            formValid = true;
        }
    }
    return formValid;
}

const SpecialCharCustomValidate = (value) => {
    const regex = /^[a-zA-Z0-9_\s-]*$/;
    return !regex.test(value) ? "Special characters not allowed" : !(/^[^<]*$/).test(value) ? "Special characters not allowed" : true;
}

function actionFormDataValidation(value, errorId, text = '', type = '', checkArray = []) {
    const errorElement = $(`[data-id='${errorId}']`);

    if (!value) {
        errorElement.text(text).addClass('field-validation-error');
    } else {
        if (type == 'text') {

            const validationResults = [
                ShouldNotBeginWithSpace(value),
                ShouldNotEndWithSpace(value),
            ];

            checkArray.push(CommonValidation(errorElement, validationResults));
        } else {
            errorElement.removeClass('field-validation-error').text('');
        }
    }

    return checkArray;

}
$('#workflowList').on('change', function () {
    const value = $("#workflowList").val();
    workFlowList(value);
})
function workFlowList(value) {
    const errorElement = $('#workflowList-error');
    if (!value) {
        errorElement.text('Select workflow name').addClass('field-validation-error');
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}

$('#btnLoadWorkFlow').on('click', function () {
    var errorElement = ['#workflowList-error']
    $("#workflowList").val('');
    conditionalArray = []
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
})

////////////Attach modal validation ///////////////////////
$('#infraObjectList').on('change', function () {
    const value = $("#infraObjectList").val();
    infraObjectList(value);
})
$('#actionTypeAttach').on('change', function () {
    const value = $("#actionTypeAttach").val();
    actionType(value);
})
function infraObjectList(value) {
    const errorElement = $('#infraobjectname-error');
    if (!value) {
        errorElement.text('Select infraObject').addClass('field-validation-error');
        return false;
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return true;
    }
}
function actionType(value) {
    const errorElement = $('#actiontypeattach-error')
    if (!value) {
        errorElement.text('Select operation type').addClass('field-validation-error')
        return false;
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return true;
    }

}
$('#workflowAttach').on('click', function () {
    var errorElement = ['#infraobjectname-error', '#actiontypeattach-error']
    $("#infraObjectList").val('');
    $("#actionTypeAttach").val('');
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
})

//////////Generate Template Modal validation///////////////btnGenerateTemplate
$('#templateName').on('keyup', async function () {
    const value = $("#templateName").val();
    await templateNameValidate(value);
})

$('#templateActionType').on('change', function () {
    const value = $("#templateActionType").val();
    templateActionValidate(value);
})

$('#replicationList').on('change', function () {
    const value = $("#replicationList").val();
    templateRestoreValidate(value, '#replicationRestore-error', 'Select replication type');
})

$('#operationList').on('change', function () {
    const value = $("#operationList").val();
    templateRestoreValidate(value, '#operationRestore-error', 'Select operation type');
})

async function templateNameValidate(value, id = null) {
    const errorElement = $('#templatename-error')

    if (!value) {
        errorElement.text('Enter template name').addClass('field-validation-error')
        return false;
    }
    var url = RootUrl + Urls.templateNameExist;
    var data = {};
    data.name = value;
    data.id = id;

    const validationResults = [
        SpecialCharValidate(value),
        ShouldNotBeginWithUnderScore(value),
        ShouldNotBeginWithSpace(value),
        OnlyNumericsValidate(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        ShouldNotBeginWithNumber(value),
        ShouldNotEndWithSpace(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        minMaxlength(value),
        secondChar(value),
        await IsNameExist(url, data.name, data, OnError)
    ];

    return CommonValidation(errorElement, validationResults);
}
function templateActionValidate(value) {
    const errorElement = $('#templateactiontype-error')
    if (!value) {
        errorElement.text('Select operation type').addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}

function templateRestoreValidate(value, errorId, textContent) {
    const errorElement = $(errorId)
    if (!value) {
        errorElement.text(textContent).addClass('field-validation-error')
        return "true";
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return "false";
    }
}
$('#btnGenerateTemplate').on('click', function () {
    var errorElement = ['#templatename-error', '#templateicon-error', '#templateactiontype-error', "#Activetype-error", "#SelectDatabaseType-error", "#SelectReplicationType-error", "#ReplicationType-error"]
    $("#templateName").val('');
    $("#templateActionType").val('');
    errorElement.forEach(element => {
        $(element).text('').removeClass('field-validation-error')
    });
})

////////////// Save Modal validation /////////////////
$('#workflowName').on('keyup', async function () {
    const value = $("#workflowName").val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await WorkflowNameValidate(value, '', 'workflowName-error');
})

$('#saveAsWorkflowName').on('keyup', async function () {
    const value = $("#saveAsWorkflowName").val();
    let sanitizedValue = value.replace(/\s{2,}/g, ' ');
    $(this).val(sanitizedValue);
    await WorkflowNameValidate(value, '', 'saveAsWorkflowName-error');
})
async function WorkflowNameValidate(value, id = null, errorId) {

    const errorElement = $('#' + errorId);

    if (!value) {
        errorElement.text('Enter workflow name').addClass('field-validation-error')
        return false;
    } else {
        var url = RootUrl + Urls.workFlowNameExist;
        var data = {};
        data.workflowName = value;
        data.id = id;

        const validationResults = [
            SpecialCharCustomValidate(value),
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithNumber(value),
            SpaceWithUnderScore(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxWorkflowlength(value),
            secondChar(value)
        ];
        if (workflowSaveMode === "Save" || workflowSaveMode === 'Update' || workflowSaveMode === "Extract" || $('#SaveAsWorkflowListModal').hasClass('show')) {
            if (workflowSaveMode === 'Update') {
                if (data.workflowName !== GlobalWorkflowName) {
                    validationResults.push(await IsNameExist(url, data.workflowName, data, OnError))
                }
            } else {
                validationResults.push(await IsNameExist(url, data.workflowName, data, OnError))
            }

        }
        return CommonValidation(errorElement, validationResults);
    }
}

const minMaxWorkflowlength = (value) => {
    return value.length > 2 && value.length < 201 ? true : "Between 3 to 200 characters"
}


const partialValidation = (value, id = null, errorId, ErrorText) => {
    const errorElement = $('#' + errorId);

    if (!value) {
        errorElement.text(ErrorText).addClass('field-validation-error')
        return false;
    } else {

        let validationResults = [
            ShouldNotBeginWithUnderScore(value),
            ShouldNotBeginWithSpace(value),
            SpaceWithUnderScore(value),
            OnlyNumericsValidate(value),
            ShouldNotBeginWithNumber(value),
            ShouldNotEndWithUnderScore(value),
            ShouldNotEndWithSpace(value),
            MultiUnderScoreRegex(value),
            SpaceAndUnderScoreRegex(value),
            minMaxlength(value),
            secondChar(value),
        ];
        errorId.toLowerCase().includes('actiongroupname') ? validationResults.push(groupValidation(value)) : null;
        errorId.includes('findValue') ? validationResults.push(checkFindActionName(value)) : null
        return CommonValidation(errorElement, validationResults);
    }
}

//  workflow group name validation
$('#groupName').on('keyup', function (e) {
    const value = $("#groupName").val();
    partialValidation(value, '', 'actionGroupName-error', 'Enter group name');
})

const groupValidation = (value) => {
    if (value?.length) {
        if (globalGroupArray.includes(value)) {
            return 'Group name already exists';
        } else {
            return true;
        }
    }
}

const checkFindActionName = (value) => {
    let checkValue
    let checkValueArray = []
    $(".workflowActions").each(function (idx, obj) {
        let id = obj.id
        let Details = JSON.parse(atob($("#" + id).attr("details")))
        checkValue = Details.actionInfo.actionName.includes(value);
        checkValueArray.push(checkValue)
    })
    return checkValueArray.includes(true) ? true : 'Entered find value atleast match with one action'
}

$('#findValue').on('keyup', function (e) {
    let enteredText = e.target.value
    partialValidation(enteredText, '', 'findValue-error')
})

$('#replaceValue').on('keyup', function (e) {
    let enteredText = e.target.value
    partialValidation(enteredText, '', 'replaceValue-error')
})

$('#appendText').on('keyup', function (e) {
    let enteredText = e.target.value
    partialValidation(enteredText, '', 'appendText-error')
})

async function IsNameExist(url, name, data, errorFunc) {
    return !name.trim() ? true : (await GetAsync(url, data, errorFunc)) ? "Name already exists" : true;
}
function handleSaveAsChange(e, errorId, text = '') {
    actionSaveDataValidation(e.target.value, errorId, text);
}

function actionSaveDataValidation(value, errorId, text = '') {
    const errorElement = $('#' + errorId);
    if (!value) {
        errorElement.text(text).addClass('field-validation-error');
    } else {
        partialValidation(value, '', errorId)
    }
}

$("#saveAsAppendCheckBox").on('keyup', function () {
    if (!$("#saveAsAppendCheckBox").prop("checked")) {
        $('#appendText-error').text('').removeClass('field-validation-error')
    }
})

$("#appendText").on('keyup', function (e) {
    if (e.target.value) {
        ['#replaceValue-error', '#findValue-error'].forEach(element => {
            $(element).text('').removeClass('field-validation-error')
        });
    }
})

// Import and Export Validation
$('#ExportName').on('keyup', function () {
    const value = $("#ExportName").val();
    importExportValidation(value, 'exportName-error', 'Enter export name');
})

$('#ImportWorkflow').on('change', function (e) {
    const value = $("#ImportWorkflow").val();
    importExportValidation(value, 'importWorkflow-error', 'Choose workflow');
})

function importExportValidation(value, errorId, text) {
    const errorElement = $('#' + errorId)
    if (!value) {
        errorElement.text(text).addClass('field-validation-error')
        return false;
    } else {
        errorElement.removeClass('field-validation-error').text('');
        return true;
    }
}

////////////// Save As Modal Validation /////////////////////////////
$("#btnExtract").on('click', function () {

    let checkedExtract = $('input[name="actionCheck"]:checked');
    if (checkedExtract.length === 0) {
        notificationAlert("warning", 'Mark the actions for extraction')
        return false
    }
    totalEstimatedRto = 0
    $('#workflowName').val('');
    $('#workflowName-error').text('').removeClass('field-validation-error');
    workflowSaveMode = "Extract"
    //$("#workflowModalTitle").text("Extract Workflow")
    $("#workflowModalTitle").html('<i class="cp-zip"></i> Extract Workflow');
    getExtractdata();
});

$("#confirmationSave").on('click', function () {

    $("#confirmationModal").modal('hide');
    workflowSaveMode = 'Extract';
    globalExtractData = false
    $('#SaveWorkflowModal').modal('show');

})

$("#confirmationDiscard").on('click', function () {
    $("#confirmationModal").modal('hide');
    globalExtractData = false
    return false;
})

const checkGroup = (obj) => {

    $("#confirmationModalText").html("<span class='text-primary'>" + obj + "</span> group contains only one action. If you want to extract the action, It will not continue as group while saving")
    globalExtractData = true;
    $("#confirmationModal").modal('show');
}

const checkParallel = () => {
    $("#confirmationModalText").text("Parallel group contains only one action. If you want to extract the action, It will considered as sequence")
    globalExtractData = true;
    $("#confirmationModal").modal('show');

}

const getExtractdata = () => {
    globalExtractArray = [];
    $("#workflowActions").children().not('canvas').each((idx, obj) => {

        let id = ($(obj).find('.parentConditionCont').length === 0 && !$(obj)[0].classList.contains('workflowCanvas')) ? obj.children[1].id : '';
        let details;
        if (id.includes('node')) {
            if ($("#" + id).find(".actionCheckBox").is(":checked")) {
                let decodedDetails = atob($('#' + id).attr('details'))
                details = JSON.parse(decodedDetails)
                calculateTotalRto(details)
                globalExtractArray.push(details)
            }
        } else if (id.includes('parallel')) {
            let parallelArray = []
            $('#' + id).children().each(function (idx, child) {
                let childId = child.id;
                if ($("#" + childId).find(".actionCheckBox").is(":checked")) {
                    details = atob($('#' + childId).attr('details'))
                    let parsedDetails = JSON.parse(details)
                    parsedDetails.actionInfo['IsParallel'] = true;
                    calculateTotalRto(parsedDetails)
                    parallelArray.push(parsedDetails)
                }
            })
            if (parallelArray.length === 0) {
                globalExtractArray
            } else if (parallelArray.length === 1) {
                checkParallel()
                parallelArray[0].actionInfo['IsParallel'] = false;
                calculateTotalRto(parallelArray[0])
                globalExtractArray.push(parallelArray[0])
            } else {
                globalExtractArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': parallelArray })
            }
        } else if (id.includes('group')) {
            let groupId = obj?.children[1]?.getAttribute('id')
            let groupName = obj?.children[1]?.getAttribute('groupName')
            let groupColor = obj?.children[1]?.getAttribute('groupColor')
            let groupArray = [];

            $('#' + id + ' .accordion-body').children().each(function (idx, child) {
                let groupChild
                if (idx !== 0) {
                    groupChild = child.children[1].id;
                } else {
                    groupChild = child.children[0].id;
                }
                if (groupChild.includes('node')) {
                    if ($("#" + groupChild).find(".actionCheckBox").is(":checked")) {
                        details = atob($('#' + groupChild).attr('details'))
                        let parsedDetails = JSON.parse(details)
                        parsedDetails.actionInfo['IsGroup'] = true;
                        calculateTotalRto(parsedDetails)
                        groupArray.push(parsedDetails)
                    }
                } else if (groupChild.includes('parallel')) {
                    let groupParallelArray = []
                    $('#' + groupChild).children().each(function (idx, child) {
                        let childId = child.id;
                        if ($("#" + childId).find(".actionCheckBox").is(":checked")) {
                            details = atob($('#' + childId).attr('details'))
                            let parsedDetails = JSON.parse(details)
                            parsedDetails.actionInfo['IsParallel'] = true;
                            parsedDetails.actionInfo['IsGroup'] = true;
                            calculateTotalRto(parsedDetails)
                            groupParallelArray.push(parsedDetails)
                        }
                    })

                    groupArray.push({ 'actionInfo': { 'IsParallel': true }, 'children': groupParallelArray })
                }

                //$('#' + id + ' .workflowActions').each(function (idx, child) {
                //    let childId = child.id;
                //    if ($("#" + childId).find(".actionCheckBox").is(":checked")) {
                //        details = $('#' + childId).attr('details')
                //        let parsedDetails = JSON.parse(details)
                //        parsedDetails.actionInfo['IsGroup'] = true;
                //        groupArray.push(parsedDetails)
                //    }
                //});
            })
            //if (groupArray.length === 0) {
            //    globalExtractArray
            //} else if (groupArray.length === 1) {
            //
            //    checkGroup(groupName)
            //    delete groupArray[0].actionInfo.IsGroup
            //    globalExtractArray.push(groupArray[0])
            //} else {
            if (groupArray.length) globalExtractArray.push({ 'groupName': groupName, 'groupColor': groupColor, 'groupId': groupId, 'groupActions': groupArray })

            //}

        }

    })

    if (!globalExtractData) {
        $('#workflowName').val('')
        workflowSaveMode = 'Extract';
        $('#SaveWorkflowModal').modal('show')

    }

}

$(window).on('beforeunload', function () {
    if ($('.checkSaveWorkflow').is(':visible') && !isUnAuthorize) {
        return false
    }
});



//$(window).on('beforeunload', function (e) {

//    // Cancel the event
//    e.preventDefault(); // If you prevent default behavior in Mozilla Firefox prompt will always be shown
//    // Chrome requires returnValue to be set
//    e.returnValue = '';
//});

//$(window).on('beforeunload', function () {
//    debugger
//    if ($(".checkSaveWorkflow").is(":visible")) {
//        return 'Are you sure you want to leave?';
//    }
//});






