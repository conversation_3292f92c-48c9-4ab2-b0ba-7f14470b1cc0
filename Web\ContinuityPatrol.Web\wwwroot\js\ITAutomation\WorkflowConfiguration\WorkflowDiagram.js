$(".checkSaveWorkflow, #collapseStatus, #EndWorkflowButton, #btnPaste").hide();
let connectImage = '<i class="cp-workflow-line fs-7"></i></div>';
let groupColorPallate = ["#00AAC1", "#FE0178", "#00C3A5", "#FFA730", "#2AAB35", "#F459F4", "#E67612",
    "#B2912F", "#DF3657", "#5E4D8A", "#5E4D8A", "#C0B900", "#1E89C8", "#034577", "#7F66FF"]

let usedGroupColorPalatte = [];
$("#groupNameSave").text("Save")
let clickCount = 0;
let clickTimeout;
let workflowContainer = $("#workflowActions");
let ParentContainer = $("#parentAction");
let globalEditGroupId = '';
let selectedActions = [];
let lastGoToConditional = '';
let isParallel = false;
let ConditionDetails = { 'If': false, 'Else': false }
let tempActionsArray = []

const contextClose = () => $(".contextMenu").fadeOut(100);

function btnDebounce(func, timeout = 300) {
    let timer;
    return function (...args) {
        if (!timer) {
            func.apply(this, args);
        }
        clearTimeout(timer);
        timer = setTimeout(function () {
            timer = undefined;
        }, timeout);
    };
}

const getRandomId = (value) => {
    return value + "_" + Math.floor(Math.random() * 11110 * Math.random() * 103180)
}

const workflowLoader = (mode) => {
    if (mode === 'on') {
        $('#workflowContainer').css('opacity', '0.7')
        $('#workflowContainer').css('pointer-events', 'none')
        $('#WFLoader').show()
    } else {
        $('#workflowContainer').css('opacity', '')
        $('#workflowContainer').css('pointer-events', '')
        $('#WFLoader').hide()
    }
}

const btnDisableWhileClick = () => {
    disableWorkflowTools(true)
    setTimeout(() => {
        disableWorkflowTools(false)
    }, 700)
}

const removeSelection = () => {
    $('.workflowActions').removeClass("selectedWfContainer")
    $('.groupClass').removeClass("selectedWfContainer")
    $('.parallelCont').removeClass("selectedWfContainer")
    $('#filter_property').dropdown('hide')
}

const checkGroupCount = () => {

    if ($('#workflowActions').find('.parentGroup').length > 1) {
        if ($('.accordion-collapse').hasClass('show')) {
            $("#collapseStatus").removeClass('cp-circle-uparrow')
            $("#collapseStatus").addClass('cp-circle-downarrow')
        } else {
            $("#collapseStatus").removeClass('cp-circle-downarrow')
            $("#collapseStatus").addClass("cp-circle-uparrow");
        }
        $("#collapseStatus").show();
    } else {
        $("#collapseStatus").hide();
    }
}

workflowContainer.on('click', '.workflowActions', function (e) {

    e.stopPropagation()
    clickCount++;
    if (clickCount === 1) {
        if (!GlobalIsLock && !$('.actionCheckBox').is(':visible')) {
            let dd = this.id
            clickTimeout = setTimeout(function () {
                if (e.ctrlKey) {

                    if ($("#" + dd).hasClass("selectedWfContainer")) {
                        $("#" + dd).removeClass("selectedWfContainer")
                    } else {
                        if ($('#' + dd).hasClass('workflowActions')) {
                            $("#" + dd).addClass("selectedWfContainer")
                            if ($('#' + dd).closest('.parallelCont')) {
                                if ($('#' + dd).closest('.parallelCont').find('.workflowActions').not('.selectedWfContainer').length === 0) {
                                    $('#' + dd).closest('.parallelCont').addClass('selectedWfContainer')
                                    $('#' + dd).closest('.parallelCont').find('.workflowActions').removeClass('selectedWfContainer')
                                }
                            } else {
                                removeSelection()
                                $("#" + dd).addClass("selectedWfContainer")
                            }
                        }
                    }
                } else if (e.shiftKey) {
                     if ($('.selectedWfContainer').length > 0 && $("#" + dd).parents('.groupClass') && $("#" + dd).parents('.parallelCont')) {
                        let initialElementId = $('.selectedWfContainer').last().attr('id')
                        let initialElementId1 = $('.selectedWfContainer').first().attr('id')
                        let lastSelectedlength = initialElementId.includes('parent') ? $('#' + initialElementId).parent().parents().index() : $('#' + initialElementId).parents().index();
                        let firstSelectedlength = initialElementId1.includes('parent') ? $('#' + initialElementId1).parent().parents().index() : $('#' + initialElementId1).parents().index();
                        let targetSelectedlength = $('#' + dd).parents().index()

                        let isParallel = $('#' + dd)?.parent()?.attr('id')?.includes('parallel')                      

                        let checkStartElementGroup = $('#' + initialElementId)?.parents('.groupClass')
                        let checkTargetIsGroup = $('#' + dd)?.parents('.groupClass')
                         

                        if (checkStartElementGroup && checkTargetIsGroup) {
                            if (checkStartElementGroup?.attr('id') !== checkTargetIsGroup?.attr('id')) {
                                $('#' + initialElementId)?.removeClass('selectedWfContainer')
                                notificationAlert('warning', 'Invalid group selection')
                                return false;
                            }
                         }

                         if (isParallel) {

                             $('#' + initialElementId)?.removeClass('selectedWfContainer')
                             let isCheckParentParallel = $('#' + initialElementId)?.parent()?.attr('id')?.includes('parallel')
      
                             if (!isCheckParentParallel) {                                
                                 notificationAlert('warning', 'Invalid parallel selection')
                                 return false;
                             } else {
                                 $('#' + dd)?.parent()?.addClass('selectedWfContainer')
                             }                         

                         }else if (lastSelectedlength < targetSelectedlength) {
                            for (let i = lastSelectedlength + 1; i <= targetSelectedlength; i++) {
                                let selectedId = workflowContainer?.children()[i]?.children[1]?.id

                                if (checkTargetIsGroup && checkTargetIsGroup?.length) {
                                    let parentId = checkTargetIsGroup?.attr('id')

                                    if (parentId) selectedId = $(`#${parentId}`)
                                        ?.find('.ui-sortable-handle')
                                        ?.eq(i)
                                        ?.find('.workflowActions')?.attr('id')
                                }

                                if (selectedId && selectedId.includes('group')) {
                                    $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                                } else if ($('#' + selectedId)?.parent()?.attr('id')?.includes('parallel')) {
                                    $('#' + selectedId)?.parent()?.addClass('selectedWfContainer')
                                } else {
                                    $('#' + selectedId).addClass('selectedWfContainer')
                                }
                            }
                        } else if (firstSelectedlength > targetSelectedlength) {
                            for (let i = targetSelectedlength; i <= firstSelectedlength - 1; i++) {
                                let selectedId = workflowContainer?.children()[i]?.children[1]?.id

                                if (checkTargetIsGroup) {
                                    let parentId = $('#' + dd)?.parents('.groupClass')?.attr('id')

                                    if (parentId) selectedId = $(`#${parentId}`)
                                        ?.find('.ui-sortable-handle')
                                        ?.eq(i)
                                        ?.find('.workflowActions')?.attr('id')
                                }

                                if (selectedId && selectedId.includes('group')) {
                                    $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                                } else {
                                    $('#' + selectedId).addClass('selectedWfContainer')
                                }
                            }
                        }
                    }  else {
                        $("#" + dd).addClass("selectedWfContainer")
                    }
                } else {
                    if ($("#" + dd).hasClass("selectedWfContainer")) {
                        $("#" + dd).removeClass("selectedWfContainer")
                    } else {
                        removeSelection();
                        $("#" + dd).addClass("selectedWfContainer")
                    }
                }
                clickCount = 0;
            }, 200);
        }
    } else if (clickCount === 2) {
        
        clearTimeout(clickTimeout);
        actionEditData(this, 'dblClick')
        clickCount = 0;
    }
    e.stopPropagation();
    contextClose();
})

const actionEditData = (selectedId, mode='') => {

    clearInputWFFields();
    let getObjDetails = mode === 'dblClick' ? $(selectedId) : $(`#${selectedId}`);
    let decodedDetails = atob(getObjDetails.attr('details'))
    let updateObj = JSON.parse(decodedDetails);
    updateActionObject = JSON.parse(decodedDetails);
    newActionObj = updateObj;
    if (!newActionObj.actionInfo.hasOwnProperty('propertyData')) newActionObj['actionInfo']['propertyData'] = { propertiesInfo: [] }
    changeCommandScript(newActionObj, 'actionEdit')
    isEdit = true;
    $('#next').addClass('disabled')
    GetActionListByNodeId(newActionObj?.actionInfo?.nodeId)  
    populateFormWithData(updateObj?.actionInfo);
    //getUserList('754483ba-4a02-4e7e-93cf-315d47c6822e');
    form.steps('previous');
    $('#WFActionNextBtnLoader').show()
    $("#next").removeClass('next-disabled')
    $('#CreateModal').modal('show');
    removeSelection();
    $(".sectionData").empty();   

}

$(function (event) {
    workflowContainer.sortable({
        connectWith: ".accordion-body, #workflowActions",
        items: ".ui-sortable-handle",
        forceHelperSize: true,
       
        update: function (event, ui) {
            let connectId = ui.item[0].children[0].id
            updateIndexValues();
        },
    })
})

workflowContainer.on("sortstart", function (event, ui) {
    let connectId
    if (ui.item[0].children.length === 1) {
        connectId = ui.item[0].children[0].id
    } else if (ui.item[0].children.length > 1) {
        connectId = ui.item[0].children[1].id
    }
    if (connectId && !connectId.includes('condition')) {
        if (!connectId.includes("group")) {
            if ($("#" + connectId).parent().find('.cp-workflow-line').length > 0) {
                $("#" + connectId).parent().find(".cp-workflow-line").remove()
            }
        } else {
            $("#" + connectId).prev().remove()
        }
    }
   
});

workflowContainer.on("sortstop", function (event, ui) {
    let connectId = '';
    if (ui.item[0].children.length === 1) {
        connectId = ui.item[0].children[0].id
    } else if (ui.item[0].children.length > 1) {
        connectId = ui.item[0].children[1].id
    }
    if (connectId.includes('condition')) {
        if ($('#' + connectId).parent().index() === 0) {
            return false;
        }
    }
    if (connectId && !connectId.includes('condition')) {
        $("#" + connectId).before(connectImage)
        $(".workflowActions").removeClass('selectedWfContainer')
        let connectGroupId;
        if ($("#" + connectId).parents('.parentGroup').length > 0) {
            connectGroupId = $("#" + connectId).parents('.parentGroup')[0].id
            $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").find('.cp-workflow-line').remove()
            $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").prepend(connectImage);
            $("#" + connectGroupId + " .accordion-body .ui-sortable-handle").first().find('.cp-workflow-line').remove();
        }
        $(".checkSaveWorkflow").show()
        checkPositionChanged(connectId)
        if ($('#workflowActions').children().first().children().attr('id')  && $('#workflowActions').children().first().children().attr('id').includes('condition')) {
            setTimeout(() => {
                updateIndexValues();
            },200)
            $(".checkSaveWorkflow").hide()
            return false;
        }      
        if (runningCount?.length) {
            let count = runningCount.split('/')[0]
            if (ui.item.index() <= Number(count)) {
                setTimeout(() => {
                    updateIndexValues();
                }, 200)
                $(".checkSaveWorkflow").hide()
                return false;
            }
        }
        
       
    }
})

function updateIndexValues() {
    actionArray = [];
    $(".workflowActions").each(function (index) {
        $(this).find('.workflowIndex').html(index + 1)
        actionArray.push({ actionId: $(this).attr('id'), actionName: $(this).find('.actionSpan').text(), type: 'sequential', IsParallel: false })
    });

}

ParentContainer.on("contextmenu", function (e) {
    ConditionDetails = { 'If': false, 'Else': false }
    let menu = $(".contextMenu");
    if (menu.is(":visible")) {
        e.preventDefault();
        menu.hide()
    } else {
        e.preventDefault();
        menu.hide();
        $('#btnParallel, #btnGroup, #btnCopy,#btnActionEdit, #btnCut, #btnUnselect, #btndelete, #btnUnParallel, #btnSelectAll, #btnUnGroup, #btnEditGroup, #btnConditional, #btnGoToConditional, #removeIfConditional, #removeElseConditional, #removeConditional').hide()
        let groupCombineCheck = []
        let parallelCombineCheck = []
        let groupClassParallelCheck = []
        let groupActionOnlyCheck = []
        let Parentcontainer = $(".ui-sortable-handle");
        let Parallelcontainer = $(".selectedWfContainer.parallelCont")
        let SelectContainer = $(".selectedWfContainer.workflowActions")
        let groupBtnCheck = $('.btnGroup').parents('.selectedWfContainer').length
        let actionContainer = $(".workflowTextClass").parents(".selectedWfContainer.workflowActions").length
        let actionNameContainer = $(".actionSpan").parents(".selectedWfContainer.workflowActions").length
        let groupCheck = $("." + e.target.className).parents('.selectedWfContainer').length

        SelectContainer.each((idx, obj) => {

            let selectedId = obj.id;
            if ($("#" + selectedId).parents(".groupClass").length === 0) {
                groupCombineCheck.push(true)
            } else {
                groupCombineCheck.push(false)
            }
        })
        SelectContainer.each((idx, obj) => {

            let selectedId = obj.id;
            if ($("#" + selectedId).parents(".groupClass").length === 0) {
                groupClassParallelCheck.push(true)
            } else {
                groupClassParallelCheck.push(false)
            }
        })
        SelectContainer.each((idx, obj) => {

            let selectedId = obj.id;
            if ($("#" + selectedId).parents(".groupClass").length > 0) {
                if ($("#" + selectedId).parents(".parallelCont").length === 0) {
                    groupActionOnlyCheck.push(true)
                } else {
                    groupActionOnlyCheck.push(false)
                }
            } else {
                groupActionOnlyCheck.push(false)
            }
        })
        SelectContainer.each((idx, obj) => {
            let selectedId = obj.id;

            if ($("#" + selectedId).parents(".parallelCont").length === 0) {
                parallelCombineCheck.push(true)
            } else {
                if ($("#" + selectedId).parents(".groupClass").length === 0) {
                    parallelCombineCheck.push(false)
                } else {
                    if ($("#" + selectedId).parents(".parallelCont").length === 0) {
                        parallelCombineCheck.push(true)
                    } else {
                        parallelCombineCheck.push(false)
                    }
                }
            }
        })

        if (Parentcontainer.is(e.target) && Parentcontainer.has(e.target).length === 0) {
            $('#btnCut').hide()

            if (workflowContainer.children().length === $('.selectedWfContainer').length) {
                $("#btnUnselect").show()
            } else {
                if (!$('.actionCheckBox').is(':visible')) {
                    $("#btnSelectAll").show()
                }
            }
        } else if (Parallelcontainer.is(e.target) && Parallelcontainer.has(e.target).length === 0) {

            $('#btnCopy, #btndelete, #btnCut, #btnConditional').show()
            if (Parallelcontainer.length < 2) {
                $('#btnUnParallel').show()
            }

            if (SelectContainer.length) {
                $('#btnUnParallel').hide();
                $('#btnGroup').show();
            }
        } else if (SelectContainer.is(e.target) && SelectContainer.has(e.target).length === 0 || actionContainer > 0 || actionNameContainer > 0) {

            if (SelectContainer.length === 1) {
                $("#btnActionEdit,#btnCopy, #btndelete, #btnCut, #btnConditional ").show();
                if (Parallelcontainer.length) {
                    if (Parallelcontainer.parents('.parentGroup').length === 0) {
                        $('#btnGroup').show()
                    }
                }
            } else {
                $("#btnCopy, #btndelete, #btnCut, #btnConditional").show();
                if (!groupCombineCheck.includes(false) && !parallelCombineCheck.includes(false) && groupBtnCheck === 0) {
                    if (Parallelcontainer.length) {
                        if (Parallelcontainer.parents('.parentGroup').length === 0) {
                            $('#btnGroup').show()
                        }
                    } else {
                        $('#btnGroup').show()
                    }
                }
                if (!parallelCombineCheck.includes(false) && !groupClassParallelCheck.includes(false) && groupBtnCheck === 0 && Parallelcontainer.length === 0) {
                    $('#btnParallel').show();
                    isParallel = true;
                }
                if (!groupActionOnlyCheck.includes(false) && groupBtnCheck === 0 && Parallelcontainer.length === 0) {
                    $('#btnParallel').show();
                    isParallel = true;
                }
            }
        } else if (groupCheck > 0 || groupBtnCheck > 0) {
            $('#btnUnGroup, #btnCopy, #btnCut, #btndelete, #btnConditional, #btnEditGroup').show();
        }

        if (e.target.classList.contains('conditionDiagram')) {
            $('#btnCopy, #btndelete, #btnCut, #btnConditional').hide()
            lastGoToConditional = e.target.parentElement.parentElement.parentElement.id;
            if ($(`#${lastGoToConditional}`).attr('conditionDetails') !== undefined) {
                let condDetails = JSON.parse($(`#${lastGoToConditional}`).attr('conditionDetails'))
                if (condDetails.length < 2) {
                    $('#btnGoToConditional').show();
                    $('#removeConditional').show()
                    if (condDetails[0].condition === 'ifcondition') {
                        $('#removeIfConditional').show()
                        ConditionDetails['If'] = true
                    } else {
                        $('#removeElseConditional').show();
                        ConditionDetails['Else'] = true
                    }
                } else {
                    $('#removeIfConditional').show();
                    $('#removeElseConditional').show();
                    $('#removeConditional').show()
                }
            } else {
                $('#btnGoToConditional').show()
                $('#removeConditional').show()
            }
        }

        if (workflowContainer.children().length < 2) {
            $('#btnConditional').hide();
        }

        //ContextMenu Position
        menu.fadeIn(100);

        let pageX = e.pageX;
        let pageY = e.pageY;
        let mwidth = $(".UlContextBtn").width();
        let mheight = $(".UlContextBtn").height() + 20;
        let screenWidth = $(window).width();
        let screenHeight = $(window).height();
        let scrTop = $(window).scrollTop();

        if (pageX + mwidth > screenWidth) {
            pageX = pageX - mwidth
        }
        if (pageY + mheight > screenHeight + scrTop) {
            pageY = pageY - mheight;
        }
        menu.css({
            top: pageY - 50,
            left: pageX
        });
    }
});
ParentContainer.on("click", function () {
    contextClose();
});

//window.addEventListener('resize', function () {
//    console.log('Window was resized.');
//});

$('#btnActionEdit').on('click', function () {
    let selected = $('#workflowActions .selectedWfContainer')

    if (selected.length) {
        let selectedId = selected.attr('id');

        if (selectedId) {
            actionEditData(selectedId)
            $('#filter_property').dropdown('hide')
        }
    }
})
$("#btnCopy").on("click", function () {
    if ($('.selectedWfContainer').length > 0) {
        copyWorkflowActions();
    }
})

$("#btnPaste").on("click", function () {
    BtnPasteWorkFlow();
    $('#filter_property').dropdown('hide')
});

$("#btnCut").on("click", function () {
    cutWorkflowActions();
});
$("#btnSelectAll").on('click', function () {
    if (!$('.actionCheckBox').is(':visible')) {
        selectAllActions()
        $('#filter_property').dropdown('hide')
    }
});

$("#btnUnselect").on('click', function () {
    unSelectAllActions()
    $('#filter_property').dropdown('hide')
});

$("#btndelete").on("click", function () {
    actionDeletion()
});


const actionDeletion = () => {

    const allSelected = $('#workflowActions').children().length === $('.selectedWfContainer').length

    if (allSelected) {
        const isSingleParallel = $('.selectedWfContainer')?.length == 1 && $('.selectedWfContainer')?.parent()?.hasClass('parallelCont')

        if (!isSingleParallel) {
            notificationAlert('warning', 'Minimum one action is required in the workflow');
            removeSelection();
            return false;
        }
    }

    let condCheck = []
    let selected = $('#workflowActions .selectedWfContainer')
    if (selected.length > 0) {
        selected.each(function (idx, obj) {
            if (obj?.id && ($(`#${obj?.id}`).hasClass('canvasEnable') || $(`#${obj.id}`).parent().next().find('.parentConditionCont').length > 0)) {
                condCheck.push(true)
            }
        })
    }
    if (condCheck.includes(true)) {

        $('#confirmationDeleteText').text('Selected action(s) connected with condition, Remove the condition that is linked to the selected action before delete.')
        $('#confirmationDeleteModal').modal('show');
        $('#filter_property').dropdown('hide');
    } else {
        actionDelete()
    }
}

$("#collapseStatus").on("click", function (e) {
    e.preventDefault();
    if ($('.accordionClass').hasClass('show')) {
        $('.accordionClass').removeClass('show').addClass('collapse')
        $("#collapseStatus").removeClass('cp-circle-uparrow').addClass('cp-circle-downarrow')
    } else {
        $('.accordionClass').addClass('show')
        $("#collapseStatus").removeClass('cp-circle-downarrow').addClass("cp-circle-uparrow");
    }

    $('#filter_property').dropdown('hide')
    contextClose();
    setTimeout(() => {
        checkPositionChanged()
    }, 350)
})

$('#btnParallel').on('click', function () {
    let selected = $('#workflowActions .selectedWfContainer')
    let isSelect = []
    selected.each(function (idx, obj) {
        if ($(`#${obj?.id}`).parent().next().find('.workflowActions.selectedWfContainer').length > 0 || $(`#${obj?.id}`).parent().prev().find('.workflowActions.selectedWfContainer').length > 0) {
            isSelect.push(true)
        } else {
            isSelect.push(false)
        }
    })
    if (isSelect.includes(false)) {
        removeSelection()
        notificationAlert('warning', 'Sequence actions are limited to parallel operations')
    } else {
        if (!checkConditionDiagramExist()) {
            if (!isRunning) {
                if (!$('.selectedWfContainer').filter('[isindependent=true]').length) {
                    ParallelActions();
                } else {
                    removeSelection();
                    notificationAlert('warning', 'Independent actions cannot be permitted to parallel operations.')
                }
               
            } else {
                removeSelection();
                notificationAlert('warning', 'Actions cannot be paralleled while the workflow is running')
            }
            
        } else {       
            notificationAlert('warning', 'To parallel the actions, remove the condition diagram.')
        }
    }
});

$('#btnUnParallel').on('click', function () {
    if (!isRunning) {
        unParallelActions();
    } else {
        removeSelection();
        notificationAlert('warning', 'Actions cannot be sequenced while the workflow is running')
    }
});

$('#btnGroup').on('click', function () {
    if (!checkConditionDiagramExist()) {
        $('#groupName').text('').removeClass('field-validation-error')
        createGroupActions();
    } else {
        removeSelection();
        notificationAlert('warning', 'To group the actions, remove the condition diagram.')
    }
});

$('#btnUnGroup').on('click', function () {
    unGroupActions();
})

const copyWorkflowActions = () => {
    $("#btnPaste").show();
    selectedActions = document.querySelectorAll('#workflowActions .selectedWfContainer');
    let datum = getWorkFlowData('paste');
    localStorage.setItem('copyValue', JSON.stringify(datum))
    contextClose();
    removeSelection();

}

const parallelPaste = async (clone) => {
    let ParallelId = getRandomId("parallel")
    let parallelLength = clone.children.length
    for (let j = 0; j < parallelLength; j++) {
        let nodeId = getRandomId("node")
        let stepId = generateStepId();
        let decodedDetails = atob($(clone.children[j]).attr('details'))
        let cloneDetails = JSON.parse(decodedDetails)
        cloneDetails.actionInfo.uniqueId = nodeId
        cloneDetails.stepId = stepId        
        actionArray.push({ actoinId: nodeId, actionName: cloneDetails.actionInfo.actionName, type: 'parallel', IsParallel: true })
        loadActionsCount(cloneDetails)
        $(clone.children[j]).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails)))
        $(clone).attr("id", ParallelId)
    }
}

const groupPaste = async (clone) => {
    let groupChildren = $('#' + clone.id + ' .accordion-body').children()
    let groupChildrenLength = groupChildren.length;
    let groupId = getRandomId("group")
    let accordionId = getRandomId("accordion")
    let parentId = getRandomId('parent')
    let group_Color = groupColorPallate[Math.floor(Math.random() * groupColorPallate.length)];

    for (let k = 0; k < groupChildrenLength; k++) {
        let childId = k === 0 ? groupChildren[k].children[0].id : groupChildren[k].children[1].id
        if (childId.includes('parallel')) {
            let ParallelId = getRandomId("parallel")
            let childlength = k === 0 ? groupChildren[k].children[0].children.length : groupChildren[k].children[1].children.length;
            let parentIndex = k === 0 ? 0 : 1;
            for (let l = 0; l < childlength; l++) {
                let nodeId = getRandomId('node')
                let stepId = generateStepId();
                if (parentIndex === 0) {
                    let decodedDetails = atob($(groupChildren[k].children[0].children[l]).attr('details'))
                    let cloneDetails = JSON.parse(decodedDetails)
                    cloneDetails.actionInfo.uniqueId = nodeId
                    cloneDetails.stepId = stepId
                    actionArray.push({ actoinId: nodeId, actionName: cloneDetails.actionInfo.actionName, type: 'parallel', IsParallel: true })
                    loadActionsCount(cloneDetails)
                     $(groupChildren[k].children[0].children[l]).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails)))
                     $(groupChildren[k].children[0]).attr("id", ParallelId)
                } else {
                    let decodedDetails = atob($(groupChildren[k].children[1].children[l]).attr('details'))
                    let cloneDetails = JSON.parse(decodedDetails)
                    cloneDetails.actionInfo.uniqueId = nodeId
                    cloneDetails.stepId = stepId
                    actionArray.push({ actoinId: nodeId, actionName: cloneDetails.actionInfo.actionName, type: 'parallel', IsParallel: true })
                    loadActionsCount(cloneDetails)
                     $(groupChildren[k].children[1].children[l]).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails)))
                     $(groupChildren[k].children[1]).attr("id", ParallelId)
                }
            }
        } else {
            let nodeId = getRandomId('node')
            let stepId = generateStepId();
            let decodedDetails = k === 0 ? atob($(groupChildren[k].children[0]).attr('details')) : atob($(groupChildren[k].children[1]).attr('details'))
            let cloneDetails = JSON.parse(decodedDetails)
            cloneDetails.actionInfo.uniqueId = nodeId
            cloneDetails.stepId = stepId            
            actionArray.push({ actoinId: nodeId, actionName: cloneDetails.actionInfo.actionName, type: 'sequential', IsParallel: false })
            loadActionsCount(cloneDetails)
            
            k === 0 ?  $(groupChildren[k].children[0]).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails))) :  $(groupChildren[k].children[1]).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails)))
        }
    }
    $('#' + clone.id + ' .accordionClass').attr('id', accordionId)
    $('#' + clone.id + ' .btnAccordion').attr('data-bs-target', '#' + accordionId)
    $('#' + clone.id + ' .groupClass').attr('id', parentId)
    $('#' + clone.id).attr('id', groupId)
    $('#' + groupId).attr('groupcolor', group_Color)
    $('#' + groupId + " .findGroupClass").css('background-color', group_Color)   
    groupChildren = [];
    groupChildrenLength = 0
}


const pasteWorkflowActions = async (selectedActionsData, index) => {
    let clone;
    if (selectedActionsData.classList.contains('groupClass')) {
        clone = selectedActionsData.parentElement.cloneNode(true);
    } else {
        clone = selectedActionsData.cloneNode(true);
    }

    clone?.classList?.remove('flowLineEnable');
    clone?.removeAttribute('conditionarray');

    if (clone.classList.contains('parallelCont')) {
        await parallelPaste(clone)
    } else if (clone.classList.contains('parentGroup')) {
        await groupPaste(clone)
    } else {
        let nodeId = getRandomId('node')
        let stepId = generateStepId();
        let details = atob($(clone).attr('details'))
        let cloneDetails = JSON.parse(details)
        cloneDetails.actionInfo.uniqueId = nodeId
        cloneDetails.stepId = stepId;
        
       
        actionArray.push({ actoinId: nodeId, actionName: cloneDetails.actionInfo.actionName, type: 'sequential', IsParallel: false })
        loadActionsCount(cloneDetails)       
        $(clone).attr("id", nodeId).attr("details", btoa(JSON.stringify(cloneDetails)))

       
    }
    let di = $(clone).attr("id")
    if ($('.selectedWfContainer.workflowActions').length <= 0) {
        workflowContainer.append($('<div class="ui-sortable-handle"></div>').append(connectImage, clone));
        removeSelection();
    } else {
        if ($('#' + di).parent().attr('id') && $('#' + di).parent().attr('id').includes('parallel')) {
            $('#' + di).parent().append(clone)
        } else {
            $(".selectedWfContainer.workflowActions").last().parent().after($('<div class="ui-sortable-handle"></div>').append(connectImage, clone));
        }
        if (index == selectedActions?.length - 1) removeSelection();
    }
    updateIndexValues()
    let actionCount = $('.workflowActions').length
    $("#actionsCount").text(actionCount)
}

const reCopyActionContainer = (arr) => {
    arr.forEach((d) => {
        if (d?.includes('group')) {
            $(`#${d} .groupClass`).addClass('selectedWfContainer')
        } else if (d?.includes('parallel')) {
            $(`#${d}.parallelCont`).addClass('selectedWfContainer')
        } else if (d?.includes('node')) {
            $(`#${d}.workflowActions`).addClass('selectedWfContainer')
        }
    })
    //selectedActions = document.querySelectorAll('#workflowActions .selectedWfContainer');
    removeSelection();
}


const BtnPasteWorkFlow = async () => {
    workflowLoader('on')
    if (!selectedActions.length && localStorage.getItem('copyValue')) {
        fromAnotherTab = JSON.parse(localStorage.getItem('copyValue'))
        let workflowDataLength = fromAnotherTab.length;
        for (let i = 0; i < workflowDataLength; ++i) {
            let parallelId = '';
            if (fromAnotherTab[i].hasOwnProperty('groupName')) {
                fromAnotherTab[i].groupActions.forEach((d) => {
                    let groupNodeId = getRandomId('node')
                    d.actionInfo.uniqueId = groupNodeId
                    d.stepId = generateStepId()
                })
                tempActionsArray.push(fromAnotherTab[i].groupId)
            } else if (fromAnotherTab[i].hasOwnProperty('children')) {
                parallelId = getRandomId("parallel")
                fromAnotherTab[i].children.forEach((d) => {
                    let parallelNodeId = getRandomId('node')
                    d.actionInfo.uniqueId = parallelNodeId
                    d.stepId = generateStepId()
                })             
                tempActionsArray.push(parallelId)
            } else {
                let nodeId = getRandomId('node')
                fromAnotherTab[i].actionInfo.uniqueId = nodeId
                fromAnotherTab[i].stepId = generateStepId();
                tempActionsArray.push(nodeId)
            }
            
            setTimeout(async () => {
                conditionalAppending(fromAnotherTab[i], i, parallelId)
            }, 40)
            if (workflowDataLength - 1 === i) {
                    setTimeout(() => {
                        reCopyActionContainer(tempActionsArray)
                        if (!$('#workflowTitle').text().length) {
                            $('#workflowTitle').text('Untitled Workflow')
                            //selectAllActions()
                            //selectedActions = document.querySelectorAll('#workflowActions .selectedWfContainer');
                            //setTimeout(() => {
                            //    unSelectAllActions();
                            //    localStorage.removeItem('copyValue')
                            //},200)
                            
                        }
                    },400)
            }         
        }   
        $('#btnSaveModalOpen').prop('disabled', false);  
      
        $(".checkSaveWorkflow").show()           
    } else {
        $('#AISuggestion').remove()
        $('#offcanvasScrolling').offcanvas('hide')  
        
        let selectedActionsLength = selectedActions.length
        for (let i = 0; i < selectedActionsLength; i++) {
            setTimeout(async () => {
                await pasteWorkflowActions(selectedActions[i], i)
            }, 30)
        }      
    }   
    $('.workflowCanvas').attr('height', $('#workflowActions').height())
    $('.workflowCanvas').attr('width', $('#workflowActions').width())
    setTimeout(() => {
        checkPositionChanged();       
    }, 200)
    checkGroupCount();
  
    $('#btnSaveModalOpen').prop('disabled', false);
    //if (!$('#workflowTitle').text().length) {
    //    $('#workflowTitle').text('Untitled Workflow')
    //}
    $(".checkSaveWorkflow").show()
    workflowLoader('off')
}

const cutWorkflowActions = () => {
    let selectedAction = $('.selectedWfContainer')
    selectedActions = document.querySelectorAll('.selectedWfContainer');
    selectedAction.each(function (idx, obj) {
        if (obj.classList.contains('groupClass')) {
            let getGroupId = obj.parentElement.id
            $('#' + getGroupId).prev().remove()
            $('#' + getGroupId).unwrap()
            $('#' + getGroupId).remove()
        } else {
            $('#' + obj.id).prev().remove()
            $('#' + obj.id).parent().remove();
            $('#' + obj.id).remove()
        }
    });
    $("#btnPaste").show()
    $('#filter_property').dropdown('hide');
    $(".workflowActions").removeClass("selectedWfContainer");
    contextClose();
    updateIndexValues();  
    $(".checkSaveWorkflow").show();
    checkPositionChanged();
}

const actionDelete = () => {
    if ($('#workflowActions .selectedWfContainer').length > 0) {
        $('#deleteActionsModal').modal('show');
        $('#filter_property').dropdown('hide');
        if ($('#workflowActions .selectedWfContainer').length > 1) {
            $('#ActionDeleteText').text('Are you sure you want to delete these selected actions?')
        } else {
            if ($('#workflowActions .selectedWfContainer').hasClass('groupClass')) {
                $('#ActionDeleteText').text('Are you sure you want to delete this group?')
            } else if ($('#workflowActions .selectedWfContainer')[0].classList.contains('parallelCont')) {
                $('#ActionDeleteText').text('Are you sure you want to delete these parallel actions?')
            } else {
                $('#ActionDeleteText').text('Are you sure you want to delete this action?')
            }
        }
    }
}

const conditionalDeleteCheck = () => {
    $('#deleteActionsModal').modal('show');
    $('#filter_property').dropdown('hide');
    $('#ActionDeleteText').text('Condition made with this action. Are you sure you want to delete these selected actions?')
}

const selectAllActions = () => {
    $('.workflowActions').each(function (idx, obj) {
        if ($(obj).parents('.parallelCont').length !== 0) {
            if ($(obj).parents('.groupClass').length === 0) {
                let parallelId = $(obj).parents('.parallelCont')[0].id
                $("#" + parallelId).addClass("selectedWfContainer")
            }
        } else if ($(obj).parents('.groupClass').length !== 0) {
            $(".groupClass").addClass("selectedWfContainer")
        }
        else {
            $(obj).addClass("selectedWfContainer");
        }
    });
    contextClose();
}

const unSelectAllActions = () => {
    removeSelection();
    contextClose();
}

const ParallelActions = () => {
    let ParallelId = getRandomId("parallel")
    $('.selectedWfContainer').prev().remove()
    $('.selectedWfContainer').unwrap()
    $('.selectedWfContainer').wrapAll("<div id=" + ParallelId + " class='parallelCont selectedWfContainer parallel-icon gap-2'></div>")
    $('#' + ParallelId).wrapAll('<div class="ui-sortable-handle"></div>')
    if ($('#' + ParallelId).parents('.parentGroup').length > 0) {
        let findId = $('#' + ParallelId).parents('.parentGroup')[0].id
        if (!$('#' + findId + ' .accordion-body').children().first().children().hasClass('selectedWfContainer')) {
            $('#' + ParallelId).parent().prepend(connectImage)
        }
    } else {
        $('#' + ParallelId).parent().prepend(connectImage)
    }
    $('#' + ParallelId).removeClass('selectedWfContainer')
    $(".workflowActions").removeClass("selectedWfContainer")
    contextClose();
    $(".checkSaveWorkflow").show();
    $('#filter_property').dropdown('hide')
    checkPositionChanged();
}

const deleteAction = () => {
    $('.selectedWfContainer').each(function (idx, obj) {
        if ($(obj).parents('.parallelCont').length !== 0 || obj.classList.contains('parallelCont')) {
            $('#' + obj.id).find('.workflowActions').each(function (index, element) {
                loadActionsCount(JSON.parse(atob($('#' + element.id).attr('details'))), 'delete')
            });
            if ($('#' + obj.id).parent('.parallelCont').children().length < 3) {
                if ($('#' + obj.id).hasClass('parallelCont')) {
                    $('#' + obj.id).parent().remove();
                } else {
                    let parallelId = $('#' + obj.id).parent('.parallelCont')[0].id
                    $('#' + obj.id).remove()
                    $('#' + parallelId).addClass('selectedWfContainer')
                    unParallelActions()
                }
            } else {
                $('#' + obj.id).remove()
            }
        } else if ($(obj).parents('.parentGroup').length !== 0) {
            let getGroupId = $(obj).parents('.parentGroup')[0].id
            $('#' + obj.id).find('.workflowActions').each(function (index, element) {
                loadActionsCount(JSON.parse(atob($('#' + element.id).attr('details'))), 'delete')
            });
            if ($(obj).hasClass('groupClass')) {
                $('#' + getGroupId).parent().remove()
            } else if ($(obj).parents('.parentGroup').find('.workflowActions').length == 1) {
                $('#' + obj.id).parent().remove()
                $('#' + getGroupId).parent().remove()

            } else {
                $('#' + obj.id).parent().remove()
            }
        } else if (obj.classList.contains('workflowActions') && $(obj).hasClass('workflowActions')) {
            loadActionsCount(JSON.parse(atob($('#' + obj.id).attr('details'))), 'delete')
            $('#' + obj.id).parent().remove()
        }
    });
    checkPositionChanged()
    checkGroupCount();
    updateIndexValues();
    let actionCount = $('.workflowActions').length
    $("#actionsCount").text(actionCount)
    $('#deleteActionsModal').modal('hide')
    $(".checkSaveWorkflow").show();
    let tempArrs = [];

    $(".workflowActions").each(function () {
        if (!tempArrs.includes($(this).attr('parentId'))) {
            tempArrs.push($(this).attr('parentId'))
        }
    });

    $("#filter_property ul").children('li').each(function () {
        let checkboxValue = $(this).find('input[type="checkbox"]').val();
        if (checkboxValue) {
            if (!tempArrs.includes(checkboxValue) && checkboxValue) {
                $(this).remove()
                return
            }
        }
    });
    if (workflowContainer.children().length === 0) clearWFFields()
    contextClose();
}

$(document).on('click', '.parallelCont', function (e) {
    if (!$('.actionCheckBox').is(':visible')) {
        if (!e.shiftKey) {
            if ($("#" + this.id).hasClass("selectedWfContainer")) {
                $("#" + this.id).removeClass("selectedWfContainer")
                $('.parellelCont').removeClass("selectedWfContainer")
            } else if (e.ctrlKey) {
                $("#" + this.id).addClass("selectedWfContainer");
            } else {
                $('.parallelCont').removeClass('selectedWfContainer');
                $('.groupClass').removeClass("selectedWfContainer");
                $('.workflowActions').removeClass("selectedWfContainer");
                $("#" + this.id).addClass("selectedWfContainer");
            }
        } else {
            if ($('.selectedWfContainer').length > 0 && $('#' + this.id).parents('.groupClass')) {
                let initialElementId = $('.selectedWfContainer').last().attr('id')
                let initialElementId1 = $('.selectedWfContainer').first().attr('id')
                let lastSelectedlength = $('#' + initialElementId).parents().index()
                let firstSelectedlength = $('#' + initialElementId1).parents().index()
                let targetSelectedlength = $('#' + this.id).parents().index()
                let totalWorkflowActions = $('#workflowActions')
                if (lastSelectedlength < targetSelectedlength) {
                    for (let i = lastSelectedlength + 1; i <= targetSelectedlength; i++) {
                        let selectedId = totalWorkflowActions.children()[i].children[1].id
                        if (selectedId.includes('group')) {
                            $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                        } else {
                            $('#' + selectedId).addClass('selectedWfContainer')
                        }

                    }
                } else if (firstSelectedlength > targetSelectedlength) {

                    for (let i = targetSelectedlength; i <= firstSelectedlength - 1; i++) {
                        let selectedId = totalWorkflowActions.children()[i].children[1].id
                        if (selectedId.includes('group')) {
                            $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                        } else {
                            $('#' + selectedId).addClass('selectedWfContainer')
                        }
                    }
                }
            } else {
                $("#" + dd).addClass("selectedWfContainer")
            }
        }
    }
    e.stopPropagation();
});

workflowContainer.on("click", function (e) {
    let Parentcontainer1 = $("#workflowActions")
    let Parentcontainer = $(".ui-sortable-handle");
    if (Parentcontainer.is(e.target) && Parentcontainer.has(e.target).length === 0 || Parentcontainer1.is(e.target) && Parentcontainer1.has(e.target).length === 0) {
        removeSelection();
        contextClose();
    }
});

$(document).on('click', '.groupClass', function (e) {
    if (!$('.actionCheckBox').is(':visible')) {
        if (!e.shiftKey) {
            if ($("#" + this.id).hasClass("selectedWfContainer")) {
                $("#" + this.id).removeClass("selectedWfContainer");
                removeSelection();
            } else if (e.ctrlKey) {
                $("#" + this.id).addClass("selectedWfContainer")
            } else {
                removeSelection();
                $("#" + this.id).addClass("selectedWfContainer")
            }
        } else {
            if ($('.selectedWfContainer').length > 0) {

                let initialElementId = $('.selectedWfContainer').last().attr('id')
                let initialElementId1 = $('.selectedWfContainer').first().attr('id')
                let lastSelectedlength = initialElementId.includes('parent') ? $('#' + initialElementId).parent().parents().index() : $('#' + initialElementId).parents().index()
                let firstSelectedlength = initialElementId1.includes('parent') ? $('#' + initialElementId1).parent().parents().index() : $('#' + initialElementId1).parents().index()
                let targetSelectedlength = $('#' + this.id).parent().parents().index()
                let totalWorkflowActions = $('#workflowActions')
                if (lastSelectedlength < targetSelectedlength) {
                    for (let i = lastSelectedlength + 1; i <= targetSelectedlength; i++) {
                        let selectedId = totalWorkflowActions.children()[i].children[1].id
                        if (selectedId.includes('group')) {
                            $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                        } else {
                            $('#' + selectedId).addClass('selectedWfContainer')
                        }
                    }
                } else if (firstSelectedlength > targetSelectedlength) {
                    for (let i = targetSelectedlength; i <= firstSelectedlength - 1; i++) {
                        let selectedId = totalWorkflowActions.children()[i].children[1].id
                        if (selectedId.includes('group')) {
                            $('#' + selectedId + ' .groupClass').addClass('selectedWfContainer')
                        } else {
                            $('#' + selectedId).addClass('selectedWfContainer')
                        }
                    }
                }
            } else {
                $("#" + this.id).addClass("selectedWfContainer")
            }
        }
    }
    e.stopPropagation()
});

const unParallelActions = () => {
    let selectedLength = $('.selectedWfContainer').children().length;
    let actionList = $('.selectedWfContainer').children();

    for (let i = 0; i < selectedLength; i++) {

        $('#' + actionList[i].id).wrapAll('<div class="ui-sortable-handle"></div>')
        setTimeout(() => {
            $('#' + actionList[i].id).before(connectImage)
        }, 100)
    }
    setTimeout(() => {
        if ($('.selectedWfContainer').parents('.parentGroup').length > 0) {

            let findId = $('.selectedWfContainer').parents('.parentGroup')[0].id
            if ($('#' + findId + ' .accordion-body').children().first().children().hasClass('selectedWfContainer')) {
                $('.selectedWfContainer').children().first().children().first().remove()
            }
        }
    }, 150)
    setTimeout(() => {
        $('.selectedWfContainer').children().first().parent().prev().remove()
        $('.selectedWfContainer').children().unwrap().unwrap();
        $(".checkSaveWorkflow").show();
        $('#filter_property').dropdown('hide')
        checkPositionChanged();
    }, 200) 
}

function GenerateGroupName(group_name, group_color, parentId) {
    let Accordian = '<div class="accordion groupClass Workflow-Digram-Accordion" id=' + parentId + '><div class="accordion-item">'
        + ' <div class="accordion-header">'
        + ' <button class="accordion-button btnGroup col" type="button">'
        + ' <div class="flex-fill groupHeaderContainer text-truncate">'
        + ' <i class="cp-group fs-7 me-2 findGroupClass d-inline-flex" style = "background-color: ' + group_color + '" ></i >'
        + ' <span id="groupNameText" class="text-truncate" style="max-width: 80px;" title="' + group_name + '">' + group_name + '</span>'
        + ' </div >'
        + ' <div class="w-auto d-flex">'
        + ' <input type="checkbox" parentId=' + parentId + ' class="groupCheckBox align-middle me-2 " name="groupCheck" />'
        + ' <i class="cp-circle-downarrow p-0 me-2 text-dark collapsed btnAccordion"  data-bs-toggle="collapse"  aria-expanded="false"></i>'
        + ' </div >'
        + ' </button>'
        + ' </div>'
        + ' <div  class="accordion-collapse show accordian accordionClass" aria-labelledby="headingTwo">'
        + ' <div class="accordion-body pt-0">'
        + ' </div>'
        + ' </div>'
        + ' </div></div>'

    return Accordian
}

const createGroupActions = () => {
    $('#actionGroupName-error').removeClass('field-validation-error').text('');
    $('#groupName').val('');
    $('#CreateGroupNameModal').modal('show');
    $("#groupNameSave").text("Save");
    $('#filter_property').dropdown('hide');
    contextClose();
    setTimeout(() => {
        $('#groupName').trigger('focus');
    },400)
    
}

const unGroupActions = () => {
    let groupId = $('.selectedWfContainer').parent()[0].id
    if ($('#' + groupId).hasClass('canvasEnable')) {
        notificationAlert('warning', 'Group can not be ungrouped, Remove the condition that is linked to the selected group before ungroup.')
        $('#filter_property').dropdown('hide');
        $('#' + groupId + ' .groupClass').removeClass('selectedWfContainer')
        return false;
    } else {
        $('#' + groupId + ' .groupClass').removeClass('selectedWfContainer')
        $("#" + groupId + " .accordion-body").children().each(function (idx, obj) {
            if (idx === 0) {
                let id = obj.children[0].id
                $("#" + id).addClass('selectedWfContainer')
            } else {
                let id = obj.children[1].id
                $("#" + id).addClass('selectedWfContainer')
            }
        })
        $('#' + groupId).prev().remove()
        $('#' + groupId + ' .selectedWfContainer').prev().remove();
        $('#' + groupId + ' .accordion-button').unwrap().unwrap().unwrap().remove();
        let selectedLength = $('.selectedWfContainer').length;
        let actionList = $('.selectedWfContainer');
        $('#' + groupId + ' .selectedWfContainer').unwrap().unwrap().unwrap().unwrap().unwrap();
        for (let i = 0; i < selectedLength; i++) {
            $('#' + actionList[i].id).wrapAll('<div class="ui-sortable-handle"></div>')
        }
        $('.selectedWfContainer').before(connectImage)
        checkGroupCount();
        $('.workflowActions').removeClass('selectedWfContainer')
        $('.parallelCont').removeClass("selectedWfContainer");
        $(".checkSaveWorkflow").show();
        $('#filter_property').dropdown('hide');
        removeGroupName(groupId)
        groupArray = groupArray.filter((d) => d.groupId !== groupId)
        checkPositionChanged();
    }
}

$('#btnEditGroup').on('click', function () {
    globalEditGroupId = $('.selectedWfContainer').parent()[0].id
    $('#groupName').val($("#" + globalEditGroupId + " #groupNameText").text())
    $('#CreateGroupNameModal').modal('show');
    contextClose();
    $("#groupNameSave").text("Update");
    $('#filter_property').dropdown('hide')
    $(".groupClass").removeClass("selectedWfContainer")
    $('#actionGrouptype-error').removeClass('field-validation-error').text('');
    removeGroupName(globalEditGroupId)
})

const removeGroupName = (globalEditGroupId) => {
    let indexVal = globalGroupArray.indexOf($("#" + globalEditGroupId + " #groupNameText").text());
    globalGroupArray.splice(indexVal, 1);
}

$('#groupNameSave').on('click', function () {
    let groupValidation = partialValidation($('#groupName').val(), 'groupName', 'actionGroupName-error', 'Enter group name')
    if (groupValidation) {
        if ($('#groupNameSave').text() == "Update") {
            $("#" + globalEditGroupId + " #groupNameText").text($('#groupName').val())
            $('#' + globalEditGroupId).attr('groupName', $('#groupName').val())
            $('#CreateGroupNameModal').modal('hide');
            $("#groupNameSave").text("Save")          
            $(".checkSaveWorkflow").show();
        } else {
            let group_name = $('#groupName').val();
            let group_Color = groupColorPallate[Math.floor(Math.random() * groupColorPallate.length)];
            let groupId = getRandomId('group')
            let accordionId = getRandomId("accordion")
            let parentId = getRandomId('parent')
            $('.selectedWfContainer').parent().wrapAll("<div id=" + groupId + " groupName=" + group_name + " groupColor=" + group_Color + " class='parentGroup'>" + GenerateGroupName(group_name, group_Color, parentId) + "</div>")
            $('#' + groupId + ' .accordion-body').append($('.selectedWfContainer').parent())
            $('#' + groupId + ' .accordionClass').attr('id', accordionId)
            $('#' + groupId + ' .btnAccordion').attr('data-bs-target', '#' + accordionId)
            $('#' + groupId + ' .selectedWfContainer').first().prev().remove()
            $('#' + groupId + ' .groupClass').removeClass('selectedWfContainer')
            $('#' + groupId).wrapAll('<div class="ui-sortable-handle"></div>')
            $('#' + groupId).before(connectImage)
            setTimeout(() => {
                $('#' + groupId + ' .workflowActions').removeClass('selectedWfContainer')
                $('#' + groupId + ' .parallelCont').removeClass("selectedWfContainer")
                checkGroupCount();
                contextClose();
            }, 100)
            $('.groupCheckBox').prop('checked', false);
            $(".groupCheckBox").hide();
            $('#filter_property').dropdown('hide');
            $(".checkSaveWorkflow").show();
            $('#CreateGroupNameModal').modal('hide');
            globalGroupArray.push(group_name)
            groupArray.push({ groupId: groupId, groupName: group_name, type: 'group' })
            checkPositionChanged();

        }
    }
})

//$('#groupName').on('keyup', function () {
//    const value = $("#groupName").val();
//    GroupnameValidate(value);
//})
//function GroupnameValidate(value) {
//    const errorElement = $('#actionGroup-error')
//    if (!value) {
//        errorElement.text('Enter group name')
//            .addClass('field-validation-error')
//        return "true";
//    } else {
//        errorElement.removeClass('field-validation-error').text('');
//        return "false";
//    }
//}

$(document).on('click', '.btnAccordion', function () {
    setTimeout(() => {
        checkPositionChanged()
    },350)
})

