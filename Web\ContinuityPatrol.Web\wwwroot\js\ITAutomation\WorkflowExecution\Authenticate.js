﻿let isPasswordEncrypted = false;
let isCustomExecution = false;

function WFExeEventDebounce(func, timeout = 300) {
    let timer;
    return function (...args) {
        if (!timer) {
            func.apply(this, args);
        }
        clearTimeout(timer);
        timer = setTimeout(function () {
            timer = undefined;
        }, timeout);
    };
}

$(document).on('click', '.btnStart', WFExeEventDebounce(async function () {
    isCustomExecution = false;
    infraObjectStatus = [];
    let activeInfraObjectName = '';
    let getProfileId = $(this).parents('.profileContainer')[0].id
    globalProfileId = getProfileId

    if ($(`#${getProfileId} .profileChildCheckBox:checked`).length) {
        let getWorkflowContainer = $(`#${getProfileId} .workflowListContainer`);
        let foundCustom = getWorkflowContainer?.length &&
            Array.from(getWorkflowContainer)?.some(obj => $(obj)?.attr('isCustom') === 'true');
        let tableData = '';


        getWorkflowContainer.each((idx, obj) => {
            let workflowId = $(obj).attr('id')
            if ($(`#${workflowId + ' .profileChildCheckBox'}`).prop('checked')) {
             
                let workflowName = $(obj).attr('workflowName')
                let isParallel = $(obj).attr('isParallel')
                let isCustom = $(obj).attr('isCustom')
                let tempId = $(obj).attr('tempId')
                let state = $(obj).attr('state')

                if (isCustom == 'true') {
                    isCustomExecution = true
                }

                if (state?.toLowerCase() === 'active') {
                    let infraObjectId = $(`#${workflowId} .infraObjectContainer`).attr('id')
                    let infraObjectName = $(`#${workflowId} .infraObjectContainer`).text()
                    infraObjectStatus.push(infraObjectId)
                    activeInfraObjectName += infraObjectName + ','
                }

                tableData += `<tr class='selectedWorkflowContainer' workflowId="${workflowId}"><td>${idx + 1}</td><td class='text-truncate' title='${workflowName}'>${workflowName}</td>`

                tableData += `<td class="modeSelection"><div class='form-check form-check-inline' id="${'auto_' + workflowId}">
                                <input class='form-check-input workflowMode auto' value='Auto' type='radio' checked>
                                <label class='form-check-label' for="${'auto_' + workflowId}">Auto</label>
                                </div> 
                                <div class='form-check form-check-inline' id="${'step_' + workflowId}">
                                <input class='form-check-input workflowMode step' value='Step' type='radio' ${isParallel === 'true' ? 'disabled' : ''} >
                                <label class='form-check-label' for="${'step_' + workflowId}">Step</label>
                                </div>
                                <div class='form-check form-check-inline' style="display:none">
                                <input class='form-check-input workflowMode simulate' value='Simulate' type='radio'  id='${'simulate_' + workflowId}'>
                                <label class='form-check-label' for='${'simulate_' + workflowId}'>Simulate</label>
                                </div></td>`

                tableData += `<td class='checkboxDatatable text-center ${!foundCustom ? 'd-none' : ''}'>
                              ${isCustom === 'true' ? `<div class='form-check' > <input class='form-check-input checkboxData' id='${' custom_' + workflowId}' mode = "Auto" tempId = '${tempId}' type = 'checkbox' ${ isCustom == 'true' ? 'checked' : '' }/>` : ''}
                              </div></tr>`
            }
           
        })

        $('#workflowModeContainer').empty().append(tableData)

        let getProfileName = $(`#${getProfileId} .profileNameClass`).text()
        //setDatetoDescription(getProfileName)
       
        $(".toggle-password").removeClass().addClass('input-group-text toggle-password');
        if (infraObjectStatus.length) {
            activeInfraObjectName = activeInfraObjectName.slice(0, activeInfraObjectName.length - 1)
            $('#infraNameDetails').text(activeInfraObjectName)
            $('#ConfimationModal').modal("show")
        } else {
            //if (isCustomExecution) {
            //    $('#customConfirmationModal').modal('show')
            //} else {

            //}
            await getServerDateTime(getProfileName)
           // setDatetoDescription(getProfileName);
            $('#AuthendicateModal').modal('show');
            
        }

        setTimeout(() => {
            if (isCustomExecution) {
                $('#custumExecutionAuthContainer').removeClass('d-none');
            } else {
                $('#custumExecutionAuthContainer').addClass('d-none');
            }              
            $('#txtPassword').val('').trigger('focus')
            if ($('#workflowModeContainer').children().length <= 1) {
                $('#modeSelectionContainer').hide()
            } else {
                $('#modeSelectionContainer').show()
            }
        }, 300)

    } else {
        notificationAlert('warning', 'Choose the workflow to execute.')
    }
}, 800))

$(document).on("keyup", function (event) {
    if (event.key === 'Enter' && $('#AuthendicateModal').is(':visible')) {
        setTimeout(() => {
            $("#ProceedBtn").trigger('click')
        },800)
       
    }
})

$(document).on('click', '.workflowMode', function () {
    $(this).parents('.modeSelection').find('.workflowMode').prop('checked', false)
    $(this).prop('checked', true)
})

const getServerDateTime = async (getProfileName) => {

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getServerDateTime,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result.success) {
                let formattedDateAndTime = formatDate(result?.data)
                $('#txtDescription').val(`${getProfileName} ${formattedDateAndTime}`)
            }
        }
    })
}

function formatDate(dateString) {
    const date = new Date(dateString);

    const day = String(date?.getDate())?.padStart(2, '0');
    const month = String(date?.getMonth() + 1)?.padStart(2, '0'); 
    const year = date?.getFullYear();

    let hours = date?.getHours();
    const minutes = String(date?.getMinutes())?.padStart(2, '0');
    const seconds = String(date?.getSeconds())?.padStart(2, '0');

    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;
    const formattedHours = String(hours)?.padStart(2, '0');
    
    return `${day}-${month}-${year}, ${formattedHours}:${minutes}:${seconds} ${ampm}`;
}


const srvTime = () => {
    try {
        xmlHttp = new XMLHttpRequest();
    }
    catch (err1) {
        try {
            xmlHttp = new ActiveXObject('Msxml2.XMLHTTP');
        }
        catch (err2) {
            try {
                xmlHttp = new ActiveXObject('Microsoft.XMLHTTP');
            }
            catch (err3) {
                notificationAlert('warning', "Error occured while fetching the server time")
                return false;
            }
        }
    }
    xmlHttp?.open('HEAD', window.location.href.toString(), false);
    xmlHttp?.setRequestHeader("Content-Type", "text/html");
    xmlHttp?.send('');
    return xmlHttp?.getResponseHeader("Date");
}

const setDatetoDescription = (profileName) => {
  
    let dateTime = new Date(srvTime());
    let date = dateTime.toLocaleString('en-GB', {
        day: 'numeric',
        month: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true
    }).replace(/am|pm/i, match => match.toUpperCase());

    let [datePart, timePart] = date?.split(', ');
    let [day, month, year] = datePart?.split('/');
    let reformattedDate = `${day}-${month}-${year}, ${timePart}`;
    $('#txtDescription').val(profileName + " " + reformattedDate)
}

$("#CancelBtn").on('click', function () {
    clearPassword()
    $("#AuthendicateModal").hide()
});

$('#txtDescription').on('input', async function (e) {
    var $this = $(this);
    //$(this).val($this.val().replace(/(\s{2,})|[^a-zA-Z0-9_']/g, ' ').replace(/^\s*/, ''));
    let displayName = $this.val()
    await validateName(displayName);
});

$("#txtPassword").on('keyup', async function (e) {
    if ($("#txtPassword").val().length > 64) {
        $('#txtPassword').val('');
    }
    $("#txtPassword-error").text('').removeClass('field-validation-error')
});

$("#txtPassword").on('focus', async function () {
    $(".toggle-password").addClass('fs-6 cp-password-visible').attr('title', 'Show Password');
});


$("#txtPassword").on('blur', async function (e) {
    e.stopPropagation()
    let password = $(this).val();
    if (password !== "" && password.length > 0 && password.length < 64) {
        try {
            let passwordBlur = await EncryptPassword(password, "#" + this.id);
            $(this).val(passwordBlur);
            /*isPasswordEncrypted = true;*/
            $(this).attr('type', 'password');
            $(".toggle-password").removeClass('fs-6 cp-password-hide').addClass('fs-6 cp-password-visible').attr('title', 'Show Password');
        } catch (error) {
            console.error("Error occurred during encryption: ", error);
        }
    }

});

$(".toggle-password").on('click', async function (e) {
    e.stopPropagation();
    let password = $('#txtPassword').val();
    let showPassword = $(this).hasClass('cp-password-visible');
    let passwordInput = $('#txtPassword')
    if (showPassword) {
        if (password) {
            try {
                let passwordBlur = await DecryptPassword(password, "#" + this.id);
                passwordInput.val(passwordBlur);
                //isPasswordEncrypted = false;
                passwordInput.attr('type', 'text');
                $(this).removeClass('fs-6 cp-password-visible').addClass('fs-6 cp-password-hide').attr('title', 'Hide Password');
            } catch (error) {
                console.error("Error occurred during encryption: ", error);
            }
        }
    } else {
        if (password) {
            try {
                let passwordBlur = await EncryptPassword(password, "#" + this.id);
                passwordInput.val(passwordBlur);
                //isPasswordEncrypted = true;
                passwordInput.attr('type', 'password');
                $(this).removeClass('fs-6 cp-password-hide').addClass('fs-6 cp-password-visible').attr('title', 'Show Password');
            } catch (error) {
                console.error("Error occurred during encryption: ", error);
            }
        }
    }

});

function clearPassword() {
    var password = $("#txtPassword").val();
    if (password != "" && password.length > 0) {
        $("#txtPassword").val('');
    }
}


async function validateDiscription(value) {
    const errorElement = $('#txtDescription-error');

    if (!value) {
        errorElement.text('Enter description name')
            .addClass('field-validation-error');
        return false;
    }

    const validationResults = [
        ShouldNotBeginWithUnderScore(value),
        OnlyNumericsValidate(value),
        SpaceWithUnderScore(value),
        ShouldNotEndWithUnderScore(value),
        MultiUnderScoreRegex(value),
        SpaceAndUnderScoreRegex(value),
        secondChar(value),
    ];

    return CommonValidation(errorElement, validationResults);
}

$(document).on('change', '.execution_type', function (e) {
    if (e.target.checked) {
        let getId = this.id
        if (getId === 'executionMode') {
            $('.workflowMode.simulate').parent().hide()
            $('.workflowMode.auto, .workflowMode.step').parent().show()
            $('.workflowMode.auto').attr('checked', true)
            if ($('.selectedWorkflowContainer').length > 1) {
                $('#modeSelectionContainer').show()
            }
            $('.simulationMode').hide()
        } else {
            $('.workflowMode.simulate').attr('checked', true)
            $('#modeSelectionContainer').hide()
            $('.workflowMode.auto, .workflowMode.step').attr('checked', false)
            $('.workflowMode.auto, .workflowMode.step').parent().hide()
            $('.workflowMode.simulate').parent().show()
        }
    }
})

$('#allModeOperation').on('change', function (e) {
    $('.workflowMode.auto, .workflowMode.step').attr('checked', false)
    if (e.target.value === '2') {
        $('.workflowMode.step').not(':disabled').attr('checked', true)
    } else {
        $('.workflowMode.auto').attr('checked', true)
    }
})

$('#customConfirmation').on('click', function () {
    $('#customConfirmationModal').modal('hide')
    isCustomExecution = false
    setTimeout(() => {
        $("#ProceedBtn").trigger('click')
    }, 200)

})

$('#CancelCustomConfirmation').on('click', function () {
    $('#customConfirmationModal').modal('hide')
    setTimeout(() => {
        $("#AuthendicateModal").modal('show');
    }, 300)
})

$("#ProceedBtn").on('click', WFExeEventDebounce(async function () {
    let password = $('#txtPassword').val()
    let description = $('#txtDescription').val();

    let isDescription = await validateDiscription(description);

    if (password && isDescription) {
        if (password?.length < 64) {
            password = await EncryptPassword(password);
            $('#txtPassword').attr('type', 'password').val(password)
        }
    } else {
        $("#txtPassword-error").text('Please enter the password').addClass('field-validation-error')
        return false
    }

    setTimeout(async () => {
        if (password?.length > 64) {
            if (isCustomExecution) {
                $("#AuthendicateModal").modal('hide');
                setTimeout(() => {
                    $('#customConfirmationModal').modal('show')
                },300)
            } else {
                await verifyProfile(password, description)
            }
            
        }
    }, 800)


}, 800));


const verifyProfile = async (password, description) => {
    let data = { profileId: globalProfileId, password: password, description: description }

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.verifyProfile,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result) {
                getProfileInfo(description);
            } else {
                $('#txtPassword').attr('type', 'password');
                $(".toggle-password").removeClass('fs-6 bi-eye-slash').addClass('fs-6 bi-eye').attr('title', 'Show Password');
                $("#txtPassword-error").text('Please enter valid password').addClass('field-validation-error')
                    
            }
        }
    })
}

const getProfileInfo = async (description) => {
    let execution_type = $('.execution_type:checked').val();
    let workflowData = {}
    let data = { profileId: globalProfileId }

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getWorkflowsByProfileId,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result) {
                workflowData.profileId = result.id,
                    workflowData.profileName = result.name,
                    workflowData.password = result.password,
                    workflowData.companyId = "",
                    workflowData.description = description
                workflowData.runMode = execution_type

                if (workflowData) {
                    getProfileInfoList(workflowData)
                }
            } else {
                errorNotification(result)
            }
        }
    })
}

const getProfileInfoList = async (workflowData) => {

    $('#proceedBtnLoader')?.removeClass('d-none')
    $('#ProceedBtn')?.addClass('next-disabled')
    
    let jsonWorkflowDetails = [];
    let data = { profileId: globalProfileId }

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getWorkflowListByProfileId,
        data: data,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result?.workflowProfileInfos && result?.workflowProfileInfos?.length) {
                let profileData = result?.workflowProfileInfos
                let length = profileData?.length
                for (let i = 0; i < length; i++) {
                    let selectedorkflowContainer = $(`#workflowModeContainer .selectedWorkflowContainer[workflowid=${profileData[i].workflowId}]`)
                    if (selectedorkflowContainer.length) {
                        let mode = selectedorkflowContainer.find('.modeSelection').children().find(':checked').val();

                    //    let customVisible = selectedorkflowContainer.find('.checkboxDatatable').is(':visible');
                        let isCustom = selectedorkflowContainer.find('.checkboxData').prop('checked');
                        let tempId = isCustom ? selectedorkflowContainer.find('.checkboxData').attr('tempId') : '';

                        var objectData = {}
                        objectData.CompanyId = "",
                            objectData.currentActionId = profileData[i]?.currentActionId,
                            objectData.currentActionName = profileData[i]?.currentActionName,
                            objectData.infraobjectId = profileData[i]?.infraObjectId,
                            objectData.infraobjectName = profileData[i]?.infraObjectName,
                            objectData.workflowId = profileData[i]?.workflowId,
                            objectData.workflowName = profileData[i]?.workflowName,
                            objectData.businessServiceId = profileData[i]?.businessServiceId,
                            objectData.businessServiceName = profileData[i]?.businessServiceName,
                            objectData.workflowOperationId = "",
                            objectData.status = "Pending",
                            objectData.progressStatus = profileData[i]?.progressStatus,
                            objectData.jobName = 0,
                            objectData.message = "",
                            objectData.isResume = 0,
                            objectData.isReExecute = 0,
                            objectData.isPause = 0,
                            objectData.isAbort = 0,
                            objectData.actionMode = mode,
                            objectData.isCustom = isCustom,
                            objectData.workflowExecutionTempId = tempId

                        jsonWorkflowDetails.push(objectData)
                    }
                }

               

                //let modeContainer = $('#workflowModeContainer')
                //result?.workflowProfileInfos?.forEach(function (i) {
                //    modeContainer.children().each(function () {
                //        if ($(this).attr("id") == i.workflowId) {
                //            let mode = $(this).find('.modeSelection').children().find(':checked').val();

                //            let customVisible = $(this).find('.checkboxDatatable').is(':visible');
                //            let isCustom = customVisible ? $(this).find('.checkboxDatatable').children().children().prop('checked') : false;
                //            let tempId = isCustom ? $(this).find('.checkboxDatatable').children().children().attr('tempId') : '';

                //            var objectData = {}
                //            objectData.CompanyId = "",
                //                objectData.currentActionId = i.currentActionId,
                //                objectData.currentActionName = i.currentActionName,
                //                objectData.infraobjectId = i.infraObjectId,
                //                objectData.infraobjectName = i.infraObjectName,
                //                objectData.workflowId = i.workflowId,
                //                objectData.workflowName = i.workflowName,
                //                objectData.businessServiceId = i.businessServiceId,
                //                objectData.businessServiceName = i.businessServiceName,
                //                objectData.workflowOperationId = " ",
                //                objectData.status = "Pending",
                //                objectData.progressStatus = "0/0",
                //                objectData.jobName = 0,
                //                objectData.message = " ",
                //                objectData.isResume = 0,
                //                objectData.isReExecute = 0,
                //                objectData.isPause = 0,
                //                objectData.isAbort = 0,
                //                objectData.actionMode = mode,
                //                objectData.isCustom = isCustom,
                //                objectData.workflowExecutionTempId = tempId

                //            jsonWorkflowDetails.push(objectData)
                //        }

                //    })
                //})
            }
        }
    })
    workflowData.createWorkflowOperationGroupListCommands = jsonWorkflowDetails;

    if (jsonWorkflowDetails) {
        executeWorkflowProfile(workflowData);
    }
}

const executeWorkflowProfile = async (workflowData) => {
    await $.ajax({
        type: "POST",
        url: RootUrl + 'ITAutomation/WorkflowExecution/CreateWorkflowOperationGroup',
        contentType: 'application/json; charset=UTF-8',
        dataType: "json",
        headers: {
            'RequestVerificationToken': gettoken()
        },
        data: JSON.stringify(workflowData),
        traditional: true,
        success: function (result) {
            if (result.success) {
                // notificationAlert('success', result.data.message)               
                $("#AuthendicateModal").modal('hide');
                $('#proceedBtnLoader')?.addClass('d-none')
                $('#ProceedBtn')?.removeClass('next-disabled')
                $("#hideProfile").hide()
                $('#LoadRunningProfile').html("");

                loadUsersData();
                loadExistingProfiles();
            } else {
                $('#proceedBtnLoader')?.addClass('d-none')
                $('#ProceedBtn')?.removeClass('next-disabled')
                errorNotification(result)
            }
        }
    })
}
var runningExecutionAJAX;

const RunningExecutionList = async () => {
    await $.ajax({
        type: "GET",
        url: RootUrl + 'ITAutomation/WorkflowExecution/CurrentExecutionList',
        dataType: "json",
        traditional: true,
        success: function (result) {

            if (result.success) {
                let runningWF = result.data
                let length = runningWF.length
                if (length != 0) {
                    runningWF.forEach((data, index) => {

                        $(".successCount" + data.workflowOperationId).text(data.successCount)
                        $(".runningCount" + data.workflowOperationId).text(data.runningCount)
                        $(".skipCount" + data.workflowOperationId).text(data.skipCount)
                        $(".byPassedCount" + data.workflowOperationId).text(data.bypassedCount)
                        $(".errorCount" + data.workflowOperationId).text(data.errorCount)
                        data.workflowOperationGroupRunningStatusListVm.forEach((workflowOperationdata, index1) => {
                            WorkflowOperationGroupSignalR(workflowOperationdata)
                        })
                        while (index - 1 === index) {
                            setTimeout(() => {
                                RunningExecutionList();
                            }, 4000)
                        }
                    })

                }
            } else {
                errorNotification(result)

            }
        },

    })
}

const ActiveTimeLineList = async () => {
    let activeCardId = $('.timelineAndLog.Active-Card').attr('id')
    if (activeCardId) {
        let activeContainer = $('.timelineAndLog.Active-Card')
        let workflowId = activeContainer.attr('workflowId');
        let groupId = activeContainer.attr('id');

        if (workflowId && groupId) {
            let timeLineData = {
                id: workflowId,
                workflowGroupId: groupId,
            }

            await $.ajax({
                type: "GET",
                url: RootUrl + 'ITAutomation/WorkflowExecution/TimeLineView',
                data: timeLineData,
                dataType: "json",
                traditional: true,
                success: function (result) {
                    if (result && result.length) {
                        let length = result.length
                        for (let i = 0; i < length; i++) {
                            TimeLineViewSignalR(result[i])
                            while (length - 1 === i) {
                                setTimeout(() => {
                                    ActiveTimeLineList()
                                }, 4000)
                            }
                        }
                    }
                }
            })

        }
    }
}

