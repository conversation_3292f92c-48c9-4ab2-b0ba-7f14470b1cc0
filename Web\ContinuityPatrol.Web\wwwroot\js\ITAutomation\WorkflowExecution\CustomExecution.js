﻿$(document).on('click', '.btnCustomWorkflow', async function (e) {
    e.stopPropagation();
    let getWorkflowId = $(this).closest('li').attr('id');
    let getWorkflowName = $(`#${getWorkflowId}`).attr('workflowName') || '';
    let isCustom = $(`#${getWorkflowId}`).attr('isCustom')

    if ($(this).hasClass('active')) {
        if (isCustom !== 'true') {
            $(this).removeClass('active')
            $('#timeline_view .allSelect').remove()
            $('.groupCheckBoxContainer .timelineGroupCheckbox').remove();
            $('.checkBoxContainer .timelineCheckbox').remove();
            // $('#createCustomCont, #customCountCont, #discardTimeline').addClass('d-none');
            $(this).parents('.profileContainer').find('.btnStart').prop('disabled', false)
            $('#workflowCustomContainer').addClass('d-none')
        } else {         
            $(this).removeClass('active')
            $('#timeline_view .allSelect').remove()
            $('.checkBoxContainer .timelineCheckbox').remove();
            $('.groupCheckBoxContainer .timelineGroupCheckbox').remove();
            $('#workflowCustomContainer').addClass('d-none')
            let data = {
                id: getWorkflowId,
                workflowName: getWorkflowName
            }

            await defaultTimeLineView(data, false)           
        }
       
    } else {
        $('.btnCustomWorkflow').removeClass('active');
        $(this).addClass('active');
        $(this).parents('.profileContainer').find('.btnStart').prop('disabled', true)
        $('#workflowCustomContainer').removeClass('d-none');
        $('#createCustomExecution').text('Save');
        $('#createCustomCont, #discardTimeline').addClass('d-none');
        if ($(`#${getWorkflowId}`).hasClass('Active-Card') && isCustom !== 'true') {
            let timeLineCont = $('#timeline_view .timeLineList')
            $('#timeline_view .allSelect').empty();
            $('#timeline_view')
                .find('.groupCheckBoxContainer, .checkBoxContainer')
                .find('input[type="checkbox"]')
                .remove();

            $('#timeline_view').prepend(`<div class="form-check allSelect"><input class="form-check-input timelineCheckboxAll" type="checkbox" value="" id="flexCheckDefault"><label class="form-check-label" for="flexCheckDefault">Select All</label></div>`)
          //  $('#timeline_view .timeLineList .checkBoxContainer').prepend(`<input type="checkbox" class="form-check-input timelineCheckbox me-1" id="${getRandomId('checkBox')}"/>`)
            $('#timeline_view .groupCheckBoxContainer').prepend(`<input type="checkbox" class="form-check-input timelineGroupCheckbox me-1" id="${getRandomId('checkBox')}"/>`)
            $.each(timeLineCont, function () {
                let getId = this.id
                $(`#${getId} .checkBoxContainer`).prepend(`<input type="checkbox" class="form-check-input timelineCheckbox me-1" id="${getRandomId('checkBox')}"/>`)
            })

        } else {
            $('.workflowListContainer').removeClass('Active-Card')
            $(`#${getWorkflowId}`).addClass('Active-Card')

            let data = {
                id: getWorkflowId,
                workflowName: getWorkflowName
            }

            await defaultTimeLineView(data, true)
        }
    }
})

$(document).on('change', '.timelineCheckboxAll', function (e) {
    e.stopPropagation();
    $('#timeline_view .timelineCheckbox').prop('checked', e.target.checked)
    $(".timelineGroupCheckbox") && $(".timelineGroupCheckbox").length && $("#timeline_view .timelineGroupCheckbox").prop('checked', e.target.checked);
    if (!$('#createCustomCont').hasClass('d-none')) $('#createCustomCont').addClass('d-none')
})

$(document).on('click', '.timelineGroupCheckbox', function (e) {
    let getGroupId = $(this).parents('.timelineGroupContainer').attr('id');
    $(`#${getGroupId} .timelineCheckbox`).prop('checked', e.target.checked);

    if ($('#timeline_view .timelineCheckbox').length === $('#timeline_view .timelineCheckbox:checked').length) {
        $('#timeline_view .timelineCheckboxAll').prop('checked', true);
        $('#createCustomCont').addClass('d-none');
    } else {
        $('#timeline_view .timelineCheckboxAll').prop('checked', false)
        $('#createCustomCont').removeClass('d-none');     
    }
})

$(document).on('click', '.timelineCheckbox', function (e) {
    e.stopPropagation();
    if (e.shiftKey) {
        if (e.target.checked && $('.timelineCheckbox:checked').length === 2) {
            let getPreviousId = $('.timelineCheckbox:checked').first()[0].id
            let getCurrentId = $('.timelineCheckbox:checked').last()[0].id

            $('#timeline_view a').each((idx, obj) => {
                if (idx > $('#timeline_view a').index($(`#${getPreviousId}`).parent().parent().parent()) && idx < $('#timeline_view a').index($(`#${getCurrentId}`).parent().parent().parent())) {
                    $(obj).find('.timelineCheckbox').prop('checked', e.target.checked)
                }
            })
        } 
    } else {
        if ($('#timeline_view .timelineCheckbox').length === $('#timeline_view .timelineCheckbox:checked').length) {
            $('#timeline_view .timelineCheckboxAll').prop('checked', true);
            $('#createCustomCont').addClass('d-none');
        } else {
            $('#timeline_view .timelineCheckboxAll').prop('checked', false)
            $('#createCustomCont').removeClass('d-none');
            if ($('#timeline_view .timelineCheckbox:checked').length == 0) {
                $('#createCustomCont').addClass('d-none')
            }
        }
    }
})

$('#createCustomExecution').on('click', async function () {
    let workflowId = $('.workflowListContainer.Active-Card')[0].id
    $(this).prop('disabled', true)

    let workflowData = {
        'workflowId': workflowId,
    }

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getWorkflowById,
        data: workflowData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result) {
                let workflowProperty = JSON.parse(result.properties)
                let properties = workflowProperty.nodes
                if (properties && Array.isArray(properties) && properties.length) {
                    let length = properties.length
                    for (let i = 0; i < length; i++) {
                        if (properties[i].hasOwnProperty('groupName')) {
                            let groupAction = properties[i].groupActions
                            if (groupAction && groupAction.length) {
                                groupAction.forEach((action) => {
                                    if (action.hasOwnProperty('children')) {
                                        action.children.forEach((child) => {
                                            let isCustom = $(`#${child.stepId} .timelineCheckbox`).prop('checked')
                                            child.actionInfo['isCustom'] = isCustom
                                        })
                                    } else {
                                        let isCustom = $(`#${action.stepId} .timelineCheckbox`).prop('checked')
                                        action.actionInfo['isCustom'] = isCustom
                                    }
                                })
                            }
                        } else if (properties[i].hasOwnProperty('children')) {
                            properties[i].children.forEach((child) => {
                                let isCustom = $(`#${child.stepId} .timelineCheckbox`).prop('checked')
                                child.actionInfo['isCustom'] = isCustom
                            })
                        } else if (!properties[i].actionInfo.hasOwnProperty('IsGroup')) {
                            let isCustom = $(`#${properties[i].stepId} .timelineCheckbox`).prop('checked')
                            properties[i].actionInfo['isCustom'] = isCustom
                        }
                    }
                }
                workflowProperty['nodes'] = properties
                createWorkflowExecutionTemp(workflowId, workflowProperty)
            }
        }
    })
})

const createWorkflowExecutionTemp = async (workflowId, workflowProperty, mode='') => {
    let customUrl = '';
    let data = {
        "WorkflowId": workflowId,
        "WorkflowName": $(`#${workflowId} .workflowTextContainer`).text(),
        "Properties": JSON.stringify(workflowProperty),
    }
    
    if ($('#createCustomExecution').text()?.toLowerCase() == 'update' || mode == 'custom') {
        customUrl = executionMethods.updateExecutionTemp
        data.Id = $(`#${workflowId}`).attr('tempId')
    } else {
        customUrl = executionMethods.createExecutionTemp
    }

    await $.ajax({
        type: "POST",
        url: RootUrl + customUrl,
        data: data,
        dataType: "json",
        traditional: false,
        success: async function (result) {
            if (result.success) {
                notificationAlert('success', result.message)
                $(`#${workflowId}.workflowListContainer`).attr('tempId', result.data.workflowExecutionTempId).attr('isCustom', true)

                if (mode === 'custom') {
                    let actionId = getActionId();
                    //let workflowId = $(this).parents('.workflowTextContainerContainer').attr('id')
                    let operationGroupId = $(`#${workflowId}`).attr('operationGroupId')
                    let conditionalOperation = 3
                    let getPauseOrResume = '';

                    let data = {
                        groupId: operationGroupId,
                        conditionalOperation: conditionalOperation,
                        actionIds: actionId,
                        pauseOrResume: getPauseOrResume,
                        __RequestVerificationToken: gettoken()
                    }
                    
                    await updateConditionOperation(data)
                }

                setTimeout(async () => {
                    $('#timeline_view .allSelect').remove()
                    $('.checkBoxContainer .timelineCheckbox, .groupCheckBoxContainer .timelineCheckbox').remove();
                    $('#workflowCustomContainer').addClass('d-none');
                    $(`#${workflowId} .btnCustomWorkflow`).removeClass('active')
                    $(`#${workflowId}`).parents('.profileContainer').find('.btnStart').prop('disabled', false)
                    $(`#${workflowId} .infraObjectContainer`).find('.cp-pin-point').removeClass('d-none')

                    let workflowData = {
                        id: data.WorkflowId,
                        workflowName: data.WorkflowName
                    }
                     await defaultTimeLineView(workflowData, false)
                }, 1000)

            } else {
                errorNotification(result)
            }
        }
    })
    $('#createCustomExecution').prop('disabled', false)
}

const resetCustomExecution = async (workflowId, mode='') => {
  
        await $.ajax({
            type: "DELETE",
            url: RootUrl + executionMethods.deleteExecutionTemp,
            data: { tempId: workflowId },
            dataType: "json",
            traditional: false,
            success: function (result) {
                let data = result.data
                if (data?.success) {
                    $(`#${workflowId}`).attr('isCustom', false).removeAttr('tempId')
                    $(`#${workflowId} .btnCustomWorkflow`).removeClass('active')
                    notificationAlert('success', data?.message);
                    if (mode == 'execute' && !$('.checkboxData').is(':visible')) {
                        $('#timeline_view a').attr('style', '').find('.image.cp-pending.text-secondary').removeClass('text-secondary').addClass('text-warning')
                        $('#custumExecutionAuthContainer').addClass('d-none')
                    } 
                    $('#timeline_view .allSelect').remove()
                    $('.checkBoxContainer .timelineCheckbox, .groupCheckBoxContainer .timelineGroupCheckbox').remove();
                    $('#workflowCustomContainer').addClass('d-none');
                    let profileId = $(`#${workflowId}`).parents('.profileContainer').attr('id')
                    $(`#${profileId} .btnStart`).prop('disabled', false)
                    $(`#${workflowId}`).attr('isCustom', 'false').attr('tempId', '')
                    $('#timeline_view a').attr('style', '')
                    $('#createCustomExecution').text('Save')
                    $(`#${workflowId} .infraObjectContainer`).find('.cp-pin-point').addClass('d-none')
                } else {
                    errorNotification(data)
                }
            }
        })
}

$('#discardTimeline').on('click', WFExeEventDebounce(async () => {
    let workflowId = $('.workflowListContainer.Active-Card')[0].id
    let getTempId = $(`#${workflowId}`).attr('tempId')
    if (getTempId) {
        resetCustomExecution(workflowId)
    }
}, 800))

$('#workflowModeContainer').on('click', '.checkboxData', function () {
    let workflowId = $(this).attr('id')
    workflowId = workflowId.replace('custom_', '')
    let workflowName = $(`#${workflowId} .workflowTextContainer`).text();
    let tempId = $(`#${workflowId}`).attr('tempId');
    $('#deleteCustomWorkflowName').text(workflowName)
    $('#btnDeleteCustomExecution').attr('workflowId', workflowId).attr('tempId', tempId)
    $('#btnCancelResetCustomExecution').attr('workflowId', workflowId)
    $('#AuthendicateModal').modal('hide');
    $('#DeleteCustomExeModal').modal('show')
})

$('#btnCancelResetCustomExecution').on('click', function () {
    $('#DeleteCustomExeModal').modal('hide')
    let workflowId = $(this).attr('workflowId')
    $(`#custom_${workflowId}`).prop('checked', true)
    $('#AuthendicateModal').modal('show');
    
})

$('#btnDeleteCustomExecution').on('click', function () {    
    let workflowId = $(this).attr('workflowId')
    resetCustomExecution(workflowId, 'execute')
    setTimeout(() => {
        $(`#custom_${workflowId}`).prop('checked', false).parents('td').addClass('d-none')
        $('#DeleteCustomExeModal').modal('hide')
        $('#AuthendicateModal').modal('show');       
    },500)
})

$(document).on('change', '.customIndex', function (e) {
    e.preventDefault();
    if (e.target.checked) {
        $('.customIndex').prop('checked', false)
        $(this).prop('checked', true)
        let value = e.target.value
        if (value === 'betweenIndex') {
            $('#customIndexEndContainer').removeClass('d-none')
        } else {
            $('#customIndexEndContainer').addClass('d-none')
        }
    } else {
        return false
    }
})

$('#btnCustomTimelineIndex').on('click', function () {
    if (!$('#customIndexDropdownCont').is(':visible')) {
        let count = $('.timeLineList').length
        $('#textIndexContainer').text(`Enter Sr. No (Max ${count})`)
    } 
    $(this).dropdown('toggle')
})

$('#btnCancelCustomCount').on('click', function (e) {
    e.preventDefault();
    $('.customIndex').prop('checked', false)
    $('#customDownIndex').prop('checked', true)
    $('#fromIndexCount, endIndexCount').val('')
    $('#btnCustomTimelineIndex').dropdown('toggle')
})

$('#fromIndexCount, #endIndexCount').on('input', function (e) {
    let totalLength = $('#timeline_view a').length
    if ($('.customIndex:checked').val() === 'upIndex' || $('.customIndex:checked').val() === 'downIndex' || ($('.customIndex:checked').val() === 'betweenIndex') && e.target.id === 'endIndexCount') {
        if (Number(e.target.value) > totalLength) {
            $(this).val('')
            return false
        }
    } else {
        if (Number(e.target.value) >= totalLength) {
            $(this).val('')
            return false
        }
    }  
})

$('#btnSubmitCustomCount').on('click', function (e) {
    e.preventDefault();
    let customMode = $('.customIndex:checked').val()
    let customFromValue = $('#fromIndexCount').val()
    let customToValue = $('#endIndexCount').val()
    if (customMode === 'upIndex') {
        let timeLineContainer = $('#timeline_view a')
        $('#timeline_view a').each((idx, obj) => {
            if (idx <= Number(customFromValue)-1) {
                $(obj).find('.timelineCheckbox').prop('checked', true)
            }
        })
    } else if (customMode === 'downIndex') {
        let timeLineContainer = $('#timeline_view a')
        $('#timeline_view a').each((idx, obj) => {
            if (idx >= Number(customFromValue)-1) {
                $(obj).find('.timelineCheckbox').prop('checked', true)
            }
        })
    } else if (customMode === 'betweenIndex') {
        let timeLineContainer = $('#timeline_view a')
        $('#timeline_view a').each((idx, obj) => {
            if (idx >= Number(customFromValue)-1 && idx <= Number(customToValue)-1) {
                $(obj).find('.timelineCheckbox').prop('checked', true)
            }
        })
    }
    $('#btnCustomTimelineIndex').dropdown('toggle')
})

const getWorkflowById = async (workflowId) => {
    let workflowData = {
        'workflowId': workflowId,
    }

    await $.ajax({
        type: "GET",
        url: RootUrl + executionMethods.getWorkflowById,
        data: workflowData,
        dataType: "json",
        traditional: true,
        success: function (result) {
            if (result) {
                let workflowProperty = JSON.parse(result.properties)
                let properties = workflowProperty.nodes
                if (properties && Array.isArray(properties) && properties.length) {
                    let length = properties.length
                    for (let i = 0; i < length; i++) {
                        if (properties[i].hasOwnProperty('groupName')) {
                            let groupAction = properties[i].groupActions
                            if (groupAction && groupAction.length) {
                                groupAction.forEach((action) => {
                                    if (action.hasOwnProperty('children')) {
                                        action.children.forEach((child) => {
                                            let isCustom = $(`#${child.stepId}`).css('opacity') === '1' ? true : false;
                                            child.actionInfo['isCustom'] = isCustom
                                        })
                                    } else {
                                        let isCustom = $(`#${action.stepId}`).css('opacity') === '1' ? true : false;
                                        action.actionInfo['isCustom'] = isCustom
                                    }
                                })
                            }
                        } else if (properties[i].hasOwnProperty('children')) {
                            properties[i].children.forEach((child) => {
                                let isCustom = $(`#${child.stepId}`).css('opacity') === '1' ? true : false;
                                child.actionInfo['isCustom'] = isCustom
                            })
                        } else if (!properties[i].actionInfo.hasOwnProperty('IsGroup')) {
                            let isCustom = $(`#${properties[i].stepId}`).css('opacity') === '1' ? true : false;
                            properties[i].actionInfo['isCustom'] = isCustom
                        }
                    }
                }
                workflowProperty['nodes'] = properties
                setTimeout(() => {
                    createWorkflowExecutionTemp(workflowId, workflowProperty, 'custom')
                },300)
                
            }
        }
    })
}

//<------  Infra State and Completed State   ------>

$("#confirmStateButton").on('click', WFExeEventDebounce(async function () {

    if (infraObjectStatus.length) {
        let textValue = $("#textArea").val();
        let infraObjectArray = []

        infraObjectStatus.forEach((id) => {
            infraObjectArray.push({ "id": id, "state": "Maintenance", "Reason": textValue })
        })
        let action = { "updateInfraObjectStates": infraObjectArray }

        await updateInfraObjectStatus(action, 'maintenance', '', true)
    }
}, 800))

$(document).on('click', '#btnProfileCompleted', async function () {
    let profileId = $(this).parents('.profileContainer').attr('id')
    let infraObjectContainer = $(`#${profileId} .infraObjectContainer`)
    let operationId = $(`#${profileId} .runningWorkflowContainer`).first().attr('operationid')
    let infraObjectArray = []
    let findDuplicate = [];
    let infraObjectNameList = []
    $.each(infraObjectContainer, function (idx, obj) {
        let infraObjectId = $(obj).attr('id')
        let infraObjectName = $(`#${infraObjectId}`).text();
        if (!findDuplicate.includes(infraObjectId)) {
            infraObjectArray.push({ id: infraObjectId, 'state': 'Active', 'Reason': '' })
            findDuplicate.push(infraObjectId);
            infraObjectNameList.push(infraObjectName);
        }
    })
    let action = { "updateInfraObjectStates": infraObjectArray }

    let InfraObjectlist = ''
    infraObjectNameList && infraObjectNameList.forEach((d) => {
        InfraObjectlist += d + ', '
    })
    InfraObjectlist = InfraObjectlist?.trim().slice(0, -1)
    $('#completeInfraDetails').text(InfraObjectlist).attr('title', InfraObjectlist)
    $('#activeStateButton').attr('infraObjectDetails', JSON.stringify(action)).attr('operationId', operationId)
    $("#activeConfimationModal").modal("show")
})

$('#activeStateButton').on('click', async function () {
    let operationId = $(this).attr('operationid')
    let infraObjectDetails = $(this).attr('infraObjectDetails')
    infraObjectDetails = JSON.parse(infraObjectDetails)
    await updateInfraObjectStatus(infraObjectDetails, 'active', operationId)
})

$('#btnCloseConfirmationModal').on('click', function () {
    $('#activeConfimationModal').modal('hide')
})

const updateInfraObjectStatus = async (action, state, operationId = '', mode = false) => {

    await $.ajax({
        type: "PUT",
        url: RootUrl + executionMethods.updateInfraObjectStatus,
        dataType: "json",
        contentType: "application/json",
        headers: {
            'RequestVerificationToken': gettoken()
        },
        data: JSON.stringify(action),
        success: function (response) {
            if (response.success) {
                if (state === 'maintenance') {
                    $('#ConfimationModal').modal('hide');
                    $('#AuthendicateModal').modal('show');
                    //if (isCustomExecution) {
                    //    $('#customConfirmationModal').modal('show')
                    //} else {
                      
                    //}
                    action?.updateInfraObjectStates?.forEach((d) => {
                        $(`#${d.id}`).prev().removeClass().addClass('fs-7 me-2 text-primary cp-maintenance')
                    })
                } else {
                    completeExecutedProfile(operationId)
                }

            } else {
                errorNotification(response)
                setTimeout(() => {
                    $('#ConfimationModal').modal('hide');
                }, 2000)
            }
        },
        complete: function (xhr) {
            if (mode) {
                const serverTime = xhr.getResponseHeader('Date');
                let getProfileName = $('.workflowListContainer.Active-Card').parents('.profileContainer').find('.profileNameClass').text()
                let formattedDateAndTime = formatDate(serverTime)
              //  let dateFormat = new Date(serverTime)?.toLocaleString()?.split(',')
              //  let splitFormat = dateFormat[0]?.split('/')
              //  let dayZone = Number(splitFormat[1]) < 10 ? '0' + splitFormat[1] : splitFormat[1]
              //  let monthZone = Number(splitFormat[0]) < 10 ? '0' + splitFormat[0] : splitFormat[0]
              //    // .map((d) => Number(d) < 10 ? '0' + d : d).join('-')
              //  //  let date = new Date(serverTime).toLocaleString().replaceAll('/', '-')
                $('#txtDescription').val(`${getProfileName} ${formattedDateAndTime}`)
            }
           
        }
    })
}

$("#cancelStateButton").on('click', WFExeEventDebounce(function (e) {
    let getOperationId = $('#activeStateButton').attr('operationid')
    completeExecutedProfile(getOperationId);
}, 800))

$('#cancelStateChangeButton').on('click', function () {
    $('#activeConfimationModal').modal('hide')
})

const completeExecutedProfile = async (operationId) => {

    await $.ajax({
        type: "POST",
        url: RootUrl + executionMethods.updateCompleteStatus,
        data: {
            id: operationId,
            __RequestVerificationToken: gettoken()
        },
        dataType: 'text',
        success: function (response) {
            if ($('#UserList').val() !== 'all') {
                $('#UserList').trigger('change')
            } else {
                $('#btnExecutionRefresh').trigger('click')
            }
            if ($("#LoadRunningProfile").children().length == 0) {
                $('#logViewer').empty().append(noDataImages.logView)
                $('#LoadRunningProfile').empty().html(noDataImages.ProfileContainer)
                $("#timeline_view").empty().html(noDataImages.timeLine)
                $('#runningProfileContainer').addClass('d-none')
                $('#timelineWFCountContainer .timelineConditionalCounts, #btnToggleTimeline').addClass('d-none')
                if (!$('#bypassTimelineContainer').parent().hasClass('d-none')) $('#bypassTimelineContainer').parent().addClass('d-none')
            }
            currentActiveContainerId = '';
            loadProfileList();
        }
    });
}

//Profiles by user

$("#UserList").on('change', WFExeEventDebounce(async function () {
    let userId = $(this).val()
    if (userId == 'all' || !userId) {
        loadExistingProfiles();
    } else {
        let data = {
            userId: userId
        }
        loadExistingProfiles(data, 'user');
    }  
}, 600))


