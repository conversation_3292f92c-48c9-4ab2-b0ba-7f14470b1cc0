﻿// Variable Assignment
let workflowProfileURL = {
    nameUrl: "ITAutomation/WorkflowProfileManagement/IsWorkflowProfileNameExist",
    getBusinessFunctionUrl: "ITAutomation/WorkflowProfileManagement/GetBusinessFunctionByBusinessServiceId",
    getInfraObjectUrl: "ITAutomation/WorkflowProfileManagement/GetInfraObjectByBusinessFunctionId",
    getWorkflowUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowInfraObjectByInfraObjectId",
    getWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowProfileInfoByProfileId",
    createWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/CreateWorkflowProfileInfo",
    updateWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/UpdateWorkflowProfileInfo",
    deleteWorkflowProfileUrl: "ITAutomation/WorkflowProfileManagement/DeleteWorkflowProfileInfo",
    idInfo: "ITAutomation/WorkflowProfileManagement/WorkflowProfileInfo",
    getSavefunctionUrl: "ITAutomation/WorkflowProfileManagement/CreateOrUpdate",
    getpolicyUrl: "ITAutomation/WorkflowProfileManagement/GetWorkflowProfileById",
    getDeleteUrl: "ITAutomation/WorkflowProfileManagement/Delete",
    getGroupNode: "Admin/GroupNodePolicy/GetGroupPolicyByType",
    settingList : "Admin/Settings/GetList"
}
let createPermission = $("#orchestrationCreate").data("create-permission").toLowerCase();
let deletePermission = $("#orchestrationDelete").data("delete-permission").toLowerCase();
let globalProfileUpdateId;
let NoDataFoundImage = "<img src='../../img/isomatric/Workflow_Execution_No_Data_Found.svg' class='Card_NoData_ImgExe' style='margin-left:337px;margin-top:110px;'>"

// Access field
if (createPermission === 'false') {
    const buttons = $("#btnWorkflowProfileCreate, #btnWorkflowProfileEdit, #btnSaveProfile, #btnProfileChangePassword");
    buttons.addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle data-bs-target id');
}
if (deletePermission == 'false') {
    $("#btnWorkfloProfileDelete").addClass('btn-disabled').css("cursor", "not-allowed").removeAttr('data-bs-toggle').removeAttr('data-bs-target').removeAttr('id');
}

$(function () {
    async function EditWorkflow(workflowId, workflowName) {
        let data = { 'workflowProfileInfoId': workflowId };
        $.ajax({
            type: "GET",
            url: RootUrl + workflowProfileURL.idInfo,
            data: data,
            dataType: "json"
        }).then((response) => {
            if (response && response.success && response.data) {
                const datas = response.data;
                $("#btnSaveProfile").hide();
                $("#btnWorkflowProfileEditProfile").show();
                $('#operationalServiceId').val(datas.businessServiceId).trigger('change');
                return getAysncWithHandler(RootUrl + workflowProfileURL.getBusinessFunctionUrl, { businessServiceId: datas.businessServiceId }, OnError)
                    .then((functions) => {
                    if (functions?.length > 0) {
                        SetDropDownValues(functions, "operationalFunction", "Select Operational Function");
                        $('#operationalFunction').val(datas.businessFunctionId).trigger('change');
                    }
                        return getAysncWithHandler(RootUrl + workflowProfileURL.getInfraObjectUrl, { businessFunctionId: datas.businessFunctionId }, OnError)
                            .then((infraObjects) => {
                        if (infraObjects?.length > 0) {
                            SetDropDownValues(infraObjects, "infraObject", "Select InfraObject");
                            $('#infraObject').val(datas.infraObjectId).trigger('change');
                        }
                           return getAysncWithHandler(RootUrl + workflowProfileURL.getWorkflowUrl, { infraObjectId: datas.infraObjectId }, OnError)
                            .then((workflows) => {
                            const filteredWorkflows = workflows?.filter(wf => wf.workflowId);
                                if (filteredWorkflows?.length > 0) {
                                    let options = `<option value="">Select workflow</option>`;
                                    filteredWorkflows.forEach(wf => {
                                        options += `<option value="${wf.workflowId}" data-name="${wf.workflowName}" data-action="${wf.actionType}">${wf.workflowName} - ${wf.actionType}</option>`;
                                    });
                                    $("#workflowName").html(options);
                                    $('#workflowName').val(datas.workflowId).trigger('change');
                                }
                            return response;
                        });
                    });
                });
            } else {
                errorNotification(workflowName);
            }
        }).catch((error) => {
            console.error("Error in EditWorkflow:", error);
            errorNotification(workflowName);
        });
    }
    function validateDropDown(value, errorMsg, errorElement) {
        if (!value) {
            errorElement.text(errorMsg).addClass('field-validation-error');
            return false;
        } else {
            errorElement.text('').removeClass('field-validation-error');
            return true;
        }
    }
    function SetDropDownValues(values, elementId) {
        if (Array.isArray(values) && values.length) {
            let dropdownOptions = `<option value=""></option>`;
            values.forEach(item => {               
                dropdownOptions += `<option value="${item.id}" data-name="${item.name}">${item.name}</option>`;
            });
            $("#" + elementId).empty().append(dropdownOptions);
        }
    }

    // Events
    $(document).on("click", "#editProfileId", async function (e) {
        if ($(this).prop('disabled')) {
            e.preventDefault();
            return false;
        }  
        $('#btnWorkflowProfileResetProfile').show();
        const workflowButton = $(this);
        const workflowId = workflowButton.attr("workflowProInfoId");
        const workflowName = workflowButton.attr("workflowName");
        if (workflowId) {
            globalProfileUpdateId = workflowId;
            $('.actionWorkflowprofileEdit').css("visibility", "visible");
            $('.actionWorkflowprofileDelete').removeClass('disabled').css({ 'pointer-events': 'auto', 'opacity': '1' });
            workflowButton.css("visibility", "hidden");
            const deleteBtn = workflowButton.closest('tr').find('[id="deleteProfileId"]');
            deleteBtn.addClass('disabled').css({ 'pointer-events': 'none', 'opacity': '0.5' });
        }
        await EditWorkflow(workflowId, workflowName);
    });

    $('#workflowtable').on('click', '#deleteProfileId', function (e) {
        if ($(this).prop('disabled')) {
            e.preventDefault();
            return false;
        }  
        let deleteId = $(this).attr('workflowProInfoId');
        let workflowName = $(this).attr('workflowName');
        $('#txtWorkflowDeleteId').val(deleteId);
        $('#txtWorkflow').text(workflowName);
    });

    $("#confirmWFProfileDeleteButton").on("click", commonDebounce(async function () {
        await $.ajax({
            type: 'DELETE',
            url: RootUrl + workflowProfileURL.deleteWorkflowProfileUrl,
            data: { 'workflowProfileInfoId': $('#txtWorkflowDeleteId').val() },
            dataType: "json",
            success: function (result) {
                if (result && result?.success) {
                    notificationAlert("success", result?.data.message)
                    $('#DeleteWorkflowProfileModal').modal('hide')
                    $('#selectWorkflowprofileName').trigger('change')
                }
                else {
                    errorNotification(result)
                    $('#DeleteWorkflowProfileModal').modal('hide')
                }
            }
        })
    }, 800));

    $('#operationalServiceId').on('change', async function () {
        operationalServiceId = $('#operationalServiceId').val();
        operationalServiceName = $('#operationalServiceId option:selected').text();
        if (operationalServiceId) {
            validateDropDown(operationalServiceId, "Select operational service", $('#operationalServiceError'));
            $('#workflowName, #operationalFunction, #infraObject').empty();
            let data = { businessServiceId: operationalServiceId };
            let businessFunctions = await getAysncWithHandler(RootUrl + workflowProfileURL.getBusinessFunctionUrl, data, OnError);            
            if (businessFunctions?.length > 0) {
                SetDropDownValues(businessFunctions, "operationalFunction", "Select Operational Function");
            }
        }
    });

    $('#operationalFunction').on('change', async function () {
        operationalFunctionId = $('#operationalFunction').val();
        operationalFunctionName = $('#operationalFunction option:selected').text();
        $("#infraObject,#workflowName").empty();
        if (operationalFunctionId) {
            let data = { 'businessFunctionId': operationalFunctionId };
            let infraObject = await getAysncWithHandler(RootUrl + workflowProfileURL.getInfraObjectUrl, data, OnError);
            validateDropDown(operationalFunctionName, "Select Operational Function", $('#operationalFunctionError'));
            if (infraObject && infraObject?.length > 0) {
                SetDropDownValues(infraObject, "infraObject", "Select InfraObject");
            }
        }
    });

    $('#infraObject').on('change', async function () {       
        infraObjectId = $('#infraObject').val();
        infraObjectName = $('#infraObject option:selected').text();
        $("#workflowName").empty();
        if (infraObjectId) {
            let data = { 'infraObjectId': infraObjectId};            
            let workflows = (await getAysncWithHandler(RootUrl + workflowProfileURL.getWorkflowUrl, data, OnError)) ?? [];
            workflows = workflows.filter(wf => wf.workflowId);
            if (workflows && workflows?.length > 0) {
                let options = `<option value="">Select workflow</option>`;
                workflows.forEach(wf => {
                    options += `<option value="${wf.workflowId}" data-name="${wf.workflowName}" data-action="${wf.actionType}">${wf.workflowName} - ${wf.actionType}</option>`;
                });
                $("#workflowName").append(options);
            }
        }        
        $('#workflowName').select2();
        validateDropDown($('#infraObject').val(), "Select infraObject", $('#infraObjectError'));
    });

    $('#workflowName').on('change', function () {           
        validateDropDown($('#workflowName').val(), "Select workflow", $('#workflowError'));
    });

    $('#btnSaveProfile').on('click', commonDebounce(async function (e) {
        if ($(this).prop('disabled')) {
            e.preventDefault();
            return false;
        }        
        const profileId = $('#profileId').val();
        const businessServiceId = $('#operationalServiceId').val();
        const businessServiceName = $('#operationalServiceId option:selected').text();
        const businessFunctionId = $('#operationalFunction').val();
        const businessFunctionName = $('#operationalFunction option:selected').text();
        const infraObjectId = $('#infraObject').val();
        const infraObjectName = $('#infraObject option:selected').text();
        const workflowId = $('#workflowName').val();
        let workflowName = $('#workflowName option:selected').text().trim();
        workflowName = workflowName === "Select workflow" ? '' : workflowName;
        const workflowType = $('#workflowName option:selected').data('action');
        const workflowProfileName = $('#selectWorkflowprofileName option:selected').text();
        const workflowProfileId = $('#selectWorkflowprofileName').val();
        // Validate dropdowns
        const isBusinessService = validateDropDown(businessServiceName, "Select operational service", $('#operationalServiceError'));
        const isBusinessFunction = validateDropDown(businessFunctionName, "Select operational function", $('#operationalFunctionError'));
        const isInfraObject = validateDropDown(infraObjectName, "Select infraobject", $('#infraObjectError'));
        const isWorkflow = validateDropDown(workflowName, "Select workflow", $('#workflowError'));
        const isSelectWorkflowProfile = validateDropDown(workflowProfileId, "Select workflow profile", $('#workfloProfileError'));
        if (isBusinessService && isBusinessFunction && isInfraObject && isWorkflow && isSelectWorkflowProfile) {
            const formData = {
                ProfileId: profileId,
                ProfileName: workflowProfileName,
                BusinessServiceId: businessServiceId,
                BusinessServiceName: businessServiceName,
                BusinessFunctionId: businessFunctionId,
                BusinessFunctionName: businessFunctionName,
                InfraObjectId: infraObjectId,
                InfraObjectName: infraObjectName,
                WorkflowId: workflowId,
                WorkflowName: workflowName,
                WorkflowType: workflowType,
                __RequestVerificationToken: gettoken()
            };
                const response = await $.ajax({
                    url: `${RootUrl}${workflowProfileURL.createWorkflowProfileUrl}`,
                    data: formData,
                    dataType: "json",
                    traditional: true,
                    type: 'POST'
                });
                if (response?.success && response?.data) {
                    $('#selectWorkflowprofileName').val(profileId).trigger('change');
                    notificationAlert('success', response.data.message);
                } else {
                    errorNotification(response);
                };
                clearProfileInfo();            
        }
    }, 800));
    $('#btnWorkflowProfileEditProfile').on('click', commonDebounce(async function () {
        const profileId = $('#profileId').val();
        const businessServiceId = $('#operationalServiceId').val();
        const businessServiceName = $('#operationalServiceId option:selected').text();
        const businessFunctionId = $('#operationalFunction').val();
        const businessFunctionName = $('#operationalFunction option:selected').text().trim() === "Select Value" ? '' : $('#operationalFunction option:selected').text();
        const infraObjectId = $('#infraObject').val();
        const infraObjectName = $('#infraObject option:selected').text().trim() === "Select Value" ? '' : $('#infraObject option:selected').text();
        const workflowId = $('#workflowName').val();        
        let workflowName = $('#workflowName option:selected').text().trim();
        workflowName = workflowName === "Select workflow" ? '' : workflowName;
        const workflowType = $('#workflowName option:selected').data('action');
        const workflowProfileName = $('#selectWorkflowprofileName option:selected').text();
        const workflowProfileId = $('#selectWorkflowprofileName').val();
        // Validate dropdowns
        const isBusinessService = validateDropDown(businessServiceName, "Select operational service", $('#operationalServiceError'));
        const isBusinessFunction = validateDropDown(businessFunctionName, "Select operational function", $('#operationalFunctionError'));
        const isInfraObject = validateDropDown(infraObjectName, "Select infraobject", $('#infraObjectError'));
        const isWorkflow = validateDropDown(workflowName, "Select workflow", $('#workflowError'));
        const isSelectWorkflowProfile = validateDropDown(workflowProfileId, "Select workflow profile", $('#workfloProfileError'));

        if (isBusinessService && isBusinessFunction && isInfraObject && isWorkflow && isSelectWorkflowProfile) {
            const formData = {
                Id: globalProfileUpdateId,
                ProfileId: profileId,
                ProfileName: workflowProfileName,
                BusinessServiceId: businessServiceId,
                BusinessServiceName: businessServiceName,
                BusinessFunctionId: businessFunctionId,
                BusinessFunctionName: businessFunctionName,
                InfraObjectId: infraObjectId,
                InfraObjectName: infraObjectName,
                WorkflowId: workflowId,
                WorkflowName: workflowName,
                WorkflowType: workflowType,
                __RequestVerificationToken: gettoken()
            };
                const result = await $.ajax({
                    url: RootUrl + workflowProfileURL.updateWorkflowProfileUrl,
                    data: formData,
                    dataType: "json",
                    traditional: true,
                    type: 'POST'
                });

            if (result&&result?.success) {
                    $('#selectWorkflowprofileName').val(profileId).trigger('change');
                    notificationAlert('success', result.data.message);
                    $('#btnWorkflowProfileEditProfile,#btnWorkflowProfileResetProfile').hide();
                } else {
                    errorNotification(result);
                }
                clearProfileInfo();
        }
    }, 800));

    $('#btnWorkflowProfileResetProfile').on('click', commonDebounce(async function () {
        clearProfileInfo();
        $('#btnWorkflowProfileEditProfile, #btnWorkflowProfileResetProfile').hide();
        $('#operationalServiceError, #operationalFunctionError,#infraObjectError,#workflowError').text('').removeClass('field-validation-error');
        $('#btnSaveProfile').show();
        $('.actionWorkflowprofileEdit').css("visibility", "visible");
        $('.actionWorkflowprofileDelete').removeClass('disabled').css({ 'pointer-events': 'auto', 'opacity': '1' });
    }, 800)); 

    //Validation
    const clearProfileInfo = () => {
        $('#operationalServiceId').val('').trigger('change');
        $('#operationalFunction, #infraObject, #workflowName').empty();
    };
})