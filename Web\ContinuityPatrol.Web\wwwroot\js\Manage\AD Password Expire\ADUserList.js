﻿let serverList = [];
let selectedServers = [];
const adUserList = {
    getPagination: "/Manage/ADPasswordExpire/GetPagination",
    getServerbyUser: "Manage/ADPasswordExpire/GetServerByUserName",
    GetDomainDns: "Configuration/Server/GetServerNamesForSaveAs",
    userListSave: "Manage/ADPasswordExpire/CreateOrUpdate",
    adScheduleSave: "Manage/ADPasswordExpire/AdJobCreateOrAdJobUpdate",
    getServerRole: "Manage/ADPasswordExpire/GetServerRoleTypeAndServerType",
    getSchedulePagination: "/Manage/ADPasswordExpire/GetSchedulerPagination",
};
$(function () {
    let createPermission = $("#AdpasswordCreate").data("create-permission").toLowerCase();
    let deletePermission = $("#AdpasswordDelete").data("delete-permission").toLowerCase();
    if (createPermission == 'false') {
        $("#createADPasswordExpiry").removeClass('#createADPasswordExpiry').removeAttr('data-bs-toggle').removeAttr('data-bs-target').addClass('btn-disabled', createPermission == 'false');
    }
    if (!$.fn.dataTable.isDataTable('#tblADExpiryUser')) {
        let selectedValues = [];
        let dataTable = $('#tblADExpiryUser').DataTable(
            {
                language: {
                    decimal: ",",
                    paginate: {
                        next: '<i class="cp-rignt-arrow fw-bold table-pagination-arrow" title="Next"></i>',
                        previous: '<i class="cp-left-arrow fw-bold table-pagination-arrow" title="Previous"></i>'
                    }
                },
                dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
                scrollY: true,
                retrieve: true,
                deferRender: true,
                scroller: true,
                "processing": true,
                "serverSide": false,
                "filter": true,
                "Sortable": true,
                "order": [],
                fixedColumns: {
                    left: 1,
                    right: 1
                },
                "ajax": {
                    "type": "GET",
                    "url": adUserList.getPagination,
                    "dataType": "json",
                    "data": function (d) {
                        d.PageNumber = Math.ceil(d?.start / d?.length) + 1;
                        d.pageSize = d?.length;
                        d.searchString = selectedValues.length === 0 ? $('#adPassScheduleSearch').val() : selectedValues.join(';');
                        selectedValues.length = 0;
                    },
                    "dataSrc": function (json) {
                        if (json.success) {
                            json.recordsTotal = json?.data?.totalPages;
                            json.recordsFiltered = json?.data?.totalCount;
                            if (json?.data?.data?.length === 0) {
                                $(".pagination-column").addClass("disabled")
                            }
                            else {
                                $(".pagination-column").removeClass("disabled")
                            }
                            return json?.data?.data;
                        }
                        else {
                            errorNotification(json)
                        }
                    },
                },
                "columnDefs": [
                    {
                        "targets": [3],
                        "className": "truncate"
                    }
                ],
                "columns": [
                    {
                        "data": null, "name": "Sr. No.", "autoWidth": true, "orderable": false,
                        "render": function (data, type, row, meta) {
                            if (type === 'display') {
                                return meta?.row + 1;
                            }
                            return meta?.row + 1;
                        }
                    },
                    {
                        "data": "userName", "name": "Username", "autoWidth": true,
                        "render": function (data, type, row) {
                            if (type === 'display') {
                                let editedData = data ? data : 'NA';
                                return `<span title='${editedData}'>${editedData} </span>`;
                            }
                            return data;
                        }
                    },
                    {
                        "data": "domainServer", "name": "Domain Server", "autoWidth": true,
                        "render": function (data, type, row) {
                            if (type === 'display') {
                                let editedData = data ? data : 'NA';
                                return `<span title='${editedData}'>${editedData} </span>`;
                            }
                            return data;
                        }
                    },
                    {
                        "data": "serverList", "name": "Server Info", "autoWidth": true,
                        "render": function (data, type, row) {
                            if (type === 'display') {
                                let editedData = (JSON?.parse(data)?.map(item => item.name).join(', ') || 'NA');
                                return `<span title='${editedData}'>${editedData} </span>`;
                            }
                            return data;
                        }
                    },
                    {
                        "data": "email", "name": "EMail", "autoWidth": true,
                        "render": function (data, type, row) {
                            if (type === 'display') {
                                let editedData = data ? data : 'NA';
                                return `<span title='${editedData}'>${editedData} </span>`;
                            }
                            return data;
                        }
                    },
                    {
                        "data": "notificationDays", "name": "Notification Days", "autoWidth": true,
                        "render": function (data, type, row) {
                            if (type === 'display') {
                                let editedData = data ? data : 'NA';
                                return `<span title='${editedData}'>${editedData} </span>`;
                            }
                            return data;
                        }
                    },
                    {
                        "data": "isDelete", "orderable": false, "width": '100px',
                        "render": function (data, type, row) {
                            if (createPermission === 'true' && deletePermission === "true") {
                                return `
                                  <div class="d-flex align-items-center gap-2">
                                    <span role="button"  title="Edit" id="editButton" data-form-data='${JSON.stringify(row)}'>
                                      <i class="cp-edit"></i>
                                    </span>
                                    <span role="button" id="deleteButton" title="Delete" data-form-id="${row.id}" data-form-name="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteADPasswordModal">
                                      <i class="cp-Delete"></i>
                                    </span>
                                </div>`;
                            }
                            else if (createPermission === 'true' && deletePermission === "false") {
                                return `
                       <div class="d-flex align-items-center  gap-2">
                       
                                             <span role="button"  title="Edit" id="editButton" data-form-data='${JSON.stringify(row)}'>
                                      <i class="cp-edit"></i>
                                    </span>
                                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>  
                                            
                                </div>`;
                            }
                            else if (createPermission === 'false' && deletePermission === "true") {
                                return `
                       <div class="d-flex align-items-center  gap-2">                       
                                           <span role="button" title="Edit" class="icon-disabled">
                                                <i class="cp-edit"></i>
                                            </span>  
                                               <span role="button" id="deleteButton" title="Delete" data-form-id="${row.id}" data-form-name="${row.userName}" data-bs-toggle="modal" data-bs-target="#DeleteADPasswordModal">
                                      <i class="cp-Delete"></i>
                                    </span>                                            
                                </div>`;
                            }
                            else {
                                return `
                       <div class="d-flex align-items-center  gap-2">
                       
                                           <span role="button" class="icon-disabled"><i class="cp-edit"></i>
                                            </span>  
                                                <span role="button" title="Delete" class="icon-disabled"><i class="cp-Delete"></i>
                                                </span>                                              
                                </div>`;
                            }
                        },
                        "orderable": false
                    }
                ],
                "rowCallback": function (row, data, index) {
                    let api = this.api();
                    let startIndex = api.context[0]._iDisplayStart;
                    let counter = startIndex + index + 1;
                    $('td:eq(0)', row).html(counter);
                },
                initComplete: function () {
                    $('.paginate_button.page-item.previous').attr('title', 'Previous');
                    $('.paginate_button.page-item.next').attr('title', 'Next');
                },
            });
    }

    GetdomainServerNames();
    ///Events
    $('#txtUser').on('blur', function () {
        getServerbyUserName($('#txtUser').val())
    })

    $('#adServerList').on('change', function () {
        let value = $(this).val();
        selectedServers = [];
        if (Array.isArray(value) && value.length > 0) {
            value.forEach((d) => {
                let filteredData = serverList.find((k) => k.id === d);
                if (filteredData) {
                    selectedServers.push({ id: filteredData.id, name: filteredData.name });
                }
            });
        }
        selectFieldValidaiton(value, $("#serverListError"), "Select server list");
    });

    $("#adDomainID").on("change", function () {
       $("#ddlDomainName").val($(this).find(":selected").text());
        inputValidaiton($('#adDomainID').val(), $("#domainServerError"), "Select domain server");
    });

    $('#txtUser').on("keyup", function () {
        inputValidaiton($('#txtUser').val(), $("#userNameError"), "Enter username");
    });

    $('#txtEmail').on("keyup", function () {
        emailValidation($('#txtEmail').val(), $("#EmailError"), "Enter notification email");
    });

    $('#txtNotificationDays').on("keyup", function () {
        inputValidaiton($('#txtNotificationDays').val(), $("#notificationDaysError"), "Enter notification days");
    });

    $("#SaveFunction").on("click", async function () {
        let stringifiedData = JSON.stringify(selectedServers); 
        $("#serverList").val(stringifiedData);
        let domainServer = inputValidaiton($('#adDomainID').val(), $("#domainServerError"), "Select domain server");
        let username = inputValidaiton($('#txtUser').val(), $("#userNameError"), "Enter username");
        let email = emailValidation($('#txtEmail').val(), $("#EmailError"), "Enter notification email");
        let serverList = selectFieldValidaiton($('#adServerList').val(), $("#serverListError"), "Select server list");
        let notificationDays = inputValidaiton($('#txtNotificationDays').val(), $("#notificationDaysError"), "Enter notification days");
        if (domainServer && username && email && serverList && notificationDays) {
            await $.ajax({
                url: RootUrl + adUserList.userListSave,
                method: "POST",
                dataType: "json",
                data: {
                    Id: $('#adPasswordID').val(),
                    DomainServer: $('#adDomainID option:selected').text(),
                    DomainServerId: $('#adDomainID').val(),
                    Email: $('#txtEmail').val(),
                    NotificationDays: $('#txtNotificationDays').val(),
                    ServerList: stringifiedData,
                    UserName: $('#txtUser').val(),
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result&&result.success) {
                        $('#CreateModal').modal('hide');
                        notificationAlert("success", result.data.message);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        errorNotification("Unexpected error occurred.");
                    }
                },
               
            });
        }
    });

    $("#createADPasswordExpiry").on("click", function () {
        let idArray = ['adDomainID', 'DomainServerId', 'ddlDomainName', 'txtUser', 'txtEmail', 'adServerList',
            'txtNotificationDays', 'adPasswordID']
        idArray.forEach((d) => {
            $(`#${d}`).val('')
        });
        $('#serverList').val([]);
        $("#domainServerError, #userNameError, #EmailError, #serverListError, #notificationDaysError").text('').removeClass('field-validation-error');
        $('#SaveFunction').text('Create');
    });

    $('#tblADExpiryUser').on('click', '#deleteButton', function () {
        const username = $(this).data('form-name');
        const ADID = $(this).data('form-id');
        $('#deleteData').attr('title', username).text(username);
        $('#textDeleteId').val(ADID);
    });

    $('#tblADExpiryUser').on('click', '#editButton', function () {
        $("#domainServerError, #userNameError, #EmailError, #serverListError, #notificationDaysError").text('').removeClass('field-validation-error');
        const formData = $(this).data('form-data');
        $('#SaveFunction').text('Update');
        $('#CreateModal').modal('show');
        $('#adPasswordID').val(formData.id);
        $("#adDomainID").val(formData.domainServerId).trigger('change');
        $("#ddlDomainName").val($("#adDomainID :selected").text());
        $("#txtUser").val(formData.userName).trigger('blur');
        $("#txtEmail").val(formData.email);
        const data = formData.serverList;
        let idList = JSON.parse(data).map(item => item.id);
        $("#adServerList").val(idList).trigger('change');
        $("#txtNotificationDays").val(formData.notificationDays);
    });

    //Functions
function selectFieldValidaiton(value, errorElement, errorMessage) {
    let string = value.join(", ");
    if (!string) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');
    return true;
}

async function emailValidation(value, errorElement, errorMessage) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    const validationResults = [
        await emailRegex(value)
    ];
    return await CommonValidation(errorElement, validationResults);
}

function inputValidaiton(value, errorElement, errorMessage) {
    if (!value) {
        errorElement.text(errorMessage).addClass('field-validation-error');
        return false;
    }
    if (value.includes('<')) {
        errorElement.text('Special characters not allowed').addClass('field-validation-error');
        return false;
    }
    errorElement.text('').removeClass('field-validation-error');
    return true;
    }

async function GetdomainServerNames() {
    try {
        const response = await $.ajax({
            type: "GET",
            url: RootUrl + adUserList.GetDomainDns,
            dataType: "json"
        });
        if (response && response?.success && Array.isArray(response.data)) {
            const filteredDnsServers = response.data.filter(server =>
                server.roleType?.toLowerCase() === "dns"
            );
            const ddlDomain = $('#adDomainID');
            ddlDomain.empty().append($('<option>').val("").text("Select Domain Server"));
            if (filteredDnsServers?.length > 0) {
                filteredDnsServers.forEach(item => {
                    ddlDomain.append($('<option>').val(item.id).text(item.name));
                });
            } else {
                console.warn("No matching 'dns' servers found.");
            }
        } else {
            errorNotification(response);
        }
    } catch (error) {
        console.error("Error fetching DNS server list:", error);
    }
    }

const getServerbyUserName = async (userName) => {
    await $.ajax({type: "GET", async: false,   url: RootUrl + adUserList.getServerbyUser, data: { userName: userName, osType: '' },
        success: function (result) {
            let ddlDomain = $('#adServerList');
            serverList = result
            ddlDomain.empty().append($('<option>').val("").text("Select Server"));
            if (result && result?.length > 0) {
                result?.forEach(function (item) {
                    ddlDomain.append($('<option>').val(item.id).text(item.name));
                });
            }
        },
    });
   }
});

//function fetchData(url, value = null) {
//    let resultData = null;
//    $.ajax({
//        type: "GET", async: false, url: RootUrl + url, dataType: "json", data: { name: value },
//        success: function (result) {
//            if (result&&result?.success) {
//                resultData = result.data;
//            } else {
//                errorNotification(result)
//            }
//        },
//    });
//    return resultData;
//}

//async function GetdomainServerNames() {
//    let serverRoleData = fetchData('Configuration/Server/GetServerNamesForSaveAs');

//    if (serverRoleData) {
//        const filteredData = serverRoleData.filter(value =>
//            value.roleType && value.roleType.toLowerCase() === "dns"
//        );
//        if (filteredData&&filteredData.length > 0) {
//            let ddlDomain = $('#ddlDomainID');
//            ddlDomain.empty().append($('<option>').val("").text("Select Domain Server"));
//            filteredData.forEach(item => {
//                ddlDomain.append($('<option>').val(item.name).text(item.name));
//            });
//        } else {
//            console.warn("No matching 'dns' servers found.");
//        }
//    } else {
//        console.error("Failed to fetch serverRoleData.");
//    }
//}
//async function getServerType(Roletype, Servertype) {
//    await $.ajax({
//        type: "GET",
//        async: false,
//        url: RootUrl + 'Configuration/Server/GetServerNames',
//        dataType: "json",
//        data: { Roletype, Servertype },
//        success: function (result) {
//            let ddlDomain = $('#ddlDomainID');
//            ddlDomain.empty().append($('<option>').val("").text("Select Domain Server"));
//            if (result.success) {
//                result?.data?.forEach(function (item) {
//                    ddlDomain.append($('<option>').val(item.id).text(item.name));
//                });
//            } else {
//                errorNotification(result);
//            }
//        },
//    });
//}

