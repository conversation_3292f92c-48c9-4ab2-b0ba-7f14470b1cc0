﻿let Domaindata = '', selectedValues = [], ArchivedataId = '';
const exceptThisSymbols = ["e", "E", "+", "-", "."];
$(function () {
    const getModuleState = () => {
        let state = localStorage.getItem('currentState')
        if (state && state.length) {
            setTimeout(() => {
                $('#pills-profile-tab').trigger('click')
                localStorage.removeItem('currentState')
            }, 200)
        }
    }

    GetServerRoleTypeAndServerType()

    let dataTable = $('#AdPassword').DataTable({
        language: {
            paginate: {
                next: '<i class="cp-rignt-arrow fw-semibold table-pagination-arrow" title="Next"></i>',
                previous: '<i class="cp-left-arrow fw-semibold table-pagination-arrow" title="Previous"></i>'
            }
        },
        dom: '<"pull-left"B><"pull-right"f>rt<"row pagination-column mt-3"<"col"l><"col text-center"i><"col"p>>',
        scrollY: true,
        retrieve: true,
        deferRender: true,
        scroller: true,
        processing: true,
        serverSide: false,
        filter: true,
        order: [],
        fixedColumns: { left: 1, right: 1 },
        ajax: {
            type: "GET",
            url: adUserList.getSchedulePagination,
            dataType: "json",
            data: function (d) {
                d.PageNumber = Math.ceil(d.start / d.length) + 1;
                d.pageSize = d.length;
                d.searchString = selectedValues.length === 0
                    ? $('#adPassScheduleSearch').val()
                    : selectedValues.join(';');
                selectedValues.length = 0;
            },
            dataSrc: function (json) {
                json.recordsTotal = json.totalPages || 0;
                json.recordsFiltered = json.totalCount || 0;
                $(".pagination-column").toggleClass("disabled", json?.data?.length === 0);
                return json.data || [];
            }
        },
        columnDefs: [
            { targets: [1], className: "truncate" }
        ],
        columns: [
            {
                data: null,
                name: "Sr. No.",
                autoWidth: true,
                orderable: false,
                render: (data, type, row, meta) => type === 'display' ? meta.row + 1 : data
            },
            {
                data: "domainServer",
                name: "Server Name",
                autoWidth: true,
                render: (data, type, row) =>
                    type === 'display'
                        ? `<td><span title="${row.domainServer}">${row.domainServer}</span></td>`
                        : data
            },
            {
                data: "scheduleTime",
                name: "Schedule Time",
                autoWidth: true,
                render: (data) => `<td><span title="${data}">${data}</span></td>`
            },
            {
                data: "state",
                name: "State",
                autoWidth: true,
                render: (data) => {
                    const iconClass =
                        data === "Active"
                            ? "cp-active-inactive text-success me-1"
                            : "cp-active-inactive text-danger me-1";

                    const displayState = data ?? "NA";
                    return `<td><i class="${iconClass}" id="icon" title="${data}"></i></td><td><span id="jobmanagestate">${displayState}</span></td>`;
                }
            },
            {
                orderable: false,
                render: (data, type, row) => `
                <div class="d-flex align-items-center gap-2">
                    <span role="button" class="adScheduleEditButton" title="Edit" data-Archive='${JSON.stringify(row)}'>
                        <i class="cp-edit"></i>
                    </span>
                    <span role="button" title="Delete" class="adscheduleDeleteButton"
                        data-Archive-id="${row.id}" data-Archive-name="${row.domainServer}"
                        data-bs-toggle="modal" data-bs-target="#DeleteModal">
                        <i class="cp-Delete"></i>
                    </span>
                </div>`
            }
        ],
        rowCallback: function (row, data, index) {
            const startIndex = this.api().context[0]._iDisplayStart;
            $('td:eq(0)', row).html(startIndex + index + 1);
        },
        initComplete: function () {
            $('.paginate_button.page-item.previous').attr('title', 'Previous');
            $('.paginate_button.page-item.next').attr('title', 'Next');
        }
    });
        //Events
    $('#adPassScheduleSearch').on('keydown input', commonDebounce(function (e) {
        if (e.key === '=' || e.key === 'Enter') {
            e.preventDefault();
            return false;
        }

        const adNameCheckbox = $("#Name");
        const inputValue = $('#adPassScheduleSearch').val();

        if (adNameCheckbox.is(':checked')) {
            selectedValues.push(adNameCheckbox.val() + inputValue);
        }

        // Check active tab and reload the correct DataTable
        if ($('#pills-profile-tab').hasClass('active')) {
            dataTable.ajax.reload(function (json) {
                if (json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            });
        } else if ($('#pills-home-tab').hasClass('active')) {
            $('#tblADExpiryUser').DataTable().ajax.reload(function (json) {
                if (json.recordsFiltered === 0) {
                    $('.dataTables_empty').text('No matching records found');
                }
            });
        }

    }, 500));


    $('#adPassScheduleSearch').attr('autocomplete', 'off');    
    //Update
    $('#AdPassword').on('click', '.adScheduleEditButton',function () {
        clearADUserField();
            $('#pills-profile-tab').on('click');
            Domaindata = $(this).data("archive");            
            Tab_schedule_type(Domaindata);
            $('#scheduleModal').modal('show');
            populateModalFieldss(Domaindata);
        $('#btnScheduleSave').text("Update");
        });
    //Delete
    $('#AdPassword').on('click', '.adscheduleDeleteButton', function () {
        $('#pills-profile-tab').on('click');
        let domainId = $(this).data("archiveId");
        let domainName = $(this).data("archiveName")
        $('#textAdDeleteId').val(domainId);
        $('#adDeleteData').text(domainName);
    });

    $('#shedulerCreate').on('click', function () {
        GetServerRoleTypeAndServerType()
        clearInputFields();
        clearADUserField();
        $('#btnScheduleSave').text("Save");
        $('#pills-profile-tab').on('click');
        initializeStateRadio();
    });

    $('#btnCancel').on('click', async function () {
        clearADUserField();
        $('#pills-profile-tab').on('click');
    })

    $('#domainServer').on('change', function () {
        const value = $(this).val();
        const activeType = $('#domainServer option:selected').text();
        $("#domainId").val(activeType)
        validateDropDown(value, 'Select domain name', 'domainNameError');
    });

    $('#lblMonth').on("change", function () {
        $('input[name="Monthyday"]').prop("checked", false)
        validateDayNumber($(this).val(), "Select month and year", $('#CronMonthly-error'));
        let selectedDate = new Date($(this).val());
        let currentDate = new Date();
        const getDays = (year, month) => {
            return new Date(year, month, 0).getDate();        };
        const daysInmonth = getDays(selectedDate.getFullYear(), selectedDate.getMonth() + 1)
        for (let i = 0; i < daysInmonth; i++) {
            let data = i + 1;
            $('input[name="Monthyday"]').each(function () {
                let checkboxValue = parseInt($(this).val());
                $(this).css("display", checkboxValue > data ? "none" : "block");
            })
            $(".checklabel").each(function () {
                let checkboxValue = parseInt($(this).text());
                $(this).css("display", checkboxValue > data ? "none" : "block");
            })
        }
        if ($(this).val() == "") {
            $('input[name="Monthyday"]').prop('disabled', true);
            $('input[name="Monthyday"]').prop('checked', false);
            $("#CronMon-error").text("").removeClass("field-validation-error")
        } else {
            $('input[name="Monthyday"]').each(function () {
                var checkboxValue = parseInt($(this).val());

                if ((selectedDate.getMonth() === currentDate.getMonth() && selectedDate.getFullYear() === currentDate.getFullYear() && checkboxValue < currentDate.getDate()) ||
                    (selectedDate.getMonth() < currentDate.getMonth() && selectedDate.getFullYear() <= currentDate.getFullYear())) {
                    $(this).prop('disabled', true);
                } else {
                    $(this).prop('disabled', false);

                }
            })
        }
    });

    $('#txtMins').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#txtMins').val('');
        }
        if ($(this).val() == 0 || $(this).val() > 59) {
            $('#txtMins').val("")
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateMinJobNumber($(this).val(), "Enter minutes", $('#CronMin-error'));
    });
    // Hourly Button
    $('#txtHours').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || $(this).val() > 23) {
            event.preventDefault();
            $('#txtHours').val('');
        }
        if ($(this).val() == 0) {
            $('#txtHours').val("")
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateHourJobNumber($(this).val(), "Enter hours", $('#CronHourly-error'));
    });

    $('#txtMinutes').on('input keypress', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key) || $(this).val() > 59) {
            event.preventDefault();
            $('#txtMinutes').val('');
        }
        if ($(this).val().length >= 2) {
            event.preventDefault()
        }
        validateMiniteJobNumber($(this).val(), "Enter minutes", $('#CronHourMin-error'));
    });
    // Daily Button
    $('input[name=daysevery]').on('click', function () {
        ValidateCronRadioButton($('#Crondaysevery-error'));
    });

    $('#everyHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#everyHours').val('');
        }
        validateHourJobNumber($(this).val(), "Select time", $('#CroneveryHour-error'));
    });

    $('#everyMinutes').on('click', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69) {
            event.preventDefault();
            $('#everyMinutes').val('');
        }
        validateMinJobNumber($(this).val(), "Select minutes", $('#CroneveryMin-error'));
    });

    // weekly Button 
    $('input[name=weekDays]').on('click', function () {

        let checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
        let Dayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        validateDayNumber(Dayvalue, "Select day", $('#CronDay-error'));
    });

    $('#ddlHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || ["e", "E", "+", "-", "."].includes(event.key)) {
            $('#ddlHours').val('');
        }
        validateHourJobNumber($(this).val(), "Select start time", $('#CronddlHour-error'));
    });    

    $('input[name=Monthyday]').on('click', function () {
        let checkedCheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        let MonthDayvalue = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        validateDayNumber(MonthDayvalue, "Select date", $('#CronMon-error'));
    });

    $('input[name=month]').on('change', function () {
        var selectedMonth = $(this).val().split('-')[1];
        monthcheck = selectedMonth.padStart(2, '0');
    });

    $('#MonthlyHours').on('input', function (event) {
        if (event.key == 109 || event.key == 107 || event.key == 69 || exceptThisSymbols.includes(event.key)) {
            event.preventDefault();
            $('#MonthlyHours').val('');
        }
        validateHourJobNumber($(this).val(), "Select hours", $('#MonthlyHours-error'));
    });    

    $("#btnScheduleSave").on('click', async function () {
        Get_ScheduleTypes();
        let scheduleType = $('#textScheduleType').val();
        let domainName = $("#domainServer").val();
        let isDomainName = await validateDropDown(domainName, ' Select domain name', 'domainNameError');
        let isScheduler = CronValidation();
        let { CronExpression, listcron } = JobCronExpression();
        $('#textCronExpression').val(CronExpression);
        $('#txtCronViewList').val(listcron);
        let state = $('input[name="state"]:checked').val();
        if (isDomainName && isScheduler) {
            localStorage.setItem('currentState', 'sheduler');
            await $.ajax({
                url: RootUrl + adUserList.adScheduleSave,
                method: "POST",
                dataType: "json",
                data: {
                    Id: $('#Id').val(),
                    CronExpression: CronExpression,
                    DomainServer: $("#domainServer option:selected").text(),
                    DomainServerId: domainName,
                    ScheduleTime: listcron,
                    State: state,
                    ScheduleType: scheduleType,
                    __RequestVerificationToken: gettoken()
                },
                success: function (result) {
                    if (result && result.success) {
                        $('#scheduleModal').modal('hide');
                        notificationAlert("success", result.data.message);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        errorNotification("Unexpected error occurred.");
                    }
                }
            });
        }
    });  

    $("#nav-Monthly-tab").on("click", function () {
        let isSave = $("#btnScheduleSave").text().trim() === "Save";
        if (isSave) {
            $('input[name=Monthyday]').attr('disabled', 'disabled');
        }
    })

    $(document).on('click', '.adConfigureTabs', function () {
        let getId = $(this)[0].id
        if (getId === 'pills-profile-tab') {
            $('#shedulerCreate').removeClass('d-none')
            if (!$('#createADPasswordExpiry').hasClass('d-none')) {
                $('#createADPasswordExpiry').addClass('d-none')
                $('#adPassScheduleSearch').val('');
                dataTable.ajax.reload();
            }
        } else {
            $('#createADPasswordExpiry').removeClass('d-none')
            if (!$('#shedulerCreate').hasClass('d-none')) {
                $('#shedulerCreate').addClass('d-none')
                $('#adPassScheduleSearch').val('');
                $('#tblADExpiryUser').DataTable().ajax.reload();
            }
        }
    })
    // Functions
    let Radiobutton;
    function initializeStateRadio() {
        const $stateInputs = $("input[name='state']");
        if (!$stateInputs.is(':checked')) {
            $stateInputs.first().prop('checked', true);
        }
        Radiobutton = $stateInputs.filter(':checked').val();
        $("#StateVal").val(Radiobutton);
        $stateInputs.off('change').on('change', function () {
            Radiobutton = $stateInputs.filter(':checked').val();
            $("#StateVal").val(Radiobutton);
        });
    }    

    async function GetServerRoleTypeAndServerType() {
        $("#domainServer").empty();
        await $.ajax({
            type: "GET",
            url: RootUrl + adUserList.getServerRole,
            dataType: "json",
            traditional: true,
            success: function (result) {
                if (result?.success) {
                    if (result?.data && result?.data.length > 0) {
                        $('#domainServer').append('<option value=""></option>');
                        result.data.forEach(item => {
                            $('#domainServer').append('<option  value="' + item.id + '">' + item.name + '</option>');
                        });
                    }
                } else {
                    errorNotification(result);
                }
            }
        })
    }
    function validateDropDown(value, errorMessage, errorElement) {

        if (!value) {
            $('#' + errorElement).text(errorMessage).addClass('field-validation-error');
            return false;
        } else {
            $('#' + errorElement).text('').removeClass('field-validation-error');
            return true;
        }
    }
    function ClearErrorElements(errorElements) {
        errorElements.forEach(element => {
            $(element).text('').removeClass('field-validation-error');
        });

    }
    const errorElements = ['#domainNameError,#CronMin-error,#CronHourly-error,#CronHourMin-error,#CronExpression-error,#Crondaysevery-error,#CroneveryHour-error,#CroneveryMin-error,#CronDay-error,#adCronMonthMinsError,#CronddlHour-error,#MonthlyHours-error,#CronMonthly-error,#MonthlyHours-error,#CronMon-error'];
    const clearADUserField = () => {
        $('#btnScheduleSave').text("Save");
        $('#Id').val('');
        ClearErrorElements(errorElements);
        clearCronExpressionData();
    };
    const clearCronExpressionData = () => {        
        $('#txtMins, #txtHours, #txtMinutes, #ddlHours, #ddlMinutes, #everyHours, #everyMinutes, #datetimeCron, #textCronExpression, #lblMonth, #txtHourss, #txtMinss').val('');
        ArchivedataId = '';        
        $('input[name="weekDays"], input[name="Monthyday"], input[name="daysevery"]').prop("checked", false);     
        $('.tab-pane.fade').removeClass('active show');
        $('#nav-Minutes').addClass('active show');
        $('#nav-Minutes-tab').addClass('active');
        $('#nav-Hourly, #nav-Hourly-tab, #nav-Daily, #nav-Daily-tab, #nav-Weekly, #nav-Weekly-tab, #nav-Monthly-tab, #nav-Monthly').removeClass('active');

    };
    let monthInput = document.getElementById("lblMonth");
    let today = new Date();
    let currentYear = today.getFullYear();
    let currentMonth = today.getMonth() + 1;
    let minMonth = currentYear + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    let maxMonth = (currentYear + 77) + "-" + (currentMonth < 10 ? "0" : "") + currentMonth;
    monthInput.setAttribute("min", minMonth);
    monthInput.setAttribute("max", maxMonth);    
    function CronValidation() {
        var checkedCheckboxes = document.querySelectorAll('[name="weekDays"]:checked');
        var txtDay = Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
        var Monthlycheckboxes = document.querySelectorAll('[name="Monthyday"]:checked');
        var txtmonthday = Array.from(Monthlycheckboxes).map(checkbox => checkbox.value);
        var monthlymonth = $('#lblMonth').val();
        var Minutes = $('#txtMins').val();
        var txtHours = $('#txtHours').val();
        var txtHourMinutes = $('#txtMinutes').val();
        var everyHours = $('#everyHours').val();
        var MonthlyHours = $('#MonthlyHours').val();
        var isScheduler = '';
        $('#datetimeCron').val('');
        var Scheduler_types = $('.nav-tabs .active').text().trim();
        switch (Scheduler_types) {
            case "Minutes":
                isScheduler = validateMinJobNumber(Minutes, "Enter minutes", $('#CronMin-error'));
                break;
            case "Hourly":
                isScheduler = validateHourJobNumber(txtHours, "Enter hours", $('#CronHourly-error'));
                isScheduler = validateMiniteJobNumber(txtHourMinutes, "Enter minutes", $('#CronHourMin-error'));
                break;
            case "Daily":
                isSchedulerHour = validateHourJobNumber(everyHours, "Select start time", $('#CroneveryHour-error'));
                isSchedulerDay = ValidateCronRadioButton($('#Crondaysevery-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Weekly":
                isSchedulerHour = validateHourJobNumber($('#ddlHours').val(), "Select start time", $('#CronddlHour-error'));
                isSchedulerDay = validateDayNumber(txtDay, "Select day", $('#CronDay-error'));
                if (isSchedulerHour && isSchedulerDay) {
                    isScheduler = true;
                }
                break;
            case "Monthly":
                isSchedulerHour = validateHourJobNumber(MonthlyHours, "Select start time", $('#MonthlyHours-error'));
                isSchedulerDay = validateDayNumber(txtmonthday, "Select date", $('#CronMon-error'));
                isSchedulerMonth = validateDayNumber(monthlymonth, "Select month and year", $('#CronMonthly-error'));
                if (isSchedulerHour && isSchedulerDay && isSchedulerMonth) {
                    isScheduler = true;
                }
                break;
        }
        return isScheduler;
    }     
    function populateModalFieldss(Domaindata) {
        const { cronExpression, state, id, domainServerId, domainServer, scheduleTime: scheduleStr } = Domaindata;
        $('#textCronExpression').val(cronExpression);
        $('#Id').val(id);
        $("#domainServer").val(domainServerId);
        $("#domainId").val(domainServer);
        ArchivedataId = id;
        $("#textStateActive").prop("checked", state === 'Active');
        $("#textStateInactive").prop("checked", state === 'Inactive');
        const scheduleTime = scheduleStr?.split(" ") || [];
        setTimeout(() => {
            if (scheduleStr.includes("Every day")) {
                $("#defaultCheck-everyday").prop("checked", true);
                $("#everyHours").val(`${scheduleTime[4]}:${scheduleTime[6]}`).trigger("change");
            }
            if (scheduleStr.includes("MON-FRI")) {
                $("#defaultCheck-MON-FRI").prop("checked", true);
                $("#everyHours").val(`${scheduleTime[3]}:${scheduleTime[5]}`).trigger("change");
            }
            if (scheduleTime.length === 7) {
                $("#txtMinutes").val(scheduleTime[5]);
                $("#txtHours").val(scheduleTime[1]);
            }
            if (!$("#defaultCheck-MON-FRI").prop("checked")) {
                const weekdays = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
                weekdays.forEach((day, index) => {
                    if (scheduleStr.includes(day)) {
                        $(`#defaultCheck-${index}`).prop("checked", true);
                    }
                });
                $("#ddlHours").val(`${scheduleTime[2]}:${scheduleTime[4]}`).trigger("change");
            }
            if (scheduleTime.length >= 12) {
                const monthMap = {
                    JAN: "01", FEB: "02", MAR: "03", APR: "04",
                    MAY: "05", JUN: "06", JUL: "07", AUG: "08",
                    SEP: "09", OCT: "10", NOV: "11", DEC: "12"
                };
                let year = parseInt(scheduleTime[12], 10);
                let month = monthMap[scheduleTime[8]] || "12";
                if (month === "00") {
                    month = "12";
                    year -= 1;
                }
                const newDate = `${year}-${month}`;
                $("#lblMonth").val(newDate).trigger('change');
                (scheduleTime[5]?.split(",") || []).forEach(i => {
                    $(`#inlineCheckbox${i}`).prop("checked", !!i);
                });
                $("#MonthlyHours").val(`${scheduleTime[0]}:${scheduleTime[2]}`).trigger("change");
            }
        }, 500);
        Radiobutton = Domaindata.type;
    }   
    getModuleState()
})
//function populateModalFieldss(Domaindata) {
//    $('#textCronExpression').val(Domaindata.cronExpression);
//    if (Domaindata.state === 'Active') {
//        $("#textStateActive").prop("checked", true);
//        $("#textStateInactive").prop("checked", false);
//    } else if (Domaindata.state === 'Inactive') {
//        $("#textStateInactive").prop("checked", true);
//        $("#textStateActive").prop("checked", false);
//    }
//    $('#Id').val(Domaindata.id);
//    $("#domainServer").val(Domaindata.domainServerId)
//    $("#domainId").val(Domaindata.domainServer)
//    ArchivedataId = Domaindata?.id
//    let scheduleTime = Domaindata?.scheduleTime.split(" ")
//    setTimeout(() => {
//        if (Domaindata.scheduleTime.includes("Every day") == true) {
//            $("#defaultCheck-everyday").prop("checked", true)
//            $("#everyHours").val(scheduleTime[4] + ":" + scheduleTime[6]).trigger("change")
//        }
//        if (Domaindata.scheduleTime.includes("MON-FRI") == true) {
//            $("#defaultCheck-MON-FRI").prop("checked", true)
//            $("#everyHours").val(scheduleTime[3] + ":" + scheduleTime[5]).trigger("change")
//        }
//        if (scheduleTime.length == 7) {
//            $("#txtMinutes").val(scheduleTime[5])
//            $("#txtHours").val(scheduleTime[1])
//        }
//        if ($("#defaultCheck-MON-FRI").prop("checked") != true) {
//            if (Domaindata.scheduleTime.includes("MON") == true) {
//                $("#defaultCheck-1").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("TUE") == true) {
//                $("#defaultCheck-2").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("WED") == true) {
//                $("#defaultCheck-3").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("THU") == true) {
//                $("#defaultCheck-4").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("FRI") == true) {
//                $("#defaultCheck-5").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("SAT") == true) {
//                $("#defaultCheck-6").prop("checked", true)
//            }
//            if (Domaindata.scheduleTime.includes("SUN") == true) {
//                $("#defaultCheck-0").prop("checked", true)
//            }
//            $("#ddlHours").val(scheduleTime[2] + ":" + scheduleTime[4]).trigger("change")
//        }
//        if (scheduleTime.length >= 12) {
//            var year = parseInt(scheduleTime[12])
//            var month = parseInt(scheduleTime[8] == "JAN" ? "01" : scheduleTime[8] == "FEB" ? "02" : scheduleTime[8] == "MAR" ? "03" : scheduleTime[8] == "APR" ? "04" :
//                scheduleTime[8] == "MAY" ? "05" : scheduleTime[8] == "JUN" ? "06" : scheduleTime[8] == "JUL" ? "07" : scheduleTime[8] == "AUG" ? "08" : scheduleTime[8] == "SEP" ? "09" :
//                    scheduleTime[8] == "OCT" ? "10" : scheduleTime[8] == "NOV" ? "11" : scheduleTime[8] == "DEC" ? "12" : "")
//            if (month <= 9 && month > 0) {
//                month = "0" + month;
//            }
//            else if (month == 0) {
//                month = "12";
//                year = year - 1;
//            }
//            var newdate = year + "-" + month;
//            $("#lblMonth").val(newdate).trigger('change')
//            scheduleTime[5]?.split(",").forEach(function (i) {
//                if (i) {
//                    $("#inlineCheckbox" + i).prop("checked", true)
//                } else {
//                    $("#inlineCheckbox" + i).prop("checked", false)
//                }
//            })
//            $("#MonthlyHours").val(scheduleTime[0] + ":" + scheduleTime[2]).trigger("change")
//        }
//    }, 500)
//    Radiobutton = Domaindata.type;
//}  