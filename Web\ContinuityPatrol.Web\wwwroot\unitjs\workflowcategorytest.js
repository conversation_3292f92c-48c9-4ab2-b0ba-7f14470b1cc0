﻿// Test data using correct values

QUnit.test("Category Name Unit Test (Enter category name)", function (assert) {
    let categoryName = "";
    assert.ok(validateName(categoryName));
});

QUnit.test("Category Name Unit Test (Should not end with space)", function (assert) {
    let categoryName = "testUser ";
    assert.ok(validateName(categoryName));
});
// Test data using empty password

// Test data using empty username
QUnit.test("Category Name Unit Test (Special characters not allowed)", function (assert) {
    let categoryName = "testUser$";
    assert.ok(validateName(categoryName));
});
// Test data using invalid length password
QUnit.test("Category Name Unit Test (Should not begin with number)", function (assert) {
    let categoryName = "1testUser";
    assert.ok(validateName(categoryName));
});



